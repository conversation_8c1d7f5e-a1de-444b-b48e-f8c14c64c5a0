#!/bin/bash
 
#runType=$1
#description=$2
#
#echo -e ${runType}
#echo -e ${description}
#
##runType为unit时则跑单元用例，其他的则跑集成用例
#if [[ "x$runType" == "xunit" ]];then
#    echo -e "runtype:$runType"
#    echo -e "单元用例执行......"
#    sh gradlew clean
#    sh gradlew unitTest -stacktrace
#else
#    echo -e "runtype:$runType"
#    echo -e "集成用例执行......"
#    sh gradlew clean
#    sh gradlew integrationTest -stacktrace
#fi

#!/bin/bash

runType=$1
content=$2
isRerun=$3

survival=0
if [ "x$content" = 'x' ];then
  survival=1
fi

CRTDIR="$( cd "$( dirname "$0" )" && pwd )"
PROTFile=${CRTDIR}/src/main/resources/bootstrap.properties
if [ "x$runType" = 'xunit' ];then
        RESULT_PATH=unit-test-results
else
        RESULT_PATH=integration-test-results
fi
CASE=""
#安全扫描
if [ "x$securecase" != 'x' ];then
        var=`echo ${securecase} | awk -F';' '{print $0}' | sed "s/;/ /g"`
        for j in $var
        do
                CASE+=" --tests $j"
        done
fi

function geterrorcase {
        for i in `find ${CRTDIR}/build/$RESULT_PATH -name "*.xml"`;
        do
        #如果没错误 不输出
        if [ `grep -c "failures=\"0" $i` -eq '0' ]; then
            a=${i##*TEST-}
            b=${a%%.xml*}
            CASE+=" --tests $b"
        fi
        done
        mv ${CRTDIR}/build  ${CRTDIR}/build_bak
}
if [ "x$isRerun" != 'x' ];then
    geterrorcase
fi
function prop {
	#[ -f "$PROTFile" ] && grep -P "^\s*[^#]?${1}=.*$" $PROTFile | cut -d'=' -f2
	[ -f "$PROTFile" ] && grep -P "^\s*[^#]?${1}\s*=.*$" $PROTFile  | sed 's/.*=\s*//'
}
var=`echo ${content} | awk -F';' '{print $0}' | sed "s/;/ /g"`
function check {
	for i in $var
	do
    		project=`echo ${i%=*}| sed "s/ //g"`
    		ip=`echo ${i#*=}|sed "s/ //g"`
    		grep -rn $project $CRTDIR --exclude-dir=build
    		if [ $? -eq 0 ] && [[ "x$project" != "xMULTI_STREAM_ADDR_DISPATCHER_ADDR" ]]; then
                nport=`echo ""|telnet ${ip} ${port} 2>/dev/null|grep "\^]"|wc -l`
    		    export ${project}="http://"${ip}":"${port}
    		    if [ $nport -ne 0 ];then
                    survival=1
    		    else
    		        survival=0
                fi
                break
            fi
    echo "默认存活"
	survival=1
	done
}


port=$(prop "server.port")

# 获取cirun.sh脚本绝对路径
DIR="$( cd "$( dirname "$0" )" && pwd )"
# cirun.sh脚本的上一层路径，用于执行gradlew脚本
gradlew_dir=$(dirname "$DIR")

#runType为unit时则跑单元用例，其他的则跑集成用例
if [[ "x$runType" == "xunit" ]];then
    echo -e "runtype:$runType"
    echo -e "单元用例执行......"
    sh gradlew :clean :unitTest $CASE -Dorg.gradle.internal.http.socketTimeout=300000

else
    for i in `seq 1 15`
    do
        check
        if [ "x$survival" = 'x1' ];then
            echo "IP地址存活,执行接口用例"
            echo -e "runtype:$runType"
            if  [[ "x$runType" == "xnewintegrationTest" ]];then
                echo -e "runtype:$runType"
                echo -e "集成用例执行......"
                sh gradlew :clean :newintegrationTest $CASE -Dorg.gradle.internal.http.socketTimeout=300000
            elif [[ "x$runType" == "xTest" ]];then
                echo -e "runtype:$runType"
                echo -e "测试环节用例执行......"
                sh gradlew :clean :integrationTest $CASE -Dorg.gradle.internal.http.socketTimeout=300000
            elif [[ "x$runType" == "xDev" ]];then
                export caseEnv=dev
                echo -e "runtype:$runType"
                echo -e "研发环节用例执行......"
                sh gradlew :clean :integrationTest $CASE -Dorg.gradle.internal.http.socketTimeout=300000
            elif [[ "x$runType" == "xonlineTest" ]];then
                export caseEnv=online
                echo -e "runtype:$runType"
                echo -e "线上巡检用例执行......"
                sh gradlew :clean :onlineTest $CASE -Dorg.gradle.internal.http.socketTimeout=300000
            elif [[ "x$runType" == "xsecure" ]];then
                export SETPROXY=1
                echo -e "runtype:$runType"
                echo -e "安全用例执行......"
                sh gradlew :clean
                sh gradlew :integrationTest $CASE -Dorg.gradle.internal.http.socketTimeout=300000
            else
                echo -e "runtype:$runType"
                echo -e "不区分标签,集成目录下全量用例执行......"
                sh gradlew :clean :integrationTest $CASE -Dorg.gradle.internal.http.socketTimeout=300000
            fi
            if [ "x$isRerun" != 'x' ];then
                cp -n ${CRTDIR}/build_bak/${RESULT_PATH}/* ${CRTDIR}/build/${RESULT_PATH}
                rm -rf ${CRTDIR}/build_bak
            fi
            break
        else
            echo "服务IP地址不存活,继续检测"
            sleep 10
        fi
    done

fi

