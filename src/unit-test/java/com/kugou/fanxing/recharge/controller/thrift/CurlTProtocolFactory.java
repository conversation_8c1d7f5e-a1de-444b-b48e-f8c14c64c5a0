package com.kugou.fanxing.recharge.controller.thrift;

import com.google.common.base.Charsets;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.protocol.TJSONProtocol;
import org.apache.thrift.protocol.TProtocol;
import org.apache.thrift.transport.TIOStreamTransport;
import org.apache.thrift.transport.TTransportException;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;

/**
 * <AUTHOR>
 */
public class CurlTProtocolFactory {

    private CurlTProtocolFactory() {
    }

    public static TProtocol buildTJSONProtocol(String host, String uri) throws TTransportException {
        return buildTJSONProtocol(host + uri);
    }

    public static TProtocol buildTJSONProtocol(String url) throws TTransportException {
        CurlTTransport transport = new CurlTTransport(url);
        return new TJSONProtocol(transport);
    }

    @SuppressWarnings("unchecked")
    public static <T> T getClient(Class<T> clientClass, String url) throws TTransportException, NoSuchMethodException, IllegalAccessException, InvocationTargetException, InstantiationException {
        Constructor<?> constructor = clientClass.getConstructor(TProtocol.class);
        return (T) constructor.newInstance(CurlTProtocolFactory.buildTJSONProtocol(url));
    }

    @Slf4j
    private static class CurlTTransport extends TIOStreamTransport {

        private String url;

        public CurlTTransport(String url) {
            super(new ByteArrayInputStream("[1,\"aaa\",1,1,{\"1\":{\"i32\":1},\"2\":{\"str\":\"\"},\"5\":{\"lst\":[\"rec\",0]}}]".getBytes()), new ByteArrayOutputStream());
            this.url = url;
        }

        @Override
        public void flush() throws TTransportException {
            super.flush();
            printCurl();
        }

        private void printCurl() {
            if (super.outputStream_ instanceof ByteArrayOutputStream) {
                ByteArrayOutputStream output = (ByteArrayOutputStream) outputStream_;
                String arg = new String(output.toByteArray(), Charsets.UTF_8);
                log.info("curl -H 'protocol:json' -H 'Content-Type:application/x-thrift' -d '{}' '{}'", arg, url);
            }
        }
    }
}


