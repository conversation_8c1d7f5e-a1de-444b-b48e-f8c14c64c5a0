package com.kugou.fanxing.recharge.controller.thrift;

import com.kugou.fanxing.recharge.thrift.QueryRechargeOrderByOrderNumRequest;
import com.kugou.fanxing.recharge.thrift.QueryRechargeSuccessRequest;
import com.kugou.fanxing.recharge.thrift.RechargeService;
import com.kugou.fanxing.recharge.thrift.RechargeThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.thrift.TApplicationException;
import org.apache.thrift.TException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.concurrent.TimeUnit;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class RechargeThriftServiceImplUnitTest {
    private static final String HOST = "http://127.0.0.1:18888";
    private static final String URI = "/platform_recharge_service/thrift/rechargeThriftService";

    @Test(expected = TApplicationException.class)
    public void queryRechargeOrderByOrderNum() throws TException {
        QueryRechargeSuccessRequest request = new QueryRechargeSuccessRequest();
        request.setBeginTime(TimeUnit.MILLISECONDS.toSeconds(DateUtils.addDays(new Date(), -10).getTime()));
        request.setEndTime(TimeUnit.MILLISECONDS.toSeconds(DateUtils.addDays(new Date(), 0).getTime()));
        request.setBatchSize(10);
        request.setLastRechargeId(0L);
        request.setIncludeSingCoin(true);
        RechargeThriftService.Client service = new RechargeThriftService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.getRechargeSuccessListV2(request);
    }


}
