package com.kugou.fanxing.recharge.controller.thrift;

import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.biz.commons.util.SnowFlake;
import com.kugou.fanxing.recharge.constant.DrawTypeEnum;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.withdraw.thrift.*;
import org.apache.thrift.TApplicationException;
import org.apache.thrift.TException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.Silent.class)
public class WithdrawServiceImplUnitTest {

    private static final String HOST = "http://127.0.0.1:18888";
    private static final String URI = "/platform_recharge_service/thrift/withdrawService";
    private static final String TOKEN = "159f3d79d02e020a0182f1fa944afd1dd0c0014c33aae6cb589554b88ff87ad1";

    /**
     * 绑定支付宝提现
     * kugouId: **********, openid: osVWIjimHCDYGdgCuf_0apng8xCM
     */
    @Test(expected = TApplicationException.class)
    public void bindAccountInfo() throws TException {
        AccountChangeRequest request = new AccountChangeRequest()
                .setOrderId(generateGlobalId())
                .setAppId(1000_0002)
                .setKugouId(**********)
                .setAccount("***********")
                .setRealName("刘守凯")
                .setToken(TOKEN)
                .setRequestTime(DateHelper.getCurrentSeconds())
                .setIp("*********");
        request.setSign(FinanceSignUtils.makeSign(request, "49e6qxHDhmkfqDyU"));
        WithdrawService.Client service = new WithdrawService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.bindAccountInfo(request);
    }

    /**
     * 绑定微信提现
     * kugouId: **********, openid: osVWIjimHCDYGdgCuf_0apng8xCM
     */
    @Test(expected = TApplicationException.class)
    public void bindAccountInfoV2ForWechat() throws TException {
        AccountChangeV2Request request = new AccountChangeV2Request()
                .setOrderId(generateGlobalId())
                .setAppId(1000_0002)
                .setBizAppId(10015)
                .setKugouId(**********)
                .setOpenid("osVWIjimHCDYGdgCuf_0apng8xCM")
                .setNickname("Kay")
                .setAvatar("https://s.gravatar.com/avatar/15672e33593d49818cfab30ed2b1f578?s=80")
                .setAccount("")
                .setRealName("")
                .setToken(TOKEN)
                .setRequestTime(DateHelper.getCurrentSeconds())
                .setIp("127.0.0.1");
        request.setSign(FinanceSignUtils.makeSign(request, "49e6qxHDhmkfqDyU"));
        WithdrawService.Client service = new WithdrawService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.bindAccountInfoV2(request);
    }

    /**
     * 绑定微信提现
     * kugouId: **********, openid: osVWIjimHCDYGdgCuf_0apng8xCM
     */
    @Test(expected = TApplicationException.class)
    public void bindAccountInfoV2ForAlipay() throws TException {
        AccountChangeV2Request request = new AccountChangeV2Request()
                .setOrderId(generateGlobalId())
                .setAppId(1000_0002)
                .setBizAppId(10015)
                .setKugouId(**********)
                .setOpenid("")
                .setNickname("")
                .setAvatar("")
                .setAccount("***********")
                .setRealName("刘守凯")
                .setToken(TOKEN)
                .setRequestTime(DateHelper.getCurrentSeconds())
                .setIp("127.0.0.1");
        request.setSign(FinanceSignUtils.makeSign(request, "49e6qxHDhmkfqDyU"));
        WithdrawService.Client service = new WithdrawService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.bindAccountInfoV2(request);
    }

    @Test(expected = TApplicationException.class)
    public void queryAccountInfoByKugouId() throws TException {
        WithdrawAccountQueryRequest request = new WithdrawAccountQueryRequest()
                .setAppId(1000_0002)
                .setKugouId(**********)
                .setReqTime(DateHelper.getCurrentSeconds());
        WithdrawService.Client service = new WithdrawService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.queryAccountInfoByKugouId(request);
    }

    @Test(expected = TApplicationException.class)
    public void queryWechatAccountInfoByKugouId() throws TException {
        WechatAccountQueryRequest request = new WechatAccountQueryRequest()
                .setAppId(1000_0002)
                .setKugouId(**********)
                .setBizAppId(10015)
                .setReqTime(DateHelper.getCurrentSeconds());
        WithdrawService.Client service = new WithdrawService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.queryWechatAccountInfoByKugouId(request);
    }

    @Test(expected = TApplicationException.class)
    public void createWithdrawOrder() throws TException {
        CreateWithdrawOrderRequest request = new CreateWithdrawOrderRequest();
        request.setOrderId(generateGlobalId());
        request.setAppId(1000_0002);
        request.setBizAppId(1084);
        request.setKugouId(**********);
        request.setTotalAmount(10);
        request.setPid(0);
        request.setReqTime(DateHelper.getCurrentSeconds());
        request.setDrawTime(DateHelper.getCurrentSeconds() + 120);
        request.setClientIp("127.0.0.1");
        request.setDfid("-");
        request.setUuid("");
        request.setMid("FFFF");
        request.setClientver("0");
        request.setApplicationId("1010");
        request.setToken(TOKEN);
        request.setSign(FinanceSignUtils.makeSign(request, "49e6qxHDhmkfqDyU"));
        WithdrawService.Client service = new WithdrawService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.createWithdrawOrder(request);
    }

    @Test(expected = TApplicationException.class)
    public void createWithdrawOrderV2ForWechat() throws TException {
        CreateWithdrawOrderV2Request request = new CreateWithdrawOrderV2Request();
        request.setOrderId(generateGlobalId());
        request.setAppId(1000_0008);
        request.setBizAppId(10028);
        request.setKugouId(**********);
        request.setTotalAmount(100);
        request.setPid(0);
        request.setDrawType(DrawTypeEnum.DRAW_TYPE_WECHAT.getCode());
        request.setReqTime(DateHelper.getCurrentSeconds());
        request.setDrawTime(DateHelper.getCurrentSeconds() + 60);
        request.setClientIp("*********");
        request.setDfid("-");
        request.setUuid("");
        request.setMid("FFFF");
        request.setClientver("0");
        request.setApplicationId("1010");
        request.setToken(TOKEN);
        request.setSign(FinanceSignUtils.makeSign(request, "WB6mjkZPNKrzDv6G"));
        WithdrawService.Client service = new WithdrawService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.createWithdrawOrderV2(request);
    }

    @Test(expected = TApplicationException.class)
    public void createWithdrawOrderV2ForStWechat() throws TException {
        CreateWithdrawOrderV2Request request = new CreateWithdrawOrderV2Request();
        request.setOrderId(generateGlobalId());
        request.setAppId(10000011);
        request.setBizAppId(10028);
        request.setKugouId(**********);
        request.setTotalAmount(30);
        request.setPid(0);
        request.setDrawType(DrawTypeEnum.DRAW_TYPE_ST_WECHAT.getCode());
        request.setReqTime(DateHelper.getCurrentSeconds());
        request.setDrawTime(DateHelper.getCurrentSeconds() + 60);
        request.setClientIp("*********");
        request.setDfid("-");
        request.setUuid("");
        request.setMid("FFFF");
        request.setClientver("0");
        request.setApplicationId("1010");
        request.setToken(TOKEN);
        request.setSign(FinanceSignUtils.makeSign(request, "GX5UNCAQU4NV8W"));
        WithdrawService.Client service = new WithdrawService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.createWithdrawOrderV2(request);
    }

    @Test(expected = TApplicationException.class)
    public void createWithdrawOrderV2ForAlipay() throws TException {
        CreateWithdrawOrderV2Request request = new CreateWithdrawOrderV2Request();
        request.setOrderId(generateGlobalId());
        request.setDrawType(DrawTypeEnum.DRAW_TYPE_ALIPAY.getCode());
        request.setAppId(1000_0002);
        request.setBizAppId(1084);
        request.setKugouId(**********);
        request.setTotalAmount(10);
        request.setPid(0);
        request.setReqTime(DateHelper.getCurrentSeconds());
        request.setDrawTime(DateHelper.getCurrentSeconds() + 60);
        request.setClientIp("*********43");
        request.setDfid("-");
        request.setUuid("");
        request.setMid("FFFF");
        request.setClientver("0");
        request.setApplicationId("1010");
        request.setToken(TOKEN);
        request.setSign(FinanceSignUtils.makeSign(request, "49e6qxHDhmkfqDyU"));
        WithdrawService.Client service = new WithdrawService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.createWithdrawOrderV2(request);
    }

    @Test(expected = TApplicationException.class)
    public void queryWithdrawOrder() throws TException {
        QueryWithdrawOrderRequest request = new QueryWithdrawOrderRequest();
        request.setAppId(1000_0002);
        request.setKugouId(**********L);
        request.setOrderId(525711044425814015L);
        request.setReqTime(DateHelper.getCurrentSeconds());
        request.setSign(FinanceSignUtils.makeSign(request, "49e6qxHDhmkfqDyU"));
        WithdrawService.Client service = new WithdrawService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.queryWithdrawOrder(request);
    }

    @Test(expected = TApplicationException.class)
    public void getKugouIdByAliPayAccount() throws TException {
        QueryWithdrawKugouIdRequest request = new QueryWithdrawKugouIdRequest();
        request.setAppId(1000_0002);
        request.setAccount("***********");
        request.setReqTime(DateHelper.getCurrentSeconds());
        request.setSign(FinanceSignUtils.makeSign(request, "49e6qxHDhmkfqDyU"));
        WithdrawService.Client service = new WithdrawService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.getKugouIdByAliPayAccount(request);
    }

    private long generateGlobalId() {
        return new SnowFlake(0, 0).nextId();
    }
}
