package com.kugou.fanxing.recharge.controller.thrift;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Sets;
import com.kugou.api.springcloud.GlobalIdServiceThrift.GlobalIdService;
import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.biz.commons.util.SnowFlake;
import com.kugou.fanxing.coupon.thrift.CouponService;
import com.kugou.fanxing.coupon.thrift.UnFreezeVO;
import com.kugou.fanxing.recharge.constant.ApplyTypeEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.thrift.*;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.PlatformStrategyService;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.StrategyVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.thrift.TApplicationException;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class RechargeServiceImplUnitTest {

    private static final String HOST = "http://127.0.0.1:18888";
    private static final String URI = "/platform_recharge_service/thrift/rechargeService";

    @InjectMocks
    private ApolloConfigService apolloConfigService;

    @Test(expected = TApplicationException.class)
    public void queryUserRechargeList() throws TException {

        GlobalIdService.Client g = new GlobalIdService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, "/service"));
        g.get();

//        StrategyVO vo = new StrategyVO()
//                .setSid("")
//                .setEndtype("")
//                .setAppid("1010")
//                .setKugouId(1290249156L)
//                .setRoomId(1)
//                .setBiz("recharge")
//                .setDeviceId("")
//                .setIp("**************");
//        PlatformStrategyService.Client service2 = new PlatformStrategyService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
//        service2.conclude(vo);
    }

    @Test
    public void checkSign() {
        QueryUserRechargeListRequest request = new QueryUserRechargeListRequest()
                .setKugouId(129994690L)
                .setAppId(1000_0001)
                .setMonth("202002")
                .setPage(1)
                .setPageSize(12)
                .setPid(0)
                .setTimestamp(1582526244);
        log.warn("expected sign: {}, actual sign: {}", "872ae01686f5c54e3e8db6b5d2136cbd", FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXs"));
        Assert.assertEquals("872ae01686f5c54e3e8db6b5d2136cbd", FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXs"));
    }

    @Test(expected = TApplicationException.class)
    public void queryOfflineRechargeApproveList() throws TException {
        QueryApproveListRequest request = new QueryApproveListRequest()
                .setApplyType(ApplyTypeEnum.APPLY_TYPE_3.getStatus())
                .setBatchNo(1373979005241481543L)
                .setRechargeOrderNum("")
                .setPage(1)
                .setPageSize(12);
        RechargeService.Client service = new RechargeService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.queryOfflineRechargeApproveList(request);
    }

    @Test(expected = TApplicationException.class)
    public void createOrderForPurchaseProduct() throws TException {
        PurchaseProductRequest request = new PurchaseProductRequest()
                .setBusinessId("10000001")
                .setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_3.getPayTypeId())
                .setBusinessTime(DateHelper.getCurrentSeconds())
                .setKugouId(1290249156L)
                .setToKugouId(1290249156L)
                .setSubject("购买演唱会门票")
                .setAmount(new BigDecimal("0.01").stripTrailingZeros().toPlainString())
                .setSyncUrl("")
                .setTopic("fx.buyConcertTicket")
                .setClientIp("127.0.0.1")
                .setPid(2)
                .setCouponId(0)
                .setOpenId("")
                .setExtJson(JSON.toJSONString(ImmutableMap.<String, String>builder().put("concertId", "6899").build()))
                .setRedirectUrl("")
                .setConsumeParam(new ConsumeParam()
                        .setAccountChangeType(110209)
                        .setFromKugouId(1290249156L)
                        .setToKugouId(0L)
                        .setGiftId(0)
                        .setGiftName("演唱会门票")
                        .setGiftNum(1)
                        .setRoomId(0)
                        .setExt(JSON.toJSONString(ImmutableMap.<String, String>builder().put("concertId", "6899").build())));
        request.setSign(FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXs"));
        RechargeService.Client service = new RechargeService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.createOrderForPurchaseProduct(request);
    }

    @Test(expected = TApplicationException.class)
    public void createOrderForPurchaseProductV2() throws TException {
        PurchaseOrder purchaseOrder = new PurchaseOrder();
        purchaseOrder.setAccountChangeType(110223);
        purchaseOrder.setFromKugouId(1290249156L);
        purchaseOrder.setToKugouId(1290249156L);
        purchaseOrder.setGoodsId(0);
        purchaseOrder.setGoodsNum(100);
        purchaseOrder.setGoodsType(3);
        purchaseOrder.setExt("");
        List<PurchaseOrder> orderList = new ArrayList<>();
        orderList.add(purchaseOrder);
        PurchaseProductRequestV2 request = new PurchaseProductRequestV2()
                .setBusinessId("********")
                .setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_39.getPayTypeId())
                .setBusinessTime(DateHelper.getCurrentSeconds())
                .setKugouId(1290249156L)
                .setSubject("购买么么哒礼物")
                .setAmount(new BigDecimal("0.01").stripTrailingZeros().toPlainString())
                .setSyncUrl("")
                .setClientIp("127.0.0.1")
                .setPid(2)
                .setOpenId("")
                .setExtJson(JSON.toJSONString(ImmutableMap.of("concertId", "6899", "frontQrCode", 1)))
                .setOrderList(orderList)
                .setRedirectUrl("");
        request.setSign(FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXa"));
        RechargeService.Client service = new RechargeService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.createOrderForPurchaseProductV2(request);
    }

    @Test(expected = TApplicationException.class)
    public void createOrderForPurchaseCurrencyAlipay() throws TException {
        PurchaseCurrencyRequest request = new PurchaseCurrencyRequest()
                .setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_30.getPayTypeId())
                .setBusinessId(10000010)
                .setOrderNo(String.valueOf(new SnowFlake(0, 0).nextId()))
                .setOrderTime(System.currentTimeMillis())
                .setOrderExpireTime(System.currentTimeMillis())
                .setCoinType(2)
                .setDescription("充值唱币")
                .setKugouId(1290249156L)
                .setAmount(new BigDecimal("0.01").stripTrailingZeros().toPlainString())
                .setClientIp("127.0.0.1")
                .setRoomId(0)
                .setCouponId(0)
                .setStdPlat(60)
                .setAppVersion("1");
        request.setSign(FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXi"));
        RechargeService.Client service = new RechargeService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.createOrderForPurchaseCurrency(request);
    }

    /**
     * request: PurchaseCurrencyRequest(businessId:10000010,
     * orderNo:s20230922175879649742023699284, orderTime:1695376737, payTypeId:40, description:微信支付测试, amount:1, coinType:2, kugouId:2023699284, clientIp:***********, stdPlat:60, roomId:0, sign:adf505d39073d95cd2220a62e3b5ddc1)?
     * @throws TException
     */
    @Test(expected = TApplicationException.class)
    public void createOrderForPurchaseCurrencyWechat() throws TException {
        PurchaseCurrencyRequest request = new PurchaseCurrencyRequest()
                .setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_40.getPayTypeId())
                .setBusinessId(10000010)
                .setOrderNo("s20230922175879649742023699284")
                .setOrderTime(System.currentTimeMillis())
                .setOrderExpireTime(System.currentTimeMillis())
                .setCoinType(2)
                .setDescription("微信支付测试")
                .setKugouId(2023699284L)
                .setAmount(new BigDecimal("1").stripTrailingZeros().toPlainString())
                .setClientIp("***********")
                .setRoomId(0)
                .setCouponId(0)
                .setStdPlat(60)
                .setAppVersion("1");
        request.setSign(FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXi"));
        RechargeService.Client service = new RechargeService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.createOrderForPurchaseCurrency(request);
    }


    @SneakyThrows
    @Test(expected = TApplicationException.class)
    public void queryBizOrder() {
        QueryBizOrderRequest request = new QueryBizOrderRequest()
                .setBusinessId(10000010)
                .setOrderNo("s20230922175879649742023699284");
        request.setSign(FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXi"));
        RechargeService.Client service = new RechargeService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.queryBizOrder(request);
    }

    @SneakyThrows
    @Test(expected = TApplicationException.class)
    public void queryRechargeList() {
        QueryRechargeListRequest request = new QueryRechargeListRequest()
                .setBusinessId(10000010)
                .setBeginTime(1696650354)
                .setEndTime(1696660497)
                .setLastRechargeId(1821943990585664071L)
                .setCoinTypes(Sets.newHashSet(2))
                .setBatchSize(2);
        request.setSign(FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXi"));
        RechargeService.Client service = new RechargeService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.queryRechargeList(request);
    }

    @Test(expected = TApplicationException.class)
    public void createOrderForPurchaseCurrencyWechatGzh() throws TException {
        PurchaseCurrencyRequest request = new PurchaseCurrencyRequest()
                .setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_41.getPayTypeId())
                .setBusinessId(10000010)
                .setOrderNo(String.valueOf(new SnowFlake(0, 0).nextId()))
                .setOrderTime(System.currentTimeMillis())
                .setOrderExpireTime(System.currentTimeMillis())
                .setCoinType(2)
                .setDescription("充值唱币")
                .setKugouId(1290249156L)
                .setAmount(new BigDecimal("0.01").stripTrailingZeros().toPlainString())
                .setClientIp("127.0.0.1")
                .setRoomId(0)
                .setCouponId(0)
                .setOpenid("ov0ghwZAxMk86iNjph-Os-rR0y3A")
                .setStdPlat(316)
                .setAppVersion("1");
        request.setSign(FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXi"));
        RechargeService.Client service = new RechargeService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.createOrderForPurchaseCurrency(request);
    }


    @Test(expected = TApplicationException.class)
    public void createOrderForPurchaseCurrencyAppStore() throws TException {
        PurchaseCurrencyRequest request = new PurchaseCurrencyRequest()
                .setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_1006.getPayTypeId())
                .setBusinessId(10000010)
                .setOrderNo("s2023101119356148056644671045")
                .setOrderTime(1697024126)
                .setOrderExpireTime(System.currentTimeMillis())
                .setCoinType(2)
                .setDescription("ios支付测试")
                .setKugouId(1290249156L)
                .setAmount("6")
                .setClientIp("***********")
                .setRoomId(0)
                .setCouponId(0)
                .setProductId("com.chang.kcoin.6yuan")
                .setAppVersion("45000")
                .setStdPlat(61);
        request.setSign(FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXi"));
        RechargeService.Client service = new RechargeService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.createOrderForPurchaseCurrency(request);
    }

    @Test(expected = TApplicationException.class)
    public void purchaseRenewBag() throws TException {
        PurchaseOrder purchaseOrder = new PurchaseOrder();
        purchaseOrder.setAccountChangeType(110403);
        purchaseOrder.setFromKugouId(1290249156L);
        purchaseOrder.setToKugouId(1290249156L);
        purchaseOrder.setGoodsId(0);
        purchaseOrder.setGoodsNum(1);
        purchaseOrder.setGoodsType(0);
        purchaseOrder.setExt("");
        Map<String, Object> extJsonMap = ImmutableMap.<String, Object>builder()
                .put("bizType", 1)
                .put("activityType", 110403)
                .put("roomId", 1020728)
                .put("gearId", 1)
                .put("bizNotifyUrl", "http://zuultest.fxwork.kugou.com/revenue_new_trial/revenue_new_trial/inner/recharge/renewbag/notify")
                .build();
        PurchaseProductRequestV2 request = new PurchaseProductRequestV2()
                .setBusinessId("********")
                .setBusinessTime(DateHelper.getCurrentSeconds())
                .setKugouId(1290249156L)
                .setSubject("购买续充礼包")
                .setAmount(new BigDecimal("0.01").stripTrailingZeros().toPlainString())
                .setSyncUrl("https://fx100.fxwork.kugou.com/")
                .setClientIp("127.0.0.1")
                .setPid(7)
                .setOpenId("")
                .setExtJson(JSON.toJSONString(extJsonMap))
                .setOrderList(Lists.newArrayList(purchaseOrder))
                .setRedirectUrl("https://fx100.fxwork.kugou.com/")
                .setShowUrl("https://fx100.fxwork.kugou.com/");
        request.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_39.getPayTypeId());
        request.setSign(FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXa"));
        RechargeService.Client service = new RechargeService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.createOrderForPurchaseProductV2(request);
    }

    @Test(expected = TApplicationException.class)
    public void createOrderForPurchaseProductForWechatMP() throws TException {
        PurchaseProductRequest request = new PurchaseProductRequest()
                .setBusinessId("10000001")
                .setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_42.getPayTypeId())
                .setBusinessTime(DateHelper.getCurrentSeconds())
                .setKugouId(1290249156L)
                .setToKugouId(1290249156L)
                .setSubject("购买演唱会门票")
                .setAmount(new BigDecimal("0.01").stripTrailingZeros().toPlainString())
                .setSyncUrl("")
                .setTopic("fx.buyConcertTicket")
                .setClientIp("127.0.0.1")
                .setPid(2)
                .setCouponId(0)
                .setOpenId("***********")
                .setExtJson(JSON.toJSONString(ImmutableMap.<String, String>builder().put("concertId", "6899").build()))
                .setRedirectUrl("")
                .setConsumeParam(new ConsumeParam()
                        .setAccountChangeType(110209)
                        .setFromKugouId(1290249156L)
                        .setToKugouId(0L)
                        .setGiftId(0)
                        .setGiftName("演唱会门票")
                        .setGiftNum(1)
                        .setRoomId(0)
                        .setExt(JSON.toJSONString(ImmutableMap.<String, String>builder().put("concertId", "6899").build())));
        request.setSign(FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXs"));
        RechargeService.Client service = new RechargeService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.createOrderForPurchaseProduct(request);
    }

    @Test(expected = TApplicationException.class)
    public void queryUserRenewalInfo() throws TException {
        QueryUserRenewalRequest request = new QueryUserRenewalRequest();
        request.setPid(2);
        request.setAppId(1000_0006);
        request.setKugouId(1290249156L);
        request.setBusinessId("10000004");
        request.setTimestamp(System.currentTimeMillis());
        request.setRenewalType(3);
        request.setSign(FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXs"));
        RechargeService.Client service = new RechargeService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.queryUserRenewalInfo(request);
    }

    @Test(expected = TApplicationException.class)
    public void cancelCoupon() throws TException {
        UnFreezeVO unFreezeVO = new UnFreezeVO()
                .setOrderId(new SnowFlake(0, 0).nextId())
                .setOriginOrderId(1544361445300319732L)
                .setKugouId(1133381928)
                .setPId(0)
                .setActionId(1019)
                .setReqTimestamp(DateHelper.getCurrentSeconds());
        unFreezeVO.setSign(FinanceSignUtils.makeSign(unFreezeVO, "17vmgkymub"));
        CouponService.Client service = new CouponService.Client(CurlTProtocolFactory.buildTJSONProtocol("http://***********:18090", "/platform_coupon_service/couponService"));
        service.cancel(unFreezeVO);
    }

    @Test(expected = TApplicationException.class)
    public void queryRechargeOrder() throws TException {
        QueryRechargeOrderRequest request = new QueryRechargeOrderRequest().setRechargeOrderNum("R092022020107111865243616");
        RechargeService.Client service = new RechargeService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.queryRechargeOrder(request);
    }

    @Test(expected = TApplicationException.class)
    public void queryRechargeOrderForIos() throws TException {
        QueryRechargeOrderRequest request = new QueryRechargeOrderRequest().setRechargeOrderNum("R09202202S1TID1000000976978980");
        RechargeService.Client service = new RechargeService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.queryRechargeOrder(request);
    }

    @Test(expected = TApplicationException.class)
    public void queryRechargeOrderByOrderNum() throws TException {
        QueryRechargeOrderByOrderNumRequest request = new QueryRechargeOrderByOrderNumRequest().setOrderNum("1609949002407703730");
        RechargeService.Client service = new RechargeService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.queryRechargeOrderByOrderNum(request);
    }

    @Test(expected = TApplicationException.class)
    public void iapNotification() throws TException {
        String notification;
        try (InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream("notification.json")) {
            notification = IOUtils.toString(is, StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new ContextedRuntimeException(e);
        }
        IapNotificationRequest request = new IapNotificationRequest();
        request.setBusinessId(10000010);
        request.setNotification(notification);
        request.setSign(FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXi"));
        RechargeService.Client service = new RechargeService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.iapNotification(request);
    }
}
