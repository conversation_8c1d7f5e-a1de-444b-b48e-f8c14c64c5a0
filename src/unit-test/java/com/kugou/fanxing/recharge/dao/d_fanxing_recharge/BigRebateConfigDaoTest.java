package com.kugou.fanxing.recharge.dao.d_fanxing_recharge;

import com.kugou.fanxing.recharge.model.po.offline.BigRebateConfig;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.session.SqlSession;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

/**
 * Forbidden using spring container for unit test.
 *
 * <AUTHOR>
 */
public class BigRebateConfigDaoTest extends BaseDaoTest {
    @Test
    public void queryAll() {
        try (SqlSession sqlSession = getSqlSessionFactory().openSession()) {
            BigRebateConfigDao bigRebateConfigDao = sqlSession.getMapper(BigRebateConfigDao.class);
            List<BigRebateConfig> bigRebateConfigList = bigRebateConfigDao.queryAll();
            Assert.assertNotNull(bigRebateConfigList);
            Assert.assertTrue(CollectionUtils.isNotEmpty(bigRebateConfigList));
        }
    }
}