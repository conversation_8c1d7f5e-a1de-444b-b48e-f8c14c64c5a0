package com.kugou.fanxing.recharge.dao.d_fanxing_recharge;

import com.kugou.fanxing.recharge.model.po.offline.BigRechargeOrder;
import org.apache.commons.lang3.RandomUtils;
import org.apache.ibatis.session.SqlSession;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.Timeout;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Set;

import static java.util.stream.Collectors.toSet;

public class BigRechargeOrderDaoTest extends BaseDaoTest {

    @Rule
    public Timeout globalTimeout = Timeout.seconds(10);

    @Test
    public void testRechargeOrder() throws IOException {
        SqlSession sqlSession = null;
        try {
            sqlSession = getSqlSessionFactory().openSession(false);
            BigRechargeOrderDao bigRechargeApplyDao = sqlSession.getMapper(BigRechargeOrderDao.class);
            List<BigRechargeOrder> bigRechargeOrders = buildBigRechargeApplyList();
            int affected = bigRechargeApplyDao.saveRecords(bigRechargeOrders);
            Assert.assertEquals(2, affected);

            Set<String> orderNumSet = bigRechargeOrders.stream().map(BigRechargeOrder::getOrderNum).collect(toSet());
            List<BigRechargeOrder> actual = bigRechargeApplyDao.query(orderNumSet, (short)1);
            Assert.assertEquals(2, actual.size());

            // 删除申请记录
            for (BigRechargeOrder bigRechargeOrder : bigRechargeOrders) {
                sqlSession.getMapper(GeneralDeleteDao.class).deleteById("t_recharge_big_recharge_order", "globalId", bigRechargeOrder.getGlobalId());
            }
            sqlSession.commit();
        } catch (Exception e) {
            if (sqlSession != null) {
                sqlSession.rollback();
            }
            throw e;
        } finally {
            if (sqlSession != null) { sqlSession.close(); }
        }
    }

    private List<BigRechargeOrder> buildBigRechargeApplyList() {
        Date now = new Date();
        List<BigRechargeOrder> bigRechargeOrders = Lists.newArrayList();
        BigRechargeOrder bigRechargeOrder1 = new BigRechargeOrder();
        bigRechargeOrder1.setGlobalId(RandomUtils.nextLong(1000000000000000000L, 2000000000000000000L));
        bigRechargeOrder1.setApplyUserId(0);
        bigRechargeOrder1.setApplyKugouId(100L);
        bigRechargeOrder1.setType((short)1);
        bigRechargeOrder1.setOrderNum("100000000000000");
        bigRechargeOrder1.setCreateTime(now);
        bigRechargeOrder1.setUpdateTime(now);
        bigRechargeOrders.add(bigRechargeOrder1);
        BigRechargeOrder bigRechargeOrder2 = new BigRechargeOrder();
        bigRechargeOrder2.setGlobalId(RandomUtils.nextLong(1000000000000000000L, 2000000000000000000L));
        bigRechargeOrder2.setApplyUserId(0);
        bigRechargeOrder2.setApplyKugouId(100L);
        bigRechargeOrder2.setType((short)1);
        bigRechargeOrder2.setOrderNum("200000000000000");
        bigRechargeOrder2.setCreateTime(now);
        bigRechargeOrder2.setUpdateTime(now);
        bigRechargeOrders.add(bigRechargeOrder2);
        return bigRechargeOrders;
    }

}