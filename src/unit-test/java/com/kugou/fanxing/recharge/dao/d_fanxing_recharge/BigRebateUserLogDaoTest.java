package com.kugou.fanxing.recharge.dao.d_fanxing_recharge;

import com.google.common.collect.Sets;
import com.kugou.fanxing.recharge.model.po.offline.BigRebateUserLog;
import org.apache.commons.lang3.RandomUtils;
import org.apache.ibatis.session.SqlSession;
import org.junit.Assert;
import org.junit.Test;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

public class BigRebateUserLogDaoTest extends BaseDaoTest {

    @Test
    public void testSaveRecord() throws IOException {
        SqlSession sqlSession = null;
        try {
            sqlSession = getSqlSessionFactory().openSession(false);
            long userId = 10086L;
            BigRebateUserLogDao bigRebateUserLogDao = sqlSession.getMapper(BigRebateUserLogDao.class);

            // 插入充值记录R1001与R1002
            BigRebateUserLog bigRebateUserLog1 = buildBigRebateUserLog(userId, "R1001", (short) 3);
            Assert.assertEquals(1, bigRebateUserLogDao.saveRecord(bigRebateUserLog1));
            BigRebateUserLog bigRebateUserLog2 = buildBigRebateUserLog(userId, "R1002", (short) 3);
            Assert.assertEquals(1, bigRebateUserLogDao.saveRecord(bigRebateUserLog2));

            // 查询充值记录R1001与R1002
            List<BigRebateUserLog> bigRebateUserLogList = bigRebateUserLogDao.query(userId, Sets.newHashSet("R1001", "R1002"));
            Assert.assertNotNull(bigRebateUserLogList);
            Assert.assertEquals(2, bigRebateUserLogList.size());

            // 更新充值记录R1001与R1002状态
            int affected = bigRebateUserLogDao.updateCombinedRecord(userId, Sets.newHashSet("R1001", "R1002"), 1);
            Assert.assertEquals("更新记录行数与预期一致", 2, affected);
            // 删除充值记录R1001与R1002
            sqlSession.getMapper(GeneralDeleteDao.class).deleteById("t_recharge_big_rebate_user_log", "id", bigRebateUserLog1.getId());
            sqlSession.getMapper(GeneralDeleteDao.class).deleteById("t_recharge_big_rebate_user_log", "id", bigRebateUserLog2.getId());

            sqlSession.commit();
        } catch (Exception e) {
            if (sqlSession != null) {
                sqlSession.rollback();
            }
            throw e;
        } finally {
            if (sqlSession != null) { sqlSession.close(); }
        }
    }

    @Test
    public void testUpdateSingleRebateRecord() throws IOException {
        SqlSession sqlSession = null;
        try {
            sqlSession = getSqlSessionFactory().openSession(false);
            long userId = 10091L;
            BigRebateUserLogDao bigRebateUserLogDao = sqlSession.getMapper(BigRebateUserLogDao.class);

            // 插入充值记录R1003
            BigRebateUserLog bigRebateUserLog = buildBigRebateUserLog(userId, "R1003", (short) 0);
            Assert.assertEquals(1, bigRebateUserLogDao.saveRecord(bigRebateUserLog));

            // 更新充值记录R1003
            int affected = bigRebateUserLogDao.updateSingleRebateRecord(userId, Sets.newHashSet("R1003"), bigRebateUserLog.getId());
            Assert.assertEquals("更新记录行数与预期一致", 1, affected);

            // 查询充值记录R1003
            List<BigRebateUserLog> bigRebateUserLogList = bigRebateUserLogDao.query(userId, Sets.newHashSet("R1003"));
            Assert.assertNotNull(bigRebateUserLogList);
            Assert.assertEquals(1, bigRebateUserLogList.size());
            Assert.assertEquals(bigRebateUserLog.getId(), bigRebateUserLogList.stream().findAny().orElseThrow(RuntimeException::new).getPid());

            // 删除充值记录R1003
            sqlSession.getMapper(GeneralDeleteDao.class).deleteById("t_recharge_big_rebate_user_log", "id", bigRebateUserLog.getId());

            sqlSession.commit();
        } catch (Exception e) {
            if (sqlSession != null) {
                sqlSession.rollback();
            }
            throw e;
        } finally {
            if (sqlSession != null) { sqlSession.close(); }
        }
    }

    private BigRebateUserLog buildBigRebateUserLog(long kugouId, String rechargeOrderNum, short status) {
        BigRebateUserLog bigRebateUserLog = new BigRebateUserLog();
        bigRebateUserLog.setId(RandomUtils.nextLong(1000000000000000000L, 2000000000000000000L));
        bigRebateUserLog.setPid(0);
        bigRebateUserLog.setAddTime(Math.toIntExact(System.currentTimeMillis() / 1000));
        bigRebateUserLog.setCheckId(1);
        bigRebateUserLog.setCheckTime(Math.toIntExact(System.currentTimeMillis() / 1000));
        bigRebateUserLog.setCoin(BigDecimal.valueOf(100));
        bigRebateUserLog.setCoinAward(BigDecimal.valueOf(10));
        bigRebateUserLog.setCoinFee(BigDecimal.valueOf(5));
        bigRebateUserLog.setMedalId(1);
        bigRebateUserLog.setMedalExpire(3);
        bigRebateUserLog.setMountId(1);
        bigRebateUserLog.setMountExpire(5);
        bigRebateUserLog.setCombineNum(2);
        bigRebateUserLog.setRechargeOrderNum(rechargeOrderNum);
        bigRebateUserLog.setUserId(0);
        bigRebateUserLog.setKugouId(kugouId);
        bigRebateUserLog.setAmount(BigDecimal.valueOf(100));
        bigRebateUserLog.setStatus(status);
        return bigRebateUserLog;
    }

}