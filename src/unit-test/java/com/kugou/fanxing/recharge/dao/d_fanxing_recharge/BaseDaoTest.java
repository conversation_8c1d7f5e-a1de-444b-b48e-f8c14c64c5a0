package com.kugou.fanxing.recharge.dao.d_fanxing_recharge;

import com.alibaba.druid.pool.DruidDataSource;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.mapping.Environment;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.apache.ibatis.transaction.TransactionFactory;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;

import javax.sql.DataSource;

public abstract class BaseDaoTest {

    private static final String TEST_DB_URL = "*************************************************************************************************************************";
    private static final String TEST_DB_USERNAME = "fanxing";
    private static final String TEST_DB_PASSWORD = "kugou2014";

    protected DataSource getRechargeDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setUrl(TEST_DB_URL);
        dataSource.setUsername(TEST_DB_USERNAME);
        dataSource.setPassword(TEST_DB_PASSWORD);
        return dataSource;
    }

    protected SqlSessionFactory getSqlSessionFactory() {
        TransactionFactory transactionFactory = new JdbcTransactionFactory();
        Environment environment = new Environment("development", transactionFactory, getRechargeDataSource());
        Configuration configuration = new Configuration(environment);
        configuration.getMapperRegistry().addMappers("com.kugou.fanxing.recharge.dao.d_fanxing_recharge");
        return new SqlSessionFactoryBuilder().build(configuration);
    }

    public interface GeneralDeleteDao {
        @Delete("delete from ${tableName} where ${primaryKey} = #{id}")
        int deleteById(@Param("tableName") String tableName, @Param("primaryKey") String primaryKey, @Param("id") long id);
    }

}
