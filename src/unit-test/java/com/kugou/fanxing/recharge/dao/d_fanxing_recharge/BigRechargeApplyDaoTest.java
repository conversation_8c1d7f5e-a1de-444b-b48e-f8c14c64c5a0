package com.kugou.fanxing.recharge.dao.d_fanxing_recharge;

import com.kugou.fanxing.recharge.constant.ApplyTypeEnum;
import com.kugou.fanxing.recharge.constant.ApproveStatusEnum;
import com.kugou.fanxing.recharge.model.po.offline.BigRechargeApply;
import com.kugou.fanxing.recharge.model.po.offline.BigRechargeApplyDetail;
import com.kugou.fanxing.recharge.model.vo.offline.ApplyInfoDetail;
import com.kugou.fanxing.recharge.util.Pagination;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.ibatis.session.SqlSession;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class BigRechargeApplyDaoTest extends BaseDaoTest {

    @Test
    public void testRechargeApply() throws IOException {
        SqlSession sqlSession = null;
        try {
            sqlSession = getSqlSessionFactory().openSession(false);
            BigRechargeApplyDao bigRechargeApplyDao = sqlSession.getMapper(BigRechargeApplyDao.class);
            BigRechargeApplyDetailDao bigRechargeApplyDetailDao = sqlSession.getMapper(BigRechargeApplyDetailDao.class);

            // 插入申请记录
            BigRechargeApply bigRechargeApply = buildBigRechargeApply(10086L, "100000000,200000000");
            Assert.assertEquals(1, bigRechargeApplyDao.addRecord(bigRechargeApply));
            List<BigRechargeApplyDetail> bigRechargeApplyDetails = buildBigRechargeApplyDetailList(bigRechargeApply.getBatchNo());
            Assert.assertEquals(2, bigRechargeApplyDao.addDetailRecord(bigRechargeApplyDetails));

            // 验证查询语句
            List<ApplyInfoDetail> bigRechargeApplyDetailList;
            long bigRechargeApplyDetailListCount;
            Pagination pagination = new Pagination.Builder(1, 10).build();
            bigRechargeApplyDetailList = bigRechargeApplyDao.getApplyDetailList(ApplyTypeEnum.APPLY_TYPE_2.getStatus(), bigRechargeApply.getBatchNo(), "", pagination);
            bigRechargeApplyDetailListCount = bigRechargeApplyDetailDao.getApplyDetailListCount(ApplyTypeEnum.APPLY_TYPE_2.getStatus(), bigRechargeApply.getBatchNo(), "");
            Assert.assertEquals(2, bigRechargeApplyDetailList.size());
            Assert.assertEquals(2, bigRechargeApplyDetailListCount);

            bigRechargeApplyDetailList = bigRechargeApplyDao.getApplyDetailList(ApplyTypeEnum.APPLY_TYPE_2.getStatus(), bigRechargeApply.getBatchNo(), bigRechargeApplyDetailList.get(0).getNewRechargeOrderNum(), pagination);
            bigRechargeApplyDetailListCount = bigRechargeApplyDetailDao.getApplyDetailListCount(ApplyTypeEnum.APPLY_TYPE_2.getStatus(), bigRechargeApply.getBatchNo(), bigRechargeApplyDetailList.get(0).getNewRechargeOrderNum());
            Assert.assertEquals(1, bigRechargeApplyDetailList.size());
            Assert.assertEquals(1, bigRechargeApplyDetailListCount);

            bigRechargeApplyDetailList = bigRechargeApplyDao.getApplyDetailList(ApplyTypeEnum.APPLY_TYPE_2.getStatus(), bigRechargeApply.getBatchNo(), "NOT EXISTS", pagination);
            bigRechargeApplyDetailListCount = bigRechargeApplyDetailDao.getApplyDetailListCount(ApplyTypeEnum.APPLY_TYPE_2.getStatus(), bigRechargeApply.getBatchNo(), "NOT EXISTS");
            Assert.assertEquals(0, bigRechargeApplyDetailList.size());
            Assert.assertEquals(0, bigRechargeApplyDetailListCount);

            List<BigRechargeApply> bigRechargeApplyList = bigRechargeApplyDao.getApplyListByKugouId(ApplyTypeEnum.APPLY_TYPE_2.getStatus(), bigRechargeApply.getApplyKugouId());
            Assert.assertEquals("验证指定用户申请大额充值订单数量仅有一笔",1, bigRechargeApplyList.size());

            // 验证更新语句
            int affected1 = bigRechargeApplyDao.updateRecord(bigRechargeApply.getBatchNo(), ApproveStatusEnum.APPROVE_STATUS_1.getStatus(), "审核通过");
            Assert.assertEquals("验证更新汇总记录行数与预期一致", 1, affected1);
            int affected2 = bigRechargeApplyDetailDao.updateRecord(bigRechargeApply.getBatchNo(), ApproveStatusEnum.APPROVE_STATUS_1.getStatus());
            Assert.assertEquals("验证更新明细记录行数与预期一致", 2, affected2);

            // 删除申请记录
            sqlSession.getMapper(GeneralDeleteDao.class).deleteById("t_recharge_big_recharge_apply", "batchNo", bigRechargeApply.getBatchNo());
            sqlSession.getMapper(GeneralDeleteDao.class).deleteById("t_recharge_big_recharge_apply_detail", "batchNo", bigRechargeApply.getBatchNo());

            sqlSession.commit();
        } catch (Exception e) {
            if (sqlSession != null) {
                sqlSession.rollback();
            }
            throw e;
        } finally {
            if (sqlSession != null) { sqlSession.close(); }
        }
    }

    @Test
    public void testRebateApply() throws IOException {
        SqlSession sqlSession = null;
        try {
            sqlSession = getSqlSessionFactory().openSession(false);
            BigRechargeApplyDao bigRechargeApplyDao = sqlSession.getMapper(BigRechargeApplyDao.class);

            // 插入申请记录
            BigRechargeApply bigRechargeApply = buildBigRebateApply(10087L, "R300000000,R400000000");
            Assert.assertEquals("验证插入记录行数与预期一致",1, bigRechargeApplyDao.addRecord(bigRechargeApply));
            Assert.assertNotNull(bigRechargeApplyDao.getRechargeApplyByBatchNo(bigRechargeApply.getBatchNo()));

            // 删除申请记录
            sqlSession.getMapper(GeneralDeleteDao.class).deleteById("t_recharge_big_recharge_apply", "batchNo", bigRechargeApply.getBatchNo());

            sqlSession.commit();
        } catch (Exception e) {
            if (sqlSession != null) {
                sqlSession.rollback();
            }
            throw e;
        } finally {
            if (sqlSession != null) { sqlSession.close(); }
        }
    }

    private BigRechargeApply buildBigRebateApply(long userId, String orderNums) {
        Date now = new Date();
        BigRechargeApply bigRechargeApply = new BigRechargeApply();
        bigRechargeApply.setBatchNo(RandomUtils.nextLong(1000000000000000000L, 2000000000000000000L));
        bigRechargeApply.setApplyType(ApplyTypeEnum.APPLY_TYPE_1.getStatus());
        bigRechargeApply.setUserId(userId);
        bigRechargeApply.setQq("********");
        bigRechargeApply.setPhone("***********");
        bigRechargeApply.setOrderNums(orderNums);
        bigRechargeApply.setApproveStatus(ApproveStatusEnum.APPROVE_STATUS_0.getStatus());
        bigRechargeApply.setAlipayAccount(StringUtils.EMPTY);
        bigRechargeApply.setImagePath(StringUtils.EMPTY);
        bigRechargeApply.setJsonExtend("");
        bigRechargeApply.setCreateTime(now);
        bigRechargeApply.setUpdateTime(now);
        return bigRechargeApply;
    }

    private BigRechargeApply buildBigRechargeApply(long applyKugouId, String orderNums) {
        Date now = new Date();
        BigRechargeApply bigRechargeApply = new BigRechargeApply();
        bigRechargeApply.setBatchNo(RandomUtils.nextLong(1000000000000000000L, 2000000000000000000L));
        bigRechargeApply.setApplyType(ApplyTypeEnum.APPLY_TYPE_2.getStatus());
        bigRechargeApply.setApplyKugouId(applyKugouId);
        bigRechargeApply.setApplyUserId(0);
        bigRechargeApply.setUserId(12397129847L);
        bigRechargeApply.setQq("********");
        bigRechargeApply.setPhone("***********");
        bigRechargeApply.setOrderNums(orderNums);
        bigRechargeApply.setApproveStatus(ApproveStatusEnum.APPROVE_STATUS_0.getStatus());
        bigRechargeApply.setAlipayAccount("***********");
        bigRechargeApply.setImagePath("helpcenter/********/IN********141449EImd.png");
        bigRechargeApply.setJsonExtend("");
        bigRechargeApply.setCreateTime(now);
        bigRechargeApply.setUpdateTime(now);
        return bigRechargeApply;
    }

    public List<BigRechargeApplyDetail> buildBigRechargeApplyDetailList(long batchNo) {
        BigRechargeApplyDetail bigRechargeApplyDetail1 = new BigRechargeApplyDetail();
        bigRechargeApplyDetail1.setId(RandomUtils.nextLong(1000000000000000000L, 2000000000000000000L));
        bigRechargeApplyDetail1.setBatchNo(batchNo);
        bigRechargeApplyDetail1.setToUserId(100000L);
        bigRechargeApplyDetail1.setToUserName("test1");
        bigRechargeApplyDetail1.setToUserCoin(BigDecimal.valueOf(100));
        bigRechargeApplyDetail1.setToUserAmount(BigDecimal.valueOf(1));
        bigRechargeApplyDetail1.setStatus(ApproveStatusEnum.APPROVE_STATUS_0.getStatus());
        bigRechargeApplyDetail1.setNewRechargeOrderNum("R500");

        BigRechargeApplyDetail bigRechargeApplyDetail2 = new BigRechargeApplyDetail();
        bigRechargeApplyDetail2.setId(RandomUtils.nextLong(1000000000000000000L, 2000000000000000000L));
        bigRechargeApplyDetail2.setBatchNo(batchNo);
        bigRechargeApplyDetail2.setToUserId(200000L);
        bigRechargeApplyDetail2.setToUserName("test2");
        bigRechargeApplyDetail2.setToUserCoin(BigDecimal.valueOf(100));
        bigRechargeApplyDetail2.setToUserAmount(BigDecimal.valueOf(1));
        bigRechargeApplyDetail2.setStatus(ApproveStatusEnum.APPROVE_STATUS_0.getStatus());
        bigRechargeApplyDetail2.setNewRechargeOrderNum("R600");
        return Lists.newArrayList(bigRechargeApplyDetail1, bigRechargeApplyDetail2);
    }
}