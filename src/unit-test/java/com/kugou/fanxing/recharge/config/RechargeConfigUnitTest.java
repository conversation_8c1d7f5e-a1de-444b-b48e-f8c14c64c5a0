package com.kugou.fanxing.recharge.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.kugou.config.Env;
import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.model.bo.KupayAppTypeInfoBO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.util.SpringContextUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class RechargeConfigUnitTest {

    private static final String PROD_PREFIX_BJ = "http://fxbj.kgidc.cn/gwphp/platform_recharge_service";
    private static final String PROD_PREFIX_GZ = "http://fxgz.kgidc.cn/gwphp/platform_recharge_service";

    @Mock
    private Env env;
    @Mock
    private ApolloConfigService apolloConfigService;
    @InjectMocks
    private RechargeConfig rechargeConfig;
    @Mock
    private HttpServletRequest request;

    @Before
    public void init() {
        ReflectionTestUtils.setField(rechargeConfig, "dataCenter", "bj-sjq");
    }

    @Test
    public void getNotifyUrl() {
        when(env.isDev()).thenReturn(true);
        when(apolloConfigService.getBjZuulAddress()).thenReturn("zuultest.fxwork.kugou.com");
        when(apolloConfigService.getGzZuulAddress()).thenReturn("zuultest.fxwork.kugou.com");
        String notifyUrl0 = rechargeConfig.getNotifyUrl(ReTypeEnum.RETYPE_RECHARGE);
        String notifyUrl1 = rechargeConfig.getNotifyUrl(ReTypeEnum.RETYPE_PURCHASE);
        Assert.assertEquals("http://zuultest.fxwork.kugou.com/platform_recharge_service/intranet/api/v1/purchase/callBackCoin", notifyUrl0);
        Assert.assertEquals("http://zuultest.fxwork.kugou.com/platform_recharge_service/intranet/api/v1/purchase/callbackGoods", notifyUrl1);
       try{
            rechargeConfig.getNotifyUrl(ReTypeEnum.RETYPE_RENEWALS);
       }catch (Exception e)
       {
           Assert.assertEquals(SysResultCode.RECHARGE_MISS_NOTIFY_URL.getMsg(), e.getMessage());
       }
    }

    @Test
    public void getRechargeNotifyUrlDev() {
        String testPrefix = "http://zuultest.fxwork.kugou.com/platform_recharge_service";
        when(env.isDev()).thenReturn(true);
        when(apolloConfigService.getBjZuulAddress()).thenReturn("zuultest.fxwork.kugou.com");
        when(apolloConfigService.getGzZuulAddress()).thenReturn("zuultest.fxwork.kugou.com");
        String notifyUrl0 = rechargeConfig.getRechargeNotifyUrl(ReTypeEnum.RETYPE_RECHARGE);
        String notifyUrl1 = rechargeConfig.getRechargeNotifyUrl(ReTypeEnum.RETYPE_PURCHASE);
        Assert.assertEquals(testPrefix + UrlConstants.RECHARGE_CALLBACK_COIN, notifyUrl0);
        Assert.assertEquals(testPrefix + UrlConstants.RECHARGE_CALLBACK_GOODS, notifyUrl1);
        when(apolloConfigService.enableRechargeZuulUrlPrefix()).thenReturn(true);
        rechargeConfig.getRechargeNotifyUrl(ReTypeEnum.RETYPE_PURCHASE);
    }

    @Test
    public void getRechargeNotifyUrlProd() {
        when(env.isDev()).thenReturn(false);
        when(env.isTest()).thenReturn(false);
        when(env.isProd()).thenReturn(true);
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            when(apolloConfigService.getBjZuulAddress()).thenReturn("fxbj.kgidc.cn/gwphp");
            when(apolloConfigService.getGzZuulAddress()).thenReturn("fxgz.kgidc.cn/gwphp");
            String notifyUrl3 = rechargeConfig.getRechargeNotifyUrl(ReTypeEnum.RETYPE_RECHARGE);
            String notifyUrl4 = rechargeConfig.getRechargeNotifyUrl(ReTypeEnum.RETYPE_PURCHASE);
            Assert.assertEquals(PROD_PREFIX_BJ + UrlConstants.RECHARGE_CALLBACK_COIN, notifyUrl3);
            Assert.assertEquals(PROD_PREFIX_BJ + UrlConstants.RECHARGE_CALLBACK_GOODS, notifyUrl4);
            String notifyUrl5 = rechargeConfig.getRechargeNotifyUrl(ReTypeEnum.RETYPE_RECHARGE);
            String notifyUrl6 = rechargeConfig.getRechargeNotifyUrl(ReTypeEnum.RETYPE_PURCHASE);
            Assert.assertEquals(PROD_PREFIX_BJ + UrlConstants.RECHARGE_CALLBACK_COIN, notifyUrl5);
            Assert.assertEquals(PROD_PREFIX_BJ + UrlConstants.RECHARGE_CALLBACK_GOODS, notifyUrl6);
            Assert.assertTrue(notifyUrl3.startsWith("http"));
            Assert.assertTrue(notifyUrl4.startsWith("http"));
            Assert.assertTrue(notifyUrl5.startsWith("http"));
            Assert.assertTrue(notifyUrl6.startsWith("http"));
        }
    }

    @Test
    public void getMPayList() {
        when(apolloConfigService.getPayTypeList()).thenReturn(Lists.newArrayList(30, 40, 35));
        List<Integer> filteredPayTypeList = rechargeConfig.getMPayList(5, 9203);
        Assert.assertTrue(filteredPayTypeList.contains(30));
        Assert.assertTrue(filteredPayTypeList.contains(40));
        Assert.assertFalse(filteredPayTypeList.contains(35));
    }

    @Test
    public void getKupayAppTypeInfoByPid() {
        String json = "[{\"pid\":35,\"appType\":\"fast\",\"appName\":\"酷狗直播极速版\"}," +
                "{\"pid\":38,\"appType\":\"hwbp\",\"appName\":\"华为白牌\"}," +
                "{\"pid\":60,\"appType\":\"kgcc\",\"appName\":\"酷狗唱唱\"}," +
                "{\"pid\":52,\"appType\":\"tzzb\",\"appName\":\"团子直播\"}," +
                "{\"pid\":56,\"appType\":\"fxradar\",\"appName\":\"浮浮雷达\"}]";
        TypeReference<List<KupayAppTypeInfoBO>> reference = new TypeReference<List<KupayAppTypeInfoBO>>() {};
        List<KupayAppTypeInfoBO> kupayAppTypeInfoBOList = JSON.parseObject(json, reference);
        when(this.apolloConfigService.getKupayAppTypeInfoList()).thenReturn(kupayAppTypeInfoBOList);
        Assert.assertEquals("fast", this.rechargeConfig.getKupayAppTypeInfoByPid(35));
        Assert.assertEquals("hwbp", this.rechargeConfig.getKupayAppTypeInfoByPid(38));
        Assert.assertEquals("kgcc", this.rechargeConfig.getKupayAppTypeInfoByPid(60));
        Assert.assertEquals("tzzb", this.rechargeConfig.getKupayAppTypeInfoByPid(52));
        Assert.assertEquals("fxradar", this.rechargeConfig.getKupayAppTypeInfoByPid(56));
        Assert.assertEquals("fanxing", this.rechargeConfig.getKupayAppTypeInfoByPid(10086));
    }

    @Test
    public void useKupayAppTypeDirectly() {
        when(this.apolloConfigService.useKupayAppTypeDirectly(anyString())).thenReturn(true);
        Assert.assertTrue(this.rechargeConfig.useKupayAppTypeDirectly("player2"));
        Assert.assertFalse(this.rechargeConfig.useKupayAppTypeDirectly(""));
        when(this.apolloConfigService.useKupayAppTypeDirectly(anyString())).thenReturn(false);
        Assert.assertFalse(this.rechargeConfig.useKupayAppTypeDirectly("player2"));
    }

    @Test
    public void getUnionPayPath() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            when(request.getParameter(eq("union_ver"))).thenReturn("2");
            Assert.assertEquals("/v1/unionpay_v2?", this.rechargeConfig.getUnionPayPath());
            when(request.getParameter(eq("union_ver"))).thenReturn(null);
            Assert.assertEquals("/v1/unionpay?", this.rechargeConfig.getUnionPayPath());
        }
    }

    @Test
    public void getOpenNotifyUrl() {
        String testPrefix = "http://zuultest.fxwork.kugou.com/platform_recharge_service";
        when(env.isDev()).thenReturn(true);
        when(apolloConfigService.getBjZuulAddress()).thenReturn("zuultest.fxwork.kugou.com");
        when(apolloConfigService.getGzZuulAddress()).thenReturn("zuultest.fxwork.kugou.com");
        String notifyUrl0 = this.rechargeConfig.getOpenNotifyUrl();
        Assert.assertEquals(testPrefix + UrlConstants.KUGOU_OPEN_PURCHASE_CALLBACK, notifyUrl0);
    }

    @Test
    public void getRechargeZuulNotifyUrl() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            when(env.isProd()).thenReturn(true);
            String uri = "/" + RandomStringUtils.randomAlphabetic(10);
            when(apolloConfigService.getBjZuulAddress()).thenReturn("fxbj.kgidc.cn/gwphp");
            when(apolloConfigService.getGzZuulAddress()).thenReturn("fxgz.kgidc.cn/gwphp");
            String notifyUrl = this.rechargeConfig.getRechargeZuulNotifyUrl(uri);
            Assert.assertEquals(PROD_PREFIX_BJ + uri, notifyUrl);
            notifyUrl = this.rechargeConfig.getRechargeZuulNotifyUrl(uri);
            Assert.assertEquals(PROD_PREFIX_BJ + uri, notifyUrl);
        }
    }
}
