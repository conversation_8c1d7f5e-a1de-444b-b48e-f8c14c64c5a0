package com.kugou.fanxing.recharge.service.command;

import com.google.common.collect.Maps;
import com.kugou.fanxing.risk.sdk.RiskStrategyService;
import com.kugou.fanxing.risk.sdk.model.RiskResult;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class RiskStrategyServiceConcludePlainCommandUnitTest {

    @Mock
    private RiskStrategyService riskStrategyService;

    private RiskStrategyServiceConcludePlainCommand.RiskRequestDto riskRequestDto;

    @Before
    public void before() {
        Map<String, Object> data = Maps.newHashMap();
        data.put("drawType", 32);
        data.put("bizAppId", 10069);
        data.put("totalAmount", 1);
        riskRequestDto = RiskStrategyServiceConcludePlainCommand.RiskRequestDto.builder()
                .appid("0")
                .biz("withdraw")
                .endtype("")
                .sid(String.valueOf(1596569726073471854L))
                .kugouId(1355952166L)
                .ip("***********")
                .deviceId("-")
                .platform(0)
                .roomId(0)
                .clientver(0)
                .clienttype(-1)
                .ts(System.currentTimeMillis())
                .data(data)
                .build();
    }

    @Test
    public void conclude() {
        RiskStrategyServiceConcludePlainCommand.RiskRequestDto dto = new RiskStrategyServiceConcludePlainCommand.RiskRequestDto();
        Assert.assertNotNull(dto);
        RiskResult riskResult = new RiskResult();
        riskResult.setCode(0);
        riskResult.setLevel(4);
        when(riskStrategyService.conclude(anyString(), anyString(), anyString(), anyString(), anyLong(), anyString(), anyString(),
                anyInt(), anyLong(), anyInt(), anyInt(), anyLong(), anyMap())).thenReturn(riskResult);
        RiskStrategyServiceConcludePlainCommand command = new RiskStrategyServiceConcludePlainCommand(riskStrategyService, riskRequestDto);
        Assert.assertTrue(command.execute().isPresent());
    }
    
    @Test
    public void concludeException() {
        when(riskStrategyService.conclude(anyString(), anyString(), anyString(), anyString(), anyLong(), anyString(), anyString(),
                anyInt(), anyLong(), anyInt(), anyInt(), anyLong(), anyMap())).thenThrow(new ContextedRuntimeException("test"));
        RiskStrategyServiceConcludePlainCommand command = new RiskStrategyServiceConcludePlainCommand(riskStrategyService, riskRequestDto);
        Assert.assertFalse(command.execute().isPresent());
    }
}
