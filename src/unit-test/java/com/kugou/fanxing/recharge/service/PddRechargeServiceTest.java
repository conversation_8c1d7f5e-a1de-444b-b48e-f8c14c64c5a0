package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.model.PddOrderCheckResult;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.bo.PddProductBo;
import com.kugou.fanxing.recharge.model.dto.CoinCallbackDTO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.PddCreateOrderCheckRequest;
import com.kugou.fanxing.recharge.service.after.AfterRechargeService;
import com.kugou.fanxing.recharge.service.common.ConsumeRpcService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.service.recharge.PddRechargeService;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.recharge.util.SpringContextUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class PddRechargeServiceTest {

    @InjectMocks
    private PddRechargeService pddRechargeService;
    @Mock
    private ValidatingService validatingService;
    @Mock
    private UserFacadeService userFacadeService;
    @Mock
    private RemoteStrategyService remoteStrategyService;
    @Mock
    private ApolloConfigService apolloConfigService;
    @Mock
    private RechargeConfig rechargeConfig;
    @Mock
    private RechargeOrderService rechargeOrderService;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    private ConsumeRpcService consumeRpcService;
    @Mock
    private AfterRechargeService afterRechargeService;
    @Mock
    private HttpServletRequest httpServletRequest;
    @Mock
    private RechargeCommonService rechargeCommonService;
    @Mock
    private RechargeCouponService rechargeCouponService;
    @Mock
    private RechargeAcrossDao rechargeAcrossDao;

    @Test
    public void createOrderCheck() {
        when(this.validatingService.checkViolation(any(PddCreateOrderCheckRequest.class))).thenReturn(Optional.empty());
        when(this.userFacadeService.getKugouIdByUserId(anyLong(), anyBoolean())).thenReturn(Optional.of(1290249156L));
        when(userFacadeService.isBannedAccount(anyLong())).thenReturn(false);
        when(userFacadeService.isRevokedAccount(anyLong())).thenReturn(false);
        when(userFacadeService.isOverseasRegisterUser(anyLong())).thenReturn(false);
        when(remoteStrategyService.strategyVerifyForX(any(PayTypeIdEnum.class), anyLong(), anyMap())).thenReturn(false);
        List<PddProductBo> pddProductBoList = Lists.newArrayList(
                PddProductBo.builder().prodNo("abc").prodCoin(BigDecimal.valueOf(30000)).build()
        );
        when(this.apolloConfigService.getPddProductPriceList()).thenReturn(pddProductBoList);
        PddCreateOrderCheckRequest request = new PddCreateOrderCheckRequest();
        request.setUserid(1290249156L);
        request.setProdNo("abc");
        JsonResult<PddOrderCheckResult> jsonResult = this.pddRechargeService.createOrderCheck(request);
        Assert.assertEquals(SysResultCode.SUCCESS.getCode(), jsonResult.getCode());
        Assert.assertEquals(1, jsonResult.getData().getPermitRecharge());
    }

    @Test
    public void getPddProductPriceByProdNo() {
        List<PddProductBo> pddProductBoList = Lists.newArrayList(
                PddProductBo.builder().prodNo("a").prodCoin(BigDecimal.valueOf(10000)).build(),
                PddProductBo.builder().prodNo("b").prodCoin(BigDecimal.valueOf(20000)).build(),
                PddProductBo.builder().prodNo("c").prodCoin(BigDecimal.valueOf(30000)).build(),
                PddProductBo.builder().prodNo("d").prodCoin(BigDecimal.valueOf(0)).build(),
                PddProductBo.builder().prodNo("e").prodCoin(BigDecimal.valueOf(-1)).build()
        );
        when(this.apolloConfigService.getPddProductPriceList()).thenReturn(pddProductBoList);
        Optional<PddProductBo> optionalPddProductBo = this.pddRechargeService.getPddProductByProdNo("a");
        Assert.assertEquals("10000", optionalPddProductBo.orElseThrow(ContextedRuntimeException::new).getProdCoin().stripTrailingZeros().toPlainString());
        optionalPddProductBo = this.pddRechargeService.getPddProductByProdNo("b");
        Assert.assertEquals("20000", optionalPddProductBo.orElseThrow(ContextedRuntimeException::new).getProdCoin().stripTrailingZeros().toPlainString());
        optionalPddProductBo = this.pddRechargeService.getPddProductByProdNo("c");
        Assert.assertEquals("30000", optionalPddProductBo.orElseThrow(ContextedRuntimeException::new).getProdCoin().stripTrailingZeros().toPlainString());
        Assert.assertFalse(this.pddRechargeService.getPddProductByProdNo("d").isPresent());
        Assert.assertFalse(this.pddRechargeService.getPddProductByProdNo("e").isPresent());
    }

    @Test
    public void callbackNotify() {
        List<PddProductBo> pddProductBoList = Lists.newArrayList(
                PddProductBo.builder().prodNo("abc").prodCoin(BigDecimal.valueOf(3000)).build()
        );
        when(this.apolloConfigService.getPddProductPriceList()).thenReturn(pddProductBoList);
        when(rechargeConfig.getKupayAppIdByAppId(anyInt()))
                .thenReturn(new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J"));
        when(this.validatingService.checkViolation(any(PddCreateOrderCheckRequest.class))).thenReturn(Optional.empty());
        when(this.userFacadeService.getKugouIdByUserId(anyLong(), anyBoolean())).thenReturn(Optional.of(1290249156L));
        when(rechargeOrderService.generateRechargeOrderNumByTradeNo(any(PayTypeIdEnum.class), anyString()))
                .thenReturn(Optional.of("R092022110211515816590463"));
        when(this.rechargeOrderService.addRechargeOrder(any(RechargeAcrossPO.class))).thenReturn(1);
        when(this.orderIdService.generateGlobalId()).thenReturn(1453818833151602596L);
        when(rechargeOrderService.queryByRechargeOrderNum(anyString())).thenReturn(Optional.empty());
        when(userFacadeService.getUserIdByKugouId(anyLong(), anyBoolean())).thenReturn(Optional.of(1290249156L));
        when(consumeRpcService.rechargeCoin(any(RechargeAcrossPO.class))).thenReturn(true);
        when(orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202211");
        when(remoteStrategyService.strategyVerifyForX(any(PayTypeIdEnum.class), anyLong(), anyMap())).thenReturn(false);
        when(this.rechargeConfig.getKupayAppIdByAppId(anyInt())).thenReturn(
                new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J")
        );
        doNothing().when(afterRechargeService).afterRechargeSuccess(any(RechargeAcrossPO.class));
        when(rechargeConfig.getKupayIntranet()).thenReturn("https://kupay.kugou.com");
        when(rechargeCommonService.buildExtendStr(any(RechargeAcrossPO.class), anyMap())).thenReturn("extend");
        when(rechargeCouponService.extractValidCoupon(any(RechargeAcrossPO.class))).thenReturn(Optional.empty());
        when(this.rechargeAcrossDao.insertIgnore(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class);
             MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class)
        ) {
            when(httpServletRequest.getParameterNames()).thenReturn(new Enumeration<String>() {
                @Override
                public boolean hasMoreElements() {
                    return false;
                }
                @Override
                public String nextElement() {
                    return "";
                }
            });
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(httpServletRequest);
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doSyncGet(anyString(), anyMap())).thenReturn(Optional.of("success"));
            CoinCallbackDTO coinCallbackDTO = new CoinCallbackDTO();
            coinCallbackDTO.setTotal_fee(BigDecimal.valueOf(30));
            coinCallbackDTO.setTrade_no("test1");
            coinCallbackDTO.setUserid(1290249156L);
            coinCallbackDTO.setSign("7e14f00fc8620c60dbc01a0c9a394672");
            coinCallbackDTO.setOrder_no("R092022110211515816590463");
            coinCallbackDTO.setExtend("{\"amount\":\"1\",\"chargeNo\":\"1290249156\",\"chargeTag\":\"1\",\"mctNo\":\"880859775\",\"notifyUrl\":\"http://gw-api.pinduoduo.com/api/router\",\"outOrderNo\":\"3012-1669259064104\",\"prodName\":\"测试名字\",\"prodNo\":\"abc\",\"signType\":\"md5\"}");
            JsonResult<Map<String, String>> jsonResult = this.pddRechargeService.callbackNotify(coinCallbackDTO);
            Assert.assertEquals(SysResultCode.SUCCESS.getCode(), jsonResult.getCode());
        }
    }
}
