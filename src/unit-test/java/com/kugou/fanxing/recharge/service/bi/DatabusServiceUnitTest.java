package com.kugou.fanxing.recharge.service.bi;

import com.kugou.fanxing.commons.lock.DLockFactory;
import com.kugou.fanxing.commons.lock.impl.DLockRedisImpl;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.DatabusDataDao;
import com.kugou.fanxing.recharge.model.po.DatabusDataPo;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import groovy.util.logging.Slf4j;
import org.codehaus.plexus.util.FileUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.BDDMockito;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@Ignore
@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DatabusServiceUnitTest {
    @InjectMocks
    private DatabusService databusService;
    @Mock
    private DatabusDataDao databusDataDao;
    @Mock
    private DLockFactory dLockFactory;
    @Mock
    private DLockRedisImpl dLock;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    private ApolloConfigService apolloConfigService;

    @Before
    public void before() {
        BDDMockito.given(FileUtils.fileExists(anyString())).willReturn(true);
    }

    @Test
    public void fetchData() {
        long dataId = 468L;
        String dt = "2022-08-22";
        String databusUrl = "http://fxadmin.kugou.net/databus/worker/hadoop/file/test_download?std_bid=0&dataId=468&appId=48fa5f20-6f9b-4f7d-b74d-e89de656bbab&appSecret=19D615FC9D71ECDEDF2E38D8603A8205&dt=2022-08-22";
        when(dLockFactory.getLock(anyString())).thenReturn(dLock);
        when(dLock.acquire(anyInt())).thenReturn(true);
        when(apolloConfigService.getDatabusPersistThreshold()).thenReturn(10);
        when(orderIdService.generateGlobalId()).thenReturn(1234L);
        when(databusDataDao.batchInsert(anyLong(), anyList())).thenReturn(1);
        when(apolloConfigService.enableClearDatabusFile()).thenReturn(true);
        Assert.assertTrue(databusService.fetchData(dataId, dt , databusUrl));
    }

    @Test
    public void isAirwallexAllowKugouId() {
        when(apolloConfigService.enableAirwallexBiData()).thenReturn(true);
        when(this.databusDataDao.query(anyLong(), anyLong(), anyLong())).thenReturn(null);
        Assert.assertFalse(databusService.isAirwallexAllowKugouId(1290249156L));
        when(this.databusDataDao.query(anyLong(), anyLong(), anyLong())).thenReturn(DatabusDataPo.builder().kugouId(1290249156L).globalId(1L).build());
        Assert.assertTrue(databusService.isAirwallexAllowKugouId(1290249156L));
    }
}
