package com.kugou.fanxing.recharge.service.callback;

import com.kugou.config.Env;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.model.bo.KugouOpenBusinessBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import com.kugou.fanxing.recharge.util.SignUtils;
import com.kugou.fanxing.recharge.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.net.Proxy;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class OpenRechargeCallbackServiceUnitTest {

    @InjectMocks
    private OpenRechargeCallbackService openRechargeCallbackService;
    @Mock
    private HttpServletRequest request;
    @Mock
    private Env env;
    @Mock
    private ApolloConfigService apolloConfigService;

    @Before
    public void testBefore()  {
        when(apolloConfigService.getOpenRechargeBusinessConfig(anyString())).thenReturn(buildKugouOpenBusinessBO());
        when(env.isProd()).thenReturn(true);
    }

    @Test
    public void notifyGameServed() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class);
             MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class);
             MockedStatic<SignUtils> signUtilsMockedStatic = Mockito.mockStatic(SignUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doPostJSONProxy(any(Proxy.class), anyString(), anyMap(), anyMap())).thenReturn(Optional.of("{\"code\":0,\"msg\":\"\"}"));
            signUtilsMockedStatic.when(() -> SignUtils.buildSign(anyMap(), anyString())).thenReturn("test");
            RechargeAcrossPO targetOrder = new RechargeAcrossPO();
            targetOrder.setMoney(BigDecimal.ONE);
            targetOrder.setBusinessId("10161");
            targetOrder.setExtend("eyJjYWxsQmFja1NpZ24iOiJjYjVhNTVjOWNiMDRiNjIzM2IwMWNhMjYzMmVjZmNmMiIsImNhbGxCYWNrQXJnIjp7ImFtb3VudCI6IjAuMDEiLCJhZGRUaW1lIjoiMTYzMjAyMTkzNCIsImNvdXBvbiI6IjAiLCJ1c2VyT3BlbmlkIjoiYzAzOWQxYTc5ZDY1N2RkODFiYzExNzY0YzI5YWNjN2QiLCJwcm9kdWN0SWQiOiIxMDAwMSIsInJlYmF0ZSI6MCwiYnV5UmljaExldmVsIjpbXSwiY29uc3VtZUFyZ3MiOltdLCJidXNpbmVzc0lkIjoiMTAxNjEiLCJjb3Vwb25JZCI6MCwidmVyc2lvbiI6IjIwMTcwMTExIiwiY291cG9uT3JkZXJJZCI6MCwicGF5VHlwZUlkIjoxMDA2LCJtb25leSI6IjAuMDEiLCJyZWZlciI6MCwiY0Zyb20iOjMwNywiY2hhbm5lbElkIjowLCJrdWdvdUlkIjo3OTYzNzQ2MzR9fQ==");
            SysResultCode sysResultCode = this.openRechargeCallbackService.notifyGameServed(targetOrder);
            Assert.assertTrue(sysResultCode.isSuccess());
        }
    }

    @Test
    public void notifyGameServedEmptyData() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class);
             MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class);
             MockedStatic<SignUtils> signUtilsMockedStatic = Mockito.mockStatic(SignUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            signUtilsMockedStatic.when(() -> SignUtils.buildSign(anyMap(), anyString())).thenReturn("test");
            RechargeAcrossPO targetOrder = new RechargeAcrossPO();
            targetOrder.setBusinessId("10161");
            targetOrder.setMoney(BigDecimal.ONE);
            targetOrder.setExtend("eyJjYWxsQmFja1NpZ24iOiJjYjVhNTVjOWNiMDRiNjIzM2IwMWNhMjYzMmVjZmNmMiIsImNhbGxCYWNrQXJnIjp7ImFtb3VudCI6IjAuMDEiLCJhZGRUaW1lIjoiMTYzMjAyMTkzNCIsImNvdXBvbiI6IjAiLCJ1c2VyT3BlbmlkIjoiYzAzOWQxYTc5ZDY1N2RkODFiYzExNzY0YzI5YWNjN2QiLCJwcm9kdWN0SWQiOiIxMDAwMSIsInJlYmF0ZSI6MCwiYnV5UmljaExldmVsIjpbXSwiY29uc3VtZUFyZ3MiOltdLCJidXNpbmVzc0lkIjoiMTAxNjEiLCJjb3Vwb25JZCI6MCwidmVyc2lvbiI6IjIwMTcwMTExIiwiY291cG9uT3JkZXJJZCI6MCwicGF5VHlwZUlkIjoxMDA2LCJtb25leSI6IjAuMDEiLCJyZWZlciI6MCwiY0Zyb20iOjMwNywiY2hhbm5lbElkIjowLCJrdWdvdUlkIjo3OTYzNzQ2MzR9fQ==");
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doPostJSONProxy(any(Proxy.class), anyString(), anyMap(), anyMap())).thenReturn(Optional.empty());
            SysResultCode sysResultCode = this.openRechargeCallbackService.notifyGameServed(targetOrder);
            Assert.assertFalse(sysResultCode.isSuccess());
            Assert.assertEquals(SysResultCode.E_50000004.getCode(), sysResultCode.getCode());
        }
    }

    @Test
    public void notifyGameServedErrorData() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class);
             MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class);
             MockedStatic<SignUtils> signUtilsMockedStatic = Mockito.mockStatic(SignUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            signUtilsMockedStatic.when(() -> SignUtils.buildSign(anyMap(), anyString())).thenReturn("test");
            RechargeAcrossPO targetOrder = new RechargeAcrossPO();
            targetOrder.setBusinessId("10161");
            targetOrder.setMoney(BigDecimal.ONE);
            targetOrder.setExtend("eyJjYWxsQmFja1NpZ24iOiJjYjVhNTVjOWNiMDRiNjIzM2IwMWNhMjYzMmVjZmNmMiIsImNhbGxCYWNrQXJnIjp7ImFtb3VudCI6IjAuMDEiLCJhZGRUaW1lIjoiMTYzMjAyMTkzNCIsImNvdXBvbiI6IjAiLCJ1c2VyT3BlbmlkIjoiYzAzOWQxYTc5ZDY1N2RkODFiYzExNzY0YzI5YWNjN2QiLCJwcm9kdWN0SWQiOiIxMDAwMSIsInJlYmF0ZSI6MCwiYnV5UmljaExldmVsIjpbXSwiY29uc3VtZUFyZ3MiOltdLCJidXNpbmVzc0lkIjoiMTAxNjEiLCJjb3Vwb25JZCI6MCwidmVyc2lvbiI6IjIwMTcwMTExIiwiY291cG9uT3JkZXJJZCI6MCwicGF5VHlwZUlkIjoxMDA2LCJtb25leSI6IjAuMDEiLCJyZWZlciI6MCwiY0Zyb20iOjMwNywiY2hhbm5lbElkIjowLCJrdWdvdUlkIjo3OTYzNzQ2MzR9fQ==");
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doPostJSONProxy(any(Proxy.class), anyString(), anyMap(), anyMap())).thenReturn(Optional.of("{\"code\":1,\"msg\":\"\"}"));
            SysResultCode sysResultCode = this.openRechargeCallbackService.notifyGameServed(targetOrder);
            Assert.assertFalse(sysResultCode.isSuccess());
            Assert.assertEquals(SysResultCode.E_50000004.getCode(), sysResultCode.getCode());
        }
    }

    private KugouOpenBusinessBO buildKugouOpenBusinessBO() {
        KugouOpenBusinessBO kugouOpenBusinessBO = new KugouOpenBusinessBO();
        kugouOpenBusinessBO.setOpenAppId("10161");
        kugouOpenBusinessBO.setSecretKey("1V8UcfwcXVeRnjuqsZe0GhKq0TTW9WFv");
        kugouOpenBusinessBO.setBusinessId("10161");
        kugouOpenBusinessBO.setBusinessNotifyUrl("https://fxwbgame.kugou.com/game/zt2/charge/chargecallback");
        kugouOpenBusinessBO.setBusinessNotifyUrlForIos("https://fxwbgame.kugou.com/game/zt2/charge/chargecallback");
        kugouOpenBusinessBO.setKupayAppId(10043);
        kugouOpenBusinessBO.setAppType("share");
        kugouOpenBusinessBO.setPid(306);
        return kugouOpenBusinessBO;
    }
}
