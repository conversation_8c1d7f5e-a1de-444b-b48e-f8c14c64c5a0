package com.kugou.fanxing.recharge.service.command;

import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.platform.after.recharge.asset.allocate.thrift.AfterRechargeAssetAllocateReadService;
import com.kugou.platform.after.recharge.asset.allocate.thrift.PresentInfo;
import com.kugou.platform.after.recharge.asset.allocate.thrift.RechargePresentItem;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class GetRechargePresentByNumCommandUnitTest {

    @Mock
    private AfterRechargeAssetAllocateReadService.Iface afterRechargeAsserAllocateReadService;

    @Test
    public void getPersonalCouponList() throws TException {
        Map<Long,List<RechargePresentItem>> data = Maps.newHashMap();
        data.put(1L, Lists.newArrayList(new RechargePresentItem().setAssetId(1).setAssetType(1).setGoodsType(1).setNum(1).setExpireTime(DateHelper.getCurrentSeconds())));
        PresentInfo presentInfo = new PresentInfo();
        presentInfo.setRet(0);
        presentInfo.setData(data);
        when(afterRechargeAsserAllocateReadService.getRechargePresentByNum(anyList())).thenReturn(presentInfo);
        GetRechargePresentByNumCommand command = new GetRechargePresentByNumCommand(afterRechargeAsserAllocateReadService, Lists.newArrayList(1L));
        Assert.assertEquals(1, command.execute().size());
        command = new GetRechargePresentByNumCommand(afterRechargeAsserAllocateReadService, Lists.newArrayList());
        Assert.assertEquals(0, command.execute().size());
        presentInfo.setRet(1);
        when(afterRechargeAsserAllocateReadService.getRechargePresentByNum(anyList())).thenReturn(presentInfo);
        command = new GetRechargePresentByNumCommand(afterRechargeAsserAllocateReadService, Lists.newArrayList(1L));
        Assert.assertEquals(0, command.execute().size());
    }

    @Test
    public void getPersonalCouponListException() throws TException {
        when(afterRechargeAsserAllocateReadService.getRechargePresentByNum(anyList())).thenThrow(new ContextedRuntimeException());
        GetRechargePresentByNumCommand command = new GetRechargePresentByNumCommand(afterRechargeAsserAllocateReadService, Lists.newArrayList(1L));
        Map<Long,List<RechargePresentItem>> presentMap = command.execute();
        Assert.assertEquals(0, presentMap.size());
    }
}
