package com.kugou.fanxing.recharge.service.withdraw;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.Silent.class)
public class AlipayRespUnitTest {

    /**
     * 是否提现中
     *
     * @return 是否提现中
     */
    @Test
    public void isTransferProcess() {
        AlipayResp.AlipayResult result = new AlipayResp.AlipayResult();
        result.setTransfer_status(0);
        AlipayResp resp = new AlipayResp();
        resp.setData(result);
        Assert.assertTrue(resp.isTransferProcess());
    }

    /**
     * 是否提现成功
     *
     * @return 是否提现成功
     */
    @Test
    public void isTransferSuccess() {
        AlipayResp.AlipayResult result = new AlipayResp.AlipayResult();
        result.setTransfer_status(1);
        AlipayResp resp = new AlipayResp();
        resp.setData(result);
        Assert.assertTrue(resp.isTransferSuccess());
    }

    /**
     * 是否提现失败
     *
     * @return 是否提现失败
     */
    @Test
    public void isTransferFailure() {
        AlipayResp.AlipayResult result = new AlipayResp.AlipayResult();
        result.setTransfer_status(2);
        AlipayResp resp = new AlipayResp();
        resp.setData(result);
        Assert.assertTrue(resp.isTransferFailure());
    }

    /**
     * 是否提现取消
     *
     * @return 是否提现取消
     */
    @Test
    public void isTransferCanceled() {
        AlipayResp.AlipayResult result = new AlipayResp.AlipayResult();
        result.setTransfer_status(4);
        AlipayResp resp = new AlipayResp();
        resp.setData(result);
        Assert.assertTrue(resp.isTransferCanceled());
    }

    /**
     * 是否提现订单不存在
     *
     * @return 是否提现订单不存在
     */
    @Test
    public void isTransferNotExists() {
        AlipayResp resp = new AlipayResp();
        resp.setData(null);
        resp.setStatus(1);
        resp.setError_code(0);
        Assert.assertTrue(resp.isTransferNotExists());
    }
}
