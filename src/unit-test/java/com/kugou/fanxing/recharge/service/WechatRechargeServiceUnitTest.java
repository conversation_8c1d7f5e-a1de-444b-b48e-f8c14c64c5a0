package com.kugou.fanxing.recharge.service;

import com.google.common.collect.ImmutableMap;
import com.jayway.jsonpath.TypeRef;
import com.kugou.fanxing.biz.commons.util.SnowFlake;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.model.bo.CouponInfoBO;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.bo.KupayAppTypeInfoBO;
import com.kugou.fanxing.recharge.model.bo.KupayServerInfoBO;
import com.kugou.fanxing.recharge.model.dto.WxMiniProgramDTO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.GetOrderRequest;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.model.request.WxMiniProgramRequest;
import com.kugou.fanxing.recharge.model.request.WxgzhRequest;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.util.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class WechatRechargeServiceUnitTest {

    @Mock
    private HttpServletRequest request;
    @Mock
    private RechargeConfig rechargeConfig;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    private RechargeAcrossDao rechargeAcrossDao;
    @Mock
    private RechargeCouponService rechargeCouponService;
    @Mock
    private RechargeCommonService rechargeCommonService;
    @Mock
    private RechargeOrderService rechargeOrderService;
    @Mock
    private ValidatingService validatingService;
    @Mock
    private ApolloConfigService apolloConfigService;
    @InjectMocks
    private WechatRechargeService wechatRechargeService;

    @Before
    public void setUp() {
        when(rechargeCommonService.checkPreconditionForGetOrder(anyString(), any(WebCommonParam.class), any(GetOrderRequest.class)))
                .thenReturn(SysResultCode.SUCCESS);
        when(orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202008");
    }

    @Test
    public void getOrder4WxMiniProgram() {
        long kugouId = 1554749117L;
        String openId = "oL0xd5ej-O0hmuhF7BnTb7jLr9Zg";
        BigDecimal money = BigDecimal.valueOf(0.01);
        String rechargeOrderNum = "R092020011317431884786716";
        WebCommonParam webCommonParam = new WebCommonParam();
        webCommonParam.setPid(40);
        webCommonParam.setKugouId(kugouId);
        webCommonParam.setIp("127.0.0.1");
        WxMiniProgramRequest wxMiniProgramRequest = new WxMiniProgramRequest()
                .setOpenId(openId)
                .setMoney(money);
        when(rechargeConfig.getKupayInternet()).thenReturn("http://kupay.kugou.com");
        when(rechargeAcrossDao.add(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
        when(rechargeConfig.getDataCenterZoneId()).thenReturn(0);
        when(rechargeCommonService.buildRechargeAcross(anyInt(), anyLong(), anyString(), any(PayTypeIdEnum.class), any(BigDecimal.class), anyString())).thenReturn(new RechargeAcrossPO().setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_42.getPayTypeId()));
        when(rechargeConfig.getActionUrlPrefix(any())).thenReturn("http://kupay.kugou.com/v1/wxminiprogram");
        when(rechargeConfig.getKupayAppIdByAppId(anyInt())).thenReturn(new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J"));
        when(rechargeConfig.getKupayAppIdByPayType(any(PayTypeIdEnum.class))).thenReturn(new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J"));
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class);
             MockedStatic<IpUtils> ipUtilsMockedStatic = Mockito.mockStatic(IpUtils.class);
             MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            springContextUtilsMockedStatic.when(IpUtils::getClientIpAddress).thenReturn("**************");
            springContextUtilsMockedStatic.when(() -> HttpClientUtils.doSyncGet(anyString(), anyMap())).thenReturn(Optional.of("{\"error_code\":0,\"error_msg\":\"\",\"order_no\":\"R092020011317431884786716\",\"out_trade_no\":\"01202001131743180100019924\",\"data\":\"{\\\"appId\\\":\\\"wx234f5fe95a717b99\\\",\\\"timeStamp\\\":\\\"1578908599\\\",\\\"nonceStr\\\":\\\"0b5cfec9f38119d993680fef74d706a3\\\",\\\"package\\\":\\\"prepay_id=wx1317431924119134df38621f1063613600\\\",\\\"signType\\\":\\\"MD5\\\",\\\"paySign\\\":\\\"246288E784FDE50778FE1E3D13C52C3B\\\"}\"}"));
            when(orderIdService.getYearMonthFromRechargeOrderNum(anyString())).thenReturn(Optional.of("202001"));
            Optional<WxMiniProgramDTO> optionalWxMiniProgramDTO = this.wechatRechargeService.getOrderForWxxcx(rechargeOrderNum, webCommonParam, wxMiniProgramRequest);
            Assert.assertTrue(optionalWxMiniProgramDTO.isPresent());
        }
    }

    @Test
    public void buildWxMiniProgramParams() {
        String rechargeOrderNum = "R092020011014580532783488";
        String openId = "oL0xd5ej-O0hmuhF7BnTb7jLr9Zg";
        long kugouId = 1554749117L;
        BigDecimal money = BigDecimal.valueOf(0.01);
        String extend = "eyJjYWxsQmFja1NpZ24iOiI0NGQ5YTAzNTQ5MjM1MzdlNzFhNjFmOTViMWRhYjVlYSIsImNhbGxCYWNrQXJnIjp7ImFtb3VudCI6MC4wMSwiYWRkVGltZSI6MTU3ODg4MjU2MCwiY291cG9uIjowLCJidXlSaWNoTGV2ZWwiOnt9LCJyZWJhdGUiOiIwIiwiY29uc3VtZUFyZ3MiOnt9LCJjb3Vwb25JZCI6MCwidmVyc2lvbiI6IjIwMTcwMTExIiwiY291cG9uT3JkZXJJZCI6MCwicGF5VHlwZUlkIjo0MiwibW9uZXkiOjAuMDEsInJlZmVyIjowLCJjRnJvbSI6MzksImNoYW5uZWxJZCI6MCwia3Vnb3VJZCI6MTU1NDc0OTExN319";
        when(rechargeConfig.getKupayAppIdByAppId(anyInt())).thenReturn(new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J"));
        when(rechargeConfig.getKupayAppIdByPayType(any(PayTypeIdEnum.class))).thenReturn(new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J"));
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(IpUtils::getClientIpAddress).thenReturn("**************");
            RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
            rechargeAcrossPO.setRechargeOrderNum(rechargeOrderNum);
            rechargeAcrossPO.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_42.getPayTypeId());
            Map<String, String> params = wechatRechargeService.buildWxMiniProgramParams(rechargeAcrossPO, openId, kugouId, money, extend, ReTypeEnum.RETYPE_PURCHASE);
            Assert.assertEquals(14, params.size());
            Assert.assertEquals("1084", params.get("appid"));
        }
    }

    @Test
    public void totalFee() {
        BigDecimal totalFee = BigDecimal.valueOf(100000.345);
        Assert.assertEquals("100000.345", totalFee.toPlainString());
        totalFee = BigDecimal.valueOf(100000.3);
        Assert.assertEquals("100000.3", totalFee.toPlainString());
        totalFee = BigDecimal.valueOf(100000);
        Assert.assertEquals("100000", totalFee.toPlainString());
    }

    @Test
    public void getOrderForWxgzh() {
        WebCommonParam webCommonParam = new WebCommonParam();
        webCommonParam.setKugouId(1554749117L);
        webCommonParam.setIp("127.0.0.1");
        webCommonParam.setPid(2);
        webCommonParam.setExt("");
        long orderId = new SnowFlake(1, 1).nextId();
        long couponId = new SnowFlake(1, 2).nextId();
        WxgzhRequest wxgzhRequest = new WxgzhRequest();
        wxgzhRequest.setCouponId(0);
        wxgzhRequest.setAmount(BigDecimal.valueOf(10));
        wxgzhRequest.setPayTypeIdEnum(PayTypeIdEnum.payTypeOf(41));
        wxgzhRequest.setOpenId("123");
        when(orderIdService.getYearMonthFromRechargeOrderNum(anyString())).thenReturn(Optional.of("202001"));
        CouponInfoBO couponInfoBO = new CouponInfoBO()
                .setOrderId(orderId)
                .setCouponId(couponId)
                .setValue(BigDecimal.valueOf(0.2));
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            when(this.rechargeConfig.isRechargeWithChange(anyInt())).thenReturn(true);
            when(this.rechargeCommonService.checkThirdPart(anyString(), any(WebCommonParam.class), any(GetOrderRequest.class))).thenReturn(SysResultCode.SUCCESS);
            when(validatingService.validateParams(any(Object.class))).thenReturn("");
            when(rechargeCouponService.handleCouponForOrder(anyLong(), anyLong(), any(BigDecimal.class), any(CoinTypeEnum.class))).thenReturn(Optional.ofNullable(couponInfoBO));
            when(rechargeCouponService.handleCouponForOrder(anyLong(), anyLong(), any(BigDecimal.class), any(CoinTypeEnum.class))).thenReturn(Optional.ofNullable(couponInfoBO));
            when(rechargeConfig.getWebMethod()).thenReturn("POST");
            when(rechargeAcrossDao.add(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
            when(rechargeConfig.getActionUrlPrefix(any(PayTypeIdEnum.class))).thenReturn("http://kupay.kugou.com/v1/wxgzhpay?");
            when(rechargeCommonService.buildRechargeAcross(anyInt(), anyLong(), anyString(), any(PayTypeIdEnum.class), any(BigDecimal.class), anyString())).thenReturn(new RechargeAcrossPO());
            when(rechargeCommonService.buildRechargeAcross(anyInt(), anyLong(), anyLong(), anyString(), any(PayTypeIdEnum.class), any(BigDecimal.class), anyString(), any(CoinTypeEnum.class), any(Optional.class))).thenReturn(new RechargeAcrossPO());
            when(rechargeCommonService.createRechargeResultForMobile(any(RechargeAcrossPO.class), anyString(), anyString(), anyMap())).thenReturn(ImmutableMap.of("actionUrl", "http://test", "webMethod", "POST"));
            when(orderIdService.generateRechargeOrderNumForAcross()).thenReturn("R092020011014580532783488");
            Map<String, Object> dataMap = this.wechatRechargeService.getOrderForWxgzh(webCommonParam, wxgzhRequest);
            Assert.assertNotNull(dataMap);
            Assert.assertNotNull(dataMap.get("actionUrl"));
            Assert.assertNotNull(dataMap.get("webMethod"));
        }
    }

    @Test
    public void createRechargeParamByPayType() {
        when(rechargeConfig.getKupayAppTypeInfoByPid(anyInt())).thenReturn("fanxing");
        when(rechargeConfig.useKupayAppTypeDirectly(anyString())).thenReturn(true);
        Map<String, String> extParam = this.wechatRechargeService.createRechargeParamByPayType("player2", 5);
        Assert.assertEquals("player2", extParam.get("apptype"));
        when(rechargeConfig.useKupayAppTypeDirectly(anyString())).thenReturn(false);
        when(rechargeConfig.getKupayAppTypeInfoByPid(anyInt())).thenReturn("player");
        extParam = this.wechatRechargeService.createRechargeParamByPayType("", 5);
        Assert.assertEquals("player", extParam.get("apptype"));
        extParam = this.wechatRechargeService.createRechargeParamByPayType("", 6);
        Assert.assertEquals("player", extParam.get("apptype"));
        when(rechargeConfig.getKupayAppTypeInfoByPid(anyInt())).thenReturn("fast");
        extParam = this.wechatRechargeService.createRechargeParamByPayType("", 35);
        Assert.assertEquals("fast", extParam.get("apptype"));
    }

    @Test
    public void refreshWechatQrCode() {
        String json = "{\n" +
                "    \"status\": 1,\n" +
                "    \"error_code\": 0,\n" +
                "    \"error_msg\": \"\",\n" +
                "    \"data\": {\n" +
                "        \"prepay_id\": \"wx041615502870325af77003d23aabe60000\",\n" +
                "        \"wx_url\": \"weixin://wxpay/bizpayurl?pr=g1iJ7oyzz\"\n" +
                "    }\n" +
                "}";
        Map<String, Object> dataMap = JsonUtils.parseJsonPathMap(json, "$.data", new TypeRef<Map<String, Object>>() {});
        Assert.assertEquals("wx041615502870325af77003d23aabe60000", dataMap.get("prepay_id"));
        Assert.assertEquals("weixin://wxpay/bizpayurl?pr=g1iJ7oyzz", dataMap.get("wx_url"));


        WebCommonParam webCommonParam = new WebCommonParam();
        webCommonParam.setKugouId(1554749117L);
        webCommonParam.setIp("127.0.0.1");
        webCommonParam.setPid(2);
        webCommonParam.setExt("");
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setKugouId(1554749117L);
        KupayAppTypeInfoBO kupayAppTypeInfoBO= new KupayAppTypeInfoBO();
        kupayAppTypeInfoBO.setKupayAppId(2);
        KupayServerInfoBO kupayServerInfoBO= new KupayServerInfoBO();
        kupayServerInfoBO.setKupayServerKey("123");
        when(rechargeConfig.getKupayIntranet()).thenReturn("http://kupay.kugou.com");
        when(rechargeConfig.getKupayServerInfoByServerId(anyInt())).thenReturn(kupayServerInfoBO);
        when(rechargeConfig.getKupayAppTypeInfoBO(anyInt())).thenReturn(Optional.of(kupayAppTypeInfoBO));
        when(rechargeOrderService.queryByRechargeOrderNum(anyString())).thenReturn(Optional.of(rechargeAcrossPO));
        when(apolloConfigService.getWechatQrCodeExpireMinutes()).thenReturn(1);
        when(this.apolloConfigService.getWechatQrCodeRefreshSecond()).thenReturn(1);
        JsonResult<Map<String, Object>> result= wechatRechargeService.refreshWechatQrCode(webCommonParam, "123");
        Assert.assertEquals(SysResultCode.RECHARGE_SYS_ERROR.getCode(),result.getCode());

    }
}