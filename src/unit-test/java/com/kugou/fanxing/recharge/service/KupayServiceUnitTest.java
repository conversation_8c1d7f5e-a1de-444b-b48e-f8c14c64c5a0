package com.kugou.fanxing.recharge.service;

import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.model.ConfirmAirwallexV1;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.bo.OfflinePayDataBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import com.kugou.fanxing.recharge.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class KupayServiceUnitTest {

    @InjectMocks
    private KupayService kupayService;
    @Mock
    private RechargeConfig rechargeConfig;
    @Mock
    private RechargeCommonService rechargeCommonService;

    @Before
    public void before() {
        ReflectionTestUtils.setField(kupayService, "serverId", "1870");
        ReflectionTestUtils.setField(kupayService, "serverKey", "rYyNI4G7BdzzCYfXww3eHaW1U0haNcqE");
    }

    @Test
    public void commonUrlParams() {
        try (MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            String json = "{\"status\":1,\"error_code\":0,\"error_msg\":\"\",\"data\":{\"alipay_url\":\"https:\\/\\/mapi.alipay.com\\/gateway.do?service=create_direct_pay_by_user&partner=****************&_input_charset=utf-8&out_trade_no=01202106101534120100017909&subject=%E6%98%9F%E5%B8%81%E5%85%85%E5%80%BC%E6%9C%8D%E5%8A%A1&payment_type=1&total_fee=0.02&seller_email=kgqd7%40kugou.com&body=%E6%98%9F%E5%B8%81%E5%85%85%E5%80%BC%E6%9C%8D%E5%8A%A1&return_url=https%3A%2F%2Fkupay.kugou.com%2Fv1%2Falipay%2Ffx_sync&notify_url=https%3A%2F%2Fkupay.kugou.com%2Fv1%2Falipay%2Ffx_notify&extra_common_param=3&sign=8e25c24fd8681d3971ec7553e57c3c9d&sign_type=MD5\",\"order_no\":\"R092021061015340996820212\",\"out_trade_no\":\"01202106101534120100017909\"}}";
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doPostJsonBody(anyString(), anyMap(), anyString())).thenReturn(Optional.of(json));
            Map<String, String> params = kupayService.commonUrlParams("12345");
            Assert.assertEquals(7, params.size());
        }
    }

    @Test
    public void alipayV2() {
        try (MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            String json = "{\"status\":1,\"error_code\":0,\"error_msg\":\"\",\"data\":{\"alipay_url\":\"https:\\/\\/mapi.alipay.com\\/gateway.do?service=create_direct_pay_by_user&partner=****************&_input_charset=utf-8&out_trade_no=01202106101534120100017909&subject=%E6%98%9F%E5%B8%81%E5%85%85%E5%80%BC%E6%9C%8D%E5%8A%A1&payment_type=1&total_fee=0.02&seller_email=kgqd7%40kugou.com&body=%E6%98%9F%E5%B8%81%E5%85%85%E5%80%BC%E6%9C%8D%E5%8A%A1&return_url=https%3A%2F%2Fkupay.kugou.com%2Fv1%2Falipay%2Ffx_sync&notify_url=https%3A%2F%2Fkupay.kugou.com%2Fv1%2Falipay%2Ffx_notify&extra_common_param=3&sign=8e25c24fd8681d3971ec7553e57c3c9d&sign_type=MD5\",\"order_no\":\"R092021061015340996820212\",\"out_trade_no\":\"01202106101534120100017909\"}}";
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doPostJsonBody(anyString(), anyMap(), anyString())).thenReturn(Optional.of(json));
            ReflectionTestUtils.setField(kupayService, "serverKey", "rYyNI4G7BdzzCYfXww3eHaW1U0haNcqE");
            KupayAppInfoBO kupayAppInfoBO = new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J");
            when(rechargeConfig.getKupayAppIdByPayType(any(PayTypeIdEnum.class))).thenReturn(kupayAppInfoBO);
            when(rechargeConfig.getKupayIntranet()).thenReturn("http://kupay.kugou.com");
            when(rechargeConfig.getRechargeNotifyUrl(any(ReTypeEnum.class))).thenReturn("http://zuultest.fxwork.kugou.com/platform_recharge_service");
            when(rechargeConfig.getSyncUrl()).thenReturn("http://fanxing.kugou.com/index.php?action=rechargeFinish");
            when(rechargeCommonService.getOrderExpireTime()).thenReturn("20210601120212");
            when(rechargeCommonService.buildExtendStr(any(RechargeAcrossPO.class), anyMap())).thenReturn("test");
            Map<String, Object> payload = kupayService.alipayV2(buildRechargeAcrossPO(), "127.0.0.1", Maps.newHashMap());
            Assert.assertEquals("https://mapi.alipay.com/gateway.do?service=create_direct_pay_by_user&partner=****************&_input_charset=utf-8&out_trade_no=01202106101534120100017909&subject=%E6%98%9F%E5%B8%81%E5%85%85%E5%80%BC%E6%9C%8D%E5%8A%A1&payment_type=1&total_fee=0.02&seller_email=kgqd7%40kugou.com&body=%E6%98%9F%E5%B8%81%E5%85%85%E5%80%BC%E6%9C%8D%E5%8A%A1&return_url=https%3A%2F%2Fkupay.kugou.com%2Fv1%2Falipay%2Ffx_sync&notify_url=https%3A%2F%2Fkupay.kugou.com%2Fv1%2Falipay%2Ffx_notify&extra_common_param=3&sign=8e25c24fd8681d3971ec7553e57c3c9d&sign_type=MD5", payload.get("alipay_url"));
            Assert.assertEquals("R092020011014580532783488", payload.get("rechargeOrderNum"));
        }
    }

    @Test(expected = AckException.class)
    public void alipayV2Exception() {
        try (MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            String json = "{\"status\":1,\"error_code\":0,\"error_msg\":\"\",\"data\":{\"alipay_url\":\"https:\\/\\/mapi.alipay.com\\/gateway.do?service=create_direct_pay_by_user&partner=****************&_input_charset=utf-8&out_trade_no=01202106101534120100017909&subject=%E6%98%9F%E5%B8%81%E5%85%85%E5%80%BC%E6%9C%8D%E5%8A%A1&payment_type=1&total_fee=0.02&seller_email=kgqd7%40kugou.com&body=%E6%98%9F%E5%B8%81%E5%85%85%E5%80%BC%E6%9C%8D%E5%8A%A1&return_url=https%3A%2F%2Fkupay.kugou.com%2Fv1%2Falipay%2Ffx_sync&notify_url=https%3A%2F%2Fkupay.kugou.com%2Fv1%2Falipay%2Ffx_notify&extra_common_param=3&sign=8e25c24fd8681d3971ec7553e57c3c9d&sign_type=MD5\",\"order_no\":\"R092021061015340996820212\",\"out_trade_no\":\"01202106101534120100017909\"}}";
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doPostJsonBody(anyString(), anyMap(), anyString())).thenReturn(Optional.of(json));
            KupayAppInfoBO kupayAppInfoBO = new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J");
            when(rechargeConfig.getKupayAppIdByPayType(any(PayTypeIdEnum.class))).thenReturn(kupayAppInfoBO);
            when(rechargeConfig.getKupayIntranet()).thenReturn("http://kupay.kugou.com");
            when(rechargeConfig.getRechargeNotifyUrl(any(ReTypeEnum.class))).thenReturn("http://zuultest.fxwork.kugou.com/platform_recharge_service");
            when(rechargeConfig.getSyncUrl()).thenReturn("http://fanxing.kugou.com/index.php?action=rechargeFinish");
            when(rechargeCommonService.getOrderExpireTime()).thenReturn("20210601120212");
            when(rechargeCommonService.buildExtendStr(any(RechargeAcrossPO.class), anyMap())).thenThrow(new AckException(SysResultCode.RECHARGE_SYS_ERROR));
            kupayService.alipayV2(buildRechargeAcrossPO(), "127.0.0.1", Maps.newHashMap());
        }
    }

    private RechargeAcrossPO buildRechargeAcrossPO() {
        long kugouId = 1554749117L;
        int addTime = DateHelper.getCurrentSeconds();
        BigDecimal money = BigDecimal.valueOf(1.5);
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setRechargeOrderNum("R092020011014580532783488");
        rechargeAcrossPO.setAddTime(addTime);
        rechargeAcrossPO.setKugouId(kugouId);
        rechargeAcrossPO.setAgentKugouId(0);
        rechargeAcrossPO.setFromKugouId(kugouId);
        rechargeAcrossPO.setCoinBefore(BigDecimal.ZERO);
        rechargeAcrossPO.setCoin(BigDecimal.ZERO);
        rechargeAcrossPO.setCoinAfter(BigDecimal.ZERO);
        rechargeAcrossPO.setAmount(money);
        rechargeAcrossPO.setMoney(money);
        rechargeAcrossPO.setCoupon(BigDecimal.ZERO);
        rechargeAcrossPO.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_42.getPayTypeId());
        rechargeAcrossPO.setStatus(0);
        rechargeAcrossPO.setRefer(0);
        rechargeAcrossPO.setCFrom(39);
        rechargeAcrossPO.setChannelId(0);
        rechargeAcrossPO.setServerRoom(0);
        rechargeAcrossPO.setRealAmount(BigDecimal.ZERO);
        return rechargeAcrossPO;
    }

    @Test
    public void isKupaySuccess() {
        try (MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            String json = "{\"status\":1,\"error_code\":0,\"error_msg\":\"\",\"data\":{\"alipay_url\":\"https:\\/\\/mapi.alipay.com\\/gateway.do?service=create_direct_pay_by_user&partner=****************&_input_charset=utf-8&out_trade_no=01202106101534120100017909&subject=%E6%98%9F%E5%B8%81%E5%85%85%E5%80%BC%E6%9C%8D%E5%8A%A1&payment_type=1&total_fee=0.02&seller_email=kgqd7%40kugou.com&body=%E6%98%9F%E5%B8%81%E5%85%85%E5%80%BC%E6%9C%8D%E5%8A%A1&return_url=https%3A%2F%2Fkupay.kugou.com%2Fv1%2Falipay%2Ffx_sync&notify_url=https%3A%2F%2Fkupay.kugou.com%2Fv1%2Falipay%2Ffx_notify&extra_common_param=3&sign=8e25c24fd8681d3971ec7553e57c3c9d&sign_type=MD5\",\"order_no\":\"R092021061015340996820212\",\"out_trade_no\":\"01202106101534120100017909\"}}";
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doPostJsonBody(anyString(), anyMap(), anyString())).thenReturn(Optional.of(json));
            Assert.assertFalse(this.kupayService.isKupaySuccess(" "));
            String jsonContent = "{\"status\":1,\"error_code\":0,\"error_msg\":\"\",\"data\":{\"alipay_url\":\"https:\\/\\/mapi.alipay.com\\/gateway.do?service=create_direct_pay_by_user&partner=****************&_input_charset=utf-8&out_trade_no=01202106211444290100014080&subject=%E6%98%9F%E5%B8%81%E5%85%85%E5%80%BC%E6%9C%8D%E5%8A%A1&payment_type=1&total_fee=0.02&seller_email=kgqd7%40kugou.com&body=%E6%98%9F%E5%B8%81%E5%85%85%E5%80%BC%E6%9C%8D%E5%8A%A1&return_url=https%3A%2F%2Fkupay.kugou.com%2Fv1%2Falipay%2Ffx_sync&notify_url=https%3A%2F%2Fkupay.kugou.com%2Fv1%2Falipay%2Ffx_notify&extra_common_param=24&defaultbank=CMB&sign=20bfb3f0926b815f88132575ac808bea&sign_type=MD5\",\"order_no\":\"R092021062114442667277170\",\"out_trade_no\":\"01202106211444290100014080\"}}";
            Assert.assertTrue(this.kupayService.isKupaySuccess(jsonContent));
        }
    }

    @Test
    public void parseJsonPathChecked() {
        try (MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            String json = "{\"status\":1,\"error_code\":0,\"error_msg\":\"\",\"data\":{\"alipay_url\":\"https:\\/\\/mapi.alipay.com\\/gateway.do?service=create_direct_pay_by_user&partner=****************&_input_charset=utf-8&out_trade_no=01202106101534120100017909&subject=%E6%98%9F%E5%B8%81%E5%85%85%E5%80%BC%E6%9C%8D%E5%8A%A1&payment_type=1&total_fee=0.02&seller_email=kgqd7%40kugou.com&body=%E6%98%9F%E5%B8%81%E5%85%85%E5%80%BC%E6%9C%8D%E5%8A%A1&return_url=https%3A%2F%2Fkupay.kugou.com%2Fv1%2Falipay%2Ffx_sync&notify_url=https%3A%2F%2Fkupay.kugou.com%2Fv1%2Falipay%2Ffx_notify&extra_common_param=3&sign=8e25c24fd8681d3971ec7553e57c3c9d&sign_type=MD5\",\"order_no\":\"R092021061015340996820212\",\"out_trade_no\":\"01202106101534120100017909\"}}";
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doPostJsonBody(anyString(), anyMap(), anyString())).thenReturn(Optional.of(json));
            String jsonContent = "{\"status\":1,\"error_code\":0,\"error_msg\":\"\",\"data\":{\"appid\":1084,\"order_no\":\"R092020010100004044790426\",\"trade_no\":\"4200000485202001014914805275\",\"out_trade_no\":\"01202107162119590100016975\",\"subject\":\"\\u661f\\u5e01\\u5145\\u503c\\u670d\\u52a1\",\"desc\":\"\\u661f\\u5e01\\u5145\\u503c\\u670d\\u52a1\",\"total_fee\":5}}";
            OfflinePayDataBO offlinePayDataBO = JsonUtils.parseJsonPathObject(jsonContent, "$.data", OfflinePayDataBO.class);
            log.warn("offlinePayDataBO: {}", offlinePayDataBO);
            Assert.assertNotNull(offlinePayDataBO);
        }
    }

    @Test
    public void confirmAirwallexV1() {
        try (MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            String json = "{\"error_code\":0,\"error_msg\":\"\",\"status\":1,\"data\":{\"type\":\"call_sdk\",\"data\":{\"appId\":\"wx4c86d73fe4f82431\",\"nonceStr\":\"UAuTGjvx1OKqBzwNDpkrskQ2x0t1DSYN\",\"package\":\"Sign=WXPay\",\"partnerId\":\"115494984\",\"prepayId\":\"https:\\/\\/pci-api-demo.airwallex.com\\/pa\\/mock\\/wechat\\/hk\\/v2\\/qr_code_scanned?outTradeNo=66495557990005241660184645457065&amount=500\",\"sign\":\"8C18A688B12F76EF2F180F3CEA4DBEF07CB9272C481B0116D73E77EFFC52E335\",\"timeStamp\":\"1660184645\"}}}";
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doPostJsonBody(anyString(), anyMap(), anyString())).thenReturn(Optional.of(json));
            RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
            rechargeAcrossPO.setRechargeOrderNum("R092022081110240389178169");
            rechargeAcrossPO.setConsumeOrderNum("01202208111024030100015870");
            rechargeAcrossPO.setPayTypeId(522);
            String paymentType = "wechatpay";
            String paymentMethod = "eyJmbG93IjoiaW5hcHAiLCJvc190eXBlIjoiYW5kcm9pZCJ9";
            KupayAppInfoBO kupayAppInfoBO = new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J");
            when(rechargeConfig.getKupayAppIdByPayType(any(PayTypeIdEnum.class))).thenReturn(kupayAppInfoBO);
            when(rechargeConfig.getKupayIntranet()).thenReturn("http://kupay.kugou.com");
            BDDMockito.given(HttpClientUtils.doPostJsonBody(anyString(), anyMap(), anyString())).willReturn(Optional.of(json));
            ConfirmAirwallexV1 confirmAirwallexV1 = this.kupayService.confirmAirwallexV1(rechargeAcrossPO, paymentType, paymentMethod);
            Assert.assertNotNull(confirmAirwallexV1);
        }
    }

}
