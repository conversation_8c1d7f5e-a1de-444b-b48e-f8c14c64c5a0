package com.kugou.fanxing.recharge.service;
import java.math.BigDecimal;

import com.google.common.collect.Lists;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundOrderDao;
import com.kugou.fanxing.recharge.model.po.refund.RefundOrderPO;
import com.kugou.fanxing.recharge.model.vo.RefundHandlerVO;
import com.kugou.fanxing.recharge.service.refund.RefundNotifyService;
import com.kugou.mq.api.exception.MQException;
import com.kugou.mq.pulsar.core.PulsarTemplate;
import com.kugou.rpc.exception.HttpException;
import com.kugou.springcloud.http.KugouHttpRequest;
import com.kugou.springcloud.http.KugouHttpResponse;
import com.kugou.springcloud.http.template.KugouHttpTemplate;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.nio.charset.StandardCharsets;
import java.util.Optional;

import static org.mockito.Mockito.*;

/**
 * @Author: yuzhaopeng
 * @Description:
 * @Date: 2024/3/15 18:24
 */

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class RefundNotifyServiceTest {

    @InjectMocks
    private RefundNotifyService refundNotifyService;
    @Mock
    private RefundOrderDao refundOrderDao;

    @Mock
    private KugouHttpTemplate kugouHttpTemplate;

    @Mock
    private PulsarTemplate<String> pulsarTemplate;

    @Mock
    private ApolloConfigService apolloConfigService;

    @Test
    public void execute_zero() throws MQException {
        when(refundOrderDao.getUnNotifyList(anyLong(), anyLong(), anyLong(), anyInt())).thenReturn(Lists.newArrayList());
        // Act
        String result = refundNotifyService.execute();
        // Assert
        Assertions.assertThat( result).isEqualTo("退款通知总数量:0, 成功通知数量:0");
    }


    @Test
    public void execute_WithUnnotifyList_ReturnsTotalAndSuccessTotal() throws MQException {
        // Arrange
        when(apolloConfigService.getRefundHandler(any())).thenReturn(Optional.of(new RefundHandlerVO()));
        RefundOrderPO refundOrderPO = new RefundOrderPO();
        refundOrderPO.setId(0L);
        refundOrderPO.setRechargeOrderNum("");
        refundOrderPO.setCoin(new BigDecimal("0"));
        refundOrderPO.setPrice(new BigDecimal("0"));
        refundOrderPO.setDeductCoin(new BigDecimal("0"));
        refundOrderPO.setAddTime(0L);
        refundOrderPO.setUpdateTime(0L);
        refundOrderPO.setCancelTime(0L);
        refundOrderPO.setKugouId(0L);
        refundOrderPO.setCoinType(0);
        refundOrderPO.setHandlerType(0);
        refundOrderPO.setBusinessId("");
        refundOrderPO.setNotifyStatus(0);
        when(refundOrderDao.getUnNotifyList(anyLong(), anyLong(), anyLong(), anyInt())).thenReturn(Lists.newArrayList(refundOrderPO));
        when(pulsarTemplate.send(anyString(), anyString())).thenReturn("ok");
        when(refundOrderDao.notifySuccess(anyString())).thenReturn(1);
        // Act
        String result = refundNotifyService.execute();
        // Assert
        Assertions.assertThat( result).isEqualTo("退款通知总数量:1, 成功通知数量:1");
    }

    @Test
    public void execute_sendMqError() throws MQException {
        // Arrange
        when(apolloConfigService.getRefundHandler(any())).thenReturn(Optional.of(new RefundHandlerVO()));
        RefundOrderPO refundOrderPO = new RefundOrderPO();
        refundOrderPO.setId(0L);
        refundOrderPO.setRechargeOrderNum("");
        refundOrderPO.setCoin(new BigDecimal("0"));
        refundOrderPO.setPrice(new BigDecimal("0"));
        refundOrderPO.setDeductCoin(new BigDecimal("0"));
        refundOrderPO.setAddTime(0L);
        refundOrderPO.setUpdateTime(0L);
        refundOrderPO.setCancelTime(0L);
        refundOrderPO.setKugouId(0L);
        refundOrderPO.setCoinType(0);
        refundOrderPO.setHandlerType(0);
        refundOrderPO.setBusinessId("");
        refundOrderPO.setNotifyStatus(0);
        when(refundOrderDao.getUnNotifyList(anyLong(), anyLong(), anyLong(), anyInt())).thenReturn(Lists.newArrayList(refundOrderPO));
        when(pulsarTemplate.send(anyString(), anyString())).thenThrow(new RuntimeException(""));
        // Act
        String result = refundNotifyService.execute();
        // Assert
        Assertions.assertThat( result).isEqualTo("退款通知总数量:1, 成功通知数量:0");
    }

    @Test
    public void execute_notifySuccessError() throws MQException {
        // Arrange
        when(apolloConfigService.getRefundHandler(any())).thenReturn(Optional.of(new RefundHandlerVO()));
        RefundOrderPO refundOrderPO = new RefundOrderPO();
        refundOrderPO.setId(0L);
        refundOrderPO.setRechargeOrderNum("");
        refundOrderPO.setCoin(new BigDecimal("0"));
        refundOrderPO.setPrice(new BigDecimal("0"));
        refundOrderPO.setDeductCoin(new BigDecimal("0"));
        refundOrderPO.setAddTime(0L);
        refundOrderPO.setUpdateTime(0L);
        refundOrderPO.setCancelTime(0L);
        refundOrderPO.setKugouId(0L);
        refundOrderPO.setCoinType(0);
        refundOrderPO.setHandlerType(0);
        refundOrderPO.setBusinessId("");
        refundOrderPO.setNotifyStatus(0);
        when(refundOrderDao.getUnNotifyList(anyLong(), anyLong(), anyLong(), anyInt())).thenReturn(Lists.newArrayList(refundOrderPO));
        when(pulsarTemplate.send(anyString(), anyString())).thenReturn("");
        when(refundOrderDao.notifySuccess(anyString())).thenReturn(0);

        // Act
        String result = refundNotifyService.execute();
        // Assert
        Assertions.assertThat( result).isEqualTo("退款通知总数量:1, 成功通知数量:0");
    }


    @Test
    public void execute_kgRpcSuccess() throws MQException, HttpException {
        // Arrange
        RefundHandlerVO refundHandlerVO = new RefundHandlerVO();
        refundHandlerVO.setHandlerType(1);
        refundHandlerVO.setBusinessId("0");
        refundHandlerVO.setNotifyType(1);
        refundHandlerVO.setCallBackUrl("http://127.0.0.1/");
        RefundOrderPO refundOrderPO = new RefundOrderPO();
        refundOrderPO.setId(0L);
        refundOrderPO.setRechargeOrderNum("");
        refundOrderPO.setCoin(new BigDecimal("0"));
        refundOrderPO.setPrice(new BigDecimal("0"));
        refundOrderPO.setDeductCoin(new BigDecimal("0"));
        refundOrderPO.setAddTime(0L);
        refundOrderPO.setUpdateTime(0L);
        refundOrderPO.setCancelTime(0L);
        refundOrderPO.setKugouId(0L);
        refundOrderPO.setCoinType(0);
        refundOrderPO.setHandlerType(0);
        refundOrderPO.setBusinessId("");
        refundOrderPO.setNotifyStatus(0);
        when(apolloConfigService.getRefundHandler(any())).thenReturn(Optional.of(refundHandlerVO));
        when(refundOrderDao.getUnNotifyList(anyLong(), anyLong(), anyLong(), anyInt())).thenReturn(Lists.newArrayList(refundOrderPO));
        KugouHttpResponse res = new KugouHttpResponse();
        res.setCode(200);
        res.setContent("{\"code\":0, \"msg\":\"ok\"}".getBytes(StandardCharsets.UTF_8));
        when(kugouHttpTemplate.post(any(KugouHttpRequest.class))).thenReturn(res);
        when(pulsarTemplate.send(anyString(), anyString())).thenReturn("");
        when(refundOrderDao.notifySuccess(anyString())).thenReturn(1);
        // Act
        String result = refundNotifyService.execute();
        // Assert
        Assertions.assertThat( result).isEqualTo("退款通知总数量:1, 成功通知数量:1");
    }


    @Test
    public void execute_kgRpcErr() throws MQException, HttpException {
        // Arrange
        RefundHandlerVO refundHandlerVO = new RefundHandlerVO();
        refundHandlerVO.setHandlerType(1);
        refundHandlerVO.setBusinessId("0");
        refundHandlerVO.setNotifyType(1);
        refundHandlerVO.setCallBackUrl("http://127.0.0.1/");
        RefundOrderPO refundOrderPO = new RefundOrderPO();
        refundOrderPO.setId(0L);
        refundOrderPO.setRechargeOrderNum("");
        refundOrderPO.setCoin(new BigDecimal("0"));
        refundOrderPO.setPrice(new BigDecimal("0"));
        refundOrderPO.setDeductCoin(new BigDecimal("0"));
        refundOrderPO.setAddTime(0L);
        refundOrderPO.setUpdateTime(0L);
        refundOrderPO.setCancelTime(0L);
        refundOrderPO.setKugouId(0L);
        refundOrderPO.setCoinType(0);
        refundOrderPO.setHandlerType(0);
        refundOrderPO.setBusinessId("");
        refundOrderPO.setNotifyStatus(0);
        when(apolloConfigService.getRefundHandler(any())).thenReturn(Optional.of(refundHandlerVO));
        when(refundOrderDao.getUnNotifyList(anyLong(), anyLong(), anyLong(), anyInt())).thenReturn(Lists.newArrayList(refundOrderPO));
        KugouHttpResponse res = new KugouHttpResponse();
        res.setCode(0);
        res.setContent("{\"code\":0, \"msg\":\"ok\"}".getBytes(StandardCharsets.UTF_8));
        when(kugouHttpTemplate.post(any(KugouHttpRequest.class))).thenReturn(res);
        // Act
        String result = refundNotifyService.execute();
        // Assert
        Assertions.assertThat( result).isEqualTo("退款通知总数量:1, 成功通知数量:0");
    }


    @Test
    public void execute_kgRpcErr2() throws MQException, HttpException {
        // Arrange
        RefundHandlerVO refundHandlerVO = new RefundHandlerVO();
        refundHandlerVO.setHandlerType(1);
        refundHandlerVO.setBusinessId("0");
        refundHandlerVO.setNotifyType(1);
        refundHandlerVO.setCallBackUrl("http://127.0.0.1/");
        RefundOrderPO refundOrderPO = new RefundOrderPO();
        refundOrderPO.setId(0L);
        refundOrderPO.setRechargeOrderNum("");
        refundOrderPO.setCoin(new BigDecimal("0"));
        refundOrderPO.setPrice(new BigDecimal("0"));
        refundOrderPO.setDeductCoin(new BigDecimal("0"));
        refundOrderPO.setAddTime(0L);
        refundOrderPO.setUpdateTime(0L);
        refundOrderPO.setCancelTime(0L);
        refundOrderPO.setKugouId(0L);
        refundOrderPO.setCoinType(0);
        refundOrderPO.setHandlerType(0);
        refundOrderPO.setBusinessId("");
        refundOrderPO.setNotifyStatus(0);
        when(apolloConfigService.getRefundHandler(any())).thenReturn(Optional.of(refundHandlerVO));
        when(refundOrderDao.getUnNotifyList(anyLong(), anyLong(), anyLong(), anyInt())).thenReturn(Lists.newArrayList(refundOrderPO));
        KugouHttpResponse res = new KugouHttpResponse();
        res.setCode(0);
        res.setContent("{\"code\":100, \"msg\":\"error\"}".getBytes(StandardCharsets.UTF_8));
        when(kugouHttpTemplate.post(any(KugouHttpRequest.class))).thenReturn(res);
        // Act
        String result = refundNotifyService.execute();
        // Assert
        Assertions.assertThat( result).isEqualTo("退款通知总数量:1, 成功通知数量:0");
    }

    @Test
    public void execute_kgRpcErr3() throws MQException, HttpException {
        // Arrange
        RefundHandlerVO refundHandlerVO = new RefundHandlerVO();
        refundHandlerVO.setHandlerType(1);
        refundHandlerVO.setBusinessId("0");
        refundHandlerVO.setNotifyType(1);
        refundHandlerVO.setCallBackUrl("http://127.0.0.1/");
        RefundOrderPO refundOrderPO = new RefundOrderPO();
        refundOrderPO.setId(0L);
        refundOrderPO.setRechargeOrderNum("");
        refundOrderPO.setCoin(new BigDecimal("0"));
        refundOrderPO.setPrice(new BigDecimal("0"));
        refundOrderPO.setDeductCoin(new BigDecimal("0"));
        refundOrderPO.setAddTime(0L);
        refundOrderPO.setUpdateTime(0L);
        refundOrderPO.setCancelTime(0L);
        refundOrderPO.setKugouId(0L);
        refundOrderPO.setCoinType(0);
        refundOrderPO.setHandlerType(0);
        refundOrderPO.setBusinessId("");
        refundOrderPO.setNotifyStatus(0);
        when(apolloConfigService.getRefundHandler(any())).thenReturn(Optional.of(refundHandlerVO));
        when(refundOrderDao.getUnNotifyList(anyLong(), anyLong(), anyLong(), anyInt())).thenReturn(Lists.newArrayList(refundOrderPO));
        when(kugouHttpTemplate.post(any(KugouHttpRequest.class))).thenThrow(new RuntimeException("test"));
        // Act
        String result = refundNotifyService.execute();
        // Assert
        Assertions.assertThat( result).isEqualTo("退款通知总数量:1, 成功通知数量:0");
    }


}
