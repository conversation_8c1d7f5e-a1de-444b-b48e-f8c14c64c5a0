package com.kugou.fanxing.recharge.service;

import com.ctrip.framework.apollo.Config;
import com.google.gson.JsonParseException;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.bo.*;
import com.kugou.fanxing.recharge.model.vo.CountryInfoVo;
import com.kugou.fanxing.recharge.model.vo.LocalCurrencyConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class ApolloConfigServiceUnitTest {

    private static final String BUSINESS_LIST = "[\n" +
            "    {\n" +
            "        \"businessId\": \"10000001\",\n" +
            "        \"businessKey\": \"q4Ya9B7MVq5CYBXs\",\n" +
            "        \"businessDesc\": \"购买演唱会门票\",\n" +
            "        \"businessNotifyTopic\": \"fx.buyConcertTicket\",\n" +
            "        \"accessId\": 8000031,\n" +
            "        \"accessKey\": \"dqn35tnga9\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"businessId\": \"10000002\",\n" +
            "        \"businessKey\": \"q4Ya9B7MVq5CYBXa\",\n" +
            "        \"businessDesc\": \"购买拉新礼物\",\n" +
            "        \"businessNotifyTopic\": \"\",\n" +
            "        \"accessId\": 8000080,\n" +
            "        \"accessKey\": \"l84wylhl9i\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"businessId\": \"10000003\",\n" +
            "        \"businessKey\": \"q4Ya9B7MVq5CYBXb\",\n" +
            "        \"businessDesc\": \"购买撩主播礼物\",\n" +
            "        \"businessNotifyTopic\": \"\",\n" +
            "        \"accessId\": 8000081,\n" +
            "        \"accessKey\": \"abjn4rc8td\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"businessId\": \"10000004\",\n" +
            "        \"businessKey\": \"q4Ya9B7MVq5CYBXc\",\n" +
            "        \"businessDesc\": \"礼物随心送\",\n" +
            "        \"businessNotifyTopic\": \"\",\n" +
            "        \"accessId\": 8000105,\n" +
            "        \"accessKey\": \"v1vxo87o1w\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"businessId\": \"10000005\",\n" +
            "        \"businessKey\": \"q4Ya9B7MVq5CYBXd\",\n" +
            "        \"businessDesc\": \"虚拟商品售卖\",\n" +
            "        \"businessNotifyTopic\": \"\",\n" +
            "        \"accessId\": 0,\n" +
            "        \"accessKey\": \"\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"businessId\": \"10000006\",\n" +
            "        \"businessKey\": \"q4Ya9B7MVq5CYBXe\",\n" +
            "        \"businessDesc\": \"个人演唱会门票\",\n" +
            "        \"businessNotifyTopic\": \"\",\n" +
            "        \"accessId\": 8000031,\n" +
            "        \"accessKey\": \"dqn35tnga9\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"businessId\": \"10000007\",\n" +
            "        \"businessKey\": \"q4Ya9B7MVq5CYBXf\",\n" +
            "        \"businessDesc\": \"星际战队身份\",\n" +
            "        \"businessNotifyTopic\": \"\",\n" +
            "        \"accessId\": 0,\n" +
            "        \"accessKey\": \"\"\n" +
            "    }\n" +
            "]";

    @Mock
    private Config config;
    @Mock
    private Config kupayConfig;
    @Mock
    private Config businessConfig;
    @Mock
    private Config appstoreConfig;
    @Mock
    private Config airwallexConfig;
    @InjectMocks
    private ApolloConfigService apolloConfigService;

    @Test(expected = BizException.class)
    public void appKeyOf() {
        when(config.getProperty(anyString(), anyString())).thenReturn("[{\"appId\":10000001,\"appKey\":\"q4Ya9B7MVq5CYBXs\",\"appDesc\":\"繁星主站个人中心充值记录查询\"},{\"appId\":10000002,\"appKey\":\"49e6qxHDhmkfqDyU\",\"appDesc\":\"订单服务调用提现\"},{\"appId\":10000003,\"appKey\":\"tgzau2W2KkU2v9RT\",\"appDesc\":\"交友服务维护提现账号\"},{\"appId\":10000004,\"appKey\":\"tgzau2W2KkU2v9KT\",\"appDesc\":\"用户历史充值总额查询账号\"},{\"appId\":10000005,\"appKey\":\"tgzau2W2KkU2v9QT\",\"appDesc\":\"拉新礼物业务方查询账号\"},{\"appId\":10000006,\"appKey\":\"q4Ya9B7MVq5CYBXs\",\"appDesc\":\"礼物随心送查询用户续费信息\"},{\"appId\":10000007,\"appKey\":\"tgzau2W2KkU2v7RT\",\"appDesc\":\"客服系统\"},{\"appId\":10000008,\"appKey\":\"WB6mjkZPNKrzDv6G\",\"appDesc\":\"任务中心提现\"},{\"appId\":10000009,\"appKey\":\"WB6mjkZPNKrzDv6H\",\"appDesc\":\"实名认证活动查询充值记录\"},{\"appId\":10000010,\"appKey\":\"Do8J5LheobKBHDL8\",\"appDesc\":\"【春耕】现金红包激励\"}]");
        String salt = apolloConfigService.appKeyOf(10000001);
        Assert.assertEquals("q4Ya9B7MVq5CYBXs", salt);
        apolloConfigService.appKeyOf(10086);

        when(config.getProperty(anyString(), anyString())).thenReturn("123");
    }

    @Test
    public void getServerOptionByKey() {
        String serverKey = "rechargeTmall";
        when(config.getProperty(anyString(), anyString())).thenReturn("[{\"serverKey\":\"rechargeAppstore\",\"serverValue\":0,\"serverDesc\":\"充值渠道-AppStore\"},{\"serverKey\":\"rechargeTmall\",\"serverValue\":1,\"serverDesc\":\"天猫商城充值\"}]");
        Optional<ServerOptionBO> optionalServerOptionBO = apolloConfigService.getServerOptionByKey(serverKey);
        ServerOptionBO serverOptionBO = optionalServerOptionBO.orElseThrow(() -> new ContextedRuntimeException("test"));
        Assert.assertNotNull(serverOptionBO);
        Assert.assertEquals("rechargeTmall", serverOptionBO.getServerKey());
        Assert.assertEquals(1, serverOptionBO.getServerValue());
        Assert.assertEquals("天猫商城充值", serverOptionBO.getServerDesc());
    }

    @Test
    public void getServerOptionByKeyException() {
        String serverKey = "rechargeTmall";
        when(config.getProperty(anyString(), anyString())).thenThrow(new ContextedRuntimeException("test"));
        Optional<ServerOptionBO> optionalServerOptionBO = apolloConfigService.getServerOptionByKey(serverKey);
        Assert.assertFalse(optionalServerOptionBO.isPresent());
    }

    @Test
    public void getTmallKugouBrandIds() {
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{"100001"});
        List<String> kugouBrandIds = apolloConfigService.getTmallKugouBrandIds();
        Assert.assertNotNull(kugouBrandIds);
        Assert.assertEquals(1, kugouBrandIds.size());
        Assert.assertEquals("100001", kugouBrandIds.get(0));
    }

    @Test
    public void getTmallKugouBrandIdsException() {
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenThrow(new ContextedRuntimeException("test"));
        List<String> kugouBrandIds = apolloConfigService.getTmallKugouBrandIds();
        Assert.assertNotNull(kugouBrandIds);
        Assert.assertEquals(0, kugouBrandIds.size());
    }

    @Test
    public void getTmallFanxingBrandIds() {
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{"100002"});
        List<String> fanxingBrandIds = apolloConfigService.getTmallFanxingBrandIds();
        Assert.assertNotNull(fanxingBrandIds);
        Assert.assertEquals(1, fanxingBrandIds.size());
        Assert.assertEquals("100002", fanxingBrandIds.get(0));
    }

    @Test
    public void getTmallFanxingBrandIdsException() {
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenThrow(new ContextedRuntimeException("test"));
        List<String> fanxingBrandIds = apolloConfigService.getTmallFanxingBrandIds();
        Assert.assertNotNull(fanxingBrandIds);
        Assert.assertEquals(0, fanxingBrandIds.size());
    }

    @Test
    public void getTmallKugouAccountRulePattern() {
        when(config.getProperty(anyString(), anyString())).thenReturn("comKugouPriceX");
        Assert.assertEquals("comKugouPriceX", apolloConfigService.getTmallKugouAccountRulePattern());
    }

    @Test
    public void getTmallKugouAccountRulePatternException() {
        when(config.getProperty(anyString(), anyString())).thenThrow(new ContextedRuntimeException("test"));
        Assert.assertEquals("comKugouPrice", apolloConfigService.getTmallKugouAccountRulePattern());
    }

    @Test
    public void getTmallFanxingAccountRulePattern() {
        when(config.getProperty(anyString(), anyString())).thenReturn("comFanxingPriceX");
        Assert.assertEquals("comFanxingPriceX", apolloConfigService.getTmallFanxingAccountRulePattern());
    }

    @Test
    public void getTmallFanxingAccountRulePatternException() {
        when(config.getProperty(anyString(), anyString())).thenThrow(new ContextedRuntimeException("test"));
        Assert.assertEquals("comFanxingPrice", apolloConfigService.getTmallFanxingAccountRulePattern());
    }

    @Test
    public void getPayPalWhitelist() {
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenThrow(new ContextedRuntimeException("test"));
        List<String> whitelist = apolloConfigService.getPayPalWhitelist();
        Assert.assertEquals(0, whitelist.size());
    }

    @Test
    public void getPayPalWhitelistException() {
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{"1", "2", "3"});
        List<String> whitelist = apolloConfigService.getPayPalWhitelist();
        Assert.assertEquals(3, whitelist.size());
        Assert.assertEquals("1", whitelist.get(0));
        Assert.assertEquals("2", whitelist.get(1));
        Assert.assertEquals("3", whitelist.get(2));
    }

    @Test
    public void getChangeKugouPayService() {
        when(config.getIntProperty(anyString(), anyInt())).thenReturn(1);
        Assert.assertEquals(1, apolloConfigService.getChangeKugouPayService());
    }

    @Test
    public void getFirstRechargeJump() {
        when(config.getProperty(anyString(), anyString())).thenReturn("1");
        Assert.assertEquals("1", apolloConfigService.getFirstRechargeJump());
    }

    @Test
    public void getCouponSignKey() {
        when(config.getProperty(anyString(), anyString())).thenReturn("1");
        Assert.assertEquals("1", apolloConfigService.getCouponSignKey());
    }

    @Test
    public void couponPresentSignKey() {
        when(config.getProperty(anyString(), anyString())).thenReturn("1");
        Assert.assertEquals("1", apolloConfigService.getCouponSignKey());
    }

    @Test
    public void getGzZuulAddress() {
        when(config.getProperty(anyString(), anyString())).thenReturn("1");
        Assert.assertEquals("1", apolloConfigService.getGzZuulAddress());
    }

    @Test
    public void isKuwoOfflineRechargeEnable() {
        when(config.getBooleanProperty(anyString(), anyBoolean())).thenReturn(false);
        Assert.assertEquals(false, apolloConfigService.isKuwoEnv());
    }


    @Test
    public void getWxMiniProgramPermitRechargeMoneyListNormal() {
        String[] permitRechargeMoneyArr = new String[]{"6", "30", "100", "300", "1000", "2000"};
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(permitRechargeMoneyArr);
        List<BigDecimal> permitRechargeMoneyList = apolloConfigService.getWxMiniProgramPermitRechargeMoneyList();
        Assert.assertEquals(6, permitRechargeMoneyList.size());
    }

    @Test
    public void getWxMiniProgramPermitRechargeMoneyListException() {
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenThrow(new ContextedRuntimeException("test"));
        List<BigDecimal> permitRechargeMoneyList = apolloConfigService.getWxMiniProgramPermitRechargeMoneyList();
        Assert.assertEquals(6, permitRechargeMoneyList.size());
    }

    @Test
    public void isForbiddenPayTypeEmpty() {
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{});
        boolean isForbiddenPayType = apolloConfigService.isForbiddenPayType(PayTypeIdEnum.PAY_TYPE_ID_24.getPayTypeId());
        Assert.assertFalse(isForbiddenPayType);
    }

    @Test
    public void isForbiddenPayTypeException() {
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenThrow(new ContextedRuntimeException("test"));
        boolean isForbiddenPayType = apolloConfigService.isForbiddenPayType(PayTypeIdEnum.PAY_TYPE_ID_24.getPayTypeId());
        Assert.assertFalse(isForbiddenPayType);
    }

    @Test
    public void isForbiddenPayTypeNormal() {
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{"24"});
        Assert.assertTrue(apolloConfigService.isForbiddenPayType(PayTypeIdEnum.PAY_TYPE_ID_24.getPayTypeId()));
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{"24"});
        Assert.assertFalse(apolloConfigService.isForbiddenPayType(PayTypeIdEnum.PAY_TYPE_ID_3.getPayTypeId()));
    }

    @Test
    public void isForbiddenStdPlatsException() {
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenThrow(new ContextedRuntimeException("test"));
        boolean isForbiddenPayType = apolloConfigService.isForbiddenStdPlat(7);
        Assert.assertFalse(isForbiddenPayType);
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenThrow(new ContextedRuntimeException("test"));
        boolean isForbiddenAppId = apolloConfigService.isForbiddenAppId(3402);
        Assert.assertFalse(isForbiddenAppId);
    }

    @Test
    public void isForbiddenStdPlats() {
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{"77"});
        Assert.assertTrue(apolloConfigService.isForbiddenStdPlat(77));
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{"77"});
        Assert.assertFalse(apolloConfigService.isForbiddenStdPlat(76));
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{});
        Assert.assertFalse(apolloConfigService.isForbiddenStdPlat(76));
    }

    @Test
    public void isForbiddenAppId() {
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{"3402"});
        Assert.assertTrue(apolloConfigService.isForbiddenAppId(3402));
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{"3401"});
        Assert.assertFalse(apolloConfigService.isForbiddenAppId(3400));
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{});
        Assert.assertFalse(apolloConfigService.isForbiddenAppId(3401));
    }

    @Test
    public void getRechargePoorMode() {
        when(config.getBooleanProperty(anyString(), anyBoolean())).thenReturn(true);
        Assert.assertTrue(apolloConfigService.getRechargePoorMode());
        when(config.getProperty(anyString(), anyString())).thenReturn("0.01");
        Assert.assertEquals("0.01", apolloConfigService.getRechargePoorModeAmount().stripTrailingZeros().toPlainString());
    }

    @Test
    public void getKupayAppIdException() {
        when(config.getProperty("recharge.kupay.appId", "")).thenReturn("");
        Map<Integer, KupayAppInfoBO> kupayAppInfoMap = this.apolloConfigService.getKupayAppId();
        Assert.assertEquals(0, kupayAppInfoMap.size());
    }

    @Test
    public void getKupayAppId() {
        String json = "[" +
                "{\"kupayAppId\":1084,\"kupayAppKey\":\"sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J\",\"payTypeIds\":[]}," +
                "{\"kupayAppId\":2801,\"kupayAppKey\":\"HaZKmdmxZQiXicp6CaNVczHHk8x2MhAF\",\"payTypeIds\":[]}," +
                "{\"kupayAppId\":3214,\"kupayAppKey\":\"N39eGcqH2zeh50PLvPlJo8pUTdegSxrs\",\"payTypeIds\":[]}," +
                "{\"kupayAppId\":10002,\"kupayAppKey\":\"akthrb3wjk4njty3ib7dkxhjg4aoy3io\",\"payTypeIds\":[3,24,30,31,33]}" +
                "]";
        when(kupayConfig.getProperty("recharge.kupay.appId", "")).thenReturn(json);
        Map<Integer, KupayAppInfoBO> kupayAppInfoMap = this.apolloConfigService.getKupayAppId();
        Assert.assertEquals(4, kupayAppInfoMap.size());
        Assert.assertEquals(0, kupayAppInfoMap.get(1084).getPayTypeIds().size());
        Assert.assertEquals(5, kupayAppInfoMap.get(10002).getPayTypeIds().size());
        Assert.assertTrue(kupayAppInfoMap.get(10002).getPayTypeIds().contains(31));
    }

    @Test
    public void getKupayServerId() {
        String json = "[\n" +
                "    {\n" +
                "        \"kupayServerId\": 1870,\n" +
                "        \"kupayServerKey\": \"rYyNI4G7BdzzCYfXww3eHaW1U0haNcqE\"\n" +
                "    }\n" +
                "]";
        when(kupayConfig.getProperty("recharge.kupay.serverId", "")).thenReturn(json);
        Map<Integer, KupayServerInfoBO> kupayServerInfoMap = this.apolloConfigService.getKupayServerId();
        Assert.assertEquals(1, kupayServerInfoMap.size());
        Assert.assertEquals(1870, kupayServerInfoMap.get(1870).getKupayServerId());
        Assert.assertEquals("rYyNI4G7BdzzCYfXww3eHaW1U0haNcqE", kupayServerInfoMap.get(1870).getKupayServerKey());
    }

    @Test
    public void getKupayServerIdException() {
        when(kupayConfig.getProperty("recharge.kupay.serverId", "")).thenThrow(new ContextedRuntimeException("test"));
        Map<Integer, KupayServerInfoBO> kupayServerInfoMap = this.apolloConfigService.getKupayServerId();
        Assert.assertEquals(0, kupayServerInfoMap.size());
    }

    @Test
    public void getKupayAppTypeInfoList() {
        String json = "[" +
                "{\"pid\":35,\"appType\":\"fast\",\"appName\":\"酷狗直播极速版\"}," +
                "{\"pid\":38,\"appType\":\"hwbp\",\"appName\":\"华为白牌\"}" +
                ",{\"pid\":60,\"appType\":\"kgcc\",\"appName\":\"酷狗唱唱\"}," +
                "{\"pid\":52,\"appType\":\"tzzb\",\"appName\":\"团子直播\"}," +
                "{\"pid\":56,\"appType\":\"fxradar\",\"appName\":\"浮浮雷达\"}" +
                "]";
        when(kupayConfig.getProperty(anyString(), anyString())).thenReturn(json);
        List<KupayAppTypeInfoBO> kupayAppTypeInfoBOList = this.apolloConfigService.getKupayAppTypeInfoList();
        Assert.assertEquals(5, kupayAppTypeInfoBOList.size());
        kupayAppTypeInfoBOList.forEach(kupayAppTypeInfoBO -> {
            Assert.assertTrue(kupayAppTypeInfoBO.getPid() > 0);
            Assert.assertTrue(StringUtils.isNoneBlank(kupayAppTypeInfoBO.getAppType()));
            Assert.assertTrue(StringUtils.isNoneBlank(kupayAppTypeInfoBO.getAppName()));
        });
    }

    @Test
    public void getKupayAppTypeInfoListException() {
        when(kupayConfig.getProperty(anyString(), anyString())).thenThrow(new ContextedRuntimeException("test"));
        List<KupayAppTypeInfoBO> kupayAppTypeInfoBOList = this.apolloConfigService.getKupayAppTypeInfoList();
        Assert.assertEquals(0, kupayAppTypeInfoBOList.size());
    }

    @Test
    public void getOpenRechargeBusinessConfig() {
        String json = "[\n" +
                "    {\n" +
                "        \"openAppId\": \"10063\",\n" +
                "        \"secretKey\": \"BCu9bWVm8Do8J5LheobKBHDL8Ay3wom7\",\n" +
                "        \"businessId\": \"10063\",\n" +
                "        \"businessNotifyUrl\": \"http://127.0.0.1:18888/intranet/api/v1/notifyBusiness\"\n" +
                "    }\n" +
                "]";
        when(config.getProperty(anyString(), anyString())).thenReturn(json);
        KugouOpenBusinessBO kugouOpenBusinessBO = this.apolloConfigService.getOpenRechargeBusinessConfig("10063");
        Assert.assertEquals("10063", kugouOpenBusinessBO.getOpenAppId());
        Assert.assertEquals("BCu9bWVm8Do8J5LheobKBHDL8Ay3wom7", kugouOpenBusinessBO.getSecretKey());
        Assert.assertEquals("10063", kugouOpenBusinessBO.getBusinessId());
        Assert.assertEquals("http://127.0.0.1:18888/intranet/api/v1/notifyBusiness", kugouOpenBusinessBO.getBusinessNotifyUrl());
    }

    @Test(expected = BizException.class)
    public void getOpenRechargeBusinessConfigException() {
        when(config.getProperty(anyString(), anyString())).thenThrow(new JsonParseException("test"));
        this.apolloConfigService.getOpenRechargeBusinessConfig("10063");
    }

    @Test
    public void getWxAppIdByBizAppId() {
        when(config.getProperty(anyString(), anyString())).thenReturn("wx0c3254305fc16258");
        Assert.assertEquals("wx0c3254305fc16258", this.apolloConfigService.getWxAppIdByBizAppId(10015));
    }

    @Test(expected = BizException.class)
    public void getWxAppIdByBizAppIdWithoutConfig() {
        when(config.getProperty(anyString(), anyString())).thenReturn("");
        this.apolloConfigService.getWxAppIdByBizAppId(10015);
    }

    @Test
    public void getAccountChangeTypeByIdNormal() {
        String json = "[\n" +
                "    {\n" +
                "        \"accountChangeType\": 110231,\n" +
                "        \"actionId\": 303,\n" +
                "        \"consumeSalt\": \"orety4cmh5j\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"accountChangeType\": 980008,\n" +
                "        \"actionId\": -1,\n" +
                "        \"consumeSalt\": \"c7ej8ae2ru\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"accountChangeType\": 110209,\n" +
                "        \"actionId\": 293,\n" +
                "        \"consumeSalt\": \"zavtvunzgo\",\n" +
                "        \"topic\": \"fx.buyConcertTicket\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"accountChangeType\": 110223,\n" +
                "        \"actionId\": 297,\n" +
                "        \"consumeSalt\": \"g64d9hdwer\",\n" +
                "        \"topic\": \"fx.buyConcertTicket\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"accountChangeType\": 110014,\n" +
                "        \"actionId\": 16,\n" +
                "        \"consumeSalt\": \"gmsh\",\n" +
                "        \"topic\": \"fx.buyGuardAppstore\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"accountChangeType\": 110224,\n" +
                "        \"actionId\": 300,\n" +
                "        \"consumeSalt\": \"dap2r1ujzc\",\n" +
                "        \"topic\": \"fx.buyConcertTicket\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"accountChangeType\": 110078,\n" +
                "        \"actionId\": 231,\n" +
                "        \"consumeSalt\": \"gmxsh\",\n" +
                "        \"topic\": \"fx.buyLittleGuard\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"accountChangeType\": 110031,\n" +
                "        \"actionId\": 52,\n" +
                "        \"consumeSalt\": \"gmvip\",\n" +
                "        \"topic\": \"fx.vipCallBack\"\n" +
                "    }\n" +
                "]";
        when(businessConfig.getProperty(anyString(), anyString())).thenReturn(json);
        // 110231
        Assert.assertEquals(110231, this.apolloConfigService.getAccountChangeTypeById(110231).getAccountChangeType());
        Assert.assertEquals(303, this.apolloConfigService.getAccountChangeTypeById(110231).getActionId());
        Assert.assertEquals("orety4cmh5j", this.apolloConfigService.getAccountChangeTypeById(110231).getConsumeSalt());
        Assert.assertNull(this.apolloConfigService.getAccountChangeTypeById(110231).getTopic());
        // 110224
        Assert.assertEquals(110224, this.apolloConfigService.getAccountChangeTypeById(110224).getAccountChangeType());
        Assert.assertEquals(300, this.apolloConfigService.getAccountChangeTypeById(110224).getActionId());
        Assert.assertEquals("dap2r1ujzc", this.apolloConfigService.getAccountChangeTypeById(110224).getConsumeSalt());
        Assert.assertEquals("fx.buyConcertTicket", this.apolloConfigService.getAccountChangeTypeById(110224).getTopic());
    }

    @Test(expected = ContextedRuntimeException.class)
    public void getAccountChangeTypeByIdAbnormal() {
        String json = "[\n" +
                "    {\n" +
                "        \"accountChangeType\": 980008,\n" +
                "        \"actionId\": -1,\n" +
                "        \"consumeSalt\": \"cvd3a5mg0s\"\n" +
                "    }\n" +
                "]";
        when(businessConfig.getProperty(anyString(), anyString())).thenReturn(json);
        AccountChangeTypeBO accountChangeTypeBO = this.apolloConfigService.getAccountChangeTypeById(110231);
        Assert.assertEquals(110231, accountChangeTypeBO.getAccountChangeType());
        Assert.assertEquals(303, accountChangeTypeBO.getActionId());
        Assert.assertEquals("of1b7cmh5j", accountChangeTypeBO.getTopic());
    }

    @Test(expected = ContextedRuntimeException.class)
    public void getAccountChangeTypeByIdException() {
        when(businessConfig.getProperty(anyString(), anyString())).thenThrow(new JsonParseException("test"));
        this.apolloConfigService.getAccountChangeTypeById(110231);
    }

    @Test
    public void useKupayAppTypeDirectly() {
        when(this.kupayConfig.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{"player1", "player2"});
        Assert.assertTrue(this.apolloConfigService.useKupayAppTypeDirectly("player1"));
        Assert.assertTrue(this.apolloConfigService.useKupayAppTypeDirectly("player2"));
        Assert.assertFalse(this.apolloConfigService.useKupayAppTypeDirectly("player3"));
        when(this.kupayConfig.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{""});
        Assert.assertFalse(this.apolloConfigService.useKupayAppTypeDirectly("player1"));
        Assert.assertFalse(this.apolloConfigService.useKupayAppTypeDirectly("player2"));
        Assert.assertFalse(this.apolloConfigService.useKupayAppTypeDirectly("player3"));
    }

    @Test
    public void getKugouOpenServerConfig() {
        when(config.getProperty(anyString(), anyString())).thenReturn("{\n" +
                "    \"serverId\": \"2144\",\n" +
                "    \"serverKey\": \"8FY95AlGShOjf00bZuAfth6Plj4AAoma\"\n" +
                "}");
        KugouOpenServerBO kugouOpenServerBO = this.apolloConfigService.getKugouOpenServerConfig();
        Assert.assertEquals("2144", kugouOpenServerBO.getServerId());
        Assert.assertEquals("8FY95AlGShOjf00bZuAfth6Plj4AAoma", kugouOpenServerBO.getServerKey());
    }

    @Test(expected = BizException.class)
    public void getKugouOpenServerConfigException() {
        when(config.getProperty(anyString(), anyString())).thenThrow(new ContextedRuntimeException("test"));
        this.apolloConfigService.getKugouOpenServerConfig();
    }

    @Test
    public void getWechatQrCodeExpireMinutes() {
        when(config.getIntProperty(anyString(), anyInt())).thenReturn(2);
        Assert.assertEquals(2, this.apolloConfigService.getWechatQrCodeExpireMinutes());
        when(config.getIntProperty(anyString(), anyInt())).thenReturn(-1);
        Assert.assertEquals(1, this.apolloConfigService.getWechatQrCodeExpireMinutes());
    }

    @Test
    public void getWechatQrCodeRefreshSecond() {
        when(config.getIntProperty(anyString(), anyInt())).thenReturn(3);
        Assert.assertEquals(5, this.apolloConfigService.getWechatQrCodeRefreshSecond());
        when(config.getIntProperty(anyString(), anyInt())).thenReturn(7);
        Assert.assertEquals(7, this.apolloConfigService.getWechatQrCodeRefreshSecond());
    }

    @Test
    public void getCouponFailureResultCodeList() {
        when(config.getProperty(anyString(), anyString())).thenReturn("16,4");
        Assert.assertEquals(2, this.apolloConfigService.getCouponFailureResultCodeList().size());
        when(config.getProperty(anyString(), anyString())).thenReturn("16, 4 , 9");
        List<Integer> failureResultCodeList = this.apolloConfigService.getCouponFailureResultCodeList();
        Assert.assertEquals(3, failureResultCodeList.size());
        Assert.assertTrue(failureResultCodeList.contains(16));
        Assert.assertTrue(failureResultCodeList.contains(4));
        Assert.assertTrue(failureResultCodeList.contains(9));
        when(config.getProperty(anyString(), anyString())).thenReturn(null);
        failureResultCodeList = this.apolloConfigService.getCouponFailureResultCodeList();
        Assert.assertTrue(failureResultCodeList.contains(16));
    }

    @Test
    public void getCouponFailureResultCodeListException() {
        when(config.getProperty(anyString(), anyString())).thenThrow(new ContextedRuntimeException("test"));
        List<Integer> failureResultCodeList = this.apolloConfigService.getCouponFailureResultCodeList();
        Assert.assertTrue(failureResultCodeList.contains(16));
    }

    @Test
    public void getRechargeOrderExpireMinutes() {
        when(this.config.getIntProperty(anyString(), anyInt())).thenReturn(-1);
        Assert.assertEquals(1, this.apolloConfigService.getRechargeOrderExpireMinutes());
        when(this.config.getIntProperty(anyString(), anyInt())).thenReturn(0);
        Assert.assertEquals(1, this.apolloConfigService.getRechargeOrderExpireMinutes());
        when(this.config.getIntProperty(anyString(), anyInt())).thenReturn(2);
        Assert.assertEquals(2, this.apolloConfigService.getRechargeOrderExpireMinutes());
    }

    @Test
    public void getAdjustTradeTimeRechargeOrderNums() {
        when(config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[] { "R092021060116265407976687" });
        List<String> rechargeOrderNumList = this.apolloConfigService.getAdjustTradeTimeRechargeOrderNums();
        Assert.assertEquals(1, rechargeOrderNumList.size());
        rechargeOrderNumList = this.apolloConfigService.getReproduceRechargeOrderNums();
        Assert.assertEquals(1, rechargeOrderNumList.size());
    }

    @Test
    public void getProxyConfig() {
        when(this.appstoreConfig.getProperty(anyString(), anyString())).thenReturn("forward.proxy.kgidc.cn");
        when(this.appstoreConfig.getIntProperty(anyString(), anyInt())).thenReturn(3128);
        ProxyConfigBO proxyConfigBO = this.apolloConfigService.getProxyConfig();
        Assert.assertEquals(proxyConfigBO.getProxyHost(), this.apolloConfigService.getProxyConfig().getProxyHost());
        Assert.assertEquals(proxyConfigBO.getProxyPort(), this.apolloConfigService.getProxyConfig().getProxyPort());
    }

    @Test
    public void getOCoinKupayAppId() {
        when(this.config.getIntProperty(anyString(), anyInt())).thenReturn(10058);
        Assert.assertEquals(10058, this.apolloConfigService.getOCoinKupayAppId());
    }

    @Test
    public void matchDrawCashRemarkTest(){
        when(config.getProperty(anyString(), anyString())).thenReturn("[{\"drawType\":1,\"bizAppId\":10015,\"remark\":\"任务中心金币提现\"},{\"drawType\":2,\"bizAppId\":10015,\"remark\":\"任务中心金币提现\"},{\"drawType\":32,\"bizAppId\":10015,\"remark\":\"任务中心金币提现\"},{\"drawType\":1,\"bizAppId\":10025,\"remark\":\"酷狗直播现金红包提现\"},{\"drawType\":2,\"bizAppId\":10025,\"remark\":\"酷狗直播现金红包提现\"},{\"drawType\":32,\"bizAppId\":10025,\"remark\":\"酷狗直播现金红包提现\"},{\"drawType\":1,\"bizAppId\":10028,\"remark\":\"欢迎下载酷狗直播，你喜欢的主播在等你哦！\"},{\"drawType\":2,\"bizAppId\":10028,\"remark\":\"欢迎下载酷狗直播，你喜欢的主播在等你哦！\"},{\"drawType\":32,\"bizAppId\":10028,\"remark\":\"欢迎下载酷狗直播，你喜欢的主播在等你哦！\"}]");
        String d = "default";
        String s = this.apolloConfigService.matchDrawCashRemark(10015, d);
        Assert.assertEquals(s,"任务中心金币提现");

        s = this.apolloConfigService.matchDrawCashRemark(10028, d);
        Assert.assertEquals(s,"欢迎下载酷狗直播，你喜欢的主播在等你哦！");

        s = this.apolloConfigService.matchDrawCashRemark(10025, d);
        Assert.assertEquals(s,"酷狗直播现金红包提现");

        s = this.apolloConfigService.matchDrawCashRemark(123, d);
        Assert.assertEquals(s,d);

        when(config.getProperty(anyString(), anyString())).thenReturn("drawType\":1,\"bizAppId\":10015,\"remark\":\"任务中心金币提现\"},{\"drawType\":2,\"bizAppId\":10015,\"remark\":\"任务中心金币提现\"},{\"drawType\":32,\"bizAppId\":10015,\"remark\":\"任务中心金币提现\"},{\"drawType\":1,\"bizAppId\":10025,\"remark\":\"酷狗直播现金红包提现\"},{\"drawType\":2,\"bizAppId\":10025,\"remark\":\"酷狗直播现金红包提现\"},{\"drawType\":32,\"bizAppId\":10025,\"remark\":\"酷狗直播现金红包提现\"},{\"drawType\":1,\"bizAppId\":10028,\"remark\":\"欢迎下载酷狗直播，你喜欢的主播在等你哦！\"},{\"drawType\":2,\"bizAppId\":10028,\"remark\":\"欢迎下载酷狗直播，你喜欢的主播在等你哦！\"},{\"drawType\":32,\"bizAppId\":10028,\"remark\":\"欢迎下载酷狗直播，你喜欢的主播在等你哦！\"}]");
        s = this.apolloConfigService.matchDrawCashRemark(10025, d);
        Assert.assertEquals(s,d);
    }

    @Test
    public void getBusinessInfoById() {
        Optional<BusinessInfoBo> optionalBusinessInfoBo;
        when(businessConfig.getProperty(anyString(), anyString())).thenReturn(BUSINESS_LIST);
        optionalBusinessInfoBo = apolloConfigService.getBusinessInfoById("-1");
        Assert.assertFalse(optionalBusinessInfoBo.isPresent());
        when(businessConfig.getProperty(anyString(), anyString())).thenReturn(BUSINESS_LIST);
        optionalBusinessInfoBo = apolloConfigService.getBusinessInfoById("10000004");
        Assert.assertTrue(optionalBusinessInfoBo.isPresent());
        Assert.assertEquals("礼物随心送", optionalBusinessInfoBo.get().getBusinessDesc());
        Assert.assertEquals(8000105, optionalBusinessInfoBo.get().getAccessId());
        Assert.assertEquals("v1vxo87o1w", optionalBusinessInfoBo.get().getAccessKey());
        optionalBusinessInfoBo = apolloConfigService.getBusinessInfoById("10000005");
        Assert.assertTrue(optionalBusinessInfoBo.isPresent());
        Assert.assertEquals("虚拟商品售卖", optionalBusinessInfoBo.get().getBusinessDesc());
        Assert.assertEquals("q4Ya9B7MVq5CYBXd", apolloConfigService.businessKeyOf("10000005"));
        Assert.assertEquals("虚拟商品售卖", apolloConfigService.businessDescOf("10000005"));
        Assert.assertEquals("", apolloConfigService.businessNotifyTopicOf("10000005"));
        Assert.assertEquals(0, optionalBusinessInfoBo.get().getAccessId());
        Assert.assertEquals("", optionalBusinessInfoBo.get().getAccessKey());
    }

    @Test
    public void isAppStoreRecharge() {
        when(this.config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{"2", "6"});
        Assert.assertFalse(this.apolloConfigService.isAppStoreRecharge(0));
        Assert.assertTrue(this.apolloConfigService.isAppStoreRecharge(2));
        Assert.assertTrue(this.apolloConfigService.isAppStoreRecharge(6));
        when(this.config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{});
        Assert.assertFalse(this.apolloConfigService.isAppStoreRecharge(0));
        Assert.assertFalse(this.apolloConfigService.isAppStoreRecharge(2));
        Assert.assertFalse(this.apolloConfigService.isAppStoreRecharge(6));
    }

    @Test
    public void isInvalidOpenidPatterns() {
        when(this.config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[] {"undefine"});
        Assert.assertTrue(this.apolloConfigService.isInvalidOpenidPatterns("undefine"));
        Assert.assertFalse(this.apolloConfigService.isInvalidOpenidPatterns("osVWIjimHCDYGdgCuf_0apng8xCM"));
        when(this.config.getArrayProperty(anyString(), anyString(), any(String[].class))).thenThrow(new ContextedRuntimeException("test"));
        Assert.assertFalse(this.apolloConfigService.isInvalidOpenidPatterns("undefine"));
    }

    @Test
    public void allowGetKupayConfigByPid() {
        when(this.kupayConfig.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{"96"});
        Assert.assertFalse(this.apolloConfigService.allowGetKupayConfigByPid(95));
        Assert.assertTrue(this.apolloConfigService.allowGetKupayConfigByPid(96));
        Assert.assertFalse(this.apolloConfigService.allowGetKupayConfigByPid(97));
        when(this.kupayConfig.getArrayProperty(anyString(), anyString(), any(String[].class))).thenThrow(new ContextedRuntimeException("test"));
        Assert.assertFalse(this.apolloConfigService.allowGetKupayConfigByPid(95));
        Assert.assertFalse(this.apolloConfigService.allowGetKupayConfigByPid(96));
        Assert.assertFalse(this.apolloConfigService.allowGetKupayConfigByPid(97));
    }

    @Test
    public void getOverseasCountryList() {
        String json = "[{\"country\":\"TW\",\"displayCountry\":\"中国台湾\",\"allowPaymentMethod\":[]},{\"country\":\"HK\",\"displayCountry\":\"中国香港\",\"allowPaymentMethod\":[]},{\"country\":\"MO\",\"displayCountry\":\"中国澳门\",\"allowPaymentMethod\":[]},{\"country\":\"TH\",\"displayCountry\":\"泰国\",\"allowPaymentMethod\":[]},{\"country\":\"ID\",\"displayCountry\":\"印度尼西亚\",\"allowPaymentMethod\":[]},{\"country\":\"SG\",\"displayCountry\":\"新加坡\",\"allowPaymentMethod\":[]},{\"country\":\"MY\",\"displayCountry\":\"马来西亚\",\"allowPaymentMethod\":[]},{\"country\":\"MM\",\"displayCountry\":\"缅甸\",\"allowPaymentMethod\":[]}]";
        when(this.airwallexConfig.getProperty(anyString(), anyString())).thenReturn(json);
        List<CountryInfoVo> countryInfoVoList = this.apolloConfigService.getOverseasCountryList();
        Assert.assertEquals(8, countryInfoVoList.size());
    }

    @Test
    public void getOverseasCountryListException() {
        when(this.airwallexConfig.getProperty(anyString(), anyString())).thenThrow(new ContextedRuntimeException("test"));
        List<CountryInfoVo> countryInfoVoList = this.apolloConfigService.getOverseasCountryList();
        Assert.assertEquals(0, countryInfoVoList.size());
    }

    @Test
    public void getOverseasCountryListNotConfig() {
        when(this.airwallexConfig.getProperty(anyString(), anyString())).thenReturn(null);
        List<CountryInfoVo> countryInfoVoList = this.apolloConfigService.getOverseasCountryList();
        Assert.assertEquals(0, countryInfoVoList.size());
    }

    @Test
    public void getLocalAmountListByCountry() {
        String json = "[\n" +
                "    {\n" +
                "        \"currency\": \"HKD\",\n" +
                "        \"currencyAmount\": \"5\",\n" +
                "        \"local2usdRates\": \"0.127388535\",\n" +
                "        \"usd2cnyRates\": \"6.7194\",\n" +
                "        \"volatilityCoefficient\": \"0\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"currency\": \"HKD\",\n" +
                "        \"currencyAmount\": \"20\",\n" +
                "        \"local2usdRates\": \"0.127388535\",\n" +
                "        \"usd2cnyRates\": \"6.7194\",\n" +
                "        \"volatilityCoefficient\": \"0\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"currency\": \"HKD\",\n" +
                "        \"currencyAmount\": \"100\",\n" +
                "        \"local2usdRates\": \"0.127388535\",\n" +
                "        \"usd2cnyRates\": \"6.7194\",\n" +
                "        \"volatilityCoefficient\": \"0\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"currency\": \"HKD\",\n" +
                "        \"currencyAmount\": \"500\",\n" +
                "        \"local2usdRates\": \"0.127388535\",\n" +
                "        \"usd2cnyRates\": \"6.7194\",\n" +
                "        \"volatilityCoefficient\": \"0\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"currency\": \"HKD\",\n" +
                "        \"currencyAmount\": \"1000\",\n" +
                "        \"local2usdRates\": \"0.127388535\",\n" +
                "        \"usd2cnyRates\": \"6.7194\",\n" +
                "        \"volatilityCoefficient\": \"0\"\n" +
                "    }\n" +
                "]";
        when(this.airwallexConfig.getProperty(anyString(), anyString())).thenReturn(json);
        List<LocalCurrencyConfig> localCurrencyConfigList = this.apolloConfigService.getOverseasCurrencyConfigByCountry("HK");
        Assert.assertEquals(5, localCurrencyConfigList.size());
        LocalCurrencyConfig localCurrencyConfig = localCurrencyConfigList.stream().findFirst().orElseThrow(() -> new ContextedRuntimeException("not found"));
        Assert.assertEquals("HKD", localCurrencyConfig.getCurrency());
        Assert.assertEquals("5", localCurrencyConfig.getCurrencyAmount().stripTrailingZeros().toPlainString());
        Assert.assertEquals("0.127388535", localCurrencyConfig.getLocal2usdRates().stripTrailingZeros().toPlainString());
        Assert.assertEquals("6.7194", localCurrencyConfig.getUsd2cnyRates().stripTrailingZeros().toPlainString());
        Assert.assertEquals("0", localCurrencyConfig.getVolatilityCoefficient().stripTrailingZeros().toPlainString());
    }

    @Test
    public void getLocalAmountListByCountryException() {
        when(this.airwallexConfig.getProperty(anyString(), anyString())).thenThrow(new ContextedRuntimeException("test"));
        List<LocalCurrencyConfig> localCurrencyConfigList = this.apolloConfigService.getOverseasCurrencyConfigByCountry("HK");
        Assert.assertEquals(0, localCurrencyConfigList.size());
    }

    @Test
    public void isAirwallexAllowAccount() {
        when(this.airwallexConfig.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{"2"});
        Assert.assertFalse(this.apolloConfigService.isAirwallexAllowAccount(1));
        Assert.assertTrue(this.apolloConfigService.isAirwallexAllowAccount(2));
        Assert.assertFalse(this.apolloConfigService.isAirwallexAllowAccount(3));
        when(this.airwallexConfig.getArrayProperty(anyString(), anyString(), any(String[].class))).thenThrow(new ContextedRuntimeException("test"));
        Assert.assertFalse(this.apolloConfigService.isAirwallexAllowAccount(2));
    }

    @Test
    public void isAirwallexBlockAccount() {
        when(this.airwallexConfig.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[]{"2"});
        Assert.assertFalse(this.apolloConfigService.isAirwallexBlockAccount(1));
        Assert.assertTrue(this.apolloConfigService.isAirwallexBlockAccount(2));
        Assert.assertFalse(this.apolloConfigService.isAirwallexBlockAccount(3));
        when(this.airwallexConfig.getArrayProperty(anyString(), anyString(), any(String[].class))).thenThrow(new ContextedRuntimeException("test"));
        Assert.assertFalse(this.apolloConfigService.isAirwallexBlockAccount(2));
    }

    @Test
    public void getPddProductPriceList() {
        when(this.config.getProperty(anyString(), anyString())).thenReturn(
                "[{\"prodNo\":\"a\",\"prodCoin\":\"10\"},{\"prodNo\":\"b\",\"prodCoin\":\"0\"}]");
        List<PddProductBo> pddProductBoList = this.apolloConfigService.getPddProductPriceList();
        Assert.assertEquals(2, pddProductBoList.size());
    }

    @Test
    public void getPddProductPriceListException() {
        when(this.config.getProperty(anyString(), anyString())).thenThrow(new ContextedRuntimeException("test"));
        List<PddProductBo> pddProductBoList = this.apolloConfigService.getPddProductPriceList();
        Assert.assertNotNull(pddProductBoList);
        Assert.assertEquals(0, pddProductBoList.size());
    }

    @Test
    public void getRechargeAwardConfig() {
        when(this.businessConfig.getProperty(anyString(), anyString())).thenReturn("{}");
        Optional<RechargeAwardConfig> optionalRechargeAwardConfig = this.apolloConfigService.getRechargeAwardConfig();
        Assert.assertFalse(optionalRechargeAwardConfig.isPresent());
    }
}
