package com.kugou.fanxing.recharge.service.offline;

import com.google.common.collect.Lists;
import com.kugou.fanxing.recharge.model.po.offline.BigRebateConfig;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@RunWith(MockitoJUnitRunner.Silent.class)
public class BigRebateServiceTest {

    @InjectMocks
    private BigRebateService bigRebateService;

    @Test
    public void findBigRebateConfig1() {
        List<BigRebateConfig> bigRebateConfigList = buildBigRebateConfigs();
        Optional<BigRebateConfig> bigRebateConfigOptional1 = bigRebateService.findBigRebateConfig(bigRebateConfigList, BigDecimal.valueOf(100000.00));
        Assert.assertTrue(bigRebateConfigOptional1.isPresent());
        Assert.assertEquals(1, bigRebateConfigOptional1.get().getId());
    }

    @Test
    public void findBigRebateConfig2() {
        List<BigRebateConfig> bigRebateConfigList = buildBigRebateConfigs();
        Optional<BigRebateConfig> bigRebateConfigOptional2 = bigRebateService.findBigRebateConfig(bigRebateConfigList, BigDecimal.valueOf(300000.00));
        Assert.assertTrue(bigRebateConfigOptional2.isPresent());
        Assert.assertEquals(2, bigRebateConfigOptional2.get().getId());
    }

    @Test
    public void findBigRebateConfig3() {
        List<BigRebateConfig> bigRebateConfigList = buildBigRebateConfigs();
        Optional<BigRebateConfig> bigRebateConfigOptional3 = bigRebateService.findBigRebateConfig(bigRebateConfigList, BigDecimal.valueOf(500000.00));
        Assert.assertTrue(bigRebateConfigOptional3.isPresent());
        Assert.assertEquals(3, bigRebateConfigOptional3.get().getId());
    }

    @Test
    public void findBigRebateConfig4() {
        List<BigRebateConfig> bigRebateConfigList = buildBigRebateConfigs();
        Optional<BigRebateConfig> bigRebateConfigOptional4 = bigRebateService.findBigRebateConfig(bigRebateConfigList, BigDecimal.valueOf(1000000.00));
        Assert.assertTrue(bigRebateConfigOptional4.isPresent());
        Assert.assertEquals(4, bigRebateConfigOptional4.get().getId());
    }

    @Test
    public void findBigRebateConfig5() {
        List<BigRebateConfig> bigRebateConfigList = buildBigRebateConfigs();
        Optional<BigRebateConfig> bigRebateConfigOptional5 = bigRebateService.findBigRebateConfig(bigRebateConfigList, BigDecimal.valueOf(5000000.00));
        Assert.assertTrue(bigRebateConfigOptional5.isPresent());
        Assert.assertEquals(5, bigRebateConfigOptional5.get().getId());
    }

    @Test
    public void findBigRebateConfig6() {
        List<BigRebateConfig> bigRebateConfigList = buildBigRebateConfigs();
        Optional<BigRebateConfig> bigRebateConfigOptional5 = bigRebateService.findBigRebateConfig(bigRebateConfigList, BigDecimal.valueOf(5000000.00));
        Assert.assertTrue(bigRebateConfigOptional5.isPresent());
        Assert.assertEquals(5, bigRebateConfigOptional5.get().getId());
    }

    private List<BigRebateConfig> buildBigRebateConfigs() {
        BigRebateConfig bigRebateConfig1 = new BigRebateConfig()
                .setId(1)
                .setStartAmount(BigDecimal.valueOf(100000.00))
                .setEndAmount(BigDecimal.valueOf(300000.00))
                .setRebateRate(BigDecimal.valueOf(0.12));
        BigRebateConfig bigRebateConfig2 = new BigRebateConfig()
                .setId(2)
                .setStartAmount(BigDecimal.valueOf(300000.00))
                .setEndAmount(BigDecimal.valueOf(500000.00))
                .setRebateRate(BigDecimal.valueOf(0.14));
        BigRebateConfig bigRebateConfig3 = new BigRebateConfig()
                .setId(3)
                .setStartAmount(BigDecimal.valueOf(500000.00))
                .setEndAmount(BigDecimal.valueOf(1000000.00))
                .setRebateRate(BigDecimal.valueOf(0.16));
        BigRebateConfig bigRebateConfig4 = new BigRebateConfig()
                .setId(4)
                .setStartAmount(BigDecimal.valueOf(1000000.00))
                .setEndAmount(BigDecimal.valueOf(5000000.00))
                .setRebateRate(BigDecimal.valueOf(0.18));
        BigRebateConfig bigRebateConfig5 = new BigRebateConfig()
                .setId(5)
                .setStartAmount(BigDecimal.valueOf(5000000.00))
                .setEndAmount(BigDecimal.valueOf(0))
                .setRebateRate(BigDecimal.valueOf(0.20));
        return Lists.newArrayList(bigRebateConfig1, bigRebateConfig2, bigRebateConfig3, bigRebateConfig4, bigRebateConfig5);
    }

}