package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.constant.HandlerTypeEnum;
import com.kugou.fanxing.recharge.model.vo.RefundHandlerVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * @Author: yuzhaopeng
 * @Description:
 * @Date: 2024/3/15 18:05
 */
@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class RefundHandlerServiceUnitTest {

    @InjectMocks
    private RefundHandlerService refundHandlerService;

    @Mock
    private ApolloConfigService apolloConfigService;

    @Test
    public void getHandlerType_WithValidRefundHandler_ReturnsHandlerType() {
        RefundHandlerVO refundHandlerVO = new RefundHandlerVO();
        refundHandlerVO.setBusinessId("1");
        refundHandlerVO.setHandlerType(123);
        when(apolloConfigService.getRefundHandler("1")).thenReturn(Optional.of(refundHandlerVO));
        // Act
        int result = refundHandlerService.getHandlerType("1");
        Assert.assertEquals(123,result);
    }

    @Test
    public  void getHandlerType_WithInvalidRefundHandler_ReturnsDefaultHandlerType() {
        when(apolloConfigService.getRefundHandler("2")).thenReturn(Optional.empty());
        // Act
        int result = refundHandlerService.getHandlerType("2");
        Assert.assertEquals(HandlerTypeEnum.DEFAULT.getCode(),result);
    }
}
