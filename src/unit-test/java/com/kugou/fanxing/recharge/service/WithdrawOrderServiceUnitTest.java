package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawOrderDao;
import com.kugou.fanxing.recharge.model.po.WithdrawOrderPO;
import com.kugou.fanxing.recharge.model.request.QueryWithdrawOrderReq;
import com.kugou.fanxing.recharge.model.request.QueryWithdrawOrderWechatReq;
import com.kugou.fanxing.recharge.service.common.DencryptService;
import com.kugou.fanxing.recharge.service.withdraw.WithdrawOrderService;
import com.kugou.fanxing.recharge.util.JsonResult;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class WithdrawOrderServiceUnitTest {
    @Mock
    private WithdrawOrderDao withdrawOrderDao;

    @Mock
    private DencryptService dencryptService;

    @InjectMocks
    private WithdrawOrderService withdrawOrderService;

    @Test
    public void withdrawOrderVerifyWechat() {
        QueryWithdrawOrderWechatReq param = new QueryWithdrawOrderWechatReq();
        param.setBiz_appid(1);
        param.setOpenid("xx");
        param.setTotal_fee(BigDecimal.valueOf(100));
        WithdrawOrderPO orderPO = new WithdrawOrderPO();
        orderPO.setBizAppId(1);
        orderPO.setOpenid("xx");
        when(withdrawOrderDao.getDrawCashOrderById(anyLong())).thenReturn(new WithdrawOrderPO());
        JsonResult<Map<String, Object>> jsonResult = this.withdrawOrderService.withdrawOrderVerifyWechat(param);
        Assert.assertEquals(SysResultCode.SUCCESS.getCode(), jsonResult.getCode());
    }

    @Test
    public void withdrawOrderVerifyWechatException() {
        QueryWithdrawOrderWechatReq param = new QueryWithdrawOrderWechatReq();
        param.setBiz_appid(1);
        param.setOpenid("xx");
        param.setTotal_fee(BigDecimal.valueOf(100));
        WithdrawOrderPO orderPO = new WithdrawOrderPO();
        orderPO.setBizAppId(1);
        orderPO.setOpenid("xx");
        when(withdrawOrderDao.getDrawCashOrderById(anyLong())).thenThrow(new ContextedRuntimeException());
        Assert.assertEquals(SysResultCode.RECHARGE_SYS_ERROR.getCode(), this.withdrawOrderService.withdrawOrderVerifyWechat(param).getCode());
    }

    @Test
    public void isValidWithdrawOrderWechat() {
        QueryWithdrawOrderWechatReq param = new QueryWithdrawOrderWechatReq();
        param.setBiz_appid(1);
        param.setOpenid("xx");
        param.setTotal_fee(BigDecimal.valueOf(100));
        WithdrawOrderPO orderPO = new WithdrawOrderPO();
        orderPO.setBizAppId(1);
        orderPO.setOpenid("xx");
        orderPO.setTotalAmount(BigDecimal.valueOf(1));
        Assert.assertTrue(this.withdrawOrderService.isValidWithdrawOrderWechat(param, orderPO));
    }

    @Test
    public void testWithdrawOrderIsNull() throws JSONException {
        QueryWithdrawOrderReq param = buildQueryWithdrawOrderReq();
        when(withdrawOrderDao.getDrawCashOrderById(anyLong())).thenReturn(null);
        JsonResult<Map<String, Object>> jsonResult = withdrawOrderService.withdrawOrderVerify(param);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertEquals(0, jsonResult.getCode());
        int permitRecharge = new JSONObject(jsonResult.getData()).getInt("exist");
        Assert.assertEquals(0, permitRecharge);
    }

    @Test
    public void testIsValidWithdrawOrder() throws JSONException{
        QueryWithdrawOrderReq param = buildQueryWithdrawOrderReq();
        param.setUser_account("***********");
        when(withdrawOrderDao.getDrawCashOrderById(anyLong())).thenReturn(buildWithdrawOrderPO());
        when(dencryptService.decrypt(anyString())).thenReturn("***********");
        JsonResult<Map<String, Object>> jsonResult = withdrawOrderService.withdrawOrderVerify(param);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertEquals(0, jsonResult.getCode());
        int permitRecharge = new JSONObject(jsonResult.getData()).getInt("exist");
        Assert.assertEquals(0, permitRecharge);
    }

    @Test
    public void testCorrectRsp() throws JSONException{
        QueryWithdrawOrderReq param = buildQueryWithdrawOrderReq();
        when(withdrawOrderDao.getDrawCashOrderById(anyLong())).thenReturn(buildWithdrawOrderPO());
        when(dencryptService.decrypt(anyString())).thenReturn("***********");
        JsonResult<Map<String, Object>> jsonResult = withdrawOrderService.withdrawOrderVerify(param);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertEquals(0, jsonResult.getCode());
        int permitRecharge = new JSONObject(jsonResult.getData()).getInt("exist");
        Assert.assertEquals(1, permitRecharge);
    }

    @Test
    public void testException() throws JSONException{
        QueryWithdrawOrderReq param = buildQueryWithdrawOrderReq();
        when(withdrawOrderDao.getDrawCashOrderById(anyLong())).thenThrow(new ContextedRuntimeException("test"));
        JsonResult<Map<String, Object>> jsonResult = withdrawOrderService.withdrawOrderVerify(param);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertEquals(1, jsonResult.getCode());
        int permitRecharge = new JSONObject(jsonResult.getData()).getInt("exist");
        Assert.assertEquals(0, permitRecharge);
    }
    private QueryWithdrawOrderReq buildQueryWithdrawOrderReq(){
        QueryWithdrawOrderReq param = new QueryWithdrawOrderReq();
        param.setBiz_appid(1084);
        param.setUserid(**********);
        param.setOrder_no(432982223583449087L);
        param.setUser_account("***********");
        param.setTotal_fee(new BigDecimal("0.10"));
        return param;
    }

    private WithdrawOrderPO  buildWithdrawOrderPO(){
        WithdrawOrderPO withdrawOrderPO = new WithdrawOrderPO();
        withdrawOrderPO.setBizAppId(1084);
        withdrawOrderPO.setKugouId(**********);
        withdrawOrderPO.setOrderId(432982223583449087L);
        withdrawOrderPO.setAccountEncrypted("s81nMQa8vps_lTH_6Dyl9w@DQA+AIQCAAA=");
        withdrawOrderPO.setTotalAmount(new BigDecimal("0.10"));
        return withdrawOrderPO;
    }
}
