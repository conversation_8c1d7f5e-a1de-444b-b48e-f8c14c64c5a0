package com.kugou.fanxing.recharge.service;

import com.google.common.collect.Lists;
import com.kugou.config.Env;
import com.kugou.fanxing.biz.commons.util.SnowFlake;
import com.kugou.fanxing.coupon.thrift.CouponService;
import com.kugou.fanxing.coupon.thrift.OperResult;
import com.kugou.fanxing.coupon.thrift.ReturnResult;
import com.kugou.fanxing.coupon.thrift.UnFreezeVO;
import com.kugou.fanxing.coupon.thrift.read.CouponReadService;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeCouponDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeCouponUnfreezeDao;
import com.kugou.fanxing.recharge.model.bo.CouponBO;
import com.kugou.fanxing.recharge.model.bo.CouponInfoBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.vo.RechargeCouponInfoVO;
import com.kugou.fanxing.recharge.service.common.ConsumeRpcService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.mfx.activity.infiltrate.thrift.service.CouponListService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class RechargeCouponServiceUnitTest {

    @InjectMocks
    private RechargeCouponService rechargeCouponService;
    @Mock
    private Env env;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    private ApolloConfigService apolloConfigService;
    @Mock
    private CouponService.Iface couponService;
    @Mock
    private CouponReadService.Iface couponReadService;
    @Mock
    private CouponListService.Iface couponListService;
    @Mock
    private RechargeCouponUnfreezeDao rechargeCouponUnfreezeDao;
    @Mock
    private RechargeAcrossDao rechargeAcrossDao;
    @Mock
    private RechargeCouponDao rechargeCouponDao;
    @Mock
    private ConsumeRpcService consumeRpcService;

    @Before
    public void before() {
        when(apolloConfigService.getCouponFailureResultCodeList()).thenReturn(Lists.newArrayList(4, 16));
    }
    
    @Test
    public void isValidCoupon() {
        CouponBO couponBO = new CouponBO();
        couponBO.setCoupon(BigDecimal.ZERO);
        couponBO.setCouponOrderId(1L);
        couponBO.setCouponId(2L);
        Assert.assertFalse(this.rechargeCouponService.isValidCoupon(couponBO));
        couponBO.setCoupon(BigDecimal.TEN);
        Assert.assertTrue(this.rechargeCouponService.isValidCoupon(couponBO));
    }

    @Test
    public void isCouponLimit() {
        CouponInfoBO couponInfoBO = new CouponInfoBO();
        couponInfoBO.setValue(BigDecimal.TEN);
        Assert.assertTrue(rechargeCouponService.isCouponLimit(couponInfoBO, BigDecimal.ONE));
        couponInfoBO.setUpperLimit(BigDecimal.valueOf(100));
        couponInfoBO.setLowerLimit(BigDecimal.valueOf(5));
        Assert.assertTrue(rechargeCouponService.isCouponLimit(couponInfoBO, BigDecimal.valueOf(5)));
        Assert.assertFalse(rechargeCouponService.isCouponLimit(couponInfoBO, BigDecimal.valueOf(99)));
        Assert.assertTrue(rechargeCouponService.isCouponLimit(couponInfoBO, BigDecimal.valueOf(100)));
    }

    @Test
    public void parseCouponImgPath() {
        String couponImgPath = this.rechargeCouponService.parseCouponImgPath("");
        Assert.assertTrue(StringUtils.isBlank(couponImgPath));
        couponImgPath = this.rechargeCouponService.parseCouponImgPath("http://test/aaa/bbb/cc.jpg");
        Assert.assertEquals("/aaa/bbb/cc.jpg", couponImgPath);
    }

    @Test
    public void extractValidCoupon() {
        RechargeAcrossPO order = new RechargeAcrossPO();
        order.setCouponOrderId(1458542750512689978L);
        order.setCouponId(544556346805063678L);
        order.setCoupon(new BigDecimal("0.01"));
        Optional<CouponBO> optionalCouponBO = this.rechargeCouponService.extractValidCoupon(order);
        Assert.assertTrue(optionalCouponBO.isPresent());
        CouponBO couponBO = optionalCouponBO.orElseThrow(ContextedRuntimeException::new);
        Assert.assertEquals(1458542750512689978L, couponBO.getCouponOrderId());
        Assert.assertEquals(544556346805063678L, couponBO.getCouponId());
        Assert.assertEquals(new BigDecimal("0.01"), couponBO.getCoupon());
    }

    @Test
    public void consumeCoupon() throws TException {
        long kugouId = 517910333L;
        long couponOrderId = 1458542750512689978L;
        long globalId = new SnowFlake(0, 0).nextId();
        int addTime = DateHelper.getCurrentSeconds();
        when(env.isProd()).thenReturn(false);
        ReturnResult returnResult = new ReturnResult();
        returnResult.setCode(0);
        // 充值代金券使用成功
        returnResult.setData(new OperResult().setResult(0));
        when(couponService.confirm(any(UnFreezeVO.class))).thenReturn(returnResult);
        int result = this.rechargeCouponService.consumeCoupon(kugouId, couponOrderId, globalId, addTime);
        Assert.assertEquals(1, result);
        // 充值代金券使用失败
        returnResult.setData(new OperResult().setResult(16));
        when(couponService.confirm(any(UnFreezeVO.class))).thenReturn(returnResult);
        result = this.rechargeCouponService.consumeCoupon(kugouId, couponOrderId, globalId, addTime);
        Assert.assertEquals(2, result);
        // 充值代金券已被解冻
        when(couponService.confirm(any(UnFreezeVO.class))).thenThrow(new TException());
        result = this.rechargeCouponService.consumeCoupon(kugouId, couponOrderId, globalId, addTime);
        Assert.assertEquals(0, result);
    }

    @Test
    public void getCouponListResult() {
        long kugouId = 517910333L;
        when(apolloConfigService.isCouponSwitchOpen()).thenReturn(false);
        JsonResult<List<RechargeCouponInfoVO>> jsonResult = this.rechargeCouponService.getCouponListResult(kugouId);
        Assert.assertEquals(SysResultCode.E_10000024.getCode(), jsonResult.getCode());
    }
}
