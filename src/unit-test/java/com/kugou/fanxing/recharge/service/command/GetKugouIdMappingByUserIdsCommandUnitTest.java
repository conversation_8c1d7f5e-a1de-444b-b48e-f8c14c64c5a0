package com.kugou.fanxing.recharge.service.command;

import com.google.common.collect.Maps;
import com.kugou.fanxing.userbaseinfo.service.UserModuleV2BizService;
import com.kugou.fanxing.userbaseinfo.vo.ResIDMapMsg;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class GetKugouIdMappingByUserIdsCommandUnitTest {

    @Mock
    private UserModuleV2BizService.Iface userModuleV2BizService;

    @Test
    public void getKugouIdMappingByUserIds() throws TException {
        Map<Long, Long> mapping = Maps.newHashMap();
        mapping.put(1290249156L, 1290249156L);
        when(userModuleV2BizService.getKugouIdMappingByUserIds(anyList())).thenReturn(new ResIDMapMsg().setRet(0).setData(mapping));
        GetKugouIdMappingByUserIdsCommand command = new GetKugouIdMappingByUserIdsCommand(userModuleV2BizService, Lists.newArrayList(1290249156L));
        Map<Long, Long> idMapping = command.execute();
        Assert.assertEquals(1, idMapping.size());
        Assert.assertEquals(1290249156L, (long)idMapping.get(1290249156L));
        when(userModuleV2BizService.getKugouIdMappingByUserIds(anyList())).thenReturn(null);
        command = new GetKugouIdMappingByUserIdsCommand(userModuleV2BizService, Lists.newArrayList(1290249156L));
        Assert.assertEquals(0, command.execute().size());
    }

    @Test
    public void getKugouIdMappingByUserIdsException() throws TException {
        when(userModuleV2BizService.getKugouIdMappingByUserIds(anyList())).thenThrow(new TException("test"));
        GetKugouIdMappingByUserIdsCommand command = new GetKugouIdMappingByUserIdsCommand(userModuleV2BizService, Lists.newArrayList(1290249156L));
        Map<Long, Long> idMapping = command.execute();
        Assert.assertEquals(0, idMapping.size());
    }
}
