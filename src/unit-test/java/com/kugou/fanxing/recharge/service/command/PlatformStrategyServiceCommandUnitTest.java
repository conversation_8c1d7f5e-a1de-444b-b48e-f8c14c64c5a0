package com.kugou.fanxing.recharge.service.command;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.PlatformStrategyService;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.Result;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.StrategyResult;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.StrategyVO;
import joptsimple.internal.Strings;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class PlatformStrategyServiceCommandUnitTest {

    @Mock
    private PlatformStrategyService.Iface platformStrategyService;

    private StrategyVO strategyVO = new StrategyVO();

    @Before
    public void before() {
        strategyVO = new StrategyVO()
                .setAppid("0")
                .setBiz("BigRecharge")
                .setDeviceId("")
                .setEndtype(Strings.EMPTY)
                .setTs(System.currentTimeMillis())
                .setData(JSON.toJSONString(Maps.newHashMap()))
                .setKugouId(1290249156L)
                .setIp("127.0.0.1")
                .setSid("0");
    }

    @Test
    public void conclude() throws TException {
        StrategyResult strategyResult = new StrategyResult();
        strategyResult.setRecode(0);
        strategyResult.setData(new Result().setCode(1).setBizRecode("1"));
        when(platformStrategyService.conclude(any(StrategyVO.class))).thenReturn(strategyResult);
        PlatformStrategyServiceCommand command = new PlatformStrategyServiceCommand(platformStrategyService, strategyVO);
        Assert.assertEquals(0, command.execute().getRecode());
    }

    @Test
    public void concludeException() throws TException {
        when(platformStrategyService.conclude(any(StrategyVO.class))).thenThrow(new ContextedRuntimeException());
        PlatformStrategyServiceCommand command = new PlatformStrategyServiceCommand(platformStrategyService, strategyVO);
        Assert.assertEquals(0, command.execute().getRecode());
    }
}
