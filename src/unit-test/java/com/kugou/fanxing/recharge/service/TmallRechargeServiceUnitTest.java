package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.model.bo.ServerOptionBO;
import com.kugou.fanxing.recharge.model.dto.BaseGametopDTO;
import com.kugou.fanxing.recharge.model.dto.GametopVerifyDTO;
import com.kugou.fanxing.recharge.model.request.QueryTmallChargeReq;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.thrift.plat.user.UserPlatBizService;
import com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse;
import com.taobao.api.internal.spi.CheckResult;
import com.taobao.api.internal.spi.SpiUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class TmallRechargeServiceUnitTest {

    private static final String FANXING_CARD_ID_PREFIX = "comFanxingPrice";
    private static final String KUGOU_CARD_ID_PREFIX = "comKugouPrice";
    private static final String FANXING_CARD_ID = FANXING_CARD_ID_PREFIX + "XXX";
    private static final String KUGOU_CARD_ID = KUGOU_CARD_ID_PREFIX + "XXX";

    @Mock
    private HttpServletRequest request;
    @Mock
    private ApolloConfigService apolloConfigService;
    @Mock
    private UserFacadeService userFacadeService;
    @Mock
    private UserPlatBizService.Iface userPlatBizService;
    @Mock
    private RemoteStrategyService remoteStrategyService;
    @InjectMocks
    private TmallRechargeService tmallRechargeService;

    @Test
    public void checkTmallCanRechargeGetKugouIdByUserIdException() throws JSONException {
        QueryTmallChargeReq param = buildQueryTmallChargeReq(FANXING_CARD_ID);
        when(apolloConfigService.getTmallFanxingAccountRulePattern()).thenReturn(FANXING_CARD_ID_PREFIX);
        when(userFacadeService.getKugouIdByUserId(anyLong())).thenReturn(Optional.empty());
        JsonResult<Map<String, Object>> jsonResult = tmallRechargeService.checkTmallCanRecharge(param);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertEquals(0, jsonResult.getCode());
        int permitRecharge = new JSONObject(jsonResult.getData()).getInt("permitRecharge");
        Assert.assertEquals(0, permitRecharge);
    }

    @Test
    public void checkTmallCanRechargeGetUserIdByKugouIdException() throws JSONException {
        QueryTmallChargeReq param = buildQueryTmallChargeReq(KUGOU_CARD_ID);
        when(apolloConfigService.getTmallFanxingAccountRulePattern()).thenReturn(FANXING_CARD_ID_PREFIX);
        when(userFacadeService.getKugouIdByUserId(anyLong())).thenReturn(Optional.of(1290249156L));
        when(apolloConfigService.getTmallKugouAccountRulePattern()).thenReturn(KUGOU_CARD_ID_PREFIX);
        when(userFacadeService.getUserIdByKugouId(anyLong())).thenReturn(Optional.empty());
        JsonResult<Map<String, Object>> jsonResult = tmallRechargeService.checkTmallCanRecharge(param);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertEquals(0, jsonResult.getCode());
        int permitRecharge = new JSONObject(jsonResult.getData()).getInt("permitRecharge");
        Assert.assertEquals(0, permitRecharge);
    }

    @Test
    public void checkTmallCanRechargeKugouAccountConfigException() throws JSONException {
        QueryTmallChargeReq param = buildQueryTmallChargeReq(KUGOU_CARD_ID);
        when(userFacadeService.getUserIdByKugouId(anyLong())).thenReturn(Optional.of(1L));
        when(userFacadeService.getKugouIdByUserId(anyLong())).thenReturn(Optional.of(1290249156L));
        when(apolloConfigService.getTmallFanxingAccountRulePattern()).thenReturn("test");
        when(apolloConfigService.getTmallKugouAccountRulePattern()).thenReturn("test");
        JsonResult<Map<String, Object>> jsonResult = tmallRechargeService.checkTmallCanRecharge(param);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertEquals(0, jsonResult.getCode());
        int permitRecharge = new JSONObject(jsonResult.getData()).getInt("permitRecharge");
        Assert.assertEquals(0, permitRecharge);
    }

    @Test
    public void checkTmallCanRechargeRobot() throws TException, JSONException {
        QueryTmallChargeReq param = buildQueryTmallChargeReq(FANXING_CARD_ID);
        when(userFacadeService.getKugouIdByUserId(anyLong())).thenReturn(Optional.of(510022001L));
        when(userFacadeService.getUserIdByKugouId(anyLong())).thenReturn(Optional.of(1L));
        when(apolloConfigService.getTmallFanxingAccountRulePattern()).thenReturn(FANXING_CARD_ID_PREFIX);
        when(apolloConfigService.getTmallKugouAccountRulePattern()).thenReturn(KUGOU_CARD_ID_PREFIX);
        JsonResult<Map<String, Object>> jsonResult = tmallRechargeService.checkTmallCanRecharge(param);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertEquals(0, jsonResult.getCode());
        int permitRecharge = new JSONObject(jsonResult.getData()).getInt("permitRecharge");
        Assert.assertEquals(0, permitRecharge);
    }

    @Test
    public void checkTmallCanRechargeAccountIsCancel() throws TException, JSONException {
        QueryTmallChargeReq param = buildQueryTmallChargeReq(FANXING_CARD_ID);
        when(userFacadeService.getKugouIdByUserId(anyLong())).thenReturn(Optional.of(83339607L));
        when(userFacadeService.getUserIdByKugouId(anyLong())).thenReturn(Optional.of(1L));
        when(apolloConfigService.getTmallFanxingAccountRulePattern()).thenReturn(FANXING_CARD_ID_PREFIX);
        when(apolloConfigService.getTmallKugouAccountRulePattern()).thenReturn(KUGOU_CARD_ID_PREFIX);
        when(userPlatBizService.getUserCancelStatus(anyLong())).thenReturn(buildCancelStatusResp(false, 0));
        JsonResult<Map<String, Object>> jsonResult = tmallRechargeService.checkTmallCanRecharge(param);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertEquals(0, jsonResult.getCode());
        int permitRecharge = new JSONObject(jsonResult.getData()).getInt("permitRecharge");
        Assert.assertEquals(0, permitRecharge);
    }


    @Test
    public void checkTmallCanRechargeAccountIsCancelError() throws TException, JSONException {
        QueryTmallChargeReq param = buildQueryTmallChargeReq(FANXING_CARD_ID);
        when(userFacadeService.getKugouIdByUserId(anyLong(), anyBoolean())).thenReturn(Optional.of(83339607L));
        when(userFacadeService.getUserIdByKugouId(anyLong(), anyBoolean())).thenReturn(Optional.of(1L));
        when(apolloConfigService.getTmallFanxingAccountRulePattern()).thenReturn(FANXING_CARD_ID_PREFIX);
        when(apolloConfigService.getTmallKugouAccountRulePattern()).thenReturn(KUGOU_CARD_ID_PREFIX);
        when(userPlatBizService.getUserCancelStatus(anyLong())).thenReturn(buildCancelStatusResp(false, 2002008));
        JsonResult<Map<String, Object>> jsonResult = tmallRechargeService.checkTmallCanRecharge(param);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertEquals(0, jsonResult.getCode());
        int permitRecharge = new JSONObject(jsonResult.getData()).getInt("permitRecharge");
        Assert.assertEquals(1, permitRecharge);
    }

    @Test
    public void checkTmallCanRechargeAccountIsCancelException() throws TException, JSONException {
        QueryTmallChargeReq param = buildQueryTmallChargeReq(FANXING_CARD_ID);
        when(userFacadeService.getKugouIdByUserId(anyLong(), anyBoolean())).thenReturn(Optional.of(83339607L));
        when(userFacadeService.getUserIdByKugouId(anyLong(), anyBoolean())).thenReturn(Optional.of(1L));
        when(apolloConfigService.getTmallFanxingAccountRulePattern()).thenReturn(FANXING_CARD_ID_PREFIX);
        when(apolloConfigService.getTmallKugouAccountRulePattern()).thenReturn(KUGOU_CARD_ID_PREFIX);
        when(userPlatBizService.getUserCancelStatus(anyLong())).thenThrow(new ContextedRuntimeException("test"));
        JsonResult<Map<String, Object>> jsonResult = tmallRechargeService.checkTmallCanRecharge(param);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertEquals(0, jsonResult.getCode());
        int permitRecharge = new JSONObject(jsonResult.getData()).getInt("permitRecharge");
        Assert.assertEquals(1, permitRecharge);
    }

    @Test
    public void checkTmallCanRechargeClose() throws JSONException {
        QueryTmallChargeReq param = buildQueryTmallChargeReq(KUGOU_CARD_ID);
        when(userFacadeService.getKugouIdByUserId(anyLong())).thenReturn(Optional.of(1290249156L));
        when(userFacadeService.getUserIdByKugouId(anyLong())).thenReturn(Optional.of(1L));
        when(apolloConfigService.getServerOptionByKey(anyString())).thenReturn(Optional.of(buildServerOptionBOClose()));
        when(apolloConfigService.getTmallFanxingAccountRulePattern()).thenReturn(FANXING_CARD_ID_PREFIX);
        when(apolloConfigService.getTmallKugouAccountRulePattern()).thenReturn(KUGOU_CARD_ID_PREFIX);
        JsonResult<Map<String, Object>> jsonResult = tmallRechargeService.checkTmallCanRecharge(param);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertEquals(0, jsonResult.getCode());
        int permitRecharge = new JSONObject(jsonResult.getData()).getInt("permitRecharge");
        Assert.assertEquals(0, permitRecharge);
    }

    @Test
    public void checkTmallCanRechargeConfigException() throws JSONException {
        QueryTmallChargeReq param = buildQueryTmallChargeReq(KUGOU_CARD_ID);
        when(userFacadeService.getKugouIdByUserId(anyLong(), anyBoolean())).thenReturn(Optional.of(1290249156L));
        when(userFacadeService.getUserIdByKugouId(anyLong(), anyBoolean())).thenReturn(Optional.of(1L));
        when(apolloConfigService.getServerOptionByKey(anyString())).thenThrow(new ContextedRuntimeException("test"));
        when(apolloConfigService.getTmallFanxingAccountRulePattern()).thenReturn(FANXING_CARD_ID_PREFIX);
        when(apolloConfigService.getTmallKugouAccountRulePattern()).thenReturn(KUGOU_CARD_ID_PREFIX);
        JsonResult<Map<String, Object>> jsonResult = tmallRechargeService.checkTmallCanRecharge(param);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertEquals(1, jsonResult.getCode());
        int permitRecharge = new JSONObject(jsonResult.getData()).getInt("permitRecharge");
        Assert.assertEquals(0, permitRecharge);
    }

    @Test
    public void checkTmallCanRechargeStrategyVerifyBlock() throws Exception {
        QueryTmallChargeReq param = buildQueryTmallChargeReq(FANXING_CARD_ID);
        when(userFacadeService.getKugouIdByUserId(anyLong(), anyBoolean())).thenReturn(Optional.of(83339607L));
        when(userFacadeService.getUserIdByKugouId(anyLong(), anyBoolean())).thenReturn(Optional.of(1L));
        when(apolloConfigService.getTmallFanxingAccountRulePattern()).thenReturn(FANXING_CARD_ID_PREFIX);
        when(apolloConfigService.getTmallKugouAccountRulePattern()).thenReturn(KUGOU_CARD_ID_PREFIX);
        when(userPlatBizService.getUserCancelStatus(anyLong())).thenReturn(buildCancelStatusResp(false, 2002008));
        when(remoteStrategyService.strategyVerifyForTmall(anyLong(), anyString())).thenReturn(true);
        JsonResult<Map<String, Object>> jsonResult = tmallRechargeService.checkTmallCanRecharge(param);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertEquals(0, jsonResult.getCode());
        int permitRecharge = new JSONObject(jsonResult.getData()).getInt("permitRecharge");
        Assert.assertEquals(0, permitRecharge);
    }

    @Test
    public void checkTmallCanRecharge() throws JSONException {
        QueryTmallChargeReq param = buildQueryTmallChargeReq(KUGOU_CARD_ID);
        when(userFacadeService.getKugouIdByUserId(anyLong(), anyBoolean())).thenReturn(Optional.of(1290249156L));
        when(userFacadeService.getUserIdByKugouId(anyLong(), anyBoolean())).thenReturn(Optional.of(1L));
        when(apolloConfigService.getServerOptionByKey(anyString())).thenReturn(Optional.of(buildServerOptionBO()));
        when(apolloConfigService.getTmallFanxingAccountRulePattern()).thenReturn(FANXING_CARD_ID_PREFIX);
        when(apolloConfigService.getTmallKugouAccountRulePattern()).thenReturn(KUGOU_CARD_ID_PREFIX);
        when(remoteStrategyService.strategyVerifyForTmall(anyLong(), anyString())).thenReturn(false);
        JsonResult<Map<String, Object>> jsonResult = tmallRechargeService.checkTmallCanRecharge(param);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertEquals(0, jsonResult.getCode());
        int permitRecharge = new JSONObject(jsonResult.getData()).getInt("permitRecharge");
        Assert.assertEquals(1, permitRecharge);
    }

    @Test
    public void checkTmallCanRechargeSupportKugouBrandSwitchIsCloseException() throws JSONException {
        QueryTmallChargeReq param = buildQueryTmallChargeReq(FANXING_CARD_ID);
        when(apolloConfigService.getTmallFanxingAccountRulePattern()).thenReturn(FANXING_CARD_ID_PREFIX);
        when(userFacadeService.getKugouIdByUserId(anyLong(), anyBoolean())).thenReturn(Optional.empty());
        JsonResult<Map<String, Object>> jsonResult = tmallRechargeService.checkTmallCanRecharge(param);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertEquals(0, jsonResult.getCode());
        int permitRecharge = new JSONObject(jsonResult.getData()).getInt("permitRecharge");
        Assert.assertEquals(0, permitRecharge);
    }


    private ResUserCancelStatusResponse buildCancelStatusResp(boolean data, int code){
        ResUserCancelStatusResponse response = new ResUserCancelStatusResponse();
        response.setData(data);
        response.setCode(code);
        return response;
    }
    private ServerOptionBO buildServerOptionBO() {
        return new ServerOptionBO().setServerKey("rechargeTmall").setServerValue(1).setServerDesc("天猫商城充值");
    }

    private ServerOptionBO buildServerOptionBOClose() {
        return new ServerOptionBO().setServerKey("rechargeTmall").setServerValue(0).setServerDesc("天猫商城充值");
    }

    private QueryTmallChargeReq buildQueryTmallChargeReq(String cardId) {
        return this.buildQueryTmallChargeReq("********", cardId);
    }

    private QueryTmallChargeReq buildQueryTmallChargeReq(String customer, String cardId) {
        QueryTmallChargeReq param = new QueryTmallChargeReq();
        param.setCustomer(customer);
        param.setCardId(cardId);
        return param;
    }

    @Test
    public void queryRechargeAccountNicknameAbNormal() {
        CheckResult checkResult = new CheckResult();
        checkResult.setSuccess(false);
        checkResult.setRequestBody("");
        try (MockedStatic<SpiUtils> mockedStatic = Mockito.mockStatic(SpiUtils.class)) {
            mockedStatic.when(() -> SpiUtils.checkSign(any(HttpServletRequest.class), anyString())).thenReturn(checkResult);
            when(request.toString()).thenReturn("test request");
            BaseGametopDTO baseGametopDTO = tmallRechargeService.queryRechargeAccountNickname(request);
            Assert.assertNotNull(baseGametopDTO);
            Assert.assertEquals("-1", baseGametopDTO.getResultCode());
            Assert.assertEquals("", baseGametopDTO.getResultMsg());
            Assert.assertEquals("sign-check-failure", baseGametopDTO.getSubCode());
            Assert.assertEquals("Illegal request", baseGametopDTO.getSubMessage());
        }
    }

    @Test
    public void queryRechargeAccountNicknameNormalKugouBrand() throws IOException {
        CheckResult checkResult = new CheckResult();
        checkResult.setSuccess(true);
        checkResult.setRequestBody("{\n" +
                "    \"ext\": \"json字符串格式\",\n" +
                "    \"orderNo\": \"1000001\",\n" +
                "    \"brandId\": \"100001\",\n" +
                "    \"coopId\": \"**********\",\n" +
                "    \"accounts\": \"1001,1002\",\n" +
                "    \"version\": \"1.0.0\",\n" +
                "    \"tags\": \"YKXK,IQYXK\"\n" +
                "}");
        try (MockedStatic<SpiUtils> mockedStatic = Mockito.mockStatic(SpiUtils.class)) {
            mockedStatic.when(() -> SpiUtils.checkSign(any(HttpServletRequest.class), anyString())).thenReturn(checkResult);
            when(request.toString()).thenReturn("test request");
            // 检查是否为酷狗品牌
            when(apolloConfigService.getTmallKugouBrandIds()).thenReturn(Lists.newArrayList("100001"));
            when(apolloConfigService.getTmallFanxingBrandIds()).thenReturn(Lists.newArrayList());
            BaseGametopDTO baseGametopDTO = tmallRechargeService.queryRechargeAccountNickname(request);
            Assert.assertNotNull(baseGametopDTO);
            Assert.assertEquals("00", baseGametopDTO.getResultCode());
            Assert.assertTrue(baseGametopDTO instanceof GametopVerifyDTO);
            GametopVerifyDTO gametopVerifyDTO = ((GametopVerifyDTO) baseGametopDTO);
            Assert.assertEquals("1000001", gametopVerifyDTO.getOrderNo());
            Assert.assertEquals(2, gametopVerifyDTO.getAccountIinfo().size());
            gametopVerifyDTO.getAccountIinfo().forEach(accountIinfoDTO -> {
                Assert.assertNotNull(accountIinfoDTO.getAccount());
                Assert.assertNotNull(accountIinfoDTO.getNick());
            });
        }
    }

    @Test
    public void queryRechargeAccountNicknameNormalFanxingBrand() throws IOException {
        CheckResult checkResult = new CheckResult();
        checkResult.setSuccess(true);
        checkResult.setRequestBody("{\n" +
                "    \"ext\": \"json字符串格式\",\n" +
                "    \"orderNo\": \"1000001\",\n" +
                "    \"brandId\": \"100001\",\n" +
                "    \"coopId\": \"**********\",\n" +
                "    \"accounts\": \"1001,1002\",\n" +
                "    \"version\": \"1.0.0\",\n" +
                "    \"tags\": \"YKXK,IQYXK\"\n" +
                "}");
        try (MockedStatic<SpiUtils> mockedStatic = Mockito.mockStatic(SpiUtils.class)) {
            mockedStatic.when(() -> SpiUtils.checkSign(any(HttpServletRequest.class), anyString())).thenReturn(checkResult);
            when(request.toString()).thenReturn("test request");
            // 检查是否为酷狗品牌
            when(apolloConfigService.getTmallKugouBrandIds()).thenReturn(Lists.newArrayList());
            when(apolloConfigService.getTmallFanxingBrandIds()).thenReturn(Lists.newArrayList("100001"));
            BaseGametopDTO baseGametopDTO = tmallRechargeService.queryRechargeAccountNickname(request);
            Assert.assertNotNull(baseGametopDTO);
            Assert.assertEquals("00", baseGametopDTO.getResultCode());
            Assert.assertTrue(baseGametopDTO instanceof GametopVerifyDTO);
            GametopVerifyDTO gametopVerifyDTO = ((GametopVerifyDTO) baseGametopDTO);
            Assert.assertEquals("1000001", gametopVerifyDTO.getOrderNo());
            Assert.assertEquals(2, gametopVerifyDTO.getAccountIinfo().size());
            gametopVerifyDTO.getAccountIinfo().forEach(accountIinfoDTO -> {
                Assert.assertNotNull(accountIinfoDTO.getAccount());
                Assert.assertNotNull(accountIinfoDTO.getNick());
            });
        }
    }

    @Test
    public void queryRechargeAccountNicknameNormalNoBrand() throws IOException {
        CheckResult checkResult = new CheckResult();
        checkResult.setSuccess(true);
        checkResult.setRequestBody("{\n" +
                "    \"ext\": \"json字符串格式\",\n" +
                "    \"orderNo\": \"1000001\",\n" +
                "    \"brandId\": \"100001\",\n" +
                "    \"coopId\": \"**********\",\n" +
                "    \"accounts\": \"1001,1002\",\n" +
                "    \"version\": \"1.0.0\",\n" +
                "    \"tags\": \"YKXK,IQYXK\"\n" +
                "}");
        try (MockedStatic<SpiUtils> mockedStatic = Mockito.mockStatic(SpiUtils.class)) {
            mockedStatic.when(() -> SpiUtils.checkSign(any(HttpServletRequest.class), anyString())).thenReturn(checkResult);
            when(request.toString()).thenReturn("test request");
            // 检查是否为酷狗品牌
            when(apolloConfigService.getTmallKugouBrandIds()).thenReturn(Lists.newArrayList());
            when(apolloConfigService.getTmallFanxingBrandIds()).thenReturn(Lists.newArrayList());
            BaseGametopDTO baseGametopDTO = tmallRechargeService.queryRechargeAccountNickname(request);
            Assert.assertNotNull(baseGametopDTO);
            Assert.assertEquals("-1", baseGametopDTO.getResultCode());
            Assert.assertEquals("", baseGametopDTO.getResultMsg());
            Assert.assertEquals("sign-check-failure", baseGametopDTO.getSubCode());
            Assert.assertEquals("Illegal request", baseGametopDTO.getSubMessage());
        }
    }

    @Test
    public void queryRechargeAccountNicknameException() throws IOException {
        CheckResult checkResult = new CheckResult();
        checkResult.setSuccess(true);
        checkResult.setRequestBody("");
        try (MockedStatic<SpiUtils> mockedStatic = Mockito.mockStatic(SpiUtils.class)) {
            mockedStatic.when(() -> SpiUtils.checkSign(any(HttpServletRequest.class), anyString())).thenReturn(checkResult);
            when(request.toString()).thenReturn("test request");
            // 检查是否为酷狗品牌
            when(apolloConfigService.getTmallKugouBrandIds()).thenReturn(Lists.newArrayList());
            when(apolloConfigService.getTmallFanxingBrandIds()).thenReturn(Lists.newArrayList());
            BaseGametopDTO baseGametopDTO = tmallRechargeService.queryRechargeAccountNickname(request);
            Assert.assertNotNull(baseGametopDTO);
            Assert.assertEquals("-1", baseGametopDTO.getResultCode());
            Assert.assertEquals("", baseGametopDTO.getResultMsg());
            Assert.assertEquals("sign-check-failure", baseGametopDTO.getSubCode());
            Assert.assertEquals("Illegal request", baseGametopDTO.getSubMessage());
        }
    }

    @Test
    public void parseKugouIdByBrandFanxing() {
        String extend = "{\"desc\":\"\\\\u5929\\\\u732b\\\\u5145\\\\u503c\",\"cardId\":\"comFanxingPrice30\"}";
        when(apolloConfigService.getTmallFanxingAccountRulePattern()).thenReturn(FANXING_CARD_ID_PREFIX);
        when(apolloConfigService.getTmallKugouAccountRulePattern()).thenReturn(KUGOU_CARD_ID_PREFIX);
        when(this.userFacadeService.getKugouIdByUserId(anyLong())).thenReturn(Optional.of(1L));
        when(this.userFacadeService.getUserIdByKugouId(anyLong())).thenReturn(Optional.of(2L));
        Optional<Long> optionalKugouId = this.tmallRechargeService.parseKugouIdByBrand(1290249156L, extend);
        long actual = optionalKugouId.get();
        Assert.assertEquals(1L, actual);
    }

    @Test
    public void parseKugouIdByBrandKugou() {
        String extend = "{\"desc\":\"\\\\u5929\\\\u732b\\\\u5145\\\\u503c\",\"cardId\":\"comKugouPrice30\"}";
        when(apolloConfigService.getTmallFanxingAccountRulePattern()).thenReturn(FANXING_CARD_ID_PREFIX);
        when(apolloConfigService.getTmallKugouAccountRulePattern()).thenReturn(KUGOU_CARD_ID_PREFIX);
        when(this.userFacadeService.getKugouIdByUserId(anyLong())).thenReturn(Optional.of(1L));
        when(this.userFacadeService.getUserIdByKugouId(anyLong())).thenReturn(Optional.of(2L));
        Optional<Long> optionalKugouId = this.tmallRechargeService.parseKugouIdByBrand(1290249156L, extend);
        long actual = optionalKugouId.get();
        Assert.assertEquals(1290249156L, actual);
    }

    @Test
    public void parseKugouIdByBrandIllegal() {
        String extend = "{\"desc\":\"\\\\u5929\\\\u732b\\\\u5145\\\\u503c\",\"cardId\":\"Price30\"}";
        when(apolloConfigService.getTmallFanxingAccountRulePattern()).thenReturn(FANXING_CARD_ID_PREFIX);
        when(apolloConfigService.getTmallKugouAccountRulePattern()).thenReturn(KUGOU_CARD_ID_PREFIX);
        when(this.userFacadeService.getKugouIdByUserId(anyLong())).thenReturn(Optional.of(1L));
        when(this.userFacadeService.getUserIdByKugouId(anyLong())).thenReturn(Optional.of(2L));
        Optional<Long> optionalKugouId = this.tmallRechargeService.parseKugouIdByBrand(1290249156L, extend);
        Assert.assertFalse(optionalKugouId.isPresent());
    }

    @Test
    public void parseKugouIdByBrandException() {
        String extend = "{\"desc\":\"\\\\u5929\\\\u732b\\\\u5145\\\\u503c\"}";
        when(apolloConfigService.getTmallFanxingAccountRulePattern()).thenReturn(FANXING_CARD_ID_PREFIX);
        when(apolloConfigService.getTmallKugouAccountRulePattern()).thenReturn(KUGOU_CARD_ID_PREFIX);
        when(this.userFacadeService.getKugouIdByUserId(anyLong())).thenReturn(Optional.of(1L));
        when(this.userFacadeService.getUserIdByKugouId(anyLong())).thenReturn(Optional.of(2L));
        Optional<Long> optionalKugouId = this.tmallRechargeService.parseKugouIdByBrand(1290249156L, extend);
        Assert.assertFalse(optionalKugouId.isPresent());
    }


    @Test
    public void checkTmallCanRechargeKw() throws JSONException {
        QueryTmallChargeReq param = buildQueryTmallChargeReq("1", "");
        when(userFacadeService.getKugouIdByKuwoId(anyLong())).thenReturn(Optional.of(1290249156L));
        when(apolloConfigService.getServerOptionByKey(anyString())).thenReturn(Optional.of(buildServerOptionBO()));
        when(apolloConfigService.getTmallFanxingAccountRulePattern()).thenReturn(FANXING_CARD_ID_PREFIX);
        when(apolloConfigService.getTmallKugouAccountRulePattern()).thenReturn(KUGOU_CARD_ID_PREFIX);
        when(remoteStrategyService.strategyVerifyForTmall(anyLong(), anyString())).thenReturn(false);
        JsonResult<Map<String, Object>> jsonResult = tmallRechargeService.checkTmallCanRechargeKw(param);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertEquals(0, jsonResult.getCode());
        int permitRecharge = new JSONObject(jsonResult.getData()).getInt("permitRecharge");
        Assert.assertEquals(1, permitRecharge);
    }
}
