package com.kugou.fanxing.recharge.service.stat;

import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.factory.ConsumeServiceFactory;
import com.kugou.fanxing.recharge.model.vo.UserEntity;
import com.kugou.fanxing.recharge.service.common.CacheService;
import com.kugou.fanxing.recharge.thrift.UserEverRechargeDTO;
import com.kugou.fanxing.thrift.consume.read.service.PlatformConsumeReadService;
import com.kugou.fanxing.thrift.consume.service.ConsumeResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class UserEverRechargeStatServiceTest {

    @InjectMocks
    private UserEverRechargeStatService userEverRechargeStatService;
    @Mock
    private CacheService cacheService;
    @Mock
    private PlatformConsumeReadService.Iface platformConsumeReadService;

    @Mock
    private ConsumeServiceFactory consumeServiceFactory;

    @Before
    public void setUp() {
        when(this.consumeServiceFactory.getConsumeReadService(anyInt())).thenReturn(platformConsumeReadService);
    }


    @Test(expected = AckException.class)
    public void getUserMoneyDataNotExits() throws TException {
        when(platformConsumeReadService.getUserMoney(anyLong(), anyInt())).thenReturn(null);
        userEverRechargeStatService.getUserEntity(1L, 1);
    }

    @Test
    public void getUserMoneyDataInvalid() throws TException {
        when(platformConsumeReadService.getUserMoney(anyLong(), anyInt())).thenReturn(new ConsumeResp().setRet(0).setData("{}"));
        Optional<UserEntity> userYearRechargeStatData = userEverRechargeStatService.getUserEntity(1L, 1);
        Assert.assertTrue(userYearRechargeStatData.isPresent());
        Assert.assertEquals(0, userYearRechargeStatData.get().getCoin(), 0.00);
    }

    @Test
    public void getUserMoney() throws TException {
        when(platformConsumeReadService.getUserMoney(anyLong(), anyInt())).thenReturn(new ConsumeResp().setRet(0).setData("{\"coin\":100}"));
        Optional<UserEntity> userYearRechargeStatData = userEverRechargeStatService.getUserEntity(1L, 1);
        Assert.assertTrue(userYearRechargeStatData.isPresent());
        Assert.assertEquals(100.00, userYearRechargeStatData.get().getCoin(), 0.00);
    }

    @Test(expected = Exception.class)
    public void getUserEverRechargeRedisException() {
        when(cacheService.get(anyString())).thenThrow(new Exception("test"));
        this.userEverRechargeStatService.getUserEverRecharge(1);
    }

    @Test
    public void getUserEverRechargeCache() throws TException {
        when(cacheService.get(anyString())).thenReturn("{\"coins\":200,\"kugouId\":1290249156}");
        when(platformConsumeReadService.getUserMoney(anyLong(), anyInt())).thenReturn(new ConsumeResp().setRet(0).setData("{\"coin\":300,\"czTotal\":300}}"));
        UserEverRechargeDTO userEverRechargeDTO = this.userEverRechargeStatService.getUserEverRecharge(1290249156L);
        Assert.assertEquals(1290249156L, userEverRechargeDTO.getKugouId());
        Assert.assertEquals(200, userEverRechargeDTO.getCoins());
    }

    @Test
    public void getUserEverRechargeDb() throws TException {
        when(cacheService.get(anyString())).thenReturn(null);
        when(platformConsumeReadService.getUserMoney(anyLong(), anyInt())).thenReturn(new ConsumeResp().setRet(0).setData("{\"coin\":300,\"czTotal\":300}"));
        UserEverRechargeDTO userEverRechargeDTO = this.userEverRechargeStatService.getUserEverRecharge(1290249156L);
        Assert.assertEquals(1290249156L, userEverRechargeDTO.getKugouId());
        Assert.assertEquals(300, userEverRechargeDTO.getCoins());
    }

}
