package com.kugou.fanxing.recharge.service.offline;

import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class BigRechargeServiceUnitTest {

    @InjectMocks
    private BigRechargeService bigRechargeService;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    private RechargeAcrossDao rechargeAcrossDao;

    @Test
    public void adjustTradeTime() {
        when(this.orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202106");
        when(this.rechargeAcrossDao.updateRechargeOrderTradeTime(anyString(), anyString(), anyInt())).thenReturn(1);
        Assert.assertEquals(1, this.bigRechargeService.adjustTradeTime("R092021060116265407976687", 1782373800));
        when(this.rechargeAcrossDao.updateRechargeOrderTradeTime(anyString(), anyString(), anyInt())).thenReturn(0);
        Assert.assertEquals(0, this.bigRechargeService.adjustTradeTime("R092021060116265407976687", 1782373740));
    }
}
