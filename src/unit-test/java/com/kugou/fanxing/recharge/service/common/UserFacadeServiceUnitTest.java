package com.kugou.fanxing.recharge.service.common;

import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.util.IpUtils;
import com.kugou.fanxing.thrift.banaccount.dto.Result;
import com.kugou.fanxing.thrift.banaccount.service.BanAccountService;
import com.kugou.fanxing.thrift.idmapping.user.UserIdMappingService;
import com.kugou.fanxing.thrift.plat.user.UserPlatBizService;
import com.kugou.fanxing.thrift.plat.user.vo.ResMsg;
import com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse;
import com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq;
import com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp;
import com.kugou.fanxing.userbaseinfo.service.UserModuleV2BizService;
import com.kugou.fanxing.userbaseinfo.vo.*;
import com.kugou.kw.idservice.api.struct.BatchIdResponse;
import com.kugou.kw.idservice.api.struct.IdInfo;
import com.kugou.kw.idservice.api.struct.IdResponse;
import com.kugou.kw.idservice.api.struct.KuwoIdMappingService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class UserFacadeServiceUnitTest {

    @Mock
    private UserIdMappingService.Iface userIdMappingService;
    @Mock
    private UserModuleV2BizService.Iface userModuleV2BizService;
    @Mock
    private UserPlatBizService.Iface userPlatBizService;
    @Mock
    private BanAccountService.Iface banAccountService;
    @Mock
    private KuwoIdMappingService.Iface kuwoIdMappingService;
    @Mock
    private ApolloConfigService apolloConfigService;
    @InjectMocks
    private UserFacadeService userFacadeService;


    @Test
    public void getNicknameByKugouIds() throws TException {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            UserListResponse userListResponse = new UserListResponse();
            userListResponse.setRet(0);
            userListResponse.setData(Lists.newArrayList(new UserVO().setKugouId(1290249156L).setNickName("Kay")));
            when(userModuleV2BizService.getUserListByKugouIdList(anyList())).thenReturn(userListResponse);
            Map<Long, String> userMap = userFacadeService.getNicknameByKugouIds(Lists.newArrayList(1290249156L));
            Assert.assertNotNull(userMap);
            Assert.assertEquals("Kay", userMap.get(1290249156L));
        }
    }

    @Test
    public void getUserByKugouIds() throws TException {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            UserListResponse userListResponse = new UserListResponse();
            userListResponse.setRet(0);
            userListResponse.setData(Lists.newArrayList(new UserVO().setKugouId(1290249156L).setNickName("Kay")));
            when(userModuleV2BizService.getUserListByKugouIdList(anyList())).thenReturn(userListResponse);
            Map<Long, UserVO> userMap = userFacadeService.getUserByKugouIds(Lists.newArrayList(1290249156L));
            Assert.assertNotNull(userMap);
            Assert.assertEquals("Kay", userMap.get(1290249156L).getNickName());
        }
    }


    @Test
    public void getUserIdByKugouId() throws TException {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            when(userIdMappingService.getUserIdByKugouId(anyLong())).thenReturn(1290249156L);
            Optional<Long> optionalKugouId = userFacadeService.getUserIdByKugouId(1290249156L);
            Assert.assertNotNull(optionalKugouId);
            Assert.assertEquals(1290249156L, optionalKugouId.orElse(0L).longValue());

            reset(userIdMappingService);
            when(userIdMappingService.getUserIdByKugouId(anyLong())).thenReturn(1290249156L);
            optionalKugouId = userFacadeService.getUserIdByKugouId(1290249156L, false);
            Assert.assertNotNull(optionalKugouId);
            Assert.assertEquals(1290249156L, optionalKugouId.orElse(0L).longValue());
        }
    }

    @Test
    public void getUserVOByKugouId() throws TException {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            UserVO userVO = new UserVO().setKugouId(1290249156L);
            UserResponse response = new UserResponse().setRet(0).setData(userVO);
            when(userModuleV2BizService.getUserByKugouId(anyLong())).thenReturn(response);
            Optional<UserVO> optionalUserVO = userFacadeService.getUserVOByKugouId(1290249156L);
            Assert.assertNotNull(optionalUserVO);
            Assert.assertEquals(1290249156L, optionalUserVO.get().getKugouId());

            reset(userModuleV2BizService);
            response = new UserResponse().setRet(0).setData(null);
            when(userModuleV2BizService.getUserByKugouId(anyLong())).thenReturn(response);
            optionalUserVO = userFacadeService.getUserVOByKugouId(1290249156L);
            Assert.assertTrue(!optionalUserVO.isPresent());

            reset(userModuleV2BizService);
            when(userModuleV2BizService.getUserByKugouId(anyLong())).thenThrow(new TException("test"));
            try {
                userFacadeService.getUserVOByKugouId(1290249156L);
            } catch (AckException e) {
                Assert.assertTrue(e.getCode() == SysResultCode.RECHARGE_SYS_ERROR.getCode());
            }
        }
    }

    @Test
    public void getKugouIdByUserId() throws TException {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            when(userIdMappingService.getKugouIdByUserId(anyLong())).thenReturn(1290249156L);
            Optional<Long> optionalKugouId = userFacadeService.getKugouIdByUserId(1290249156L);
            Assert.assertNotNull(optionalKugouId);
            Assert.assertEquals(1290249156L, optionalKugouId.orElse(0L).longValue());

            reset(userIdMappingService);
            when(userIdMappingService.getKugouIdByUserId(anyLong())).thenReturn(1290249156L);
            optionalKugouId = userFacadeService.getKugouIdByUserId(1290249156L, false);
            Assert.assertNotNull(optionalKugouId);
            Assert.assertEquals(1290249156L, optionalKugouId.orElse(0L).longValue());
        }
    }

    @Test
    public void isValidKugouId() throws TException {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            ResMsg resMsg = new ResMsg();
            resMsg.setResponseCode("000");
            resMsg.setData("test");
            when(userPlatBizService.getUserInfoFromKugouV1(anyLong())).thenReturn(resMsg);
            boolean isValid = userFacadeService.isValidKugouId(1290249156L);
            Assert.assertTrue(isValid);
        }
    }

    @Test
    public void isBannedAccount() throws TException {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            when(banAccountService.isBanAccount(anyLong())).thenReturn(new Result().setResult(true));
            Assert.assertTrue(this.userFacadeService.isBannedAccount(1290249156L));
            when(banAccountService.isBanAccount(anyLong())).thenReturn(new Result().setResult(false));
            Assert.assertFalse(this.userFacadeService.isBannedAccount(1290249156L));
            when(banAccountService.isBanAccount(anyLong())).thenThrow(new TException("test"));
            Assert.assertFalse(this.userFacadeService.isBannedAccount(1290249156L));
        }
    }

    @Test
    public void isRevokedAccount() throws TException {
        when(userPlatBizService.getUserCancelStatus(anyLong())).thenReturn(new ResUserCancelStatusResponse().setCode(0).setData(false));
        Assert.assertTrue(this.userFacadeService.isRevokedAccount(1290249156L));
        when(userPlatBizService.getUserCancelStatus(anyLong())).thenReturn(new ResUserCancelStatusResponse().setCode(1).setData(true));
        Assert.assertFalse(this.userFacadeService.isRevokedAccount(1290249156L));
        when(userPlatBizService.getUserCancelStatus(anyLong())).thenThrow(new TException("test"));
        Assert.assertFalse(this.userFacadeService.isRevokedAccount(1290249156L));
    }

    @Test
    public void sendSmsByKugouId() throws TException {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            SendSmsByKugouIdResp response = new SendSmsByKugouIdResp();
            response.setCode(0);
            response.setData(true);
            when(userPlatBizService.sendSmsByKugouId(any(SendSmsByKugouIdReq.class))).thenReturn(response);
            Assert.assertTrue(this.userFacadeService.sendSmsByKugouId(1290249156L, "test"));
        }
    }

    @Test
    public void sendSmsByKugouIds() throws TException {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            List<Long> kugouIds = Lists.newArrayList(1290249156L, 415770726L);
            SendSmsByKugouIdResp response = new SendSmsByKugouIdResp();
            response.setCode(0);
            response.setData(true);
            when(userPlatBizService.sendSmsByKugouId(any(SendSmsByKugouIdReq.class))).thenReturn(response);
            this.userFacadeService.sendSmsByKugouIds(kugouIds, "test");
            verify(userPlatBizService, atLeast(1)).sendSmsByKugouId(any(SendSmsByKugouIdReq.class));
        }
    }

    /**
     * curl http://jxservice.kgidc.cn/gwphp/account/jx/backend/register?kuwoId=*********
     * {"code":0,"msg":"success","data":{"kugouId":**********,"kuwoId":*********,"userId":**********}}
     */
    @Test
    @SneakyThrows
    public void getKugouIdsByKuwoIds() {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            when(apolloConfigService.isKuwoEnv()).thenReturn(true);
            IdInfo idInfo = new IdInfo();
            idInfo.setKugouId(**********L);
            idInfo.setKuwoId(*********L);
            List<IdInfo> idInfoList = Lists.newArrayList(idInfo);
            List<Long> kuwoIdList = Lists.newArrayList(*********L);
            BatchIdResponse response = new BatchIdResponse();
            response.setCode(0);
            response.setMsg("");
            response.setData(idInfoList);
            when(this.kuwoIdMappingService.batchGetIdInfoByKuwoId(anyList())).thenReturn(response);
            List<Long> kugouIdList = this.userFacadeService.getKugouIdsByKuwoIds(kuwoIdList);
            Assert.assertFalse(kugouIdList.isEmpty());
        }
    }

    @Test
    @SneakyThrows
    public void getKuwoIdByKugouId() {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            when(apolloConfigService.isKuwoEnv()).thenReturn(true);
            IdInfo idInfo = new IdInfo();
            idInfo.setKugouId(**********L);
            idInfo.setKuwoId(*********L);
            IdResponse response = new IdResponse();
            response.setCode(0);
            response.setMsg("");
            response.setData(idInfo);
            when(this.kuwoIdMappingService.getIdInfoByKugouId(anyLong())).thenReturn(response);
            Optional<Long> optionalLong = this.userFacadeService.getKuwoIdByKugouId(**********L);
            Assert.assertTrue(optionalLong.isPresent());
            Assert.assertEquals(*********L, optionalLong.get().longValue());
        }
    }

    @Test
    @SneakyThrows
    public void getKugouIdsByUserIds() {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            Map<Long, Long> idMap = Maps.newHashMap();
            idMap.put(1290249156L, 1290249156L);
            ResIDMapMsg resIDMapMsg = new ResIDMapMsg();
            resIDMapMsg.setRet(0);
            resIDMapMsg.setMsg("");
            resIDMapMsg.setData(idMap);
            when(this.userModuleV2BizService.getKugouIdMappingByUserIds(anyList())).thenReturn(resIDMapMsg);
            List<Long> userIds = Lists.newArrayList(1290249156L);
            Map<Long, Long> kugouIdMap = this.userFacadeService.getKugouIdsByUserIds(userIds);
            Assert.assertEquals(1, kugouIdMap.size());
        }
    }

    @Test
    @SneakyThrows
    public void getKugouIdByKuwoId() {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            when(apolloConfigService.isKuwoEnv()).thenReturn(true);
            IdInfo idInfo = new IdInfo();
            idInfo.setKugouId(**********L);
            idInfo.setKuwoId(*********L);
            IdResponse response = new IdResponse();
            response.setCode(0);
            response.setMsg("");
            response.setData(idInfo);
            when(this.kuwoIdMappingService.getIdInfoByKuwoId(anyLong())).thenReturn(response);
            Optional<Long> optionalLong = this.userFacadeService.getKugouIdByKuwoId(*********L);
            Assert.assertTrue(optionalLong.isPresent());
            Assert.assertEquals(**********L, optionalLong.get().longValue());
        }
    }

    @Test
    @SneakyThrows
    public void getKuwoIdByKugouIdSingle() {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            when(apolloConfigService.isKuwoEnv()).thenReturn(true);
            IdInfo idInfo = new IdInfo();
            idInfo.setKugouId(**********L);
            idInfo.setKuwoId(*********L);
            IdResponse response = new IdResponse();
            response.setCode(0);
            response.setData(idInfo);
            when(this.kuwoIdMappingService.getIdInfoByKugouId(anyLong())).thenReturn(response);
            Optional<Long> optionalKuwoId = this.userFacadeService.getKuwoIdByKugouId(**********L);
            Assert.assertTrue(optionalKuwoId.isPresent());
            Assert.assertEquals(*********L, optionalKuwoId.get().longValue());
        }
    }

    @Test
    @SneakyThrows
    public void isOverseasRegisterUserTrue() {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            UserCoreInfoVO data = new UserCoreInfoVO();
            data.setKugouId(**********L);
            data.setRegisterFrom(1);
            UserCoreInfoResponse response = new UserCoreInfoResponse();
            response.setRet(0);
            response.setData(data);
            when(this.userModuleV2BizService.getUserCoreInfoByKugouId(anyLong())).thenReturn(response);
            Assert.assertTrue(this.userFacadeService.isOverseasRegisterUser(**********L));
        }
    }

    @Test
    @SneakyThrows
    public void isOverseasRegisterUserFalse() {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            UserCoreInfoVO data = new UserCoreInfoVO();
            data.setKugouId(**********L);
            data.setRegisterFrom(0);
            UserCoreInfoResponse response = new UserCoreInfoResponse();
            response.setRet(0);
            response.setData(data);
            when(this.userModuleV2BizService.getUserCoreInfoByKugouId(anyLong())).thenReturn(response);
            Assert.assertFalse(this.userFacadeService.isOverseasRegisterUser(**********L));
        }
    }

    @Test(expected = ContextedRuntimeException.class)
    @SneakyThrows
    public void isOverseasRegisterUserException() {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            when(this.userModuleV2BizService.getUserCoreInfoByKugouId(anyLong())).thenThrow(new ContextedRuntimeException("test"));
            this.userFacadeService.isOverseasRegisterUser(3000392771L);
            UserCoreInfoResponse response = new UserCoreInfoResponse();
            response.setRet(1);
            response.setData(null);
            when(this.userModuleV2BizService.getUserCoreInfoByKugouId(anyLong())).thenReturn(response);
            this.userFacadeService.isOverseasRegisterUser(3000392772L);
        }
    }

    @Test
    public void isOverseasRegisterUserNull() throws TException {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("**************");
            when(this.userModuleV2BizService.getUserCoreInfoByKugouId(anyLong())).thenReturn(null);
            try {
                this.userFacadeService.isOverseasRegisterUser(3000392772L);
            } catch (Exception e) {
                Assert.assertTrue(true);
            }
        }
    }

}
