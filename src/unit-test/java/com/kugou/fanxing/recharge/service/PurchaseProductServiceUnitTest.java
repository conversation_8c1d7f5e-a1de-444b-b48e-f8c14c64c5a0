package com.kugou.fanxing.recharge.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.kugou.fanxing.biz.commons.util.SnowFlake;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.thrift.PurchaseOrder;
import com.kugou.fanxing.recharge.thrift.PurchaseProductRequestV2;
import com.kugou.fanxing.recharge.util.DateHelper;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class PurchaseProductServiceUnitTest {
    @InjectMocks
    private PurchaseProductService purchaseProductService;
    @Mock
    private PayService payService;
    @Mock
    private RechargeConfig rechargeConfig;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    private ApolloConfigService apolloConfigService;
    @Mock
    private WechatRechargeService wechatRechargeService;
    @Mock
    private AlipayRechargeService alipayRechargeService;
    @Mock
    private RechargeCommonService rechargeCommonService;
    @Mock
    private ValidatingService validatingService;

    @Before
    public void before() {
        when(orderIdService.generateGlobalId()).thenReturn(new SnowFlake(0, 0).nextId());
        when(rechargeConfig.getNotifyUrl(any())).thenReturn("recharge55.fxwork.kugou.com");
        when(payService.getActionIdByAccountChangeType(anyInt())).thenReturn("0");
        when(payService.getGiftName(anyString(), any(PurchaseOrder.class))).thenReturn("");
        when(apolloConfigService.businessNotifyTopicOf(anyString())).thenReturn("");
        when(payService.buildReqExt(any(PurchaseProductRequestV2.class))).thenReturn("{}");
        when(rechargeCommonService.buildRechargeAcross(anyInt(), anyLong(), anyLong(), anyString(), any(PayTypeIdEnum.class), any(BigDecimal.class), anyString(), any(CoinTypeEnum.class)))
                .thenReturn(new RechargeAcrossPO().setRechargeOrderNum("R2"));
        when(orderIdService.generateRechargeOrderNumForAcross()).thenReturn("R1");
        when(rechargeCommonService.buildExtendStr(any(RechargeAcrossPO.class), anyMap())).thenReturn("extend");
        when(validatingService.checkViolation(any())).thenReturn(Optional.empty());
        when(rechargeCommonService.checkThirdPart(anyInt(), anyLong(), anyString(), anyInt(), any(BigDecimal.class))).thenReturn(SysResultCode.SUCCESS);
    }

    /**
     * {
     * "bizType":1,
     * "bizNotifyUrl":"http://zuultest.fxwork.kugou.com/revenue_new_trial/revenue_new_trial/inner/recharge/renewbag/notify",
     * "accountChangeType":0,
     * "goodsList":[{"goodsId":0,"goodsNum":1,"goodsType":1}],"gearId":1,"activityType":110403,"roomId":1020728}
     */
    @Test
    public void callPurchaseBiz() {
        PurchaseOrder purchaseOrder = new PurchaseOrder();
        purchaseOrder.setAccountChangeType(110403);
        purchaseOrder.setFromKugouId(1290249156L);
        purchaseOrder.setToKugouId(1290249156L);
        purchaseOrder.setGoodsId(0);
        purchaseOrder.setGoodsNum(1);
        purchaseOrder.setGoodsType(0);
        purchaseOrder.setExt("");
        Map<String, Object> extJsonMap = ImmutableMap.<String, Object>builder()
                .put("bizType", "1")
                .put("activityType", 110403)
                .put("roomId", 1020728)
                .put("bizNotifyUrl", "http://zuultest.fxwork.kugou.com/revenue_new_trial/revenue_new_trial/inner/recharge/renewbag/notify")
                .build();
        PurchaseProductRequestV2 request = new PurchaseProductRequestV2()
                .setBusinessId("********")
                .setBusinessTime(DateHelper.getCurrentSeconds())
                .setKugouId(1290249156L)
                .setSubject("购买续充礼包")
                .setAmount(new BigDecimal("0.01").stripTrailingZeros().toPlainString())
                .setSyncUrl("https://bing.com")
                .setClientIp("127.0.0.1")
                .setPid(7)
                .setOpenId("")
                .setExtJson(JSON.toJSONString(extJsonMap))
                .setOrderList(Lists.newArrayList(purchaseOrder))
                .setRedirectUrl("https://fx100.fxwork.kugou.com/")
                .setShowUrl("https://fx100.fxwork.kugou.com/");
        request.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_3.getPayTypeId());
        Map<String, Object> payload = Maps.newHashMap();
        payload.put("rechargeOrderNum", "R1");
        when(this.alipayRechargeService.purchaseProductsForQr(any(RechargeAcrossPO.class), anyMap(), anyMap())).thenReturn(payload);
        Map<String, Object> resMap = purchaseProductService.callJavaRechargeV2(request);
        Assert.assertEquals(3, resMap.size());
        when(this.wechatRechargeService.purchaseProductsForQr(any(RechargeAcrossPO.class), anyMap(), anyMap())).thenReturn(payload);
        request.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_39.getPayTypeId());
        resMap = purchaseProductService.callJavaRechargeV2(request);
        Assert.assertEquals(3, resMap.size());
        when(this.wechatRechargeService.purchaseProductsForH5(any(RechargeAcrossPO.class), anyMap(), anyMap())).thenReturn(payload);
        request.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_32.getPayTypeId());
        resMap = purchaseProductService.callJavaRechargeV2(request);
        Assert.assertEquals(3, resMap.size());
        when(this.alipayRechargeService.purchaseProductsForH5(any(RechargeAcrossPO.class), anyMap(), anyMap())).thenReturn(payload);
        request.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_33.getPayTypeId());
        resMap = purchaseProductService.callJavaRechargeV2(request);
        Assert.assertEquals(3, resMap.size());
    }
}
