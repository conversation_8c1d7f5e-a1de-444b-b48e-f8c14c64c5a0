package com.kugou.fanxing.recharge.service;

import com.google.common.collect.Lists;
import com.kugou.fanxing.activity.register.recharge.GenGrayPlanReq;
import com.kugou.fanxing.activity.register.recharge.GrayPlan;
import com.kugou.fanxing.activity.register.recharge.GrayPlanResponse;
import com.kugou.fanxing.activity.register.recharge.RechargeGrayPlanService;
import com.kugou.fanxing.recharge.config.GrayPlanConfig;
import com.kugou.fanxing.recharge.model.vo.*;
import com.kugou.fanxing.recharge.service.stat.UserEverRechargeStatService;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2022/5/24
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class RechargeGrayServiceTest {

    @InjectMocks
    private RechargeGrayService rechargeGrayService;

    @Mock
    private RechargeOptionService rechargeOptionService;

    @Mock
    private RechargeGrayPlanService.Iface rechargeGrayPlanService;

    @Mock
    private UserEverRechargeStatService userEverRechargeStatService;

    @Mock
    private GrayPlanConfig grayPlanConfig;


    @Test
    public void getRechargeOptions() throws TException {
        GrayRechargeOption rechargeOptions = rechargeGrayService.getRechargeOptions(1, "1", 97);
        boolean checked = rechargeOptions.getOptions().stream().anyMatch(GrayRechargeOptionItem::isChecked);
        Assert.assertFalse(checked);

        Map<Integer, Integer> grayDefaultCoin = new HashMap<>();
        grayDefaultCoin.put(3, 100);
        when(grayPlanConfig.grayDefaultCoin()).thenReturn(grayDefaultCoin);

        Map<Integer, Integer> grayMinCoin = new HashMap<>();
        grayMinCoin.put(3, 6);
        when(grayPlanConfig.grayMinCoin()).thenReturn(grayMinCoin);
        when(grayPlanConfig.noviceRechargeGraySwitch()).thenReturn(true);
        when(grayPlanConfig.noviceRechargeGrayPlatform()).thenReturn(new HashSet<>(Arrays.asList(97)));
        List<RechargeOptionItem> rechargeOptionItems = new ArrayList<>();
        RechargeOptionItem item1 = new RechargeOptionItem();
        item1.setCoins(1000);
        item1.setMoney(10);
        rechargeOptionItems.add(item1);

        RechargeOptionItem item2 = new RechargeOptionItem();
        item2.setCoins(10000);
        item2.setMoney(100);

        rechargeOptionItems.add(item2);
        when(rechargeOptionService.getDefaultList()).thenReturn(rechargeOptionItems);
        when(userEverRechargeStatService.hasUserEverRecharge(1)).thenReturn(false);
        GenGrayPlanReq req = new GenGrayPlanReq();
        req.setKugouId(1);
        req.setDeviceId("1");
        when(rechargeGrayPlanService.generatePlan(req)).thenReturn(new GrayPlanResponse(0, "", new GrayPlan().setPlanId(3)));
        rechargeOptions = rechargeGrayService.getRechargeOptions(1, "1", 97);
        GrayRechargeOptionItem item = rechargeOptions.getOptions().stream().filter(v -> v.getMoney() == 100).findFirst().orElse(new GrayRechargeOptionItem());
        Assert.assertTrue(item.isChecked());
        Assert.assertEquals(6, rechargeOptions.getCustomMinimm());
    }


    @Test
    public void grayRechargePage() throws TException {
        when(grayPlanConfig.noviceRechargeGrayPlatform()).thenReturn(new HashSet<>(Arrays.asList(97)));
        when(userEverRechargeStatService.hasUserEverRecharge(1)).thenReturn(false);
        when(grayPlanConfig.noviceRechargeGraySwitch()).thenReturn(true);
        GenGrayPlanReq req = new GenGrayPlanReq();
        req.setKugouId(1);
        req.setDeviceId("1");
        when(rechargeGrayPlanService.generatePlan(req)).thenReturn(new GrayPlanResponse(0, "", new GrayPlan().setPlanId(3)));
        GrayRechargePage grayRechargePage = rechargeGrayService.grayRechargePage(1, "1", 97);
        Assert.assertEquals(1, grayRechargePage.getStyleType());
    }


    @Test
    public void grayNewPage() throws TException {
        when(grayPlanConfig.noviceRechargeGrayPlatform()).thenReturn(new HashSet<>(Arrays.asList(97)));
        when(grayPlanConfig.noviceRechargeGraySwitch()).thenReturn(true);
        when(rechargeGrayPlanService.queryPlan(1)).thenReturn(new GrayPlanResponse(0, "", new GrayPlan().setPlanId(3)));
        Map<Integer, GrayNewPageOption> planOptionMap = new HashMap<>();
        GrayNewPageOption newPageOption = new GrayNewPageOption(BigDecimal.valueOf(6), "123");
        planOptionMap.put(3, newPageOption);
        when(grayPlanConfig.grayNewPagePlanOption()).thenReturn(planOptionMap);

        GrayNewPageOption newPageOption2 = new GrayNewPageOption(BigDecimal.valueOf(100), "456");
        when(grayPlanConfig.grayNewPageGeneralOptions()).thenReturn(Lists.newArrayList(newPageOption2));

        GrayNewPage grayNewPage = rechargeGrayService.grayNewPage(1, 97);
        GrayNewPageOption grayNewPageOption = grayNewPage.getOptions().get(0);
        Assert.assertEquals(6, grayNewPageOption.getMoney().intValue());
        Assert.assertEquals(100, grayNewPage.getOptions().get(1).getMoney().intValue());
        Assert.assertEquals(0, grayNewPage.getDefaultTab());
    }
}
