package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.coupon.thrift.read.CouponInfoVO;
import com.kugou.fanxing.coupon.thrift.read.CouponListByParamsRequest;
import com.kugou.fanxing.coupon.thrift.read.CouponReadService;
import com.kugou.fanxing.coupon.thrift.read.PersonalCouponListResultV2;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class GetPersonalCouponListCommandUnitTest {

    @Mock
    private CouponReadService.Iface couponReadService;

    @Test
    public void getPersonalCouponList() throws TException {
        PersonalCouponListResultV2 resultV2 = new PersonalCouponListResultV2();
        resultV2.setCode(0);
        resultV2.setData(Lists.newArrayList(new CouponInfoVO().setCouponId(1L).setCategory(2), new CouponInfoVO().setCouponId(2L).setCategory(2)));
        when(couponReadService.getPersonalCouponList(any(CouponListByParamsRequest.class))).thenReturn(resultV2);
        GetPersonalCouponListCommand command = new GetPersonalCouponListCommand(couponReadService, 1290249156L);
        List<CouponInfoVO> couponInfoVOList = command.execute();
        Assert.assertEquals(2, couponInfoVOList.size());
        when(couponReadService.getPersonalCouponList(any(CouponListByParamsRequest.class))).thenReturn(null);
        command = new GetPersonalCouponListCommand(couponReadService, 1290249156L);
        couponInfoVOList = command.execute();
        Assert.assertEquals(0, couponInfoVOList.size());
    }

    @Test
    public void getPersonalCouponListException() throws TException {
        when(couponReadService.getPersonalCouponList(any(CouponListByParamsRequest.class))).thenThrow(new TException("test"));
        GetPersonalCouponListCommand command = new GetPersonalCouponListCommand(couponReadService, 1290249156L);
        List<CouponInfoVO> couponInfoVOList = command.execute();
        Assert.assertEquals(0, couponInfoVOList.size());
    }
}
