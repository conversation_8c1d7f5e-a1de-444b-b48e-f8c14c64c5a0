package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.biz.commons.util.SnowFlake;
import com.kugou.fanxing.coupon.thrift.CouponService;
import com.kugou.fanxing.coupon.thrift.OperResult;
import com.kugou.fanxing.coupon.thrift.ReturnResult;
import com.kugou.fanxing.coupon.thrift.UnFreezeVO;
import com.kugou.fanxing.recharge.util.DateHelper;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class CouponServiceConfirmCommandTest {

    @Mock
    private CouponService.Iface couponService;

    @Test
    public void confirm() throws TException {
        List<Integer> failureResultCodeList = Lists.newArrayList(16, 4);
        UnFreezeVO unFreezeVO = new UnFreezeVO()
                .setOrderId(new SnowFlake(1, 1).nextId())
                .setOriginOrderId(1)
                .setKugouId(1)
                .setPId(0)
                .setActionId(1)
                .setReqTimestamp(DateHelper.getCurrentSeconds());
        when(couponService.confirm(unFreezeVO)).thenReturn(new ReturnResult().setCode(0).setMsg("").setData(new OperResult().setOrderId(1).setResult(8)));
        CouponServiceConfirmCommand command = new CouponServiceConfirmCommand(couponService, unFreezeVO, failureResultCodeList);
        Assert.assertEquals(1, command.execute().intValue());
        when(couponService.confirm(unFreezeVO)).thenReturn(new ReturnResult().setCode(0).setMsg("").setData(new OperResult().setOrderId(1).setResult(16)));
        command = new CouponServiceConfirmCommand(couponService, unFreezeVO, failureResultCodeList);
        Assert.assertEquals(2, command.execute().intValue());
        when(couponService.confirm(unFreezeVO)).thenReturn(new ReturnResult().setCode(0).setMsg("").setData(new OperResult().setOrderId(1).setResult(4)));
        command = new CouponServiceConfirmCommand(couponService, unFreezeVO, failureResultCodeList);
        Assert.assertEquals(2, command.execute().intValue());
        when(couponService.confirm(unFreezeVO)).thenReturn(null);
        command = new CouponServiceConfirmCommand(couponService, unFreezeVO, failureResultCodeList);
        Assert.assertEquals(0, command.execute().intValue());
    }

    @Test
    public void confirmException() throws TException {
        List<Integer> failureResultCodeList = Lists.newArrayList(16, 4);
        when(couponService.confirm(any(UnFreezeVO.class))).thenThrow(new ContextedRuntimeException("test"));
        CouponServiceConfirmCommand command = new CouponServiceConfirmCommand(couponService, new UnFreezeVO(), failureResultCodeList);
        Assert.assertEquals(0, command.execute().intValue());
    }
}
