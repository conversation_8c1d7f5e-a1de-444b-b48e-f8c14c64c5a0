package com.kugou.fanxing.recharge.service;

import com.ctrip.framework.apollo.Config;
import com.kugou.fanxing.recharge.model.vo.RefundHandlerVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.Asserts;
import org.assertj.core.api.Assertions;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * @Author: yuzhaopeng
 * @Description:
 * @Date: 2024/3/15 15:41
 */
@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class RefundServiceUnitTest {

    @InjectMocks
    private ApolloConfigService apolloConfigService;

    @Mock
    private Config config;

    @Test
    public void getRefundHandler_businessIsEmpty(){
        Optional<RefundHandlerVO> refundHandler = apolloConfigService.getRefundHandler("");
        Assertions.assertThat(refundHandler.isPresent()).isFalse();
    }

    @Test
    public void getRefundHandler_configIsEmpty(){
        Mockito.when(config.getProperty("refund.handler.config", "")).thenReturn("");
        Optional<RefundHandlerVO> refundHandler = apolloConfigService.getRefundHandler("123");
        Assertions.assertThat(refundHandler.isPresent()).isFalse();
    }

    @Test
    public void getRefundHandler_exception(){
        Mockito.when(config.getProperty("refund.handler.config", "test")).thenReturn("");
        Optional<RefundHandlerVO> refundHandler = apolloConfigService.getRefundHandler("123");
        Assertions.assertThat(refundHandler.isPresent()).isFalse();
    }


    @Test
    public void getRefundHandler_WithInvalidBusinessId_ReturnsEmptyOptional(){
        Mockito.when(config.getProperty("refund.handler.config", "[{\"businessId\":\"1\",\"handlerType\":\"1\"}]")).thenReturn("");
        Optional<RefundHandlerVO> refundHandler = apolloConfigService.getRefundHandler("123");
        Assertions.assertThat(refundHandler.isPresent()).isFalse();
    }

    @Test
    public void getRefundHandler_WithInvalidBusinessId_ReturnsEmptyList(){
        Mockito.when(config.getProperty("refund.handler.config", "[{\"test\":\"1\"}]")).thenReturn("");
        Optional<RefundHandlerVO> refundHandler = apolloConfigService.getRefundHandler("123");
        Assertions.assertThat(refundHandler.isPresent()).isFalse();
    }


}
