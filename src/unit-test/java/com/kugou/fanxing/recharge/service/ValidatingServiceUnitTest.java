package com.kugou.fanxing.recharge.service;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.HashSet;
import java.util.Set;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class ValidatingServiceUnitTest {
    @Mock
    private Validator validator;
    @InjectMocks
    private ValidatingService validatingService;

    @Test
    public void validateParams() {
        Set<ConstraintViolation<String>> violations = new HashSet<>();
        when(validator.validate(anyString())).thenReturn(violations);
        Assert.assertTrue(StringUtils.isBlank(validatingService.validateParams(new Object())));
    }

    @Test
    public void checkViolation() {
        Set<ConstraintViolation<String>> violations = new HashSet<>();
        when(validator.validate(anyString())).thenReturn(violations);
        Assert.assertFalse(validatingService.checkViolation(new Object()).isPresent());
    }
}
