package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.model.ConfirmAirwallexResponse;
import com.kugou.fanxing.recharge.model.ConfirmAirwallexV1;
import com.kugou.fanxing.recharge.model.OrderAirwallexV1;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.*;
import com.kugou.fanxing.recharge.model.vo.BankNameListDto;
import com.kugou.fanxing.recharge.model.vo.LocalAmountVo;
import com.kugou.fanxing.recharge.model.vo.LocalCurrencyConfigPo;
import com.kugou.fanxing.recharge.model.vo.PaymentMethodTypesDto;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.recharge.AirwallexRechargeService;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.fanxing.recharge.util.Pagination;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class AirwallexRechargeServiceUnitTest {

    @InjectMocks
    private AirwallexRechargeService airwallexRechargeService;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    private KupayService kupayService;
    @Mock
    private RechargeOrderService rechargeOrderService;
    @Mock
    private RechargeCommonService rechargeCommonService;
    @Mock
    private RemoteStrategyService remoteStrategyService;

    /**
     * 通用请求参数
     */
    private final WebCommonParam commonParam = new WebCommonParam();

    @Before
    public void before() {
        commonParam.setKugouId(1290249156L);
        commonParam.setPid(0);
        commonParam.setStdPlat(800);
        commonParam.setIp("*************");
        commonParam.setToken("test-token");
        commonParam.setAreaCode("HK");
        when(orderIdService.generateRechargeOrderNumForAcross()).thenReturn("R092021010719280960882719");
    }

    @Test
    public void getBankNameList() {
        GetBankNameListRequest request = new GetBankNameListRequest();
        request.setCountry("HK");
        request.setPaymentMethodType("");
        request.setPage(1);
        request.setPageSize(100);
        when(rechargeCommonService.checkParameter(any(WebCommonParam.class), any(GetBankNameListRequest.class)))
                .thenReturn(SysResultCode.SUCCESS);
        BankNameListDto bankNameListDto = BankNameListDto.builder().has_more(false).items(Lists.newArrayList()).build();
        when(this.kupayService.bankListsAirwallexV1(anyString(), anyString(), any(Pagination.class))).thenReturn(bankNameListDto);
        JsonResult<Map<String, Object>> jsonResult = airwallexRechargeService.getBankNameList(commonParam, request);
        Assert.assertEquals(0, jsonResult.getCode());
    }

    @Test
    public void getPaymentMethodTypes() {
        GetPaymentMethodTypesRequest request = new GetPaymentMethodTypesRequest();
        request.setCountry("HK");
        request.setPage(1);
        request.setPageSize(100);
        when(rechargeCommonService.checkParameter(any(WebCommonParam.class), any(GetPaymentMethodTypesRequest.class)))
                .thenReturn(SysResultCode.SUCCESS);
        PaymentMethodTypesDto paymentMethodTypesDto = PaymentMethodTypesDto.builder().build();
        when(this.rechargeCommonService.getPaymentMethodTypes(anyLong(), anyString(), any(Pagination.class))).thenReturn(paymentMethodTypesDto);
        List<LocalAmountVo> localAmountVoList = Lists.newArrayList();
        when(this.rechargeCommonService.queryLocalAmountVoList(anyString())).thenReturn(localAmountVoList);
        BankNameListDto bankNameListDto = BankNameListDto.builder().has_more(false).items(Lists.newArrayList()).build();
        when(this.kupayService.bankListsAirwallexV1(anyString(), anyString(), any(Pagination.class))).thenReturn(bankNameListDto);
        JsonResult<Map<String, Object>> jsonResult = airwallexRechargeService.getPaymentMethodTypes(commonParam, request);
        Assert.assertEquals(0, jsonResult.getCode());
    }

    @Test
    public void appCreateOrder() {
        AirwallexCreateRequest request = new AirwallexCreateRequest();
        request.setCountry("HK");
        request.setCurrency("HKD");
        request.setCurrencyAmount(BigDecimal.valueOf(100));
        request.setReturnUrl("https://bing.com");
        request.setAmount(BigDecimal.valueOf(600));
        request.setPayTypeIdEnum(PayTypeIdEnum.PAY_TYPE_ID_522);
        when(orderIdService.generateRechargeOrderNumForAcross()).thenReturn("R092020081014580532783499");
        when(this.rechargeCommonService.isValidCountryCode(anyString())).thenReturn(true);
        List<LocalCurrencyConfigPo> localCurrencyConfigPos = Lists.newArrayList();
        localCurrencyConfigPos.add(LocalCurrencyConfigPo.builder().currency("HKD").currencyAmount(BigDecimal.valueOf(100)).cnyAmount(BigDecimal.valueOf(600)).build());
        when(this.rechargeCommonService.queryLocalCurrencyList(anyString())).thenReturn(localCurrencyConfigPos);
        when(rechargeCommonService.checkParameter(any(WebCommonParam.class), any(GetPaymentMethodTypesRequest.class)))
                .thenReturn(SysResultCode.SUCCESS);
        when(rechargeCommonService.checkPreconditionForGetOrder(anyString(), any(WebCommonParam.class), any(GetOrderRequest.class)))
                .thenReturn(SysResultCode.SUCCESS);
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setRechargeOrderNum("R092020081014580532783499");
        rechargeAcrossPO.setConsumeOrderNum("12312381u2938y981724");
        when(this.rechargeCommonService.buildRechargeAcross(anyInt(), anyLong(), anyString(), any(PayTypeIdEnum.class), any(BigDecimal.class), anyString()))
                .thenReturn(rechargeAcrossPO);
        PaymentMethodTypesDto paymentMethodTypesDto = PaymentMethodTypesDto.builder().build();
        when(this.rechargeCommonService.getPaymentMethodTypes(anyLong(), anyString(), any(Pagination.class))).thenReturn(paymentMethodTypesDto);
        List<LocalAmountVo> localAmountVoList = Lists.newArrayList();
        when(this.rechargeCommonService.queryLocalAmountVoList(anyString())).thenReturn(localAmountVoList);
        BankNameListDto bankNameListDto = BankNameListDto.builder().has_more(false).items(Lists.newArrayList()).build();
        when(this.kupayService.bankListsAirwallexV1(anyString(), anyString(), any(Pagination.class))).thenReturn(bankNameListDto);
        OrderAirwallexV1 orderAirwallexV1 = new OrderAirwallexV1();
        orderAirwallexV1.setOut_trade_no("12312381u2938y981724");
        orderAirwallexV1.setIntent("test-intent");
        when(this.kupayService.orderAirwallexV1(any(RechargeAcrossPO.class), anyMap(), anyMap())).thenReturn(orderAirwallexV1);
        when(rechargeOrderService.addRechargeOrder(any(RechargeAcrossPO.class))).thenReturn(1);
        when(remoteStrategyService.forceAirwallexCard3DS(anyLong(), anyString(), any(AirwallexCreateRequest.class))).thenReturn(false);
        JsonResult<Map<String, Object>> jsonResult = airwallexRechargeService.appCreateOrder(commonParam, request);
        Assert.assertEquals(0, jsonResult.getCode());
        Assert.assertEquals("test-intent", jsonResult.getData().get("intent"));
        Assert.assertEquals("R092020081014580532783499", jsonResult.getData().get("rechargeOrderNum"));
        Assert.assertEquals("12312381u2938y981724", jsonResult.getData().get("outTradeNo"));
    }

    @Test
    public void appConfirmOrder() {
        AirwallexConfirmRequest request = new AirwallexConfirmRequest();
        request.setRechargeOrderNum("R092020081014580532783499");
        request.setOutTradeNo("12312381u2938y981724");
        request.setPaymentMethod("test");
        request.setPaymentType("test");
        when(rechargeCommonService.checkParameter(any(WebCommonParam.class), any(AirwallexConfirmRequest.class)))
                .thenReturn(SysResultCode.SUCCESS);
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setRechargeOrderNum("R092020081014580532783499");
        rechargeAcrossPO.setConsumeOrderNum("12312381u2938y981724");
        rechargeAcrossPO.setKugouId(1290249156L);
        when(rechargeOrderService.queryByRechargeOrderNum(anyString())).thenReturn(Optional.of(rechargeAcrossPO));
        ConfirmAirwallexV1 confirmAirwallexV1 = ConfirmAirwallexV1.builder().url("test").method("POST").build();
        when(this.kupayService.confirmAirwallexV1(rechargeAcrossPO, request.getPaymentType(), request.getPaymentMethod()))
                .thenReturn(confirmAirwallexV1);
        JsonResult<Map<String, Object>> jsonResult = airwallexRechargeService.appConfirmOrder(commonParam, request);
        Assert.assertEquals(0, jsonResult.getCode());
    }

    @Test
    public void testJson() {
        String wechatPayJson = "{\"error_code\":0,\"error_msg\":\"\",\"status\":1,\"data\":{\"type\":\"call_sdk\",\"data\":{\"appId\":\"wx4c86d73fe4f82431\",\"nonceStr\":\"lhj0Ka2jB1YCnAkvncgDyZNMd8XfZlpM\",\"package\":\"Sign=WXPay\",\"partnerId\":\"115494984\",\"prepayId\":\"https:\\/\\/pci-api-demo.airwallex.com\\/pa\\/mock\\/wechat\\/hk\\/v2\\/qr_code_scanned?outTradeNo=66495557990005241660287676415651&amount=500\",\"sign\":\"E6F035BCC2DB229B04A9F8A8CE8855132258589B3D7DBDB8719BE7640F1AD30D\",\"timeStamp\":\"1660287676\"}}}";
        ConfirmAirwallexResponse response1 = JsonUtils.readValue(wechatPayJson, ConfirmAirwallexResponse.class);
        Assert.assertNotNull(response1);
        String alipayHKJson = "{\"error_code\":0,\"error_msg\":\"\",\"status\":1,\"data\":{\"type\":\"redirect\",\"qrcode_url\":\"https:\\/\\/cdn-psp.marmot-cloud.com\\/acwallet\\/alipayconnectcode?code=golcashier1660287915776sandbox&golSandbox=true&pspName=ALIPAY_HK&origin=apwallet_download\",\"qrcode\":\"https:\\/\\/cdn-psp.marmot-cloud.com\\/acwallet\\/alipayconnectcode?code=golcashier1660287915776sandbox&golSandbox=true&pspName=ALIPAY_HK&origin=apwallet_download\",\"method\":\"GET\",\"url\":\"https:\\/\\/cdn-psp.marmot-cloud.com\\/acwallet\\/alipayconnectcode?code=golcashier1660287915776sandbox&golSandbox=true&pspName=ALIPAY_HK&origin=apwallet_download\"}}";
        ConfirmAirwallexV1 response2 = JsonUtils.parseJsonPathChecked(alipayHKJson, "$.data", ConfirmAirwallexV1.class);
        Assert.assertNotNull(response2);
    }

}
