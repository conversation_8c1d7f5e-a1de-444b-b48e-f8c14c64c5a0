package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.thrift.idmapping.user.UserIdMappingService;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class GetUserIdByKugouIdCommandUnitTest {

    @Mock
    private UserIdMappingService.Iface userIdMappingService;

    @Test
    public void confirm() throws TException {
        when(userIdMappingService.getUserIdByKugouId(anyLong())).thenReturn(1290249156L);
        GetUserIdByKugouIdCommand command = new GetUserIdByKugouIdCommand(userIdMappingService, 1290249156L);
        Assert.assertTrue(command.execute().isPresent());
    }

    @Test
    public void confirmException() throws TException {
        when(userIdMappingService.getUserIdByKugouId(anyLong())).thenThrow(new ContextedRuntimeException());
        GetUserIdByKugouIdCommand command = new GetUserIdByKugouIdCommand(userIdMappingService, 1290249156L);
        Assert.assertFalse(command.execute().isPresent());
    }
}
