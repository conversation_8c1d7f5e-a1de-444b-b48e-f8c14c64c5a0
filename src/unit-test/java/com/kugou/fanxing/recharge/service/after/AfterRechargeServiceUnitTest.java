package com.kugou.fanxing.recharge.service.after;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.FirstRechargeDao;
import com.kugou.fanxing.recharge.model.dto.AfterRechargeAcrossDTO;
import com.kugou.fanxing.recharge.model.dto.CoinCallbackDTO;
import com.kugou.fanxing.recharge.model.dto.PurchaseMsgDTO;
import com.kugou.fanxing.recharge.model.po.FirstRechargePO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.mq.PostRechargeNsqProducer;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.thrift.callback.ConsumeParam;
import com.kugou.fanxing.recharge.thrift.callback.PurchaseForIosRequest;
import com.kugou.fanxing.recharge.thrift.callback.RenewalsForIosRequest;
import com.kugou.fanxing.recharge.util.SpringContextUtils;
import com.kugou.fanxing.thrift.acksocket.gather.service.AppDispatchServiceV2;
import com.kugou.fanxing.thrift.acksocket.gather.types.BoolResponse;
import com.kugou.fanxing.thrift.acksocket.gather.types.MsgOption;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.nio.ByteBuffer;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AfterRechargeServiceUnitTest {

    private static final String EXTEND = "eyJjYWxsQmFja1NpZ24iOiI5OWQ3MjdjMGY4ZTc3OTY5ZGZkZGVkYmRiNTI4MmQ4NyIsImNhbGxCYWNrQXJnIjp7ImFtb3VudCI6NjE4LCJhZGRUaW1lIjoxNjA5NzU2MjgyLCJjb3Vwb24iOjAsImJ1eVJpY2hMZXZlbCI6e30sInJlYmF0ZSI6IjAiLCJjb25zdW1lQXJncyI6e30sImJ1c2luZXNzRXh0IjoiIiwiY291cG9uSWQiOjAsInZlcnNpb24iOiIyMDE3MDExMSIsImNvdXBvbk9yZGVySWQiOjAsInBheVR5cGVJZCI6MTAwNiwibW9uZXkiOjYxOCwicmVmZXIiOjAsImNGcm9tIjoyLCJjaGFubmVsSWQiOjEwMDksImt1Z291SWQiOjQxNTc3MDcyNn19";

    @InjectMocks
    private AfterRechargeService afterRechargeService;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    private FirstRechargeDao firstRechargeDao;
    @Mock
    private PostRechargeNsqProducer postRechargeNsqProducer;
    @Mock
    private HttpServletRequest request;
    @Mock
    private AppDispatchServiceV2.Iface appDispatchServiceV2;

    @Test
    public void afterRechargeSuccess() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            when(orderIdService.generateGlobalId()).thenReturn(123L);
            when(this.firstRechargeDao.add(any(FirstRechargePO.class))).thenReturn(1);
            when(postRechargeNsqProducer.sendPostRechargeMsg(anyString(), any(Object.class))).thenReturn(true);
            RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO()
                    .setAmount(BigDecimal.valueOf(100))
                    .setCoupon(BigDecimal.valueOf(100))
                    .setMoney(BigDecimal.valueOf(100))
                    .setRealAmount(BigDecimal.valueOf(100))
                    .setCoin(BigDecimal.valueOf(100))
                    .setConsumeOrderNum("")
                    .setExtend(EXTEND)
                    .setRefer(1)
                    .setPayTypeId(1012);
            this.afterRechargeService.afterRechargeSuccess(rechargeAcrossPO);
            verify(postRechargeNsqProducer, atLeast(1)).sendPostRechargeMsg(anyString(), any(Object.class));

        }
    }

    @Test
    public void afterRechargeSuccessSingBusiness(){
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO()
                .setAmount(BigDecimal.valueOf(100))
                .setCoupon(BigDecimal.valueOf(100))
                .setMoney(BigDecimal.valueOf(100))
                .setRealAmount(BigDecimal.valueOf(100))
                .setCoin(BigDecimal.valueOf(100))
                .setConsumeOrderNum("")
                .setExtend(EXTEND)
                .setRefer(1)
                .setCoinType(CoinTypeEnum.SING_COIN.getCoinType())
                .setPayTypeId(1012);
        this.afterRechargeService.afterRechargeSuccess(rechargeAcrossPO);
        verify(postRechargeNsqProducer, times(1)).sendPostRechargeMsg(anyString(), any(Object.class));
    }

    @Test
    public void decodeExtend() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            String decoded = this.afterRechargeService.decodeExtend(EXTEND);
            Assert.assertTrue(StringUtils.isNotBlank(decoded));
        }
    }

    @Test
    public void parseExtendToJSONObject() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            JSONObject jsonObject = this.afterRechargeService.parseExtendToJSONObject(null);
            Assert.assertNotNull(jsonObject);
            log.warn(JSON.toJSONString(jsonObject));
            jsonObject = this.afterRechargeService.parseExtendToJSONObject(EXTEND);
            log.warn(JSON.toJSONString(jsonObject));
            jsonObject = this.afterRechargeService.parseExtendToJSONObject("not base64 decode info");
            Assert.assertNotNull(jsonObject);
        }
    }

    @Test
    public void setupRechargeRebateGrayFlag() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            String extraJsonData = "{\"signBook\":\"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\",\"fromType\":\"moblie7\",\"goodsId\":\"com.kugou.hangFanxing.318yuan\"}";
            RechargeAcrossPO callbackOrder = new RechargeAcrossPO();
            callbackOrder.setExtraJsonData(extraJsonData);
            AfterRechargeAcrossDTO afterRechargeAcrossDTO = new AfterRechargeAcrossDTO();
            this.afterRechargeService.setupRechargeRebateGrayFlag(callbackOrder, afterRechargeAcrossDTO);
            Assert.assertTrue(afterRechargeAcrossDTO.getExtraJsonData().contains("rechargeRebateGray"));
        }
    }

    @Test
    public void isTmallFirstRecharge() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            RechargeAcrossPO callbackOrder = new RechargeAcrossPO();
            callbackOrder.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_1012.getPayTypeId());
            when(this.firstRechargeDao.add(any(FirstRechargePO.class))).thenReturn(1);
            Assert.assertTrue(this.afterRechargeService.isTmallFirstRecharge(callbackOrder));
        }
    }

    @Test
    public void isTmallFirstRechargeException() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            RechargeAcrossPO callbackOrder = new RechargeAcrossPO();
            callbackOrder.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_1012.getPayTypeId());
            when(this.firstRechargeDao.add(any(FirstRechargePO.class))).thenThrow(new ContextedRuntimeException());
            Assert.assertFalse(this.afterRechargeService.isTmallFirstRecharge(callbackOrder));
        }
    }

    @Test
    public void buildFirstRechargePO() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            RechargeAcrossPO callbackOrder = new RechargeAcrossPO();
            when(orderIdService.generateGlobalId()).thenReturn(123L);
            FirstRechargePO firstRechargePO = this.afterRechargeService.buildFirstRechargePO(callbackOrder);
            Assert.assertEquals("123", firstRechargePO.getGlobalId());
        }
    }

    @Test
    public void sendPurchaseTopicForIos() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            String topic = "";
            RechargeAcrossPO targetOrder = new RechargeAcrossPO();
            targetOrder.setRechargeOrderNum("R09202012S1TID1000000750236975");
            targetOrder.setStatus(1);
            targetOrder.setMoney(new BigDecimal("1048"));
            targetOrder.setAddTime(1607080679);
            targetOrder.setReType(1);
            targetOrder.setPayTypeId(1006);
            targetOrder.setRefer(1);
            targetOrder.setChannelId(2);
            targetOrder.setCFrom(3);
            targetOrder.setCoin(new BigDecimal("104800"));
            targetOrder.setExtraJsonData("{\"signBook\":\"ewoJInNpZ25hdHVyZSIgPSAiQXpId0JtcmxXMk1oeE1GTmNJLzl0ckFFMWhJakY4VWlUZVlXd2RUUGFWYnV4U3NMZ3E1VHlOYklSUzBGRVhEUzZaa3VOdFdYY1lpRElmWGVHNzhDNUQxdTJGZU04Y1pSekdvTzArMGRvaEVwcGVmV1ZnWHNycEpYQzdNZEhENmV1SjU3bUoyclFseXZnV1NjcGJGVk4rRnJIeis2YmJmRUtzOUxWK3Y5TUpWSERCU1I4bHBTTDhOb0UxdG5jY2RYT05wem8xcmhWUUVqOEcvM3krcnVORjkzb0NwVEtuZVhCTGtwd01UVHRoanB6WnQ0TnVTOFV1ZC9nc044OTNSNVBTdlhQTU52RkxUaDBzVXRnWnVvU2pTV25NTmsrVXNyTVova2thWlpoM1lBZUpndHpHb3hTOVA3MGxhVWY1YWtCUFB3Um1NUU01Wkova3hKS2pidjRDSUFBQVdBTUlJRmZEQ0NCR1NnQXdJQkFnSUlEdXRYaCtlZUNZMHdEUVlKS29aSWh2Y05BUUVGQlFBd2daWXhDekFKQmdOVkJBWVRBbFZUTVJNd0VRWURWUVFLREFwQmNIQnNaU0JKYm1NdU1Td3dLZ1lEVlFRTERDTkJjSEJzWlNCWGIzSnNaSGRwWkdVZ1JHVjJaV3h2Y0dWeUlGSmxiR0YwYVc5dWN6RkVNRUlHQTFVRUF3dzdRWEJ3YkdVZ1YyOXliR1IzYVdSbElFUmxkbVZzYjNCbGNpQlNaV3hoZEdsdmJuTWdRMlZ5ZEdsbWFXTmhkR2x2YmlCQmRYUm9iM0pwZEhrd0hoY05NVFV4TVRFek1ESXhOVEE1V2hjTk1qTXdNakEzTWpFME9EUTNXakNCaVRFM01EVUdBMVVFQXd3dVRXRmpJRUZ3Y0NCVGRHOXlaU0JoYm1RZ2FWUjFibVZ6SUZOMGIzSmxJRkpsWTJWcGNIUWdVMmxuYm1sdVp6RXNNQ29HQTFVRUN3d2pRWEJ3YkdVZ1YyOXliR1IzYVdSbElFUmxkbVZzYjNCbGNpQlNaV3hoZEdsdmJuTXhFekFSQmdOVkJBb01Da0Z3Y0d4bElFbHVZeTR4Q3pBSkJnTlZCQVlUQWxWVE1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBcGMrQi9TV2lnVnZXaCswajJqTWNqdUlqd0tYRUpzczl4cC9zU2cxVmh2K2tBdGVYeWpsVWJYMS9zbFFZbmNRc1VuR09aSHVDem9tNlNkWUk1YlNJY2M4L1cwWXV4c1FkdUFPcFdLSUVQaUY0MWR1MzBJNFNqWU5NV3lwb041UEM4cjBleE5LaERFcFlVcXNTNCszZEg1Z1ZrRFV0d3N3U3lvMUlnZmRZZUZScjZJd3hOaDlLQmd4SFZQTTNrTGl5a29sOVg2U0ZTdUhBbk9DNnBMdUNsMlAwSzVQQi9UNXZ5c0gxUEttUFVockFKUXAyRHQ3K21mNy93bXYxVzE2c2MxRkpDRmFKekVPUXpJNkJBdENnbDdaY3NhRnBhWWVRRUdnbUpqbTRIUkJ6c0FwZHhYUFEzM1k3MkMzWmlCN2o3QWZQNG83UTAvb21WWUh2NGdOSkl3SURBUUFCbzRJQjF6Q0NBZE13UHdZSUt3WUJCUVVIQVFFRU16QXhNQzhHQ0NzR0FRVUZCekFCaGlOb2RIUndPaTh2YjJOemNDNWhjSEJzWlM1amIyMHZiMk56Y0RBekxYZDNaSEl3TkRBZEJnTlZIUTRFRmdRVWthU2MvTVIydDUrZ2l2Uk45WTgyWGUwckJJVXdEQVlEVlIwVEFRSC9CQUl3QURBZkJnTlZIU01FR0RBV2dCU0lKeGNKcWJZWVlJdnM2N3IyUjFuRlVsU2p0ekNDQVI0R0ExVWRJQVNDQVJVd2dnRVJNSUlCRFFZS0tvWklodmRqWkFVR0FUQ0IvakNCd3dZSUt3WUJCUVVIQWdJd2diWU1nYk5TWld4cFlXNWpaU0J2YmlCMGFHbHpJR05sY25ScFptbGpZWFJsSUdKNUlHRnVlU0J3WVhKMGVTQmhjM04xYldWeklHRmpZMlZ3ZEdGdVkyVWdiMllnZEdobElIUm9aVzRnWVhCd2JHbGpZV0pzWlNCemRHRnVaR0Z5WkNCMFpYSnRjeUJoYm1RZ1kyOXVaR2wwYVc5dWN5QnZaaUIxYzJVc0lHTmxjblJwWm1sallYUmxJSEJ2YkdsamVTQmhibVFnWTJWeWRHbG1hV05oZEdsdmJpQndjbUZqZEdsalpTQnpkR0YwWlcxbGJuUnpMakEyQmdnckJnRUZCUWNDQVJZcWFIUjBjRG92TDNkM2R5NWhjSEJzWlM1amIyMHZZMlZ5ZEdsbWFXTmhkR1ZoZFhSb2IzSnBkSGt2TUE0R0ExVWREd0VCL3dRRUF3SUhnREFRQmdvcWhraUc5Mk5rQmdzQkJBSUZBREFOQmdrcWhraUc5dzBCQVFVRkFBT0NBUUVBRGFZYjB5NDk0MXNyQjI1Q2xtelQ2SXhETUlKZjRGelJqYjY5RDcwYS9DV1MyNHlGdzRCWjMrUGkxeTRGRkt3TjI3YTQvdncxTG56THJSZHJqbjhmNUhlNXNXZVZ0Qk5lcGhtR2R2aGFJSlhuWTR3UGMvem83Y1lmcnBuNFpVaGNvT0FvT3NBUU55MjVvQVE1SDNPNXlBWDk4dDUvR2lvcWJpc0IvS0FnWE5ucmZTZW1NL2oxbU9DK1JOdXhUR2Y4YmdwUHllSUdxTktYODZlT2ExR2lXb1IxWmRFV0JHTGp3Vi8xQ0tuUGFObVNBTW5CakxQNGpRQmt1bGhnd0h5dmozWEthYmxiS3RZZGFHNllRdlZNcHpjWm04dzdISG9aUS9PamJiOUlZQVlNTnBJcjdONFl0UkhhTFNQUWp2eWdhWndYRzU2QWV6bEhSVEJoTDhjVHFBPT0iOwoJInB1cmNoYXNlLWluZm8iID0gImV3b0pJbTl5YVdkcGJtRnNMWEIxY21Ob1lYTmxMV1JoZEdVdGNITjBJaUE5SUNJeU1ESXdMVEV5TFRBMElEQXpPakUzT2pNNElFRnRaWEpwWTJFdlRHOXpYMEZ1WjJWc1pYTWlPd29KSW5WdWFYRjFaUzFwWkdWdWRHbG1hV1Z5SWlBOUlDSXdZbVF3Wm1ZNVpqVmpNRFUzTXpCaE9HTmpZVEZsTldVME1qSXdNREF6TmpBeFpURmpOalUzSWpzS0NTSnZjbWxuYVc1aGJDMTBjbUZ1YzJGamRHbHZiaTFwWkNJZ1BTQWlNVEF3TURBd01EYzFNREl6TmprM05TSTdDZ2tpWW5aeWN5SWdQU0FpTkM0NU55NDJNQzR4SWpzS0NTSjBjbUZ1YzJGamRHbHZiaTFwWkNJZ1BTQWlNVEF3TURBd01EYzFNREl6TmprM05TSTdDZ2tpY1hWaGJuUnBkSGtpSUQwZ0lqRWlPd29KSW05eWFXZHBibUZzTFhCMWNtTm9ZWE5sTFdSaGRHVXRiWE1pSUQwZ0lqRTJNRGN3T0RBMk5UZzVPVGdpT3dvSkluVnVhWEYxWlMxMlpXNWtiM0l0YVdSbGJuUnBabWxsY2lJZ1BTQWlOelJETkRWR1JUTXRSVFU0UVMwMFFqWTRMVGt5TXpRdE1UUXdPRVk1TmtNNU1qRTBJanNLQ1NKd2NtOWtkV04wTFdsa0lpQTlJQ0pqYjIwdWEzVm5iM1V1Wm1GdWVHbHVaMkZ3Y0hOMGIzSmxMbk5vYUhVeE1EUTRJanNLQ1NKcGRHVnRMV2xrSWlBOUlDSXhORFkyTnpVek5ESTNJanNLQ1NKaWFXUWlJRDBnSW1OdmJTNXJkV2R2ZFM1bVlXNTRhVzVuWVhCd2MzUnZjbVVpT3dvSkltbHpMV2x1TFdsdWRISnZMVzltWm1WeUxYQmxjbWx2WkNJZ1BTQWlabUZzYzJVaU93b0pJbkIxY21Ob1lYTmxMV1JoZEdVdGJYTWlJRDBnSWpFMk1EY3dPREEyTlRnNU9UZ2lPd29KSW5CMWNtTm9ZWE5sTFdSaGRHVWlJRDBnSWpJd01qQXRNVEl0TURRZ01URTZNVGM2TXpnZ1JYUmpMMGROVkNJN0Nna2lhWE10ZEhKcFlXd3RjR1Z5YVc5a0lpQTlJQ0ptWVd4elpTSTdDZ2tpY0hWeVkyaGhjMlV0WkdGMFpTMXdjM1FpSUQwZ0lqSXdNakF0TVRJdE1EUWdNRE02TVRjNk16Z2dRVzFsY21sallTOU1iM05mUVc1blpXeGxjeUk3Q2draWIzSnBaMmx1WVd3dGNIVnlZMmhoYzJVdFpHRjBaU0lnUFNBaU1qQXlNQzB4TWkwd05DQXhNVG94Tnpvek9DQkZkR012UjAxVUlqc0tmUT09IjsKCSJlbnZpcm9ubWVudCIgPSAiU2FuZGJveCI7CgkicG9kIiA9ICIxMDAiOwoJInNpZ25pbmctc3RhdHVzIiA9ICIwIjsKfQ==\",\"goodsId\":\"com.kugou.fanxingappstore.shhu1048\",\"fromType\":\"moblie7\",\"businessId\":\"1446219001364501362\"}");
            PurchaseForIosRequest request = new PurchaseForIosRequest();
            ConsumeParam consumeParam = new ConsumeParam();
            consumeParam.setFromKugouId(732115211);
            consumeParam.setToKugouId(490000052);
            request.setConsumeParam(consumeParam);
            request.setExt("");
            when(postRechargeNsqProducer.sendPostRechargeMsg(anyString(), any(PurchaseMsgDTO.class))).thenReturn(true);
            Assert.assertTrue(this.afterRechargeService.sendPurchaseTopicForIos(topic, targetOrder, request));
            when(postRechargeNsqProducer.sendPostRechargeMsg(anyString(), any(PurchaseMsgDTO.class))).thenReturn(false);
            Assert.assertFalse(this.afterRechargeService.sendPurchaseTopicForIos(topic, targetOrder, request));
        }
    }

    @Test
    public void sendRenewalsTopicForIos() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            String topic = "";
            RechargeAcrossPO targetOrder = new RechargeAcrossPO();
            targetOrder.setRechargeOrderNum("R09202012S1TID1000000750236975");
            targetOrder.setStatus(1);
            targetOrder.setMoney(new BigDecimal("1048"));
            targetOrder.setAddTime(1607080679);
            targetOrder.setReType(2);
            targetOrder.setPayTypeId(1006);
            targetOrder.setRefer(1);
            targetOrder.setChannelId(2);
            targetOrder.setCFrom(3);
            targetOrder.setCoin(new BigDecimal("104800"));
            targetOrder.setExtraJsonData("{\"signBook\":\"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\",\"goodsId\":\"com.kugou.fanxingappstore.shhu1048\",\"fromType\":\"moblie7\",\"businessId\":\"1446219001364501362\"}");
            RenewalsForIosRequest request = new RenewalsForIosRequest();
            ConsumeParam consumeParam = new ConsumeParam();
            consumeParam.setFromKugouId(732115211);
            consumeParam.setToKugouId(490000052);
            request.setConsumeParam(consumeParam);
            request.setExt("");
            when(postRechargeNsqProducer.sendPostRechargeMsg(anyString(), any(PurchaseMsgDTO.class))).thenReturn(true);
            Assert.assertTrue(this.afterRechargeService.sendRenewalsTopicForIos(topic, targetOrder, request));
            when(postRechargeNsqProducer.sendPostRechargeMsg(anyString(), any(PurchaseMsgDTO.class))).thenReturn(false);
            Assert.assertFalse(this.afterRechargeService.sendRenewalsTopicForIos(topic, targetOrder, request));
        }
    }

    @Test
    public void sendPurchaseTopic() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            String topic = "";
            RechargeAcrossPO targetOrder = new RechargeAcrossPO();
            targetOrder.setRechargeOrderNum("R09202012S1TID1000000750236975");
            targetOrder.setStatus(1);
            targetOrder.setMoney(new BigDecimal("1048"));
            targetOrder.setAddTime(1607080679);
            targetOrder.setReType(1);
            targetOrder.setPayTypeId(1006);
            targetOrder.setRefer(1);
            targetOrder.setChannelId(2);
            targetOrder.setCFrom(3);
            targetOrder.setCoin(new BigDecimal("104800"));
            targetOrder.setExtraJsonData("{\"signBook\":\"ewoJInNpZ25hdHVyZSIgPSAiQXpId0JtcmxXMk1oeE1GTmNJLzl0ckFFMWhJakY4VWlUZVlXd2RUUGFWYnV4U3NMZ3E1VHlOYklSUzBGRVhEUzZaa3VOdFdYY1lpRElmWGVHNzhDNUQxdTJGZU04Y1pSekdvTzArMGRvaEVwcGVmV1ZnWHNycEpYQzdNZEhENmV1SjU3bUoyclFseXZnV1NjcGJGVk4rRnJIeis2YmJmRUtzOUxWK3Y5TUpWSERCU1I4bHBTTDhOb0UxdG5jY2RYT05wem8xcmhWUUVqOEcvM3krcnVORjkzb0NwVEtuZVhCTGtwd01UVHRoanB6WnQ0TnVTOFV1ZC9nc044OTNSNVBTdlhQTU52RkxUaDBzVXRnWnVvU2pTV25NTmsrVXNyTVova2thWlpoM1lBZUpndHpHb3hTOVA3MGxhVWY1YWtCUFB3Um1NUU01Wkova3hKS2pidjRDSUFBQVdBTUlJRmZEQ0NCR1NnQXdJQkFnSUlEdXRYaCtlZUNZMHdEUVlKS29aSWh2Y05BUUVGQlFBd2daWXhDekFKQmdOVkJBWVRBbFZUTVJNd0VRWURWUVFLREFwQmNIQnNaU0JKYm1NdU1Td3dLZ1lEVlFRTERDTkJjSEJzWlNCWGIzSnNaSGRwWkdVZ1JHVjJaV3h2Y0dWeUlGSmxiR0YwYVc5dWN6RkVNRUlHQTFVRUF3dzdRWEJ3YkdVZ1YyOXliR1IzYVdSbElFUmxkbVZzYjNCbGNpQlNaV3hoZEdsdmJuTWdRMlZ5ZEdsbWFXTmhkR2x2YmlCQmRYUm9iM0pwZEhrd0hoY05NVFV4TVRFek1ESXhOVEE1V2hjTk1qTXdNakEzTWpFME9EUTNXakNCaVRFM01EVUdBMVVFQXd3dVRXRmpJRUZ3Y0NCVGRHOXlaU0JoYm1RZ2FWUjFibVZ6SUZOMGIzSmxJRkpsWTJWcGNIUWdVMmxuYm1sdVp6RXNNQ29HQTFVRUN3d2pRWEJ3YkdVZ1YyOXliR1IzYVdSbElFUmxkbVZzYjNCbGNpQlNaV3hoZEdsdmJuTXhFekFSQmdOVkJBb01Da0Z3Y0d4bElFbHVZeTR4Q3pBSkJnTlZCQVlUQWxWVE1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBcGMrQi9TV2lnVnZXaCswajJqTWNqdUlqd0tYRUpzczl4cC9zU2cxVmh2K2tBdGVYeWpsVWJYMS9zbFFZbmNRc1VuR09aSHVDem9tNlNkWUk1YlNJY2M4L1cwWXV4c1FkdUFPcFdLSUVQaUY0MWR1MzBJNFNqWU5NV3lwb041UEM4cjBleE5LaERFcFlVcXNTNCszZEg1Z1ZrRFV0d3N3U3lvMUlnZmRZZUZScjZJd3hOaDlLQmd4SFZQTTNrTGl5a29sOVg2U0ZTdUhBbk9DNnBMdUNsMlAwSzVQQi9UNXZ5c0gxUEttUFVockFKUXAyRHQ3K21mNy93bXYxVzE2c2MxRkpDRmFKekVPUXpJNkJBdENnbDdaY3NhRnBhWWVRRUdnbUpqbTRIUkJ6c0FwZHhYUFEzM1k3MkMzWmlCN2o3QWZQNG83UTAvb21WWUh2NGdOSkl3SURBUUFCbzRJQjF6Q0NBZE13UHdZSUt3WUJCUVVIQVFFRU16QXhNQzhHQ0NzR0FRVUZCekFCaGlOb2RIUndPaTh2YjJOemNDNWhjSEJzWlM1amIyMHZiMk56Y0RBekxYZDNaSEl3TkRBZEJnTlZIUTRFRmdRVWthU2MvTVIydDUrZ2l2Uk45WTgyWGUwckJJVXdEQVlEVlIwVEFRSC9CQUl3QURBZkJnTlZIU01FR0RBV2dCU0lKeGNKcWJZWVlJdnM2N3IyUjFuRlVsU2p0ekNDQVI0R0ExVWRJQVNDQVJVd2dnRVJNSUlCRFFZS0tvWklodmRqWkFVR0FUQ0IvakNCd3dZSUt3WUJCUVVIQWdJd2diWU1nYk5TWld4cFlXNWpaU0J2YmlCMGFHbHpJR05sY25ScFptbGpZWFJsSUdKNUlHRnVlU0J3WVhKMGVTQmhjM04xYldWeklHRmpZMlZ3ZEdGdVkyVWdiMllnZEdobElIUm9aVzRnWVhCd2JHbGpZV0pzWlNCemRHRnVaR0Z5WkNCMFpYSnRjeUJoYm1RZ1kyOXVaR2wwYVc5dWN5QnZaaUIxYzJVc0lHTmxjblJwWm1sallYUmxJSEJ2YkdsamVTQmhibVFnWTJWeWRHbG1hV05oZEdsdmJpQndjbUZqZEdsalpTQnpkR0YwWlcxbGJuUnpMakEyQmdnckJnRUZCUWNDQVJZcWFIUjBjRG92TDNkM2R5NWhjSEJzWlM1amIyMHZZMlZ5ZEdsbWFXTmhkR1ZoZFhSb2IzSnBkSGt2TUE0R0ExVWREd0VCL3dRRUF3SUhnREFRQmdvcWhraUc5Mk5rQmdzQkJBSUZBREFOQmdrcWhraUc5dzBCQVFVRkFBT0NBUUVBRGFZYjB5NDk0MXNyQjI1Q2xtelQ2SXhETUlKZjRGelJqYjY5RDcwYS9DV1MyNHlGdzRCWjMrUGkxeTRGRkt3TjI3YTQvdncxTG56THJSZHJqbjhmNUhlNXNXZVZ0Qk5lcGhtR2R2aGFJSlhuWTR3UGMvem83Y1lmcnBuNFpVaGNvT0FvT3NBUU55MjVvQVE1SDNPNXlBWDk4dDUvR2lvcWJpc0IvS0FnWE5ucmZTZW1NL2oxbU9DK1JOdXhUR2Y4YmdwUHllSUdxTktYODZlT2ExR2lXb1IxWmRFV0JHTGp3Vi8xQ0tuUGFObVNBTW5CakxQNGpRQmt1bGhnd0h5dmozWEthYmxiS3RZZGFHNllRdlZNcHpjWm04dzdISG9aUS9PamJiOUlZQVlNTnBJcjdONFl0UkhhTFNQUWp2eWdhWndYRzU2QWV6bEhSVEJoTDhjVHFBPT0iOwoJInB1cmNoYXNlLWluZm8iID0gImV3b0pJbTl5YVdkcGJtRnNMWEIxY21Ob1lYTmxMV1JoZEdVdGNITjBJaUE5SUNJeU1ESXdMVEV5TFRBMElEQXpPakUzT2pNNElFRnRaWEpwWTJFdlRHOXpYMEZ1WjJWc1pYTWlPd29KSW5WdWFYRjFaUzFwWkdWdWRHbG1hV1Z5SWlBOUlDSXdZbVF3Wm1ZNVpqVmpNRFUzTXpCaE9HTmpZVEZsTldVME1qSXdNREF6TmpBeFpURmpOalUzSWpzS0NTSnZjbWxuYVc1aGJDMTBjbUZ1YzJGamRHbHZiaTFwWkNJZ1BTQWlNVEF3TURBd01EYzFNREl6TmprM05TSTdDZ2tpWW5aeWN5SWdQU0FpTkM0NU55NDJNQzR4SWpzS0NTSjBjbUZ1YzJGamRHbHZiaTFwWkNJZ1BTQWlNVEF3TURBd01EYzFNREl6TmprM05TSTdDZ2tpY1hWaGJuUnBkSGtpSUQwZ0lqRWlPd29KSW05eWFXZHBibUZzTFhCMWNtTm9ZWE5sTFdSaGRHVXRiWE1pSUQwZ0lqRTJNRGN3T0RBMk5UZzVPVGdpT3dvSkluVnVhWEYxWlMxMlpXNWtiM0l0YVdSbGJuUnBabWxsY2lJZ1BTQWlOelJETkRWR1JUTXRSVFU0UVMwMFFqWTRMVGt5TXpRdE1UUXdPRVk1TmtNNU1qRTBJanNLQ1NKd2NtOWtkV04wTFdsa0lpQTlJQ0pqYjIwdWEzVm5iM1V1Wm1GdWVHbHVaMkZ3Y0hOMGIzSmxMbk5vYUhVeE1EUTRJanNLQ1NKcGRHVnRMV2xrSWlBOUlDSXhORFkyTnpVek5ESTNJanNLQ1NKaWFXUWlJRDBnSW1OdmJTNXJkV2R2ZFM1bVlXNTRhVzVuWVhCd2MzUnZjbVVpT3dvSkltbHpMV2x1TFdsdWRISnZMVzltWm1WeUxYQmxjbWx2WkNJZ1BTQWlabUZzYzJVaU93b0pJbkIxY21Ob1lYTmxMV1JoZEdVdGJYTWlJRDBnSWpFMk1EY3dPREEyTlRnNU9UZ2lPd29KSW5CMWNtTm9ZWE5sTFdSaGRHVWlJRDBnSWpJd01qQXRNVEl0TURRZ01URTZNVGM2TXpnZ1JYUmpMMGROVkNJN0Nna2lhWE10ZEhKcFlXd3RjR1Z5YVc5a0lpQTlJQ0ptWVd4elpTSTdDZ2tpY0hWeVkyaGhjMlV0WkdGMFpTMXdjM1FpSUQwZ0lqSXdNakF0TVRJdE1EUWdNRE02TVRjNk16Z2dRVzFsY21sallTOU1iM05mUVc1blpXeGxjeUk3Q2draWIzSnBaMmx1WVd3dGNIVnlZMmhoYzJVdFpHRjBaU0lnUFNBaU1qQXlNQzB4TWkwd05DQXhNVG94Tnpvek9DQkZkR012UjAxVUlqc0tmUT09IjsKCSJlbnZpcm9ubWVudCIgPSAiU2FuZGJveCI7CgkicG9kIiA9ICIxMDAiOwoJInNpZ25pbmctc3RhdHVzIiA9ICIwIjsKfQ==\",\"goodsId\":\"com.kugou.fanxingappstore.shhu1048\",\"fromType\":\"moblie7\",\"businessId\":\"1446219001364501362\"}");
            PurchaseForIosRequest request = new PurchaseForIosRequest();
            ConsumeParam consumeParam = new ConsumeParam();
            consumeParam.setFromKugouId(732115211);
            consumeParam.setToKugouId(490000052);
            request.setConsumeParam(consumeParam);
            request.setExt("");
            CoinCallbackDTO coinCallbackDTO = new CoinCallbackDTO();
            coinCallbackDTO.setExtend("************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
            coinCallbackDTO.setTrade_status(1);
            coinCallbackDTO.setOut_trade_no("2");
            when(postRechargeNsqProducer.sendPostRechargeMsg(anyString(), any(PurchaseMsgDTO.class))).thenReturn(true);
            Assert.assertTrue(this.afterRechargeService.sendPurchaseTopic(topic, targetOrder, coinCallbackDTO));
        }
    }

    @Test
    public void sendPurchaseTopicByRechargeAcrossPO() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            RechargeAcrossPO targetOrder = new RechargeAcrossPO();
            targetOrder.setRechargeOrderNum("R09202012S1TID1000000750236975");
            targetOrder.setStatus(1);
            targetOrder.setMoney(new BigDecimal("1048"));
            targetOrder.setAddTime(1607080679);
            targetOrder.setReType(1);
            targetOrder.setPayTypeId(1006);
            targetOrder.setRefer(1);
            targetOrder.setChannelId(2);
            targetOrder.setCFrom(3);
            targetOrder.setCoin(new BigDecimal("104800"));
            targetOrder.setExtend("********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
            targetOrder.setExtraJsonData("{\"signBook\":\"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\",\"goodsId\":\"com.kugou.fanxingappstore.shhu1048\",\"fromType\":\"moblie7\",\"businessId\":\"1446219001364501362\"}");
            when(postRechargeNsqProducer.sendPostRechargeMsg(anyString(), any(PurchaseMsgDTO.class))).thenReturn(true);
            Assert.assertTrue(this.afterRechargeService.sendPurchaseTopic(targetOrder));
        }
    }

    @Test
    public void tryParseExt() {
        JSONObject jsonObject = this.afterRechargeService.tryParseExt("not json");
        Assert.assertNotNull(jsonObject);
    }

    @SneakyThrows
    @Test
    public void pushSocketToRoom() {
        RechargeAcrossPO targetOrder = new RechargeAcrossPO();
        targetOrder.setRechargeOrderNum("R09202012S1TID1000000750236975");
        targetOrder.setStatus(1);
        targetOrder.setMoney(new BigDecimal("1048"));
        targetOrder.setAddTime(1607080679);
        targetOrder.setReType(1);
        targetOrder.setPayTypeId(1006);
        targetOrder.setRefer(1);
        targetOrder.setChannelId(2);
        targetOrder.setCFrom(3);
        targetOrder.setCoin(new BigDecimal("104800"));
        targetOrder.setCoinType(CoinTypeEnum.SING_COIN.getCoinType());
        targetOrder.setExtend("************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
        BoolResponse boolResponse = new BoolResponse();
        boolResponse.setRet(0);
        boolResponse.setData(true);
        when(appDispatchServiceV2.sendToUser(anyInt(), anyLong(), anyInt(), any(ByteBuffer.class), any(MsgOption.class))).thenReturn(boolResponse);
        this.afterRechargeService.pushSocketToRoom(targetOrder);
        verify(appDispatchServiceV2, times(1)).sendToUser(anyInt(), anyLong(), anyInt(), any(ByteBuffer.class), any(MsgOption.class));
    }

    @SneakyThrows
    @Test
    public void pushSocketToRoomException() {
        RechargeAcrossPO targetOrder = new RechargeAcrossPO();
        targetOrder.setRechargeOrderNum("R09202012S1TID1000000750236975");
        targetOrder.setStatus(1);
        targetOrder.setMoney(new BigDecimal("1048"));
        targetOrder.setAddTime(1607080679);
        targetOrder.setReType(1);
        targetOrder.setPayTypeId(1006);
        targetOrder.setRefer(1);
        targetOrder.setChannelId(2);
        targetOrder.setCFrom(3);
        targetOrder.setCoin(new BigDecimal("104800"));
        targetOrder.setCoinType(CoinTypeEnum.SING_COIN.getCoinType());
        targetOrder.setExtend("************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
        BoolResponse boolResponse = new BoolResponse();
        boolResponse.setRet(0);
        boolResponse.setData(true);
        when(appDispatchServiceV2.sendToUser(anyInt(), anyLong(), anyInt(), any(ByteBuffer.class), any(MsgOption.class))).thenThrow(new ContextedRuntimeException("test"));
        this.afterRechargeService.pushSocketToRoom(targetOrder);
        verify(appDispatchServiceV2, times(1)).sendToUser(anyInt(), anyLong(), anyInt(), any(ByteBuffer.class), any(MsgOption.class));
    }
}
