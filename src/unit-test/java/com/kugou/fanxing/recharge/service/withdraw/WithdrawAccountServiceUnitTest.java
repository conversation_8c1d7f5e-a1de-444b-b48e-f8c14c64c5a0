package com.kugou.fanxing.recharge.service.withdraw;

import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.biz.commons.util.SnowFlake;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawAccountDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawAccountWechatDao;
import com.kugou.fanxing.recharge.model.dto.CertificationDTO;
import com.kugou.fanxing.recharge.model.po.WithdrawAccountPO;
import com.kugou.fanxing.recharge.model.po.WithdrawAccountWechatPO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.common.DencryptService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.common.PlatformCertificationServiceImpl;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.withdraw.thrift.AccountChangeRequest;
import com.kugou.fanxing.recharge.withdraw.thrift.AccountChangeV2Request;
import com.kugou.fanxing.recharge.withdraw.thrift.WithdrawAccountDTO;
import com.kugou.fanxing.recharge.withdraw.thrift.WithdrawAccountWechatDTO;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class WithdrawAccountServiceUnitTest {

    @Mock
    private OrderIdService orderIdService;
    @Mock
    private PlatformCertificationServiceImpl platformCertificationService;
    @Mock
    private WithdrawAccountDao withdrawAccountDao;
    @Mock
    private WithdrawAccountWechatDao withdrawAccountWechatDao;
    @Mock
    private DencryptService dencryptService;
    @Mock
    private ApolloConfigService apolloConfigService;
    @InjectMocks
    private WithdrawAccountService withdrawAccountService;

    @Test
    public void bindAccountForAlipay() {
        when(orderIdService.generateGlobalId()).thenReturn(1L);
        when(dencryptService.encrypt(anyString())).thenReturn("xxx");
        when(dencryptService.fingerprint(anyString())).thenReturn("xxx");
        when(withdrawAccountDao.saveOrUpdate(any(WithdrawAccountPO.class))).thenReturn(1);
        Assert.assertEquals(SysResultCode.SUCCESS, withdrawAccountService.bindAccountForAlipay(buildAccountChangeV2Request()));
        when(withdrawAccountDao.saveOrUpdate(any(WithdrawAccountPO.class))).thenReturn(0);
        Assert.assertEquals(SysResultCode.WITHDRAW_ACCOUNT_BIND_FAIL, withdrawAccountService.bindAccountForAlipay(buildAccountChangeV2Request()));
    }

    @Test(expected = ContextedRuntimeException.class)
    public void bindAccountForAlipayException() {
        when(orderIdService.generateGlobalId()).thenReturn(2L);
        when(dencryptService.encrypt(anyString())).thenReturn("xxx");
        when(dencryptService.fingerprint(anyString())).thenReturn("xxx");
        when(withdrawAccountDao.saveOrUpdate(any(WithdrawAccountPO.class))).thenThrow(new ContextedRuntimeException("test"));
        withdrawAccountService.bindAccountForAlipay(buildAccountChangeV2Request());
    }

    @Test
    public void bindAccountForWechat() {
        when(orderIdService.generateGlobalId()).thenReturn(3L);
        when(dencryptService.encrypt(anyString())).thenReturn("xxx");
        when(dencryptService.fingerprint(anyString())).thenReturn("xxx");
        when(withdrawAccountWechatDao.saveOrUpdate(any(WithdrawAccountWechatPO.class))).thenReturn(1);
        when(apolloConfigService.getWxAppIdByBizAppId(anyInt())).thenReturn("xxx");
        Assert.assertEquals(SysResultCode.SUCCESS, withdrawAccountService.bindAccountForWechat(buildAccountChangeV2Request()));
        when(withdrawAccountWechatDao.saveOrUpdate(any(WithdrawAccountWechatPO.class))).thenReturn(0);
        Assert.assertEquals(SysResultCode.WITHDRAW_ACCOUNT_BIND_FAIL, withdrawAccountService.bindAccountForWechat(buildAccountChangeV2Request()));
    }

    @Test(expected = ContextedRuntimeException.class)
    public void bindAccountForWechatException() {
        when(orderIdService.generateGlobalId()).thenReturn(4L);
        when(dencryptService.encrypt(anyString())).thenReturn("xxx");
        when(dencryptService.fingerprint(anyString())).thenReturn("xxx");
        when(apolloConfigService.getWxAppIdByBizAppId(anyInt())).thenReturn("xxx");
        when(withdrawAccountWechatDao.saveOrUpdate(any(WithdrawAccountWechatPO.class))).thenThrow(new ContextedRuntimeException("test"));
        withdrawAccountService.bindAccountForWechat(buildAccountChangeV2Request());
    }

    @Test
    public void saveAccountInfoV2() {
        when(orderIdService.generateGlobalId()).thenReturn(5L);
        when(dencryptService.encrypt(anyString())).thenReturn("xxx");
        when(dencryptService.fingerprint(anyString())).thenReturn("xxx");
        when(withdrawAccountWechatDao.saveOrUpdate(any(WithdrawAccountWechatPO.class))).thenReturn(1);
        when(apolloConfigService.getWxAppIdByBizAppId(anyInt())).thenReturn("xxx");
        when(apolloConfigService.isInvalidOpenidPatterns(anyString())).thenReturn(false);
        AccountChangeV2Request request = buildAccountChangeV2Request().setOpenid("").setAccount("");
        Assert.assertEquals(SysResultCode.RECHARGE_PARAM_ERROR, withdrawAccountService.saveAccountInfoV2(request));
    }

    @Test
    public void saveAccountInfo() {
        when(orderIdService.generateGlobalId()).thenReturn(5L);
        when(dencryptService.encrypt(anyString())).thenReturn("xxx");
        when(dencryptService.fingerprint(anyString())).thenReturn("xxx");
        when(withdrawAccountDao.saveOrUpdate(any(WithdrawAccountPO.class))).thenReturn(0);
        when(apolloConfigService.getWxAppIdByBizAppId(anyInt())).thenReturn("xxx");
        when(apolloConfigService.isInvalidOpenidPatterns(anyString())).thenReturn(false);
        AccountChangeRequest request = buildAccountChangeRequest();
        Assert.assertEquals(SysResultCode.WITHDRAW_ACCOUNT_BIND_FAIL, withdrawAccountService.saveAccountInfo(request));
    }

    @Test
    public void saveAccountInfoSuccess() {
        when(orderIdService.generateGlobalId()).thenReturn(5L);
        when(dencryptService.encrypt(anyString())).thenReturn("xxx");
        when(dencryptService.fingerprint(anyString())).thenReturn("xxx");
        when(withdrawAccountDao.saveOrUpdate(any(WithdrawAccountPO.class))).thenReturn(1);
        when(apolloConfigService.getWxAppIdByBizAppId(anyInt())).thenReturn("xxx");
        AccountChangeRequest request = buildAccountChangeRequest();
        Assert.assertEquals(SysResultCode.SUCCESS, withdrawAccountService.saveAccountInfo(request));
    }

    @Test
    public void queryAccountInfoByKugouId() {
        WithdrawAccountPO withdrawAccountPO = new WithdrawAccountPO();
        withdrawAccountPO.setAccountEncrypted("test");
        withdrawAccountPO.setKugouId(1L);
        withdrawAccountPO.setRealName("xxx");
        withdrawAccountPO.setGlobalId(123L);
        withdrawAccountPO.setCreateTime(new Date());
        when(withdrawAccountDao.selectByKugouId(anyLong())).thenReturn(withdrawAccountPO);
        when(dencryptService.decrypt(anyString())).thenReturn("xxxx");
        Optional<WithdrawAccountDTO> optionalWithdrawAccountDTO = withdrawAccountService.queryAccountInfoByKugouId(1L);
        Assert.assertTrue(optionalWithdrawAccountDTO.isPresent());
    }

    @Test
    public void queryWechatAccountInfoByKugouId() {
        WithdrawAccountWechatPO withdrawAccountWechatPO = new WithdrawAccountWechatPO();
        withdrawAccountWechatPO.setKugouId(1L);
        withdrawAccountWechatPO.setOpenid("xx");
        withdrawAccountWechatPO.setNickname("test");
        withdrawAccountWechatPO.setAvatar("xxxx");
        withdrawAccountWechatPO.setWxAppId("tset");
        withdrawAccountWechatPO.setCreateTime(new Date());
        when(apolloConfigService.getWxAppIdByBizAppId(anyInt())).thenReturn("xxx");
        when(withdrawAccountWechatDao.selectByKugouId(anyLong(), anyString())).thenReturn(withdrawAccountWechatPO);
        Optional<WithdrawAccountWechatDTO> optionalWithdrawAccountWechatDTO = withdrawAccountService.queryWechatAccountInfoByKugouId(1L, 2);
        Assert.assertTrue(optionalWithdrawAccountWechatDTO.isPresent());
    }

    @Test
    public void checkAccountNameIsMatchRealName() {
        Optional<CertificationDTO> optionalCertificationDTO = Optional.of(new CertificationDTO().setStatus(2).setName("xxx"));
        when(platformCertificationService.getRealNameStatus(anyLong())).thenReturn(optionalCertificationDTO);
        Assert.assertTrue(this.withdrawAccountService.checkAccountNameIsMatchRealName(1L, "xxx"));
    }

    private AccountChangeRequest buildAccountChangeRequest() {
        AccountChangeRequest request = new AccountChangeRequest()
                .setOrderId(new SnowFlake(0, 0).nextId())
                .setAppId(1000_0002)
                .setKugouId(**********)
                .setAccount("***********")
                .setRealName("刘守凯")
                .setToken("TOKEN")
                .setRequestTime(DateHelper.getCurrentSeconds())
                .setIp("*********");
        request.setSign(FinanceSignUtils.makeSign(request, "49e6qxHDhmkfqDyU"));
        return request;
    }

    private AccountChangeV2Request buildAccountChangeV2Request() {
        AccountChangeV2Request request = new AccountChangeV2Request()
                .setOrderId(new SnowFlake(0, 0).nextId())
                .setAppId(1000_0002)
                .setBizAppId(10015)
                .setKugouId(**********)
                .setOpenid("osVWIjimHCDYGdgCuf_0apng8xCM")
                .setNickname("Kay")
                .setAvatar("https://s.gravatar.com/avatar/15672e33593d49818cfab30ed2b1f578?s=80")
                .setAccount("")
                .setRealName("test")
                .setToken("TOKEN")
                .setRequestTime(DateHelper.getCurrentSeconds())
                .setIp("127.0.0.1");
        request.setSign(FinanceSignUtils.makeSign(request, "49e6qxHDhmkfqDyU"));
        return request;
    }


}
