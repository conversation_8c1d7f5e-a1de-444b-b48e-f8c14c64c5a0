package com.kugou.fanxing.recharge.service.appstore;

import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeRenewalDao;
import com.kugou.fanxing.recharge.model.po.RechargeRenewalPO;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class RechargeAutoRenewServiceUnitTest {

    @Mock
    private RechargeRenewalDao rechargeRenewalDao;
    @InjectMocks
    private RechargeAutoRenewService rechargeAutoRenewService;

    @Test
    public void getUserRechargeRenewalListNormal() {
        when(rechargeRenewalDao.getLatestUserRenewal(anyLong(), anyInt(), anyLong())).thenReturn(new RechargeRenewalPO());
        Optional<RechargeRenewalPO> optionalRechargeRenewalPO = rechargeAutoRenewService.getLatestUserRenewal(1290249156, 3, 100004);
        Assert.assertTrue(optionalRechargeRenewalPO.isPresent());
    }

    @Test
    public void getUserRechargeRenewalListAbNormal() {
        when(rechargeRenewalDao.getLatestUserRenewal(anyLong(), anyInt(), anyLong())).thenReturn(null);
        Optional<RechargeRenewalPO> optionalRechargeRenewalPO = rechargeAutoRenewService.getLatestUserRenewal(1290249156, 3, 100004);
        Assert.assertFalse(optionalRechargeRenewalPO.isPresent());
    }

    @Test(expected = ContextedRuntimeException.class)
    public void getUserRechargeRenewalListException() {
        when(rechargeRenewalDao.getLatestUserRenewal(anyLong(), anyInt(), anyLong())).thenThrow(new ContextedRuntimeException("test"));
        rechargeAutoRenewService.getLatestUserRenewal(1290249156, 3, 100001);
    }
}
