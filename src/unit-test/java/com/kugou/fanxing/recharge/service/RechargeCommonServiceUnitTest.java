package com.kugou.fanxing.recharge.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.jayway.jsonpath.JsonPath;
import com.kugou.fanxing.biz.commons.util.SnowFlake;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.LocalCurrencyConfigDao;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.model.bo.AccountChangeTypeBO;
import com.kugou.fanxing.recharge.model.bo.CouponInfoBO;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.bo.KupayAppTypeInfoBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.AlipayRequest;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.model.vo.CountryInfoVo;
import com.kugou.fanxing.recharge.model.vo.LocalCurrencyConfig;
import com.kugou.fanxing.recharge.service.common.ConsumeRpcService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.thrift.callback.ConsumeParam;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import com.kugou.fanxing.recharge.util.IpUtils;
import com.kugou.fanxing.recharge.util.SpringContextUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class RechargeCommonServiceUnitTest {

    @Mock
    private HttpServletRequest request;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    private RechargeConfig rechargeConfig;
    @Mock
    private RemoteFamilyControlService remoteFamilyControlService;
    @Mock
    private RemoteStrategyService remoteStrategyService;
    @Mock
    private ApolloConfigService apolloConfigService;
    @Mock
    private ConsumeRpcService consumeRpcService;
    @Mock
    private LocalCurrencyConfigDao localCurrencyConfigDao;
    @Mock
    private UserFacadeService userFacadeService;
    @InjectMocks
    private RechargeCommonService rechargeCommonService;
    private WebCommonParam webCommonParam;

    @Before
    public void before() {
        this.webCommonParam = new WebCommonParam();
        webCommonParam.setKugouId(**********);
        when(rechargeConfig.getCallBackKey()).thenReturn("kugoufanxing2016");
    }

    @Test
    public void isForbiddenPayType() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class);
             MockedStatic<IpUtils> ipUtilsMockedStatic = Mockito.mockStatic(IpUtils.class);) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            ipUtilsMockedStatic.when(IpUtils::getClientIpAddress).thenReturn("**************");
            when(apolloConfigService.isForbiddenPayType(PayTypeIdEnum.PAY_TYPE_ID_3.getPayTypeId())).thenReturn(false);
            Assert.assertFalse(rechargeCommonService.isForbiddenPayType(PayTypeIdEnum.PAY_TYPE_ID_3));
        }
    }

    @Test
    public void checkThirdPart1() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class);
             MockedStatic<IpUtils> ipUtilsMockedStatic = Mockito.mockStatic(IpUtils.class);) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            ipUtilsMockedStatic.when(IpUtils::getClientIpAddress).thenReturn("**************");
            AlipayRequest alipayRequest = new AlipayRequest();
            alipayRequest.setAmount(BigDecimal.ONE);
            alipayRequest.setPayTypeIdEnum(PayTypeIdEnum.PAY_TYPE_ID_3);
            when(this.remoteFamilyControlService.checkFamilyControl(anyLong(), anyInt())).thenReturn(false);
            when(this.remoteStrategyService.strategyVerifyForGetOrder(anyLong(), anyString(), anyInt(), any(BigDecimal.class))).thenReturn(false);
            when(this.userFacadeService.getUserIdByKugouId(anyLong(),anyBoolean())).thenReturn(Optional.of(1L));
            SysResultCode sysResultCode = rechargeCommonService.checkThirdPart("R092020081310504988506492", this.webCommonParam, alipayRequest);
            Assert.assertEquals(SysResultCode.SUCCESS.getCode(), sysResultCode.getCode());
        }
    }

    @Test
    public void checkThirdPart2() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class);
             MockedStatic<IpUtils> ipUtilsMockedStatic = Mockito.mockStatic(IpUtils.class);) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            ipUtilsMockedStatic.when(IpUtils::getClientIpAddress).thenReturn("**************");
            when(this.remoteFamilyControlService.checkFamilyControl(anyLong(), anyInt())).thenReturn(false);
            when(this.remoteStrategyService.strategyVerifyForGetOrder(anyLong(), anyString(), anyInt(), any(BigDecimal.class))).thenReturn(false);
            when(this.userFacadeService.getUserIdByKugouId(anyLong(),anyBoolean())).thenReturn(Optional.of(1L));
            SysResultCode sysResultCode = rechargeCommonService.checkThirdPart(1, 2, "R092020081310504988506492", 1, BigDecimal.ONE);
            Assert.assertEquals(SysResultCode.SUCCESS.getCode(), sysResultCode.getCode());
        }
    }

    @Test
    @SneakyThrows
    public void checkThirdPart3() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class);
             MockedStatic<IpUtils> ipUtilsMockedStatic = Mockito.mockStatic(IpUtils.class)) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            ipUtilsMockedStatic.when(IpUtils::getClientIpAddress).thenReturn("**************");
            when(this.remoteFamilyControlService.checkFamilyControl(anyLong(), anyInt())).thenReturn(true).thenReturn(false).thenReturn(false);
            when(this.remoteStrategyService.strategyVerifyForGetOrder(anyLong(), anyString(), anyInt(), any(BigDecimal.class))).thenReturn(true).thenReturn(false);
            when(this.userFacadeService.getUserIdByKugouId(anyLong(),anyBoolean())).thenReturn(Optional.of(1L));
            SysResultCode sysResultCode = rechargeCommonService.checkThirdPart(1, 2, "R092020081310504988506492", 1, BigDecimal.ONE);
            Assert.assertEquals(SysResultCode.RECHARGE_FAMILY_CONTROL.getCode(), sysResultCode.getCode());
            sysResultCode = rechargeCommonService.checkThirdPart(1, 2, "R092020081310504988506492", 1, BigDecimal.ONE);
            Assert.assertEquals(SysResultCode.RECHARGE_RISK_STRATEGY.getCode(), sysResultCode.getCode());
            sysResultCode = rechargeCommonService.checkThirdPart(1, 2, "R092020081310504988506492", 1, BigDecimal.ONE);
            Assert.assertEquals(SysResultCode.SUCCESS.getCode(), sysResultCode.getCode());
            when(userFacadeService.getUserIdByKugouId(anyLong(), anyBoolean())).thenReturn(Optional.of(1L));
            sysResultCode = rechargeCommonService.checkThirdPart(1, 2, "R092020081310504988506492", 1, BigDecimal.ONE);
            Assert.assertEquals(SysResultCode.SUCCESS.getCode(), sysResultCode.getCode());
            when(userFacadeService.getUserIdByKugouId(anyLong(), anyBoolean())).thenReturn(Optional.empty());
            sysResultCode = rechargeCommonService.checkThirdPart(1, 2, "R092020081310504988506492", 1, BigDecimal.ONE);
            Assert.assertEquals(SysResultCode.RECHARGE_NOT_FANXING.getCode(), sysResultCode.getCode());
            when(userFacadeService.getUserIdByKugouId(anyLong(), anyBoolean())).thenReturn(Optional.of(1L));
            when(apolloConfigService.isKuwoEnv()).thenReturn(true);
            sysResultCode = rechargeCommonService.checkThirdPart(1, 2, "R092020081310504988506492", 1, BigDecimal.ONE);
            Assert.assertEquals(SysResultCode.RECHARGE_NOT_KUWO_ID.getCode(), sysResultCode.getCode());
        }
    }

    @Test
    public void buildExtendStr() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class);
             MockedStatic<IpUtils> ipUtilsMockedStatic = Mockito.mockStatic(IpUtils.class);) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            ipUtilsMockedStatic.when(IpUtils::getClientIpAddress).thenReturn("**************");
            when(rechargeConfig.getCallBackKey()).thenReturn("kugoufanxing2016");
            RechargeAcrossPO rechargeAcrossPO = buildRechargeAcrossPO();
            String extend = rechargeCommonService.buildExtendStr(rechargeAcrossPO, Maps.newHashMap());
            String json = new String(Base64.decodeBase64(extend));
            BigDecimal amount = JsonPath.parse(json).read("$.callBackArg.amount", BigDecimal.class);
            Assert.assertNotNull(amount);
            String callbackSign = JsonPath.parse(json).read("$.callBackSign", String.class);
            Assert.assertEquals("44d9a0354923537e71a61f95b1dab5ea", callbackSign);
        }
    }

    @Test
    public void buildRechargeAcross() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class);
             MockedStatic<IpUtils> ipUtilsMockedStatic = Mockito.mockStatic(IpUtils.class);) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            ipUtilsMockedStatic.when(IpUtils::getClientIpAddress).thenReturn("**************");
            WebCommonParam webCommonParam = new WebCommonParam();
            webCommonParam.setPid(39);
            long kugouId = 1554749117L;
            String rechargeOrderNum = "R092020011014580532783488";
            when(rechargeConfig.getDataCenterZoneId()).thenReturn(2);
            when(orderIdService.generateGlobalId()).thenReturn(new SnowFlake(0, 0).nextId());
            RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(webCommonParam.getPid(),
                    kugouId, rechargeOrderNum, PayTypeIdEnum.PAY_TYPE_ID_41, BigDecimal.valueOf(1.5), IpUtils.getServerIpAddress());
            Assert.assertEquals(BigDecimal.valueOf(1.5), rechargeAcrossPO.getAmount());
            Assert.assertEquals(BigDecimal.valueOf(1.5), rechargeAcrossPO.getMoney());
            Assert.assertEquals(0, rechargeAcrossPO.getCouponOrderId());
            Assert.assertEquals(BigDecimal.ZERO, rechargeAcrossPO.getCoupon());
        }
    }

    public void buildAgentRechargeAcross() {
        WebCommonParam webCommonParam = new WebCommonParam();
        webCommonParam.setPid(39);
        long kugouId = 1554749117L;
        long rechargeKugouId = 697229863L;
        String rechargeOrderNum = "R092020011014580532783488";
        when(rechargeConfig.getDataCenterZoneId()).thenReturn(2);
        when(orderIdService.generateGlobalId()).thenReturn(new SnowFlake(0, 0).nextId());
        RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(webCommonParam.getPid(),
                kugouId, rechargeKugouId, rechargeOrderNum, PayTypeIdEnum.PAY_TYPE_ID_41, BigDecimal.valueOf(1.5), IpUtils.getServerIpAddress());

        Assert.assertEquals(BigDecimal.valueOf(1.5), rechargeAcrossPO.getAmount());
        Assert.assertEquals(BigDecimal.valueOf(1.5), rechargeAcrossPO.getMoney());
        Assert.assertEquals(0, rechargeAcrossPO.getCouponOrderId());
        Assert.assertEquals(BigDecimal.ZERO, rechargeAcrossPO.getCoupon());

        // 验证代充相关参数设置
        Assert.assertEquals(rechargeKugouId, rechargeAcrossPO.getKugouId());
        Assert.assertEquals(rechargeKugouId, rechargeAcrossPO.getFromKugouId());
        Assert.assertEquals(kugouId, rechargeAcrossPO.getAgentKugouId());
    }


    @Test
    public void buildRechargeAcross_chanelId_0_3() {
        WebCommonParam webCommonParam = new WebCommonParam();
        webCommonParam.setPid(39);
        long kugouId = 1554749117L;
        String rechargeOrderNum = "R092020011014580532783488";
        when(rechargeConfig.getDataCenterZoneId()).thenReturn(2);
        when(orderIdService.generateGlobalId()).thenReturn(new SnowFlake(0, 0).nextId());
        RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(webCommonParam.getPid(),
                kugouId, rechargeOrderNum, PayTypeIdEnum.PAY_TYPE_ID_3, BigDecimal.valueOf(1.5), IpUtils.getServerIpAddress());
        Assert.assertEquals(1, rechargeAcrossPO.getChannelId());
    }

    @Test
    public void buildRechargeAcross_chanelId_0_39() {
        WebCommonParam webCommonParam = new WebCommonParam();
        webCommonParam.setPid(39);
        long kugouId = 1554749117L;
        String rechargeOrderNum = "R092020011014580532783488";
        when(rechargeConfig.getDataCenterZoneId()).thenReturn(2);
        when(orderIdService.generateGlobalId()).thenReturn(new SnowFlake(0, 0).nextId());
        RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(webCommonParam.getPid(),
                kugouId, rechargeOrderNum, PayTypeIdEnum.PAY_TYPE_ID_39, BigDecimal.valueOf(1.5), IpUtils.getServerIpAddress());
        Assert.assertEquals(1, rechargeAcrossPO.getChannelId());
    }

    @Test
    public void buildRechargeAcross_chanelId_0_1() {
        WebCommonParam webCommonParam = new WebCommonParam();
        webCommonParam.setPid(39);
        long kugouId = 1554749117L;
        String rechargeOrderNum = "R092020011014580532783488";
        when(rechargeConfig.getDataCenterZoneId()).thenReturn(2);
        when(orderIdService.generateGlobalId()).thenReturn(new SnowFlake(0, 0).nextId());
        RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(webCommonParam.getPid(),
                kugouId, rechargeOrderNum, PayTypeIdEnum.PAY_TYPE_ID_3, BigDecimal.valueOf(1.5), IpUtils.getServerIpAddress());
        Assert.assertEquals(1, rechargeAcrossPO.getChannelId());
    }

    @Test
    public void buildRechargeAcrossSupplier() {
        WebCommonParam webCommonParam = new WebCommonParam();
        webCommonParam.setPid(39);
        long kugouId = 1554749117L;
        String rechargeOrderNum = "R092020011014580532783488";
        when(rechargeConfig.getDataCenterZoneId()).thenReturn(2);
        when(orderIdService.generateGlobalId()).thenReturn(new SnowFlake(0, 0).nextId());
        RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(webCommonParam.getPid(),
                kugouId, rechargeOrderNum, PayTypeIdEnum.PAY_TYPE_ID_41, BigDecimal.valueOf(1.5), IpUtils.getServerIpAddress(), Optional.empty());
        Assert.assertEquals(BigDecimal.valueOf(1.5), rechargeAcrossPO.getAmount());
        Assert.assertEquals(BigDecimal.valueOf(1.5), rechargeAcrossPO.getMoney());
        Assert.assertEquals(0, rechargeAcrossPO.getCouponOrderId());
        Assert.assertEquals(BigDecimal.ZERO, rechargeAcrossPO.getCoupon());
    }

    @Test
    public void buildAgentRechargeAcrossSupplier() {
        WebCommonParam webCommonParam = new WebCommonParam();
        webCommonParam.setPid(39);
        long kugouId = 1554749117L;
        long rechargeKugouId = 697229863L;
        String rechargeOrderNum = "R092020011014580532783488";
        when(rechargeConfig.getDataCenterZoneId()).thenReturn(2);
        when(orderIdService.generateGlobalId()).thenReturn(new SnowFlake(0, 0).nextId());
        RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(webCommonParam.getPid(),
                kugouId, rechargeKugouId, rechargeOrderNum, PayTypeIdEnum.PAY_TYPE_ID_41, BigDecimal.valueOf(1.5), IpUtils.getServerIpAddress(), Optional.empty());
        Assert.assertEquals(BigDecimal.valueOf(1.5), rechargeAcrossPO.getAmount());
        Assert.assertEquals(BigDecimal.valueOf(1.5), rechargeAcrossPO.getMoney());
        Assert.assertEquals(0, rechargeAcrossPO.getCouponOrderId());
        Assert.assertEquals(BigDecimal.ZERO, rechargeAcrossPO.getCoupon());

        // 验证代充相关参数设置
        Assert.assertEquals(rechargeKugouId, rechargeAcrossPO.getKugouId());
        Assert.assertEquals(rechargeKugouId, rechargeAcrossPO.getFromKugouId());
        Assert.assertEquals(kugouId, rechargeAcrossPO.getAgentKugouId());
    }

    @Test
    public void buildRechargeAcrossWithCoupon() {
        WebCommonParam webCommonParam = new WebCommonParam();
        webCommonParam.setPid(39);
        long kugouId = 1554749117L;
        long orderId = new SnowFlake(1, 1).nextId();
        long couponId = new SnowFlake(1, 2).nextId();
        String rechargeOrderNum = "R092020011014580532783488";
        when(rechargeConfig.getDataCenterZoneId()).thenReturn(2);
        when(orderIdService.generateGlobalId()).thenReturn(new SnowFlake(0, 0).nextId());
        CouponInfoBO couponInfoBO = new CouponInfoBO()
                .setOrderId(orderId)
                .setCouponId(couponId)
                .setValue(BigDecimal.valueOf(0.2));
        Optional<CouponInfoBO> optionalCouponInfoBO = Optional.of(couponInfoBO);
        RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(webCommonParam.getPid(),
                kugouId, 0, rechargeOrderNum, PayTypeIdEnum.PAY_TYPE_ID_41, BigDecimal.valueOf(1.5),
                IpUtils.getServerIpAddress(), CoinTypeEnum.STAR_COIN, optionalCouponInfoBO);
        Assert.assertEquals(BigDecimal.valueOf(1.5), rechargeAcrossPO.getAmount());
        Assert.assertEquals(BigDecimal.valueOf(1.3), rechargeAcrossPO.getMoney());
        Assert.assertEquals(orderId, rechargeAcrossPO.getCouponOrderId());
        Assert.assertEquals(couponId, rechargeAcrossPO.getCouponId());
    }

    @Test
    public void buildRechargeAcrossWithCouponSupplier() {
        WebCommonParam webCommonParam = new WebCommonParam();
        webCommonParam.setPid(39);
        long kugouId = 1554749117L;
        long orderId = new SnowFlake(1, 1).nextId();
        long couponId = new SnowFlake(1, 2).nextId();
        String rechargeOrderNum = "R092020011014580532783488";
        when(rechargeConfig.getDataCenterZoneId()).thenReturn(2);
        when(orderIdService.generateGlobalId()).thenReturn(new SnowFlake(0, 0).nextId());
        CouponInfoBO couponInfoBO = new CouponInfoBO()
                .setOrderId(orderId)
                .setCouponId(couponId)
                .setValue(BigDecimal.valueOf(0.2));
        RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(webCommonParam.getPid(),
                kugouId, rechargeOrderNum, PayTypeIdEnum.PAY_TYPE_ID_41, BigDecimal.valueOf(1.5), IpUtils.getServerIpAddress(), Optional.of(couponInfoBO));
        Assert.assertEquals(BigDecimal.valueOf(1.5), rechargeAcrossPO.getAmount());
        Assert.assertEquals(BigDecimal.valueOf(1.3), rechargeAcrossPO.getMoney());
        Assert.assertEquals(orderId, rechargeAcrossPO.getCouponOrderId());
        Assert.assertEquals(couponId, rechargeAcrossPO.getCouponId());
    }

    @Test
    public void createCallbackSign() {
        when(rechargeConfig.getCallBackKey()).thenReturn("kugoufanxing2016");
        String callbackSign = rechargeCommonService.createCallbackSign("R092020011014580532783488", 1554749117L);
        log.warn("callbackSign: {}", callbackSign);
        Assert.assertEquals("44d9a0354923537e71a61f95b1dab5ea", callbackSign);
    }

    private RechargeAcrossPO buildRechargeAcrossPO() {
        long kugouId = 1554749117L;
        int addTime = DateHelper.getCurrentSeconds();
        BigDecimal money = BigDecimal.valueOf(1.5);
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setRechargeOrderNum("R092020011014580532783488");
        rechargeAcrossPO.setAddTime(addTime);
        rechargeAcrossPO.setKugouId(kugouId);
        rechargeAcrossPO.setAgentKugouId(0);
        rechargeAcrossPO.setFromKugouId(kugouId);
        rechargeAcrossPO.setCoinBefore(BigDecimal.ZERO);
        rechargeAcrossPO.setCoin(BigDecimal.ZERO);
        rechargeAcrossPO.setCoinAfter(BigDecimal.ZERO);
        rechargeAcrossPO.setAmount(money);
        rechargeAcrossPO.setMoney(money);
        rechargeAcrossPO.setCoupon(BigDecimal.ZERO);
        rechargeAcrossPO.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_42.getPayTypeId());
        rechargeAcrossPO.setStatus(0);
        rechargeAcrossPO.setRefer(0);
        rechargeAcrossPO.setCFrom(39);
        rechargeAcrossPO.setChannelId(0);
        rechargeAcrossPO.setServerRoom(rechargeConfig.getDataCenterZoneId());
        rechargeAcrossPO.setRealAmount(BigDecimal.ZERO);
        return rechargeAcrossPO;
    }

    @Test
    public void getRechargeRebateVersion() {
        when(request.getParameter(anyString())).thenReturn("0");
        int rebate = this.rechargeCommonService.getRechargeRebateVersion(request);
        Assert.assertEquals(0, rebate);
        when(request.getParameter(anyString())).thenReturn("1");
        rebate = this.rechargeCommonService.getRechargeRebateVersion(request);
        Assert.assertEquals(1, rebate);
        when(request.getParameter(anyString())).thenReturn("NotNumber");
        rebate = this.rechargeCommonService.getRechargeRebateVersion(request);
        Assert.assertEquals(0, rebate);
    }

    @Test(expected = ContextedRuntimeException.class)
    public void createRechargeResultForMobileByPostNotConfig() {
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_35.getPayTypeId());
        rechargeAcrossPO.setCFrom(300);
        WebCommonParam webCommonParam = new WebCommonParam();
        Map<String, String> extParam = Maps.newHashMap();
        extParam.put("apptype", "share");
        Map<String, Object> callBackArgMap = Maps.newHashMap();
        KupayAppInfoBO kupayAppInfoBO = new KupayAppInfoBO();
        kupayAppInfoBO.setKupayAppId(1084);
        when(this.rechargeConfig.getKupayAppTypeInfoBO(anyInt())).thenReturn(Optional.empty());
        this.rechargeCommonService.createRechargeResultForMobileByPost(rechargeAcrossPO, extParam, callBackArgMap);
    }

    @Test(expected = AckException.class)
    public void createRechargeResultForMobileByPost() {
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_35.getPayTypeId());
        rechargeAcrossPO.setCFrom(300);
        rechargeAcrossPO.setMoney(BigDecimal.valueOf(100));
        rechargeAcrossPO.setAmount(BigDecimal.valueOf(100));
        rechargeAcrossPO.setCoupon(BigDecimal.valueOf(0));
        rechargeAcrossPO.setCoin(BigDecimal.ONE);
        WebCommonParam webCommonParam = new WebCommonParam();
        Map<String, String> extParam = Maps.newHashMap();
        extParam.put("apptype", "share");
        Map<String, Object> callBackArgMap = Maps.newHashMap();
        KupayAppInfoBO kupayAppInfoBO = new KupayAppInfoBO();
        kupayAppInfoBO.setKupayAppId(1084);
        KupayAppTypeInfoBO kupayAppTypeInfoBO = new KupayAppTypeInfoBO();
        kupayAppTypeInfoBO.setKupayAppId(10023);
        when(this.rechargeConfig.getKupayAppTypeInfoBO(anyInt())).thenReturn(Optional.of(kupayAppTypeInfoBO));
        KupayAppInfoBO kupayAppInfoBO2 = new KupayAppInfoBO();
        kupayAppInfoBO2.setKupayAppId(10023);
        when(rechargeConfig.getKupayAppIdByAppId(anyInt())).thenReturn(kupayAppInfoBO2);
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class);
             MockedStatic<IpUtils> ipUtilsMockedStatic = Mockito.mockStatic(IpUtils.class);
             MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            ipUtilsMockedStatic.when(IpUtils::getClientIpAddress).thenReturn("**************");
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doPostJSON(anyString(), anyMap(), anyMap())).thenReturn(Optional.empty());
            when(this.rechargeConfig.getActionUrlPrefix(any(PayTypeIdEnum.class))).thenReturn("http://kupay.kugou.com/v1/wxapppay?");
            Map<String, Object> paramsMap = this.rechargeCommonService.createRechargeResultForMobileByPost(rechargeAcrossPO, extParam, callBackArgMap);
            Assert.assertTrue(paramsMap.isEmpty());
        }
    }

    @Test(expected = ContextedRuntimeException.class)
    public void createRechargeResultAlipayH5() {
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setClientIp("************");
        rechargeAcrossPO.setPayTypeId(3);
        Map<String, String> kupayParam = Maps.newHashMap();
        kupayParam.put("sync_url", "https://fanxing.kugou.com/");
        Map<String, Object> extendParam = Maps.newHashMap();
        extendParam.put("businessExt", StringUtils.defaultString(""));
        this.rechargeCommonService.createRechargeResultAlipayH5(rechargeAcrossPO, kupayParam, extendParam, Optional.empty());
    }

    @Test
    public void createCallBackArgForIosPurchase() {
        RechargeAcrossPO rechargeAcrossPO = buildRechargeAcrossPO();
        rechargeAcrossPO.setAmount(new BigDecimal("0.01"));
        rechargeAcrossPO.setExtraJsonData("{\"signBook\":\"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\",\"fromType\":\"moblie7\",\"goodsId\":\"com.kugou.fanxingappstore.smallguard12\",\"businessId\":\"1504070309043584785\"}");
        ConsumeParam consumeParam = new ConsumeParam();
        String businessExt = "{\"days\":30,\"subStatus\":0}";
        String clientIp = "127.0.0.1";
        when(apolloConfigService.getAccountChangeTypeById(anyInt())).thenReturn(new AccountChangeTypeBO().setActionId(110014));
        when(this.consumeRpcService.buildIosPurchaseUserFundPlatParam(rechargeAcrossPO, consumeParam, clientIp)).thenReturn(Maps.newHashMap());
        Map<String, Object> callbackArg = this.rechargeCommonService.createCallBackArgForIosPurchase(rechargeAcrossPO, consumeParam, businessExt, clientIp);
        Assert.assertEquals("1504070309043584785", String.valueOf(callbackArg.get("businessId")));
        Assert.assertEquals(businessExt, String.valueOf(callbackArg.get("businessExt")));
        Assert.assertEquals("0.01", String.valueOf(callbackArg.get("amount")));
    }

    @Test
    public void buildExtendStrForIosPurchase() {
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setRechargeOrderNum("R09202105S1TID1000000811847320");
        rechargeAcrossPO.setKugouId(**********);
        rechargeAcrossPO.setAmount(BigDecimal.valueOf(12));
        rechargeAcrossPO.setExtraJsonData("{\"signBook\":\"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\",\"fromType\":\"moblie7\",\"goodsId\":\"com.kugou.fanxingappstore.smallguard12\",\"businessId\":\"1504070309043584785\"}");
        ConsumeParam consumeParam = new ConsumeParam();
        String businessExt = "{\"days\":30,\"subStatus\":0}";
        String clientIp = "127.0.0.1";
        when(apolloConfigService.getAccountChangeTypeById(anyInt())).thenReturn(new AccountChangeTypeBO().setActionId(110014));
        when(this.consumeRpcService.buildIosPurchaseUserFundPlatParam(rechargeAcrossPO, consumeParam, clientIp)).thenReturn(Maps.newHashMap());
        String extend = this.rechargeCommonService.buildExtendStrForIosPurchase(rechargeAcrossPO, consumeParam, businessExt, clientIp);
        Assert.assertNotNull(extend);
    }

    @Test
    public void createWxRechargeResultTest() {
        RechargeAcrossPO rechargeAcrossPO = buildRechargeAcrossPO();
        String businessExt = "{\"days\":30,\"subStatus\":0}";
        String clientIp = "127.0.0.1";
        Map<String, String> extParam = Maps.newHashMap();
        extParam.put("apptype", "share");
        KupayAppTypeInfoBO kupayAppTypeInfoBO = new KupayAppTypeInfoBO();
        kupayAppTypeInfoBO.setKupayAppId(1);
        when(rechargeConfig.getKupayAppTypeInfoBO(anyInt())).thenReturn(Optional.ofNullable(kupayAppTypeInfoBO));
        KupayAppInfoBO kupayAppInfoPO = new KupayAppInfoBO();
        kupayAppInfoPO.setKupayAppKey("123");
        kupayAppInfoPO.setKupayAppId(1);
        when(rechargeConfig.getKupayAppIdByPayType(any())).thenReturn(kupayAppInfoPO);
        when(rechargeConfig.getActionUrlPrefix(any())).thenReturn("http://kupay.kugou.com");
        when(rechargeConfig.getKupayAppIdByAppId(anyInt())).thenReturn(kupayAppInfoPO);
        Map<String, Object> extendParam = Maps.newHashMap();
        try {
            Map<String, Object> result = rechargeCommonService.createWxRechargeResult(rechargeAcrossPO, clientIp, extParam, true, extendParam);
        } catch (Exception e) {
            Assert.assertEquals("生成订单出错", e.getMessage());
        }
    }

    @Test
    public void getOverseasCountryList() {
        String json = "[{\"country\":\"TW\",\"displayCountry\":\"中国台湾\",\"allowPaymentMethod\":[]},{\"country\":\"HK\",\"displayCountry\":\"中国香港\",\"allowPaymentMethod\":[]},{\"country\":\"MO\",\"displayCountry\":\"中国澳门\",\"allowPaymentMethod\":[]},{\"country\":\"TH\",\"displayCountry\":\"泰国\",\"allowPaymentMethod\":[]},{\"country\":\"ID\",\"displayCountry\":\"印度尼西亚\",\"allowPaymentMethod\":[]},{\"country\":\"SG\",\"displayCountry\":\"新加坡\",\"allowPaymentMethod\":[]},{\"country\":\"MY\",\"displayCountry\":\"马来西亚\",\"allowPaymentMethod\":[]},{\"country\":\"MM\",\"displayCountry\":\"缅甸\",\"allowPaymentMethod\":[]}]";
        List<CountryInfoVo> countryInfoVos = JSON.parseArray(json, CountryInfoVo.class);
        when(this.apolloConfigService.getOverseasCountryList()).thenReturn(countryInfoVos);
        Lists.newArrayList("TW","HK","MO","TH","ID","MM","SG","MY").forEach(s -> {
            Optional<String> optionalCountryCode = rechargeCommonService.parseCountryCode("overseas.currencyConfig.country-" + s);
            Assert.assertTrue("无法解析的国家/区域代码" + s, optionalCountryCode.isPresent() && s.equals(optionalCountryCode.get()));
        });
        Lists.newArrayList("US","CN","UNKNOW").forEach(s -> {
            Optional<String> optionalCountryCode = rechargeCommonService.parseCountryCode("overseas.currencyConfig.country-" + s);
            Assert.assertFalse("无法解析的国家/区域代码" + s, optionalCountryCode.isPresent() && s.equals(optionalCountryCode.get()));
        });
    }

    @Test
    public void isValidCountryCode() {
        String json = "[{\"country\":\"TW\",\"displayCountry\":\"中国台湾\"},{\"country\":\"HK\",\"displayCountry\":\"中国香港\"},{\"country\":\"MO\",\"displayCountry\":\"中国澳门\"},{\"country\":\"TH\",\"displayCountry\":\"泰国\"},{\"country\":\"ID\",\"displayCountry\":\"印度尼西亚\"},{\"country\":\"SG\",\"displayCountry\":\"新加坡\"},{\"country\":\"MY\",\"displayCountry\":\"马来西亚\"},{\"country\":\"MM\",\"displayCountry\":\"缅甸\"}]";
        when(apolloConfigService.getOverseasCountryList()).thenReturn(JSON.parseArray(json, CountryInfoVo.class));
        Assert.assertTrue(this.rechargeCommonService.isValidCountryCode("HK"));
        Assert.assertTrue(this.rechargeCommonService.isValidCountryCode("MO"));
        Assert.assertTrue(this.rechargeCommonService.isValidCountryCode("TW"));
        Assert.assertTrue(this.rechargeCommonService.isValidCountryCode("TH"));
        Assert.assertTrue(this.rechargeCommonService.isValidCountryCode("SG"));
        Assert.assertTrue(this.rechargeCommonService.isValidCountryCode("MY"));
        Assert.assertTrue(this.rechargeCommonService.isValidCountryCode("MM"));
        Assert.assertTrue(this.rechargeCommonService.isValidCountryCode("ID"));
    }

    @Test
    public void generateLocalCurrencyList() {
        String json = "[{\"currency\":\"TWD\",\"currencyAmount\":\"50\",\"local2usdRates\":\"0.033463\",\"usd2cnyRates\":\"6.7194\",\"volatilityCoefficient\":\"1\"},{\"currency\":\"TWD\",\"currencyAmount\":\"150\",\"local2usdRates\":\"0.033463\",\"usd2cnyRates\":\"6.7194\",\"volatilityCoefficient\":\"1\"},{\"currency\":\"TWD\",\"currencyAmount\":\"300\",\"local2usdRates\":\"0.033463\",\"usd2cnyRates\":\"6.7194\",\"volatilityCoefficient\":\"1\"},{\"currency\":\"TWD\",\"currencyAmount\":\"1000\",\"local2usdRates\":\"0.033463\",\"usd2cnyRates\":\"6.7194\",\"volatilityCoefficient\":\"1\"},{\"currency\":\"TWD\",\"currencyAmount\":\"5000\",\"local2usdRates\":\"0.033463\",\"usd2cnyRates\":\"6.7194\",\"volatilityCoefficient\":\"1\"}]";
        List<LocalCurrencyConfig> currencyConfigList = JSON.parseArray(json, LocalCurrencyConfig.class);
        when(this.apolloConfigService.getOverseasCurrencyConfigByCountry(any())).thenReturn(currencyConfigList);
        when(orderIdService.generateGlobalId()).thenReturn(123L);
        when(localCurrencyConfigDao.batchInsert(anyList())).thenReturn(12);
        CountryInfoVo countryInfoVo = CountryInfoVo.builder().country("TW").displayCountry("中国台湾").allowPaymentMethod(Lists.newArrayList()).build();
        Assert.assertEquals(12, rechargeCommonService.generateLocalCurrencyList(countryInfoVo));
    }

    @Test
    public void isForbiddenStdPlats() {
        when(this.apolloConfigService.isForbiddenStdPlat(anyInt())).thenReturn(true).thenReturn(false);
        Assert.assertTrue(rechargeCommonService.isForbiddenStdPlat(7));
        Assert.assertFalse(rechargeCommonService.isForbiddenStdPlat(7));
    }

    @Test
    public void isForbiddenAppId() {
        when(this.apolloConfigService.isForbiddenAppId(anyInt())).thenReturn(true).thenReturn(false);
        Assert.assertTrue(rechargeCommonService.isForbiddenAppId(7));
        Assert.assertFalse(rechargeCommonService.isForbiddenAppId(7));
    }

    @Test
    public void getSubject() {
        String expected = "星币充值服务";
        Assert.assertEquals(expected, this.rechargeCommonService.getSubject(0));
        Assert.assertEquals(expected, this.rechargeCommonService.getSubject(1));
        Assert.assertEquals("唱币充值服务", this.rechargeCommonService.getSubject(2));
        Assert.assertEquals(expected, this.rechargeCommonService.getSubject(3));
    }
}
