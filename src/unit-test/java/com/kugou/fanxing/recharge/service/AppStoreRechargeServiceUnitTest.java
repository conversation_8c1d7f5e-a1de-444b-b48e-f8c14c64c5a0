package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.model.request.KugouOpenDispatchParam;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.model.vo.AppStoreProductVO;
import com.kugou.fanxing.recharge.service.appstore.AppStoreReceiptService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AppStoreRechargeServiceUnitTest {
    @InjectMocks
    private AppStoreRechargeService appStoreRechargeService;
    @Mock
    private ApolloConfigService apolloConfigService;

    @Mock
    private AppStoreReceiptService appStoreReceiptService;
    @Test
    public void getAppStoreProductList() {
        when(apolloConfigService.getAppStoreProductList(anyString(), anyInt())).thenReturn("[\n" +
                "    {\n" +
                "        \"productId\": \"com.fanxing.fxappstore.66coin\",\n" +
                "        \"price\": \"1\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"productId\": \"com.fanxing.fxappstore.800coin\",\n" +
                "        \"price\": \"12\"\n" +
                "    }\n" +
                "]");
        List<AppStoreProductVO> appStoreProductVOList = appStoreRechargeService.getProductList("10145", 307);
        Assert.assertEquals(2, appStoreProductVOList.size());
    }

    @Test
    public void finishOrderTest() {
        WebCommonParam webCommonParam=new WebCommonParam();
        KugouOpenDispatchParam kugouOpenDispatchParam = new KugouOpenDispatchParam();
        kugouOpenDispatchParam.setBusiness_data("{\"std_imei\":\"28F5F70A-FCE5-4E34-BD15-C2049C2DAA4B\",\"rechargeOrderNum\":\"R092021100818005604483634\",\"productId\":\"20006\",\"std_plat\":307,\"std_dev\":\"28F5F70A-FCE5-4E34-BD15-C2049C2DAA4B\",\"version\":100,\"sysVersion\":\"iOS 14.3\",\"receiptData\":\"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\"}");
        webCommonParam.setKugouOpenDispatchParam(kugouOpenDispatchParam);
        when(appStoreReceiptService.isSandboxReceipt(anyString())).thenReturn(true);
        when(apolloConfigService.permitSandboxVersion(anyString())).thenReturn(false);
        SysResultCode sysResultCode = appStoreRechargeService.finishOrder(webCommonParam);
        Assert.assertEquals(50000006,sysResultCode.getCode());
    }
}
