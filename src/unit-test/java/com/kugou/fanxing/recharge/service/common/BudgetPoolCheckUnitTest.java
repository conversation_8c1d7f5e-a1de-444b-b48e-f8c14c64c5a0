package com.kugou.fanxing.recharge.service.common;

import com.ctrip.framework.apollo.Config;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class BudgetPoolCheckUnitTest {

    @InjectMocks
    private BudgetPoolCheck budgetPoolCheck;
    @Mock
    private Config config;

    @Test
    public void isBudgetCheckOpen() {
        when(config.getBooleanProperty(eq("budget.check.open"), eq(true))).thenReturn(true);
        Assert.assertTrue(budgetPoolCheck.isBudgetCheckOpen(123456));
        when(config.getBooleanProperty(eq("budget.check.open"), eq(true))).thenReturn(false);
        when(config.getArrayProperty(eq("need.budget.check.business.id"), anyString(), eq(new String[]{}))).thenThrow(new ContextedRuntimeException("test"));
        Assert.assertFalse(budgetPoolCheck.isBudgetCheckOpen(123456));
    }
}
