package com.kugou.fanxing.recharge.service.callback;

import com.google.common.collect.Maps;
import com.kugou.fanxing.biz.commons.util.SnowFlake;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.AreaIdEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.AppStoreReceiptDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeOpenDao;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.bo.ReceiptInfoBO;
import com.kugou.fanxing.recharge.model.dto.CoinCallbackDTO;
import com.kugou.fanxing.recharge.model.dto.RechargeExtendDTO;
import com.kugou.fanxing.recharge.model.po.AppStoreReceiptPO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.service.PayPalRechargeService;
import com.kugou.fanxing.recharge.service.RechargeCouponService;
import com.kugou.fanxing.recharge.service.RechargeOrderService;
import com.kugou.fanxing.recharge.service.ValidatingService;
import com.kugou.fanxing.recharge.service.after.AfterRechargeService;
import com.kugou.fanxing.recharge.service.common.ConsumeRpcService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.service.recharge.GooglePlayRechargeService;
import com.kugou.fanxing.recharge.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Enumeration;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DefaultCallbackServiceUnitTest {

    @InjectMocks
    private DefaultCallbackService defaultCallbackService;
    @Mock
    protected RechargeConfig rechargeConfig;
    @Mock
    private HttpServletRequest request;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    protected RechargeOrderService rechargeOrderService;
    @Mock
    private ValidatingService validatingService;
    @Mock
    private RechargeAcrossDao rechargeAcrossDao;
    @Mock
    private ConsumeRpcService consumeRpcService;
    @Mock
    private AfterRechargeService afterRechargeService;
    @Mock
    private UserFacadeService userFacadeService;
    @Mock
    private RechargeCouponService rechargeCouponService;
    @Mock
    protected RechargeOpenDao rechargeOpenDao;
    @Mock
    protected PayPalRechargeService payPalRechargeService;
    @Mock
    protected AppStoreReceiptDao appStoreReceiptDao;
    @Mock
    protected GooglePlayRechargeService googlePlayRechargeService;

    @Before
    public void testBefore()  {
        when(this.rechargeOrderService.queryByRechargeOrderNum(anyString())).thenReturn(Optional.empty());
        when(this.rechargeConfig.getKupayAppIdByAppId(anyInt())).thenReturn(new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J"));
        when(request.getParameterNames()).thenReturn(new Enumeration<String>() {
            @Override
            public boolean hasMoreElements() {
                return false;
            }
            @Override
            public String nextElement() {
                return null;
            }
        });
        when(this.rechargeConfig.getKupayIntranet()).thenReturn("http://kupay.kugou.com");
    }

    @Test
    public void checkSignOfPcCallBack() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            Assert.assertTrue(defaultCallbackService.checkSignOfPcCallBack(1084, "7e14f00fc8620c60dbc01a0c9a394672"));
        }
    }

    @Test
    public void handleOpenCallbackOrder() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            when(orderIdService.generateGlobalId()).thenReturn(1453818833151602596L);
            when(rechargeConfig.getDataCenterZoneId()).thenReturn(1);
            CoinCallbackDTO coinCallbackDTO = new CoinCallbackDTO();
            coinCallbackDTO.setUserid(1290249156L);
            coinCallbackDTO.setExtend("************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
            SysResultCode sysResultCode = this.defaultCallbackService.handleOpenCallbackOrder(coinCallbackDTO, rechargeAcrossPO -> SysResultCode.RECHARGE_COUPON_FREEZE);
            Assert.assertEquals(sysResultCode.getCode(), SysResultCode.RECHARGE_COUPON_FREEZE.getCode());
        }
    }

    /**
     * {
     *     "callBackSign":"aa4502a06028204c8048bbc5bcc9fa7b",
     *     "callBackArg":{
     *         "amount":"30",
     *         "addTime":"1614182400",
     *         "coupon":"0",
     *         "rebate":0,
     *         "buyRichLevel":[
     *
     *         ],
     *         "consumeArgs":[
     *
     *         ],
     *         "businessExt":"",
     *         "couponId":0,
     *         "version":"20170111",
     *         "couponOrderId":0,
     *         "payTypeId":41,
     *         "money":"30",
     *         "refer":0,
     *         "cFrom":0,
     *         "channelId":0,
     *         "kugouId":1377161991
     *     }
     * }
     */
    @Test
    public void parseRechargeExtendCoin() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            String extend = "eyJjYWxsQmFja1NpZ24iOiJhYTQ1MDJhMDYwMjgyMDRjODA0OGJiYzViY2M5ZmE3YiIsImNhbGxCYWNrQXJnIjp7ImFtb3VudCI6IjMwIiwiYWRkVGltZSI6IjE2MTQxODI0MDAiLCJjb3Vwb24iOiIwIiwicmViYXRlIjowLCJidXlSaWNoTGV2ZWwiOltdLCJjb25zdW1lQXJncyI6W10sImJ1c2luZXNzRXh0IjoiIiwiY291cG9uSWQiOjAsInZlcnNpb24iOiIyMDE3MDExMSIsImNvdXBvbk9yZGVySWQiOjAsInBheVR5cGVJZCI6NDEsIm1vbmV5IjoiMzAiLCJyZWZlciI6MCwiY0Zyb20iOjAsImNoYW5uZWxJZCI6MCwia3Vnb3VJZCI6MTM3NzE2MTk5MX19";
            Assert.assertEquals("20170111", ExtendParseUtils.getCallbackArgVersion(extend));
            Assert.assertEquals("30", ExtendParseUtils.getCallbackArgAmount(extend).toPlainString());
            Assert.assertEquals(0, ExtendParseUtils.getCallbackArgChannelId(extend));
            Assert.assertEquals(0, ExtendParseUtils.getCallbackArgCFrom(extend));
            Assert.assertEquals(0, ExtendParseUtils.getCallbackArgRefer(extend));
            Assert.assertEquals(1377161991L, ExtendParseUtils.getCallbackArgKugouId(extend));
        }
    }

    /**
     * {
     * "appid":"1084",
     * "areaid":"02",
     * "time":"1614182426",
     * "notify_id":"f417bc913743cc8cc920b792fbf77d28",
     * "order_no":"R092021022500001019783079",
     * "total_fee":"10.00",
     * "out_trade_no":"02202102250000100100012507",
     * "trade_status":"1",
     * "sign_type":"md5",
     * "trade_time":"**************",
     * "trade_no":"4200000943202102253066954917",
     * "partner":"**********",
     * "userid":"1248375203",
     * "extend":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
     * "sign":"7fde63b63a0a9742a99bdbf6bc47ceff"
     * }
     */
    @Test
    public void buildSuccessOrder() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            int addTime = DateHelper.getCurrentSeconds();
            String rechargeOrderNum = "R092021022500001019783079";
            CoinCallbackDTO coinCallbackDTO = new CoinCallbackDTO();
            coinCallbackDTO.setTrade_status(1);
            coinCallbackDTO.setTrade_no("4200000943202102253066954917");
            coinCallbackDTO.setTrade_time("**************");
            coinCallbackDTO.setPartner("**********");
            coinCallbackDTO.setOut_trade_no("02202102250000100100012507");
            coinCallbackDTO.setExtend("************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
            when(orderIdService.generateGlobalId()).thenReturn(new SnowFlake(0, 0).nextId());
            RechargeAcrossPO rechargeAcrossPO = this.defaultCallbackService.buildSuccessOrder(rechargeOrderNum, addTime, coinCallbackDTO);
            Assert.assertEquals(rechargeOrderNum, rechargeAcrossPO.getRechargeOrderNum());
            Assert.assertEquals(1614182410, rechargeAcrossPO.getAddTime());
            Assert.assertEquals(addTime, rechargeAcrossPO.getRechargeTime());
            Assert.assertEquals("4200000943202102253066954917", rechargeAcrossPO.getTradeNo());
            Assert.assertEquals(**********, rechargeAcrossPO.getTradeTime());
            Assert.assertEquals("**********", rechargeAcrossPO.getPartner());
            Assert.assertEquals(1, rechargeAcrossPO.getReType());
        }
    }

    @Test
    public void callBackGoods() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            when(this.validatingService.checkViolation(any())).thenReturn(Optional.empty());
            KupayAppInfoBO kupayAppInfoBO = new KupayAppInfoBO();
            kupayAppInfoBO.setKupayAppId(1084);
            kupayAppInfoBO.setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J");
            kupayAppInfoBO.setPayTypeIds(Lists.newArrayList());
            when(this.rechargeConfig.getKupayAppIdByAppId(anyInt())).thenReturn(kupayAppInfoBO);
            Map<String, String> params = Maps.newHashMap();
            String actualSign = SignUtils.buildSign(params, kupayAppInfoBO.getKupayAppKey());
            CoinCallbackDTO coinCallbackDTO = new CoinCallbackDTO();
            coinCallbackDTO.setOrder_no("R092020120818243801500636");
            coinCallbackDTO.setUserid(596069021L);
            coinCallbackDTO.setSign(actualSign);
            try (MockedStatic<HttpClientUtils> mockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
                mockedStatic.when(() -> HttpClientUtils.doSyncGet(anyString(), anyMap())).thenReturn(Optional.of("success"));
                when(rechargeConfig.getCallBackKey()).thenReturn("kugoufanxing2016");
                coinCallbackDTO.setExtend("eyJjYWxsQmFja1NpZ24iOiIxYzYxNjRiNDBkZWUzMjBhODkwZjlhMDJhMjNhNTk0NSIsImNhbGxCYWNrQXJnIjp7ImFtb3VudCI6MSwiYWRkVGltZSI6MTYwNzQyMzA3OCwiY291cG9uIjowLCJ1c2VyT3BlbmlkIjoiZjUwZTZiY2E0ZjA5Y2Y5NmFkYjg1Yzg3ZGYwZGVjZGYiLCJidXlSaWNoTGV2ZWwiOnt9LCJyZWJhdGUiOiIwIiwiY29uc3VtZUFyZ3MiOnt9LCJidXNpbmVzc0V4dCI6IiIsImJ1c2luZXNzSWQiOiIxMDA2MyIsImNvdXBvbklkIjowLCJ2ZXJzaW9uIjoiMjAxNzAxMTEiLCJjb3Vwb25PcmRlcklkIjowLCJwYXlUeXBlSWQiOjMwLCJtb25leSI6MSwicmVmZXIiOjAsImNGcm9tIjowLCJjaGFubmVsSWQiOjAsImt1Z291SWQiOjU5NjA2OTAyMX19");
                when(this.rechargeConfig.getKupayAppIdByAppId(anyInt())).thenReturn(new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J"));
                RechargeAcrossPO sourceOrder = new RechargeAcrossPO();
                sourceOrder.setStatus(0);
                Optional<RechargeAcrossPO> optionalRechargeAcrossPO = Optional.of(sourceOrder);
                when(rechargeAcrossDao.insertIgnore(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
                when(consumeRpcService.rechargeFee(anyString(), anyInt(), anyString(), anyInt())).thenReturn(true);
                when(orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202109");
                doNothing().when(afterRechargeService).afterRechargeSuccess(any(RechargeAcrossPO.class));
                SysResultCode sysResultCode = this.defaultCallbackService.callBackGoods(coinCallbackDTO);
                Assert.assertTrue(sysResultCode.isSuccess());
            }
        }
    }

    @Test
    public void purchaseCurrencyCallback() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            KupayAppInfoBO kupayAppInfoBO = new KupayAppInfoBO();
            kupayAppInfoBO.setKupayAppId(1084);
            kupayAppInfoBO.setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J");
            kupayAppInfoBO.setPayTypeIds(Lists.newArrayList());
            when(this.rechargeConfig.getKupayAppIdByAppId(anyInt())).thenReturn(kupayAppInfoBO);
            Map<String, String> params = Maps.newHashMap();
            String actualSign = SignUtils.buildSign(params, kupayAppInfoBO.getKupayAppKey());
            CoinCallbackDTO coinCallbackDTO = new CoinCallbackDTO();
            coinCallbackDTO.setOrder_no("R092020120818243801500636");
            coinCallbackDTO.setUserid(596069021L);
            coinCallbackDTO.setSign(actualSign);
            coinCallbackDTO.setTotal_fee(BigDecimal.valueOf(100));
            try (MockedStatic<HttpClientUtils> mockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
                mockedStatic.when(() -> HttpClientUtils.doSyncGet(anyString(), anyMap())).thenReturn(Optional.of("success"));
                when(rechargeConfig.getCallBackKey()).thenReturn("kugoufanxing2016");
                coinCallbackDTO.setExtend("eyJjYWxsQmFja1NpZ24iOiIxYzYxNjRiNDBkZWUzMjBhODkwZjlhMDJhMjNhNTk0NSIsImNhbGxCYWNrQXJnIjp7ImFtb3VudCI6MSwiYWRkVGltZSI6MTYwNzQyMzA3OCwiY291cG9uIjowLCJ1c2VyT3BlbmlkIjoiZjUwZTZiY2E0ZjA5Y2Y5NmFkYjg1Yzg3ZGYwZGVjZGYiLCJidXlSaWNoTGV2ZWwiOnt9LCJyZWJhdGUiOiIwIiwiY29uc3VtZUFyZ3MiOnt9LCJidXNpbmVzc0V4dCI6IiIsImJ1c2luZXNzSWQiOiIxMDA2MyIsImNvdXBvbklkIjowLCJ2ZXJzaW9uIjoiMjAxNzAxMTEiLCJjb3Vwb25PcmRlcklkIjowLCJwYXlUeXBlSWQiOjMwLCJtb25leSI6MSwicmVmZXIiOjAsImNGcm9tIjowLCJjaGFubmVsSWQiOjAsImt1Z291SWQiOjU5NjA2OTAyMX19");
                when(this.rechargeConfig.getKupayAppIdByAppId(anyInt())).thenReturn(new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J"));
                RechargeAcrossPO sourceOrder = new RechargeAcrossPO();
                sourceOrder.setStatus(0);
                when(rechargeCouponService.extractValidCoupon(any(RechargeAcrossPO.class))).thenReturn(Optional.empty());
                when(consumeRpcService.rechargeCoin(any(RechargeAcrossPO.class))).thenReturn(true);
                when(userFacadeService.getUserIdByKugouId(anyLong(), anyBoolean())).thenReturn(Optional.of(1290249156L));
                Optional<RechargeAcrossPO> optionalRechargeAcrossPO = Optional.of(sourceOrder);
                when(this.rechargeAcrossDao.insertIgnore(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
                when(orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202107");
                when(this.rechargeOrderService.queryByRechargeOrderNum(anyString())).thenReturn(optionalRechargeAcrossPO);
                SysResultCode sysResultCode = this.defaultCallbackService.purchaseCurrencyCallback(coinCallbackDTO);
                Assert.assertTrue(sysResultCode.isSuccess());
            }
        }
    }

    @Test
    public void saveCallbackSuccessOrder() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(SpringContextUtils::getHttpServletRequest).thenReturn(request);
            when(this.rechargeAcrossDao.insertIgnore(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
            when(orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202107");
            RechargeAcrossPO callbackOrder = new RechargeAcrossPO();
            callbackOrder.setRechargeOrderNum("R092021071320573485337066");
            Assert.assertTrue(this.defaultCallbackService.saveCallbackSuccessOrder(callbackOrder));
        }
    }

    @Test
    public void saveGameCallbackSuccessOrder() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            RechargeAcrossPO targetOrder = new RechargeAcrossPO();
            targetOrder.setRechargeOrderNum("R09202012S1TID1000000750236975");
            String receiptData = "";
            ReceiptInfoBO receiptInfoBO = new ReceiptInfoBO();
            receiptInfoBO.setReceipt(new ReceiptInfoBO.Receipts());
            when(orderIdService.generateGlobalId()).thenReturn(1L);
            when(appStoreReceiptDao.insertIgnore(any(AppStoreReceiptPO.class))).thenReturn(1);
            when(orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202112");
            when(rechargeOpenDao.insertIgnore(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
            boolean result = this.defaultCallbackService.saveGameCallbackSuccessOrder(targetOrder, receiptData, receiptInfoBO);
            Assert.assertTrue(result);
        }
    }

    @Test
    public void parseRechargeExtendDTO() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            String extend = "eyJjYWxsQmFja1NpZ24iOiI1ZWRhMzMzYjVkMmY4MGZhM2FiMWU2YzM2MjQ0MmE3ZSIsImNhbGxCYWNrQXJnIjp7ImFtb3VudCI6IjAuMDIiLCJhZGRUaW1lIjoiMTY0MDY4NTcyNCIsImNvdXBvbiI6IjAiLCJyZWJhdGUiOjAsImJ1eVJpY2hMZXZlbCI6W10sImNvbnN1bWVBcmdzIjpbXSwiYnVzaW5lc3NFeHQiOiIiLCJjb3Vwb25JZCI6MCwidmVyc2lvbiI6IjIwMTcwMTExIiwiYWdlbnRLdWdvdUlkIjo5OTE2MTI4MDIsImNvdXBvbk9yZGVySWQiOjAsInBheVR5cGVJZCI6NTIwLCJtb25leSI6IjAuMTIiLCJyZWZlciI6MCwiY0Zyb20iOjAsImNoYW5uZWxJZCI6MCwia3Vnb3VJZCI6MTI5MDI0OTE1Nn19";
            String json = new String(Base64.decodeBase64(extend));
            RechargeExtendDTO rechargeExtendDTO = this.defaultCallbackService.parseRechargeExtendDTO(json);
            Assert.assertTrue(rechargeExtendDTO.getAgentKugouId() > 0);
            Assert.assertNotNull(rechargeExtendDTO.getCoin());
            Assert.assertTrue(rechargeExtendDTO.getCoin().compareTo(BigDecimal.ZERO) >= 0);
        }
    }

    @Test
    public void checkKupayCallBackParams() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            CoinCallbackDTO coinCallbackDTO = new CoinCallbackDTO();
            RechargeExtendDTO rechargeExtendDTO = new RechargeExtendDTO();
            rechargeExtendDTO.setAreaId(AreaIdEnum.OUT_SEA.getAreaId());
            rechargeExtendDTO.setKugouId(1);
            rechargeExtendDTO.setUsdAmount(BigDecimal.ONE);
            coinCallbackDTO.setTotal_fee(BigDecimal.TEN);
            defaultCallbackService.checkKupayCallbackParams(coinCallbackDTO, rechargeExtendDTO);
            Assert.assertFalse(coinCallbackDTO.getTotal_fee().equals(rechargeExtendDTO.getUsdAmount()));
            rechargeExtendDTO.setKugouId(0);
            rechargeExtendDTO.setAmount(BigDecimal.ONE);
            coinCallbackDTO.setTotal_fee(BigDecimal.TEN);
            defaultCallbackService.checkKupayCallbackParams(coinCallbackDTO, rechargeExtendDTO);
            Assert.assertFalse(coinCallbackDTO.getTotal_fee().equals(rechargeExtendDTO.getUsdAmount()));
        }

    }

    @Test
    public void checkCallbackOrder() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
            RechargeExtendDTO rechargeExtendDTO = new RechargeExtendDTO();
            rechargeExtendDTO.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_3.getPayTypeId());
            //不是Google play不处理，返回空
            Optional<SysResultCode> emptyRs = defaultCallbackService.checkCallbackOrder(rechargeAcrossPO, rechargeExtendDTO);
            Assert.assertFalse(emptyRs.isPresent());
            rechargeExtendDTO.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_521.getPayTypeId());
            rechargeAcrossPO.setIsSandbox(0);
            //google play 非沙盒 不在黑名单
            when(googlePlayRechargeService.isInGooglePlayBlackCurrencylist(anyString())).thenReturn(false);
            Optional<SysResultCode> newRs = defaultCallbackService.checkCallbackOrder(rechargeAcrossPO, rechargeExtendDTO);
            Assert.assertFalse(newRs.isPresent());
            when(googlePlayRechargeService.isInGooglePlayBlackCurrencylist(any())).thenReturn(true);

            newRs = defaultCallbackService.checkCallbackOrder(rechargeAcrossPO, rechargeExtendDTO);
            Assert.assertTrue(newRs.isPresent());
            Assert.assertEquals(newRs.get().getCode(), SysResultCode.GP_BLACK_CURRENCY.getCode());

            rechargeAcrossPO.setIsSandbox(1);
            when(googlePlayRechargeService.isInGooglePlaySandboxWhitelist(anyLong())).thenReturn(false);
            newRs = defaultCallbackService.checkCallbackOrder(rechargeAcrossPO, rechargeExtendDTO);
            Assert.assertTrue(newRs.isPresent());
            Assert.assertEquals(newRs.get().getCode(), SysResultCode.E_30000014.getCode());
        }
    }

    @Test
    public void calculateCoinValue() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            CoinCallbackDTO coinCallbackDTO = new CoinCallbackDTO();
            RechargeExtendDTO rechargeExtendDTO = new RechargeExtendDTO();
            rechargeExtendDTO.setAreaId(AreaIdEnum.OUT_SEA.getAreaId());
            rechargeExtendDTO.setKugouId(1);
            rechargeExtendDTO.setUsdAmount(BigDecimal.ONE);
            rechargeExtendDTO.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_521.getPayTypeId());
            rechargeExtendDTO.setCoin(BigDecimal.valueOf(123));
            coinCallbackDTO.setTotal_fee(BigDecimal.TEN);
            rechargeExtendDTO.setProductId("test");
            when(googlePlayRechargeService.getProductCoinById(eq("test"))).thenReturn(BigDecimal.valueOf(456));
            BigDecimal coin = this.defaultCallbackService.calculateCoinValue(coinCallbackDTO, rechargeExtendDTO);
            Assert.assertEquals(0, coin.compareTo(BigDecimal.valueOf(456)));
            rechargeExtendDTO.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_522.getPayTypeId());
            rechargeExtendDTO.setCoin(BigDecimal.valueOf(123));
            coinCallbackDTO.setTotal_fee(BigDecimal.TEN);
            coin = this.defaultCallbackService.calculateCoinValue(coinCallbackDTO, rechargeExtendDTO);
            Assert.assertEquals(0, coin.compareTo(BigDecimal.valueOf(123)));
            rechargeExtendDTO.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_40.getPayTypeId());
            coin = this.defaultCallbackService.calculateCoinValue(coinCallbackDTO, rechargeExtendDTO);
            Assert.assertEquals(0, coin.compareTo(BigDecimal.valueOf(1000)));
        }
    }

    @Test(expected = ContextedRuntimeException.class)
    public void calculateCoinValueException() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            CoinCallbackDTO coinCallbackDTO = new CoinCallbackDTO();
            RechargeExtendDTO rechargeExtendDTO = new RechargeExtendDTO();
            rechargeExtendDTO.setAreaId(AreaIdEnum.OUT_SEA.getAreaId());
            rechargeExtendDTO.setKugouId(1);
            rechargeExtendDTO.setUsdAmount(BigDecimal.ONE);
            rechargeExtendDTO.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_522.getPayTypeId());
            rechargeExtendDTO.setCoin(BigDecimal.ZERO);
            coinCallbackDTO.setTotal_fee(BigDecimal.TEN);
            this.defaultCallbackService.calculateCoinValue(coinCallbackDTO, rechargeExtendDTO);
        }
    }

}
