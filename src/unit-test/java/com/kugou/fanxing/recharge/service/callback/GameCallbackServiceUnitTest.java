package com.kugou.fanxing.recharge.service.callback;

import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeOpenDao;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.dto.CoinCallbackDTO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.service.RechargeOrderService;
import com.kugou.fanxing.recharge.service.ValidatingService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.util.SignUtils;
import com.kugou.fanxing.recharge.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class GameCallbackServiceUnitTest {

    @InjectMocks
    private GameCallbackService gameCallbackService;
    @Mock
    private ValidatingService validatingService;
    @Mock
    private RechargeConfig rechargeConfig;
    @Mock
    private HttpServletRequest request;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    protected RechargeOrderService rechargeOrderService;
    @Mock
    private RechargeAcrossDao rechargeAcrossDao;
    @Mock
    private RechargeOpenDao rechargeOpenDao;

    @Before
    public void testBefore()  {
        when(this.validatingService.checkViolation(any())).thenReturn(Optional.empty());
        when(this.rechargeOrderService.queryByRechargeOrderNum(anyString())).thenReturn(Optional.empty());
        when(this.rechargeConfig.getKupayAppIdByAppId(anyInt())).thenReturn(new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J"));
        when(request.getParameterNames()).thenReturn(new Enumeration<String>() {
            @Override
            public boolean hasMoreElements() {
                return false;
            }
            @Override
            public String nextElement() {
                return null;
            }
        });
        when(this.rechargeConfig.getKupayIntranet()).thenReturn("http://kupay.kugou.com");
    }

    @Test
    public void purchaseCurrencyCallback() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            KupayAppInfoBO kupayAppInfoBO = new KupayAppInfoBO();
            kupayAppInfoBO.setKupayAppId(1084);
            kupayAppInfoBO.setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J");
            kupayAppInfoBO.setPayTypeIds(Lists.newArrayList());
            when(this.rechargeConfig.getKupayAppIdByAppId(anyInt())).thenReturn(kupayAppInfoBO);
            Map<String, String> params = Maps.newHashMap();
            String actualSign = SignUtils.buildSign(params, kupayAppInfoBO.getKupayAppKey());
            CoinCallbackDTO coinCallbackDTO = new CoinCallbackDTO();
            coinCallbackDTO.setSign(actualSign);
            SysResultCode sysResultCode = gameCallbackService.purchaseCurrencyCallback(coinCallbackDTO);
            Assert.assertFalse(sysResultCode.isSuccess());
        }
    }

    @Test
    public void saveTargetOrder() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            when(rechargeOpenDao.insertIgnore(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
            when(orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202012");
            RechargeAcrossPO targetOrder = new RechargeAcrossPO();
            Assert.assertFalse(this.gameCallbackService.saveTargetOrder(targetOrder));
        }
    }

    @Test
    public void queryGameRechargeOrder() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            when(orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202012");
            when(this.rechargeOpenDao.queryByRechargeOrderNum(anyString(), anyString())).thenReturn(new RechargeAcrossPO());
            Optional<RechargeAcrossPO> optionalRechargeAcrossPO = this.gameCallbackService.queryGameRechargeOrder("R092021040118065796380088");
            Assert.assertTrue(optionalRechargeAcrossPO.isPresent());
        }
    }
}
