package com.kugou.fanxing.recharge.service.withdraw.transfer;

import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawOrderDao;
import com.kugou.fanxing.recharge.model.DrawCashUpdater;
import com.kugou.fanxing.recharge.service.withdraw.AlipayResp;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class TransferNotExistsServiceUnitTest {
    @InjectMocks
    private TransferNotExistsService transferNotExistsService;
    @Mock
    private WithdrawOrderDao withdrawOrderDao;
    @Test(expected = Exception.class)
    public void handleAlipayResp() {
        AlipayResp alipayResp = new AlipayResp();
        alipayResp.setError_code(0);
        alipayResp.setStatus(1);
        alipayResp.setData(null);
        when(this.withdrawOrderDao.updateOrderStatus(anyInt(), any(DrawCashUpdater.class))).thenReturn(1).thenReturn(0);
        Assert.assertTrue(this.transferNotExistsService.handleAlipayResp(1, alipayResp));

        this.transferNotExistsService.handleAlipayResp(1, alipayResp);
    }

    @Test(expected = ContextedRuntimeException.class)
    public void handleAlipayRespException() {
        AlipayResp alipayResp = new AlipayResp();
        alipayResp.setError_code(0);
        alipayResp.setStatus(1);
        alipayResp.setData(null);
        when(this.withdrawOrderDao.updateOrderStatus(anyInt(), any(DrawCashUpdater.class))).thenThrow(new ContextedRuntimeException("test"));
        this.transferNotExistsService.handleAlipayResp(1, alipayResp);
    }

    @Test
    public void handleAlipayRespFail() {
        AlipayResp alipayResp = new AlipayResp();
        alipayResp.setError_code(1);
        alipayResp.setData(null);
        Assert.assertFalse(this.transferNotExistsService.handleAlipayResp(1, alipayResp));
    }
}
