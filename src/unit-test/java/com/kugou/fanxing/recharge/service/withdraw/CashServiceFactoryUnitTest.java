package com.kugou.fanxing.recharge.service.withdraw;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class CashServiceFactoryUnitTest {

    @Mock
    private ApplicationContext applicationContext;
    @InjectMocks
    private CashServiceFactory cashServiceFactory;

    @Test
    public void createCashService() {
        when(applicationContext.getBean(any(Class.class))).thenReturn(new WechatCashService());
        CashService cashService1 = this.cashServiceFactory.createCashService(1);
        Assert.assertTrue(cashService1 instanceof WechatCashService);
        when(applicationContext.getBean(any(Class.class))).thenReturn(new AlipayCashService());
        CashService cashService2 = this.cashServiceFactory.createCashService(2);
        Assert.assertTrue(cashService2 instanceof AlipayCashService);
    }

    @Test(expected = UnsupportedOperationException.class)
    public void createCashServiceException() {
        this.cashServiceFactory.createCashService(3);
    }
}
