package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.ip.api.FXIpService;
import com.kugou.fanxing.ip.api.IPInfoReq;
import com.kugou.fanxing.ip.api.SimpleIPInfoResp;
import com.kugou.fanxing.ip.api.SimpleIpInfo;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class GetSimpleIPInfoByIpCommandTest {

    @Mock
    private FXIpService.Iface fxIpService;

    @Test
    public void confirm() throws TException {
        SimpleIPInfoResp simpleIPInfoResp = new SimpleIPInfoResp();
        simpleIPInfoResp.setCode(0);
        simpleIPInfoResp.setSimpleIpInfo(new SimpleIpInfo().setCity("广州"));
        when(fxIpService.getSimpleIPInfoByIp(any(IPInfoReq.class))).thenReturn(simpleIPInfoResp);
        GetSimpleIPInfoByIpCommand command = new GetSimpleIPInfoByIpCommand(fxIpService, "127.0.0.1");
        Optional<SimpleIpInfo> optionalSimpleIpInfo = command.execute();
        Assert.assertTrue(optionalSimpleIpInfo.isPresent());
        Assert.assertEquals("广州", optionalSimpleIpInfo.get().getCity());
        when(fxIpService.getSimpleIPInfoByIp(any(IPInfoReq.class))).thenReturn(null);
        command = new GetSimpleIPInfoByIpCommand(fxIpService, "127.0.0.1");
        Assert.assertFalse(command.execute().isPresent());
    }

    @Test
    public void confirmException() throws TException {
        when(fxIpService.getSimpleIPInfoByIp(any(IPInfoReq.class))).thenThrow(new ContextedRuntimeException("test"));
        GetSimpleIPInfoByIpCommand command = new GetSimpleIPInfoByIpCommand(fxIpService, "127.0.0.1");
        Assert.assertFalse(command.execute().isPresent());
    }
}
