package com.kugou.fanxing.recharge.service.command;

import com.kugou.kw.idservice.api.struct.BatchIdResponse;
import com.kugou.kw.idservice.api.struct.IdInfo;
import com.kugou.kw.idservice.api.struct.KuwoIdMappingService;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class BatchGetIdInfoByKugouIdCommandUnitTest {

    @Mock
    private KuwoIdMappingService.Iface kuwoIdMappingService;

    @Test
    public void batchGetIdInfoByKugouId() throws TException {
        BatchIdResponse response = new BatchIdResponse();
        response.setCode(0);
        IdInfo info = new IdInfo();
        info.setKugouId(1290249156L);
        info.setKuwoId(12345678L);
        response.setData(Lists.newArrayList(Lists.newArrayList(info)));
        when(kuwoIdMappingService.batchGetIdInfoByKugouId(anyList())).thenReturn(response);
        BatchGetIdInfoByKugouIdCommand command = new BatchGetIdInfoByKugouIdCommand(kuwoIdMappingService, Lists.newArrayList(1290249156L));
        Assert.assertFalse(command.execute().isEmpty());

        when(kuwoIdMappingService.batchGetIdInfoByKugouId(anyList())).thenReturn(null);
        command = new BatchGetIdInfoByKugouIdCommand(kuwoIdMappingService, Lists.newArrayList(1290249156L));
        Assert.assertTrue(command.execute().isEmpty());
    }

    @Test
    public void batchGetIdInfoByKugouIdException() throws TException {
        when(kuwoIdMappingService.batchGetIdInfoByKugouId(anyList())).thenThrow(new ContextedRuntimeException());
        BatchGetIdInfoByKugouIdCommand command = new BatchGetIdInfoByKugouIdCommand(kuwoIdMappingService, Lists.newArrayList(1290249156L));
        Assert.assertEquals(0, command.execute().size());
    }
}
