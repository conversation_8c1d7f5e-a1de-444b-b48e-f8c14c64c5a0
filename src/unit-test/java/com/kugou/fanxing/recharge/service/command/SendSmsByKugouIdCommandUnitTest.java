package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.thrift.plat.user.UserPlatBizService;
import com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq;
import com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class SendSmsByKugouIdCommandUnitTest {

    @Mock
    private UserPlatBizService.Iface userPlatBizService;

    @Test
    public void sendSmsByKugouId() throws TException {
        SendSmsByKugouIdReq request = new SendSmsByKugouIdReq();
        SendSmsByKugouIdResp resp = new SendSmsByKugouIdResp();
        resp.setCode(0);
        when(userPlatBizService.sendSmsByKugouId(any(SendSmsByKugouIdReq.class))).thenReturn(resp);
        SendSmsByKugouIdCommand command1 = new SendSmsByKugouIdCommand(userPlatBizService, request);
        Assert.assertTrue(command1.execute());
        when(userPlatBizService.sendSmsByKugouId(any(SendSmsByKugouIdReq.class))).thenReturn(null);
        SendSmsByKugouIdCommand command2 = new SendSmsByKugouIdCommand(userPlatBizService, request);
        Assert.assertFalse(command2.execute());
    }

    @Test
    public void sendSmsByKugouIdException() throws TException {
        SendSmsByKugouIdReq request = new SendSmsByKugouIdReq();
        when(userPlatBizService.sendSmsByKugouId(any(SendSmsByKugouIdReq.class))).thenThrow(new TException("test"));
        SendSmsByKugouIdCommand command = new SendSmsByKugouIdCommand(userPlatBizService, request);
        Assert.assertFalse(command.execute());
    }
}
