package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.thrift.banaccount.dto.Result;
import com.kugou.fanxing.thrift.banaccount.service.BanAccountService;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class IsBanAccountCommandUnitTest {

    @Mock
    private BanAccountService.Iface banAccountService;

    @Test
    public void isBanAccount() throws TException {
        when(banAccountService.isBanAccount(anyLong())).thenReturn(new Result().setResult(true));
        IsBanAccountCommand command = new IsBanAccountCommand(banAccountService, 1290249156L);
        Assert.assertTrue(command.execute());
        when(banAccountService.isBanAccount(anyLong())).thenReturn(new Result().setResult(false));
        command = new IsBanAccountCommand(banAccountService, 1290249156L);
        Assert.assertFalse(command.execute());
    }

    @Test
    public void isBanAccountException() throws TException {
        when(banAccountService.isBanAccount(anyLong())).thenThrow(new TException("test"));
        IsBanAccountCommand command = new IsBanAccountCommand(banAccountService, 1290249156L);
        Assert.assertFalse(command.execute());
    }
}
