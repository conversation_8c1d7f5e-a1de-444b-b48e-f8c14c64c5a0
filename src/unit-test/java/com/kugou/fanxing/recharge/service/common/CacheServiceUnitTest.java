package com.kugou.fanxing.recharge.service.common;

import com.alibaba.fastjson.TypeReference;
import com.kugou.cache.rediscluster.RedisClusterInterface;
import com.kugou.fanxing.recharge.thrift.UserYearRechargeStatData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.internal.util.collections.Sets;
import org.mockito.junit.MockitoJUnitRunner;
import redis.clients.jedis.Tuple;

import java.util.List;
import java.util.Set;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class CacheServiceUnitTest {

    @InjectMocks
    private CacheService cacheService;
    @Mock
    private RedisClusterInterface redisClusterInterface;

    @Test
    public void getException() {
        when(redisClusterInterface.get(anyString())).thenThrow(new ContextedRuntimeException("test"));
        Assert.assertNull(cacheService.get("test", UserYearRechargeStatData.class));
    }

    @Test
    public void del() {
        when(redisClusterInterface.del(anyString())).thenReturn(1L);
        long ret = this.cacheService.del("test");
        Assert.assertEquals(1L, ret);
        when(redisClusterInterface.del(anyString())).thenThrow(new ContextedRuntimeException("test"));
        ret = this.cacheService.del("test");
        Assert.assertEquals(0, ret);
    }

    @Test
    public void get() {
        when(redisClusterInterface.get(anyString())).thenReturn("OK");
        Assert.assertEquals("OK", this.cacheService.get("test"));
        when(redisClusterInterface.get(anyString())).thenThrow(new ContextedRuntimeException("test"));
        Assert.assertEquals("", this.cacheService.get("test"));
    }

    @Test
    public void getByClass() {
        when(redisClusterInterface.get(anyString())).thenReturn("12");
        int ret = this.cacheService.get("test", Integer.class);
        Assert.assertEquals(12, ret);
    }

    @Test
    public void getTypeReference() {
        when(redisClusterInterface.get(anyString())).thenReturn("[1,2,3]");
        List<Integer> rets = this.cacheService.get("test", new TypeReference<List<Integer>>(){});
        Assert.assertEquals(3, rets.size());
    }

    @Test
    public void zrangeWithScores() {
        when(redisClusterInterface.zrangeWithScores(anyString(), anyLong(), anyLong())).thenReturn(Sets.newSet(new Tuple("a", 1d), new Tuple("b", 2d)));
        Set<Tuple> ret = this.cacheService.zrangeWithScores("test", 1, 2);
        Assert.assertEquals(ret.size(), ret.size());
        when(redisClusterInterface.zrangeWithScores(anyString(), anyLong(), anyLong())).thenThrow(new ContextedRuntimeException("test"));
        ret = this.cacheService.zrangeWithScores("test", 1, 2);
        Assert.assertEquals(0, ret.size());
    }

    @Test
    public void zincrby() {
        when(redisClusterInterface.zincrby(anyString(), anyDouble(), anyString())).thenReturn(1d);
        double ret = this.cacheService.zincrby("test", 1, "aa");
        Assert.assertTrue(1L == ret);
        when(redisClusterInterface.zincrby(anyString(), anyDouble(), anyString())).thenThrow(new ContextedRuntimeException("test"));
        ret = this.cacheService.zincrby("test", 1, "aa");
        Assert.assertTrue(0 == ret);
    }

    @Test
    public void sismember() {
        when(redisClusterInterface.sismember(anyString(), anyString())).thenReturn(true);
        boolean ret = this.cacheService.sismember("test",  "aa");
        Assert.assertTrue(ret);
        when(redisClusterInterface.sismember(anyString(), anyString())).thenThrow(new ContextedRuntimeException("test"));
        ret = this.cacheService.sismember("test", "aa");
        Assert.assertFalse(ret);
    }

    @Test
    public void set() {
        when(redisClusterInterface.set(anyString(), anyString())).thenReturn("OK");
        String ret = this.cacheService.set("test", "aa");
        Assert.assertEquals("OK", ret);
        when(redisClusterInterface.set(anyString(), anyString())).thenThrow(new ContextedRuntimeException("test"));
        ret = this.cacheService.set("test", "aa");
        Assert.assertEquals("", ret);
        when(redisClusterInterface.setex(anyString(), anyInt(), anyString())).thenReturn("OK");
        ret = this.cacheService.set("test", 10, "aa");
        Assert.assertEquals("OK", ret);
    }

    @Test
    public void setex() {
        when(redisClusterInterface.setex(anyString(), anyInt(), anyString())).thenReturn("OK");
        String ret = this.cacheService.setex("test", 10, "aa");
        Assert.assertEquals("OK", ret);
        when(redisClusterInterface.setex(anyString(), anyInt(), anyString())).thenThrow(new ContextedRuntimeException("test"));
        ret = this.cacheService.setex("test", 5, "aa");
        Assert.assertEquals("", ret);
    }
}
