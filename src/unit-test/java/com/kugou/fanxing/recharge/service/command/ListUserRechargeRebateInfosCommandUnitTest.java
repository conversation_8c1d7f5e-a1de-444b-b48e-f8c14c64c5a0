package com.kugou.fanxing.recharge.service.command;

import com.kugou.platform.recharge.queryservice.thrift.RechargeInfoQueryThriftService;
import com.kugou.platform.recharge.queryservice.thrift.RechargeRebateInfo;
import com.kugou.platform.recharge.queryservice.thrift.ResultInfo;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class ListUserRechargeRebateInfosCommandUnitTest {

    @Mock
    private RechargeInfoQueryThriftService.Iface rechargeInfoQueryThriftService;

    @Test
    public void listUserRechargeRebateInfos() throws TException {
        ResultInfo resultInfo = new ResultInfo();
        resultInfo.setRet(0);
        resultInfo.setData(Lists.newArrayList(new RechargeRebateInfo().setKugouId(1290249156L)));
        when(rechargeInfoQueryThriftService.listUserRechargeRebateInfos(anyString(), anyList())).thenReturn(resultInfo);
        ListUserRechargeRebateInfosCommand command = new ListUserRechargeRebateInfosCommand(rechargeInfoQueryThriftService, "2021", Lists.newArrayList("R1"));
        Map<String, RechargeRebateInfo> rechargeRebateInfoMap = command.execute();
        Assert.assertFalse(rechargeRebateInfoMap.isEmpty());
        when(rechargeInfoQueryThriftService.listUserRechargeRebateInfos(anyString(), anyList())).thenReturn(null);
        command = new ListUserRechargeRebateInfosCommand(rechargeInfoQueryThriftService, "2021", Lists.newArrayList("R1"));
        rechargeRebateInfoMap = command.execute();
        Assert.assertTrue(rechargeRebateInfoMap.isEmpty());
        when(rechargeInfoQueryThriftService.listUserRechargeRebateInfos(anyString(), anyList())).thenReturn(resultInfo);
        command = new ListUserRechargeRebateInfosCommand(rechargeInfoQueryThriftService, "2021", Lists.newArrayList());
        rechargeRebateInfoMap = command.execute();
        Assert.assertTrue(rechargeRebateInfoMap.isEmpty());
    }

    @Test
    public void listUserRechargeRebateInfosException() throws TException {
        when(rechargeInfoQueryThriftService.listUserRechargeRebateInfos(anyString(), anyList())).thenThrow(new ContextedRuntimeException());
        ListUserRechargeRebateInfosCommand command = new ListUserRechargeRebateInfosCommand(rechargeInfoQueryThriftService, "2021", Lists.newArrayList("R1"));
        Map<String, RechargeRebateInfo> rechargeRebateInfoMap = command.execute();
        Assert.assertTrue(rechargeRebateInfoMap.isEmpty());
    }
}
