package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.thrift.fangroup.service.FanGroupService;
import com.kugou.fanxing.thrift.fangroup.service.GetFanGroupInfoRequest;
import com.kugou.fanxing.thrift.fangroup.service.GetFanGroupInfoResponse;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * @Description:
 * @author: jackiechen
 * @Date: 2021/12/15
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class GetFanGroupInfoCommandTest {

    @Mock
    private FanGroupService.Iface fanGroupService;

    @Test
    public void confirm() throws TException {
        GetFanGroupInfoResponse response = new GetFanGroupInfoResponse();
        when(fanGroupService.getFanGroupInfo(any(GetFanGroupInfoRequest.class))).thenReturn(response);
        GetFanGroupInfoCommand command = new GetFanGroupInfoCommand(fanGroupService, new GetFanGroupInfoRequest());
        Optional<GetFanGroupInfoResponse> optionalGetFanGroupInfoResponse = command.execute();
        GetFanGroupInfoResponse getFanGroupInfoResponse = optionalGetFanGroupInfoResponse.orElseThrow(() -> new ContextedRuntimeException("test"));
        Assert.assertEquals(0, getFanGroupInfoResponse.getCode());
    }

    @Test
    public void confirmException() throws TException {
        when(fanGroupService.getFanGroupInfo(any(GetFanGroupInfoRequest.class))).thenThrow(new ContextedRuntimeException("test"));
        GetFanGroupInfoCommand command = new GetFanGroupInfoCommand(fanGroupService, new GetFanGroupInfoRequest());
        Optional<GetFanGroupInfoResponse> optionalGetFanGroupInfoResponse = command.execute();
        Assert.assertFalse(optionalGetFanGroupInfoResponse.isPresent());
    }

}
