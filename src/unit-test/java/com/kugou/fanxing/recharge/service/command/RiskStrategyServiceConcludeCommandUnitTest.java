package com.kugou.fanxing.recharge.service.command;

import com.google.common.collect.Maps;
import com.kugou.fanxing.risk.sdk.RiskStrategyService;
import com.kugou.fanxing.risk.sdk.model.RiskResult;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class RiskStrategyServiceConcludeCommandUnitTest {

    @Mock
    private RiskStrategyService riskStrategyService;
    @Mock
    private HttpServletRequest request;
    private Map<String, Object> paramsMap;

    @Before
    public void before() {
        paramsMap = Maps.newHashMap();
    }

    @Test
    public void conclude() {
        RiskResult riskResult = new RiskResult();
        riskResult.setCode(0);
        riskResult.setLevel(4);
        when(riskStrategyService.conclude(any(HttpServletRequest.class), anyMap())).thenReturn(riskResult);
        RiskStrategyServiceConcludeCommand command = new RiskStrategyServiceConcludeCommand(riskStrategyService, request, paramsMap);
        Assert.assertTrue(command.execute().isPresent());
    }

    @Test
    public void concludeException() {
        when(riskStrategyService.conclude(any(HttpServletRequest.class), anyMap())).thenThrow(new ContextedRuntimeException("test"));
        RiskStrategyServiceConcludeCommand command = new RiskStrategyServiceConcludeCommand(riskStrategyService, request, paramsMap);
        Assert.assertFalse(command.execute().isPresent());
    }
}
