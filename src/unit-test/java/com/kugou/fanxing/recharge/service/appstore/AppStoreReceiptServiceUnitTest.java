package com.kugou.fanxing.recharge.service.appstore;

import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.AppStoreReceiptDao;
import com.kugou.fanxing.recharge.model.bo.ProxyConfigBO;
import com.kugou.fanxing.recharge.model.bo.ReceiptInfoBO;
import com.kugou.fanxing.recharge.model.po.AppStoreReceiptPO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.Proxy;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;


@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AppStoreReceiptServiceUnitTest {

    private static final String receiptData = "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";

    @Mock
    private ApolloConfigService apolloConfigService;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    private AppStoreReceiptDao appStoreReceiptDao;

    @InjectMocks
    private AppStoreReceiptService appStoreReceiptService;

    @Test
    public void verifyReceipt() {
        try (MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doPostJsonBody(any(Proxy.class), anyString(), anyMap(), anyString())).thenReturn(Optional.empty());
            ProxyConfigBO proxyConfigBO = new ProxyConfigBO();
            proxyConfigBO.setProxyPort(3128);
            proxyConfigBO.setProxyHost("forward.proxy.kgidc.cn");
            when(this.apolloConfigService.getProxyConfig()).thenReturn(proxyConfigBO);
            Optional<ReceiptInfoBO> optionalReceiptInfoBO = this.appStoreReceiptService.verifyReceipt(receiptData);
            Assert.assertFalse(optionalReceiptInfoBO.isPresent());
        }
    }

    @Test
    public void verifyReceiptSandbox() {
        try (MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doPostJsonBody(any(Proxy.class), anyString(), anyMap(), anyString())).thenReturn(Optional.of("{\"status\":21007}"));
            ProxyConfigBO proxyConfigBO = new ProxyConfigBO();
            proxyConfigBO.setProxyPort(3128);
            proxyConfigBO.setProxyHost("forward.proxy.kgidc.cn");
            when(this.apolloConfigService.getProxyConfig()).thenReturn(proxyConfigBO);
            Optional<ReceiptInfoBO> optionalReceiptInfoBO = this.appStoreReceiptService.verifyReceipt(receiptData);
            Assert.assertTrue(optionalReceiptInfoBO.isPresent());
        }
    }

    @Test
    public void isSandboxReceipt() {
        Assert.assertTrue(this.appStoreReceiptService.isSandboxReceipt(receiptData));
    }

    @Test
    public void parseReceiptDataEnvironment() {
        String environment = this.appStoreReceiptService.parseReceiptDataEnvironment(receiptData);
        Assert.assertEquals("Sandbox", environment);
    }

    @Test
    public void parseReceipt() {
        String json = "{\n" +
                "    \"receipt\": {\n" +
                "        \"original_purchase_date_pst\": \"2022-08-05 01:59:15 America/Los_Angeles\",\n" +
                "        \"purchase_date_ms\": \"1659689955819\",\n" +
                "        \"unique_identifier\": \"00008101-000D6D141491001E\",\n" +
                "        \"original_transaction_id\": \"2000000123323768\",\n" +
                "        \"bvrs\": \"5.75.00\",\n" +
                "        \"transaction_id\": \"2000000123323768\",\n" +
                "        \"quantity\": \"1\",\n" +
                "        \"in_app_ownership_type\": \"PURCHASED\",\n" +
                "        \"unique_vendor_identifier\": \"CCE67425-9735-48ED-9446-9780C764C53F\",\n" +
                "        \"item_id\": \"1146327652\",\n" +
                "        \"original_purchase_date\": \"2022-08-05 08:59:15 Etc/GMT\",\n" +
                "        \"is_in_intro_offer_period\": \"false\",\n" +
                "        \"product_id\": \"com.fanxing.fxappstore.800coin\",\n" +
                "        \"purchase_date\": \"2022-08-05 08:59:15 Etc/GMT\",\n" +
                "        \"is_trial_period\": \"false\",\n" +
                "        \"purchase_date_pst\": \"2022-08-05 01:59:15 America/Los_Angeles\",\n" +
                "        \"bid\": \"com.kugou.fanxingappstore\",\n" +
                "        \"original_purchase_date_ms\": \"1659689955819\"\n" +
                "    },\n" +
                "    \"status\": 0\n" +
                "}";
        Optional<ReceiptInfoBO> optionalReceiptInfoBO = this.appStoreReceiptService.parseReceipt(json);
        Assert.assertTrue(optionalReceiptInfoBO.isPresent());
    }

    @Test
    public void addSourceReceipt() {
        String rechargeOrderNum = "R09202012S1TID1000000756911994";
        when(orderIdService.generateGlobalId()).thenReturn(1L);
        when(appStoreReceiptDao.insertIgnore(any(AppStoreReceiptPO.class))).thenReturn(1);
        this.appStoreReceiptService.addSourceReceipt(rechargeOrderNum, receiptData);
        Assert.assertEquals(1, 1);
    }

}
