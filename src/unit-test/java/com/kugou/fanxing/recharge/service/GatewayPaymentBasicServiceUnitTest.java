package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.model.vo.GatewayPayRecordVo;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class GatewayPaymentBasicServiceUnitTest {

    @InjectMocks
    private GatewayPaymentBasicService gatewayPaymentBasicService;
    
    @Mock
    private ApolloConfigService apolloConfigService;

    @Test
    public void getSuccessOrdersFail() {
        String errorResult = "{\"error_code\":20010,\"error_msg\":\"startTime\\u6216endTime\\u53c2\\u6570\\u9519\\u8bef\",\"data\":\"\"}";
        try (MockedStatic<HttpClientUtils> mockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            mockedStatic.when(() -> HttpClientUtils.doSyncGet(anyString(), anyMap())).thenReturn(Optional.of(errorResult));
            List<GatewayPayRecordVo> recordVoList = gatewayPaymentBasicService.getSuccessOrders(1614565771, 1614565781);
            Assert.assertEquals(recordVoList.size(), 0);
        }
    }

    @Test
    public void getSuccessOrdersSuccess() {
        String response = "{\"error_code\":0,\"error_msg\":\"\",\"data\":[{\"order_no\":\"R092021030110292138440921\",\"trade_no\":\"4200000895202103017795924243\",\"out_trade_no\":\"02202103011029210100014602\",\"total_fee\":10,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:21\",\"pay_time\":\"2021-03-01 10:29:31\",\"clientip\":\"***************\"},{\"order_no\":\"R292021030110292277341764\",\"trade_no\":\"4200000904202103016585306346\",\"out_trade_no\":\"01202103011029229002700627\",\"total_fee\":30,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:23\",\"pay_time\":\"2021-03-01 10:29:32\",\"clientip\":\"240e:468:8fa0:b6c8:6\"},{\"order_no\":\"R292021030110292713362510\",\"trade_no\":\"4200000896202103012147767990\",\"out_trade_no\":\"01202103011029272502500624\",\"total_fee\":100,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:27\",\"pay_time\":\"2021-03-01 10:29:32\",\"clientip\":\"*************\"},{\"order_no\":\"R092021030110292716628910\",\"trade_no\":\"4200000880202103012514460750\",\"out_trade_no\":\"02202103011029270100012297\",\"total_fee\":300,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:27\",\"pay_time\":\"2021-03-01 10:29:33\",\"clientip\":\"***************\"},{\"order_no\":\"R092021030110292526099153\",\"trade_no\":\"4200000903202103010336099212\",\"out_trade_no\":\"02202103011029250100013225\",\"total_fee\":4,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:25\",\"pay_time\":\"2021-03-01 10:29:34\",\"clientip\":\"240e:431:ba26:900c::\"},{\"order_no\":\"R292021030110292557679597\",\"trade_no\":\"4200000933202103012056595640\",\"out_trade_no\":\"01202103011029250100017452\",\"total_fee\":1,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:25\",\"pay_time\":\"2021-03-01 10:29:35\",\"clientip\":\"**************\"},{\"order_no\":\"R292021030110292845650899\",\"trade_no\":\"4200000942202103011010977057\",\"out_trade_no\":\"01202103011029280100015916\",\"total_fee\":30,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:28\",\"pay_time\":\"2021-03-01 10:29:34\",\"clientip\":\"240e:464:2c10:1e6a::\"},{\"order_no\":\"R092021030110291840263288\",\"trade_no\":\"4200000986202103018104918524\",\"out_trade_no\":\"02202103011029180100014537\",\"total_fee\":10,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:18\",\"pay_time\":\"2021-03-01 10:29:39\",\"clientip\":\"*************\"},{\"order_no\":\"R092021030110292802575086\",\"trade_no\":\"4200000889202103012569281416\",\"out_trade_no\":\"02202103011029280100011018\",\"total_fee\":30,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:28\",\"pay_time\":\"2021-03-01 10:29:38\",\"clientip\":\"************\"},{\"order_no\":\"R092021030110293040319734\",\"trade_no\":\"4200000940202103015035273822\",\"out_trade_no\":\"02202103011029300100013319\",\"total_fee\":10,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:30\",\"pay_time\":\"2021-03-01 10:29:40\",\"clientip\":\"************\"},{\"order_no\":\"R092021030110293168904840\",\"trade_no\":\"4200000936202103016835834012\",\"out_trade_no\":\"02202103011029310100017505\",\"total_fee\":124,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:31\",\"pay_time\":\"2021-03-01 10:29:39\",\"clientip\":\"*************\"},{\"order_no\":\"R092021030110293254036641\",\"trade_no\":\"4200000897202103014875941375\",\"out_trade_no\":\"02202103011029320100016141\",\"total_fee\":30,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:32\",\"pay_time\":\"2021-03-01 10:29:40\",\"clientip\":\"**************\"},{\"order_no\":\"R092021030110293301747596\",\"trade_no\":\"4200000902202103014273718565\",\"out_trade_no\":\"02202103011029330100011220\",\"total_fee\":10,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:33\",\"pay_time\":\"2021-03-01 10:29:40\",\"clientip\":\"***************\"},{\"order_no\":\"R292021030110292688478621\",\"trade_no\":\"4200000877202103017376357172\",\"out_trade_no\":\"01202103011029270100010100\",\"total_fee\":30,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:27\",\"pay_time\":\"2021-03-01 10:29:36\",\"clientip\":\"**************\"},{\"order_no\":\"R292021030110292857897877\",\"trade_no\":\"4200000904202103013440704111\",\"out_trade_no\":\"01202103011029287153000624\",\"total_fee\":1,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:28\",\"pay_time\":\"2021-03-01 10:29:36\",\"clientip\":\"***************\"},{\"order_no\":\"R292021030110293011104161\",\"trade_no\":\"4200000878202103018410261552\",\"out_trade_no\":\"01202103011029302364500620\",\"total_fee\":8,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:30\",\"pay_time\":\"2021-03-01 10:29:40\",\"clientip\":\"***************\"},{\"order_no\":\"R292021030110293145393911\",\"trade_no\":\"4200000935202103013602193106\",\"out_trade_no\":\"01202103011029310100015905\",\"total_fee\":1,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:31\",\"pay_time\":\"2021-03-01 10:29:38\",\"clientip\":\"*************\"}]}";
        try (MockedStatic<HttpClientUtils> mockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            mockedStatic.when(() -> HttpClientUtils.doSyncGet(anyString(), anyMap())).thenReturn(Optional.of(response));
            ReflectionTestUtils.setField(gatewayPaymentBasicService, "SUCCESS_ORDERS_URL", "http://kupay.kugou.com/v1/orderstatus/getbypaytime");
            List<GatewayPayRecordVo> successRecordVoList = gatewayPaymentBasicService.getSuccessOrders(1614565771, 1614565781);
            successRecordVoList = successRecordVoList.stream()
                    .filter(gatewayPayRecordVo -> StringUtils.startsWith(gatewayPayRecordVo.getOrderNo(), "R"))
                    .collect(Collectors.toList());
            Assert.assertEquals(0, successRecordVoList.size());
        }
    }

    @Test
    public void getMultiAppidSuccessOrdersSuccess() {
        String response = "{\"error_code\":0,\"error_msg\":\"\",\"data\":[{\"order_no\":\"R092021030110292138440921\",\"trade_no\":\"4200000895202103017795924243\",\"out_trade_no\":\"02202103011029210100014602\",\"total_fee\":10,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:21\",\"pay_time\":\"2021-03-01 10:29:31\",\"clientip\":\"***************\"},{\"order_no\":\"R292021030110292277341764\",\"trade_no\":\"4200000904202103016585306346\",\"out_trade_no\":\"01202103011029229002700627\",\"total_fee\":30,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:23\",\"pay_time\":\"2021-03-01 10:29:32\",\"clientip\":\"240e:468:8fa0:b6c8:6\"},{\"order_no\":\"R292021030110292713362510\",\"trade_no\":\"4200000896202103012147767990\",\"out_trade_no\":\"01202103011029272502500624\",\"total_fee\":100,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:27\",\"pay_time\":\"2021-03-01 10:29:32\",\"clientip\":\"*************\"},{\"order_no\":\"R092021030110292716628910\",\"trade_no\":\"4200000880202103012514460750\",\"out_trade_no\":\"02202103011029270100012297\",\"total_fee\":300,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:27\",\"pay_time\":\"2021-03-01 10:29:33\",\"clientip\":\"***************\"},{\"order_no\":\"R092021030110292526099153\",\"trade_no\":\"4200000903202103010336099212\",\"out_trade_no\":\"02202103011029250100013225\",\"total_fee\":4,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:25\",\"pay_time\":\"2021-03-01 10:29:34\",\"clientip\":\"240e:431:ba26:900c::\"},{\"order_no\":\"R292021030110292557679597\",\"trade_no\":\"4200000933202103012056595640\",\"out_trade_no\":\"01202103011029250100017452\",\"total_fee\":1,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:25\",\"pay_time\":\"2021-03-01 10:29:35\",\"clientip\":\"**************\"},{\"order_no\":\"R292021030110292845650899\",\"trade_no\":\"4200000942202103011010977057\",\"out_trade_no\":\"01202103011029280100015916\",\"total_fee\":30,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:28\",\"pay_time\":\"2021-03-01 10:29:34\",\"clientip\":\"240e:464:2c10:1e6a::\"},{\"order_no\":\"R092021030110291840263288\",\"trade_no\":\"4200000986202103018104918524\",\"out_trade_no\":\"02202103011029180100014537\",\"total_fee\":10,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:18\",\"pay_time\":\"2021-03-01 10:29:39\",\"clientip\":\"*************\"},{\"order_no\":\"R092021030110292802575086\",\"trade_no\":\"4200000889202103012569281416\",\"out_trade_no\":\"02202103011029280100011018\",\"total_fee\":30,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:28\",\"pay_time\":\"2021-03-01 10:29:38\",\"clientip\":\"************\"},{\"order_no\":\"R092021030110293040319734\",\"trade_no\":\"4200000940202103015035273822\",\"out_trade_no\":\"02202103011029300100013319\",\"total_fee\":10,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:30\",\"pay_time\":\"2021-03-01 10:29:40\",\"clientip\":\"************\"},{\"order_no\":\"R092021030110293168904840\",\"trade_no\":\"4200000936202103016835834012\",\"out_trade_no\":\"02202103011029310100017505\",\"total_fee\":124,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:31\",\"pay_time\":\"2021-03-01 10:29:39\",\"clientip\":\"*************\"},{\"order_no\":\"R092021030110293254036641\",\"trade_no\":\"4200000897202103014875941375\",\"out_trade_no\":\"02202103011029320100016141\",\"total_fee\":30,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:32\",\"pay_time\":\"2021-03-01 10:29:40\",\"clientip\":\"**************\"},{\"order_no\":\"R092021030110293301747596\",\"trade_no\":\"4200000902202103014273718565\",\"out_trade_no\":\"02202103011029330100011220\",\"total_fee\":10,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:33\",\"pay_time\":\"2021-03-01 10:29:40\",\"clientip\":\"***************\"},{\"order_no\":\"R292021030110292688478621\",\"trade_no\":\"4200000877202103017376357172\",\"out_trade_no\":\"01202103011029270100010100\",\"total_fee\":30,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:27\",\"pay_time\":\"2021-03-01 10:29:36\",\"clientip\":\"**************\"},{\"order_no\":\"R292021030110292857897877\",\"trade_no\":\"4200000904202103013440704111\",\"out_trade_no\":\"01202103011029287153000624\",\"total_fee\":1,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:28\",\"pay_time\":\"2021-03-01 10:29:36\",\"clientip\":\"***************\"},{\"order_no\":\"R292021030110293011104161\",\"trade_no\":\"4200000878202103018410261552\",\"out_trade_no\":\"01202103011029302364500620\",\"total_fee\":8,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:30\",\"pay_time\":\"2021-03-01 10:29:40\",\"clientip\":\"***************\"},{\"order_no\":\"R292021030110293145393911\",\"trade_no\":\"4200000935202103013602193106\",\"out_trade_no\":\"01202103011029310100015905\",\"total_fee\":1,\"trade_status\":1,\"callback_status\":1,\"order_time\":\"2021-03-01 10:29:31\",\"pay_time\":\"2021-03-01 10:29:38\",\"clientip\":\"*************\"}]}";
        try (MockedStatic<HttpClientUtils> mockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            mockedStatic.when(() -> HttpClientUtils.doSyncGet(anyString(), anyMap())).thenReturn(Optional.of(response));
            ReflectionTestUtils.setField(gatewayPaymentBasicService, "SUCCESS_ORDERS_URL", "http://kupay.kugou.com/v1/orderstatus/getbypaytime");
            Mockito.when(apolloConfigService.getKupayOrderAppids()).thenReturn(Arrays.asList("1084"));
            List<GatewayPayRecordVo> successRecordVoList = gatewayPaymentBasicService.getSuccessOrders(1614565771, 1614565781);
            successRecordVoList = successRecordVoList.stream()
                    .filter(gatewayPayRecordVo -> StringUtils.startsWith(gatewayPayRecordVo.getOrderNo(), "R"))
                    .collect(Collectors.toList());
            Assert.assertEquals(0, successRecordVoList.size());
        }
    }
}
