package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.thrift.idmapping.user.UserIdMappingService;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class GetKugouIdByUserIdCommandUnitTest {

    @Mock
    private UserIdMappingService.Iface userIdMappingService;

    @Test
    public void getKugouIdByUserId() throws TException {
        when(userIdMappingService.getKugouIdByUserId(anyLong())).thenReturn(1290249156L);
        GetKugouIdByUserIdCommand command = new GetKugouIdByUserIdCommand(userIdMappingService, 1290249156L);
        Optional<Long> optionalKugouId = command.execute();
        long kugouId = optionalKugouId.orElseThrow(() -> new ContextedRuntimeException("test"));
        Assert.assertEquals(1290249156L, kugouId);
    }

    @Test
    public void getKugouIdByUserIdException() throws TException {
        when(userIdMappingService.getKugouIdByUserId(anyLong())).thenThrow(new ContextedRuntimeException("test"));
        GetKugouIdByUserIdCommand command = new GetKugouIdByUserIdCommand(userIdMappingService, 1290249156L);
        Optional<Long> optionalKugouId = command.execute();
        Assert.assertFalse(optionalKugouId.isPresent());
    }
}
