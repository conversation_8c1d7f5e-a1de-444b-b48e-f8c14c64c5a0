package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.model.vo.FapiaoRechargeInfoVO;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.thrift.pay.v2.FapiaoRechargeInfo;
import com.kugou.fanxing.thrift.pay.v2.PlatformPayV2Service;
import com.kugou.fanxing.thrift.pay.v2.QueryFapiaoRechargeListResponse;
import com.kugou.fanxing.thrift.pay.v2.QueryFapiaoRechargeListV2Request;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class FapiaoServiceUnitTest {

    @InjectMocks
    private FapiaoService fapiaoService;
    @Mock
    private PlatformPayV2Service.Iface platformPayV2Service;
    @Mock
    private ApolloConfigService apolloConfigService;

    @Test
    @SneakyThrows
    public void parseDate() {
        Date date = DateUtils.parseDate("2020-04-04 12:23:30", "yyyy-MM-dd HH:mm:ss");
        Assert.assertNotNull(date);
        Assert.assertEquals("2020-04-01 00:00:00", DateHelper.format(DateHelper.atStartOfMonth(date)));
        Assert.assertEquals("2020-04-30 23:59:59", DateHelper.format(DateHelper.atEndOfMonth(date)));
    }

    @Test
    @SneakyThrows
    public void getFapiaoRechargeListNoData() {
        QueryFapiaoRechargeListResponse response = new QueryFapiaoRechargeListResponse();
        response.setCode(0);
        response.setData(Lists.newArrayList(new FapiaoRechargeInfo()));
        when( this.platformPayV2Service.queryFapiaoRechargeListV2(any(QueryFapiaoRechargeListV2Request.class))).thenReturn(response);
        when(apolloConfigService.getFapiaoRechargeListPageSize()).thenReturn(100L);
        when(apolloConfigService.isAppStoreRecharge(anyInt())).thenReturn(false);
        JsonResult<Map<String, Object>> jsonResult = this.fapiaoService.getFapiaoRechargeList(0, 1290249156L, 0);
        Assert.assertEquals(0, jsonResult.getCode());
    }

    @Test
    @SneakyThrows
    public void getFapiaoRechargeListTmall(){
        FapiaoRechargeInfo fapiaoRechargeInfo = new FapiaoRechargeInfo();
        fapiaoRechargeInfo.setRechargeId(1L);
        fapiaoRechargeInfo.setOrderNo("orderNo");
        fapiaoRechargeInfo.setTradeNo("tradeNo");
        fapiaoRechargeInfo.setOutTradeNo("outTradeNo");
        fapiaoRechargeInfo.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_1012.getPayTypeId());
        QueryFapiaoRechargeListResponse response = new QueryFapiaoRechargeListResponse();
        response.setCode(0);
        response.setData(Lists.newArrayList(fapiaoRechargeInfo));
        when( this.platformPayV2Service.queryFapiaoRechargeListV2(any(QueryFapiaoRechargeListV2Request.class))).thenReturn(response);
        when(apolloConfigService.getFapiaoRechargeListPageSize()).thenReturn(100L);
        when(apolloConfigService.isAppStoreRecharge(anyInt())).thenReturn(false);
        JsonResult<Map<String, Object>> jsonResult = this.fapiaoService.getFapiaoRechargeList(0, 1290249156L, -1);
        Assert.assertEquals(0, jsonResult.getCode());
        Map<String, Object> payload = jsonResult.getData();
        List<FapiaoRechargeInfoVO> fapiaoRechargeInfoVOList = (List<FapiaoRechargeInfoVO>) payload.get("rechargeInfos");
        Assert.assertEquals(1, fapiaoRechargeInfoVOList.size());
        Assert.assertEquals("tradeNo", fapiaoRechargeInfoVOList.get(0).getOrderNo());
    }

}
