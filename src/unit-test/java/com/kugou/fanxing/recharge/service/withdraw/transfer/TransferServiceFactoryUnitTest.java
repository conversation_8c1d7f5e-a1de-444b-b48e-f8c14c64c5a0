package com.kugou.fanxing.recharge.service.withdraw.transfer;

import com.kugou.fanxing.recharge.service.withdraw.AlipayResp;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class TransferServiceFactoryUnitTest {
    @Mock
    private ApplicationContext applicationContext;
    @InjectMocks
    private TransferServiceFactory transferServiceFactory;
    @Test
    public void createTransferService() {
        AlipayResp.AlipayResult alipayResult = new AlipayResp.AlipayResult();
        alipayResult.setTransfer_status(4);
        AlipayResp alipayResp = new AlipayResp();
        alipayResp.setError_code(1);
        alipayResp.setData(alipayResult);
        when(applicationContext.getBean(any(Class.class))).thenReturn(new TransferCanceledService());
        Optional<TransferService> optionalTransferService = this.transferServiceFactory.createTransferService(alipayResp);
        Assert.assertTrue(optionalTransferService.isPresent());
    }

    @Test
    public void createTransferService_1() {
        AlipayResp.AlipayResult alipayResult = new AlipayResp.AlipayResult();
        alipayResult.setTransfer_status(4);
        AlipayResp alipayResp = new AlipayResp();
        alipayResp.setStatus(1);
        alipayResp.setError_code(0);
        alipayResp.setData(null);
        when(applicationContext.getBean(any(Class.class))).thenReturn(new TransferNotExistsService());
        Optional<TransferService> optionalTransferService = this.transferServiceFactory.createTransferService(alipayResp);
        Assert.assertTrue(optionalTransferService.isPresent());
    }

    @Test
    public void createTransferService_2() {
        AlipayResp.AlipayResult alipayResult = new AlipayResp.AlipayResult();
        alipayResult.setTransfer_status(1);
        AlipayResp alipayResp = new AlipayResp();
        alipayResp.setStatus(1);
        alipayResp.setError_code(0);
        alipayResp.setData(alipayResult);
        when(applicationContext.getBean(any(Class.class))).thenReturn(new TransferSuccessService());
        Optional<TransferService> optionalTransferService = this.transferServiceFactory.createTransferService(alipayResp);
        Assert.assertTrue(optionalTransferService.isPresent());
    }

    @Test
    public void createTransferService_3() {
        AlipayResp.AlipayResult alipayResult = new AlipayResp.AlipayResult();
        alipayResult.setTransfer_status(0);
        AlipayResp alipayResp = new AlipayResp();
        alipayResp.setStatus(1);
        alipayResp.setError_code(0);
        alipayResp.setData(alipayResult);
        when(applicationContext.getBean(any(Class.class))).thenReturn(new TransferProcessService());
        Optional<TransferService> optionalTransferService = this.transferServiceFactory.createTransferService(alipayResp);
        Assert.assertTrue(optionalTransferService.isPresent());
    }

    @Test
    public void createTransferService_4() {
        AlipayResp.AlipayResult alipayResult = new AlipayResp.AlipayResult();
        alipayResult.setTransfer_status(4);
        AlipayResp alipayResp = new AlipayResp();
        alipayResp.setStatus(1);
        alipayResp.setError_code(0);
        alipayResp.setData(alipayResult);
        when(applicationContext.getBean(any(Class.class))).thenReturn(new TransferCanceledService());
        Optional<TransferService> optionalTransferService = this.transferServiceFactory.createTransferService(alipayResp);
        Assert.assertTrue(optionalTransferService.isPresent());
    }
}
