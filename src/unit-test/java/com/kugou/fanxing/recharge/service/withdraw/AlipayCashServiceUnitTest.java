package com.kugou.fanxing.recharge.service.withdraw;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawAccountWechatDao;
import com.kugou.fanxing.recharge.model.po.WithdrawAccountPO;
import com.kugou.fanxing.recharge.model.po.WithdrawAccountWechatPO;
import com.kugou.fanxing.recharge.model.po.WithdrawOrderPO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.common.DencryptService;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import com.kugou.rpc.client.http.HttpService;
import com.kugou.rpc.client.http.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.HttpUriRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AlipayCashServiceUnitTest {

    @Mock
    private HttpService httpService;
    @Mock
    private WithdrawAccountWechatDao withdrawAccountWechatDao;
    @Mock
    private ApolloConfigService apolloConfigService;
    @InjectMocks
    private AlipayCashService alipayCashService;
    @Mock
    private DencryptService dencryptService;

    @Before
    public void before() {

    }

    @Test
    public void withdraw() throws NoSuchFieldException {
        Field withdrawHostUrl = AlipayCashService.class.getDeclaredField("withdrawHostUrl");
        withdrawHostUrl.setAccessible(true);
        ReflectionUtils.setField(withdrawHostUrl, this.alipayCashService, "http://kupay.kugou.com/");
        Field withdrawServerId = AlipayCashService.class.getDeclaredField("withdrawServerId");
        withdrawServerId.setAccessible(true);
        ReflectionUtils.setField(withdrawServerId, this.alipayCashService, "1870");
        Field withdrawServerKey = AlipayCashService.class.getDeclaredField("withdrawServerKey");
        withdrawServerKey.setAccessible(true);
        ReflectionUtils.setField(withdrawServerKey, this.alipayCashService, "test");
        AlipayResp resp = new AlipayResp();
        resp.setData(null);
        resp.setStatus(1);
        resp.setError_code(0);
        Response response = new Response();
        response.setCode(200);
        response.setBody(JSON.toJSONString(resp));
        WithdrawOrderPO withdrawOrderPO = new WithdrawOrderPO();
        withdrawOrderPO.setTotalAmount(BigDecimal.valueOf(100));
        withdrawOrderPO.setOrderId(1366376686793184933L);
        withdrawOrderPO.setAppId(1000_0001);
        withdrawOrderPO.setBizAppId(1084);
        withdrawOrderPO.setKugouId(**********);
        withdrawOrderPO.setTotalAmount(BigDecimal.valueOf(100));
        withdrawOrderPO.setReqTime(DateHelper.getCurrentSeconds());
        withdrawOrderPO.setDrawTime(DateHelper.getCurrentSeconds() + 120);
        withdrawOrderPO.setOpenid("test");
        WithdrawClientParams withdrawClientParams = new WithdrawClientParams();
        withdrawClientParams.setDfid("-");
        withdrawClientParams.setUuid("");
        withdrawClientParams.setMid("FFFF");
        withdrawClientParams.setClientver("0");
        withdrawClientParams.setApplicationId("1010");
        withdrawClientParams.setClientip("127.0.0.1");
        when(dencryptService.decrypt(anyString())).thenReturn("test");
        when(httpService.execute(any(HttpUriRequest.class), anyString())).thenReturn(response);
        try (MockedStatic<HttpClientUtils> mockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            mockedStatic.when(() -> HttpClientUtils.doPostJsonBody(anyString(), anyMap(), anyString())).thenReturn(Optional.of(response.getBody()));
            Optional<AlipayResp> optionalAlipayResp = this.alipayCashService.withdraw(withdrawOrderPO, withdrawClientParams, null);
            Assert.assertTrue(optionalAlipayResp.isPresent());
            mockedStatic.when(() -> HttpClientUtils.doPostJsonBody(anyString(), anyMap(), anyString())).thenReturn(Optional.empty());
            optionalAlipayResp = this.alipayCashService.withdraw(withdrawOrderPO, withdrawClientParams, null);
            Assert.assertFalse(optionalAlipayResp.isPresent());
        }
    }

    @Test
    public void withdrawOrderQuery() throws NoSuchFieldException {
        Field withdrawHostUrl = AlipayCashService.class.getDeclaredField("withdrawHostUrl");
        withdrawHostUrl.setAccessible(true);
        ReflectionUtils.setField(withdrawHostUrl, this.alipayCashService, "http://kupay.kugou.com/");
        Field withdrawServerId = AlipayCashService.class.getDeclaredField("withdrawServerId");
        withdrawServerId.setAccessible(true);
        ReflectionUtils.setField(withdrawServerId, this.alipayCashService, "1870");
        Field withdrawServerKey = AlipayCashService.class.getDeclaredField("withdrawServerKey");
        withdrawServerKey.setAccessible(true);
        ReflectionUtils.setField(withdrawServerKey, this.alipayCashService, "test");
        WithdrawAccountPO withdrawAccountPO = new WithdrawAccountPO();
        WithdrawAccountWechatPO withdrawAccountWechatPO = new WithdrawAccountWechatPO();
        withdrawAccountWechatPO.setOpenid("aaa");
        when(this.withdrawAccountWechatDao.selectByKugouId(anyLong(), anyString())).thenReturn(withdrawAccountWechatPO);
        AlipayResp resp = new AlipayResp();
        resp.setData(null);
        resp.setStatus(1);
        resp.setError_code(0);
        Response response = new Response();
        response.setCode(200);
        response.setBody(JSON.toJSONString(resp));
        WithdrawOrderPO withdrawOrderPO = new WithdrawOrderPO();
        withdrawOrderPO.setTotalAmount(BigDecimal.valueOf(100));
        withdrawOrderPO.setOrderId(1366376686793184933L);
        withdrawOrderPO.setAppId(1000_0001);
        withdrawOrderPO.setBizAppId(1084);
        withdrawOrderPO.setKugouId(**********);
        withdrawOrderPO.setTotalAmount(BigDecimal.valueOf(100));
        withdrawOrderPO.setReqTime(DateHelper.getCurrentSeconds());
        withdrawOrderPO.setDrawTime(DateHelper.getCurrentSeconds() + 120);
        withdrawOrderPO.setOpenid("test");
        withdrawOrderPO.setBizAppId(3134);
        withdrawOrderPO.setKugouId(1260991360L);
        withdrawOrderPO.setOrderId(1366376686793184933L);
        WithdrawClientParams withdrawClientParams = new WithdrawClientParams();
        withdrawClientParams.setDfid("-");
        withdrawClientParams.setUuid("");
        withdrawClientParams.setMid("FFFF");
        withdrawClientParams.setClientver("0");
        withdrawClientParams.setApplicationId("1010");
        withdrawClientParams.setClientip("127.0.0.1");
        try (MockedStatic<HttpClientUtils> mockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            mockedStatic.when(() -> HttpClientUtils.doSyncGet(anyString(), anyMap())).thenReturn(Optional.of(response.getBody()));
            when(httpService.execute(any(HttpUriRequest.class), anyString())).thenReturn(response);
            when(apolloConfigService.getWxAppIdByBizAppId(anyInt())).thenReturn("wx0c3254305fc16258");
            Optional<AlipayResp> optionalAlipayResp = this.alipayCashService.withdrawOrderQuery(withdrawOrderPO);
            Assert.assertTrue(optionalAlipayResp.isPresent());
            mockedStatic.when(() -> HttpClientUtils.doSyncGet(anyString(), anyMap())).thenReturn(Optional.empty());
            optionalAlipayResp = this.alipayCashService.withdrawOrderQuery(withdrawOrderPO);
            Assert.assertFalse(optionalAlipayResp.isPresent());
        }
    }

    /**
     * appid=1010
     * clientver=0
     * dfid=-
     * mid=FFFF
     * serverid=1870
     * servertime=1606967880
     * uuid=
     * signature=48f4607e43961dc9e676029416bc5319
     *
     * params: {
     * "order_no":531840086086520831
     * "openid":"osVWIjimHCDYGdgCuf_0apng8xCM"
     * "clientip":"*********"
     * "total_fee":"100"
     * "remark":"酷狗直播提现"
     * "biz_appid":10015
     * }
     */
    @Test
    public void getWechatSign() {
        // 通用参数
        Map<String, String> treeMap = Maps.newTreeMap();
        treeMap.put("serverid", "1870");
        treeMap.put("servertime", "1606967880");
        treeMap.put("appid", "1010");
        treeMap.put("clientver", "0");
        treeMap.put("mid", "FFFF");
        treeMap.put("uuid", "");
        treeMap.put("dfid", "-");
        // 业务参数
        JSONObject params = new JSONObject();
        params.put("biz_appid", 10015);
        params.put("clientip", "*********");
        params.put("order_no", 531840086086520831L);
        params.put("openid", "osVWIjimHCDYGdgCuf_0apng8xCM");
        params.put("total_fee", "100");
        params.put("remark", "酷狗直播提现");
        ReflectionTestUtils.setField(alipayCashService, "withdrawServerKey", "rYyNI4G7BdzzCYfXww3eHaW1U0haNcqE");
        String actualSignature = this.alipayCashService.getCommandSign("rYyNI4G7BdzzCYfXww3eHaW1U0haNcqE", treeMap, params.toJSONString());
        log.warn("actualSignature: {}", actualSignature);
        Assert.assertEquals("fc058d8204c181e4ba4979c1ad49febf", actualSignature);
    }

}
