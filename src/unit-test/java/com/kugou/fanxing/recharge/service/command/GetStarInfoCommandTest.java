package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.star.api.StarInfoResp;
import com.kugou.fanxing.star.api.StarQueryService;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class GetStarInfoCommandTest {

    @Mock
    private StarQueryService.Iface starQueryService;

    @Test
    public void confirm() throws TException {
        StarInfoResp result = new StarInfoResp();
        result.setCode(0);
        when(starQueryService.getStarInfo(anyLong())).thenReturn(result);
        GetStarInfoCommand command = new GetStarInfoCommand(starQueryService, 1540712049L);
        Optional<StarInfoResp> optionalStarInfoRespResult = command.execute();
        StarInfoResp starInfoResp = optionalStarInfoRespResult.orElseThrow(() -> new ContextedRuntimeException("test"));
        Assert.assertEquals(0, starInfoResp.getCode());
    }

    @Test
    public void confirmException() throws TException {
        when(starQueryService.getStarInfo(anyLong())).thenThrow(new ContextedRuntimeException("test"));
        GetStarInfoCommand command = new GetStarInfoCommand(starQueryService, 0L);
        Optional<StarInfoResp> optionalStarInfoResp = command.execute();
        Assert.assertFalse(optionalStarInfoResp.isPresent());
    }
}
