package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.thrift.plat.user.UserPlatBizService;
import com.kugou.fanxing.thrift.plat.user.vo.ResMsg;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class GetUserInfoFromKugouV1CommandUnitTest {

    @Mock
    private UserPlatBizService.Iface userPlatBizService;

    @Test
    public void getUserInfoFromKugouV1() throws TException {
        ResMsg resMsg = new ResMsg();
        resMsg.setData("1290249156");
        resMsg.setResponseCode("11000");
        when(userPlatBizService.getUserInfoFromKugouV1(anyLong())).thenReturn(resMsg);
        GetUserInfoFromKugouV1Command command = new GetUserInfoFromKugouV1Command(userPlatBizService, 1290249156L);
        Optional<String> optional = command.execute();
        Assert.assertTrue(optional.isPresent());
        Assert.assertEquals("1290249156", optional.get());
        when(userPlatBizService.getUserInfoFromKugouV1(anyLong())).thenReturn(null);
        command = new GetUserInfoFromKugouV1Command(userPlatBizService, 1290249156L);
        Assert.assertFalse(command.execute().isPresent());
    }

    @Test
    public void getUserInfoFromKugouV1Exception() throws TException {
        when(userPlatBizService.getUserInfoFromKugouV1(anyLong())).thenThrow(new ContextedRuntimeException());
        GetUserInfoFromKugouV1Command command = new GetUserInfoFromKugouV1Command(userPlatBizService, 1290249156L);
        Assert.assertFalse(command.execute().isPresent());
    }

}
