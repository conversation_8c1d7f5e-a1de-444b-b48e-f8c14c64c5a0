package com.kugou.fanxing.recharge.service.withdraw;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.Silent.class)
public class AlipayCancelDrawCashRespUnitTest {

    @Test
    public void isCancelSuccess() {
        AlipayCancelDrawCashResp resp = new AlipayCancelDrawCashResp();
        resp.setStatus(1);
        resp.setError_code(0);
        Assert.assertTrue(resp.isCancelSuccess());
    }

    @Test
    public void isCancelForbidden() {
        AlipayCancelDrawCashResp resp = new AlipayCancelDrawCashResp();
        resp.setStatus(0);
        resp.setError_code(30970);
        Assert.assertTrue(resp.isCancelForbidden());
    }

}
