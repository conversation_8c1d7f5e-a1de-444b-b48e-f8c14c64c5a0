package com.kugou.fanxing.recharge.service;

import com.ctrip.framework.apollo.Config;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeBigRebateConfigDao;
import com.kugou.fanxing.recharge.model.po.RechargeBigRebateConfigPO;
import com.kugou.fanxing.recharge.model.vo.RechargeAmountGear;
import com.kugou.fanxing.recharge.model.vo.RechargeAmountPresent;
import com.kugou.fanxing.recharge.model.vo.RechargeOptionItem;
import com.kugou.platform.after.recharge.asset.allocate.thrift.AfterRechargeAssetAllocateReadService;
import com.kugou.platform.after.recharge.asset.allocate.thrift.GetRechargePresentRequest;
import com.kugou.platform.after.recharge.asset.allocate.thrift.PresentInfo;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class RechargeOptionServiceUnitTest {

    @InjectMocks
    private RechargeOptionService rechargeOptionService;

    @Mock
    private AfterRechargeAssetAllocateReadService.Iface afterRechargeAssetAllocateReadService;

    @Mock
    private Config config;

    @Mock
    private RechargeBigRebateConfigDao rechargeBigRebateConfigDao;

    @Test
    public void testGetBySpecialNum() throws TException {
        //小额返点
        Long num = 1000L;
        //大于10W为大额
        when(config.getIntProperty(anyString(), anyInt())).thenReturn(100000);
        //充值后返回状态码不对
        when(afterRechargeAssetAllocateReadService.getRechargePresentInfo(any(GetRechargePresentRequest.class))).thenReturn(new PresentInfo().setRet(1));
        //降级不展示奖励，只展示星币数及充值金额
        RechargeOptionItem item1 = rechargeOptionService.getBySpecialNum(num, 0L, 0);
        assertThat(item1.getMoney()).isEqualTo(num);
        assertThat(item1.getCoins()).isEqualTo(num * 100);
        //充值后返回结果为空
        when(afterRechargeAssetAllocateReadService.getRechargePresentInfo(any(GetRechargePresentRequest.class))).thenReturn(new PresentInfo().setRet(0).setData(null));
        //降级不展示奖励，只展示星币数及充值金额
        RechargeOptionItem item2 = rechargeOptionService.getBySpecialNum(num, 0L, 0);
        assertThat(item2.getPresentList()).isEmpty();
        assertThat(item2.getMoney()).isEqualTo(num);
        assertThat(item2.getCoins()).isEqualTo(num * 100);

        //大额返点
        num = 100000L;
        when(afterRechargeAssetAllocateReadService.getRechargePresentInfo(any(GetRechargePresentRequest.class))).thenReturn(new PresentInfo().setRet(0).setData(null));
        when(rechargeBigRebateConfigDao.getConfig(any())).thenReturn(new RechargeBigRebateConfigPO().setRebateRate(new BigDecimal("0.1")));
        RechargeOptionItem item3 = rechargeOptionService.getBySpecialNum(num, 0L, 0);
        assertThat(item3.getPresentList()).isNotEmpty();
        assertThat(item3.getPresentList().size()).isEqualTo(1);
        assertThat(item3.getPresentList().get(0).getNum()).isEqualTo(num * 10);
        assertThat(item3.getMoney()).isEqualTo(num);
        assertThat(item3.getCoins()).isEqualTo(num * 100);
    }

    @Test
    public void getRechargePresentList() {
        when(config.getProperty(anyString(), anyString())).thenReturn("");
        RechargeAmountPresent rechargeAmountPresent = this.rechargeOptionService.getRechargePresentList(1);
        assertThat(rechargeAmountPresent.getRuleUrl()).isNotNull();
    }

    @Test
    public void getRechargeAmountGear() {
        when(config.getProperty(anyString(), anyString())).thenReturn("");
        List<RechargeAmountGear> defaultGearList1 = this.rechargeOptionService.getRechargeAmountGear(10);
        Assert.assertEquals(5, defaultGearList1.size());
        when(config.getProperty(anyString(), anyString())).thenReturn("*@6R#");
        List<RechargeAmountGear> defaultGearList2 = this.rechargeOptionService.getRechargeAmountGear(10);
        Assert.assertEquals(5, defaultGearList2.size());
    }
}
