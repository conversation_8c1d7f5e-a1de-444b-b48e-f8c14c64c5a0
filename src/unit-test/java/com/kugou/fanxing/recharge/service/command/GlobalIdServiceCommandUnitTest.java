package com.kugou.fanxing.recharge.service.command;

import com.kugou.api.springcloud.GlobalIdServiceThrift.GlobalIdService;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class GlobalIdServiceCommandUnitTest {

    @Mock
    private GlobalIdService.Iface globalIdService;

    @Test
    public void getUserInfoFromKugouV1() throws TException {
        when(globalIdService.get()).thenReturn(1L);
        GlobalIdServiceCommand command = new GlobalIdServiceCommand(globalIdService);
        long globalId = command.execute();
        Assert.assertEquals(1L, globalId);
    }

    @Test
    public void getUserInfoFromKugouV1Exception() throws TException {
        when(globalIdService.get()).thenThrow(new ContextedRuntimeException());
        GlobalIdServiceCommand command = new GlobalIdServiceCommand(globalIdService);
        long globalId = command.execute();
        Assert.assertTrue(globalId > 0);
    }
}
