package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.coupon.thrift.CouponService;
import com.kugou.fanxing.coupon.thrift.OperResult;
import com.kugou.fanxing.coupon.thrift.ReturnResult;
import com.kugou.fanxing.coupon.thrift.UnFreezeVO;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class UnFreezeCouponCommandUnitTest {

    @Mock
    private CouponService.Iface couponService;

    private UnFreezeVO unFreezeVO;

    @Before
    public void before() {
        unFreezeVO = new UnFreezeVO();
        unFreezeVO.setKugouId(1290249156L);
        unFreezeVO.setActionId(1);
    }

    @Test
    public void cancel() throws TException {
        ReturnResult returnResult = new ReturnResult();
        returnResult.setCode(0);
        returnResult.setData(new OperResult().setResult(0).setOrderId(1L));
        when(couponService.cancel(any(UnFreezeVO.class))).thenReturn(returnResult);
        UnFreezeCouponCommand command = new UnFreezeCouponCommand(couponService, unFreezeVO);
        Assert.assertTrue(command.execute());
        when(couponService.cancel(any(UnFreezeVO.class))).thenReturn(null);
        command = new UnFreezeCouponCommand(couponService, unFreezeVO);
        Assert.assertFalse(command.execute());
    }

    @Test
    public void cancelException() throws TException {
        when(couponService.cancel(any(UnFreezeVO.class))).thenThrow(new ContextedRuntimeException());
        UnFreezeCouponCommand command = new UnFreezeCouponCommand(couponService, unFreezeVO);
        Assert.assertFalse(command.execute());
    }
}
