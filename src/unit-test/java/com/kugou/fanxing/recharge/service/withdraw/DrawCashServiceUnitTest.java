package com.kugou.fanxing.recharge.service.withdraw;

import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.biz.commons.util.SnowFlake;
import com.kugou.fanxing.recharge.constant.DrawTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawAccountDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawAccountWechatDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawOrderDao;
import com.kugou.fanxing.recharge.model.po.WithdrawAccountPO;
import com.kugou.fanxing.recharge.model.po.WithdrawAccountWechatPO;
import com.kugou.fanxing.recharge.model.po.WithdrawOrderPO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.RemoteStrategyService;
import com.kugou.fanxing.recharge.service.common.BudgetPoolCheck;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.withdraw.thrift.CreateWithdrawOrderRequest;
import com.kugou.fanxing.recharge.withdraw.thrift.CreateWithdrawOrderV2Request;
import com.kugou.fanxing.recharge.withdraw.thrift.WithdrawOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class DrawCashServiceUnitTest {

    private static final String TOKEN = "7e7f4e86b599b6d1ee8a8180635fff622459a83068384def0a77af953bfdb36e";

    @InjectMocks
    private DrawCashService drawCashService;
    @Mock
    private WithdrawAccountDao withdrawAccountDao;
    @Mock
    private WithdrawOrderDao withdrawOrderDao;
    @Mock
    private ApolloConfigService apolloConfigService;
    @Mock
    private BudgetPoolCheck budgetPoolCheck;
    @Mock
    private WithdrawAccountWechatDao withdrawAccountWechatDao;
    @Mock
    private RemoteStrategyService remoteStrategyService;
    @Mock
    private CashServiceFactory cashServiceFactory;


    @Test
    public void buildWithdrawOrderPOMin() {
        WithdrawOrderPO withdrawOrderPO = this.drawCashService.buildWithdrawOrderPO(
                new CreateWithdrawOrderRequest().setTotalAmount(1), new WithdrawAccountPO(), new WithdrawClientParams());
        log.warn("totalAmount: {}", withdrawOrderPO.getTotalAmount().toPlainString());
        Assert.assertEquals("0.01", withdrawOrderPO.getTotalAmount().toPlainString());
    }

    @Test
    public void buildWithdrawOrderPONormal() {
        WithdrawOrderPO withdrawOrderPO = this.drawCashService.buildWithdrawOrderPO(
                new CreateWithdrawOrderRequest().setTotalAmount(123456), new WithdrawAccountPO(), new WithdrawClientParams());
        log.warn("totalAmount: {}", withdrawOrderPO.getTotalAmount().toPlainString());
        Assert.assertEquals("1234.56", withdrawOrderPO.getTotalAmount().toPlainString());
    }

    @Test
    public void queryKugouIdAccount() {
        when(this.withdrawAccountDao.getAccountNumByFingerprint(anyString())).thenReturn(Lists.newArrayList());
        Optional<List<WithdrawAccountPO>> optionalWithdrawAccountPOList = this.drawCashService.queryKugouIdAccount("test");
        Assert.assertTrue(optionalWithdrawAccountPOList.isPresent());
    }

    @Test
    public void queryWithdrawOrderByOrderId() {
        when(this.withdrawOrderDao.getDrawCashOrderById(anyLong())).thenReturn(null);
        Optional<WithdrawOrderDTO> optionalWithdrawOrderDTO = this.drawCashService.queryWithdrawOrderByOrderId(1L);
        Assert.assertFalse(optionalWithdrawOrderDTO.isPresent());
    }

    @Test
    public void drawCashV2AmountLimit() {
        CreateWithdrawOrderV2Request request = this.buildCreateWithdrawOrderV2Request();
        request.setTotalAmount(99);
        when(apolloConfigService.getWithdrawMaxAmount()).thenReturn(10000L);
        SysResultCode sysResultCode = this.drawCashService.drawCashV2(request);
        Assert.assertEquals(SysResultCode.WITHDRAW_ORDER_ILLEGAL_AMOUNT, sysResultCode);
    }

    @Test
    public void drawCashV2AmountBizAppId() {
        CreateWithdrawOrderV2Request request = this.buildCreateWithdrawOrderV2Request();
        when(apolloConfigService.getWithdrawMaxAmount()).thenReturn(10000L);
        when(apolloConfigService.permitWithdrawBizAppId(anyInt())).thenReturn(false);
        SysResultCode sysResultCode = this.drawCashService.drawCashV2(request);
        Assert.assertEquals(SysResultCode.WITHDRAW_ORDER_NOT_REGISTER, sysResultCode);
    }

    @Test
    public void drawCashV2ErrorDrawType() {
        CreateWithdrawOrderV2Request request = this.buildCreateWithdrawOrderV2Request();
        request.setDrawType(3);
        when(apolloConfigService.getWithdrawMaxAmount()).thenReturn(10000L);
        when(apolloConfigService.permitWithdrawBizAppId(anyInt())).thenReturn(true);
        when(this.withdrawOrderDao.getDrawCashOrderById(anyLong())).thenReturn(new WithdrawOrderPO());
        SysResultCode sysResultCode = this.drawCashService.drawCashV2(request);
        Assert.assertEquals(SysResultCode.WITHDRAW_ORDER_ILLEGAL_TYPE, sysResultCode);
    }

    @Test
    public void drawCashV2AlreadyExists() {
        CreateWithdrawOrderV2Request request = this.buildCreateWithdrawOrderV2Request();
        when(apolloConfigService.getWithdrawMaxAmount()).thenReturn(10000L);
        when(apolloConfigService.permitWithdrawBizAppId(anyInt())).thenReturn(true);
        when(this.withdrawOrderDao.getDrawCashOrderById(anyLong())).thenReturn(new WithdrawOrderPO());
        SysResultCode sysResultCode = this.drawCashService.drawCashV2(request);
        Assert.assertEquals(SysResultCode.SUCCESS, sysResultCode);
    }

    @Test
    public void drawCashV2Exception() {
        CreateWithdrawOrderV2Request request = this.buildCreateWithdrawOrderV2Request();
        when(apolloConfigService.getWithdrawMaxAmount()).thenReturn(10000L);
        when(apolloConfigService.permitWithdrawBizAppId(anyInt())).thenReturn(true);
        when(this.withdrawOrderDao.getDrawCashOrderById(anyLong())).thenReturn(null);
        when(budgetPoolCheck.tryDecreaseBudgetOrder(anyInt(),anyInt(),any(),anyLong(),anyLong())).thenReturn(true);
        WithdrawAccountWechatPO withdrawAccountWechatPO = new WithdrawAccountWechatPO();
        withdrawAccountWechatPO.setOpenid("111");
        when(withdrawAccountWechatDao.selectByKugouId(anyLong(),any())).thenReturn(withdrawAccountWechatPO);
        when(withdrawOrderDao.insert(any())).thenReturn(1);

        doThrow(new RuntimeException("异常")).when(budgetPoolCheck).confirmDecreaseBudgetOrder(anyInt(),anyLong(),anyLong());
       try {
           drawCashService.drawCashV2(request);
       }catch (Exception e)
       {
           Assert.assertEquals(SysResultCode.WITHDRAW_ORDER_EXCEPTION.getMsg(), e.getMessage());
       }
    }


    @Test
    public void drawCashV2NoBudget() {
        CreateWithdrawOrderV2Request request = this.buildCreateWithdrawOrderV2Request();
        when(apolloConfigService.getWithdrawMaxAmount()).thenReturn(10000L);
        when(apolloConfigService.permitWithdrawBizAppId(anyInt())).thenReturn(true);
        when(this.withdrawOrderDao.getDrawCashOrderById(anyLong())).thenReturn(null);
        when(budgetPoolCheck.tryDecreaseBudgetOrder(anyInt(),anyInt(),any(),anyLong(),anyLong())).thenReturn(false);
        SysResultCode sysResultCode = this.drawCashService.drawCashV2(request);
        Assert.assertEquals(SysResultCode.WITHDRAW_ORDER_NO_BUDGET, sysResultCode);
    }


    @Test
    public void drawCashV2budgetSuccess() {
        CreateWithdrawOrderV2Request request = this.buildCreateWithdrawOrderV2Request();
        when(apolloConfigService.getWithdrawMaxAmount()).thenReturn(10000L);
        when(apolloConfigService.permitWithdrawBizAppId(anyInt())).thenReturn(true);
        when(this.withdrawOrderDao.getDrawCashOrderById(anyLong())).thenReturn(null);
        when(budgetPoolCheck.tryDecreaseBudgetOrder(anyInt(),anyInt(),any(),anyLong(),anyLong())).thenReturn(true);
        WithdrawAccountWechatPO withdrawAccountWechatPO = new WithdrawAccountWechatPO();
        withdrawAccountWechatPO.setOpenid("111");
        when(withdrawAccountWechatDao.selectByKugouId(anyLong(),any())).thenReturn(withdrawAccountWechatPO);
        when(withdrawOrderDao.insert(any())).thenReturn(1);

        SysResultCode sysResultCode = this.drawCashService.drawCashV2(request);
        Assert.assertEquals(SysResultCode.SUCCESS, sysResultCode);
    }

    @Test
    public void drawCashV2budgetCancelSuccess() {
        CreateWithdrawOrderV2Request request = this.buildCreateWithdrawOrderV2Request();
        when(apolloConfigService.getWithdrawMaxAmount()).thenReturn(10000L);
        when(apolloConfigService.permitWithdrawBizAppId(anyInt())).thenReturn(true);
        when(this.withdrawOrderDao.getDrawCashOrderById(anyLong())).thenReturn(null);
        when(budgetPoolCheck.tryDecreaseBudgetOrder(anyInt(),anyInt(),any(),anyLong(),anyLong())).thenReturn(true);
        when(withdrawAccountWechatDao.selectByKugouId(anyLong(),any())).thenReturn(new WithdrawAccountWechatPO());
        when(withdrawOrderDao.insert(any())).thenReturn(1);

        SysResultCode sysResultCode = this.drawCashService.drawCashV2(request);
        Assert.assertEquals(SysResultCode.WITHDRAW_ACCOUNT_NOT_BIND, sysResultCode);
    }

    @Test
    public void filterNonRiskOrderTest(){
        WithdrawOrderPO withdrawOrderPO = this.drawCashService.buildWithdrawOrderPO(
                new CreateWithdrawOrderRequest().setTotalAmount(1), new WithdrawAccountPO(), new WithdrawClientParams());
        List<WithdrawOrderPO> withdrawOrderPOList=new ArrayList<>();
        withdrawOrderPOList.add(withdrawOrderPO);
        when(withdrawOrderDao.getInitialByPeriod(any(),anyInt(),anyInt(),anyInt())).thenReturn(withdrawOrderPOList);

        when(remoteStrategyService.strategyVerifyForWithdraw(any(),any())).thenReturn(true);
        Map<String, Object> result= drawCashService.triggerDelayWithdrawOrder();
        //被风控拦截了，所以返回是空
        Assert.assertEquals(0,result.size());
    }

    public CreateWithdrawOrderV2Request buildCreateWithdrawOrderV2Request() {
        CreateWithdrawOrderV2Request request = new CreateWithdrawOrderV2Request();
        request.setOrderId(generateGlobalId());
        request.setAppId(1000_0008);
        request.setBizAppId(10015);
        request.setKugouId(**********);
        request.setTotalAmount(100);
        request.setPid(0);
        request.setDrawType(DrawTypeEnum.DRAW_TYPE_WECHAT.getCode());
        request.setReqTime(DateHelper.getCurrentSeconds());
        request.setDrawTime(DateHelper.getCurrentSeconds() + 60);
        request.setClientIp("*********");
        request.setDfid("-");
        request.setUuid("");
        request.setMid("FFFF");
        request.setClientver("0");
        request.setApplicationId("1010");
        request.setToken(TOKEN);
        request.setSign(FinanceSignUtils.makeSign(request, "WB6mjkZPNKrzDv6G"));
        return request;
    }

    private long generateGlobalId() {
        return new SnowFlake(0, 0).nextId();
    }
}
