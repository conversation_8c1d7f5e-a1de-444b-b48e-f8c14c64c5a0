package com.kugou.fanxing.recharge.service.common;

import com.kugou.fanxing.thrift.certification.CertificationQueryRequest;
import com.kugou.fanxing.thrift.certification.CertificationQueryResult;
import com.kugou.fanxing.thrift.certification.CertificationVo;
import com.kugou.fanxing.thrift.certification.PlatformCertificationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class PlatformCertificationServiceImplUnitTest {
    @InjectMocks
    private PlatformCertificationServiceImpl platformCertificationServiceImpl;
    @Mock
    private PlatformCertificationService.Iface platformCertificationService;
    @Test
    public void hasCertified() throws TException {
        CertificationQueryResult result = new CertificationQueryResult();
        result.setRet(0);
        result.setMsg("success");
        result.setData(new CertificationVo().setStatus(2));
        when(platformCertificationService.findCertificationByKugouId(any(CertificationQueryRequest.class))).thenReturn(result);
        boolean hasCertified = platformCertificationServiceImpl.hasCertified(1290249156L);
        Assert.assertTrue(hasCertified);
    }
}
