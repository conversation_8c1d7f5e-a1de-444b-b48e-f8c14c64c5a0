package com.kugou.fanxing.recharge.service.common;

import com.kugou.fanxing.ip.api.FXIpService;
import com.kugou.fanxing.ip.api.IPInfoReq;
import com.kugou.fanxing.ip.api.SimpleIPInfoResp;
import com.kugou.fanxing.ip.api.SimpleIpInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class IpLocationServiceUnitTest {

    @InjectMocks
    private IpLocationService ipLocationService;
    @Mock
    private FXIpService.Iface fxIpService;

    @Test
    public void isOverseasIp() throws TException {
        SimpleIPInfoResp simpleIPInfoResp = new SimpleIPInfoResp();
        simpleIPInfoResp.setCode(0);
        simpleIPInfoResp.setSimpleIpInfo(new SimpleIpInfo().setCountry("美国"));
        when(fxIpService.getSimpleIPInfoByIp(any(IPInfoReq.class))).thenReturn(simpleIPInfoResp);
        Assert.assertTrue(this.ipLocationService.isOverseasIp("127.0.0.1"));
        simpleIPInfoResp.setSimpleIpInfo(new SimpleIpInfo().setCountry("中国"));
        when(fxIpService.getSimpleIPInfoByIp(any(IPInfoReq.class))).thenReturn(simpleIPInfoResp);
        Assert.assertFalse(this.ipLocationService.isOverseasIp("*********"));
    }

    @Test
    public void isOverseasIpException() throws TException {
        when(fxIpService.getSimpleIPInfoByIp(any(IPInfoReq.class))).thenThrow(new ContextedRuntimeException("test"));
        Assert.assertTrue(this.ipLocationService.isOverseasIp("*********"));
    }
}
