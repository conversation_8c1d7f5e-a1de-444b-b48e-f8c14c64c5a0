package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.bo.ServerOptionBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.PayPalRequest;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.service.common.IpLocationService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;


/**
 * <AUTHOR>
 */
@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class PayPalRechargeServiceUnitTest {

    @Mock
    private RechargeConfig rechargeConfig;
    @Mock
    private UserFacadeService userFacadeService;
    @Mock
    private RemoteFamilyControlService remoteFamilyControlService;
    @Mock
    private RechargeAcrossDao rechargeAcrossDao;
    @Mock
    private ApolloConfigService apolloConfigService;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    private IpLocationService ipLocationService;
    @Mock
    private RemoteStrategyService remoteStrategyService;
    @Mock
    private RechargeCommonService rechargeCommonService;
    @Mock
    private AgentRechargeService agentRechargeService;
    @InjectMocks
    private PayPalRechargeService payPalRechargeService;


    private WebCommonParam buildWebCommonParam() {
        WebCommonParam webCommonParam = new WebCommonParam();
        webCommonParam.setKugouId(1290249156L);
        webCommonParam.setIp("*************");
        webCommonParam.setExt("");
        return webCommonParam;
    }

    private PayPalRequest buildPayPalRequest() {
        PayPalRequest payPalRequest = new PayPalRequest();
        payPalRequest.setAmount(BigDecimal.valueOf(100));
        return payPalRequest;
    }

    private ServerOptionBO buildServerOptionBO() {
        return new ServerOptionBO()
                .setServerKey("USDToRMBCurrency")
                .setServerValue(6);
    }

    @Test
    public void getPayPalUrl() {
        when(userFacadeService.getUserIdByKugouId(anyLong(), anyBoolean())).thenReturn(Optional.of(1290249156L));
        when(apolloConfigService.getPayPalWhitelist()).thenReturn(Lists.newArrayList());
        when(remoteFamilyControlService.checkFamilyControl(anyLong(), anyInt())).thenReturn(false);
        when(apolloConfigService.getServerOptionByKey(anyString())).thenReturn(Optional.ofNullable(buildServerOptionBO()));
        when(orderIdService.generateRechargeOrderNumForAcross()).thenReturn("R092020081014580532783499");
        when(orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202008");
        when(remoteStrategyService.strategyVerifyForPayPal(anyString(), anyLong(), any(PayPalRequest.class))).thenReturn(false);
        when(rechargeAcrossDao.add(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
        when(rechargeConfig.getWebMethod()).thenReturn("POST");
        when(rechargeConfig.getActionUrlPrefix(any(PayTypeIdEnum.class))).thenReturn("/v1/paypal?");
        when(rechargeConfig.getKupayAppIdByAppId(anyInt())).thenReturn(new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J"));
        when(rechargeConfig.getKupayAppIdByPayType(any(PayTypeIdEnum.class))).thenReturn(new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J"));
        when(rechargeConfig.getSignType()).thenReturn("md5");
        when(rechargeConfig.getNotifyUrl(any())).thenReturn("recharge55.fxwork.kugou.com");
        when(ipLocationService.isOverseasIp(anyString())).thenReturn(true);
        when(rechargeCommonService.buildExtendStr(any(RechargeAcrossPO.class),anyMap())).thenReturn("");
        try (MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.makeHttpUrlBuilder(anyString(), anyMap())).thenReturn(new HttpUrl.Builder().scheme("http").host("test.kugou.com"));
            when(agentRechargeService.checkAgentRecharge(any(WebCommonParam.class), anyLong(), anyLong())).thenReturn(SysResultCode.SUCCESS);
            Map<String, Object> dataMap = payPalRechargeService.getPayPalUrl(buildWebCommonParam(), buildPayPalRequest());
            Assert.assertNotNull(dataMap);
            Assert.assertNotNull(dataMap.get("actionUrl"));
            Assert.assertNotNull(dataMap.get("webMethod"));
        }
    }

    @Test(expected = AckException.class)
    public void testGetUserIdByKugouId() {
        when(userFacadeService.getUserIdByKugouId(anyLong())).thenThrow(new ContextedRuntimeException("test"));
        payPalRechargeService.getPayPalUrl(buildWebCommonParam(), buildPayPalRequest());
    }

    @Test(expected = AckException.class)
    public void testFamilyControl() {
        when(userFacadeService.getUserIdByKugouId(anyLong())).thenReturn(Optional.of(1290249156L));
        when(remoteFamilyControlService.checkFamilyControl(anyLong(), anyInt())).thenReturn(true);
        payPalRechargeService.getPayPalUrl(buildWebCommonParam(), buildPayPalRequest());
    }

    @Test(expected = AckException.class)
    public void testUSDToRMBCurrency() {
        when(userFacadeService.getUserIdByKugouId(anyLong())).thenReturn(Optional.of(1290249156L));
        when(apolloConfigService.getPayPalWhitelist()).thenReturn(Lists.newArrayList());
        when(remoteFamilyControlService.checkFamilyControl(anyLong(), anyInt())).thenReturn(false);
        when(apolloConfigService.getServerOptionByKey(anyString())).thenThrow(new ContextedRuntimeException("test"));
        payPalRechargeService.getPayPalUrl(buildWebCommonParam(), buildPayPalRequest());
    }

    @Test(expected = AckException.class)
    public void testSaveOrderRecord() {
        //保存充值下单记录
        PayPalRequest payPalRequest = new PayPalRequest();
        when(rechargeConfig.isRechargeWithChange(anyInt())).thenReturn(true);
        when(remoteFamilyControlService.checkFamilyControl(anyLong(), anyInt())).thenReturn(false);
        when(apolloConfigService.getPayPalWhitelist()).thenReturn(Lists.newArrayList("2"));
        when(userFacadeService.getUserIdByKugouId(anyLong())).thenReturn(Optional.of(1L));
        when(apolloConfigService.getServerOptionByKey(anyString())).thenReturn(Optional.of(buildServerOptionBO()));
        when(orderIdService.generateRechargeOrderNumForAcross()).thenReturn("R092017082820480639182553");
        when(orderIdService.getYearMonthFromRechargeOrderNum(anyString())).thenReturn(Optional.empty());
        payPalRequest.setAmount(new BigDecimal(100));
        payPalRechargeService.getPayPalUrl(buildWebCommonParam(), payPalRequest);
    }

    @Test(expected = AckException.class)
    public void testRechargeAcrossDaoException() {
        when(userFacadeService.getUserIdByKugouId(anyLong())).thenReturn(Optional.of(1290249156L));
        when(apolloConfigService.getPayPalWhitelist()).thenReturn(Lists.newArrayList());
        when(remoteFamilyControlService.checkFamilyControl(anyLong(), anyInt())).thenReturn(false);
        when(apolloConfigService.getServerOptionByKey(anyString())).thenReturn(Optional.ofNullable(buildServerOptionBO()));
        when(orderIdService.generateRechargeOrderNumForAcross()).thenReturn("R092020081014580532783499");
        when(orderIdService.getYearMonthFromRechargeOrderNum(anyString())).thenReturn(Optional.ofNullable("202008"));
        when(remoteStrategyService.strategyVerifyForPayPal(anyString(), anyLong(), any(PayPalRequest.class))).thenReturn(false);
        when(rechargeAcrossDao.add(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
        payPalRechargeService.getPayPalUrl(buildWebCommonParam(), buildPayPalRequest());
    }

    @Test(expected = AckException.class)
    public void testReturnRechargeOrderUrlException() {
        when(userFacadeService.getUserIdByKugouId(anyLong())).thenReturn(Optional.of(1290249156L));
        when(apolloConfigService.getPayPalWhitelist()).thenReturn(Lists.newArrayList());
        when(remoteFamilyControlService.checkFamilyControl(anyLong(), anyInt())).thenReturn(false);
        when(apolloConfigService.getServerOptionByKey(anyString())).thenReturn(Optional.ofNullable(buildServerOptionBO()));
        when(orderIdService.generateRechargeOrderNumForAcross()).thenReturn("R092020081014580532783499");
        when(orderIdService.getYearMonthFromRechargeOrderNum(anyString())).thenReturn(Optional.ofNullable("202008"));
        when(remoteStrategyService.strategyVerifyForPayPal(anyString(), anyLong(), any(PayPalRequest.class))).thenReturn(false);
        when(rechargeAcrossDao.add(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
        when(rechargeConfig.getWebMethod()).thenReturn("POST");
        when(rechargeConfig.getActionUrlPrefix(any(PayTypeIdEnum.class))).thenReturn("/v1/paypal?");
        when(rechargeConfig.getKupayAppIdByAppId(anyInt())).thenReturn(new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J"));
        when(rechargeConfig.getSignType()).thenReturn("md5");
        when(rechargeConfig.getNotifyUrl(any())).thenReturn("recharge55.fxwork.kugou.com");
        when(ipLocationService.isOverseasIp(anyString())).thenReturn(true);
        when(rechargeCommonService.buildExtendStr(any(RechargeAcrossPO.class),anyMap())).thenThrow(new ContextedRuntimeException("test"));
        payPalRechargeService.getPayPalUrl(buildWebCommonParam(), buildPayPalRequest());
    }

    @Test
    public void isInPaypalWhileList() {
        List<String> whitelist = Lists.newArrayList("104815084", "12902491561", "34555180", "1594895384", "991612802", "4900000541", "39089402");
        when(apolloConfigService.getPayPalWhitelist()).thenReturn(whitelist);
        Assert.assertFalse(this.payPalRechargeService.isInPayPalWhitelist(490000054L));
    }

    @Test
    public void testEncode() throws UnsupportedEncodingException {
        Assert.assertTrue(URLEncoder.encode("eyJjYWxsQmFja1NpZ24iOiJjNzU4NmU1MTM3MzMwZDc5N2QyZDIyYmI0MzJmODkyZCIsImNhbGxCYWNrQXJnIjp7ImFtb3VudCI6MTAsImFkZFRpbWUiOjE1ODUxMDE1MTUsImNvdXBvbiI6MCwiYnV5UmljaExldmVsIjp7fSwicmViYXRlIjoiMCIsImNvbnN1bWVBcmdzIjp7fSwiY291cG9uSWQiOjAsInZlcnNpb24iOiIyMDE3MDExMSIsImNvdXBvbk9yZGVySWQiOjAsInBheVR5cGVJZCI6NTIwLCJtb25leSI6NjAsInJlZmVyIjowLCJjRnJvbSI6NywiY2hhbm5lbElkIjowLCJrdWdvdUlkIjoxMjkwMjQ5MTU2fX0=", StandardCharsets.UTF_8.name()).endsWith("%3D"));
    }

    @Test
    public void getRmbAmount() {
        BigDecimal usdAmount = BigDecimal.valueOf(1);
        BigDecimal rmbAmount = this.payPalRechargeService.getRmbAmount(usdAmount);
        Assert.assertTrue(true);
    }

}
