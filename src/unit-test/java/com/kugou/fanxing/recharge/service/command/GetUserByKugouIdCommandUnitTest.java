package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.userbaseinfo.service.UserModuleV2BizService;
import com.kugou.fanxing.userbaseinfo.vo.UserResponse;
import com.kugou.fanxing.userbaseinfo.vo.UserVO;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class GetUserByKugouIdCommandUnitTest {

    @Mock
    private UserModuleV2BizService.Iface userModuleV2BizService;

    @Test
    public void confirm() throws TException {
        UserResponse response = new UserResponse();
        response.setRet(0);
        response.setData(new UserVO().setKugouId(1290249156L));
        when(userModuleV2BizService.getUserByKugouId(anyLong())).thenReturn(response);
        GetUserByKugouIdCommand command = new GetUserByKugouIdCommand(userModuleV2BizService, 1290249156L);
        Optional<UserResponse> optionalUserResponse = command.execute();
        Assert.assertTrue(optionalUserResponse.isPresent());
        Assert.assertEquals(1290249156L, optionalUserResponse.get().getData().getKugouId());
    }

    @Test
    public void confirmException() throws TException {
        when(userModuleV2BizService.getUserByKugouId(anyLong())).thenThrow(new ContextedRuntimeException());
        GetUserByKugouIdCommand command = new GetUserByKugouIdCommand(userModuleV2BizService, 1290249156L);
        Assert.assertFalse(command.execute().isPresent());
    }
}
