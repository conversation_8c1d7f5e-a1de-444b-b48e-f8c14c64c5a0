package com.kugou.fanxing.recharge.service.command;

import com.kugou.mfx.activity.infiltrate.thrift.service.Coupon;
import com.kugou.mfx.activity.infiltrate.thrift.service.CouponListResult;
import com.kugou.mfx.activity.infiltrate.thrift.service.CouponListService;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class ListCouponCommandUnitTest {

    @Mock
    private CouponListService.Iface couponListService;

    @Test
    public void listCoupon() throws TException {
        CouponListResult response = new CouponListResult();
        response.setCode(0);
        response.setData(Lists.newArrayList(new Coupon().setCouponType(1).setCategory(2).setId(1)));
        when(couponListService.listCoupon()).thenReturn(response);
        ListCouponCommand command = new ListCouponCommand(couponListService);
        Map<Long, Coupon> couponMap = command.execute();
        Assert.assertFalse(couponMap.isEmpty());
        when(couponListService.listCoupon()).thenReturn(null);
        command = new ListCouponCommand(couponListService);
        couponMap = command.execute();
        Assert.assertTrue(couponMap.isEmpty());
    }

    @Test
    public void listCouponException() throws TException {
        when(couponListService.listCoupon()).thenThrow(new TException("test"));
        ListCouponCommand command = new ListCouponCommand(couponListService);
        Map<Long, Coupon> couponMap = command.execute();
        Assert.assertTrue(couponMap.isEmpty());
    }
}
