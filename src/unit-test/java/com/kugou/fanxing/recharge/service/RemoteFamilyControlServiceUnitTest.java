package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.thrift.operation.FamilyControllRequest;
import com.kugou.fanxing.thrift.operation.FamilyControllResult;
import com.kugou.fanxing.thrift.operation.FamilyControllService;
import com.kugou.fanxing.thrift.operation.FamilyControllVo;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class RemoteFamilyControlServiceUnitTest {

    @Mock
    private ApolloConfigService apolloConfigService;
    @Mock
    private FamilyControllService.Iface familyControllService;
    @InjectMocks
    private RemoteFamilyControlService remoteFamilyControlService;

    @Test
    public void checkFamilyControlOpen() throws TException {
        when(apolloConfigService.pidNeedToBeControl(anyInt())).thenReturn(true);
        FamilyControllResult result = new FamilyControllResult().setRet(0).setMsg("")
                .setData(new FamilyControllVo().setKugouId(1290249156L).setControllStatus(1));
        when(familyControllService.getFamilyControll(any(FamilyControllRequest.class))).thenReturn(result);
        Assert.assertTrue(this.remoteFamilyControlService.checkFamilyControl(1290249156L, 1));
    }

    @Test
    public void checkFamilyControlClose() throws TException {
        when(apolloConfigService.pidNeedToBeControl(anyInt())).thenReturn(true);
        FamilyControllResult result = new FamilyControllResult().setRet(0).setMsg("")
                .setData(new FamilyControllVo().setKugouId(1290249156L).setControllStatus(0));
        when(familyControllService.getFamilyControll(any(FamilyControllRequest.class))).thenReturn(result);
        Assert.assertFalse(this.remoteFamilyControlService.checkFamilyControl(1290249156L, 1));
    }

    @Test
    public void checkFamilyControlException() throws TException {
        when(apolloConfigService.pidNeedToBeControl(anyInt())).thenReturn(true);
        when(familyControllService.getFamilyControll(any(FamilyControllRequest.class)))
                .thenThrow(new ContextedRuntimeException("checkFamilyControlException"));
        Assert.assertFalse(this.remoteFamilyControlService.checkFamilyControl(1290249156L, 1));
    }
}
