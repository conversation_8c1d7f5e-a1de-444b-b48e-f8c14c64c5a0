package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.model.po.WithdrawOrderPO;
import com.kugou.fanxing.recharge.service.withdraw.WithdrawClientParams;
import com.kugou.fanxing.risk.sdk.RiskStrategyService;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.PlatformStrategyService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

@RunWith(MockitoJUnitRunner.Silent.class)
public class RemoteStrategyServiceUnitTest {

    @InjectMocks
    private RemoteStrategyService remoteStrategyService;
    @Mock
    private RiskStrategyService riskStrategyService;
    @Mock
    private PlatformStrategyService.Iface platformStrategyService;

    @Test
    public void strategyVerifyForWithdrawFalseTest(){
        WithdrawOrderPO withdrawOrderPO = new WithdrawOrderPO();
        withdrawOrderPO.setBizAppId(1084);
        withdrawOrderPO.setKugouId(**********);
        withdrawOrderPO.setOrderId(432982223583449087L);
        withdrawOrderPO.setAccountEncrypted("s81nMQa8vps_lTH_6Dyl9w@DQA+AIQCAAA=");
        withdrawOrderPO.setTotalAmount(new BigDecimal("0.10"));
        WithdrawClientParams withdrawClientParams = new WithdrawClientParams();
        withdrawClientParams.setDfid("-");
        withdrawClientParams.setUuid("");
        withdrawClientParams.setMid("FFFF");
        withdrawClientParams.setClientver("0");
        withdrawClientParams.setApplicationId("1010");
        withdrawClientParams.setClientip("127.0.0.1");
        //风控挂了，所以返回不需要风控
        boolean result= remoteStrategyService.strategyVerifyForWithdraw(withdrawOrderPO,withdrawClientParams);
        Assert.assertFalse(result);
    }
}
