package com.kugou.fanxing.recharge.service.callback;

import com.kugou.fanxing.biz.commons.util.SnowFlake;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.TmallCallbackRequest;
import com.kugou.fanxing.recharge.model.vo.RechargeRebateVO;
import com.kugou.fanxing.recharge.service.RechargeCouponService;
import com.kugou.fanxing.recharge.service.RechargeOrderService;
import com.kugou.fanxing.recharge.service.RechargeRebateService;
import com.kugou.fanxing.recharge.service.TmallRechargeService;
import com.kugou.fanxing.recharge.service.after.AfterRechargeService;
import com.kugou.fanxing.recharge.service.common.ConsumeRpcService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.recharge.util.SpringContextUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class TmallCallbackServiceUnitTest {

    @InjectMocks
    private TmallCallbackService tmallCallbackService;
    @Mock
    private HttpServletRequest request;
    @Mock
    private RechargeConfig rechargeConfig;
    @Mock
    private TmallRechargeService tmallRechargeService;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    private RechargeOrderService rechargeOrderService;
    @Mock
    private RechargeRebateService rechargeRebateService;
    @Mock
    private UserFacadeService userFacadeService;
    @Mock
    private RechargeCouponService rechargeCouponService;
    @Mock
    private ConsumeRpcService consumeRpcService;
    @Mock
    private RechargeAcrossDao rechargeAcrossDao;
    @Mock
    private AfterRechargeService afterRechargeService;

    @Before
    public void testBefore()  {
        when(this.orderIdService.generateGlobalId()).thenReturn(new SnowFlake(0, 0).nextId());
        when(this.rechargeConfig.getKupayAppIdByAppId(anyInt())).thenReturn(new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J"));
        when(request.getParameterNames()).thenReturn(new Enumeration<String>() {
            @Override
            public boolean hasMoreElements() {
                return false;
            }
            @Override
            public String nextElement() {
                return null;
            }
        });
        when(this.rechargeConfig.getKupayIntranet()).thenReturn("http://kupay.kugou.com");
    }

    @Test
    public void checkSignOfPcCallBack() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            Assert.assertTrue(tmallCallbackService.checkSignOfPcCallBack(1084, "7e14f00fc8620c60dbc01a0c9a394672"));
        }
    }

    @Test
    public void purchaseCoin() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class);
             MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            // E_30000009
            TmallCallbackRequest tmallCallbackRequest = new TmallCallbackRequest();
            tmallCallbackRequest.setSign("7e14f00fc8620c60dbc01a0c9a394672---");
            tmallCallbackRequest.setUserid(1L);
            tmallCallbackRequest.setExtend("test");
            tmallCallbackRequest.setOrder_no("01202012222359270100010208");
            tmallCallbackRequest.setTotal_fee(BigDecimal.valueOf(100).stripTrailingZeros().toPlainString());
            JsonResult<Map<String, String>> result = this.tmallCallbackService.purchaseCoin(tmallCallbackRequest);
            Assert.assertEquals(SysResultCode.E_30000009.getCode(), result.getCode());
            // E_30000010
            tmallCallbackRequest.setSign("7e14f00fc8620c60dbc01a0c9a394672");
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doSyncGet(anyString(), anyMap())).thenReturn(Optional.of(""));
            result = this.tmallCallbackService.purchaseCoin(tmallCallbackRequest);
            Assert.assertEquals(SysResultCode.E_30000010.getCode(), result.getCode());
            // E_30000011
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doSyncGet(anyString(), anyMap())).thenReturn(Optional.of("success"));
            when(tmallRechargeService.parseKugouIdByBrand(anyLong(), anyString())).thenReturn(Optional.empty());
            result = this.tmallCallbackService.purchaseCoin(tmallCallbackRequest);
            Assert.assertEquals(SysResultCode.E_30000011.getCode(), result.getCode());
            // E_30000012
            when(tmallRechargeService.parseKugouIdByBrand(anyLong(), anyString())).thenReturn(Optional.of(1L));
            when(this.rechargeOrderService.specialRechargeOrderNumDeal(anyString(), anyInt())).thenReturn(Optional.empty());
            result = this.tmallCallbackService.purchaseCoin(tmallCallbackRequest);
            Assert.assertEquals(SysResultCode.E_30000012.getCode(), result.getCode());
            // RECHARGE_SYS_ERROR
            when(this.rechargeOrderService.specialRechargeOrderNumDeal(anyString(), anyInt())).thenReturn(Optional.of("R09202012S21461004778253549859"));
            when(this.rechargeOrderService.queryByRechargeOrderNum(anyString())).thenReturn(Optional.empty());
            when(this.rechargeOrderService.addRechargeOrder(any(RechargeAcrossPO.class))).thenReturn(1);
            when(userFacadeService.getUserIdByKugouId(anyLong(), anyBoolean())).thenReturn(Optional.of(1L));
            List<RechargeRebateVO> rechargeRebateVOList = Lists.newArrayList(new RechargeRebateVO().setVersion(1499917607));
            when(this.rechargeRebateService.getRechargeRebateInfo()).thenReturn(rechargeRebateVOList);
            when(rechargeCouponService.extractValidCoupon(any(RechargeAcrossPO.class))).thenReturn(Optional.empty());
            when(consumeRpcService.rechargeCoin(any(RechargeAcrossPO.class))).thenReturn(true);
            when(rechargeAcrossDao.insertIgnore(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
            when(orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202212");
            doNothing().when(afterRechargeService).afterRechargeSuccess(any(RechargeAcrossPO.class));
            result = this.tmallCallbackService.purchaseCoin(tmallCallbackRequest);
            Assert.assertEquals(SysResultCode.SUCCESS.getCode(), result.getCode());
        }
    }

    @Test
    public void buildExtendStr() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            List<RechargeRebateVO> rechargeRebateVOList = Lists.newArrayList(new RechargeRebateVO().setVersion(1499917607));
            when(this.rechargeRebateService.getRechargeRebateInfo()).thenReturn(rechargeRebateVOList);
            String extend = this.tmallCallbackService.buildExtendStr();
            Assert.assertEquals("{\"callBackSign\":[],\"callBackArg\":{\"rebate\":1499917607}}", new String(Base64.decodeBase64(extend)));
        }
    }

    @Test(expected = ContextedRuntimeException.class)
    public void buildExtendStrException() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            when(this.rechargeRebateService.getRechargeRebateInfo()).thenReturn(Lists.emptyList());
            this.tmallCallbackService.buildExtendStr();
        }
    }

    @SneakyThrows
    @Test
    public void purchaseCoinKw() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class);
             MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            // E_30000009
            TmallCallbackRequest tmallCallbackRequest = new TmallCallbackRequest();
            tmallCallbackRequest.setSign("7e14f00fc8620c60dbc01a0c9a394672---");
            tmallCallbackRequest.setUserid(1L);
            tmallCallbackRequest.setExtend("test");
            tmallCallbackRequest.setOrder_no("01202012222359270100010208");
            tmallCallbackRequest.setTotal_fee(BigDecimal.valueOf(100).stripTrailingZeros().toPlainString());
            JsonResult<Map<String, String>> result = this.tmallCallbackService.purchaseCoinKw(tmallCallbackRequest);
            Assert.assertEquals(SysResultCode.E_30000009.getCode(), result.getCode());
            // E_30000010
            tmallCallbackRequest.setSign("7e14f00fc8620c60dbc01a0c9a394672");
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doSyncGet(anyString(), anyMap())).thenReturn(Optional.of(""));
            result = this.tmallCallbackService.purchaseCoinKw(tmallCallbackRequest);
            Assert.assertEquals(SysResultCode.E_30000010.getCode(), result.getCode());
            // E_30000011
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doSyncGet(anyString(), anyMap())).thenReturn(Optional.of("success"));
            when(this.userFacadeService.getKugouIdByKuwoId(anyLong())).thenReturn(Optional.empty());
            result = this.tmallCallbackService.purchaseCoinKw(tmallCallbackRequest);
            Assert.assertEquals(SysResultCode.E_30000011.getCode(), result.getCode());
            // E_30000012
            when(this.userFacadeService.getKugouIdByKuwoId(anyLong())).thenReturn(Optional.of(1L));
            when(this.rechargeOrderService.specialRechargeOrderNumDeal(anyString(), anyInt())).thenReturn(Optional.empty());
            result = this.tmallCallbackService.purchaseCoinKw(tmallCallbackRequest);
            Assert.assertEquals(SysResultCode.E_30000012.getCode(), result.getCode());
            // RECHARGE_SYS_ERROR
            when(this.rechargeOrderService.specialRechargeOrderNumDeal(anyString(), anyInt())).thenReturn(Optional.of("R09202012S21461004778253549859"));
            when(this.rechargeOrderService.queryByRechargeOrderNum(anyString())).thenReturn(Optional.empty());
            when(this.rechargeOrderService.addRechargeOrder(any(RechargeAcrossPO.class))).thenReturn(0);
            result = this.tmallCallbackService.purchaseCoinKw(tmallCallbackRequest);
            Assert.assertEquals(SysResultCode.RECHARGE_SYS_ERROR.getCode(), result.getCode());
        }
    }
}
