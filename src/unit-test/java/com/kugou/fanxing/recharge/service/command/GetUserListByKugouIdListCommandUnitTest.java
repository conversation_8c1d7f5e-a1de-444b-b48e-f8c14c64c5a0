package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.userbaseinfo.service.UserModuleV2BizService;
import com.kugou.fanxing.userbaseinfo.vo.UserListResponse;
import com.kugou.fanxing.userbaseinfo.vo.UserVO;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class GetUserListByKugouIdListCommandUnitTest {

    @Mock
    private UserModuleV2BizService.Iface userModuleV2BizService;

    @Test
    public void getUserInfoFromKugouV1() throws TException {
        UserListResponse response = new UserListResponse();
        response.setRet(0);
        response.setData(Lists.newArrayList(new UserVO().setKugouId(1290249156L)));
        when(userModuleV2BizService.getUserListByKugouIdList(anyList())).thenReturn(response);
        GetUserListByKugouIdListCommand command = new GetUserListByKugouIdListCommand(userModuleV2BizService, Lists.newArrayList(1290249156L));
        Assert.assertFalse(command.execute().isEmpty());
        when(userModuleV2BizService.getUserListByKugouIdList(anyList())).thenReturn(null);
        command = new GetUserListByKugouIdListCommand(userModuleV2BizService, Lists.newArrayList(1290249156L));
        Assert.assertTrue(command.execute().isEmpty());
    }

    @Test
    public void getUserInfoFromKugouV1Exception() throws TException {
        when(userModuleV2BizService.getUserListByKugouIdList(anyList())).thenThrow(new ContextedRuntimeException());
        GetUserListByKugouIdListCommand command = new GetUserListByKugouIdListCommand(userModuleV2BizService, Lists.newArrayList(1290249156L));
        Assert.assertEquals(0, command.execute().size());
    }
}
