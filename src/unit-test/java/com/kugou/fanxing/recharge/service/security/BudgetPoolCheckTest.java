package com.kugou.fanxing.recharge.service.security;

import com.ctrip.framework.apollo.Config;
import com.kugou.fanxing.recharge.service.common.BudgetPoolCheck;
import com.kugou.fanxing.recharge.util.DateHelper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class BudgetPoolCheckTest {
    @Mock
    private Config config;

    @InjectMocks
    private BudgetPoolCheck budgetPoolCheck;


    @Test
    public void isBudgetCheckOpenTest() {
       boolean result= budgetPoolCheck.isBudgetCheckOpen(123);
        Assert.assertEquals(false, result);

        when(config.getBooleanProperty(any(),any())).thenReturn(true);
        result= budgetPoolCheck.isBudgetCheckOpen(123);
        Assert.assertEquals(true, result);

        when(config.getBooleanProperty(any(),any())).thenReturn(false);
        result= budgetPoolCheck.isBudgetCheckOpen(123);
        Assert.assertEquals(false, result);

        when(config.getBooleanProperty(any(),any())).thenReturn(false);
        String[] string = {};
        when(config.getArrayProperty(any(),any(),any())).thenReturn(string);
        result= budgetPoolCheck.isBudgetCheckOpen(123);
        Assert.assertEquals(false, result);

        string = new String[]{"123","234"};
        when(config.getArrayProperty(any(),any(),any())).thenReturn(string);
        result= budgetPoolCheck.isBudgetCheckOpen(123);
        Assert.assertEquals(true, result);
    }

    @Test
    public void tryDecreaseBudgetOrder() {
        budgetPoolCheck.tryDecreaseBudgetOrder(1, 1, BigDecimal.valueOf(10), 1, DateHelper.getCurrentSeconds());
        Assert.assertTrue(true);
    }

    @Test
    public void confirmDecreaseBudgetOrder() {
        budgetPoolCheck.confirmDecreaseBudgetOrder(1, 1, DateHelper.getCurrentSeconds());
        Assert.assertTrue(true);
    }

    @Test
    public void cancelDecreaseBudgetOrder() {
        budgetPoolCheck.cancelDecreaseBudgetOrder(1, 1, DateHelper.getCurrentSeconds());
        Assert.assertTrue(true);
    }
}
