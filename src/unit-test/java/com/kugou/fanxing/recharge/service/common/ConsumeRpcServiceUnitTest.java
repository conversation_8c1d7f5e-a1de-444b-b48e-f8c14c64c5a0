package com.kugou.fanxing.recharge.service.common;

import com.kugou.config.Env;
import com.kugou.fanxing.biz.commons.consume.api.CostReturn;
import com.kugou.fanxing.recharge.factory.ConsumeServiceFactory;
import com.kugou.fanxing.recharge.model.bo.AccountChangeTypeBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.po.RechargeCouponPO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.thrift.callback.ConsumeParam;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.IpUtils;
import com.kugou.fanxing.thrift.consume.service.ConsumeResp;
import com.kugou.fanxing.thrift.consume.service.PlatformConsumeService;
import com.kugou.fanxing.thrift.consume.service.RechargeFeeVO;
import com.kugou.fanxing.thrift.freeze.service.CoinVO;
import com.kugou.fanxing.thrift.freeze.service.PlatformAddCoinService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class ConsumeRpcServiceUnitTest {

    @Mock
    private Env env;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    private PlatformAddCoinService.Iface platformAddCoinService;
    @Mock
    private PlatformConsumeService.Iface platformConsumeService;
    @Mock
    private ApolloConfigService apolloConfigService;
    @InjectMocks
    private ConsumeRpcService consumeRpcService;
    @Mock
    private ConsumeServiceFactory consumeServiceFactory;

    @Before
    public void setUp() throws Exception {
        when(this.consumeServiceFactory.getAddCoinService(anyInt())).thenReturn(platformAddCoinService);
        when(this.consumeServiceFactory.getConsumeService(anyInt())).thenReturn(platformConsumeService);
    }

    /**
     * {"changeFromUserData":{"coin":1000000151085,"money":292562},"increaseFromUserData":{"coin":66,"money":66},"uniqId":"recharge_R09202012S1TID1000000756911994"}
     */
    @Test
    public void rechargeCoin() throws TException {
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(IpUtils::getClientIpAddress).thenReturn("127.0.0.1");
            RechargeAcrossPO callbackOrder = new RechargeAcrossPO();
            callbackOrder.setRealAmount(BigDecimal.ONE);
            callbackOrder.setAddTime(DateHelper.getCurrentSeconds());
            callbackOrder.setRechargeOrderNum("R09202012S1TID1000000756911994");
            ConsumeResp consumeResp = new ConsumeResp().setRet(0).setMsg("")
                    .setData("{\"changeFromUserData\":{\"coin\":1000000151085,\"money\":292562},\"increaseFromUserData\":{\"coin\":66,\"money\":66},\"uniqId\":\"recharge_R09202012S1TID1000000756911994\"}");
            when(platformAddCoinService.recharge(any(CoinVO.class))).thenReturn(consumeResp);
            Assert.assertTrue(this.consumeRpcService.rechargeCoin(callbackOrder));
        }
    }

    @Test
    public void presentCouponCoin() throws TException {
        String rechargeOrderNum = "R09202012S1TID1000000756911994";
        RechargeCouponPO rechargeCouponPO = new RechargeCouponPO();
        BigDecimal coupon = BigDecimal.ZERO;
        when(env.isProd()).thenReturn(false);
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("127.0.0.1");
            ConsumeResp consumeResp = new ConsumeResp().setRet(0).setMsg("")
                    .setData("{\"changeFromUserData\":{\"coin\":1000000151085,\"money\":292562},\"increaseFromUserData\":{\"coin\":66,\"money\":66},\"uniqId\":\"recharge_R09202012S1TID1000000756911994\"}");
            when(this.platformAddCoinService.present(any(CoinVO.class))).thenReturn(consumeResp);
            Optional<CostReturn> optionalCostReturn = this.consumeRpcService.presentCouponCoin(rechargeOrderNum, rechargeCouponPO, coupon);
            Assert.assertTrue(optionalCostReturn.isPresent());
            Assert.assertNotNull(optionalCostReturn.get());
        }
    }

    @Test
    public void presentCouponCoinException() throws TException {
        String rechargeOrderNum = "R09202012S1TID1000000756911994";
        RechargeCouponPO rechargeCouponPO = new RechargeCouponPO();
        BigDecimal coupon = BigDecimal.ZERO;
        when(env.isProd()).thenReturn(false);
        try (MockedStatic<IpUtils> mockedStatic = Mockito.mockStatic(IpUtils.class)) {
            mockedStatic.when(() -> IpUtils.getClientIpAddress()).thenReturn("127.0.0.1");
            when(this.platformAddCoinService.present(any(CoinVO.class))).thenThrow(new TException());
            Optional<CostReturn> optionalCostReturn = this.consumeRpcService.presentCouponCoin(rechargeOrderNum, rechargeCouponPO, coupon);
            Assert.assertFalse(optionalCostReturn.isPresent());
        }
    }

    @Test
    public void rechargeCoinFailure() throws TException {
        RechargeAcrossPO callbackOrder = new RechargeAcrossPO();
        ConsumeResp consumeResp = new ConsumeResp().setRet(10000).setMsg("")
                .setData("{\"changeFromUserData\":{\"coin\":1000000151085,\"money\":292562},\"increaseFromUserData\":{\"coin\":66,\"money\":66},\"uniqId\":\"recharge_R09202012S1TID1000000756911994\"}");
        when(platformAddCoinService.recharge(any(CoinVO.class))).thenReturn(consumeResp);
        Assert.assertFalse(this.consumeRpcService.rechargeCoin(callbackOrder));
    }

    @Test
    public void rechargeCoinException() throws TException {
        RechargeAcrossPO callbackOrder = new RechargeAcrossPO();
        when(platformAddCoinService.recharge(any(CoinVO.class))).thenThrow(new TException());
        Assert.assertFalse(this.consumeRpcService.rechargeCoin(callbackOrder));
    }

    @Test
    public void getFxcChangeDescIos() {
        String fxcChangeDesc = this.consumeRpcService.getFxcChangeDesc(10, 100001, "R09202011S1TID1000000737536677", "");
        log.warn("fxcChangeDesc: {}", fxcChangeDesc);
        Assert.assertEquals("充值|充值|充值订单号:R09202011S1TID1000000737536677|消费订单号:", fxcChangeDesc);
    }

    @Test
    public void getFxChangeDesc() {
        String fxcChangeDesc = this.consumeRpcService.getFxcChangeDesc(10, 100001, "R092020112410193328228590", "01202011241019340100011799");
        log.warn("fxcChangeDesc: {}", fxcChangeDesc);
        Assert.assertEquals("充值|充值|充值订单号:R092020112410193328228590|消费订单号:01202011241019340100011799", fxcChangeDesc);
    }

    @Test
    public void rechargeFee() throws TException {
        String extraJsonData = "{\"signBook\":\"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\",\"goodsId\":\"com.kugou.fanxingappstore.shhu1048\",\"fromType\":\"moblie7\",\"businessId\":\"1458050062831018488\"}";
        ConsumeResp consumeResp = new ConsumeResp();
        consumeResp.setRet(0);
        consumeResp.setData("");
        RechargeAcrossPO callbackOrder = new RechargeAcrossPO();
        callbackOrder.setCoin(BigDecimal.ONE);
        callbackOrder.setExtraJsonData(extraJsonData);
        ConsumeParam consumeParam = new ConsumeParam();
        consumeParam.setAccountChangeType(110014);
        when(apolloConfigService.getAccountChangeTypeById(anyInt())).thenReturn(new AccountChangeTypeBO().setActionId(110014));
        when(this.platformConsumeService.rechargeFee(any(RechargeFeeVO.class))).thenReturn(consumeResp);
        Assert.assertTrue(this.consumeRpcService.rechargeFee(callbackOrder, consumeParam, "127.0.0.1"));
        when(this.platformConsumeService.rechargeFee(any(RechargeFeeVO.class))).thenThrow(new ContextedRuntimeException());
        Assert.assertFalse(this.consumeRpcService.rechargeFee(callbackOrder, consumeParam, "127.0.0.1"));
    }

    /**
     * {
     *     "callBackSign":"12e7524671854c8256567d67b93444ce",
     *     "callBackArg":{
     *         "businessId":"1476005819283021960",
     *         "businessType":"",
     *         "businessTime":**********,
     *         "payTime":**********,
     *         "fromKugouId":**********,
     *         "toKugouId":**********,
     *         "topic":"fx.buyLittleGuard",
     *         "refer":1,
     *         "cFrom":18,
     *         "channelId":1,
     *         "amount":10,
     *         "payTypeId":32,
     *         "addTime":**********,
     *         "kugouId":**********,
     *         "version":"********",
     *         "rebate":0,
     *         "reType":1,
     *         "userFundPlatParam":{
     *             "senderDepartmentId":"1",
     *             "senderProductId":"0",
     *             "senderMinorProductId":"0",
     *             "senderHardwarePlatform":"0",
     *             "senderChannelId":"200",
     *             "senderSubChannelId":"0",
     *             "receiverDepartmentId":"1",
     *             "fromKugouId":"**********",
     *             "toKugouId":"**********",
     *             "fxcChangeDesc":"\u7528\u6237fromKugouId:**********\u8d2d\u4e70\u8c46\u7c89",
     *             "roomId":"2520213",
     *             "giftId":"0",
     *             "giftName":"\u8c46\u7c89",
     *             "giftNum":"1",
     *             "actionId":"231",
     *             "coin":"1000",
     *             "accountChangeType":"110078",
     *             "ext":"{\"cost\":1000}",
     *             "globalId":"1476005819283021960",
     *             "pid":18,
     *             "ip":"2409:8a28:4a25:310:4179:aefb:d376:e759"
     *         },
     *         "businessExt":""
     *     }
     * }
     */
    @Test
    public void parseRechargeFeeVOByExtend() {
        String rechargeOrderNum = "R092021022500001019783079";
        int addTime = DateHelper.getCurrentSeconds();
        String extend = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        Mockito.when(apolloConfigService.getAccountChangeTypeById(anyInt())).thenReturn(new AccountChangeTypeBO().setActionId(231));
        RechargeFeeVO rechargeFeeVO = this.consumeRpcService.parseRechargeFeeVOByExtend(rechargeOrderNum, addTime, extend);
        Assert.assertEquals(1476005819283021960L, rechargeFeeVO.getGlobalId());
        Assert.assertEquals(addTime, rechargeFeeVO.getTimestamp());
        Assert.assertEquals("{\"cost\":1000,\"istar\":\"{\\\"area_code\\\":\\\"\\\",\\\"time_zone\\\":\\\"\\\",\\\"lang\\\":\\\"\\\"}\",\"rechargeId\":\"R092021022500001019783079\"}", rechargeFeeVO.getExt());
        Assert.assertEquals("用户fromKugouId:**********购买豆粉", rechargeFeeVO.getFxcChangeDesc());
        Assert.assertEquals(18, rechargeFeeVO.getPid());
        Assert.assertEquals(110078, rechargeFeeVO.getAccountChangeType());
        Assert.assertEquals(new BigDecimal("1000").stripTrailingZeros().toPlainString(), BigDecimal.valueOf(rechargeFeeVO.getCoin()).stripTrailingZeros().toPlainString());
        Assert.assertEquals(**********L, rechargeFeeVO.getFromKugouId());
        Assert.assertEquals(**********L, rechargeFeeVO.getToKugouId());
        Assert.assertEquals("2409:8a28:4a25:310:4179:aefb:d376:e759", rechargeFeeVO.getIp());
        Assert.assertEquals(2520213, rechargeFeeVO.getRoomId());
        Assert.assertEquals(0, rechargeFeeVO.getGiftId());
        Assert.assertEquals("豆粉", rechargeFeeVO.getGiftName());
        Assert.assertEquals(1, rechargeFeeVO.getGiftNum());
        Assert.assertEquals(231, rechargeFeeVO.getActionId());
        Assert.assertEquals(0, rechargeFeeVO.getSenderDepartmentId());
        Assert.assertEquals(0, rechargeFeeVO.getSenderProductId());
        Assert.assertEquals(0, rechargeFeeVO.getSenderMinorProductId());
        Assert.assertEquals(0, rechargeFeeVO.getSenderHardwarePlatform());
        Assert.assertEquals(0, rechargeFeeVO.getSenderChannelId());
        Assert.assertEquals(0, rechargeFeeVO.getSenderSubChannelId());
        Assert.assertEquals(0, rechargeFeeVO.getReceiverDepartmentId());
    }

    @Test
    public void relatedRechargeOrderNum() {
        String xx = this.consumeRpcService.relatedRechargeOrderNum(
                "R092021022500001019783079", "{}", "CN", "8", "ZH");
        Assert.assertNotNull(xx);
    }

    @Test
    public void buildIosPurchaseUserFundPlatParam() {
        AccountChangeTypeBO accountChangeTypeBO = new AccountChangeTypeBO();
        accountChangeTypeBO.setAccountChangeType(110078);
        accountChangeTypeBO.setActionId(231);
        accountChangeTypeBO.setConsumeSalt("gmxsh");
        accountChangeTypeBO.setTopic("");
        when(apolloConfigService.getAccountChangeTypeById(anyInt())).thenReturn(accountChangeTypeBO);
        RechargeAcrossPO callbackOrder = new RechargeAcrossPO();
        callbackOrder.setRechargeOrderNum("R092021022500001019783079");
        callbackOrder.setKugouId(**********);
        callbackOrder.setAmount(BigDecimal.valueOf(100));
        callbackOrder.setMoney(BigDecimal.valueOf(100));
        callbackOrder.setCoin(BigDecimal.valueOf(100));
        callbackOrder.setStatus(1);
        callbackOrder.setExtraJsonData("{\"businessId\":\"110231\",\"signBook\":\"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\",\"goodsId\":\"com.kugou.fanxingappstore.shhu1048\",\"fromType\":\"moblie7\",\"businessId\":\"1446219001364501362\"}");
        ConsumeParam consumeParam = new ConsumeParam();
        consumeParam.setAccountChangeType(100001);
        String clientIp = "127.0.0.1";
        Map<String, String> params = this.consumeRpcService.buildIosPurchaseUserFundPlatParam(callbackOrder, consumeParam, clientIp);
        Assert.assertNotNull(params);
    }


    @Test
    public void testRechargeFee_Success() throws TException {
        ConsumeResp resp = new ConsumeResp();
        resp.setRet(0);
        // 设置 mock 对象的行为
        when(platformConsumeService.rechargeFee(any(RechargeFeeVO.class)))
                .thenReturn(new ConsumeResp(resp));

        // 调用被测试方法
        boolean result = consumeRpcService.rechargeFee(new RechargeFeeVO(), 1);

        // 验证行为
        Assert.assertTrue(result);
    }

    @Test
    public void testRechargeFee2_Success() throws TException {
        ConsumeResp resp = new ConsumeResp();
        resp.setRet(0);
        // 设置 mock 对象的行为
        when(platformConsumeService.rechargeFee(any(RechargeFeeVO.class)))
                .thenReturn(new ConsumeResp(resp));
        when(apolloConfigService.getAccountChangeTypeById(anyInt())).thenReturn(new AccountChangeTypeBO());

        // 调用被测试方法
        boolean result = consumeRpcService.rechargeFee("123", 1, "test", 2);

        // 验证行为
        Assert.assertTrue(result);
    }


    @Test
    public void testRechargeFee_Failure() throws TException {
        ConsumeResp resp = new ConsumeResp();
        resp.setRet(1);
        // 设置 mock 对象的行为
        when(platformConsumeService.rechargeFee(any(RechargeFeeVO.class)))
                .thenReturn(new ConsumeResp(resp));

        // 创建被测试对象，并将 mock 对象注入
        boolean result = consumeRpcService.rechargeFee(new RechargeFeeVO(), 1);

        // 验证行为
        Assert.assertFalse(result);
    }


    @Test
    public void testRechargeFee_Exception() throws TException {
        ConsumeResp resp = new ConsumeResp();
        resp.setRet(1);
        // 设置 mock 对象的行为
        when(platformConsumeService.rechargeFee(any(RechargeFeeVO.class)))
                .thenThrow(new RuntimeException("Simulated exception"));

        // 调用被测试方法
        boolean result = consumeRpcService.rechargeFee(new RechargeFeeVO(), 1);

        // 验证行为
        Assert.assertFalse(result);
    }

}
