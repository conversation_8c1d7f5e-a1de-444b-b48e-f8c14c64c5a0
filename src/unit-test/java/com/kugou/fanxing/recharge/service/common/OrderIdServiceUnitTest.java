package com.kugou.fanxing.recharge.service.common;

import com.kugou.api.springcloud.GlobalIdServiceThrift.GlobalIdService;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class OrderIdServiceUnitTest {

    @Mock
    private GlobalIdService.Iface globalIdService;
    @Mock
    private RechargeConfig rechargeConfig;
    @InjectMocks
    private OrderIdService orderIdService;

    @Test
    public void generateRechargeOrderNumForAcross() {
        when(rechargeConfig.getDataCenterZoneId()).thenReturn(0);
        String rechargeOrderNum1 = this.orderIdService.generateRechargeOrderNumForAcross();
        Assert.assertTrue(rechargeOrderNum1.startsWith("R09"));
        when(rechargeConfig.getDataCenterZoneId()).thenReturn(2);
        String rechargeOrderNum2 = this.orderIdService.generateRechargeOrderNumForAcross();
        Assert.assertTrue(rechargeOrderNum2.startsWith("R29"));
        log.warn("rechargeOrderNum1: {}, rechargeOrderNum2: {}", rechargeOrderNum1, rechargeOrderNum2);
    }

    @Test
    public void generateGlobalIdException() throws TException {
        when(globalIdService.get()).thenThrow(new TException("test"));
        long globalId = orderIdService.generateGlobalId();
        Assert.assertTrue(globalId > 0);
    }

    @Test
    public void generateGlobalNormal() throws TException {
        when(globalIdService.get()).thenReturn(10000L);
        long globalId = orderIdService.generateGlobalId();
        Assert.assertEquals(10000L, globalId);
    }

    @Test
    public void getYearMonthFromRechargeOrderNum() {
        Optional<String> optionalRechargeOrderNum = this.orderIdService.getYearMonthFromRechargeOrderNum("R092020011314312722985482");
        Assert.assertEquals("202001", optionalRechargeOrderNum.orElseThrow(() -> new ContextedRuntimeException("test")));
        Optional<String> optionalMonth = orderIdService.getYearMonthFromRechargeOrderNum("R09201911S1TID1000000587753531");
        Assert.assertEquals("201911", optionalMonth.orElseThrow(() -> new ContextedRuntimeException("test")));
        optionalMonth = orderIdService.getYearMonthFromRechargeOrderNum("R02201911S1TID1000000587753531");
        Assert.assertTrue(!optionalMonth.isPresent());
    }

    @Test
    public void getYearMonthFromRechargeOrderNumSilent() {
        String month1 = this.orderIdService.getYearMonthFromRechargeOrderNumSilent("R092020011314312722985482");
        Assert.assertEquals("202001", month1);
        String month2 = orderIdService.getYearMonthFromRechargeOrderNumSilent("R09201911S1TID1000000587753531");
        Assert.assertEquals("201911", month2);
    }

    @Test(expected = ContextedRuntimeException.class)
    public void getYearMonthFromRechargeOrderNumSilentException() {
        this.orderIdService.getYearMonthFromRechargeOrderNumSilent("X092020011314312722985482");
    }


    @Test
    public void extractTransactionId() {
        Optional<String> optionalTransactionId1 = this.orderIdService.extractTransactionId("R09202202S1TID1000000976072314");
        Assert.assertEquals("1000000976072314", optionalTransactionId1.orElse(""));
        Optional<String> optionalTransactionId2 = orderIdService.extractTransactionId("R092022020707094777990167");
        Assert.assertFalse(optionalTransactionId2.isPresent());
        Assert.assertEquals("", optionalTransactionId2.orElse(""));
    }
}
