package com.kugou.fanxing.recharge.service;

import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeOpenDao;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.bo.KugouOpenBusinessBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.*;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class AlipayRechargeServiceUnitTest {

    @InjectMocks
    private AlipayRechargeService alipayRechargeService;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    private RechargeCouponService rechargeCouponService;
    @Mock
    private RechargeAcrossDao rechargeAcrossDao;
    @Mock
    private RechargeCommonService rechargeCommonService;
    @Mock
    private RechargeOpenDao rechargeOpenDao;
    @Mock
    private RechargeConfig rechargeConfig;
    /**
     * 通用请求参数
     */
    private final WebCommonParam webCommonParam = new WebCommonParam();
    private final RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();

    @Before
    public void before() {
        webCommonParam.setKugouId(1290249156L);
        webCommonParam.setPid(0);
        webCommonParam.setIp("************");
        rechargeAcrossPO.setRechargeOrderNum("R092021010719280960882719");
        rechargeAcrossPO.setKugouId(1290249156);
        when(this.rechargeCommonService.checkPreconditionForGetOrder(anyString(), any(WebCommonParam.class), any(GetOrderRequest.class))).thenReturn(SysResultCode.SUCCESS);
        when(orderIdService.generateRechargeOrderNumForAcross()).thenReturn("R092021010719280960882719");
        when(orderIdService.getYearMonthFromRechargeOrderNum(anyString())).thenReturn(Optional.of("202101"));
        when(rechargeAcrossDao.add(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
        when(rechargeCommonService.buildRechargeAcross(anyInt(),
                anyLong(), anyLong(), anyString(), any(PayTypeIdEnum.class),
                any(BigDecimal.class), anyString(), any(CoinTypeEnum.class), any())).thenReturn(rechargeAcrossPO);
        Map<String, Object> content = Maps.newHashMap();
        content.put("actionUrl", "_input_charset=utf-8&body=%E6%98%9F%E5%B8%81%E5%85%85%E5%80%BC%E6%9C%8D%E5%8A%A1&notify_url=http%3A%2F%2Fkupay2.kugou.com%3A6080%2Fv1%2Fmobilealipay%2Fmobile_common_notify&out_trade_no=01202310111143230100011459&partner=2088001568579021&payment_type=1&seller_id=vip%40kugou.com&service=mobile.securitypay.pay&subject=%E6%98%9F%E5%B8%81%E5%85%85%E5%80%BC%E6%9C%8D%E5%8A%A1&total_fee=0.02&sign=Qk3oona0mApC4KoP%2BDLzndC9RFuRhPC5wfr2YIiv32IpMLY5SYQoFb9DgEMQRQw9IIAVer%2B%2F%2B7JsFUrKzuRPhFjDeFVsNuc0cIvCdNtf7fqB76jn2Mk9Y9Sd6do6aeVfAXu4b6itVdwc73kELrdgCYhldKpNXlw7w31r02TI%2B00%3D&sign_type=RSA");
        when(this.rechargeCommonService.createRechargeResultForMobileByPost(any(RechargeAcrossPO.class), anyMap(), anyMap())).thenReturn(content);
    }

    @Test(expected = BizException.class)
    public void getOrderForAlipay() {
        AlipayRequest alipayRequest = new AlipayRequest();
        alipayRequest.setCouponId(0);
        alipayRequest.setAmount(BigDecimal.valueOf(100));
        when(rechargeCommonService.checkPreconditionForGetOrder(anyString(), any(WebCommonParam.class), any(GetOrderRequest.class)))
                .thenThrow(new BizException(SysResultCode.RECHARGE_PARAM_ERROR));
        alipayRechargeService.getOrderForAlipay(webCommonParam, alipayRequest, "v1");
    }

    @Test(expected = BizException.class)
    public void getOrderForAlipayMobileException() {
        AlipayRequest alipayRequest = new AlipayRequest();
        alipayRequest.setCouponId(0);
        alipayRequest.setAmount(BigDecimal.valueOf(100));
        when(rechargeCommonService.checkPreconditionForGetOrder(anyString(), any(WebCommonParam.class), any(GetOrderRequest.class)))
                .thenThrow(new BizException(SysResultCode.RECHARGE_PARAM_ERROR));
        alipayRechargeService.getOrderForAlipayMobile(webCommonParam, alipayRequest, CoinTypeEnum.STAR_COIN, false);
    }

    @Test
    public void getOrderForAlipayMobile() {
        AlipayRequest alipayRequest = new AlipayRequest();
        alipayRequest.setCouponId(0);
        alipayRequest.setAmount(BigDecimal.valueOf(100));
        alipayRequest.setPayTypeIdEnum(PayTypeIdEnum.PAY_TYPE_ID_30);
        Map<String, Object> payload = alipayRechargeService.getOrderForAlipayMobile(webCommonParam, alipayRequest,
                CoinTypeEnum.STAR_COIN, false);
        Assert.assertEquals(2, payload.size());
    }

    @Test(expected = BizException.class)
    public void getOrderForAlipayH5() {
        GetOrderH5Request request = new GetOrderH5Request();
        request.setCouponId(0);
        request.setAmount(BigDecimal.valueOf(100));
        when(rechargeCommonService.checkPreconditionForGetOrder(anyString(), any(WebCommonParam.class), any(GetOrderRequest.class)))
                .thenThrow(new BizException(SysResultCode.RECHARGE_PARAM_ERROR));
        alipayRechargeService.getOrderForAlipayH5(webCommonParam, request);
    }

    @Test(expected = BizException.class)
    public void getOrderAlipayM() {
        OpenAlipayMRequest request = new OpenAlipayMRequest();
        request.setCouponId(0);
        request.setAmount(BigDecimal.valueOf(100));
        when(rechargeCommonService.checkPreconditionForGetOrder(anyString(), any(WebCommonParam.class), any(GetOrderRequest.class)))
                .thenThrow(new BizException(SysResultCode.RECHARGE_PARAM_ERROR));
        KugouOpenBusinessBO kugouOpenBusinessBO = new KugouOpenBusinessBO();
        alipayRechargeService.getOrderAlipayM(webCommonParam, request, kugouOpenBusinessBO);
    }

    @Test(expected = AckException.class)
    public void getOrderForAlipayQr() {
        AlipayRequest alipayRequest = new AlipayRequest();
        alipayRequest.setAmount(new BigDecimal("0.02"));
        when(rechargeCommonService.checkPreconditionForGetOrder(anyString(), any(WebCommonParam.class), any(GetOrderRequest.class))).thenReturn(SysResultCode.SUCCESS);
        when(rechargeCouponService.handleCouponForOrder(anyLong(), anyLong(), any(BigDecimal.class), any(CoinTypeEnum.class))).thenThrow(AckException.class);
        when(rechargeCouponService.handleCouponForOrder(anyLong(), anyLong(), any(BigDecimal.class), any(CoinTypeEnum.class))).thenThrow(AckException.class);
        alipayRechargeService.getOrderForAlipayQr(webCommonParam, alipayRequest);
    }
}
