package com.kugou.fanxing.recharge.service.callback;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.vo.UserEntity;
import com.kugou.fanxing.recharge.service.RechargeCommonService;
import com.kugou.fanxing.recharge.service.RechargeCouponService;
import com.kugou.fanxing.recharge.service.RechargeOrderService;
import com.kugou.fanxing.recharge.service.after.AfterRechargeService;
import com.kugou.fanxing.recharge.service.common.ConsumeRpcService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.service.stat.UserEverRechargeStatService;
import com.kugou.fanxing.recharge.thrift.callback.ConsumeParam;
import com.kugou.fanxing.recharge.thrift.callback.PurchaseCoinForIosRequest;
import com.kugou.fanxing.recharge.thrift.callback.PurchaseForIosRequest;
import com.kugou.fanxing.recharge.thrift.callback.RenewalsForIosRequest;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.recharge.util.ModelUtils;
import com.kugou.fanxing.thrift.pay.v2.ConvertTradeNoRequest;
import com.kugou.fanxing.thrift.pay.v2.ConvertTradeNoResponse;
import com.kugou.fanxing.thrift.pay.v2.PlatformPayV2Service;
import com.kugou.fanxing.thrift.pay.v2.TradeNoMappingDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;
import java.util.Optional;
import java.util.function.UnaryOperator;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AppleCallbackServiceUnitTest {

    private static final String SOURCE_ORDER = "" +
            "  {\n" +
            "    \"rechargeId\": 1452323629382974510,\n" +
            "    \"rechargeOrderNum\": \"TID1000000756956362\",\n" +
            "    \"consumeOrderNum\": null,\n" +
            "    \"addTime\": 1608536177,\n" +
            "    \"rechargeTime\": null,\n" +
            "    \"kugouId\": **********,\n" +
            "    \"agentKugouId\": 0,\n" +
            "    \"fromKugouId\": **********,\n" +
            "    \"coin\": 0.00,\n" +
            "    \"amount\": 1.00,\n" +
            "    \"realAmount\": 0.00,\n" +
            "    \"coinBefore\": 0.00,\n" +
            "    \"coinAfter\": 0.00,\n" +
            "    \"payTypeId\": 1006,\n" +
            "    \"status\": 0,\n" +
            "    \"extraJsonData\": \"{\\\"signBook\\\":\\\"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\\\",\\\"goodsId\\\":\\\"com.fanxing.fxappstore.66coin\\\",\\\"fromType\\\":\\\"moblie7\\\"}\",\n" +
            "    \"refer\": 0,\n" +
            "    \"cFrom\": 2,\n" +
            "    \"channelId\": 1009,\n" +
            "    \"extend\": \"****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\n" +
            "    \"serverRoom\": 0,\n" +
            "    \"reType\": 0,\n" +
            "    \"payTime\": null,\n" +
            "    \"tradeTime\": null,\n" +
            "    \"tradeNo\": null,\n" +
            "    \"partner\": null,\n" +
            "    \"businessId\": null,\n" +
            "    \"money\": 1.00,\n" +
            "    \"coupon\": 0.00,\n" +
            "    \"couponId\": \"\",\n" +
            "    \"couponStatus\": 0,\n" +
            "    \"couponOrderId\": null,\n" +
            "    \"isSandbox\": 1,\n" +
            "    \"statExt\": null\n" +
            "  }" +
            "";

    @InjectMocks
    private AppleCallbackService appleCallbackService;
    @Mock
    private RechargeOrderService rechargeOrderService;
    @Mock
    private UserFacadeService userFacadeService;
    @Mock
    private UserEverRechargeStatService userEverRechargeStatService;
    @Mock
    protected ConsumeRpcService consumeRpcService;
    @Mock
    private RechargeCouponService rechargeCouponService;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    private RechargeAcrossDao rechargeAcrossDao;
    @Mock
    private AfterRechargeService afterRechargeService;
    @Mock
    private PlatformPayV2Service.Iface platformPayV2Service;
    @Mock
    private RechargeCommonService rechargeCommonService;

    @Test
    public void executeIdempotent() {
        RechargeAcrossPO sourceOrder = JSON.parseObject(SOURCE_ORDER, RechargeAcrossPO.class);
        when(userFacadeService.getUserIdByKugouId(anyLong(), anyBoolean())).thenReturn(Optional.of(**********L));
        when(consumeRpcService.rechargeCoin(any(RechargeAcrossPO.class))).thenReturn(true);
        UnaryOperator<RechargeAcrossPO> map2TargetOrder = source -> {
            RechargeAcrossPO target = ModelUtils.fromUnchecked(source, RechargeAcrossPO.class);
            target.setStatus(1);
            target.setRechargeTime(DateHelper.getCurrentSeconds());
            target.setReType(ReTypeEnum.RETYPE_RECHARGE.getReTypeId());
            return target;
        };
        when(rechargeCouponService.extractValidCoupon(any(RechargeAcrossPO.class))).thenReturn(Optional.empty());
        when(orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202012");
        when(this.rechargeAcrossDao.insertIgnore(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
        doNothing().when(afterRechargeService).afterRechargeSuccess(any(RechargeAcrossPO.class));
        Assert.assertTrue(this.appleCallbackService.executeIdempotent(sourceOrder, map2TargetOrder));
    }

    /**
     * array (
     *   'signBook' => '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',
     *   'tradeNo' => '1000000756956362',
     *   'goodsId' => 'com.fanxing.fxappstore.66coin',
     *   'businessExt' => '',
     *   'orderNum' => 'TID1000000756956362',
     *   'pid' => '2',
     *   'type' => '1',
     *   'version' => '49929',
     *   'tradeTime' => '2020-12-21 15:36:13',
     *   'money' => '1',
     *   'partner' => '',
     *   'time' => '1608536400',
     *   'isSandbox' => '1',
     *   'key' => '********************************',
     *   'channelId' => '1009',
     *   'kugouId' => '**********',
     *   'coin' => '66',
     * )
     */
    private PurchaseCoinForIosRequest buildPurchaseCoinForIosRequest() {
        PurchaseCoinForIosRequest request = new PurchaseCoinForIosRequest();
        request.setTime(1608536400);
        request.setKugouId(**********L);
        request.setMoney("1");
        request.setCoin("66");
        request.setPid(2);
        request.setChannelId(1009);
        request.setGoodsId("com.fanxing.fxappstore.66coin");
        request.setIsSandbox(1);
        request.setTradeNo("1000000756956362");
        request.setBusinessExt("");
        request.setOrderNum("TID1000000756956362");
        request.setType(1);
        request.setVersion("49929");
        request.setTradeTime("2020-12-21 15:36:13");
        request.setPartner("");
        request.setKey("********************************");
        request.setSignBook("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");
        return request;
    }

    @Test
    public void purchaseCoin() throws TException {
        when(orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202012");
        when(rechargeAcrossDao.insertIgnore(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
        doNothing().when(afterRechargeService).afterRechargeSuccess(any(RechargeAcrossPO.class));
        ConvertTradeNoResponse response = new ConvertTradeNoResponse();
        response.setCode(0);
        response.setData(new TradeNoMappingDTO().setRechargeOrderNum("R09201912S1TID430000555834093").setTradeNo("TID430000555834093"));
        when(platformPayV2Service.convertTradeNoToRechargeOrderNum(any(ConvertTradeNoRequest.class))).thenReturn(response);
        PurchaseCoinForIosRequest request = this.buildPurchaseCoinForIosRequest();
        when(this.rechargeOrderService.specialRechargeOrderNumDeal(anyString(), anyInt())).thenReturn(Optional.empty());
        when(userFacadeService.getUserIdByKugouId(anyLong(), anyBoolean())).thenReturn(Optional.of(request.getKugouId()));
        JsonResult<Map<String, String>> result = this.appleCallbackService.purchaseCoin(request);
        Assert.assertEquals(SysResultCode.RECHARGE_SYS_ERROR.getCode(), result.getCode());
        when(this.rechargeOrderService.specialRechargeOrderNumDeal(anyString(), anyInt())).thenReturn(Optional.of("R09202012S1TID1000000756956362"));
        RechargeAcrossPO rechargeAcrossPO = JSON.parseObject(SOURCE_ORDER, RechargeAcrossPO.class);
        rechargeAcrossPO.setStatus(1);
        when(this.rechargeOrderService.queryByRechargeOrderNum(anyString())).thenReturn(Optional.of(rechargeAcrossPO));
        result = this.appleCallbackService.purchaseCoin(request);
        when(consumeRpcService.rechargeCoin(any(RechargeAcrossPO.class))).thenReturn(true);
        Assert.assertEquals(SysResultCode.SUCCESS.getCode(), result.getCode());
        when(this.rechargeOrderService.queryByRechargeOrderNum(anyString())).thenReturn(Optional.empty());
        when(consumeRpcService.rechargeCoin(any())).thenReturn(true);
        try {
            result = this.appleCallbackService.purchaseCoin(request);
            Assert.assertEquals(SysResultCode.SUCCESS.getCode(), result.getCode());
        } catch (BizException e) {
            Assert.assertEquals(SysResultCode.RECHARGE_SYS_ERROR.getMsg(), e.getMessage());
        }
    }

    @Test
    public void queryUserCoin() {
        long kugouId = **********L;
        when(userEverRechargeStatService.getUserEntity(anyLong(),anyInt(), anyInt())).thenReturn(Optional.of(new UserEntity().setCoin(100)));
        double userCoin = this.appleCallbackService.queryUserCoin(kugouId, CoinTypeEnum.STAR_COIN.getCoinType());
        Assert.assertTrue(userCoin == 100);
    }

    @Test
    public void mockCreateOrder() {
        PurchaseCoinForIosRequest request = buildPurchaseCoinForIosRequest();
        String rechargeOrderNum = "R09202012S1TID1000000756069321";
        RechargeAcrossPO rechargeAcrossPO = this.appleCallbackService.mockCreateOrder(request, rechargeOrderNum);
        Assert.assertNotNull(rechargeAcrossPO);
    }

    @Test
    public void buildExtraJsonData() {
        String signBook = "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";
        String extraJsonData = this.appleCallbackService.buildExtraJsonData(signBook, "com.fanxing.fxappstore.66coin", Maps.newHashMap());
        Assert.assertTrue(StringUtils.isNotBlank(extraJsonData));
    }

    @Test
    public void handleRechargeCoupon() {
        String rechargeOrderNum = "R09202012S1TID1000000756069321";
        RechargeAcrossPO originalOrder = new RechargeAcrossPO();
        RechargeAcrossPO callbackOrder = new RechargeAcrossPO();
        when(rechargeCouponService.extractValidCoupon(any(RechargeAcrossPO.class))).thenReturn(Optional.empty());
        RechargeAcrossPO targetOrder = this.appleCallbackService.handleRechargeCoupon(rechargeOrderNum, originalOrder, callbackOrder);
        Assert.assertEquals(0L, targetOrder.getCouponId());
        Assert.assertEquals(0L, targetOrder.getCouponOrderId());
        Assert.assertNull(targetOrder.getCoupon());
    }

    @Test
    public void purchaseProducts() {
        RechargeAcrossPO source = new RechargeAcrossPO();
        source.setStatus(1);
        source.setRechargeOrderNum("R09202106S1TID1000000834802292");
        source.setExtraJsonData("{\"signBook\":\"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\",\"fromType\":\"moblie7\",\"goodsId\":\"com.kugou.fanxingappstore.smallguard12\",\"businessId\":\"1521544574655315694\"}");
        source.setExtend("eyJjYWxsQmFja1NpZ24iOiJmMGI0Y2I3ODU5ZjMzN2MzY2I2ZDg5MDlkMWFmNzE1ZSIsImNhbGxCYWNrQXJnIjp7ImJ1c2luZXNzSWQiOjE1MjE1NDQ1NzQ2NTUzMTU2OTQsImJ1c2luZXNzVHlwZSI6IiIsImJ1c2luZXNzVGltZSI6MTYyNTAzOTY5NSwicGF5VGltZSI6MTYyNTAzOTY5NSwiZnJvbUt1Z291SWQiOjEyOTAyNDkxNTYsInRvS3Vnb3VJZCI6OTc1MDA2OTM2LCJ0b3BpYyI6ImZ4LmJ1eUxpdHRsZUd1YXJkIiwicmVmZXIiOjAsImNGcm9tIjoyLCJjaGFubmVsSWQiOjEwMDksImFtb3VudCI6IjEyIiwicGF5VHlwZUlkIjoxMDA2LCJhZGRUaW1lIjoxNjI1MDM5Njk1LCJrdWdvdUlkIjoxMjkwMjQ5MTU2LCJ2ZXJzaW9uIjoiMjAxNzAxMTEiLCJyZWJhdGUiOjAsInJlVHlwZSI6MSwidXNlckZ1bmRQbGF0UGFyYW0iOnsicmVjZWl2ZXJEZXBhcnRtZW50SWQiOiIwIiwiYWN0aW9uSWQiOiIyMzEiLCJnaWZ0SWQiOiIwIiwic2VuZGVyRGVwYXJ0bWVudElkIjoiMCIsImNvaW4iOiI4MDAiLCJzZW5kZXJNaW5vclByb2R1Y3RJZCI6IjAiLCJzZW5kZXJQcm9kdWN0SWQiOiIwIiwic2VuZGVyQ2hhbm5lbElkIjoiMCIsImFjY291bnRDaGFuZ2VUeXBlIjoiMTEwMDc4Iiwic2VuZGVyU3ViQ2hhbm5lbElkIjoiMCIsInRvS3Vnb3VJZCI6Ijk3NTAwNjkzNiIsImZ4Y0NoYW5nZURlc2MiOiLotK3kubDlsI/lrojmiqQiLCJnaWZ0TnVtIjoiMSIsImdpZnROYW1lIjoi5bCP5a6I5oqkIiwiZnJvbUt1Z291SWQiOiIxMjkwMjQ5MTU2Iiwicm9vbUlkIjoiMTAxNDYwNiIsInNlbmRlckhhcmR3YXJlUGxhdGZvcm0iOiIwIiwiZXh0Ijoie1wiZGF5c1wiOjMwLFwic3ViU3RhdHVzXCI6MCxcInJlY2hhcmdlSWRcIjpcIlIwOTIwMjEwNlMxVElEMTAwMDAwMDgzNDgwMjI5MlwifSIsInRpbWVzdGFtcCI6IjE2MjUwMzk2OTUiLCJnbG9iYWxJZCI6IjE1MjE1NDQ1NzQ2NTUzMTU2OTQiLCJwaWQiOiIyIiwiaXAiOiIxNzIuMTkuNDEuMTUiLCJzaWduIjoiZDU5ODBiZWRlNDlhMzdhOTJhNGY4MDBlOGRlNmIwMmYifSwiYnVzaW5lc3NFeHQiOiJ7XCJkYXlzXCI6MzAsXCJzdWJTdGF0dXNcIjowfSJ9fQ==");
        when(this.rechargeOrderService.specialRechargeOrderNumDeal(anyString(), anyInt())).thenReturn(Optional.of("R09202106S1TID1000000834802292"));
        when(orderIdService.generateGlobalId()).thenReturn(1521544574655315694L);
        when(this.rechargeOrderService.queryByRechargeOrderNum(anyString())).thenReturn(Optional.of(source));
        PurchaseForIosRequest request = new PurchaseForIosRequest();
        request.setOrderNo("TID1000000834802292");
        JsonResult<Map<String, String>> result = this.appleCallbackService.purchaseProducts(request);
        Assert.assertEquals(SysResultCode.SUCCESS.getCode(), result.getCode());
    }

    @Test
    public void mockPurchaseOrder() {
        when(orderIdService.generateGlobalId()).thenReturn(123456L);
        String rechargeOrderNum = "";
        long consumeOrderNo = 1L;
        PurchaseForIosRequest purchaseRequest = new PurchaseForIosRequest();
        purchaseRequest.setSignBook("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");
        purchaseRequest.setGoodsId("com.kugou.fanxingappstore.shhu1048");
        purchaseRequest.setBusinessId("1446219001364501362");
        purchaseRequest.setTime(1607080679);
        purchaseRequest.setKugouId(732115211);
        purchaseRequest.setMoney("1084");
        purchaseRequest.setCoin("108400");
        purchaseRequest.setPid(6);
        purchaseRequest.setChannelId(0);
        purchaseRequest.setAreaId(1);
        RechargeAcrossPO rechargeAcrossPO1 = this.appleCallbackService.mockPurchaseOrder(purchaseRequest, rechargeOrderNum, consumeOrderNo);
        Assert.assertNotNull(rechargeAcrossPO1);
        Assert.assertEquals(1, rechargeAcrossPO1.getReType());
        Assert.assertEquals(0, rechargeAcrossPO1.getAreaId());
        RenewalsForIosRequest renewalsRequest = new RenewalsForIosRequest();
        renewalsRequest.setSignBook("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");
        renewalsRequest.setGoodsId("com.kugou.fanxingappstore.shhu1048");
        renewalsRequest.setBusinessId("1446219001364501362");
        renewalsRequest.setTime(1607080679);
        renewalsRequest.setKugouId(732115211);
        renewalsRequest.setMoney("1084");
        renewalsRequest.setCoin("108400");
        renewalsRequest.setPid(6);
        renewalsRequest.setChannelId(0);
        renewalsRequest.setAreaId(1);
        RechargeAcrossPO rechargeAcrossPO2 = this.appleCallbackService.mockPurchaseOrder(renewalsRequest, rechargeOrderNum, consumeOrderNo);
        Assert.assertNotNull(rechargeAcrossPO2);
        Assert.assertEquals(2, rechargeAcrossPO2.getReType());
        Assert.assertEquals(0, rechargeAcrossPO2.getAreaId());
    }

    @Test
    public void renewalsProducts() {
        when(this.rechargeOrderService.specialRechargeOrderNumDeal(anyString(), anyInt())).thenReturn(Optional.of("R09202106S1TID1000000834802292"));
        when(orderIdService.generateGlobalId()).thenReturn(1L);
        RechargeAcrossPO rechargeAcrossPO = JSON.parseObject(SOURCE_ORDER, RechargeAcrossPO.class);
        rechargeAcrossPO.setStatus(1);
        rechargeAcrossPO.setExtraJsonData("{\"signBook\":\"...\",\"fromType\":\"moblie7\",\"goodsId\":\"com.kugou.fanxingappstore.doufenautorenewa1\",\"businessId\":\"1787550828417042028\"}");
        when(this.rechargeOrderService.queryByRechargeOrderNum(anyString())).thenReturn(Optional.of(rechargeAcrossPO));
        RenewalsForIosRequest request = new RenewalsForIosRequest();
        request.setOrderNo("xxxxx");
        JsonResult<Map<String, String>> result = this.appleCallbackService.renewalsProducts(request);
        Assert.assertEquals(SysResultCode.SUCCESS.getCode(), result.getCode());
    }


    @Test
    public void buildTargetOrder() {
        PurchaseForIosRequest purchaseRequest = new PurchaseForIosRequest();
        purchaseRequest.setSignBook("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");
        purchaseRequest.setGoodsId("com.kugou.fanxingappstore.shhu1048");
        purchaseRequest.setBusinessId("1446219001364501362");
        purchaseRequest.setTime(1607080679);
        purchaseRequest.setKugouId(732115211);
        purchaseRequest.setMoney("1084");
        purchaseRequest.setCoin("108400");
        purchaseRequest.setPid(6);
        purchaseRequest.setChannelId(0);
        purchaseRequest.setTradeTime("2021-06-12 12:12:12");
        purchaseRequest.setAreaId(1);
        RenewalsForIosRequest renewalsRequest = new RenewalsForIosRequest();
        renewalsRequest.setSignBook("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");
        renewalsRequest.setGoodsId("com.kugou.fanxingappstore.shhu1048");
        renewalsRequest.setBusinessId("1446219001364501362");
        renewalsRequest.setTime(1607080679);
        renewalsRequest.setKugouId(732115211);
        renewalsRequest.setMoney("1084");
        renewalsRequest.setCoin("108400");
        renewalsRequest.setPid(6);
        renewalsRequest.setChannelId(0);
        renewalsRequest.setAreaId(1);
        renewalsRequest.setTradeTime("2021-06-12 12:12:12");
        when(rechargeCommonService.buildExtendStrForIosPurchase(any(RechargeAcrossPO.class), any(ConsumeParam.class), anyString(), anyString())).thenReturn("");
        RechargeAcrossPO target1 = this.appleCallbackService.buildTargetOrder(purchaseRequest, new RechargeAcrossPO());
        RechargeAcrossPO target2 = this.appleCallbackService.buildTargetOrder(renewalsRequest, new RechargeAcrossPO());
        Assert.assertEquals(1, target1.getStatus());
        Assert.assertEquals(1, target2.getStatus());
        Assert.assertEquals(1, target1.getAreaId());
        Assert.assertEquals(1, target2.getAreaId());
    }
}
