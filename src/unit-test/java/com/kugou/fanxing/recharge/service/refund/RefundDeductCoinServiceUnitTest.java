package com.kugou.fanxing.recharge.service.refund;

import com.kugou.fanxing.recharge.alert.AlerterFacade;
import com.kugou.fanxing.recharge.config.RefundConfig;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.AppStoreRefundOrderDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossOrderNumDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundDeductCoinOrderDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundOrderDao;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.consume.ConsumeReadService;
import com.kugou.fanxing.recharge.service.stat.UserEverRechargeStatService;
import com.kugou.fanxing.thrift.freeze.service.PlatformAddCoinService;
import com.kugou.fanxing.thrift.plat.user.UserPlatBizService;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class RefundDeductCoinServiceUnitTest {

    @InjectMocks
    private RefundDeductCoinService refundDeductCoinService;
    @Mock
    private PlatformAddCoinService.Iface platformAddCoinService;
    @Mock
    private AppStoreRefundOrderDao appStoreRefundOrderDao;
    @Mock
    private RechargeAcrossOrderNumDao rechargeAcrossOrderNumDao;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    private RefundOrderDao refundOrderDao;
    @Mock
    private AlerterFacade alerterFacade;
    @Mock
    private UserEverRechargeStatService userEverRechargeStatService;
    @Mock
    private RefundDeductCoinOrderDao refundDeductCoinOrderDao;
    @Mock
    private RefundDeductCoinTransactionService refundDeductCoinTransactionService;
    @Mock
    private UserPlatBizService.Iface userPlatBizService;
    @Mock
    private ConsumeReadService consumeReadService;
    @Mock
    private RefundConfig refundLogicService;
    @Mock
    private ApolloConfigService apolloConfigService;

    @Test
    public void isBlackTransactionId() {
        when(refundLogicService.getBlackTransactionId()).thenReturn(Lists.newArrayList("260000779289653", "260000779289654"));
        Assert.assertTrue(this.refundDeductCoinService.isBlackTransactionId("260000779289653"));
        Assert.assertFalse(this.refundDeductCoinService.isBlackTransactionId("260000779289655"));
    }
}
