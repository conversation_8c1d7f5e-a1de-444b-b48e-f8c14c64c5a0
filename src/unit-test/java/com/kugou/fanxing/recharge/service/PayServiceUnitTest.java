package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeCouponDao;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.vo.RechargeAcrossListVO;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.thrift.PurchaseOrder;
import com.kugou.fanxing.recharge.thrift.PurchaseProductRequestV2;
import com.kugou.fanxing.recharge.thrift.RechargeAcrossDTO;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.Pagination;
import com.kugou.platform.after.recharge.asset.allocate.thrift.*;
import com.kugou.platform.recharge.queryservice.thrift.RechargeInfoQueryThriftService;
import com.kugou.platform.recharge.queryservice.thrift.ResultInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class PayServiceUnitTest {

    @Mock
    private RechargeCouponDao rechargeCouponDao;
    @Mock
    private RechargeAcrossDao rechargeAcrossDao;
    @Mock
    private RechargeInfoQueryThriftService.Iface rechargeInfoQueryThriftService;
    @Mock
    private ApolloConfigService apolloConfigService;
    @Mock
    private AfterRechargeAssetAllocateReadService.Iface afterRechargeAsserAllocateReadService;
    @Mock
    private OrderIdService orderIdService;
    @InjectMocks
    private PayService payService;

    @Test
    public void getRechargeList() throws TException {
        when(rechargeAcrossDao.getRechargeAcrossList(anyString(), anyLong(), any(int[].class), anyInt(), any(Pagination.class))).thenReturn(Lists.newArrayList());
        when(rechargeAcrossDao.getRechargeAcrossListCount(anyString(), anyLong(), any(int[].class), anyInt())).thenReturn(0);
        when(rechargeInfoQueryThriftService.listUserRechargeRebateInfos(anyString(), anyList())).thenReturn(new ResultInfo().setRet(0).setData(Lists.emptyList()));
        RechargeAcrossListVO rechargeAcrossListVO = payService.getRechargeList(**********L, 0, 1006, new Pagination.Builder(1, 10).build());
        Assert.assertNotNull(rechargeAcrossListVO);
        Assert.assertEquals(0, rechargeAcrossListVO.getList().size());
        Assert.assertEquals(0, rechargeAcrossListVO.getCount());
    }

    @Test
    public void getRechargeListException() throws TException {
        when(rechargeAcrossDao.getRechargeAcrossList(anyString(), anyLong(), any(int[].class), anyInt(), any(Pagination.class))).thenReturn(Lists.newArrayList());
        when(rechargeAcrossDao.getRechargeAcrossListCount(anyString(), anyLong(), any(int[].class), anyInt())).thenReturn(0);
        when(rechargeInfoQueryThriftService.listUserRechargeRebateInfos(anyString(), anyList())).thenThrow(new ContextedRuntimeException("test"));
        RechargeAcrossListVO rechargeAcrossListVO = payService.getRechargeList(**********L, 0, 1006, new Pagination.Builder(1, 10).build());
        Assert.assertNotNull(rechargeAcrossListVO);
        Assert.assertEquals(0, rechargeAcrossListVO.getList().size());
        Assert.assertEquals(0, rechargeAcrossListVO.getCount());
    }

    @Test
    public void parseArgumentInExtend() {
        String extend = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        Optional<String> optionalConsumeType = this.payService.parseArgumentInExtend(extend, "$.callBackArg.consumeArgs.consumeType");
        Assert.assertTrue("teaseAnchor".equalsIgnoreCase(optionalConsumeType.orElseThrow(() -> new ContextedRuntimeException("test"))));
        extend = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        Optional<String> optionalAccountChangeType = this.payService.parseArgumentInExtend(extend, "$.callBackArg.userFundPlatParam.accountChangeType");
        Assert.assertTrue("110014".equalsIgnoreCase(optionalAccountChangeType.orElseThrow(() -> new ContextedRuntimeException("test"))));
    }

    @Test
    public void parseArgumentInExtendException() {
        String extend = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        Optional<String> optionalConsumeType = this.payService.parseArgumentInExtend(extend, "$.callBackArg.consumeArgs.");
        Assert.assertFalse(optionalConsumeType.isPresent());
    }

    @Test
    public void getRechargeListForPCNormal() {
        String month = "202002";
        long kugouId = **********L;
        Pagination pagination = new Pagination.Builder(1, 12).build();
        int[] reTypes = {ReTypeEnum.RETYPE_RECHARGE.getReTypeId()};
        when(rechargeAcrossDao.getRechargeAcrossList(month, kugouId, reTypes, 0, pagination)).thenReturn(Lists.emptyList());
        when(rechargeCouponDao.queryByRechargeOrderNums(any(List.class))).thenReturn(Lists.emptyList());
        List<RechargeAcrossDTO> rechargeAcrossDTOList = this.payService.getRechargeListForPC(month, kugouId, pagination);
        Assert.assertNotNull(rechargeAcrossDTOList);
        Assert.assertEquals(0, rechargeAcrossDTOList.size());
    }

    @Test
    public void getRechargeListByUserWithTimeNormal() {
        String month = "202002";
        long startTime = DateHelper.getCurrentSeconds() - 3600;
        long endTime = DateHelper.getCurrentSeconds();
        long kugouId = **********L;
        Pagination pagination = new Pagination.Builder(1, 12).build();
        int[] reTypes = {ReTypeEnum.RETYPE_RECHARGE.getReTypeId()};
        when(rechargeAcrossDao.getRechargeAcrossListWithTime(month, kugouId, reTypes, 0, startTime,endTime,pagination)).thenReturn(Lists.emptyList());
        when(rechargeAcrossDao.getRechargeAcrossListCountWithTime(month, kugouId, reTypes, 0, startTime,endTime)).thenReturn(0);
        when(apolloConfigService.getRecordremierMonth()).thenReturn("202001");
        List<RechargeAcrossDTO> rechargeAcrossDTOList = this.payService.getRechargeListByUserWithTime(month,kugouId, startTime,endTime,pagination);
        Assert.assertNotNull(rechargeAcrossDTOList);
        Assert.assertEquals(0, rechargeAcrossDTOList.size());
    }

    @Test
    public void getRechargeListByUserWithTimeStarTimeNotRight() {
        String month = "202002";
        long startTime = DateHelper.getCurrentSeconds();
        long endTime = DateHelper.getCurrentSeconds() - 10;
        long kugouId = **********L;
        Pagination pagination = new Pagination.Builder(1, 12).build();
        int[] reTypes = {ReTypeEnum.RETYPE_RECHARGE.getReTypeId()};
        when(rechargeAcrossDao.getRechargeAcrossListWithTime(month, kugouId, reTypes, 0, startTime,endTime,pagination)).thenReturn(Lists.emptyList());
        when(rechargeAcrossDao.getRechargeAcrossListCountWithTime(month, kugouId, reTypes, 0, startTime,endTime)).thenReturn(0);
        when(apolloConfigService.getRecordremierMonth()).thenReturn("202001");
        List<RechargeAcrossDTO> rechargeAcrossDTOList = this.payService.getRechargeListByUserWithTime(month,kugouId, startTime,endTime,pagination);
        Assert.assertNotNull(rechargeAcrossDTOList);
        Assert.assertEquals(0, rechargeAcrossDTOList.size());
    }

    @Test
    public void getRechargeListByUserWithTimeMonthNotRight() {
        String month = "201912";
        long startTime = DateHelper.getCurrentSeconds() - 3600;
        long endTime = DateHelper.getCurrentSeconds();
        long kugouId = **********L;
        Pagination pagination = new Pagination.Builder(1, 12).build();
        int[] reTypes = {ReTypeEnum.RETYPE_RECHARGE.getReTypeId()};
        when(rechargeAcrossDao.getRechargeAcrossListWithTime(month, kugouId, reTypes, 0, startTime,endTime,pagination)).thenReturn(Lists.emptyList());
        when(rechargeAcrossDao.getRechargeAcrossListCountWithTime(month, kugouId, reTypes, 0, startTime,endTime)).thenReturn(0);
        when(apolloConfigService.getRecordremierMonth()).thenReturn("202001");
        List<RechargeAcrossDTO> rechargeAcrossDTOList = this.payService.getRechargeListByUserWithTime(month,kugouId, startTime,endTime,pagination);
        Assert.assertNotNull(rechargeAcrossDTOList);
        Assert.assertEquals(0, rechargeAcrossDTOList.size());
    }

    @Test(expected = ContextedRuntimeException.class)
    public void getRechargeListForPCException() {
        String month = "202002";
        long kugouId = **********L;
        Pagination pagination = new Pagination.Builder(1, 12).build();
        int[] reTypes = {ReTypeEnum.RETYPE_RECHARGE.getReTypeId()};
        when(rechargeAcrossDao.getRechargeAcrossList(month, kugouId, reTypes, 0, pagination)).thenThrow(new ContextedRuntimeException("test"));
        when(rechargeCouponDao.queryByRechargeOrderNums(any(List.class))).thenReturn(Lists.emptyList());
        this.payService.getRechargeListForPC(month, kugouId, pagination);
    }

    @Test
    public void getRechargeListWithNoRebate() throws TException {
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setIsSandbox(0);
        rechargeAcrossPO.setMoney(new BigDecimal("1"));
        rechargeAcrossPO.setAmount(new BigDecimal("100"));
        rechargeAcrossPO.setCoin(new BigDecimal("100"));
        rechargeAcrossPO.setKugouId(**********L);
        List<RechargeAcrossPO> rechargeAcrossPOList = new ArrayList<>();
        rechargeAcrossPOList.add(rechargeAcrossPO);
        RechargePresentItem rechargePresentItem = new RechargePresentItem();
        rechargePresentItem.setAssetId(0);
        rechargePresentItem.setGoodsType(GoodsType.PRESENT.getValue());
        List<RechargePresentItem> presentItems = new ArrayList<>();
        presentItems.add(rechargePresentItem);

        Map<Long,List<RechargePresentItem>> rechargePresentItems = new HashMap<>();
        rechargePresentItems.put(100L,presentItems);
        when(afterRechargeAsserAllocateReadService.getRechargePresentInfo(any(GetRechargePresentRequest.class))).thenReturn(new PresentInfo().setRet(0).setData(rechargePresentItems));
        when(apolloConfigService.isCheckLittleRebateWhenLost()).thenReturn(false);
        when(rechargeAcrossDao.getRechargeAcrossList(anyString(), anyLong(), any(int[].class), anyInt(), any(Pagination.class))).thenReturn(rechargeAcrossPOList);
        when(rechargeAcrossDao.getRechargeAcrossListCount(anyString(), anyLong(), any(int[].class), anyInt())).thenReturn(1);
        when(rechargeInfoQueryThriftService.listUserRechargeRebateInfos(anyString(), anyList())).thenThrow(new ContextedRuntimeException("test"));
        RechargeAcrossListVO rechargeAcrossListVO = payService.getRechargeList(**********L, 0, 1006, new Pagination.Builder(1, 10).build());
        Assert.assertNotNull(rechargeAcrossListVO);
        Assert.assertEquals(1, rechargeAcrossListVO.getList().size());
        Assert.assertEquals(1, rechargeAcrossListVO.getCount());
        Assert.assertEquals(0,rechargeAcrossListVO.getList().get(0).getRebateDelay());
        Assert.assertEquals("",rechargeAcrossListVO.getList().get(0).getRebateInfo());

        //开启校验
        when(apolloConfigService.isCheckLittleRebateWhenLost()).thenReturn(true);
        RechargeAcrossListVO rechargeAcrossListVO1 = payService.getRechargeList(**********L, 0, 1006, new Pagination.Builder(1, 10).build());
        Assert.assertNotNull(rechargeAcrossListVO);
        Assert.assertEquals(1, rechargeAcrossListVO1.getList().size());
        Assert.assertEquals(1, rechargeAcrossListVO1.getCount());
        Assert.assertEquals(1,rechargeAcrossListVO1.getList().get(0).getRebateDelay());
    }

}
