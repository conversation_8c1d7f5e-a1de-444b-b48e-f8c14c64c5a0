package com.kugou.fanxing.recharge.service;

import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.model.bo.KugouOpenBusinessBO;
import com.kugou.fanxing.recharge.model.bo.KugouOpenServerBO;
import com.kugou.fanxing.recharge.model.request.KugouOpenDispatchParam;
import com.kugou.fanxing.recharge.model.request.KugouOpenParam;
import com.kugou.fanxing.recharge.util.SignUtils;
import com.kugou.springcloud.http.KugouHttpRequest;
import com.kugou.springcloud.http.KugouHttpResponse;
import com.kugou.springcloud.http.template.KugouHttpTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class KugouOpenServiceUnitTest {

    @InjectMocks
    private KugouOpenService kugouOpenService;
    @Mock
    private KugouHttpTemplate kgrpcProxy;
    @Mock
    private ApolloConfigService apolloConfigService;

    private static final String USER_OPENID = "16c079e70b7de48f02a832791b372546";
    private static final String ACCESS_TOKEN = "ed667d9a9b1a3f85e39adb6bc32d7e71674bf7ee";

    /**
     * Y4OHlpHnPSuITfdVUJCiWjGRbGZAqEvzappid=3244clientver=1dfid=no-dfidmid=no-midserverid=1994servertime=1609144272uuid=no-uuid{"access_token":"767876f6730617c3dc4135bb62aa1aab05bf7efe","method":"game_recharge.getOrderForWechatM","user_openid":"f50e6bca4f09cf96adb85c87df0decdf","openappid":"10063"}Y4OHlpHnPSuITfdVUJCiWjGRbGZAqEvz
     */
    @Test
    public void buildSignByKugouOpenIntrospect() {
        Map<String, String> params = Maps.newHashMap();
        params.put("serverid", "1994");
        params.put("servertime", "1609144272");
        params.put("appid", "3244");
        params.put("clientver", "1");
        params.put("mid", "no-mid");
        params.put("uuid", "no-uuid");
        params.put("dfid", "no-dfid");
        String jsonBody = "{\"access_token\":\"767876f6730617c3dc4135bb62aa1aab05bf7efe\",\"method\":\"game_recharge.getOrderForWechatM\",\"user_openid\":\"f50e6bca4f09cf96adb85c87df0decdf\",\"openappid\":\"10063\"}";
        String expected = SignUtils.buildSignByKugouOpenIntrospect(params, jsonBody, "Y4OHlpHnPSuITfdVUJCiWjGRbGZAqEvz");
        Assert.assertEquals("c4249f16d95b599ff6ff9ca6ff521532", expected);
    }

    @Test
    public void getKugouIdByOpenToken() throws Exception {
        when(this.apolloConfigService.getKugouOpenServerConfig()).thenReturn(new KugouOpenServerBO().setServerId("2144").setServerKey("8FY95AlGShOjf00bZuAfth6Plj4AAoma"));
        KugouHttpResponse kugouHttpResponse = new KugouHttpResponse();
        kugouHttpResponse.setCode(HttpStatus.SC_OK);
        when(kgrpcProxy.post(any(KugouHttpRequest.class))).thenReturn(kugouHttpResponse);
        KugouOpenDispatchParam kugouOpenDispatchParam = buildKugouOpenDispatchParam();
        kugouHttpResponse.setContent("".getBytes(StandardCharsets.UTF_8));
        when(kgrpcProxy.post(any(KugouHttpRequest.class))).thenReturn(kugouHttpResponse);
        Optional<Long> optionalKugouId = this.kugouOpenService.getKugouIdByOpenToken(kugouOpenDispatchParam);
        Assert.assertFalse(optionalKugouId.isPresent());
        kugouHttpResponse.setContent("{\"status\":0,\"error_code\":300013,\"data\":{\"right\":0,\"userid\":\"*********\"}}".getBytes(StandardCharsets.UTF_8));
        when(kgrpcProxy.post(any(KugouHttpRequest.class))).thenReturn(kugouHttpResponse);
        optionalKugouId = this.kugouOpenService.getKugouIdByOpenToken(kugouOpenDispatchParam);
        Assert.assertFalse(optionalKugouId.isPresent());
        kugouHttpResponse.setContent("{\"status\":1,\"error_code\":0,\"data\":{\"right\":1,\"userid\":\"*********\"}}".getBytes(StandardCharsets.UTF_8));
        when(kgrpcProxy.post(any(KugouHttpRequest.class))).thenReturn(kugouHttpResponse);
        optionalKugouId = this.kugouOpenService.getKugouIdByOpenToken(kugouOpenDispatchParam);
        Assert.assertTrue(optionalKugouId.isPresent());
        Assert.assertEquals(*********L, optionalKugouId.get().longValue());
    }

    @Test
    public void buildRequestUri() throws URISyntaxException {
        KugouOpenDispatchParam kugouOpenDispatchParam = buildKugouOpenDispatchParam();
        when(this.apolloConfigService.getKugouOpenServerConfig()).thenReturn(new KugouOpenServerBO().setServerId("2144").setServerKey("8FY95AlGShOjf00bZuAfth6Plj4AAoma"));
        String requestUri = this.kugouOpenService.buildRequestUri(kugouOpenDispatchParam, "");
        log.warn("requestUri: {}", requestUri);
        Assert.assertNotNull(requestUri);
    }

    /**
     * serverid:1994
     * servertime:1607406377
     * appid:3244
     * signature:d0722c6bab15ec706cff04ec9b14faab
     * clientver:1
     * dfid:no-dfid
     * uuid:no-uuid
     * mid:no-mid
     *
     * {
     *     "method": "game_recharge.getOrderForWechatM",
     *     "user_openid": "f50e6bca4f09cf96adb85c87df0decdf",
     *     "openappid": "10063",
     *     "business_data": "{\"amount\":0.1}",
     *     "access_token": "4d5949162bf0a4efb93ce374acbec414ad42b071"
     * }
     */
    private KugouOpenDispatchParam buildKugouOpenDispatchParam() {
        KugouOpenDispatchParam kugouOpenDispatchParam = new KugouOpenDispatchParam();
        kugouOpenDispatchParam.setServerid("1994");
        kugouOpenDispatchParam.setServertime("1607406377");
        kugouOpenDispatchParam.setAppid("3244");
        kugouOpenDispatchParam.setSignature("d0722c6bab15ec706cff04ec9b14faab");
        kugouOpenDispatchParam.setClientver("1");
        kugouOpenDispatchParam.setDfid("no-dfid");
        kugouOpenDispatchParam.setUuid("no-uuid");
        kugouOpenDispatchParam.setMid("no-mid");
        kugouOpenDispatchParam.setMethod("game_recharge.getOrderForAlipayM");
        kugouOpenDispatchParam.setUser_openid("f50e6bca4f09cf96adb85c87df0decdf");
        kugouOpenDispatchParam.setOpenappid("10063");
        kugouOpenDispatchParam.setBusiness_data("{\"amount\":1}");
        kugouOpenDispatchParam.setAccess_token("4d5949162bf0a4efb93ce374acbec414ad42b071");
        return kugouOpenDispatchParam;
    }

    /**
     * 构建酷狗开放平台调用参数
     */
    @Test
    public void getOrderForWechatM() {
        KugouOpenBusinessBO kugouOpenBusinessBO = new KugouOpenBusinessBO();
        kugouOpenBusinessBO.setBusinessId("10063");
        kugouOpenBusinessBO.setBusinessNotifyUrl("http://127.0.0.1:18888/intranet/api/v1/notifyBusiness");
        kugouOpenBusinessBO.setOpenAppId("10063");
        kugouOpenBusinessBO.setSecretKey("BCu9bWVm8Do8J5LheobKBHDL8Ay3wom7");
        when(this.apolloConfigService.getOpenRechargeBusinessConfig(anyString())).thenReturn(kugouOpenBusinessBO);
        KugouOpenParam kugouOpenParam = new KugouOpenParam();
        kugouOpenParam.setNonce("df95ecc20f5a013a528581f570327263");
        kugouOpenParam.setUser_openid(USER_OPENID);
        kugouOpenParam.setAccess_token(ACCESS_TOKEN);
        kugouOpenParam.setOpenappid("10063");
        kugouOpenParam.setV("1.0");
        kugouOpenParam.setTimestamp("1606730684");
        kugouOpenParam.setBusiness_data("{\"amount\":0.1}");
        kugouOpenParam.setMethod("game_recharge.getOrderForWechatM");
        kugouOpenParam.setSignature(kugouOpenService.createKugouOpenParamSign(kugouOpenParam));
        Field[] fields = kugouOpenParam.getClass().getDeclaredFields();
        Arrays.stream(fields).forEachOrdered(field -> {
            try {
                field.setAccessible(true);
                Object value = field.get(kugouOpenParam);
                if (Objects.isNull(value)) {
                    return;
                }
                System.out.println(field.getName() + ":" + field.get(kugouOpenParam));
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        });
        Assert.assertTrue(true);
    }

    /**
     * 构建酷狗开放平台调用参数
     */
    @Test
    public void getOrderForAlipayM() {
        KugouOpenBusinessBO kugouOpenBusinessBO = new KugouOpenBusinessBO();
        kugouOpenBusinessBO.setBusinessId("10063");
        kugouOpenBusinessBO.setBusinessNotifyUrl("http://127.0.0.1:18888/intranet/api/v1/notifyBusiness");
        kugouOpenBusinessBO.setOpenAppId("10063");
        kugouOpenBusinessBO.setSecretKey("BCu9bWVm8Do8J5LheobKBHDL8Ay3wom7");
        when(this.apolloConfigService.getOpenRechargeBusinessConfig(anyString())).thenReturn(kugouOpenBusinessBO);
        KugouOpenParam kugouOpenParam = new KugouOpenParam();
        kugouOpenParam.setNonce("df95ecc20f5a013a528581f570327263");
        kugouOpenParam.setUser_openid(USER_OPENID);
        kugouOpenParam.setAccess_token(ACCESS_TOKEN);
        kugouOpenParam.setOpenappid("10063");
        kugouOpenParam.setV("1.0");
        kugouOpenParam.setTimestamp("1606730684");
        kugouOpenParam.setBusiness_data("{\"amount\":0.1}");
        kugouOpenParam.setMethod("game_recharge.getOrderForAlipayM");
        kugouOpenParam.setSignature(kugouOpenService.createKugouOpenParamSign(kugouOpenParam));
        Field[] fields = kugouOpenParam.getClass().getDeclaredFields();
        Arrays.stream(fields).forEachOrdered(field -> {
            try {
                field.setAccessible(true);
                Object value = field.get(kugouOpenParam);
                if (Objects.isNull(value)) {
                    return;
                }
                System.out.println(field.getName() + ":" + field.get(kugouOpenParam));
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        });
        Assert.assertTrue(true);
    }

    /**
     * nonce=df95ecc20f5a013a528581f570327263
     * access_token=4d5949162bf0a4efb93ce374acbec414ad42b071
     * openappid=10063
     * v=1.0
     * user_openid=f50e6bca4f09cf96adb85c87df0decdf
     * timestamp=1607417851
     * business_data=%7B%22channel_id%22%3A%22oauth2_test%22%2C%22activity_id%22%3A%221%22%2C%22out_trade_no%22%3A%22keventest01%22%2C%22product_type%22%3A%22svip%22%2C%22month%22%3A2%7D
     * method=vip.direct_recharge_create_order
     * signature=1e80eee33f6a018631383fc49bd0ae41b976fe65
     */
    @Test
    public void checkKugouOpenParamSign() {
        KugouOpenBusinessBO kugouOpenBusinessBO = new KugouOpenBusinessBO();
        kugouOpenBusinessBO.setBusinessId("10063");
        kugouOpenBusinessBO.setBusinessNotifyUrl("http://127.0.0.1:18888/intranet/api/v1/notifyBusiness");
        kugouOpenBusinessBO.setOpenAppId("10063");
        kugouOpenBusinessBO.setSecretKey("BCu9bWVm8Do8J5LheobKBHDL8Ay3wom7");
        when(this.apolloConfigService.getOpenRechargeBusinessConfig(anyString())).thenReturn(kugouOpenBusinessBO);
        KugouOpenParam kugouOpenParam = this.buildKugouOpenParamWithoutSignature();
        kugouOpenParam.setSignature("2fe3c35fa23f3288d52aaafe029c7c0c2722f6be");
        Assert.assertTrue(kugouOpenService.checkKugouOpenParamSign(kugouOpenParam));
        kugouOpenParam.setSignature("2fe3c35fa23f3288d52aaafe029c7c0c2722f6be+++");
        Assert.assertFalse(kugouOpenService.checkKugouOpenParamSign(kugouOpenParam));
    }

    private KugouOpenParam buildKugouOpenParamWithoutSignature() {
        KugouOpenParam kugouOpenParam = new KugouOpenParam();
        kugouOpenParam.setNonce("df95ecc20f5a013a528581f570327263");
        kugouOpenParam.setAccess_token("4d5949162bf0a4efb93ce374acbec414ad42b071");
        kugouOpenParam.setOpenappid("10063");
        kugouOpenParam.setV("1.0");
        kugouOpenParam.setUser_openid("f50e6bca4f09cf96adb85c87df0decdf");
        kugouOpenParam.setTimestamp("1606730684");
        kugouOpenParam.setBusiness_data("{\"amount\":1}");
        kugouOpenParam.setMethod("game_recharge.getOrderForAlipayM");
        return kugouOpenParam;
    }
}
