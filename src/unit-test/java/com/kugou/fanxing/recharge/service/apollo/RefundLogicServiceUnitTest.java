package com.kugou.fanxing.recharge.service.apollo;

import com.ctrip.framework.apollo.Config;
import com.kugou.fanxing.recharge.config.RefundConfig;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class RefundLogicServiceUnitTest {

    @Mock
    private Config apolloConfig;
    @InjectMocks
    private RefundConfig refundLogicService;

    @Test
    public void isUserGrayLogicOpen() {
        when(apolloConfig.getBooleanProperty(anyString(),anyBoolean())).thenReturn(true);
        Assert.assertTrue(this.refundLogicService.isUserGrayLogicOpen());
        when(apolloConfig.getBooleanProperty(anyString(),anyBoolean())).thenReturn(false);
        Assert.assertFalse(this.refundLogicService.isUserGrayLogicOpen());
    }

    @Test
    public void getBlackTransactionId() {
        when(apolloConfig.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[] {"1","2","3"});
        List<String> blackTransactionIdList = this.refundLogicService.getBlackTransactionId();
        Assert.assertEquals(3, blackTransactionIdList.size());
    }

    @Test
    public void getBlackTransactionIdException() {
        when(apolloConfig.getArrayProperty(anyString(), anyString(), any(String[].class))).thenThrow(new ContextedRuntimeException("test"));
        List<String> blackTransactionIdList = this.refundLogicService.getBlackTransactionId();
        Assert.assertEquals(0, blackTransactionIdList.size());
    }

    @Test
    public void getGrayKugouIdList() {
        when(apolloConfig.getArrayProperty(anyString(), anyString(), any(String[].class))).thenReturn(new String[] {"1","2"});
        List<String> grayKugouIdList = this.refundLogicService.getGrayKugouIdList();
        Assert.assertEquals(2, grayKugouIdList.size());
    }

    @Test
    public void getGrayKugouIdListException() {
        when(apolloConfig.getArrayProperty(anyString(), anyString(), any(String[].class))).thenThrow(new ContextedRuntimeException("test"));
        List<String> grayKugouIdList = this.refundLogicService.getGrayKugouIdList();
        Assert.assertEquals(0, grayKugouIdList.size());
    }
}
