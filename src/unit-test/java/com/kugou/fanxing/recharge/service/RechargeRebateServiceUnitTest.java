package com.kugou.fanxing.recharge.service;

import com.google.common.cache.LoadingCache;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.model.vo.RechargeRebateVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.concurrent.ExecutionException;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class RechargeRebateServiceUnitTest {

    @InjectMocks
    private RechargeRebateService rechargeRebateService;
    @Mock
    private LoadingCache<String, List<RechargeRebateVO>> rechargeRebateInfoCache;

    @Test
    public void getRechargeRebateInfo() throws ExecutionException {
        RechargeRebateVO vo = new RechargeRebateVO().setId(1L);
        when(rechargeRebateInfoCache.get(anyString())).thenReturn(Lists.newArrayList(vo));
        ReflectionTestUtils.setField(rechargeRebateService, "rechargeRebateInfoCache", rechargeRebateInfoCache);
        List<RechargeRebateVO> rechargeRebateVOList = this.rechargeRebateService.getRechargeRebateInfo();
        Assert.assertNotNull(rechargeRebateVOList);
        Assert.assertEquals(1, rechargeRebateVOList.size());
    }

    @Test(expected = AckException.class)
    public void getRechargeRebateInfoException() throws ExecutionException {
        when(rechargeRebateInfoCache.get(anyString())).thenThrow(new ContextedRuntimeException("test"));
        this.rechargeRebateService.getRechargeRebateInfo();
    }
}
