package com.kugou.fanxing.recharge.service;

import com.alibaba.fastjson.JSON;
import com.kugou.fanxing.commons.util.Assert;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.model.vo.NoviceRechargeBaseVO;
import com.kugou.fanxing.recharge.model.vo.NoviceRechargeVO;
import com.kugou.fanxing.thrift.pay.novicerecharge.NoviceRechargeConfigDTO;
import com.kugou.fanxing.thrift.pay.novicerecharge.NoviceRechargeConfigReq;
import com.kugou.fanxing.thrift.pay.novicerecharge.NoviceRechargeConfigResp;
import com.kugou.fanxing.thrift.pay.novicerecharge.NoviceRechargeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class NoviceRechargeBusServiceTest {
    private NoviceRechargeBusService noviceRechargeBusService = new NoviceRechargeBusService();
    @Mock
    private NoviceRechargeService.Iface noviceRechargeServiceMock;

    @Before
    public void init() {
        ReflectionTestUtils.setField(noviceRechargeBusService, "noviceRechargeService", noviceRechargeServiceMock);
    }

    @Test
    public void testGetNoviceRechargeConfigNew() throws Exception {
        ClassPathResource classPathResource = new ClassPathResource("novicerecharge/configNew.json");
        String json = FileUtils.readFileToString(classPathResource.getFile());
        NoviceRechargeConfigDTO dto = JSON.parseObject(json, NoviceRechargeConfigDTO.class);
        Mockito.when(noviceRechargeServiceMock.getCfgNew(any(NoviceRechargeConfigReq.class))).thenAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                NoviceRechargeConfigResp resp = new NoviceRechargeConfigResp();
                resp.setCode(0);
                resp.setData(dto);
                return resp;
            }
        });
        WebCommonParam webCommonParam = new WebCommonParam();
        NoviceRechargeVO  baseVOList = noviceRechargeBusService.getNoviceRechargeConfigNew(webCommonParam.getKugouId());
        Assert.isTrue(CollectionUtils.isNotEmpty(baseVOList.getAwardConfig()));
    }
}
