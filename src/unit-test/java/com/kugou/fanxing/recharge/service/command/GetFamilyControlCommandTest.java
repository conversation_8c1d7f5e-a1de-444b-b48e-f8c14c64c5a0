package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.thrift.operation.FamilyControllRequest;
import com.kugou.fanxing.thrift.operation.FamilyControllResult;
import com.kugou.fanxing.thrift.operation.FamilyControllService;
import com.kugou.fanxing.thrift.operation.FamilyControllVo;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class GetFamilyControlCommandTest {

    @Mock
    private FamilyControllService.Iface familyControlService;

    @Test
    public void confirm() throws TException {
        FamilyControllResult result = new FamilyControllResult().setRet(0).setMsg("").setData(new FamilyControllVo());
        when(familyControlService.getFamilyControll(any(FamilyControllRequest.class))).thenReturn(result);
        GetFamilyControlCommand command = new GetFamilyControlCommand(familyControlService, new FamilyControllRequest());
        Optional<FamilyControllResult> optionalFamilyControllResult = command.execute();
        FamilyControllResult familyControllResult = optionalFamilyControllResult.orElseThrow(() -> new ContextedRuntimeException("test"));
        Assert.assertEquals(0, familyControllResult.getRet());
        result = new FamilyControllResult().setRet(1).setMsg("").setData(new FamilyControllVo());
        when(familyControlService.getFamilyControll(any(FamilyControllRequest.class))).thenReturn(result);
        command = new GetFamilyControlCommand(familyControlService, new FamilyControllRequest());
        optionalFamilyControllResult = command.execute();
        Assert.assertTrue(optionalFamilyControllResult.isPresent());
    }

    @Test
    public void confirmException() throws TException {
        when(familyControlService.getFamilyControll(any(FamilyControllRequest.class))).thenThrow(new ContextedRuntimeException("test"));
        GetFamilyControlCommand command = new GetFamilyControlCommand(familyControlService, new FamilyControllRequest());
        Optional<FamilyControllResult> optionalFamilyControllResult = command.execute();
        Assert.assertFalse(optionalFamilyControllResult.isPresent());
    }
}
