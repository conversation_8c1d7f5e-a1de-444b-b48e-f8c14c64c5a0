package com.kugou.fanxing.recharge.service.callback;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.AppStoreReceiptDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeOpenDao;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.bo.ReceiptInfoBO;
import com.kugou.fanxing.recharge.model.dto.CoinCallbackDTO;
import com.kugou.fanxing.recharge.model.dto.RechargeExtendDTO;
import com.kugou.fanxing.recharge.model.po.AppStoreReceiptPO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.mq.PulsarRechargeSuccessProducer;
import com.kugou.fanxing.recharge.service.PayPalRechargeService;
import com.kugou.fanxing.recharge.service.ValidatingService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.thrift.callback.PurchaseCoinForIosRequest;
import com.kugou.fanxing.recharge.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Enumeration;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class AbstractCallbackServiceUnitTest {
    
    @InjectMocks
    private OpenRechargeCallbackService openRechargeCallbackService;
    @Mock
    protected RechargeConfig rechargeConfig;
    @Mock
    protected ValidatingService validatingService;
    @Mock
    protected RechargeAcrossDao rechargeAcrossDao;
    @Mock
    protected RechargeOpenDao rechargeOpenDao;
    @Mock
    protected OrderIdService orderIdService;
    @Mock
    protected PayPalRechargeService payPalRechargeService;
    @Mock
    protected UserFacadeService userFacadeService;
    @Mock
    protected HttpServletRequest request;
    @Mock
    protected AppStoreReceiptDao appStoreReceiptDao;
    @Mock
    protected PulsarRechargeSuccessProducer pulsarRechargeSuccessProducer;
    @InjectMocks
    protected DefaultCallbackService defaultCallbackService;

    @Test
    public void checkPrerequisitesForIos() {
        PurchaseCoinForIosRequest request = new PurchaseCoinForIosRequest();
        SysResultCode sysResultCode = this.openRechargeCallbackService.checkPrerequisitesForIos(request);
        Assert.assertEquals(SysResultCode.RECHARGE_PARAM_ERROR, sysResultCode);
        request.setOrderNum("123");
        sysResultCode = this.openRechargeCallbackService.checkPrerequisitesForIos(request);
        Assert.assertEquals(SysResultCode.RECHARGE_PARAM_ERROR, sysResultCode);
        request.setKey("key");
        sysResultCode = this.openRechargeCallbackService.checkPrerequisitesForIos(request);
        Assert.assertEquals(SysResultCode.RECHARGE_PARAM_ERROR, sysResultCode);
        when(userFacadeService.getUserIdByKugouId(anyLong(), anyBoolean())).thenReturn(Optional.of(596069021L));
        request.setKugouId(596069021L);
        String actualKey = DigestUtils.md5Hex(DigestUtils.md5Hex("K7<XB4$!58$380" + request.getOrderNum()) + request.getKugouId());
        request.setKey(actualKey);
        sysResultCode = this.openRechargeCallbackService.checkPrerequisitesForIos(request);
        Assert.assertEquals(SysResultCode.SUCCESS, sysResultCode);
    }

    @Test
    public void parseTradeTime() {
        int tradeTimeTs = openRechargeCallbackService.parseTradeTime("20210510111139");
        Assert.assertEquals("2021-05-10 11:11:39", DateHelper.format(new Date(tradeTimeTs * 1000L)));
    }

    /**
     * 正常：02202105101111250100017815，trade_time=20210510111139
     * 异常：02202105101111200100010449，trade_time=-00011130000000
     */
    @Test
    public void parseTradeTimeException() {
        Assert.assertEquals(0, openRechargeCallbackService.parseTradeTime("-00011130000000"));
    }

    @Test
    public void saveCallbackSuccessOrder() {
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setRechargeOrderNum("R202012838448939347w347");
        when(orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202012");
        when(rechargeAcrossDao.insertIgnore(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
        Assert.assertTrue(openRechargeCallbackService.saveCallbackSuccessOrder(rechargeAcrossPO));
    }

    @Test
    public void saveSingBusinessCallbackSuccessOrder() {
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setRechargeOrderNum("R202012838448939347w347");
        rechargeAcrossPO.setCoinType(CoinTypeEnum.SING_COIN.getCoinType());
        when(orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202012");
        when(rechargeAcrossDao.insertIgnore(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
        Assert.assertTrue(openRechargeCallbackService.saveCallbackSuccessOrder(rechargeAcrossPO));
        verify(pulsarRechargeSuccessProducer, times(1)).sendPostRechargeMsg(any(RechargeAcrossPO.class));

    }


    @Test
    public void checkCallbackNotify() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class);
             MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class);
             MockedStatic<SignUtils> signUtilsMockedStatic = Mockito.mockStatic(SignUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            CoinCallbackDTO coinCallbackDTO = new CoinCallbackDTO();
            coinCallbackDTO.setCallback_status(1);
            Assert.assertTrue(this.openRechargeCallbackService.checkCallbackNotify(coinCallbackDTO));
            signUtilsMockedStatic.when(() -> SignUtils.buildSign(anyMap(), anyString())).thenReturn("test");
            coinCallbackDTO.setCallback_status(0);
            coinCallbackDTO.setAppid(1);
            coinCallbackDTO.setAreaid("1");
            coinCallbackDTO.setTime(12313131);
            coinCallbackDTO.setNotify_id("test123");
            when(this.rechargeConfig.getKupayAppIdByAppId(anyInt())).thenReturn(new KupayAppInfoBO().setKupayAppId(1084).setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J"));
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doSyncGet(anyString(), anyMap())).thenReturn(Optional.of("success"));
            Assert.assertTrue(this.openRechargeCallbackService.checkCallbackNotify(coinCallbackDTO));
            httpClientUtilsMockedStatic.when(() -> HttpClientUtils.doSyncGet(anyString(), anyMap())).thenReturn(Optional.of("failure"));
            Assert.assertFalse(this.openRechargeCallbackService.checkCallbackNotify(coinCallbackDTO));
        }
    }

    @Test
    public void checkCallbackExtendSign() {
        CoinCallbackDTO coinCallbackDTO = new CoinCallbackDTO();
        coinCallbackDTO.setOrder_no("R092020120818243801500636");
        coinCallbackDTO.setUserid(596069021L);
        when(rechargeConfig.getCallBackKey()).thenReturn("kugoufanxing2016");
        coinCallbackDTO.setExtend("");
        Assert.assertFalse(this.openRechargeCallbackService.checkCallbackExtendSign(coinCallbackDTO.getUserid(), coinCallbackDTO.getOrder_no(), coinCallbackDTO.getExtend()));
        coinCallbackDTO.setExtend("eyJjYWxsQmFja1NpZ24iOiIxYzYxNjRiNDBkZWUzMjBhODkwZjlhMDJhMjNhNTk0NSIsImNhbGxCYWNrQXJnIjp7ImFtb3VudCI6MSwiYWRkVGltZSI6MTYwNzQyMzA3OCwiY291cG9uIjowLCJ1c2VyT3BlbmlkIjoiZjUwZTZiY2E0ZjA5Y2Y5NmFkYjg1Yzg3ZGYwZGVjZGYiLCJidXlSaWNoTGV2ZWwiOnt9LCJyZWJhdGUiOiIwIiwiY29uc3VtZUFyZ3MiOnt9LCJidXNpbmVzc0V4dCI6IiIsImJ1c2luZXNzSWQiOiIxMDA2MyIsImNvdXBvbklkIjowLCJ2ZXJzaW9uIjoiMjAxNzAxMTEiLCJjb3Vwb25PcmRlcklkIjowLCJwYXlUeXBlSWQiOjMwLCJtb25leSI6MSwicmVmZXIiOjAsImNGcm9tIjowLCJjaGFubmVsSWQiOjAsImt1Z291SWQiOjU5NjA2OTAyMX19");
        Assert.assertTrue(this.openRechargeCallbackService.checkCallbackExtendSign(coinCallbackDTO.getUserid(), coinCallbackDTO.getOrder_no(), coinCallbackDTO.getExtend()));
        when(rechargeConfig.getCallBackKey()).thenReturn("kugoufanxing2017");
        Assert.assertFalse(this.openRechargeCallbackService.checkCallbackExtendSign(coinCallbackDTO.getUserid(), coinCallbackDTO.getOrder_no(), coinCallbackDTO.getExtend()));
    }

    /**
     * {"refer":0,"cFrom":1,"channelId":200,"amount":0.02,"payTypeId":30,"addTime":1607669862,"kugouId":1290249156,"money":0.02,"coupon":0,"couponId":"","couponOrderId":0,"version":"20170111","businessExt":"","buyRichLevel":[],"rebate":0,"consumeArgs":[]}}
     */
    @Test
    public void parseRechargeExtendDTO() {
        String extend = new String(Base64.decodeBase64(StringUtils.defaultString("eyJjYWxsQmFja1NpZ24iOiIxNzUzMzhiODVkOGVmY2QyOTg4ZDc5Y2Q3YWFiOTE0MSIsImNhbGxCYWNrQXJnIjp7InJlZmVyIjowLCJjRnJvbSI6MSwiY2hhbm5lbElkIjoyMDAsImFtb3VudCI6MC4wMiwicGF5VHlwZUlkIjozMCwiYWRkVGltZSI6MTYwNzY2OTg2Miwia3Vnb3VJZCI6MTI5MDI0OTE1NiwibW9uZXkiOjAuMDIsImNvdXBvbiI6MCwiY291cG9uSWQiOiIiLCJjb3Vwb25PcmRlcklkIjowLCJ2ZXJzaW9uIjoiMjAxNzAxMTEiLCJidXNpbmVzc0V4dCI6IiIsImJ1eVJpY2hMZXZlbCI6W10sInJlYmF0ZSI6MCwiY29uc3VtZUFyZ3MiOltdfX0=")));
        RechargeExtendDTO rechargeExtendDTO = this.openRechargeCallbackService.parseRechargeExtendDTO(extend);
        Assert.assertEquals(0, rechargeExtendDTO.getRefer());
        Assert.assertEquals(1, rechargeExtendDTO.getCFrom());
        Assert.assertEquals(200, rechargeExtendDTO.getChannelId());
        Assert.assertEquals(new BigDecimal("0.02"), rechargeExtendDTO.getAmount());
        Assert.assertEquals(30, rechargeExtendDTO.getPayTypeId());
        Assert.assertEquals(1607669862, rechargeExtendDTO.getAddTime());
        Assert.assertEquals(1290249156, rechargeExtendDTO.getKugouId());
        Assert.assertEquals(new BigDecimal("0.02"), rechargeExtendDTO.getMoney());
        Assert.assertEquals(BigDecimal.valueOf(0), rechargeExtendDTO.getCoupon());
        Assert.assertEquals(0, rechargeExtendDTO.getCouponId());
        Assert.assertEquals(0, rechargeExtendDTO.getCouponOrderId());
    }

    /**
     * {"refer":0,"cFrom":1,"channelId":200,"amount":0.02,"payTypeId":30,"addTime":1607669862,"kugouId":1290249156,"agentKugouId":697229863,"money":0.02,"coupon":0,"couponId":"","couponOrderId":0,"version":"20170111","businessExt":"","buyRichLevel":[],"rebate":0,"consumeArgs":[]}
     */
    @Test
    public void parseAgentRechargeExtendDTO() {
        String extend = new String(Base64.decodeBase64(StringUtils.defaultString("eyJjYWxsQmFja1NpZ24iOiIxNzUzMzhiODVkOGVmY2QyOTg4ZDc5Y2Q3YWFiOTE0MSIsImNhbGxCYWNrQXJnIjp7InJlZmVyIjowLCJjRnJvbSI6MSwiY2hhbm5lbElkIjoyMDAsImFtb3VudCI6MC4wMiwicGF5VHlwZUlkIjozMCwiYWRkVGltZSI6MTYwNzY2OTg2Miwia3Vnb3VJZCI6MTI5MDI0OTE1NiwiYWdlbnRLdWdvdUlkIjo2OTcyMjk4NjMsIm1vbmV5IjowLjAyLCJjb3Vwb24iOjAsImNvdXBvbklkIjoiIiwiY291cG9uT3JkZXJJZCI6MCwidmVyc2lvbiI6IjIwMTcwMTExIiwiYnVzaW5lc3NFeHQiOiIiLCJidXlSaWNoTGV2ZWwiOltdLCJyZWJhdGUiOjAsImNvbnN1bWVBcmdzIjpbXX19")));
        RechargeExtendDTO rechargeExtendDTO = this.openRechargeCallbackService.parseRechargeExtendDTO(extend);
        Assert.assertEquals(0, rechargeExtendDTO.getRefer());
        Assert.assertEquals(1, rechargeExtendDTO.getCFrom());
        Assert.assertEquals(200, rechargeExtendDTO.getChannelId());
        Assert.assertEquals(new BigDecimal("0.02"), rechargeExtendDTO.getAmount());
        Assert.assertEquals(30, rechargeExtendDTO.getPayTypeId());
        Assert.assertEquals(1607669862, rechargeExtendDTO.getAddTime());
        Assert.assertEquals(1290249156, rechargeExtendDTO.getKugouId());
        Assert.assertEquals(new BigDecimal("0.02"), rechargeExtendDTO.getMoney());
        Assert.assertEquals(BigDecimal.valueOf(0), rechargeExtendDTO.getCoupon());
        Assert.assertEquals(0, rechargeExtendDTO.getCouponId());
        Assert.assertEquals(0, rechargeExtendDTO.getCouponOrderId());
        Assert.assertEquals(697229863, rechargeExtendDTO.getAgentKugouId());
    }



    @Test
    public void checkSignOfPcCallBack() {
        try (MockedStatic<SpringContextUtils> springContextUtilsMockedStatic = Mockito.mockStatic(SpringContextUtils.class);
             MockedStatic<HttpClientUtils> httpClientUtilsMockedStatic = Mockito.mockStatic(HttpClientUtils.class);
             MockedStatic<SignUtils> signUtilsMockedStatic = Mockito.mockStatic(SignUtils.class)) {
            springContextUtilsMockedStatic.when(() -> SpringContextUtils.getHttpServletRequest()).thenReturn(request);
            signUtilsMockedStatic.when(() -> SignUtils.buildSign(anyMap(), anyString())).thenReturn("test");
            KupayAppInfoBO kupayAppInfoBO = new KupayAppInfoBO();
            kupayAppInfoBO.setKupayAppId(1084);
            kupayAppInfoBO.setKupayAppKey("sG2fHSOkezn40HsY6LhEUyVfd3T8sd3J");
            kupayAppInfoBO.setPayTypeIds(Lists.newArrayList());
            when(this.rechargeConfig.getKupayAppIdByAppId(anyInt())).thenReturn(kupayAppInfoBO);
            Map<String, String> params = Maps.newHashMap();
            String actualSign = SignUtils.buildSign(params, kupayAppInfoBO.getKupayAppKey());
            CoinCallbackDTO coinCallbackDTO = new CoinCallbackDTO();
            coinCallbackDTO.setSign(actualSign);
            when(request.getParameterNames()).thenReturn(new Enumeration<String>() {
                @Override
                public boolean hasMoreElements() {
                    return false;
                }

                @Override
                public String nextElement() {
                    return null;
                }
            });
            Assert.assertTrue(this.openRechargeCallbackService.checkSignOfPcCallBack(coinCallbackDTO));
        }
    }

    @Test
    public void saveGameCallbackSuccessOrder() {
        RechargeAcrossPO targetOrder = new RechargeAcrossPO();
        targetOrder.setRechargeOrderNum("R09202012S1TID1000000750236975");
        String receiptData = "";
        ReceiptInfoBO receiptInfoBO = new ReceiptInfoBO();
        receiptInfoBO.setReceipt(new ReceiptInfoBO.Receipts());
        when(orderIdService.generateGlobalId()).thenReturn(1L);
        when(appStoreReceiptDao.insertIgnore(any(AppStoreReceiptPO.class))).thenReturn(1);
        when(orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202112");
        when(rechargeOpenDao.insertIgnore(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
        boolean result = this.openRechargeCallbackService.saveGameCallbackSuccessOrder(targetOrder, receiptData, receiptInfoBO);
        Assert.assertTrue(result);
    }

    @Test
    public void makeOrderByKugouPayRequestApple() {
        String extend = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        CoinCallbackDTO coinCallbackDTO = new CoinCallbackDTO();
        coinCallbackDTO.setOrder_no("R092020120818243801500636");
        coinCallbackDTO.setUserid(596069021L);
        coinCallbackDTO.setExtend(extend);
        when(rechargeConfig.getDataCenterZoneId()).thenReturn(1);
        when(orderIdService.generateGlobalId()).thenReturn(1L);
        when(payPalRechargeService.getRmbAmount(any(BigDecimal.class))).thenReturn(BigDecimal.valueOf(100));
        when(orderIdService.generateGlobalId()).thenReturn(1L);
        when(appStoreReceiptDao.insertIgnore(any(AppStoreReceiptPO.class))).thenReturn(1);
        when(orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202112");
        when(rechargeOpenDao.insertIgnore(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
        RechargeAcrossPO rechargeAcrossPO = this.openRechargeCallbackService.makeOrderByKugouPayRequest(12090249156L, BigDecimal.valueOf(10), coinCallbackDTO);
        Assert.assertNotNull(rechargeAcrossPO);
    }

    @Test
    public void makeOrderByKugouPayRequest() {
        String extend = "eyJjYWxsQmFja1NpZ24iOiIxNzUzMzhiODVkOGVmY2QyOTg4ZDc5Y2Q3YWFiOTE0MSIsImNhbGxCYWNrQXJnIjp7InJlZmVyIjowLCJjRnJvbSI6MSwiY2hhbm5lbElkIjoyMDAsImFtb3VudCI6MC4wMiwicGF5VHlwZUlkIjozMCwiYWRkVGltZSI6MTYwNzY2OTg2Miwia3Vnb3VJZCI6MTI5MDI0OTE1NiwiYWdlbnRLdWdvdUlkIjo2OTcyMjk4NjMsIm1vbmV5IjowLjAyLCJjb3Vwb24iOjAsImNvdXBvbklkIjoiIiwiY291cG9uT3JkZXJJZCI6MCwidmVyc2lvbiI6IjIwMTcwMTExIiwiYnVzaW5lc3NFeHQiOiIiLCJidXlSaWNoTGV2ZWwiOltdLCJyZWJhdGUiOjAsImNvbnN1bWVBcmdzIjpbXX19";
        CoinCallbackDTO coinCallbackDTO = new CoinCallbackDTO();
        coinCallbackDTO.setOrder_no("R092020120818243801500636");
        coinCallbackDTO.setUserid(596069021L);
        coinCallbackDTO.setExtend(extend);
        when(rechargeConfig.getDataCenterZoneId()).thenReturn(1);
        when(orderIdService.generateGlobalId()).thenReturn(1L);
        when(payPalRechargeService.getRmbAmount(any(BigDecimal.class))).thenReturn(BigDecimal.valueOf(100));
        when(orderIdService.generateGlobalId()).thenReturn(1L);
        when(appStoreReceiptDao.insertIgnore(any(AppStoreReceiptPO.class))).thenReturn(1);
        when(orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202112");
        when(rechargeOpenDao.insertIgnore(anyString(), any(RechargeAcrossPO.class))).thenReturn(1);
        RechargeAcrossPO rechargeAcrossPO = this.openRechargeCallbackService.makeOrderByKugouPayRequest(12090249156L, BigDecimal.valueOf(10), coinCallbackDTO);
        Assert.assertNotNull(rechargeAcrossPO);
    }

    @Test
    public void parseBase64Extend() {
        String extend = "eyJjYWxsQmFja1NpZ24iOiIxNzUzMzhiODVkOGVmY2QyOTg4ZDc5Y2Q3YWFiOTE0MSIsImNhbGxCYWNrQXJnIjp7InJlZmVyIjowLCJjRnJvbSI6MSwiY2hhbm5lbElkIjoyMDAsImFtb3VudCI6MC4wMiwicGF5VHlwZUlkIjozMCwiYWRkVGltZSI6MTYwNzY2OTg2Miwia3Vnb3VJZCI6MTI5MDI0OTE1NiwiYWdlbnRLdWdvdUlkIjo2OTcyMjk4NjMsIm1vbmV5IjowLjAyLCJjb3Vwb24iOjAsImNvdXBvbklkIjoiIiwiY291cG9uT3JkZXJJZCI6MCwidmVyc2lvbiI6IjIwMTcwMTExIiwiYnVzaW5lc3NFeHQiOiIiLCJidXlSaWNoTGV2ZWwiOltdLCJyZWJhdGUiOjAsImNvbnN1bWVBcmdzIjpbXX19";
        String json = JsonUtils.toJSONString(ImmutableMap.of("extend", extend));
        Assert.assertEquals(extend, this.defaultCallbackService.parseBase64Extend(extend));
        Assert.assertEquals(extend, this.defaultCallbackService.parseBase64Extend(json));
    }

}
