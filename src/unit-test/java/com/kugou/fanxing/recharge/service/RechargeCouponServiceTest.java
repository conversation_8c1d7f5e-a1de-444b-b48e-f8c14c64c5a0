package com.kugou.fanxing.recharge.service;

import com.kugou.config.Env;
import com.kugou.fanxing.biz.commons.util.SnowFlake;
import com.kugou.fanxing.coupon.thrift.CouponService;
import com.kugou.fanxing.coupon.thrift.OperResult;
import com.kugou.fanxing.coupon.thrift.ReturnResult;
import com.kugou.fanxing.coupon.thrift.UnFreezeVO;
import com.kugou.fanxing.coupon.thrift.read.CouponInfoVO;
import com.kugou.fanxing.coupon.thrift.read.CouponListByParamsRequest;
import com.kugou.fanxing.coupon.thrift.read.CouponReadService;
import com.kugou.fanxing.coupon.thrift.read.PersonalCouponListResultV2;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeCouponUnfreezeDao;
import com.kugou.fanxing.recharge.model.bo.CouponInfoBO;
import com.kugou.fanxing.recharge.model.po.RechargeCouponUnfreezePO;
import com.kugou.fanxing.recharge.model.vo.RechargeCouponInfoVO;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.mfx.activity.infiltrate.thrift.service.Coupon;
import com.kugou.mfx.activity.infiltrate.thrift.service.CouponListResult;
import com.kugou.mfx.activity.infiltrate.thrift.service.CouponListService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class RechargeCouponServiceTest {

    @Mock
    private Env env;
    @Mock
    private RechargeCouponUnfreezeDao rechargeCouponUnfreezeDao;
    @Mock
    private OrderIdService orderIdService;
    @Mock
    private CouponService.Iface couponService;
    @Mock
    private CouponListService.Iface couponListService;
    @Mock
    private ApolloConfigService apolloConfigService;
    @Mock
    private CouponReadService.Iface couponReadService;
    @InjectMocks
    private RechargeCouponService rechargeCouponService;

    @Before
    public void before() {
        when(apolloConfigService.getCouponFailureResultCodeList()).thenReturn(Lists.newArrayList(4, 16));
    }

    @Test
    public void parseCouponImgPath() {
        String couponImg = "http://fxbssdl.kgimg.com/bss/mfx/379991075e7c6dd964940c6cbe558e68.png";
        String couponImgPath = rechargeCouponService.parseCouponImgPath(couponImg);
        log.warn("couponImg: {}, couponImgPath: {}", couponImg, couponImgPath);
        Assert.assertEquals("/bss/mfx/379991075e7c6dd964940c6cbe558e68.png", couponImgPath);
    }

    @Test
    public void isCouponLimit() {
        CouponInfoBO couponInfoBO = new CouponInfoBO().setValue(BigDecimal.valueOf(0.02));
        Assert.assertTrue(this.rechargeCouponService.isCouponLimit(couponInfoBO, BigDecimal.valueOf(0.01)));
        Assert.assertTrue(this.rechargeCouponService.isCouponLimit(couponInfoBO, BigDecimal.valueOf(0.02)));
        Assert.assertFalse(this.rechargeCouponService.isCouponLimit(couponInfoBO, BigDecimal.valueOf(0.03)));
    }

    @Test
    public void isCouponRangeLimit() {
        CouponInfoBO couponInfoBO = new CouponInfoBO()
                .setValue(BigDecimal.valueOf(0.01))
                .setLowerLimit(BigDecimal.valueOf(0.03))
                .setUpperLimit(BigDecimal.valueOf(0.05));
        Assert.assertTrue(this.rechargeCouponService.isCouponLimit(couponInfoBO, BigDecimal.valueOf(0.02)));
        Assert.assertFalse(this.rechargeCouponService.isCouponLimit(couponInfoBO, BigDecimal.valueOf(0.03)));
        Assert.assertFalse(this.rechargeCouponService.isCouponLimit(couponInfoBO, BigDecimal.valueOf(0.04)));
        Assert.assertTrue(this.rechargeCouponService.isCouponLimit(couponInfoBO, BigDecimal.valueOf(0.05)));
        Assert.assertTrue(this.rechargeCouponService.isCouponLimit(couponInfoBO, BigDecimal.valueOf(0.06)));
    }

    @Test
    public void unFreezeCouponQuietly() throws TException {
        long kugouId = 1290249156;
        String rechargeOrderNum = "R092020051115311510883921";
        CouponInfoBO couponInfoBO = new CouponInfoBO()
                .setCouponId(457163972114649086L)
                .setOrderId(1371147663608785129L);
        when(env.isDev()).thenReturn(true);
        when(orderIdService.generateGlobalId()).thenReturn(new SnowFlake(1, 1).nextId());
        ReturnResult returnResult = new ReturnResult()
                .setCode(0)
                .setData(new OperResult());
        when(couponService.cancel(any(UnFreezeVO.class))).thenReturn(returnResult);
        when(rechargeCouponUnfreezeDao.addRecord(any(RechargeCouponUnfreezePO.class))).thenReturn(1);
        this.rechargeCouponService.unFreezeCouponQuietly(kugouId, rechargeOrderNum, couponInfoBO);
    }

    @Test
    public void getCouponListResultSwitchOff() {
        when(apolloConfigService.isCouponSwitchOpen()).thenReturn(false);
        JsonResult<List<RechargeCouponInfoVO>> result = this.rechargeCouponService.getCouponListResult(1290249156L);
        Assert.assertEquals(SysResultCode.E_10000024.getCode(), result.getCode());
    }

    @Test
    public void getCouponListResultSwitchOn() throws TException {
        when(apolloConfigService.isCouponSwitchOpen()).thenReturn(true);
        when(apolloConfigService.getCouponExpireHideDays()).thenReturn(7);
        when(couponReadService.getPersonalCouponList(any(CouponListByParamsRequest.class))).thenReturn(buildPersonalCouponListResultV2());
        when(couponListService.listCoupon()).thenReturn(buildCouponListResult());
        JsonResult<List<RechargeCouponInfoVO>> result = this.rechargeCouponService.getCouponListResult(1290249156L);
        Assert.assertEquals(SysResultCode.SUCCESS.getCode(), result.getCode());
    }

    private CouponListResult buildCouponListResult() {
        Coupon coupon = new Coupon();
        coupon.setId(1237054107882237952L);
        List<Coupon> couponList = Lists.newArrayList(coupon);
        CouponListResult couponListResult = new CouponListResult();
        couponListResult.setCode(0);
        couponListResult.setData(couponList);
        return couponListResult;
    }

    private PersonalCouponListResultV2 buildPersonalCouponListResultV2() {
        CouponInfoVO couponInfoVO = new CouponInfoVO();
        couponInfoVO.setCategory(2);
        couponInfoVO.setCouponId(1237054107882237952L);
        List<CouponInfoVO> couponInfoVOList = Lists.newArrayList();
        couponInfoVOList.add(couponInfoVO);
        PersonalCouponListResultV2 resultV2 = new PersonalCouponListResultV2();
        resultV2.setCode(0);
        resultV2.setData(couponInfoVOList);
        return resultV2;
    }

    @Test
    public void consumeCoupon() throws TException {
        ReturnResult returnResult = new ReturnResult();
        returnResult.setCode(0);
        returnResult.setData(new OperResult().setResult(0));
        when(couponService.confirm(any(UnFreezeVO.class))).thenReturn(returnResult);
        Assert.assertEquals(1, this.rechargeCouponService.consumeCoupon(1, 4, 7, 1));
        returnResult.setData(new OperResult().setResult(8));
        when(couponService.confirm(any(UnFreezeVO.class))).thenReturn(returnResult);
        Assert.assertEquals(1, this.rechargeCouponService.consumeCoupon(2, 5, 8, 1));
        returnResult.setData(new OperResult().setResult(16));
        when(couponService.confirm(any(UnFreezeVO.class))).thenReturn(returnResult);
        Assert.assertEquals(2, this.rechargeCouponService.consumeCoupon(3, 6, 9, 1));
    }

    @Test
    public void consumeCouponException() throws TException {
        ReturnResult returnResult = new ReturnResult();
        returnResult.setCode(0);
        returnResult.setData(new OperResult().setResult(0));
        when(couponService.confirm(any(UnFreezeVO.class))).thenThrow(new ContextedRuntimeException("test"));
        Assert.assertEquals(0, this.rechargeCouponService.consumeCoupon(1, 1, 1, 1));
    }

}
