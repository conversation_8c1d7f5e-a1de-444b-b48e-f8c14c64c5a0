package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.controller.thrift.CurlTProtocolFactory;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossOrderNumDao;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossOrdernumPO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.thrift.*;
import com.kugou.fanxing.recharge.thrift.RechargeService;
import com.kugou.fanxing.thrift.pay.v2.ConvertTradeNoRequest;
import com.kugou.fanxing.thrift.pay.v2.ConvertTradeNoResponse;
import com.kugou.fanxing.thrift.pay.v2.PlatformPayV2Service;
import com.kugou.fanxing.thrift.pay.v2.TradeNoMappingDTO;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.thrift.TApplicationException;
import org.apache.thrift.TException;
import org.apache.thrift.transport.TTransportException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class RechargeOrderServiceUnitTest {

    private static final String HOST = "http://127.0.0.1:18888";
    private static final String URI = "/platform_recharge_service/thrift/rechargeThriftService";

    private static final String RECHARGE_URI = "/platform_recharge_service/thrift/rechargeService";

    @Mock
    private RechargeAcrossOrderNumDao rechargeAcrossOrderNumDao;
    @InjectMocks
    private RechargeOrderService rechargeOrderService;
    @Mock
    private PlatformPayV2Service.Iface platformPayV2Service;
    @Mock
    private RechargeAcrossDao rechargeAcrossDao;
    @Mock
    private OrderIdService orderIdService;

    @Test
    public void specialRechargeOrderNumDeal() throws TException {
        ConvertTradeNoResponse response = new ConvertTradeNoResponse();
        response.setCode(0);
        response.setData(new TradeNoMappingDTO().setRechargeOrderNum("R09201912S1TID430000555834093").setTradeNo("TID430000555834093"));
        when(platformPayV2Service.convertTradeNoToRechargeOrderNum(any(ConvertTradeNoRequest.class))).thenReturn(response);
        when(rechargeAcrossOrderNumDao.insertReturnRechargeOrderNum(anyString(), anyString())).thenReturn(1);
        when(rechargeAcrossOrderNumDao.getByPrimary(anyString())).thenReturn(RechargeAcrossOrdernumPO.builder().rechargeOrderNum("R09201912S1TID430000555834093").build());
        Optional<String> optionalRechargeAcrossOrdernumPO = rechargeOrderService.specialRechargeOrderNumDeal("TID430000555834093", PayTypeIdEnum.PAY_TYPE_ID_1006.getPayTypeId());
        Assert.assertTrue(optionalRechargeAcrossOrdernumPO.isPresent());
        Assert.assertEquals("R09201912S1TID430000555834093", optionalRechargeAcrossOrdernumPO.orElseThrow(() -> new ContextedRuntimeException("test")));
    }

    @Test
    public void specialRechargeOrderNumDealReturnNull() throws TException {
        ConvertTradeNoResponse response = new ConvertTradeNoResponse();
        response.setCode(0);
        response.setData(new TradeNoMappingDTO().setRechargeOrderNum("R09201912S1TID430000555834093").setTradeNo("TID430000555834093"));
        when(platformPayV2Service.convertTradeNoToRechargeOrderNum(any(ConvertTradeNoRequest.class))).thenReturn(null);
        when(rechargeAcrossOrderNumDao.insertReturnRechargeOrderNum(anyString(), anyString())).thenReturn(1);
        when(rechargeAcrossOrderNumDao.getByPrimary(anyString())).thenReturn(RechargeAcrossOrdernumPO.builder().rechargeOrderNum("R09201912S1TID430000555834093").build());
        Optional<String> optionalRechargeAcrossOrdernumPO = rechargeOrderService.specialRechargeOrderNumDeal("TID430000555834093", PayTypeIdEnum.PAY_TYPE_ID_1006.getPayTypeId());
        Assert.assertFalse(optionalRechargeAcrossOrdernumPO.isPresent());

    }

    @Test
    public void makeRechargeOrderNumForAcrossSpecial() {
        String rechargeOrderNum = rechargeOrderService.makeRechargeOrderNumForAcrossSpecial("TID430000555834093", PayTypeIdEnum.PAY_TYPE_ID_1006.getPayTypeId());
        Assert.assertNotNull(rechargeOrderNum);
        Assert.assertTrue(rechargeOrderNum.startsWith("R09" + DateFormatUtils.format(new Date(), "yyyyMM")));
    }

    @Test(expected = ContextedRuntimeException.class)
    public void specialRechargeOrderNumDealException() {
        when(rechargeAcrossOrderNumDao.insertReturnRechargeOrderNum(anyString(), anyString())).thenThrow(new ContextedRuntimeException("test"));
        rechargeOrderService.specialRechargeOrderNumDeal("TID430000555834093", PayTypeIdEnum.PAY_TYPE_ID_1006.getPayTypeId());
    }

    @Test
    public void getRechargeSuccessList() throws ParseException {
        when(rechargeAcrossDao.getRechargeAcrossSuccessListByTimeAndId(anyString(), anyLong(), anyLong(), anyLong(), anyInt(), anyBoolean())).thenReturn(Lists.emptyList());
        Date sTime = DateUtils.parseDate("2015-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        Date eTime = DateUtils.addMinutes(sTime, 1);
        List<RechargeSuccessInfo> rechargeSuccessInfoList = this.rechargeOrderService.getRechargeSuccessList(sTime.getTime()/1000, eTime.getTime()/1000, 0, 100, false);
        Assert.assertEquals(0, rechargeSuccessInfoList.size());
    }

    @Test(expected = BizException.class)
    public void getRechargeSuccessListException1() throws ParseException {
        when(rechargeAcrossDao.getRechargeAcrossSuccessListByTimeAndId(anyString(), anyLong(), anyLong(), anyLong(), anyInt(), anyBoolean())).thenReturn(Lists.emptyList());
        Date sTime = DateUtils.parseDate("2014-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        Date eTime = DateUtils.addMinutes(sTime, 1);
        this.rechargeOrderService.getRechargeSuccessList(sTime.getTime()/1000, eTime.getTime()/1000, 0, 100, false);
    }

    @Test(expected = BizException.class)
    public void getRechargeSuccessListException2() throws ParseException {
        when(rechargeAcrossDao.getRechargeAcrossSuccessListByTimeAndId(anyString(), anyLong(), anyLong(), anyLong(), anyInt(), anyBoolean())).thenReturn(Lists.emptyList());
        Date sTime = DateUtils.parseDate("2015-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        Date eTime = DateUtils.addMinutes(sTime, -1);
        this.rechargeOrderService.getRechargeSuccessList(sTime.getTime()/1000, eTime.getTime()/1000, 0, 100, false);
    }

    @Test
    public void getSuccessOrderList() {
        long beginTime = DateUtils.addMonths(new Date(), -1).getTime() / 1000;
        long endTime = new Date().getTime()  / 1000;
        when(rechargeAcrossDao.getRechargeAcrossSuccessListByTimeAndId(anyString(), anyLong(), anyLong(), anyLong(), anyInt(), anyBoolean())).thenReturn(Lists.newArrayList(new RechargeAcrossPO()));
        List<RechargeAcrossPO> rechargeAcrossPOList = this.rechargeOrderService.getSuccessOrderList(beginTime, endTime, 0, 100, false);
        Assert.assertEquals(2, rechargeAcrossPOList.size());
    }

    @Test(expected = BizException.class)
    public void getSuccessOrderListException() {
        long beginTime = DateUtils.addMonths(new Date(), -1).getTime() / 1000;
        long endTime = new Date().getTime()  / 1000;
        this.rechargeOrderService.getSuccessOrderList(endTime, beginTime, 0, 100, false);
    }

    @Test
    public void getRechargeInfoByRechargeOrderNum() {
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setRechargeOrderNum("R092022120414263931638595");
        rechargeAcrossPO.setAmount(BigDecimal.valueOf(1));
        rechargeAcrossPO.setRealAmount(BigDecimal.valueOf(100));
        rechargeAcrossPO.setCoin(BigDecimal.valueOf(100));
        rechargeAcrossPO.setStatus(1);
        when(this.orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202212");
        when(rechargeAcrossDao.queryByRechargeOrderNum(anyString(), anyString())).thenReturn(rechargeAcrossPO);
        RechargeInfo rechargeInfo = this.rechargeOrderService.getRechargeInfoByRechargeOrderNum("R092022120414263931638595");
        Assert.assertNotNull(rechargeInfo);
    }

    @Test
    public void getRechargeInfoByRechargeOrderNumNoData() {
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setRechargeOrderNum("R092022120414263931638595");
        rechargeAcrossPO.setAmount(BigDecimal.valueOf(1));
        rechargeAcrossPO.setRealAmount(BigDecimal.valueOf(100));
        rechargeAcrossPO.setCoin(BigDecimal.valueOf(100));
        rechargeAcrossPO.setStatus(0);
        when(this.orderIdService.getYearMonthFromRechargeOrderNumSilent(anyString())).thenReturn("202212");
        when(rechargeAcrossDao.queryByRechargeOrderNum(anyString(), anyString())).thenReturn(rechargeAcrossPO);
        RechargeInfo rechargeInfo = this.rechargeOrderService.getRechargeInfoByRechargeOrderNum("R092022120414263931638595");
        Assert.assertNull(rechargeInfo);
    }


    @Test(expected = TApplicationException.class)
    public void getRechargeInfoFromMaster() throws TException {
        RechargeThriftService.Iface service = new RechargeThriftService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.getRechargeInfoFromMaster("R092022120414263931638595");
    }

    @Test(expected = TApplicationException.class)
    public void isAgent() throws TException {
        RechargeThriftService.Iface service = new RechargeThriftService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, URI));
        service.isAgent(1290249156L);
    }

    @Test(expected = TApplicationException.class)
    public void reportIapReceipt() throws TException {
        ReportIapReceiptRequest request = new ReportIapReceiptRequest();
        request.setBusinessId(10000010);
        request.setOrderNo("s2023112815531752419405428714");
        request.setRechargeOrderNum("R092023112815532921480418");
        request.setReceiptData("ewoJInNpZ25hdHVyZSIgPSAiQkVCSmJGU1ZZdlVyQU82Ui80SnJ3MkJKR2J4dlovRjIzUHlQZTZVOGZDWng1Z2NHT0llcml6cFcwbS9IUWEyUW5hVTNFWG1SM0I1UXJNMnM3TFZWSXhLVlBzQ0M2RGJwamJDbWowVUpKeURsVW1xd1ZpV0pYRGVQSm8vaXVhUzNvc1o4U2gwUzdHREZMWkNqQ1dDVkhlZmdxbEVPeE44RGV0Y0hwNGM3VVY2aWZSMkNVV1pHMEwyVVBWY3lVWlZsLzVVSkVpeVVJZGcxZzIxRFFyNzNkUHR2QnVBdjV1QzhIaUc2UVZUTkc1WEd5cHJmUWp6VFNyS0lOQjZSeERZeC9wVWF5YnNsdFVDR3E5bHNsaFZPOStmMnprd3oySlVpeWJLUXdLUThhb3JUbWtYV0lvS2FNMjlMYlF3Rmh6Y043eU5UQWtiRVM1ZzlWM3g5V2dXM3pJY0FBQVhLTUlJRnhqQ0NCSzZnQXdJQkFnSVFGZWVmemxKVkNtVUJmSkhmNU82eldUQU5CZ2txaGtpRzl3MEJBUXNGQURCMU1VUXdRZ1lEVlFRREREdEJjSEJzWlNCWGIzSnNaSGRwWkdVZ1JHVjJaV3h2Y0dWeUlGSmxiR0YwYVc5dWN5QkRaWEowYVdacFkyRjBhVzl1SUVGMWRHaHZjbWwwZVRFTE1Ba0dBMVVFQ3d3Q1J6VXhFekFSQmdOVkJBb01Da0Z3Y0d4bElFbHVZeTR4Q3pBSkJnTlZCQVlUQWxWVE1CNFhEVEl5TURrd01qRTVNVE0xTjFvWERUSTBNVEF3TVRFNU1UTTFObG93Z1lreE56QTFCZ05WQkFNTUxrMWhZeUJCY0hBZ1UzUnZjbVVnWVc1a0lHbFVkVzVsY3lCVGRHOXlaU0JTWldObGFYQjBJRk5wWjI1cGJtY3hMREFxQmdOVkJBc01JMEZ3Y0d4bElGZHZjbXhrZDJsa1pTQkVaWFpsYkc5d1pYSWdVbVZzWVhScGIyNXpNUk13RVFZRFZRUUtEQXBCY0hCc1pTQkpibU11TVFzd0NRWURWUVFHRXdKVlV6Q0NBU0l3RFFZSktvWklodmNOQVFFQkJRQURnZ0VQQURDQ0FRb0NnZ0VCQUx4RXpndXRhakIycjhBSkREUjZHV0h2dlNBTjlmcERuaFAxclBNOGt3N1haWnQwd2xvM0oxVHdqczFHT29MTWRiOFM0QXNwN2xocm9PZENLdmVIQUoraXpLa2k1bTNvRGVmTEQvVFFaRnV6djQxanpjS2JZckFwMTk3QW80MnRHNlQ0NjJqYmM0WXVYOHk3SVgxcnVEaHVxKzhpZzBnVDlrU2lwRWFjNVdMc2REdC9ONVNpZG1xSUlYc0VmS0hUczU3aU5XMm5qbyt3NDJYV3lETWZUbzZLQSt6cHZjd2Z0YWVHamdUd2tPKzZJWTV0a21KeXdZblFtUDdqVmNsV3hqUjAvdlFlbWtOd1lYMStoc0o1M1ZCMTNRaXc1S2kxZWpaOWwvejVTU0FkNXhKaXFHWGFQQlpZL2laUmo1RjVxejFidS9rdTB6dFNCeGd3NTM4UG1POENBd0VBQWFPQ0Fqc3dnZ0kzTUF3R0ExVWRFd0VCL3dRQ01BQXdId1lEVlIwakJCZ3dGb0FVR1l1WGpVcGJZWGhYOUtWY05SS0tPUWpqc0hVd2NBWUlLd1lCQlFVSEFRRUVaREJpTUMwR0NDc0dBUVVGQnpBQ2hpRm9kSFJ3T2k4dlkyVnlkSE11WVhCd2JHVXVZMjl0TDNkM1pISm5OUzVrWlhJd01RWUlLd1lCQlFVSE1BR0dKV2gwZEhBNkx5OXZZM053TG1Gd2NHeGxMbU52YlM5dlkzTndNRE10ZDNka2NtYzFNRFV3Z2dFZkJnTlZIU0FFZ2dFV01JSUJFakNDQVE0R0NpcUdTSWIzWTJRRkJnRXdnZjh3TndZSUt3WUJCUVVIQWdFV0syaDBkSEJ6T2k4dmQzZDNMbUZ3Y0d4bExtTnZiUzlqWlhKMGFXWnBZMkYwWldGMWRHaHZjbWwwZVM4d2djTUdDQ3NHQVFVRkJ3SUNNSUcyRElHelVtVnNhV0Z1WTJVZ2IyNGdkR2hwY3lCalpYSjBhV1pwWTJGMFpTQmllU0JoYm5rZ2NHRnlkSGtnWVhOemRXMWxjeUJoWTJObGNIUmhibU5sSUc5bUlIUm9aU0IwYUdWdUlHRndjR3hwWTJGaWJHVWdjM1JoYm1SaGNtUWdkR1Z5YlhNZ1lXNWtJR052Ym1ScGRHbHZibk1nYjJZZ2RYTmxMQ0JqWlhKMGFXWnBZMkYwWlNCd2IyeHBZM2tnWVc1a0lHTmxjblJwWm1sallYUnBiMjRnY0hKaFkzUnBZMlVnYzNSaGRHVnRaVzUwY3k0d01BWURWUjBmQkNrd0p6QWxvQ09nSVlZZmFIUjBjRG92TDJOeWJDNWhjSEJzWlM1amIyMHZkM2RrY21jMUxtTnliREFkQmdOVkhRNEVGZ1FVSXNrOGUyTVRoYjQ2TzhVenFiVDZzYkNDa3hjd0RnWURWUjBQQVFIL0JBUURBZ2VBTUJBR0NpcUdTSWIzWTJRR0N3RUVBZ1VBTUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCQVFBOFJ1N1BxRHk0L1o2RHkxSHc5cWhSL09JSEhZSWszTzZTaWh2cVRhanFPMCtITXBvNU9kdGIrRnZhVFkzTit3bEtDN0hObWhsdlRzZjlhRnM3M1BsWGo1TWtTb1IwamFBa1ozYzVnamtOank5OGdZRVA3ZXRiK0hXMC9QUGVsSkc5VElVY2ZkR09aMlJJZ2dZS3NHRWt4UEJRSzFaYXJzMXV3SGVBWWM4SThxQlI1WFA1QVpFVFp6TC9NM0V6T3pCUFN6QUZmQzJ6T1d2ZkpsMnZmTGwyQnJtdUN4OWxVRlVCemFHelR6bHhCREhHU0hVVkpqOUszeXJrZ3NxT0dHWHBZTENPaHVMV1N0UnptU1N0VGhWT2JVVklhOFlEdTNjMFJwMUgxNlJvOXc5MFFFSTNlSVFvdmdJckNnNk0zbFpKbWxETkFuazdqTkE2cUsrWkhNcUIiOwoJInB1cmNoYXNlLWluZm8iID0gImV3b0pJbTl5YVdkcGJtRnNMWEIxY21Ob1lYTmxMV1JoZEdVdGNITjBJaUE5SUNJeU1ESXpMVEV4TFRJM0lESXpPalV6T2pNNUlFRnRaWEpwWTJFdlRHOXpYMEZ1WjJWc1pYTWlPd29KSW5CMWNtTm9ZWE5sTFdSaGRHVXRiWE1pSUQwZ0lqRTNNREV4TlRnd01Ua3dNREFpT3dvSkluVnVhWEYxWlMxcFpHVnVkR2xtYVdWeUlpQTlJQ0l3TURBd09EQXpNQzB3TURFMU5EZ3pOREkyTURSRE1ESkZJanNLQ1NKdmNtbG5hVzVoYkMxMGNtRnVjMkZqZEdsdmJpMXBaQ0lnUFNBaU16QXdNREF4TmpVeE5ESXdNalUxSWpzS0NTSmlkbkp6SWlBOUlDSTBMamN1TUM0d0lqc0tDU0poY0hBdGFYUmxiUzFwWkNJZ1BTQWlNVEk0TWpJM01EYzVOaUk3Q2draWRISmhibk5oWTNScGIyNHRhV1FpSUQwZ0lqTXdNREF3TVRZMU1UUXlNREkxTlNJN0Nna2ljWFZoYm5ScGRIa2lJRDBnSWpFaU93b0pJbWx1TFdGd2NDMXZkMjVsY25Ob2FYQXRkSGx3WlNJZ1BTQWlVRlZTUTBoQlUwVkVJanNLQ1NKdmNtbG5hVzVoYkMxd2RYSmphR0Z6WlMxa1lYUmxMVzF6SWlBOUlDSXhOekF4TVRVNE1ERTVNREF3SWpzS0NTSjFibWx4ZFdVdGRtVnVaRzl5TFdsa1pXNTBhV1pwWlhJaUlEMGdJamswTlRrM05qSTJMVE0zT0VVdE5FWXdNeTFDTVRoRExUa3dNVUZEUWpaR05qTXlNQ0k3Q2draWFYUmxiUzFwWkNJZ1BTQWlNVEk0TWprek16VTFOaUk3Q2draWRtVnljMmx2YmkxbGVIUmxjbTVoYkMxcFpHVnVkR2xtYVdWeUlpQTlJQ0k0TmpBMk1qWTJOVFVpT3dvSkltbHpMV2x1TFdsdWRISnZMVzltWm1WeUxYQmxjbWx2WkNJZ1BTQWlabUZzYzJVaU93b0pJbkJ5YjJSMVkzUXRhV1FpSUQwZ0ltTnZiUzVqYUdGdVp5NXJZMjlwYmk0eE1EaDVkV0Z1SWpzS0NTSndkWEpqYUdGelpTMWtZWFJsSWlBOUlDSXlNREl6TFRFeExUSTRJREEzT2pVek9qTTVJRVYwWXk5SFRWUWlPd29KSW1sekxYUnlhV0ZzTFhCbGNtbHZaQ0lnUFNBaVptRnNjMlVpT3dvSkltOXlhV2RwYm1Gc0xYQjFjbU5vWVhObExXUmhkR1VpSUQwZ0lqSXdNak10TVRFdE1qZ2dNRGM2TlRNNk16a2dSWFJqTDBkTlZDSTdDZ2tpWW1sa0lpQTlJQ0pqYjIwdWEzVm5iM1V1YTNSMk1qQXhOaUk3Q2draWNIVnlZMmhoYzJVdFpHRjBaUzF3YzNRaUlEMGdJakl3TWpNdE1URXRNamNnTWpNNk5UTTZNemtnUVcxbGNtbGpZUzlNYjNOZlFXNW5aV3hsY3lJN0NuMD0iOwoJInBvZCIgPSAiMzAiOwoJInNpZ25pbmctc3RhdHVzIiA9ICIwIjsKfQ==");
        request.setSign("8ae24cc5260d1dfe95b6be17fe0812ae");
        RechargeService.Iface service = new RechargeService.Client(CurlTProtocolFactory.buildTJSONProtocol(HOST, RECHARGE_URI));
        service.reportIapReceipt(request);
    }
}
