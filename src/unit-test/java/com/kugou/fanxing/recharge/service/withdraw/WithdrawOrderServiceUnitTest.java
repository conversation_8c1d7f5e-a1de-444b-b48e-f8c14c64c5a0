package com.kugou.fanxing.recharge.service.withdraw;

import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawOrderDao;
import com.kugou.fanxing.recharge.model.po.WithdrawOrderPO;
import com.kugou.fanxing.recharge.model.request.QueryWithdrawOrderReq;
import com.kugou.fanxing.recharge.service.common.DencryptService;
import com.kugou.fanxing.recharge.util.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class WithdrawOrderServiceUnitTest {

    @Mock
    private WithdrawOrderDao withdrawOrderDao;
    @Mock
    private DencryptService dencryptService;
    @InjectMocks
    private WithdrawOrderService withdrawOrderService;

    @Test
    public void isValidWithdrawOrder() {
        when(dencryptService.decrypt(any(String.class))).thenReturn("***********");
        Assert.assertTrue(withdrawOrderService.isValidWithdrawOrder(buildQueryWithdrawOrderReq(), buildWithdrawOrderPO()));
        when(dencryptService.decrypt(any(String.class))).thenReturn("***********");
        Assert.assertFalse(withdrawOrderService.isValidWithdrawOrder(buildQueryWithdrawOrderReq(), buildWithdrawOrderPO()));
    }

    private WithdrawOrderPO buildWithdrawOrderPO() {
        WithdrawOrderPO withdrawOrder = new WithdrawOrderPO();
        withdrawOrder.setKugouId(1290249156L);
        withdrawOrder.setBizAppId(********);
        withdrawOrder.setOrderId(1366369728736507377L);
        withdrawOrder.setTotalAmount(BigDecimal.valueOf(12.5));
        withdrawOrder.setAccountEncrypted("oDcjAJipe-HvL6Tb1Ib0JxjuQC4jb3W0fXoW_wF9Gy0@DQA9AIQCAAA=");
        return withdrawOrder;
    }

    @Test
    public void withdrawOrderVerify() {
        when(withdrawOrderDao.getDrawCashOrderById(anyLong())).thenReturn(buildWithdrawOrderPO());
        when(dencryptService.decrypt(any(String.class))).thenReturn("***********");
        JsonResult<Map<String, Object>> jsonResult = this.withdrawOrderService.withdrawOrderVerify(buildQueryWithdrawOrderReq());
        log.warn("jsonResult: {}", jsonResult);
        Assert.assertNotNull(jsonResult);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertTrue(jsonResult.getData().containsKey("exist"));
        Assert.assertEquals(1, jsonResult.getData().get("exist"));
    }

    @Test
    public void withdrawOrderVerifyAbNormal() {
        when(withdrawOrderDao.getDrawCashOrderById(anyLong())).thenReturn(buildWithdrawOrderPO());
        when(dencryptService.decrypt(any(String.class))).thenReturn("***********");
        JsonResult<Map<String, Object>> jsonResult = this.withdrawOrderService.withdrawOrderVerify(buildQueryWithdrawOrderReq());
        log.warn("jsonResult: {}", jsonResult);
        Assert.assertNotNull(jsonResult);
        Assert.assertNotNull(jsonResult.getData());
        Assert.assertTrue(jsonResult.getData().containsKey("exist"));
        Assert.assertEquals(0, jsonResult.getData().get("exist"));
    }

    private QueryWithdrawOrderReq buildQueryWithdrawOrderReq() {
        QueryWithdrawOrderReq param = new QueryWithdrawOrderReq();
        param.setBiz_appid(********);
        param.setUserid(1290249156L);
        param.setOrder_no(1366369728736507377L);
        param.setTotal_fee(BigDecimal.valueOf(12.50));
        param.setUser_account("***********");
        return param;
    }
}
