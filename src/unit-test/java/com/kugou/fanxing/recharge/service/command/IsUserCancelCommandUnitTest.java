package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.thrift.plat.user.UserPlatBizService;
import com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class IsUserCancelCommandUnitTest {

    @Mock
    private UserPlatBizService.Iface userPlatBizService;

    @Test
    public void getUserCancelStatus() throws TException {
        ResUserCancelStatusResponse response = new ResUserCancelStatusResponse();
        response.setCode(0);
        response.setData(false);
        when(userPlatBizService.getUserCancelStatus(anyLong())).thenReturn(response);
        IsUserCancelCommand command = new IsUserCancelCommand(userPlatBizService, 1290249156L);
        Assert.assertTrue(command.execute());
        when(userPlatBizService.getUserCancelStatus(anyLong())).thenReturn(null);
        command = new IsUserCancelCommand(userPlatBizService, 1290249156L);
        Assert.assertFalse(command.execute());
    }

    @Test
    public void getUserCancelStatusException() throws TException {
        when(userPlatBizService.getUserCancelStatus(anyLong())).thenThrow(new TException("test"));
        IsUserCancelCommand command = new IsUserCancelCommand(userPlatBizService, 1290249156L);
        Assert.assertFalse(command.execute());
    }

}
