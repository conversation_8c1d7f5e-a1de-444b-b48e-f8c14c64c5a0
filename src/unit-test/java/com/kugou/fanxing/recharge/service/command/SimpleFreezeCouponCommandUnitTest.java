package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.coupon.thrift.CouponService;
import com.kugou.fanxing.coupon.thrift.FreezeParamsVO;
import com.kugou.fanxing.coupon.thrift.OperResult;
import com.kugou.fanxing.coupon.thrift.ReturnResult;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class SimpleFreezeCouponCommandUnitTest {

    @Mock
    private CouponService.Iface couponService;

    private FreezeParamsVO freezeParamsVO;

    @Before
    public void before() {
        this.freezeParamsVO = new FreezeParamsVO();
        freezeParamsVO.setCouponId(1L);
    }

    @Test
    public void simpleFreeze() throws TException {
        ReturnResult returnResult = new ReturnResult();
        returnResult.setCode(0);
        returnResult.setData(new OperResult().setResult(0).setOrderId(1));
        when(couponService.simpleFreeze(any(FreezeParamsVO.class))).thenReturn(returnResult);
        SimpleFreezeCouponCommand command = new SimpleFreezeCouponCommand(couponService, freezeParamsVO);
        Assert.assertTrue(command.execute());
        when(couponService.simpleFreeze(any(FreezeParamsVO.class))).thenReturn(null);
        command = new SimpleFreezeCouponCommand(couponService, freezeParamsVO);
        Assert.assertFalse(command.execute());
    }

    @Test
    public void simpleFreezeException() throws TException {
        when(couponService.simpleFreeze(any(FreezeParamsVO.class))).thenThrow(new ContextedRuntimeException());
        SimpleFreezeCouponCommand command = new SimpleFreezeCouponCommand(couponService, freezeParamsVO);
        Assert.assertFalse(command.execute());
    }
}
