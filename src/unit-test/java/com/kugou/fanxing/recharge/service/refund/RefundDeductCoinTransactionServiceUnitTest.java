package com.kugou.fanxing.recharge.service.refund;

import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundDeductCoinOrderDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundOrderDao;
import com.kugou.fanxing.recharge.model.po.refund.RefundDeductCoinOrderPO;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class RefundDeductCoinTransactionServiceUnitTest {

    @InjectMocks
    private RefundDeductCoinTransactionService refundDeductCoinTransactionService;
    @Mock
    private RefundOrderDao refundOrderDao;
    @Mock
    private RefundDeductCoinOrderDao refundDeductCoinOrderDao;

    private RefundDeductCoinOrderPO refundDeductCoinOrderPO;

    @Before
    public void before() {
        refundDeductCoinOrderPO = new RefundDeductCoinOrderPO();
        refundDeductCoinOrderPO.setRechargeOrderNum("R092021011813353892895615");
        refundDeductCoinOrderPO.setDeductCoin(BigDecimal.TEN);
    }

    @Test(expected = ContextedRuntimeException.class)
    public void deductCoinFailException() {
        when(refundOrderDao.decrDeductCoinAmount(anyString(), any(BigDecimal.class))).thenReturn(1);
        when(refundDeductCoinOrderDao.updateByPrimay(anyLong(), anyInt())).thenReturn(0);
        Assert.assertTrue(this.refundDeductCoinTransactionService.deductCoinFail(refundDeductCoinOrderPO));
    }

    @Test
    public void deductCoinFail() {
        when(refundOrderDao.decrDeductCoinAmount(anyString(), any(BigDecimal.class))).thenReturn(1);
        when(refundDeductCoinOrderDao.updateByPrimay(anyLong(), anyInt())).thenReturn(1);
        Assert.assertTrue(this.refundDeductCoinTransactionService.deductCoinFail(refundDeductCoinOrderPO));
    }

    @Test
    public void createDeductCoinOrder() {
        when(refundDeductCoinOrderDao.saveRecord(any(RefundDeductCoinOrderPO.class))).thenReturn(1);
        when(refundOrderDao.incrDeductCoinAmount(anyString(), any(BigDecimal.class))).thenReturn(1);
        Assert.assertTrue(this.refundDeductCoinTransactionService.createDeductCoinOrder(refundDeductCoinOrderPO));
    }

    @Test(expected = ContextedRuntimeException.class)
    public void createDeductCoinOrderException() {
        when(refundDeductCoinOrderDao.saveRecord(any(RefundDeductCoinOrderPO.class))).thenReturn(0);
        when(refundOrderDao.incrDeductCoinAmount(anyString(), any(BigDecimal.class))).thenReturn(1);
        Assert.assertTrue(this.refundDeductCoinTransactionService.createDeductCoinOrder(refundDeductCoinOrderPO));
    }
}
