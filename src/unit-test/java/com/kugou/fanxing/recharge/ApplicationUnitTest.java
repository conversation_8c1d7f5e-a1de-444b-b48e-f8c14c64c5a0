package com.kugou.fanxing.recharge;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.LocaleUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class ApplicationUnitTest {
    @Test
    public void main() {
        LocaleUtils.availableLocaleSet()
                .forEach(locale -> log.warn("xx: {}, xx: {}, xx: {}, xxx: {}", locale.getDisplayLanguage(),
                        locale.getLanguage(), locale.getDisplayCountry(), locale.getCountry()));
    }

}
