package com.kugou.fanxing.recharge.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

import static com.kugou.fanxing.recharge.util.IpUtils.isValidAddress;
import static com.kugou.fanxing.recharge.util.IpUtils.isValidInet4Address;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class IpUtilsUnitTest {

    @Mock
    private HttpServletRequest request;

    @Test
    public void test() {
        try (MockedStatic<RequestContextHolder> mockedStatic = Mockito.mockStatic(RequestContextHolder.class)) {
            mockedStatic.when(() -> RequestContextHolder.currentRequestAttributes()).thenReturn(new ServletRequestAttributes(request));
            Assert.assertEquals("**********", getClientIpAddress("fc00::172:19:32:c891, **********"));
            Assert.assertEquals("fc00::172:19:32:c891", getClientIpAddressV2("fc00::172:19:32:c891, **********"));
        }
    }

    @Test
    public void testWkProxyIp() {
        try (MockedStatic<RequestContextHolder> mockedStatic = Mockito.mockStatic(RequestContextHolder.class)) {
            mockedStatic.when(() -> RequestContextHolder.currentRequestAttributes()).thenReturn(new ServletRequestAttributes(request));
            when(request.getHeader(eq("x-forwarded-for"))).thenReturn("************,************");
            String clientIp1 = IpUtils.getClientIpAddress();
            log.warn("clientIp1: {}", clientIp1);
            Assert.assertEquals("************", clientIp1);
            when(request.getHeader(eq("x-forwarded-for"))).thenReturn("*************,************");
            when(request.getHeader(eq("x-real-ip"))).thenReturn("************");
            String clientIp2 = IpUtils.getClientIpAddress();
            log.warn("clientIp2: {}", clientIp2);
            Assert.assertEquals("************", clientIp2);
            when(request.getHeader(eq("x-forwarded-for"))).thenReturn("*************,************");
            when(request.getHeader(eq("x-real-ip"))).thenReturn(null);
            String clientIp3 = IpUtils.getClientIpAddress();
            log.warn("clientIp3: {}", clientIp3);
            Assert.assertEquals("*************", clientIp3);
        }
    }

    public static String getClientIpAddress(String hostAddress) {
        String[] ips = hostAddress.split(",");
        for (String ip : ips) {
            if (isValidInet4Address(ip.trim())) {
                hostAddress = ip.trim();
                break;
            }
        }
        return hostAddress;
    }

    public static String getClientIpAddressV2(String hostAddress) {
        String[] ips = hostAddress.split(",");
        for (String ip : ips) {
            if (isValidAddress(ip.trim())) {
                hostAddress = ip.trim();
                break;
            }
        }
        return hostAddress;
    }
}
