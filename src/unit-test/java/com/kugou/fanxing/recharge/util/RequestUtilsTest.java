package com.kugou.fanxing.recharge.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@Slf4j
@RunWith(MockitoJUnitRunner.Silent.class)
public class RequestUtilsTest {

    @Mock
    private HttpServletRequest request;

    @Test
    public void getRequestURI() {
        String expectedRequestURI = "api/v1/user?id=1";
        when(request.getAttribute(anyString())).thenReturn(null);
        when(request.getContextPath()).thenReturn("/");
        when(request.getRequestURI()).thenReturn(expectedRequestURI);
        String requestURI = RequestUtils.getRequestURI(request);
        Assert.assertEquals(expectedRequestURI, requestURI);
    }

    @Test
    public void parseMultiCookie() {
        Cookie[] cookies = new Cookie[] { new Cookie("k1", "v1"), new Cookie("k2", "v2") };
        when(request.getCookies()).thenReturn(cookies);
        Map<String, Cookie> cookieMap = RequestUtils.parseCookie(request);
        log.warn("cookieMap: {}", cookieMap);
        Assert.assertNotNull(cookieMap);
        Assert.assertEquals(2, cookieMap.size());
    }

    @Test
    public void parseEmptyCookie() {
        when(request.getCookies()).thenReturn(null);
        Map<String, Cookie> cookieMap = RequestUtils.parseCookie(request);
        log.warn("cookieMap: {}", cookieMap);
        Assert.assertNotNull(cookieMap);
        Assert.assertEquals(0, cookieMap.size());
    }

    @Test
    public void parseMultiHeader() {
        when(request.getHeaderNames()).thenReturn(new Enumeration<String>() {
            private final String[] elements = {"Content-Type", "protocol"};
            private int cursor = 0;

            @Override
            public boolean hasMoreElements() {
                return cursor < elements.length;
            }
            @Override
            public String nextElement() {
                return elements[cursor++];
            }
        });
        when(request.getHeader(eq("Content-Type"))).thenReturn("application/x-thrift");
        when(request.getHeader(eq("protocol"))).thenReturn("json");
        Map<String, String> headerMap = RequestUtils.parseHeader(request);
        log.warn("headerMap: {}", headerMap);
        Assert.assertNotNull(headerMap);
        Assert.assertEquals(2, headerMap.size());
    }

    @Test
    public void parseEmptyHeader() {
        when(request.getHeaderNames()).thenReturn(Collections.emptyEnumeration());
        when(request.getHeader(eq("Content-Type"))).thenReturn("application/x-thrift");
        when(request.getHeader(eq("protocol"))).thenReturn("json");
        Map<String, String> headerMap = RequestUtils.parseHeader(request);
        log.warn("headerMap: {}", headerMap);
        Assert.assertNotNull(headerMap);
        Assert.assertEquals(0, headerMap.size());
    }

    @Test
    public void getAttribute() {
        when(request.getAttribute(anyString())).thenReturn("value");
        Optional<String> optionalAttrValue = RequestUtils.getAttribute(request, "name", String.class);
        Assert.assertTrue(optionalAttrValue.isPresent());
        Assert.assertEquals("value", optionalAttrValue.get());
    }

    @Test
    public void getNotExistsAttribute() {
        when(request.getAttribute(anyString())).thenReturn(null);
        Optional<String> optionalAttrValue = RequestUtils.getAttribute(request, "name", String.class);
        Assert.assertFalse(optionalAttrValue.isPresent());
    }

    @Test
    public void getPossibleParameter() {
        when(request.getParameter(eq("param1"))).thenReturn("value1");
        when(request.getParameter(eq("param2"))).thenReturn("value2");
        Assert.assertEquals("value1", RequestUtils.getPossibleParameter(request, "param1", "param2"));
        when(request.getParameter(eq("param1"))).thenReturn("");
        when(request.getParameter(eq("param2"))).thenReturn("value2");
        Assert.assertEquals("value2", RequestUtils.getPossibleParameter(request, "param1", "param2"));
        when(request.getParameter(eq("param1"))).thenReturn("");
        when(request.getParameter(eq("param2"))).thenReturn(null);
        Assert.assertEquals("", RequestUtils.getPossibleParameter(request, "param1", "param2"));
        Assert.assertEquals("", RequestUtils.getPossibleParameter(request));
    }
}