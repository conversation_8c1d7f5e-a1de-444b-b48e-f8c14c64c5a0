package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.thrift.fangroup.service.FanGroupService;
import com.kugou.fanxing.thrift.fangroup.service.GetFanGroupInfoRequest;
import com.kugou.fanxing.thrift.fangroup.service.GetFanGroupInfoResponse;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Optional;

/**
 * 获取粉丝组信息
 */
@Slf4j
public class GetFanGroupInfoCommand extends HystrixCommand<Optional<GetFanGroupInfoResponse>> {

    private final FanGroupService.Iface fanGroupService;
    private final GetFanGroupInfoRequest request;

    public GetFanGroupInfoCommand(final FanGroupService.Iface fanGroupService, final GetFanGroupInfoRequest request) {
        super(HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("GetFanGroupInfoCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(300)));

        this.fanGroupService = fanGroupService;
        this.request = request;
    }

    @Override
    protected Optional<GetFanGroupInfoResponse> run() throws Exception {
        return Optional.of(this.fanGroupService.getFanGroupInfo(request));
    }

    @Override
    protected Optional<GetFanGroupInfoResponse> getFallback() {
        Optional<GetFanGroupInfoResponse> fallback = Optional.empty();
        log.warn("GetFanGroupInfoCommand 服务降级! 调用粉丝组信息出错, request: {}! 降级返回数据: {}, 降级原因: {}",
                request, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }

}
