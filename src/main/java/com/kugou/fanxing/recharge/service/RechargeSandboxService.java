package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.thrift.pay.v2.PlatformPayV2Service;
import com.kugou.fanxing.thrift.pay.v2.SandboxRechargeLimitReq;
import com.kugou.fanxing.thrift.pay.v2.SandboxRechargeLimitResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Service
public class RechargeSandboxService {

    @Autowired
    private PlatformPayV2Service.Iface platformPayV2Service;
    @Autowired
    private ApolloConfigService apolloConfigService;

    public boolean allowAppleSandbox(int stdPlat, String appVersion, String rechargeOrderNum, BigDecimal amount) {
        Set<String> allowVersions = this.apolloConfigService.getAppstoreSandboxAllowVersions();
        if (!allowVersions.contains(appVersion)) {
            log.warn("沙盒充值使用检查，检查是否允许使用沙盒，非白名单版本禁止沙盒充值。rechargeOrderNum: {}, " +
                    "appVersion: {}, allowVersions: {}", rechargeOrderNum, appVersion, allowVersions);
            throw new BizException(SysResultCode.IAP_SANDBOX_VERSION_BLOCKED);
        }
        String key = String.format("%s_%s_%s", PayTypeIdEnum.PAY_TYPE_ID_1006.getPayTypeCode(), stdPlat, appVersion);
        BigDecimal limitAmount = this.apolloConfigService.getAppstoreSandboxLimitAmount();
        return this.allowSandbox(key, limitAmount, rechargeOrderNum, amount);
    }

    public boolean allowSandbox(String key, BigDecimal limitAmount, String rechargeOrderNum, BigDecimal amount) {
        SandboxRechargeLimitReq request = new SandboxRechargeLimitReq();
        request.setAppId(1000_0002);
        request.setOrderNum(rechargeOrderNum);
        request.setCalKey(key);
        request.setCalValue(amount.stripTrailingZeros().toPlainString());
        request.setLimitValue(limitAmount.longValue());
        request.setSign(FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXt"));
        try {
            SandboxRechargeLimitResponse response = platformPayV2Service.sandboxRechargeLimit(request);
            if (Objects.nonNull(response) && response.getCode() == 0 && Objects.nonNull(response.getTotalValue())) {
                boolean allowRecharge = new BigDecimal(response.getTotalValue()).longValue() <= limitAmount.longValue();
                log.error("沙盒充值使用检查，检查是否允许使用沙盒。key: {}, limitAmount: {}, rechargeOrderNum: {}, allowRecharge: {}",
                        key, limitAmount, rechargeOrderNum, allowRecharge);
                return allowRecharge;
            }
        } catch (Exception e) {
            log.error("沙盒充值使用检查，执行异常。key: {}, limitAmount: {}, rechargeOrderNum: {}", key, limitAmount, rechargeOrderNum, e);
        }
        return false;
    }
}
