package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.thrift.idmapping.user.UserIdMappingService;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Optional;

/**
 * 酷狗ID=>繁星ID
 *
 * <AUTHOR>
 */
@Slf4j
public class GetUserIdByKugouIdCommand extends HystrixCommand<Optional<Long>> {

    private final UserIdMappingService.Iface userIdMappingService;
    private final long kugouId;

    public GetUserIdByKugouIdCommand(final UserIdMappingService.Iface userIdMappingService, final long kugouId) {
        super(HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("GetUserIdByKugouIdCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(300)));
        this.userIdMappingService = userIdMappingService;
        this.kugouId = kugouId;
    }

    @Override
    protected Optional<Long> run() throws Exception {
        return Optional.of(this.userIdMappingService.getUserIdByKugouId(kugouId));
    }

    @Override
    protected Optional<Long> getFallback() {
        Optional<Long> fallback = Optional.empty();
        log.warn("GetUserIdByKugouIdCommand服务降级! 通过酷狗ID查询繁星ID信息出错, kugouId: {}! 降级返回数据: {}, 降级原因: {}",
                kugouId, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }
}
