package com.kugou.fanxing.recharge.service.handler;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.dto.WxMiniProgramDTO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.RechargeCommonService;
import com.kugou.fanxing.recharge.service.WechatRechargeService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.thrift.PurchaseProductRequest;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 微信小程序支付方式
 */
@Slf4j
@Service
public class PayType42Handler implements IPayTypeHandler {

    @Autowired
    private RechargeConfig rechargeConfig;
    @Autowired
    private OrderIdService orderIdService;
    @Autowired
    private WechatRechargeService wechatRechargeService;
    @Autowired
    private RechargeCommonService rechargeCommonService;
    @Autowired
    private RechargeAcrossDao rechargeAcrossDao;
    @Autowired
    private ApolloConfigService apolloConfigService;

    @Override
    public Map<String, Object> handle(String businessId, PurchaseProductRequest request) {
        checkPrecondition(request);
        Map<String, Object> payload = Maps.newHashMap();
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        Optional<WxMiniProgramDTO> optionalWxMiniProgramDTO = handleWxMiniProgram(rechargeOrderNum, businessId, request);
        if (!optionalWxMiniProgramDTO.isPresent()) {
            log.error("微信小程序下单接口, 调用微信小程序下单接口失败");
            throw new BizException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        payload.put("rechargeOrderNum", rechargeOrderNum);
        payload.put("paymentParams", optionalWxMiniProgramDTO.get());
        return payload;
    }

    private void checkPrecondition(PurchaseProductRequest request) {
        String openId = request.getOpenId();
        if (StringUtils.isBlank(openId)) {
            log.error("微信小程序下单接口, 调用微信小程序下单接口失败，请求参数不合法。request: {}", request);
            throw new BizException(SysResultCode.RECHARGE_PARAM_ERROR);
        }
    }

    private Optional<WxMiniProgramDTO> handleWxMiniProgram(String rechargeOrderNum, String businessId, PurchaseProductRequest request) {
        long kugouId = request.getKugouId();
        BigDecimal amount = new BigDecimal(request.getAmount());
        String url = rechargeConfig.getActionUrlPrefix(PayTypeIdEnum.PAY_TYPE_ID_42);
        RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(request.getPid(), kugouId, rechargeOrderNum, PayTypeIdEnum.PAY_TYPE_ID_42, amount, request.getClientIp());
        Map<String, String> callBackArg = buildPurchaseGoodsCallBackArgs(businessId, request);
        Map<String, String> userFundPlatParam = buildUserFundPlatParam(businessId, request);
        String extend = this.rechargeCommonService.buildProductExtendStr(kugouId, rechargeOrderNum, callBackArg, userFundPlatParam);
        Map<String, String> wxMiniProgramParams = wechatRechargeService.buildWxMiniProgramParams(rechargeAcrossPO, request.getOpenId(), kugouId, amount, extend, ReTypeEnum.RETYPE_PURCHASE);
        log.warn("调用酷狗支付网关小程序下单接口, url: {}, params: {}", url, wxMiniProgramParams);
        Optional<String> optionalJson = HttpClientUtils.doSyncGet(url, wxMiniProgramParams);
        // 校验网关响应
        if (!optionalJson.isPresent()) {
            log.error("调用酷狗支付网关小程序下单失败, url: {}, params: {}, optionalJson: {}", url, wxMiniProgramParams, optionalJson);
            return Optional.empty();
        }
        String json = optionalJson.get();
        Configuration conf = Configuration.defaultConfiguration().addOptions(Option.SUPPRESS_EXCEPTIONS);
        int errorCode = JsonPath.using(conf).parse(json).read("$.error_code", Integer.class);
        String errorMsg = JsonPath.using(conf).parse(json).read("$.error_msg", String.class);
        if (errorCode != 0) {
            log.error("调用酷狗支付网关小程序下单失败, url: {}, params: {}, errorCode: {}, errorMsg: {}", url, wxMiniProgramParams, errorCode, errorMsg);
            return Optional.empty();
        }

        // 解析有效载荷
        log.warn("调用酷狗支付网关小程序下单成功, json: {}", json);
        String orderNo = JsonPath.using(conf).parse(json).read("$.order_no", String.class);
        String outTradeNo = JsonPath.using(conf).parse(json).read("$.out_trade_no", String.class);
        String data = JsonPath.using(conf).parse(json).read("$.data", String.class);
        WxMiniProgramDTO wxMiniProgramDTO = JSON.parseObject(data, WxMiniProgramDTO.class);
        if (StringUtils.isBlank(orderNo) || StringUtils.isBlank(outTradeNo) || !rechargeOrderNum.equals(orderNo) || Objects.isNull(wxMiniProgramDTO)) {
            log.error("解析酷狗支付网关小程序订单失败, url: {}, params: {}, json: {}", url, wxMiniProgramParams, json);
            return Optional.empty();
        }

        // 保存充值订单
        Optional<String> optionalMonth = this.orderIdService.getYearMonthFromRechargeOrderNum(rechargeOrderNum);
        String month = optionalMonth.orElseThrow(() -> new ContextedRuntimeException("解析充值订单号月份信息失败").addContextValue("rechargeOrderNum", rechargeOrderNum));
        int affected = rechargeAcrossDao.add(month, rechargeAcrossPO);
        if (affected < 1) {
            log.error("记录小程序下单信息失败, rechargeAcrossPO: {}, affected: {}", rechargeAcrossPO, affected);
            return Optional.empty();
        }
        return Optional.of(wxMiniProgramDTO);
    }

    private Map<String, String> buildPurchaseGoodsCallBackArgs(String businessId, PurchaseProductRequest request) {
        Map<String, String> callBackArg = Maps.newHashMap();
        callBackArg.put("businessType", request.getBusinessId());
        callBackArg.put("accessToken", "94cd44b3ab99c0b89b5fbfb9b44fb18f");
        callBackArg.put("amount", String.valueOf(request.getAmount()));
        callBackArg.put("kugouId", String.valueOf(request.getKugouId()));
        callBackArg.put("fromKugouId", String.valueOf(request.getKugouId()));
        callBackArg.put("toKugouId", String.valueOf(request.getToKugouId()));
        callBackArg.put("businessId", businessId);
        callBackArg.put("businessTime", String.valueOf(request.getBusinessTime()));
        callBackArg.put("subject", request.getSubject());
        callBackArg.put("syncUrl", StringUtils.defaultString(request.getSyncUrl()));
        callBackArg.put("redirectUrl", StringUtils.defaultString(request.getRedirectUrl()));
        callBackArg.put("pid", String.valueOf(request.getPid()));
        callBackArg.put("topic", apolloConfigService.businessNotifyTopicOf(request.getBusinessId()));
        callBackArg.put("clientIp", String.valueOf(request.getClientIp()));
        callBackArg.put("cFrom", String.valueOf(request.getPid()));
        callBackArg.put("refer", "0");
        callBackArg.put("channelId", "0");
        callBackArg.put("businessExt", request.getExtJson());
        callBackArg.put("reType", "1");
        callBackArg.put("rebate", "0");
        callBackArg.put("version", "20170111");
        callBackArg.put("payTime", String.valueOf(DateHelper.getCurrentSeconds()));
        callBackArg.put("addTime", String.valueOf(DateHelper.getCurrentSeconds()));
        callBackArg.put("payTypeId", String.valueOf(PayTypeIdEnum.PAY_TYPE_ID_42.getPayTypeId()));

        // 微信服务号 openId
        if (StringUtils.isNotEmpty(request.getOpenId())) {
            callBackArg.put("openId", request.getOpenId());
        }
        return callBackArg;
    }

    private Map<String, String> buildUserFundPlatParam(String businessId, PurchaseProductRequest request) {
        Map<String, String> userFundPlatParam = Maps.newHashMap();
        userFundPlatParam.put("senderDepartmentId", "0");
        userFundPlatParam.put("senderProductId", "0");
        userFundPlatParam.put("senderMinorProductId", "0");
        userFundPlatParam.put("senderHardwarePlatform", "0");
        userFundPlatParam.put("senderChannelId", "0");
        userFundPlatParam.put("senderSubChannelId", "0");
        userFundPlatParam.put("receiverDepartmentId", "0");
        // TODO 兼容消费的BUG
        String actionId = "********".equals(request.getBusinessId()) ? "293" : "0";
        userFundPlatParam.put("actionId", actionId);
        userFundPlatParam.put("fromKugouId", String.valueOf(request.getKugouId()));
        userFundPlatParam.put("toKugouId", String.valueOf(request.getToKugouId()));
        userFundPlatParam.put("accountChangeType", String.valueOf(request.getConsumeParam().getAccountChangeType()));
        userFundPlatParam.put("fxcChangeDesc", String.format("用户kugouId:%s，%s", request.getSubject(), request.getKugouId()));
        userFundPlatParam.put("roomId", String.valueOf(request.getConsumeParam().getRoomId()));
        userFundPlatParam.put("giftId", String.valueOf(request.getConsumeParam().getGiftId()));
        userFundPlatParam.put("giftName", String.valueOf(request.getConsumeParam().getGiftName()));
        userFundPlatParam.put("giftNum", String.valueOf(request.getConsumeParam().getGiftNum()));
        userFundPlatParam.put("coin", new BigDecimal(request.getAmount()).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString());
        userFundPlatParam.put("ext", request.getConsumeParam().getExt());
        userFundPlatParam.put("pid", String.valueOf(request.getPid()));
        userFundPlatParam.put("ip", request.getClientIp());
        userFundPlatParam.put("globalId", businessId);
        return userFundPlatParam;
    }
}
