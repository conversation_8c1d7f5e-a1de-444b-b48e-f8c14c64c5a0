package com.kugou.fanxing.recharge.service.after;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.FirstRechargeDao;
import com.kugou.fanxing.recharge.model.dto.AfterRechargeAcrossDTO;
import com.kugou.fanxing.recharge.model.dto.CoinCallbackDTO;
import com.kugou.fanxing.recharge.model.dto.PurchaseMsgDTO;
import com.kugou.fanxing.recharge.model.po.FirstRechargePO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.vo.PayedNoticeVo;
import com.kugou.fanxing.recharge.mq.PostRechargeNsqProducer;
import com.kugou.fanxing.recharge.service.RechargeCommonService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.thrift.callback.ConsumeParam;
import com.kugou.fanxing.recharge.thrift.callback.PurchaseForIosRequest;
import com.kugou.fanxing.recharge.thrift.callback.RenewalsForIosRequest;
import com.kugou.fanxing.recharge.util.*;
import com.kugou.fanxing.thrift.acksocket.gather.service.AppDispatchServiceV2;
import com.kugou.fanxing.thrift.acksocket.gather.types.BoolResponse;
import com.kugou.fanxing.thrift.acksocket.gather.types.MsgOption;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashSet;
import java.util.Objects;

/**
 * 充值后NSQ消息发放
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AfterRechargeService {

    @Autowired
    private OrderIdService orderIdService;
    @Autowired
    private FirstRechargeDao firstRechargeDao;
    @Autowired
    private PostRechargeNsqProducer postRechargeNsqProducer;
    @Autowired
    private AppDispatchServiceV2.Iface appDispatchServiceV2;

    private MsgOption buildMsgOption(long gid) {
        MsgOption msgOption = new MsgOption();
        msgOption.setSendToAll(true);
        msgOption.setClientIds(new HashSet<>());
        msgOption.setExceptKugouIds(new HashSet<>());
        msgOption.setIsIdempotent(true);
        msgOption.setGid(gid);
        return msgOption;
    }

    public void afterRechargeSuccess(RechargeAcrossPO callbackOrder) {
        pushSocketToRoom(callbackOrder);
        log.warn("处理充值后逻辑，开始处理。order: {}", callbackOrder);
        AfterRechargeAcrossDTO afterRechargeAcrossDTO = ModelUtils.from(callbackOrder, AfterRechargeAcrossDTO.class)
                .orElseThrow(() -> new ContextedRuntimeException("对象拷贝失败").addContextValue("rechargeOrderNum", callbackOrder.getRechargeOrderNum()));
        afterRechargeAcrossDTO.setAddTime(String.valueOf(callbackOrder.getAddTime()));
        afterRechargeAcrossDTO.setAmount(callbackOrder.getAmount().stripTrailingZeros().toPlainString());
        afterRechargeAcrossDTO.setMoney(callbackOrder.getMoney().stripTrailingZeros().toPlainString());
        afterRechargeAcrossDTO.setCoupon(callbackOrder.getCoupon().stripTrailingZeros().toPlainString());
        afterRechargeAcrossDTO.setRealAmount(callbackOrder.getRealAmount().stripTrailingZeros().toPlainString());
        afterRechargeAcrossDTO.setCoin(callbackOrder.getCoin().stripTrailingZeros().toPlainString());
        afterRechargeAcrossDTO.setExtend(parseExtendToJSONObject(callbackOrder.getExtend()));
        afterRechargeAcrossDTO.setTmallFirstRecharge(this.isTmallFirstRecharge(callbackOrder));
        afterRechargeAcrossDTO.setIsSandbox(String.valueOf(callbackOrder.getIsSandbox()));
        afterRechargeAcrossDTO.setConsumeOrderNum(StringUtils.defaultString(callbackOrder.getConsumeOrderNum()));
        setupRechargeRebateGrayFlag(callbackOrder, afterRechargeAcrossDTO);
        String nsqTopic = findNsqTopic(callbackOrder);
        postRechargeNsqProducer.sendPostRechargeMsg(nsqTopic, afterRechargeAcrossDTO);
        log.warn("处理充值后逻辑，处理完毕。order: {}", callbackOrder);
    }

    private String findNsqTopic(RechargeAcrossPO callbackOrder) {
        String nsqTopic = "fx.afterRechargeSuccess";
        if (CoinTypeEnum.isSingCoinType(callbackOrder.getCoinType())) {
            nsqTopic = "fx.afterRechargeSuccess.singCoin";
        }
        return nsqTopic;
    }

    public void pushSocketToRoom(RechargeAcrossPO callbackOrder) {
        try {
            int roomId = ExtendParseUtils.getCallBackArgRoomId(callbackOrder.getExtend());
            if (roomId < 1) {
                return;
            }
            String actionId = CoinTypeEnum.isSingCoinType(callbackOrder.getCoinType()) ?
                    "singRechargeCallBackInRoom" : "rechargeCallBackInRoom";
            long kugouId = callbackOrder.getKugouId();
            PayedNoticeVo payedNoticeVo = PayedNoticeVo.builder()
                    .actionId(actionId)
                    .data(PayedNoticeVo.NoticeData.builder()
                            .kugouId(kugouId)
                            .coin(callbackOrder.getCoin().stripTrailingZeros().toPlainString())
                            .build())
                    .build();
            String content = JsonUtils.toJSONString(payedNoticeVo);
            BoolResponse boolResponse = appDispatchServiceV2.sendToUser(roomId, kugouId, 306701,
                    ByteBuffer.wrap(content.getBytes()), buildMsgOption(callbackOrder.getRechargeId()));
            log.warn("处理充值后逻辑，发送Socket消息完毕。kugouId: {}, rechargeOrderNum: {}, roomId: {}, boolResponse: {}",
                    kugouId, callbackOrder.getRechargeOrderNum(), roomId, boolResponse);
        } catch (Exception e) {
            log.warn("处理充值后逻辑，发送Socket消息失败。kugouId: {}, rechargeOrderNum: {}",
                    callbackOrder.getKugouId(), callbackOrder.getRechargeOrderNum(), e);
        }
    }

    /**
     * 设置返点灰度标志
     *
     * @param callbackOrder          完成订单
     * @param afterRechargeAcrossDTO 充值后消息格式
     */
    public void setupRechargeRebateGrayFlag(RechargeAcrossPO callbackOrder, AfterRechargeAcrossDTO afterRechargeAcrossDTO) {
        JSONObject jsonObject = new JSONObject();
        String extraJsonData = StringUtils.defaultString(callbackOrder.getExtraJsonData());
        try {
            jsonObject = JSON.parseObject(extraJsonData);
        } catch (Exception e) {
            log.error("处理充值后逻辑，解析extraJsonData异常。extraJsonData: {}", extraJsonData, e);
            Cat.logError("处理充值后逻辑，解析extraJsonData异常。", e);
        }
        jsonObject = Objects.nonNull(jsonObject) ? jsonObject : new JSONObject();
        jsonObject.put("rechargeRebateGray", 1);
        afterRechargeAcrossDTO.setExtraJsonData(JSON.toJSONString(jsonObject));
    }

    public JSONObject parseExtendToJSONObject(String extend) {
        try {
            log.warn("处理充值后逻辑，解析extend数据。extend: {}", extend);
            return JSON.parseObject(StringUtils.defaultIfBlank(decodeExtend(extend), "{}"));
        } catch (Exception e) {
            log.error("处理充值后逻辑，解析extend异常。extend: {}", extend, e);
            Cat.logError("处理充值后逻辑，解析extend异常。", e);
            return new JSONObject();
        }
    }

    public boolean isTmallFirstRecharge(RechargeAcrossPO callbackOrder) {
        boolean isTmallFirstRecharge = false;
        try {
            if (callbackOrder.getPayTypeId() == PayTypeIdEnum.PAY_TYPE_ID_1012.getPayTypeId()) {
                int affected = this.firstRechargeDao.add(buildFirstRechargePO(callbackOrder));
                isTmallFirstRecharge = affected > 0;
            }
        } catch (Exception e) {
            log.error("判断是否天猫首充逻辑，发生异常。callbackOrder: {}", callbackOrder, e);
        }
        return isTmallFirstRecharge;
    }

    public FirstRechargePO buildFirstRechargePO(RechargeAcrossPO callbackOrder) {
        return new FirstRechargePO()
                .setGlobalId(String.valueOf(orderIdService.generateGlobalId()))
                .setKugouId(callbackOrder.getKugouId())
                .setRechargeOrderNum(callbackOrder.getRechargeOrderNum())
                .setCoin(callbackOrder.getCoin())
                .setAmount(callbackOrder.getAmount())
                .setPayTypeId(callbackOrder.getPayTypeId())
                .setAddTime(DateHelper.getCurrentSeconds());
    }

    public String decodeExtend(String encodedString) {
        byte[] decodedBytes = Base64.getDecoder().decode(StringUtils.defaultString(encodedString));
        return new String(decodedBytes, StandardCharsets.UTF_8);
    }

    public boolean sendPurchaseTopic(RechargeAcrossPO rechargeAcrossPO) {
        String extend = org.codehaus.plexus.util.StringUtils.defaultString(rechargeAcrossPO.getExtend());
        PurchaseMsgDTO purchaseMsgDTO = new PurchaseMsgDTO();
        purchaseMsgDTO.setOrderNo(rechargeAcrossPO.getRechargeOrderNum());
        purchaseMsgDTO.setOutTradeNo(org.codehaus.plexus.util.StringUtils.defaultString(rechargeAcrossPO.getConsumeOrderNum()));
        purchaseMsgDTO.setTradeStatus(String.valueOf(rechargeAcrossPO.getStatus()));
        purchaseMsgDTO.setAmount(rechargeAcrossPO.getMoney().stripTrailingZeros().toPlainString());
        purchaseMsgDTO.setCallbackTime(rechargeAcrossPO.getRechargeTime());
        purchaseMsgDTO.setBusinessId(ExtendParseUtils.getCallbackArgBusinessId(extend));
        purchaseMsgDTO.setBusinessTime(ExtendParseUtils.getCallbackArgBusinessTime(extend));
        purchaseMsgDTO.setReType(rechargeAcrossPO.getReType());
        purchaseMsgDTO.setPayTypeId(rechargeAcrossPO.getPayTypeId());
        purchaseMsgDTO.setPayTime(ExtendParseUtils.getCallbackArgPayTime(extend));
        purchaseMsgDTO.setFromKugouId(ExtendParseUtils.getCallbackArgFromKugouId(extend));
        purchaseMsgDTO.setToKugouId(ExtendParseUtils.getCallbackArgToKugouId(extend));
        purchaseMsgDTO.setTopic(ExtendParseUtils.getCallbackArgTopic(extend));
        purchaseMsgDTO.setRefer(rechargeAcrossPO.getRefer());
        purchaseMsgDTO.setCFrom(rechargeAcrossPO.getCFrom());
        purchaseMsgDTO.setChannelId(rechargeAcrossPO.getChannelId());
        purchaseMsgDTO.setVersion(ExtendParseUtils.getCallbackArgVersion(extend));
        purchaseMsgDTO.setCoin(rechargeAcrossPO.getCoin().stripTrailingZeros().toPlainString());
        String ext = ExtendParseUtils.getCallbackArgUserFundPlatParamExt(extend);
        if (org.codehaus.plexus.util.StringUtils.isNotEmpty(ext)) {
            purchaseMsgDTO.setExt(tryParseExt(ext));
        }
        return this.postRechargeNsqProducer.sendPostRechargeMsg(ExtendParseUtils.getCallbackArgTopic(extend), purchaseMsgDTO);
    }

    public boolean sendPurchaseTopic(String topic, RechargeAcrossPO targetOrder, CoinCallbackDTO coinCallbackDTO) {
        String extend = coinCallbackDTO.getExtend();
        PurchaseMsgDTO purchaseMsgDTO = new PurchaseMsgDTO();
        purchaseMsgDTO.setOrderNo(targetOrder.getRechargeOrderNum());
        purchaseMsgDTO.setOutTradeNo(coinCallbackDTO.getOut_trade_no());
        purchaseMsgDTO.setTradeStatus(String.valueOf(coinCallbackDTO.getTrade_status()));
        purchaseMsgDTO.setAmount(targetOrder.getMoney().stripTrailingZeros().toPlainString());
        purchaseMsgDTO.setCallbackTime(targetOrder.getRechargeTime());
        purchaseMsgDTO.setBusinessId(ExtendParseUtils.getCallbackArgBusinessId(extend));
        purchaseMsgDTO.setBusinessTime(ExtendParseUtils.getCallbackArgBusinessTime(extend));
        purchaseMsgDTO.setReType(targetOrder.getReType());
        purchaseMsgDTO.setPayTypeId(targetOrder.getPayTypeId());
        purchaseMsgDTO.setPayTime(ExtendParseUtils.getCallbackArgPayTime(extend));
        purchaseMsgDTO.setFromKugouId(ExtendParseUtils.getCallbackArgFromKugouId(extend));
        purchaseMsgDTO.setToKugouId(ExtendParseUtils.getCallbackArgToKugouId(extend));
        purchaseMsgDTO.setTopic(topic);
        purchaseMsgDTO.setRefer(targetOrder.getRefer());
        purchaseMsgDTO.setCFrom(targetOrder.getCFrom());
        purchaseMsgDTO.setChannelId(targetOrder.getChannelId());
        purchaseMsgDTO.setVersion(ExtendParseUtils.getCallbackArgVersion(extend));
        purchaseMsgDTO.setCoin(targetOrder.getCoin().stripTrailingZeros().toPlainString());
        HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
        String ext = request.getParameter("ext");
        if (StringUtils.isNotBlank(ext)) {
            purchaseMsgDTO.setExt(tryParseExt(ext));
        }
        return postRechargeNsqProducer.sendPostRechargeMsg(topic, purchaseMsgDTO);
    }

    public boolean sendPurchaseTopicForIos(String topic, RechargeAcrossPO targetOrder, PurchaseForIosRequest request) {
        return sendTopicForIos(topic, targetOrder, request.getExt(), request.getConsumeParam());
    }

    public boolean sendRenewalsTopicForIos(String topic, RechargeAcrossPO targetOrder, RenewalsForIosRequest request) {
        return sendTopicForIos(topic, targetOrder, request.getExt(), request.getConsumeParam());
    }

    private boolean sendTopicForIos(String topic, RechargeAcrossPO targetOrder, String ext, ConsumeParam consumeParam) {
        long consumeGlobalId = JsonUtils.parseJsonPathChecked(targetOrder.getExtraJsonData(), "$.businessId", Long.class);
        PurchaseMsgDTO purchaseMsgDTO = new PurchaseMsgDTO();
        purchaseMsgDTO.setOrderNo(targetOrder.getRechargeOrderNum());
        purchaseMsgDTO.setOutTradeNo("");
        purchaseMsgDTO.setTradeStatus(String.valueOf(targetOrder.getStatus()));
        purchaseMsgDTO.setAmount(targetOrder.getMoney().stripTrailingZeros().toPlainString());
        purchaseMsgDTO.setCallbackTime(targetOrder.getAddTime());
        purchaseMsgDTO.setBusinessId(String.valueOf(consumeGlobalId));
        purchaseMsgDTO.setBusinessTime(targetOrder.getAddTime());
        purchaseMsgDTO.setReType(targetOrder.getReType());
        purchaseMsgDTO.setPayTypeId(targetOrder.getPayTypeId());
        purchaseMsgDTO.setPayTime(targetOrder.getAddTime());
        purchaseMsgDTO.setFromKugouId(consumeParam.getFromKugouId());
        purchaseMsgDTO.setToKugouId(consumeParam.getToKugouId());
        purchaseMsgDTO.setTopic(topic);
        purchaseMsgDTO.setRefer(targetOrder.getRefer());
        purchaseMsgDTO.setCFrom(targetOrder.getCFrom());
        purchaseMsgDTO.setChannelId(targetOrder.getChannelId());
        purchaseMsgDTO.setVersion(RechargeCommonService.RECHARGE_VERSION_ONLINE);
        purchaseMsgDTO.setCoin(targetOrder.getCoin().stripTrailingZeros().toPlainString());
        purchaseMsgDTO.setExt(tryParseExt(ext));
        return postRechargeNsqProducer.sendPostRechargeMsg(topic, purchaseMsgDTO);
    }

    public JSONObject tryParseExt(String ext) {
        try {
            return JSON.parseObject(ext);
        } catch (Exception e) {
            log.error("充值购买物品回调，构建Topic异常。ext: {}", ext, e);
            Cat.logError("充值购买物品回调，构建Topic异常。", e);
        }
        return new JSONObject();
    }
}
