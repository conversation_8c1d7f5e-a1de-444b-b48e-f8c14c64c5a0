package com.kugou.fanxing.recharge.service.offline;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.kugou.fanxing.recharge.constant.ApplyTypeEnum;
import com.kugou.fanxing.recharge.constant.ApproveDetailStatusEnum;
import com.kugou.fanxing.recharge.constant.ApproveStatusEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.BigRechargeApplyDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.BigRechargeApplyDetailDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.BigRechargeOrderDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.model.po.offline.BigRechargeApply;
import com.kugou.fanxing.recharge.model.po.offline.BigRechargeApplyDetail;
import com.kugou.fanxing.recharge.model.po.offline.BigRechargeOrder;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.model.vo.offline.*;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.command.PlatformStrategyServiceCommand;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.util.IpUtils;
import com.kugou.fanxing.recharge.util.ModelUtils;
import com.kugou.fanxing.recharge.util.Pagination;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.Measure;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.PlatformStrategyService;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.StrategyResult;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.StrategyVO;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Future;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class BigRechargeService {

    @Autowired
    private OrderIdService orderIdService;
    @Autowired
    private UserFacadeService userFacadeService;
    @Autowired
    private PlatformStrategyService.Iface platformStrategyService;
    @Autowired
    private BigRechargeOrderDao bigRechargeOrderDao;
    @Autowired
    private BigRechargeApplyDao bigRechargeApplyDao;
    @Autowired
    private BigRechargeApplyDetailDao bigRechargeApplyDetailDao;
    @Autowired
    private RechargeAcrossDao rechargeAcrossDao;
    @Autowired
    private ApolloConfigService apolloConfigService;

    /**
     * 处理大额充值
     *
     * @param webCommonParam  通用参数
     * @param rechargeApplyVO 申请信息
     * @return 处理结果
     */
    @Transactional(transactionManager = "transactionManager_d_fanxing_recharge", propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public SysResultCode handleBigRecharge(WebCommonParam webCommonParam, ApplyAlipayVO rechargeApplyVO) {

        // 校验被充值酷狗账号与金额顺序
        List<Long> toKugouIdList = rechargeApplyVO.getToKugouIdList();
        List<BigDecimal> toUserAmountList = rechargeApplyVO.getToUserAmountList();
        if (toKugouIdList.isEmpty() || toKugouIdList.size() != toUserAmountList.size()) {
            log.warn("处理大额充值失败, 非法请求,(请检查参数个数), toKugouIdList: {}, toUserAmountList: {}", toKugouIdList, toUserAmountList);
            return SysResultCode.E_40000211;
        }

        // 校验填写的用户酷狗号是否重复
        Set<Long> toKugouIdSet = new HashSet<>(toKugouIdList);
        if (toKugouIdSet.size() != toKugouIdList.size()) {
            log.warn("填写的用户酷狗账号重复, toKugouIdList: {}, toKugouIdSet: {}", toKugouIdList, toKugouIdSet);
            return SysResultCode.E_40000215;
        }

        // 校验填写的用户酷狗号是否存在
        Optional<Boolean> optionalInvalid = toKugouIdSet.parallelStream()
                .map(kugouId -> userFacadeService.isValidKugouId(kugouId))
                .filter(isValidKugouId -> !isValidKugouId)
                .findAny();
        if (optionalInvalid.isPresent()) {
            log.warn("填写的用户酷狗ID不存在, toKugouIdSet: {}", toKugouIdSet);
            return SysResultCode.E_40000222;
        }

        // 校验支付宝订单号是否填写正确
        List<String> alipayOrderList = rechargeApplyVO.getAlipayOrderList();
        if (CollectionUtils.isEmpty(alipayOrderList)) {
            log.warn("处理大额充值失败, 支付宝订单号信息填写错误, webCommonParam: {}, rechargeApplyVO: {}", webCommonParam, rechargeApplyVO);
            return SysResultCode.E_40000213;
        }

        // 校验填写的支付宝单号是否重复
        Set<String> alipayOrderSet = new HashSet<>(alipayOrderList);
        if (alipayOrderSet.size() != alipayOrderList.size()) {
            log.warn("填写的支付宝单号重复, alipayOrderList: {}, alipayOrderSet: {}", alipayOrderList, alipayOrderSet);
            return SysResultCode.E_40000216;
        }

        // 校验支付宝订单号是否重复申请
        List<BigRechargeOrder> existsList = bigRechargeOrderDao.query(new HashSet<>(alipayOrderList), (short) 1);
        if (CollectionUtils.isNotEmpty(existsList)) {
            log.warn("处理大额充值失败, 支付宝订单号信息重复提交, webCommonParam: {}, rechargeApplyVO: {}", webCommonParam, rechargeApplyVO);
            return SysResultCode.E_40000214;
        }

        // 添加大额赠币充值申请汇总
        BigRechargeApply bigRechargeApply = buildBigRechargeApply(webCommonParam, rechargeApplyVO);
        int rows = this.bigRechargeApplyDao.addRecord(bigRechargeApply);
        if (rows < 1) {
            throw new ContextedRuntimeException("添加大额赠币充值申请汇总失败");
        }

        // 添加大额赠币充值申请明细
        List<BigRechargeApplyDetail> bigRechargeApplyDetailList = buildBigRechargeApplyDetails(bigRechargeApply, toKugouIdList, toUserAmountList);
        int affectedRows = this.bigRechargeApplyDao.addDetailRecord(bigRechargeApplyDetailList);
        if (affectedRows != bigRechargeApplyDetailList.size()) {
            throw new ContextedRuntimeException("添加大额赠币充值申请明细失败");
        }

        return SysResultCode.SUCCESS;
    }

    /**
     * 创建大额充值-线下充值流程申请
     *
     * @param webCommonParam  公用请求参数
     * @param rechargeApplyVO 大额充值申请
     * @return 充值申请明细
     */
    private BigRechargeApply buildBigRechargeApply(WebCommonParam webCommonParam, ApplyAlipayVO rechargeApplyVO) {
        BigRechargeApply bigRechargeApply = new BigRechargeApply();
        bigRechargeApply.setBatchNo(this.orderIdService.generateGlobalId());
        bigRechargeApply.setKugouId(webCommonParam.getKugouId());
        bigRechargeApply.setUserId(webCommonParam.getUserId());
        bigRechargeApply.setQq(rechargeApplyVO.getQq());
        bigRechargeApply.setPhone(rechargeApplyVO.getPhone());
        bigRechargeApply.setAlipayAccount(rechargeApplyVO.getAlipayAccount());
        bigRechargeApply.setOrderNums(rechargeApplyVO.getAlipayOrders());
        bigRechargeApply.setApplyType(ApplyTypeEnum.APPLY_TYPE_2.getStatus());
        bigRechargeApply.setApplyKugouId(webCommonParam.getKugouId());
        bigRechargeApply.setApplyUserId(webCommonParam.getUserId());
        bigRechargeApply.setApproveStatus(ApproveStatusEnum.APPROVE_STATUS_0.getStatus());
        bigRechargeApply.setJsonExtend("{}");
        bigRechargeApply.setImagePath(rechargeApplyVO.getImagePath());
        return bigRechargeApply;
    }

    /**
     * 创建大额充值-线下充值流程申请
     *
     * @param bigRechargeApply      大额充值申请
     * @param toUserAmountList      被充值人民币
     * @return 充值申请明细
     */
    private List<BigRechargeApplyDetail> buildBigRechargeApplyDetails(BigRechargeApply bigRechargeApply, List<Long> toKugouIdList, List<BigDecimal> toUserAmountList) {
        List<BigRechargeApplyDetail> bigRechargeApplyDetailList = Lists.newArrayList();
        Map<Long, String> nicknameMap = this.userFacadeService.getNicknameByKugouIds(toKugouIdList);
        for (int i = 0; i < toKugouIdList.size(); i++) {
            long toKugouId = toKugouIdList.get(i);
            String nickname = StringUtils.defaultIfBlank(nicknameMap.get(toKugouId), StringUtils.EMPTY);
            BigDecimal toUserAmount = toUserAmountList.get(i);
            BigDecimal toUserCoin = toUserAmount.multiply(BigDecimal.valueOf(100));
            BigRechargeApplyDetail bigRechargeApplyDetail = new BigRechargeApplyDetail();
            bigRechargeApplyDetail.setId(this.orderIdService.generateGlobalId());
            bigRechargeApplyDetail.setBatchNo(bigRechargeApply.getBatchNo());
            bigRechargeApplyDetail.setNewRechargeOrderNum(this.orderIdService.generateRechargeOrderNumForAcross());
            bigRechargeApplyDetail.setStatus(ApproveDetailStatusEnum.APPROVE_STATUS_0.getStatus());
            bigRechargeApplyDetail.setToUserId(0);
            bigRechargeApplyDetail.setToKugouId(toKugouId);
            bigRechargeApplyDetail.setToUserName(nickname);
            bigRechargeApplyDetail.setToUserAmount(toUserAmount);
            bigRechargeApplyDetail.setToUserCoin(toUserCoin);
            bigRechargeApplyDetailList.add(bigRechargeApplyDetail);
        }
        return bigRechargeApplyDetailList;
    }

    /**
     * 获取大额返点申请列表
     *
     * @param applyKugouId 申请用户
     * @return
     */
    public List<ApplyInfoDetailVO> getApplyListSorted(long applyKugouId) {
        List<BigRechargeApply> bigRechargeApplyList = this.bigRechargeApplyDao.getApplyListByKugouId(ApplyTypeEnum.APPLY_TYPE_1.getStatus(), applyKugouId);
        List<ApplyInfoDetailVO> details = Lists.newArrayList();
        bigRechargeApplyList.forEach(bigRechargeApply -> {
            ApplyInfoDetailVO applyInfoDetailVO = new ApplyInfoDetailVO();
            applyInfoDetailVO.setApplyType(bigRechargeApply.getApplyType());
            applyInfoDetailVO.setBatchNo(bigRechargeApply.getBatchNo());
            applyInfoDetailVO.setToKugouId(bigRechargeApply.getKugouId());
            applyInfoDetailVO.setToUserId(bigRechargeApply.getUserId());
            applyInfoDetailVO.setStatus(bigRechargeApply.getApproveStatus());
            applyInfoDetailVO.setStatusLabel(ApproveStatusEnum.labelOf(bigRechargeApply.getApproveStatus()));
            applyInfoDetailVO.setCreateTime(bigRechargeApply.getCreateTime());
            applyInfoDetailVO.setNewRechargeOrderNum(bigRechargeApply.getOrderNums());
            if (this.apolloConfigService.isKuwoEnv()) {
                applyInfoDetailVO.setToKuwoId(userFacadeService.getKuwoIdByKugouId(bigRechargeApply.getKugouId()).orElse(0L));
            }
            details.add(applyInfoDetailVO);
        });
        return details.stream()
                .sorted((o1, o2) -> Math.toIntExact(o2.getCreateTime().getTime() - o1.getCreateTime().getTime()))
                .collect(Collectors.toList());
    }

    /**
     * 获取大额充值-线下充值流程申请列表
     *
     * @param applyKugouId 申请用户
     * @param applyType    申请类型
     * @return
     */
    public List<ApplyInfoDetailVO> getApplyDetailListSorted(long applyKugouId, int applyType) {
        List<BigRechargeApply> bigRechargeApplyList = this.bigRechargeApplyDao.getApplyListByKugouId(applyType, applyKugouId);
        return getFlatApplyDetailListSorted(bigRechargeApplyList);
    }

    private List<ApplyInfoDetailVO> getFlatApplyDetailListSorted(List<BigRechargeApply> bigRechargeApplyList) {
        List<ApplyInfoVO> applyInfoVOList = ModelUtils.fromList(bigRechargeApplyList, ApplyInfoVO.class);
        Map<Long, BigRechargeApply> bigRechargeApplyMap = bigRechargeApplyList.stream()
                .collect(Collectors.toMap(BigRechargeApply::getBatchNo, item -> item));

        Set<Long> batchNoSet = applyInfoVOList.stream().map(ApplyInfoVO::getBatchNo).collect(Collectors.toSet());
        List<BigRechargeApplyDetail> bigRechargeApplyDetailList = Lists.newArrayList();
        if (!batchNoSet.isEmpty()) {
            bigRechargeApplyDetailList = this.bigRechargeApplyDetailDao.getApplyDetailListByBatchNo(batchNoSet);
        }

        List<ApplyInfoDetailVO> applyInfoDetailVOList = Lists.newArrayList();
        bigRechargeApplyDetailList.forEach(bigRechargeApplyDetail -> {
            long batchNo = bigRechargeApplyDetail.getBatchNo();
            if (bigRechargeApplyMap.containsKey(batchNo)) {
                BigRechargeApply bigRechargeApply = bigRechargeApplyMap.get(batchNo);
                ApplyInfoDetailVO applyInfoDetailVO = new ApplyInfoDetailVO()
                        .setApplyType(bigRechargeApply.getApplyType())
                        .setToUserId(bigRechargeApplyDetail.getToUserId())
                        .setToKugouId(bigRechargeApplyDetail.getToKugouId())
                        .setToUserName(bigRechargeApplyDetail.getToUserName())
                        .setToUserAmount(bigRechargeApplyDetail.getToUserAmount())
                        .setToUserCoin(bigRechargeApplyDetail.getToUserCoin())
                        .setNewRechargeOrderNum(bigRechargeApplyDetail.getNewRechargeOrderNum())
                        .setStatus(bigRechargeApplyDetail.getStatus())
                        .setStatusLabel(ApproveDetailStatusEnum.labelOf(bigRechargeApplyDetail.getStatus()))
                        .setCreateTime(bigRechargeApply.getCreateTime())
                        .setAlipayAccount(bigRechargeApply.getAlipayAccount())
                        .setOrderNums(bigRechargeApply.getOrderNums())
                        .setBatchNo(bigRechargeApply.getBatchNo())
                        .setApproveStatus(bigRechargeApply.getApproveStatus())
                        .setApproveStatusLabel(ApproveStatusEnum.labelOf(bigRechargeApply.getApproveStatus()))
                        .setImagePath(bigRechargeApply.getImagePath())
                        .setApproveRemark(bigRechargeApply.getApproveRemark());
                if (this.apolloConfigService.isKuwoEnv()) {
                    applyInfoDetailVO.setToKuwoId(userFacadeService.getKuwoIdByKugouId(bigRechargeApplyDetail.getToKugouId()).orElse(0L));
                }
                applyInfoDetailVOList.add(applyInfoDetailVO);
            }
        });
        return applyInfoDetailVOList.stream()
                .sorted(Comparator.comparing(ApplyInfoDetailVO::getCreateTime).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 140后台-大额充值申请审核
     *
     * @param bigRechargeApply  申请汇总信息
     * @param approveStatusEnum 申请审核类型
     * @param remark
     * @return
     */
    @Transactional(transactionManager = "transactionManager_d_fanxing_recharge", propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public SysResultCode approveApply(BigRechargeApply bigRechargeApply, ApproveStatusEnum approveStatusEnum, String remark, String bankTransactionId) {
        // 针对审核通过请求的处理
        if (ApproveStatusEnum.APPROVE_STATUS_1.equals(approveStatusEnum)) {
            if (bigRechargeApply.getApplyType() == ApplyTypeEnum.APPLY_TYPE_2.getStatus()) {
                // 处理支付宝交易号
                String alipayOrders = bigRechargeApply.getOrderNums();
                List<String> alipayOrderList = Lists.newArrayList();
                if (StringUtils.isNotBlank(alipayOrders)) {
                    String[] parts = StringUtils.split(alipayOrders, ",");
                    alipayOrderList = Stream.of(parts).map(String::trim).collect(Collectors.toList());
                }
                // 校验支付宝订单号是否重复申请
                List<BigRechargeOrder> existsList = bigRechargeOrderDao.query(new HashSet<>(alipayOrderList), (short) 1);
                if (CollectionUtils.isNotEmpty(existsList)) {
                    return SysResultCode.E_40000214;
                }
                // 记录审核通过的大额充值支付宝单号
                List<BigRechargeOrder> bigRechargeOrderList = alipayOrderList.stream()
                        .map(alipayOrder -> new BigRechargeOrder()
                                .setGlobalId(this.orderIdService.generateGlobalId())
                                .setOrderNum(alipayOrder)
                                .setApplyKugouId(bigRechargeApply.getApplyKugouId())
                                .setApplyUserId(bigRechargeApply.getApplyUserId())
                                .setType((short) 1))
                        .collect(Collectors.toList());
                int rows = this.bigRechargeOrderDao.saveRecords(bigRechargeOrderList);
                if (rows != bigRechargeOrderList.size()) {
                    throw new ContextedRuntimeException("保存支付宝交易号失败");
                }
            } else if (bigRechargeApply.getApplyType() == ApplyTypeEnum.APPLY_TYPE_3.getStatus()) {
                // 检查银行卡转账交易号是否正确
                if (StringUtils.isBlank(bankTransactionId)) {
                    return SysResultCode.E_40000221;
                }
                // 校验银行卡订单号是否重复申请
                List<String> bankOrderList = Lists.newArrayList(bankTransactionId);
                List<BigRechargeOrder> existsList = bigRechargeOrderDao.query(new HashSet<>(bankOrderList), (short) 3);
                if (CollectionUtils.isNotEmpty(existsList)) {
                    return SysResultCode.E_40000220;
                }
                // 记录审核通过的大额充值银行卡
                List<BigRechargeOrder> bigRechargeOrderList = bankOrderList.stream()
                        .map(bankOrder -> new BigRechargeOrder()
                                .setGlobalId(this.orderIdService.generateGlobalId())
                                .setOrderNum(bankOrder)
                                .setApplyKugouId(bigRechargeApply.getApplyKugouId())
                                .setApplyUserId(bigRechargeApply.getApplyUserId())
                                .setType((short) 3))
                        .collect(Collectors.toList());
                int rows = this.bigRechargeOrderDao.saveRecords(bigRechargeOrderList);
                if (rows != bigRechargeOrderList.size()) {
                    throw new ContextedRuntimeException("保存银行卡交易号失败");
                }
            }
        }
        long batchNo = bigRechargeApply.getBatchNo();
        Set<Long> batchNoSet = Sets.newHashSet(batchNo);
        List<BigRechargeApplyDetail> bigRechargeApplyDetails = this.bigRechargeApplyDetailDao.getApplyDetailListByBatchNo(batchNoSet);
        int row1 = this.bigRechargeApplyDao.updateRecord(batchNo, approveStatusEnum.getStatus(), StringUtils.defaultString(remark));
        int row2 = this.bigRechargeApplyDetailDao.updateRecord(batchNo, approveStatusEnum.getStatus());
        if (row1 < 1 || row2 < 1 || row2 != bigRechargeApplyDetails.size()) {
            throw new ContextedRuntimeException("更新订单状态失败")
                    .setContextValue("batchNo", batchNo)
                    .setContextValue("row1", row1)
                    .setContextValue("row2", row2)
                    .setContextValue("size", bigRechargeApplyDetails.size());
        }
        return SysResultCode.SUCCESS;
    }

    /**
     * 140后台-大额充值申请列表
     *
     * @param batchNo          申请编号
     * @param rechargeOrderNum 充值单号
     * @param pagination       分页信息
     * @return
     */
    public List<ApplyInfoDetailVO> getApplyDetailListSortedByPage(ApplyTypeEnum applyTypeEnum, Long batchNo, String rechargeOrderNum, Pagination pagination) {
        List<ApplyInfoDetail> bigRechargeApplyList = this.bigRechargeApplyDao.getApplyDetailList(applyTypeEnum.getStatus(), batchNo, rechargeOrderNum, pagination);
        return ModelUtils.fromList(bigRechargeApplyList, ApplyInfoDetailVO.class);
    }

    public int getApplyDetailListCount(ApplyTypeEnum applyType, long batchNo, String rechargeOrderNum) {
        return this.bigRechargeApplyDetailDao.getApplyDetailListCount(applyType.getStatus(), batchNo, rechargeOrderNum);
    }

    public Optional<BigRechargeApply> getRechargeApplyByBatchNo(long batchNo) {
        return Optional.ofNullable(this.bigRechargeApplyDao.getRechargeApplyByBatchNo(batchNo));
    }

    /**
     * 提醒财务处理对公转账申请
     */
    public long getNeedHandleApprovalNum() {
        return this.bigRechargeApplyDao.getApplyListByStatus(ApproveStatusEnum.APPROVE_STATUS_0);
    }

    public boolean strategyVerify(BigRechargeApply bigRechargeApply, ApproveStatusEnum approveStatusEnum, String remark) {
        try {
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("bigRechargeApply", bigRechargeApply);
            dataMap.put("approveStatusEnum", approveStatusEnum);
            dataMap.put("remark", remark);
            StrategyVO strategyVO = new StrategyVO()
                    .setAppid("0")
                    .setBiz("BigRecharge")
                    .setDeviceId("")
                    .setEndtype(Strings.EMPTY)
                    .setTs(System.currentTimeMillis())
                    .setData(JSON.toJSONString(dataMap))
                    .setKugouId(bigRechargeApply.getApplyKugouId())
                    .setIp(IpUtils.getClientIpAddress())
                    .setSid("0");
            PlatformStrategyServiceCommand command = new PlatformStrategyServiceCommand(platformStrategyService, strategyVO);
            Future<StrategyResult> strategyResultFuture = command.observe().toBlocking().toFuture();
            StrategyResult strategyResult = strategyResultFuture.get();
            log.warn("big recharge strategy verify -> strategyVO:{} result:{}", strategyVO, strategyResult);
            if (strategyResult.getRecode() == 0 && strategyResult.getData() != null && strategyResult.getData().getMeasure() == Measure.FREEZE) {
                return true;
            }
        } catch (Exception e) {
            log.error("大额充值调用风控服务失败", e);
        }
        return false;
    }

    @Transactional(transactionManager = "transactionManager_d_fanxing_recharge", propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public SysResultCode applyBank(WebCommonParam webCommonParam, ApplyBankVO applyBankVO) {
        // 校验被充值酷狗账号与金额顺序
        List<Long> toKugouIdList = applyBankVO.getToKugouIdList();
        List<BigDecimal> toUserAmountList = applyBankVO.getToUserAmountList();
        if (toKugouIdList.isEmpty() || toKugouIdList.size() != toUserAmountList.size()) {
            log.warn("处理大额充值失败, 非法请求,(请检查参数个数), toKugouIdList: {}, toUserAmountList: {}", toKugouIdList, toUserAmountList);
            return SysResultCode.E_40000211;
        }
        // 校验填写的用户酷狗号是否重复
        Set<Long> toKugouIdSet = new HashSet<>(toKugouIdList);
        if (toKugouIdSet.size() != toKugouIdList.size()) {
            log.warn("填写的用户酷狗ID重复, toKugouIdList: {}, toKugouIdSet: {}", toKugouIdList, toKugouIdSet);
            return SysResultCode.E_40000219;
        }
        // 校验填写的用户酷狗号是否存在
        Optional<Boolean> optionalInvalid = toKugouIdSet.parallelStream()
                .map(kugouId -> userFacadeService.isValidKugouId(kugouId))
                .filter(isValidKugouId -> !isValidKugouId)
                .findAny();
        if (optionalInvalid.isPresent()) {
            log.warn("填写的用户酷狗ID不存在, toKugouIdSet: {}", toKugouIdSet);
            return SysResultCode.E_40000222;
        }
        // 添加大额赠币充值申请汇总
        BigRechargeApply bigRechargeApply = buildBigRechargeApply2(webCommonParam, applyBankVO);
        int rows = this.bigRechargeApplyDao.addRecord(bigRechargeApply);
        if (rows < 1) {
            throw new ContextedRuntimeException("添加大额赠币充值申请汇总失败");
        }
        // 添加大额赠币充值申请明细
        List<BigRechargeApplyDetail> bigRechargeApplyDetailList = buildBigRechargeApplyDetails2(bigRechargeApply, toKugouIdList, toUserAmountList);
        int affectedRows = this.bigRechargeApplyDao.addDetailRecord(bigRechargeApplyDetailList);
        if (affectedRows != bigRechargeApplyDetailList.size()) {
            throw new ContextedRuntimeException("添加大额赠币充值申请明细失败");
        }
        return SysResultCode.SUCCESS;
    }

    private List<BigRechargeApplyDetail> buildBigRechargeApplyDetails2(BigRechargeApply bigRechargeApply, List<Long> toKugouIdList, List<BigDecimal> toUserAmountList) {
        List<BigRechargeApplyDetail> bigRechargeApplyDetailList = Lists.newArrayList();
        Map<Long, String> nicknameMap = this.userFacadeService.getNicknameByKugouIds(toKugouIdList);
        for (int i = 0; i < toKugouIdList.size(); i++) {
            long toKugouId = toKugouIdList.get(i);
            BigDecimal toUserAmount = toUserAmountList.get(i);
            BigDecimal toUserCoin = toUserAmount.multiply(BigDecimal.valueOf(100));
            BigRechargeApplyDetail bigRechargeApplyDetail = new BigRechargeApplyDetail();
            bigRechargeApplyDetail.setId(this.orderIdService.generateGlobalId());
            bigRechargeApplyDetail.setBatchNo(bigRechargeApply.getBatchNo());
            bigRechargeApplyDetail.setNewRechargeOrderNum(this.orderIdService.generateRechargeOrderNumForAcross());
            bigRechargeApplyDetail.setStatus(ApproveDetailStatusEnum.APPROVE_STATUS_0.getStatus());
            bigRechargeApplyDetail.setToUserId(0);
            bigRechargeApplyDetail.setToKugouId(toKugouId);
            bigRechargeApplyDetail.setToUserName(StringUtils.defaultString(nicknameMap.get(toKugouId)));
            bigRechargeApplyDetail.setToUserAmount(toUserAmount);
            bigRechargeApplyDetail.setToUserCoin(toUserCoin);
            bigRechargeApplyDetailList.add(bigRechargeApplyDetail);
        }
        return bigRechargeApplyDetailList;
    }

    private BigRechargeApply buildBigRechargeApply2(WebCommonParam webCommonParam, ApplyBankVO applyBankVO) {
        BigRechargeApply bigRechargeApply = new BigRechargeApply();
        bigRechargeApply.setBatchNo(this.orderIdService.generateGlobalId());
        bigRechargeApply.setApplyType(ApplyTypeEnum.APPLY_TYPE_3.getStatus());
        bigRechargeApply.setKugouId(webCommonParam.getKugouId());
        bigRechargeApply.setUserId(webCommonParam.getUserId());
        bigRechargeApply.setQq("");
        bigRechargeApply.setPhone(applyBankVO.getPhone());
        bigRechargeApply.setAlipayAccount("");
        bigRechargeApply.setOrderNums("");
        bigRechargeApply.setApplyKugouId(webCommonParam.getKugouId());
        bigRechargeApply.setApplyUserId(webCommonParam.getUserId());
        bigRechargeApply.setApproveStatus(ApproveStatusEnum.APPROVE_STATUS_0.getStatus());
        Map<String, String> dataMap = ImmutableMap.<String, String>builder()
                .put("principalName", applyBankVO.getPrincipalName())
                .put("principalNo", applyBankVO.getPrincipalNo())
                .put("bankName", applyBankVO.getBankName())
                .put("bankAccount", applyBankVO.getBankAccount())
                .put("bankCardNo", applyBankVO.getBankCardNo())
                .build();
        bigRechargeApply.setJsonExtend(JSON.toJSONString(dataMap));
        bigRechargeApply.setImagePath(applyBankVO.getImagePath());
        return bigRechargeApply;
    }

    public int adjustTradeTime(String rechargeOrderNum, int maxTradeTime) {
        String month = this.orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
        return this.rechargeAcrossDao.updateRechargeOrderTradeTime(month, rechargeOrderNum, maxTradeTime);
    }
}
