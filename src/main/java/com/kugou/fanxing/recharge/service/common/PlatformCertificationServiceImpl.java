package com.kugou.fanxing.recharge.service.common;

import com.kugou.fanxing.recharge.model.dto.CertificationDTO;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.thrift.certification.CertificationQueryRequest;
import com.kugou.fanxing.thrift.certification.CertificationQueryResult;
import com.kugou.fanxing.thrift.certification.CertificationVo;
import com.kugou.fanxing.thrift.certification.PlatformCertificationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.apache.thrift.TFieldIdEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 实名认证服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PlatformCertificationServiceImpl {

    @Value("${platform.certification.appCode:platformMoneyExchangeService}")
    private String appCode;

    @Value("${platform.certification.appSecret:uzFwWuhWFmnwUEfa}")
    private String appSecret;

    @Autowired
    private PlatformCertificationService.Iface platformCertificationService;

    /**
     * 是否实名认证通过
     */
    public boolean hasCertified(long kugouId) {
        boolean hasCertified = false;
        Optional<CertificationDTO> optionalCertificationDTO = this.getRealNameStatus(kugouId);
        if (optionalCertificationDTO.isPresent() && optionalCertificationDTO.get().getStatus() == 2) {
            hasCertified = true;
        }
        log.warn("调用实名认证服务，获取实名认证信息失败。kugouId: {}, hasCertified: {}, optionalCertificationDTO: {}", kugouId, hasCertified, optionalCertificationDTO);
        return hasCertified;
    }

    public Optional<CertificationDTO> getRealNameStatus(long kugouId) {
        try {
            // 构建请求参数
            CertificationQueryRequest request = new CertificationQueryRequest()
                    .setAppCode(appCode)
                    .setKugouId(kugouId)
                    .setTimestamp(DateHelper.getCurrentSeconds());
            request.setSign(sign(CertificationQueryRequest._Fields.values(), CertificationQueryRequest.class, request, appSecret));
            log.warn("调用实名认证服务, 请求参数, kugouId: {}, request: {}", kugouId, request);

            // 检查响应报文
            CertificationQueryResult result = this.platformCertificationService.findCertificationByKugouId(request);
            if (Objects.isNull(result) || result.getRet() != 0 || Objects.isNull(result.getData())) {
                log.warn("调用实名认证服务, 调用失败, kugouId: {}, request: {}, result: {}", kugouId, request, result);
                return Optional.empty();
            }

            // 处理响应报文
            CertificationVo certificationVo = result.getData();
            CertificationDTO certificationDTO = new CertificationDTO()
                    .setKugouId(certificationVo.getKugouId())
                    .setName(StringUtils.defaultString(certificationVo.getName()))
                    .setStatus(certificationVo.getStatus())
                    .setFailReason(StringUtils.defaultString(certificationVo.getFailReason()))
                    .setCheckTime(certificationVo.getCheckTime())
                    .setCheckType(certificationVo.getCheckType())
                    .setCreateTime(certificationVo.getCreateTime())
                    .setEncryptCardNo(StringUtils.defaultString(certificationVo.getEncryptCardNo()))
                    .setCertificationFaceImage(StringUtils.defaultString(certificationVo.getCertificationFaceImage()));
            log.warn("调用实名认证服务, 调用成功, result: {}, certification: {}", result, certificationDTO);
            return Optional.of(certificationDTO);
        } catch (TException e) {
            log.error("调用实名认证服务, 调用异常, kugouId: {}", kugouId, e);
            return Optional.empty();
        }
    }

    public static String sign(TFieldIdEnum[] fields, Class<?> clazz, Object object, String secret) {
        Arrays.sort(fields, Comparator.comparing(TFieldIdEnum::getFieldName));
        StringBuilder source = new StringBuilder();
        try {
            for (TFieldIdEnum f : fields) {
                Field field = clazz.getField(f.getFieldName());
                Object obj = field.get(object);
                if (obj != null) {
                    if (obj instanceof List) {
                        // List加密有空,去除
                        source.append(obj.toString().replace(" ", ""));
                    } else {
                        source.append(obj);
                    }
                }
            }
        } catch (NoSuchFieldException | IllegalArgumentException | IllegalAccessException e) {
            log.error("调用实名认证服务, 加密失败", e);
        }
        source.append(secret);
        return DigestUtils.md5Hex(source.toString());
    }
}
