package com.kugou.fanxing.recharge.service.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Sets;
import com.kugou.cache.rediscluster.RedisClusterInterface;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Tuple;

import java.util.Set;

/**
 * 缓存(必须允许降级）
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CacheService {

    @Autowired
    private RedisClusterInterface redisClusterInterface;

    public String get(String s) {
        return new RedisGetCommand(redisClusterInterface, s).execute();
    }

    public String set(String key, String val) {
        return new RedisSetCommand(redisClusterInterface, key, val).execute();
    }

    public Long del(String s) {
        return new RedisDelCommand(redisClusterInterface, s).execute();
    }

    public Set<Tuple> zrangeWithScores(String key, long start, long end) {
        return new RedisZrangeWithScoresCommand(redisClusterInterface, key, start, end).execute();
    }

    public Double zincrby(String key, int score, String member) {
        return new RedisZincrbyCommand(redisClusterInterface, key, score, member).execute();
    }

    public boolean sismember(String key, String member) {
        return new RedisSismemberCommand(redisClusterInterface, key, member).execute();
    }

    public String setex(String key, int seconds, String value) {
        return new RedisSetexCommand(redisClusterInterface, key, seconds, value).execute();
    }

    public String set(String key, int expireTime, Object obj) {
        return this.setex(key, expireTime, JSON.toJSONString(obj));
    }

    public <T> T get(String key, TypeReference<T> type) {
        return JSON.parseObject(this.get(key), type);
    }

    public <T> T get(String key, Class<T> clazz) {
        return JSON.parseObject(this.get(key), clazz);
    }

    @Slf4j
    private static class RedisSetexCommand extends HystrixCommand<String> {

        private final RedisClusterInterface redisClusterInterface;
        private final String key;
        private final int seconds;
        private final String value;

        public RedisSetexCommand(final RedisClusterInterface redisClusterInterface, final String key, final int seconds, final String value) {
            super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("RedisSetexCommand"))
                    .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                            .withMaxQueueSize(200)
                            .withQueueSizeRejectionThreshold(15)
                            .withCoreSize(10))
                    .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                            .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                            .withExecutionTimeoutInMilliseconds(250)));
            this.redisClusterInterface = redisClusterInterface;
            this.key = key;
            this.seconds = seconds;
            this.value = value;
        }

        @Override
        protected String run() throws Exception {
            return redisClusterInterface.setex(key, seconds, value);
        }

        @Override
        protected String getFallback() {
            String fallback = "";
            log.warn("RedisSetexCommand服务降级! key: {}, seconds: {}, value: {}, fallback: {}, 降级原因: {}",
                    key, seconds, value, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
            return fallback;
        }
    }

    @Slf4j
    private static class RedisSismemberCommand extends HystrixCommand<Boolean> {

        private final RedisClusterInterface redisClusterInterface;
        private final String key;
        private final String member;

        public RedisSismemberCommand(final RedisClusterInterface redisClusterInterface, final String key, final String member) {
            super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("RedisSismemberCommand"))
                    .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                            .withMaxQueueSize(200)
                            .withQueueSizeRejectionThreshold(15)
                            .withCoreSize(10))
                    .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                            .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                            .withExecutionTimeoutInMilliseconds(250)));
            this.redisClusterInterface = redisClusterInterface;
            this.key = key;
            this.member = member;
        }

        @Override
        protected Boolean run() throws Exception {
            return redisClusterInterface.sismember(key, member);
        }

        @Override
        protected Boolean getFallback() {
            Boolean fallback = Boolean.FALSE;
            log.warn("RedisSismemberCommand服务降级! key: {}, fallback: {}, 降级原因: {}",
                    key, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
            return fallback;
        }
    }

    @Slf4j
    private static class RedisZrangeWithScoresCommand extends HystrixCommand<Set<Tuple>> {

        private final RedisClusterInterface redisClusterInterface;
        private final String key;
        private final long start;
        private final long end;

        public RedisZrangeWithScoresCommand(final RedisClusterInterface redisClusterInterface, final String key, final long start, final long end) {
            super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("RedisZrangeWithScoresCommand"))
                    .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                            .withMaxQueueSize(200)
                            .withQueueSizeRejectionThreshold(15)
                            .withCoreSize(10))
                    .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                            .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                            .withExecutionTimeoutInMilliseconds(250)));
            this.redisClusterInterface = redisClusterInterface;
            this.key = key;
            this.start = start;
            this.end = end;
        }

        @Override
        protected Set<Tuple> run() throws Exception {
            return redisClusterInterface.zrangeWithScores(key, start, end);
        }

        @Override
        protected Set<Tuple> getFallback() {
            Set<Tuple> fallback = Sets.newHashSet();
            log.warn("RedisZrangeWithScoresCommand服务降级! key: {}, fallback: {}, 降级原因: {}",
                    key, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
            return fallback;
        }
    }

    @Slf4j
    private static class RedisZincrbyCommand extends HystrixCommand<Double> {

        private final RedisClusterInterface redisClusterInterface;
        private final String key;
        private final int score;
        private final String member;

        public RedisZincrbyCommand(final RedisClusterInterface redisClusterInterface, final String key, final int score, final String member) {
            super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("RedisZincrbyCommand"))
                    .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                            .withMaxQueueSize(200)
                            .withQueueSizeRejectionThreshold(15)
                            .withCoreSize(10))
                    .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                            .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                            .withExecutionTimeoutInMilliseconds(250)));
            this.redisClusterInterface = redisClusterInterface;
            this.key = key;
            this.score = score;
            this.member = member;
        }

        @Override
        protected Double run() throws Exception {
            return redisClusterInterface.zincrby(key, score, member);
        }

        @Override
        protected Double getFallback() {
            Double fallback = 0d;
            log.warn("RedisZincrbyCommand服务降级! key: {}, fallback: {}, 降级原因: {}",
                    key, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
            return fallback;
        }
    }

    @Slf4j
    private static class RedisDelCommand extends HystrixCommand<Long> {

        private final RedisClusterInterface redisClusterInterface;
        private final String key;

        public RedisDelCommand(final RedisClusterInterface redisClusterInterface, final String key) {
            super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("RedisDelCommand"))
                    .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                            .withMaxQueueSize(200)
                            .withQueueSizeRejectionThreshold(15)
                            .withCoreSize(10))
                    .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                            .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                            .withExecutionTimeoutInMilliseconds(250)));
            this.redisClusterInterface = redisClusterInterface;
            this.key = key;
        }

        @Override
        protected Long run() throws Exception {
            return redisClusterInterface.del(key);
        }

        @Override
        protected Long getFallback() {
            Long fallback = 0L;
            log.warn("RedisDelCommand服务降级! key: {}, fallback: {}, 降级原因: {}",
                    key, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
            return fallback;
        }
    }

    @Slf4j
    private static class RedisGetCommand extends HystrixCommand<String> {

        private final RedisClusterInterface redisClusterInterface;
        private final String key;

        public RedisGetCommand(final RedisClusterInterface redisClusterInterface, final String key) {
            super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("RedisGetCommand"))
                    .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                            .withMaxQueueSize(200)
                            .withQueueSizeRejectionThreshold(15)
                            .withCoreSize(10))
                    .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                            .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                            .withExecutionTimeoutInMilliseconds(250)));
            this.redisClusterInterface = redisClusterInterface;
            this.key = key;
        }

        @Override
        protected String run() throws Exception {
            return redisClusterInterface.get(key);
        }

        @Override
        protected String getFallback() {
            String fallback = "";
            log.warn("RedisGetCommand服务降级! key: {}, fallback: {}, 降级原因: {}",
                    key, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
            return fallback;
        }
    }

    @Slf4j
    private static class RedisSetCommand extends HystrixCommand<String> {

        private final RedisClusterInterface redisClusterInterface;
        private final String key;
        private final String value;

        public RedisSetCommand(final RedisClusterInterface redisClusterInterface, final String key, final String value) {
            super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("RedisSetCommand"))
                    .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                            .withMaxQueueSize(200)
                            .withQueueSizeRejectionThreshold(15)
                            .withCoreSize(10))
                    .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                            .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                            .withExecutionTimeoutInMilliseconds(250)));
            this.redisClusterInterface = redisClusterInterface;
            this.key = key;
            this.value = value;
        }

        @Override
        protected String run() throws Exception {
            return redisClusterInterface.set(key, value);
        }

        @Override
        protected String getFallback() {
            String fallback = "";
            log.warn("RedisSetCommand服务降级! key: {}, value: {}, fallback: {}, 降级原因: {}",
                    key, value, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
            return fallback;
        }
    }
}


