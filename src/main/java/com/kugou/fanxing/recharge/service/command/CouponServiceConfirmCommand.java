package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.coupon.thrift.CouponService;
import com.kugou.fanxing.coupon.thrift.OperResult;
import com.kugou.fanxing.coupon.thrift.ReturnResult;
import com.kugou.fanxing.coupon.thrift.UnFreezeVO;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 使用充值代金券
 * 0 - 使用失败
 * 1 - 使用成功
 * 2 - 已被解冻
 * <AUTHOR>
 */
@Slf4j
public class CouponServiceConfirmCommand extends HystrixCommand<Integer> {

    private final CouponService.Iface couponService;
    private final UnFreezeVO unFreezeVO;
    private final List<Integer> failureResultCodeList;

    public CouponServiceConfirmCommand(final CouponService.Iface couponService, final UnFreezeVO unFreezeVO, final List<Integer> failureResultCodeList) {
        super(HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("CouponServiceConfirmCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(500)));
        this.couponService = couponService;
        this.unFreezeVO = unFreezeVO;
        this.failureResultCodeList = failureResultCodeList;
    }

    @Override
    protected Integer run() throws Exception {
        log.warn("调用确认充值代金券接口，请求参数。unFreezeVO: {}, failureResultCodeList: {}", unFreezeVO, failureResultCodeList);
        ReturnResult returnResult = couponService.confirm(unFreezeVO);
        if (Objects.isNull(returnResult) || returnResult.getCode() != 0 || Objects.isNull(returnResult.getData())) {
            log.warn("调用确认充值代金券接口，调用失败。unFreezeVO: {}, returnResult: {}", unFreezeVO, returnResult);
            return 0;
        }
        OperResult operResult = returnResult.getData();
        int result = 0;
        // 确认代金券成功
        if (Arrays.asList(0, 8).contains(operResult.getResult())) {
            result = 1;
        }
        // 确认代金券失败
        if (failureResultCodeList.contains(operResult.getResult())) {
            result = 2;
        }
        // 确认代金券重试
        log.warn("调用确认充值代金券接口，调用完成。unFreezeVO: {}, failureResultCodeList: {}, returnResult: {}, result: {}",
                unFreezeVO, failureResultCodeList, returnResult, result);
        return result;
    }

    @Override
    protected Integer getFallback() {
        int fallback = 0;
        log.warn("CouponServiceConfirmCommand 服务降级! 调用消费充值代金券出错, unFreezeVO: {}! 降级返回数据: {}, 降级原因: {}",
                unFreezeVO, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }
}
