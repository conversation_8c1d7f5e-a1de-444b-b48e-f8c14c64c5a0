package com.kugou.fanxing.recharge.service;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.model.bo.ServerOptionBO;
import com.kugou.fanxing.recharge.model.dto.AccountIinfoDTO;
import com.kugou.fanxing.recharge.model.dto.BaseGametopDTO;
import com.kugou.fanxing.recharge.model.dto.GametopVerifyDTO;
import com.kugou.fanxing.recharge.model.request.QueryTmallChargeReq;
import com.kugou.fanxing.recharge.model.request.TaobaoZcVerifyRequest;
import com.kugou.fanxing.recharge.service.command.GetKugouIdMappingByUserIdsCommand;
import com.kugou.fanxing.recharge.service.command.GetUserListByKugouIdListCommand;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.fanxing.userbaseinfo.service.UserModuleV2BizService;
import com.kugou.fanxing.userbaseinfo.vo.UserVO;
import com.taobao.api.internal.spi.CheckResult;
import com.taobao.api.internal.spi.SpiUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TmallRechargeService {

    @Autowired
    private UserModuleV2BizService.Iface userModuleV2BizService;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private UserFacadeService userFacadeService;
    @Autowired
    private RemoteStrategyService remoteStrategyService;

    /**
     * @param param：customer：被充值账号：酷狗id/繁星id
     * @param param：cardId：充值的商品识别码，用于区分酷狗繁星账号体系
     * @return Json：data:permitRecharge :是否允许充值：1 允许充值；0 禁止充值；
     */
    public JsonResult<Map<String, Object>> checkTmallCanRecharge(QueryTmallChargeReq param) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("permitRecharge", 0);
        try {
            long kugouId = 0;
            // 处理繁星账号体系
            if (isFanxingBrand(param.getCardId())) {
                long userId = Long.parseLong(param.getCustomer());
                Optional<Long> optionalKugouId = this.userFacadeService.getKugouIdByUserId(userId, false);
                if (!optionalKugouId.isPresent() || optionalKugouId.get() < 1L) {
                    log.error("天猫商城判断账号是否可用，通过用户id获取酷狗id失败。param: {}, userId: {} ", param, userId);
                    return JsonResult.success("", dataMap);
                }
                kugouId = optionalKugouId.get();
            }
            // 处理酷狗账号体系
            if (isKugouBrand(param.getCardId())) {
                kugouId = Long.parseLong(param.getCustomer());
                Optional<Long> optionalUserId = this.userFacadeService.getUserIdByKugouId(kugouId, false);
                if (!optionalUserId.isPresent() || optionalUserId.get() < 1L) {
                    log.error("天猫商城判断账号是否可用，通过酷狗id获取繁星id失败。param: {}, kugouId: {} ", param, kugouId);
                    return JsonResult.success("", dataMap);
                }
            }
            // 非酷狗非繁星，禁止充值
            if (!isKugouBrand(param.getCardId()) && !isFanxingBrand(param.getCardId())) {
                log.warn("天猫商城判断账号是否可用，非酷狗账号体系，非繁星账号体系, 禁止充值。param: {}", param);
                return JsonResult.success("", dataMap);
            }
            // 判断是否机器人用户
            if (kugouId >= 510022001L && kugouId <= 510172000L) {
                log.warn("天猫商城判断账号是否可用，机器人禁止充值。param: {}, kugouId: {}", param, kugouId);
                return JsonResult.success("", dataMap);
            }
            // 校验是否封禁、注销账户
            if(userFacadeService.isBannedAccount(kugouId) || userFacadeService.isRevokedAccount(kugouId)){
                log.warn("天猫商城判断账号是否可用，封禁注销账号禁止天猫充值。param: {}, kugouId: {}", param, kugouId);
                return JsonResult.success("", dataMap);
            }
            // 校验是否海外账户
            if (userFacadeService.isOverseasRegisterUser(kugouId)) {
                log.warn("天猫商城判断账号是否可用，海外注册用户禁止天猫充值。param: {}, kugouId: {}", param, kugouId);
                return JsonResult.success("", dataMap);
            }
            // 判断天猫商城开关是否打开
            Optional<ServerOptionBO> optionalServerOptionBO = this.apolloConfigService.getServerOptionByKey("rechargeTmall");
            if (optionalServerOptionBO.isPresent() && optionalServerOptionBO.get().getServerValue() == 0) {
                log.warn("天猫商城判断账号是否可用，天猫充值开关关闭。param: {}, kugouId: {}", param, kugouId);
                return JsonResult.success("", dataMap);
            }
            // 调用风控服务检查风险行为
            boolean blockByRisk = remoteStrategyService.strategyVerifyForTmall(kugouId, param.getCardId());
            if (blockByRisk) {
                log.warn("天猫商城判断账号是否可用，风控拦截阻断充值。param: {}, kugouId: {}", param, kugouId);
                return JsonResult.success("", dataMap);
            }
            // 允许账号使用天猫充值
            dataMap.put("permitRecharge", 1);
        } catch (Exception e) {
            Cat.logError("天猫商城判断账号是否可用，检查天猫充值账号异常", e);
            log.error("天猫商城判断账号是否可用，检查天猫充值账号异常。params: {}, dataMap: {}", param, dataMap, e);
            return JsonResult.failure(JsonResult.DefaultResultCodeEnum.FAILURE, JsonResult.ResultCode::getMsg, dataMap);
        }
        return JsonResult.success("", dataMap);
    }

    public boolean isKugouBrand(String cardId) {
        String kugouAccountPattern = this.apolloConfigService.getTmallKugouAccountRulePattern();
        return StringUtils.isNotBlank(kugouAccountPattern) && cardId.startsWith(kugouAccountPattern);
    }

    public boolean isFanxingBrand(String cardId) {
        String fanxingAccountPattern = this.apolloConfigService.getTmallFanxingAccountRulePattern();
        return StringUtils.isNotBlank(fanxingAccountPattern) && cardId.startsWith(fanxingAccountPattern);
    }

    public BaseGametopDTO queryRechargeAccountNickname(HttpServletRequest request) {
        try {
            CheckResult result = SpiUtils.checkSign(request, "ddd0c0464be58e957df568f962ac7e90");
            if (!result.isSuccess()) {
                log.warn("查询天猫昵称信息签名校验失败, request: {}", request);
                return buildGametopVerifyDTOFailure();
            }
            TaobaoZcVerifyRequest taobaoRequest = JSON.parseObject(result.getRequestBody(), TaobaoZcVerifyRequest.class);
            String[] accountArr = StringUtils.split(taobaoRequest.getAccounts(), ",");
            List<Long> accounts = Arrays.stream(accountArr).map(Long::valueOf).collect(Collectors.toList());
            String orderNo = taobaoRequest.getOrderNo();
            String brandId = taobaoRequest.getBrandId();
            // 检查是否为酷狗品牌
            List<String> kugouBrandIds = apolloConfigService.getTmallKugouBrandIds();
            if (kugouBrandIds.contains(brandId)) {
                return buildGametopVerifyDTOByKugouId(orderNo, accounts);
            }
            // 检查是否为繁星品牌
            List<String> fanxingBrandIds = apolloConfigService.getTmallFanxingBrandIds();
            if (fanxingBrandIds.contains(brandId)) {
                return buildGametopVerifyDTOByUserId(orderNo, accounts);
            }
        } catch (NumberFormatException e) {
            log.warn("查询天猫充值账号昵称信息，非法参数。request: {}, errorMsg: {}", request.getParameterMap(), ExceptionUtils.getMessage(e));
        } catch (Exception e) {
            log.error("查询天猫充值账号昵称信息，查询异常。request: {}", request.getParameterMap(), e);
        }
        return buildGametopVerifyDTOFailure();
    }

    private BaseGametopDTO buildGametopVerifyDTOFailure() {
        return new BaseGametopDTO()
                .setResultCode("-1")
                .setResultMsg("")
                .setSubCode("sign-check-failure")
                .setSubMessage("Illegal request");
    }

    private GametopVerifyDTO buildGametopVerifyDTOByKugouId(String orderNo, List<Long> kugouIds) {
        List<UserVO> userVOList = new GetUserListByKugouIdListCommand(userModuleV2BizService, kugouIds).execute();
        Map<Long, UserVO> userVOMap = userVOList.stream().collect(Collectors.toMap(UserVO::getKugouId, Function.identity()));
        // 构建返回数据
        GametopVerifyDTO gametopVerifyDTO = new GametopVerifyDTO();
        gametopVerifyDTO.setOrderNo(orderNo);
        gametopVerifyDTO.setResultCode("00");
        gametopVerifyDTO.setResultMsg("");
        gametopVerifyDTO.setSubCode("");
        gametopVerifyDTO.setSubMessage("");
        List<AccountIinfoDTO> accountInfoDTOList = kugouIds.stream()
                .map(kugouId -> {
                    Optional<UserVO> optionalUserVO = Optional.ofNullable(userVOMap.get(kugouId));
                    String nick = optionalUserVO.isPresent() ? optionalUserVO.get().getNickName() : StringUtils.EMPTY;
                    return new AccountIinfoDTO()
                            .setAccount(String.valueOf(kugouId))
                            .setNick(nick)
                            .setTag("")
                            .setExt("");
                })
                .collect(Collectors.toList());
        gametopVerifyDTO.setAccountIinfo(accountInfoDTOList);
        return gametopVerifyDTO;
    }

    private GametopVerifyDTO buildGametopVerifyDTOByUserId(String orderNo, List<Long> userIds) {
        Map<Long, Long> kugouId2UserIdMap = new GetKugouIdMappingByUserIdsCommand(userModuleV2BizService, userIds).execute();
        List<Long> kugouIds = userIds.stream().map(userId -> kugouId2UserIdMap.getOrDefault(userId, 0L)).collect(Collectors.toList());
        List<UserVO> userVOList = new GetUserListByKugouIdListCommand(userModuleV2BizService, kugouIds).execute();
        Map<Long, UserVO> userVOMap = userVOList.stream().collect(Collectors.toMap(UserVO::getUserId, Function.identity()));
        // 构建返回数据
        GametopVerifyDTO gametopVerifyDTO = new GametopVerifyDTO();
        gametopVerifyDTO.setOrderNo(orderNo);
        gametopVerifyDTO.setResultCode("00");
        gametopVerifyDTO.setResultMsg("");
        gametopVerifyDTO.setSubCode("");
        gametopVerifyDTO.setSubMessage("");
        List<AccountIinfoDTO> accountInfoDTOList = userIds.stream()
                .map(userId -> {
                    Optional<UserVO> optionalUserVO = Optional.ofNullable(userVOMap.get(userId));
                    String nick = optionalUserVO.isPresent() ? optionalUserVO.get().getNickName() : StringUtils.EMPTY;
                    return new AccountIinfoDTO()
                            .setAccount(String.valueOf(userId))
                            .setNick(nick)
                            .setTag("")
                            .setExt("");
                })
                .collect(Collectors.toList());
        gametopVerifyDTO.setAccountIinfo(accountInfoDTOList);
        return gametopVerifyDTO;
    }

    /**
     * 根据天猫店铺品牌的获取用户酷狗ID
     * 天猫店铺支持多品牌（繁星直播、酷狗直播）
     *
     * @param customer 客户ID
     * @param extend 天猫支付透传酷狗
     * @return 酷狗ID
     */
    public Optional<Long> parseKugouIdByBrand(long customer, String extend) {
        // 解析店铺账号体系
        Optional<String> optionalCardId = JsonUtils.parseJsonPath(StringUtils.defaultString(extend), "$.cardId", String.class);
        if (!optionalCardId.isPresent()) {
            log.error("解析天猫充值账号体系，无法识别账号体系。customer: {}, extend: {}", customer, extend);
            return Optional.empty();
        }
        String cardId = optionalCardId.get();
        // 处理繁星账号体系
        if (this.isFanxingBrand(cardId)) {
            Optional<Long> optionalKugouId = this.userFacadeService.getKugouIdByUserId(customer);
            if (!optionalKugouId.isPresent() || optionalKugouId.get() < 1L) {
                log.error("解析天猫充值账号体系，通过繁星id获取酷狗id失败。customer: {}, extend: {}", customer, extend);
                return Optional.empty();
            }
            return optionalKugouId;
        }
        // 处理酷狗账号体系
        if (this.isKugouBrand(cardId)) {
            Optional<Long> optionalUserId = this.userFacadeService.getUserIdByKugouId(customer);
            if (!optionalUserId.isPresent() || optionalUserId.get() < 1L) {
                log.error("解析天猫充值账号体系，通过酷狗id获取繁星id失败。customer: {}, extend: {}", customer, extend);
                return Optional.empty();
            }
            return Optional.of(customer);
        }
        log.error("解析天猫充值账号体系，无法识别账号体系。customer: {}, extend: {}", customer, extend);
        return Optional.empty();
    }

    public JsonResult<Map<String, Object>> checkTmallCanRechargeKw(QueryTmallChargeReq param) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("permitRecharge", 0);
        try {
            long kugouId = 0;
            // 处理酷我账号体系
            long kuwoId = Long.parseLong(param.getCustomer());
            Optional<Long> optionalKugouId = this.userFacadeService.getKugouIdByKuwoId(kuwoId);
            if (!optionalKugouId.isPresent() || optionalKugouId.get() < 1L) {
                log.warn("酷我天猫商城判断账号是否可用，通过酷我id获取酷狗id失败。param: {}, kuwoId: {} ", param, kuwoId);
                return JsonResult.success("", dataMap);
            }
            kugouId = optionalKugouId.get();
            // 校验是否封禁、注销账户
            if(userFacadeService.isBannedAccount(kugouId) || userFacadeService.isRevokedAccount(kugouId)){
                log.warn("酷我天猫商城判断账号是否可用，该用户账号已注销。param: {}, kugouId: {}", param, kugouId);
                return JsonResult.success("", dataMap);
            }
            // 调用风控服务检查
            boolean blockByRisk = remoteStrategyService.strategyVerifyForTmall(kugouId, param.getCardId());
            if (blockByRisk) {
                log.warn("酷我天猫商城判断账号是否可用，风控拦截阻断充值。param: {}, kugouId: {}", param, kugouId);
                return JsonResult.success("", dataMap);
            }
            // 允许账号使用天猫充值
            dataMap.put("permitRecharge", 1);
        } catch (Exception e) {
            Cat.logError("酷我天猫商城判断账号是否可用，检查天猫充值账号异常", e);
            log.error("酷我天猫商城判断账号是否可用，检查天猫充值账号异常。params: {}, dataMap: {}", param, dataMap, e);
            return JsonResult.failure(JsonResult.DefaultResultCodeEnum.FAILURE, JsonResult.ResultCode::getMsg, dataMap);
        }
        return JsonResult.success("", dataMap);
    }
}
