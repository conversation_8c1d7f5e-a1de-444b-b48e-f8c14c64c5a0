package com.kugou.fanxing.recharge.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.model.bo.KugouOpenBusinessBO;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.bo.ReceiptInfoBO;
import com.kugou.fanxing.recharge.model.po.AppStoreReceiptPO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.KugouOpenDispatchParam;
import com.kugou.fanxing.recharge.model.request.OpenAppStoreRequest;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.model.vo.AppStoreProductVO;
import com.kugou.fanxing.recharge.service.appstore.AppStoreReceiptService;
import com.kugou.fanxing.recharge.service.callback.AbstractCallbackService;
import com.kugou.fanxing.recharge.service.callback.OpenRechargeCallbackService;
import com.kugou.fanxing.recharge.util.*;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.UnaryOperator;

@Slf4j
@Service
public class AppStoreRechargeService extends AbstractCallbackService {

    @Autowired
    private KupayService kupayService;
    @Autowired
    private AppStoreReceiptService appStoreReceiptService;
    @Autowired
    private OpenRechargeCallbackService openRechargeCallbackService;

    public List<AppStoreProductVO> getProductList(String openAppId, int pid) {
        String json = apolloConfigService.getAppStoreProductList(openAppId, pid);
        List<AppStoreProductVO> appStoreProductVOList = JSON.parseArray(json, AppStoreProductVO.class);
        log.warn("游戏苹果内购商品列表，加载货品码列表。 openAppId: {}, pid: {}, appStoreProductVOList: {}", openAppId, pid, appStoreProductVOList);
        return appStoreProductVOList;
    }

    public Optional<AppStoreProductVO> getProduct(String openAppId, int pid, String productId) {
        List<AppStoreProductVO> productVOS = getProductList(openAppId, pid);
        if (CollectionUtils.isEmpty(productVOS)) {
            return Optional.empty();
        }
        AppStoreProductVO productVO = productVOS.stream().filter(p -> p.getProductId().equals(productId)).findFirst().orElse(null);
        if (Objects.isNull(productVO)) {
            return Optional.empty();
        }
        return Optional.of(productVO);
    }

    public Optional<String> createRechargeResultApple(RechargeAcrossPO rechargeAcrossPO, KugouOpenBusinessBO kugouOpenBusinessBO, Map<String, String> kupayParam, Map<String, Object> extendParam) {
        try {
            KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByAppId(kugouOpenBusinessBO.getKupayAppId());
            Map<String, String> params = Maps.newHashMap();
            params.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
            params.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
            params.put("subject", (kupayParam != null && kupayParam.containsKey("subject")) ? kupayParam.get("subject") : "星币充值服务");
            params.put("desc", (kupayParam != null && kupayParam.containsKey("desc")) ? kupayParam.get("desc") : "星币充值服务");
            params.put("total_fee", String.valueOf(rechargeAcrossPO.getAmount()));
            params.put("clientip", rechargeAcrossPO.getClientIp());
            params.put("notify_url", rechargeConfig.getRechargeZuulNotifyUrl(UrlConstants.KUPAY_APPSTORE_ORDER_CALLBACK));
            params.put("sign_type", rechargeConfig.getSignType());
            params.put("appid", String.valueOf(kugouOpenBusinessBO.getKupayAppId()));
            params.put("time", String.valueOf(System.currentTimeMillis()));
            params.put("sign", SignUtils.buildSign(params, kupayAppInfoPO.getKupayAppKey()));
            PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
            String actionUrl = this.rechargeConfig.getActionUrlPrefix(payTypeIdEnum);
            HttpUrl httpUrl = HttpClientUtils.makeHttpUrlBuilder(actionUrl, params).build();
            String rechargeUrl = URLDecoder.decode(String.valueOf(httpUrl), StandardCharsets.UTF_8.name());
            Optional<String> optionalJson = HttpClientUtils.doPostJSON(rechargeUrl, Maps.newHashMap(), params);
            log.warn("创建苹果支付订单，请求网关接口参数。url:{}, param:{}, response:{}", rechargeUrl, params, optionalJson.orElse(""));
            if (!optionalJson.isPresent() || !KupayUtils.isSuccessResponse(payTypeIdEnum, optionalJson.get())) {
                log.warn("创建苹果支付订单，请求接口响应失败。rechargeOrderNum: {}, response: {}", rechargeAcrossPO.getRechargeOrderNum(), optionalJson);
                return Optional.empty();
            }
            return JsonUtils.parseJsonPath(optionalJson.get(), "$.data.out_trade_no", String.class);
        } catch (Exception e) {
            log.error("创建苹果支付订单，请求接口响应异常。rechargeAcrossPO: {}", rechargeAcrossPO, e);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }


    public Map<String, Object> getOrder(WebCommonParam webCommonParam, OpenAppStoreRequest request, KugouOpenBusinessBO kugouOpenBusinessBO) {
        log.warn("创建苹果支付订单，请求参数。webCommonParam: {}", webCommonParam);
        // 解析订单月份
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
        RechargeAcrossPO rechargeAcrossPO = rechargeCommonService.buildRechargeAcross(webCommonParam.getPid(), webCommonParam.getKugouId(), rechargeOrderNum, request.getPayTypeIdEnum(), request.getAmount(), webCommonParam.getIp(), Optional.empty());
        // 生成充值链接
        Map<String, String> kupayParam = Maps.newHashMap();
        kupayParam.put("notify_url", rechargeConfig.getOpenNotifyUrl());
        kupayParam.put("desc", request.getProductId());
        kupayParam.put("subject", request.getProductId());
        Map<String, Object> extendParam = Maps.newHashMap();
        extendParam.put("userOpenid", webCommonParam.getKugouOpenDispatchParam().getUser_openid());
        extendParam.put("businessId", request.getBusinessId());
        extendParam.put("productId", request.getProductId());
        // 创建网关订单号
        Optional<String> optionalOrderNo = createRechargeResultApple(rechargeAcrossPO, kugouOpenBusinessBO, kupayParam, extendParam);
        if (!optionalOrderNo.isPresent()) {
            log.warn("创建苹果支付订单失败，调用网关下单接口失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        String orderNo = optionalOrderNo.get();
        String extendStr = this.rechargeCommonService.buildExtendStr(rechargeAcrossPO, extendParam);
        rechargeAcrossPO.setExtend(extendStr);
        rechargeAcrossPO.setConsumeOrderNum(orderNo);
        rechargeAcrossPO.setBusinessId(request.getBusinessId());
        int affected = rechargeOpenDao.add(month, rechargeAcrossPO);
        if (affected < 1) {
            log.warn("创建苹果支付订单失败，保存下单记录失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("rechargeOrderNum", rechargeOrderNum);
        return dataMap;
    }

    public SysResultCode finishOrder(WebCommonParam webCommonParam) {
        KugouOpenDispatchParam kugouOpenDispatchParam = webCommonParam.getKugouOpenDispatchParam();
        String rechargeOrderNum = JsonUtils.parseJsonPathChecked(kugouOpenDispatchParam.getBusiness_data(), "$.rechargeOrderNum", String.class);
        String receiptData = JsonUtils.parseJsonPathChecked(kugouOpenDispatchParam.getBusiness_data(), "$.receiptData", String.class);
        String version = JsonUtils.parseJsonPathChecked(kugouOpenDispatchParam.getBusiness_data(), "$.version", String.class);
        // 请求参数校验
        if (StringUtils.isBlank(rechargeOrderNum) || StringUtils.isBlank(receiptData)) {
            log.warn("游戏苹果内购收据上报, 请求参数校验不通过。webCommonParam:{}", webCommonParam);
            return SysResultCode.FAILURE;
        }
        // 限制沙盒使用
        if (this.appStoreReceiptService.isSandboxReceipt(receiptData) && !this.apolloConfigService.permitSandboxVersion(version)) {
            log.warn("游戏苹果内购收据上报, 当前版本禁止使用沙盒充值。webCommonParam:{}", webCommonParam);
            return SysResultCode.E_50000006;
        }
        // 保存待验证收据信息
        this.appStoreReceiptService.addSourceReceipt(rechargeOrderNum, receiptData);
        return doFinishOrder(rechargeOrderNum, receiptData);
    }

    public SysResultCode doFinishOrder(String rechargeOrderNum, String receiptData) {
        // 验证收据信息合法性
        Optional<ReceiptInfoBO> optionalReceiptInfoBO = this.appStoreReceiptService.verifyReceipt(receiptData);
        if (!optionalReceiptInfoBO.isPresent()) {
            log.warn("游戏苹果内购收据上报，收据验证不通过。webCommonParam: {}", new Object());
            return SysResultCode.FAILURE;
        }
        // 保存已验证收据信息
        ReceiptInfoBO receiptInfoBO = optionalReceiptInfoBO.get();
        ReceiptInfoBO.Receipts receipt = receiptInfoBO.getReceipt();
        // 绑定直播单号
        String transactionId = receipt.getTransaction_id();
        String orderNum = StringUtils.join("TID", transactionId);
        Optional<String> optionalRechargeOrderNum = this.rechargeOrderService.convertTradeNoToRechargeOrderNum(orderNum, rechargeOrderNum);
        if (!optionalRechargeOrderNum.isPresent()) {
            log.warn("游戏苹果内购收据上报，关联第三方交易号失败。webCommonParam: {}", new Object());
            return SysResultCode.FAILURE;
        }
        // 业务订单校验
        rechargeOrderNum = optionalRechargeOrderNum.get();
        String month = this.orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
        RechargeAcrossPO sourceOrder = this.rechargeOpenDao.queryByRechargeOrderNum(month, rechargeOrderNum);
        if (Objects.isNull(sourceOrder)) {
            log.warn("游戏苹果内购收据上报，业务订单不存在。webCommonParam: {}", new Object());
            return SysResultCode.FAILURE;
        }
        // 验证货品代码
        String productId = receipt.getProduct_id();
        Optional<AppStoreProductVO> optionalAppStoreProductVO = this.getProduct(sourceOrder.getBusinessId(), sourceOrder.getCFrom(), productId);
        if (!optionalAppStoreProductVO.isPresent()) {
            log.warn("游戏苹果内购收据上报，请求货品不存在。sourceOrder: {}， productId: {}", sourceOrder, productId);
            return SysResultCode.FAILURE;
        }
        // 构建到账流水
        AppStoreProductVO appStoreProductVO = optionalAppStoreProductVO.get();
        boolean isSandbox = this.appStoreReceiptService.isSandboxReceipt(receiptData);
        BigDecimal price = new BigDecimal(appStoreProductVO.getPrice());
        UnaryOperator<RechargeAcrossPO> source2Target = source -> {
            RechargeAcrossPO target = ModelUtils.fromUnchecked(source, RechargeAcrossPO.class);
            target.setCoin(BigDecimal.ZERO);
            target.setRealAmount(price.multiply(BigDecimal.valueOf(100)));
            target.setStatus(1);
            target.setRechargeTime(DateHelper.getCurrentSeconds());
            target.setReType(ReTypeEnum.RETYPE_RECHARGE.getReTypeId());
            target.setRechargeId(orderIdService.generateGlobalId());
            target.setTradeNo(transactionId);
            target.setTradeTime(getTradeTime(receipt));
            target.setPartner(StringUtils.defaultString(receipt.getUnique_identifier()));
            target.setBusinessId(sourceOrder.getBusinessId());
            target.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_1006.getPayTypeId());
            target.setIsSandbox(isSandbox ? 1 : 0);
            return target;
        };
        RechargeAcrossPO targetOrder = source2Target.apply(sourceOrder);
        // 通知游戏后端发货
        SysResultCode sysResultCode = this.openRechargeCallbackService.notifyGameServed(targetOrder);
        if (!sysResultCode.isSuccess()) {
            log.warn("游戏苹果内购收据上报，通知业务发货失败。rechargeOrderNum: {}, targetOrder: {}", rechargeOrderNum, targetOrder);
            return sysResultCode;
        }
        // 通知网关订单完成
        String outTradeNo = sourceOrder.getConsumeOrderNum();
        if (StringUtils.isEmpty(outTradeNo)) {
            log.warn("游戏苹果内购收据上报，网关无匹配的直播充值单号。webCommonParam: {}, targetOrder: {}", new Object(), targetOrder);
        }
        String totalFee = targetOrder.getMoney().stripTrailingZeros().toPlainString();
        Date tradeTime = new Date(targetOrder.getTradeTime() * 1000L);
        boolean result = this.kupayService.notifyIospayV1(rechargeOrderNum, outTradeNo, targetOrder.getTradeNo(), totalFee, tradeTime);
        if (!result) {
            log.warn("游戏苹果内购收据上报，通知网关订单完成失败。webCommonParam: {}, targetOrder: {}", new Object(), targetOrder);
            return SysResultCode.FAILURE;
        }
        // 保存充值到账流水
        boolean isSuccess = this.saveGameCallbackSuccessOrder(targetOrder, receiptData, receiptInfoBO);
        if (!isSuccess) {
            log.warn("游戏苹果内购收据上报，保存成功流水失败。webCommonParam: {}, targetOrder: {}", new Object(), targetOrder);
            return SysResultCode.FAILURE;
        }
        log.warn("游戏苹果内购收据上报，充值成功=======》OK。");
        return SysResultCode.SUCCESS;
    }

    private int getTradeTime(ReceiptInfoBO.Receipts receipt) {
        long purchaseDateMs = ParseUtils.tryParseLong(receipt.getPurchase_date_ms(), 0);
        return Math.toIntExact(purchaseDateMs / 1000);
    }

    public Map<String, Object> fixOrder(long sTime, long eTime, int limit) {
        Map<String, Object> payload = Maps.newHashMap();
        payload.put("sTime", DateHelper.format(sTime));
        payload.put("eTime", DateHelper.format(eTime));
        payload.put("limit", limit);
        payload.put("foundSize", 0);
        payload.put("fixedSize", 0);
        List<Long> globalIds = this.appStoreReceiptDao.queryGlobalIds(sTime/1000, eTime/1000, limit);
        if (CollectionUtils.isEmpty(globalIds)) {
            log.warn("苹果内购补单任务，未发现待补单数据。");
            return payload;
        }
        List<AppStoreReceiptPO> appStoreReceiptPOList = this.appStoreReceiptDao.queryByGlobalIds(globalIds);
        int foundSize = appStoreReceiptPOList.size();
        int fixedSize = appStoreReceiptPOList.stream().mapToInt(appStoreReceiptPO -> {
            String rechargeOrderNum = appStoreReceiptPO.getRechargeOrderNum();
            String receiptData = appStoreReceiptPO.getReceiptData();
            SysResultCode sysResultCode = doFinishOrder(rechargeOrderNum, receiptData);
            return sysResultCode.isSuccess() ? 1 : 0;
        }).sum();
        payload.put("foundSize", foundSize);
        payload.put("fixedSize", fixedSize);
        return payload;
    }
}
