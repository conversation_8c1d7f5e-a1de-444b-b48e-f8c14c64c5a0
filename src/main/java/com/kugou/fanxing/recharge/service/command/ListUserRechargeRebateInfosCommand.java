package com.kugou.fanxing.recharge.service.command;

import com.google.common.collect.Maps;
import com.kugou.platform.recharge.queryservice.thrift.RechargeInfoQueryThriftService;
import com.kugou.platform.recharge.queryservice.thrift.RechargeRebateInfo;
import com.kugou.platform.recharge.queryservice.thrift.ResultInfo;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 查询充值赠币
 *
 * <AUTHOR>
 */
@Slf4j
public class ListUserRechargeRebateInfosCommand extends HystrixCommand<Map<String, RechargeRebateInfo>> {

    private final String month;
    private final List<String> rechargeOrderIds;
    private final RechargeInfoQueryThriftService.Iface rechargeInfoQueryThriftService;

    public ListUserRechargeRebateInfosCommand(final RechargeInfoQueryThriftService.Iface rechargeInfoQueryThriftService, final String month, final List<String> rechargeOrderIds) {
        super(HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("ListUserRechargeRebateInfosCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(1000)));
        this.month = month;
        this.rechargeOrderIds = rechargeOrderIds;
        this.rechargeInfoQueryThriftService = rechargeInfoQueryThriftService;
    }

    @Override
    protected Map<String, RechargeRebateInfo> run() throws Exception {
        if (CollectionUtils.isEmpty(rechargeOrderIds)) {
            return Maps.newHashMap();
        }
        ResultInfo resultInfo = rechargeInfoQueryThriftService.listUserRechargeRebateInfos(month, rechargeOrderIds);
        if (Objects.isNull(resultInfo) || resultInfo.getRet() != 0) {
            log.error("查询充值返点信息失败, month: {}, rechargeOrderIds: {}, response: {}", month, rechargeOrderIds, resultInfo);
            return Maps.newHashMap();
        }
        List<RechargeRebateInfo> rechargeRebateInfoList = resultInfo.getData();
        return rechargeRebateInfoList.stream()
                .collect(Collectors.toMap(RechargeRebateInfo::getRechargeOrderId, rechargeRebateInfo -> rechargeRebateInfo));
    }

    @Override
    protected Map<String, RechargeRebateInfo> getFallback() {
        Map<String, RechargeRebateInfo> fallbackResult = Maps.newHashMap();
        log.warn("ListUserRechargeRebateInfosCommand服务降级! month: {}, rechargeOrderIds: {}. 降级返回数据: {}, 降级原因: {}",
                month, rechargeOrderIds, fallbackResult, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallbackResult;
    }
}
