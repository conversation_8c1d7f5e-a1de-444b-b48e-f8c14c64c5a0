package com.kugou.fanxing.recharge.service.withdraw;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kugou.fanxing.recharge.model.po.WithdrawOrderPO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.common.DencryptService;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

/**
 * 支付宝提现相关接口
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class AlipayCashService implements CashService {

    @Value("${kupay.intranet}")
    private String withdrawHostUrl;
    @Value("${kupay.withdraw.serverId}")
    private String withdrawServerId;
    @Value("${kupay.withdraw.serverKey}")
    private String withdrawServerKey;
    @Autowired
    private DencryptService dencryptService;
    @Autowired
    private ApolloConfigService apolloConfigService;

    /**
     * 支付宝提现接口
     * http://doc.kugou.net/showdoc-master/web/#/8?page_id=2752
     *
     * @param withdrawOrderPO 提现订单
     * @return 提现信息
     */
    public Optional<AlipayResp> withdraw(WithdrawOrderPO withdrawOrderPO, WithdrawClientParams withdrawClientParams, String token) {
        try {
            StringBuilder url = new StringBuilder(withdrawHostUrl);
            url.append("/v1/alipaycash/order");

            Map<String, String> urlParams = buildWithdrawCommonParams(withdrawServerId, withdrawClientParams);
            // 业务参数
            JSONObject params = new JSONObject();
            params.put("userid", withdrawOrderPO.getKugouId());
            params.put("biz_appid", withdrawOrderPO.getBizAppId());
            params.put("token", token);
            params.put("clientip", withdrawClientParams.getClientip());
            params.put("order_no", withdrawOrderPO.getOrderId());
            String accountEncrypted = withdrawOrderPO.getAccountEncrypted();
            String account = dencryptService.decrypt(accountEncrypted);
            params.put("user_account", account);
            // 支付网关支付宝提现金额单位是元
            String totalFee = withdrawOrderPO.getTotalAmount().toPlainString();
            params.put("total_fee", totalFee);
            params.put("payer_show_name", "广州酷狗计算机科技有限公司");
            params.put("payee_real_name", withdrawOrderPO.getRealName());
            params.put("remark", apolloConfigService.matchDrawCashRemark(withdrawOrderPO.getBizAppId(), ""));
            int delayMinutes = Math.max(withdrawOrderPO.getDrawTime() - DateHelper.getCurrentSeconds(), 0) / 60;
            params.put("delay_mins", delayMinutes);
            urlParams.put("signature", getCommandSign(withdrawServerKey, urlParams, params.toJSONString()));
            log.warn("调用支付宝提现接口，请求参数。url: {}, urlParams: {}, urlParams: {}, rechargeDrawCashPO: {}",
                    url, params, params.toJSONString(), withdrawOrderPO);
            Optional<String> optionalJson = HttpClientUtils.doPostJsonBody(url.toString(), urlParams, params.toJSONString());
            if (!optionalJson.isPresent() || StringUtils.isBlank(optionalJson.get())) {
                log.warn("调用支付宝提现接口失败, optionalJson: {}, url: {}, urlParams: {}, urlParams: {}, rechargeDrawCashPO: {}",
                        optionalJson, url, urlParams, params.toJSONString(), withdrawOrderPO);
                return Optional.empty();
            }

            AlipayResp alipayResp = JSON.parseObject(optionalJson.get(), AlipayResp.class);
            log.warn("调用支付宝提现接口成功, optionalJson: {}, url: {}, urlParams: {}, urlParams: {}, rechargeDrawCashPO: {}",
                    optionalJson, url, params, params.toJSONString(), withdrawOrderPO);
            return Optional.of(alipayResp);
        } catch (Exception e) {
            log.error("调用支付宝提现接口异常, rechargeDrawCashPO: {}", withdrawOrderPO, e);
        }
        return Optional.empty();
    }

    /**
     * 支付宝提现查询接口
     * http://doc.kugou.net/showdoc-master/web/#/8?page_id=2751
     *
     * @param withdrawOrderPO 提现订单
     * @return 提现信息
     */
    @Override
    public Optional<AlipayResp> withdrawOrderQuery(WithdrawOrderPO withdrawOrderPO) {
        int bizAppId = withdrawOrderPO.getBizAppId();
        long kugouId = withdrawOrderPO.getKugouId();
        long orderId = withdrawOrderPO.getOrderId();
        try {
            StringBuilder url = new StringBuilder(withdrawHostUrl);
            url.append("/v1/alipaycash/query");
            Map<String, String> params = buildWithdrawQueryCommonParams(withdrawServerId);
            // 业务参数
            params.put("userid", String.valueOf(kugouId));
            params.put("biz_appid", String.valueOf(bizAppId));
            params.put("order_no", String.valueOf(orderId));
            params.put("signature", getQuerySign(withdrawServerKey, params));
            Optional<String> optionalJson = HttpClientUtils.doSyncGet(url.toString(), params);
            if (!optionalJson.isPresent() || StringUtils.isBlank(optionalJson.get())) {
                log.warn("调用支付宝提现查询接口失败, optionalJson: {}, url: {}, params: {}, kugouId: {}, orderId: {}", optionalJson, url, params, kugouId, orderId);
                return Optional.empty();
            }
            AlipayResp alipayResp = JSON.parseObject(optionalJson.get(), AlipayResp.class);
            log.warn("调用支付宝提现查询接口成功, optionalJson: {}, url: {}, params: {}, alipayResp: {}, kugouId: {}, orderId: {}",
                    optionalJson, url, params, alipayResp, kugouId, orderId);
            return Optional.of(alipayResp);
        } catch (Exception e) {
            log.error("调用支付宝提现查询接口异常, kugouId: {}, orderId: {}", kugouId, orderId, e);
        }
        return Optional.empty();
    }

    /**
     * 支付宝取消提现接口
     * http://doc.kugou.net/showdoc-master/web/#/8?page_id=3334
     *
     * @param withdrawOrderPO 提现订单
     */
    public Optional<AlipayCancelDrawCashResp> cancelDrawCash(WithdrawOrderPO withdrawOrderPO) {
        try {
            WithdrawClientParams commonParams = JSON.parseObject(withdrawOrderPO.getExt(), WithdrawClientParams.class);
            StringBuilder url = new StringBuilder(withdrawHostUrl);
            url.append("/v1/alipaycash/cancel");
            // 通用参数
            Map<String, String> urlParams = buildWithdrawCommonParams(withdrawServerId, commonParams);
            // 业务参数
            JSONObject params = new JSONObject();
            params.put("userid", withdrawOrderPO.getKugouId());
            params.put("biz_appid", withdrawOrderPO.getBizAppId());
            params.put("order_no", withdrawOrderPO.getOrderId());
            params.put("reason", withdrawOrderPO.getCancelReason());
            urlParams.put("signature", getCommandSign(withdrawServerKey, urlParams, params.toJSONString()));
            // 服务调用
            Optional<String> optionalJson = HttpClientUtils.doPostJsonBody(url.toString(), urlParams, params.toJSONString());
            if (!optionalJson.isPresent() || StringUtils.isBlank(optionalJson.get())) {
                log.warn("调用支付宝取消提现接口失败, optionalJson: {}, url: {}, params: {}, jsonBody: {}, withdrawOrderPO: {}",
                        optionalJson, url, params, params.toJSONString(), withdrawOrderPO);
                return Optional.empty();
            }
            AlipayCancelDrawCashResp alipayResp = JSON.parseObject(optionalJson.get(), AlipayCancelDrawCashResp.class);
            log.warn("调用支付宝取消提现接口成功, optionalJson: {}, url: {}, params: {}, jsonBody: {}, withdrawOrderPO: {}",
                    optionalJson, url, params, params.toJSONString(), withdrawOrderPO);
            return Optional.of(alipayResp);
        } catch (Exception e) {
            log.error("调用支付宝取消提现接口异常, withdrawOrderPO: {}", withdrawOrderPO, e);
        }
        return Optional.empty();
    }
}
