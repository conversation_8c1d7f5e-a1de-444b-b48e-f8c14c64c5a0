package com.kugou.fanxing.recharge.service.withdraw;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.math.IntMath;
import com.kugou.fanxing.recharge.constant.DrawTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.WithdrawOrderStatusEnum;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawAccountDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawAccountWechatDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawOrderDao;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.model.DrawCashUpdater;
import com.kugou.fanxing.recharge.model.po.WithdrawAccountPO;
import com.kugou.fanxing.recharge.model.po.WithdrawAccountWechatPO;
import com.kugou.fanxing.recharge.model.po.WithdrawOrderPO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.RemoteStrategyService;
import com.kugou.fanxing.recharge.service.common.BudgetPoolCheck;
import com.kugou.fanxing.recharge.service.withdraw.transfer.TransferService;
import com.kugou.fanxing.recharge.service.withdraw.transfer.TransferServiceFactory;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.withdraw.thrift.CreateWithdrawOrderRequest;
import com.kugou.fanxing.recharge.withdraw.thrift.CreateWithdrawOrderV2Request;
import com.kugou.fanxing.recharge.withdraw.thrift.WithdrawOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.kugou.fanxing.recharge.constant.SysResultCode.WITHDRAW_ORDER_RISK_LIMIT;
import static com.kugou.fanxing.recharge.constant.WithdrawOrderStatusEnum.INITIAL;
import static com.kugou.fanxing.recharge.constant.WithdrawOrderStatusEnum.PROCESS;

/**
 * 提现服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DrawCashService {

    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private WithdrawOrderDao withdrawOrderDao;
    @Autowired
    private AlipayCashService alipayCashService;
    @Autowired
    private WithdrawAccountDao withdrawAccountDao;
    @Autowired
    private WithdrawAccountWechatDao withdrawAccountWechatDao;
    @Autowired
    private CashServiceFactory cashServiceFactory;
    @Autowired
    private TransferServiceFactory transferServiceFactory;
    @Autowired
    private BudgetPoolCheck budgetPoolCheck;
    @Autowired
    private RemoteStrategyService remoteStrategyService;

    /**
     * 仅支持支付宝提现
     */
    public SysResultCode drawCash(CreateWithdrawOrderRequest request) {
        long amount = request.getTotalAmount();
        // 检查提现金额是否非法（注意：支付网关限制提现金额最少0.1元）
        long amountLimit = apolloConfigService.getWithdrawMaxAmount();
        if (amount < 10 || amount > amountLimit) {
            log.warn("创建提现订单，提现金额非法, request: {}, amountLimit: {}", request, amountLimit);
            return SysResultCode.WITHDRAW_ORDER_ILLEGAL_AMOUNT;
        }
        // 检查支付网关业务ID
        int bizAppId = request.getBizAppId();
        if (!apolloConfigService.permitWithdrawBizAppId(bizAppId)) {
            log.warn("创建提现订单，支付网关业务ID非法，request: {}", request);
            return SysResultCode.WITHDRAW_ORDER_NOT_REGISTER;
        }
        // 检查是否绑定提现账号
        long kugouId = request.getKugouId();
        Optional<WithdrawAccountPO> optionalWithdrawAccountPO = Optional.ofNullable(withdrawAccountDao.selectByKugouId(kugouId));
        if (!optionalWithdrawAccountPO.isPresent() || StringUtils.isBlank(optionalWithdrawAccountPO.get().getAccountEncrypted())) {
            log.warn("创建提现订单，酷狗ID未绑定提现账号，request: {}", request);
            return SysResultCode.WITHDRAW_ACCOUNT_NOT_BIND;
        }
        WithdrawAccountPO withdrawAccountPO = optionalWithdrawAccountPO.get();
        // 检查订单是否已经受理
        long orderId = request.getOrderId();
        Optional<WithdrawOrderPO> optionalWithdrawOrderPO = Optional.ofNullable(this.withdrawOrderDao.getDrawCashOrderById(orderId));
        if (optionalWithdrawOrderPO.isPresent()) {
            return SysResultCode.SUCCESS;
        }
        // 保存用户提现订单记录
        WithdrawClientParams withdrawCommonParams = buildWithdrawClientParams(request);
        WithdrawOrderPO withdrawOrderPO = buildWithdrawOrderPO(request, withdrawAccountPO, withdrawCommonParams);
        int affected = this.withdrawOrderDao.insert(withdrawOrderPO);
        if (affected < 1) {
            log.warn("创建提现订单，保存用户提现订单失败，request: {}", request);
            return SysResultCode.WITHDRAW_ORDER_SAVE_FAIL;
        }
        // 调用酷狗支付网关提现（Tips：由于禁止存储用户Token信息，调用失败的订单无法在定时器中重试）
        Optional<AlipayResp> optionalAlipayResp = alipayCashService.withdraw(withdrawOrderPO, withdrawCommonParams, request.getToken());
        log.warn("创建提现订单，调用酷狗支付网关提现，request: {}, optionalAlipayResp: {}", request, optionalAlipayResp);
        optionalAlipayResp.ifPresent(alipayResp -> this.handleAlipayResp(orderId, alipayResp));
        return SysResultCode.SUCCESS;
    }

    public WithdrawOrderPO buildWithdrawOrderPO(CreateWithdrawOrderRequest request, WithdrawAccountPO withdrawAccountPO, WithdrawClientParams withdrawCommonParams) {
        return new WithdrawOrderPO()
                .setAppId(request.getAppId())
                .setBizAppId(request.getBizAppId())
                .setOrderId(request.getOrderId())
                .setKugouId(request.getKugouId())
                .setTotalAmount(BigDecimal.valueOf(request.getTotalAmount()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                .setAccountEncrypted(withdrawAccountPO.getAccountEncrypted())
                .setOpenid("")
                .setRealName(withdrawAccountPO.getRealName())
                .setStatus(PROCESS.getValue())
                .setDrawType(1)
                .setReqTime(request.getReqTime())
                .setDrawTime(request.getDrawTime())
                .setExt(JSON.toJSONString(withdrawCommonParams))
                .setCreateTime(new Date())
                .setUpdateTime(new Date());
    }

    private WithdrawClientParams buildWithdrawClientParams(CreateWithdrawOrderRequest request) {
        return new WithdrawClientParams()
                .setApplicationId(request.getApplicationId())
                .setClientip(request.getClientIp())
                .setClientver(request.getClientver())
                .setDfid(request.getDfid())
                .setMid(request.getMid())
                .setUuid(request.getUuid());
    }

    /**
     * 处理网关提现返回结果
     *
     * @param orderId    提现ID
     * @param alipayResp 提现返回
     * @return 是否已经处理
     */
    public boolean handleAlipayResp(long orderId, AlipayResp alipayResp) {
        Optional<TransferService> optionalTransferService = transferServiceFactory.createTransferService(alipayResp);
        return optionalTransferService.map(transferService -> transferService.handleAlipayResp(orderId, alipayResp)).orElse(false);
    }

    public Optional<WithdrawOrderDTO> queryWithdrawOrderByOrderId(long orderId) {
        Optional<WithdrawOrderPO> optionalPayOrder = Optional.ofNullable(this.withdrawOrderDao.getDrawCashOrderById(orderId));
        if (!optionalPayOrder.isPresent()) {
            return Optional.empty();
        }
        WithdrawOrderPO withdrawOrderPO = optionalPayOrder.get();
        WithdrawOrderDTO drawCashDTO = new WithdrawOrderDTO();
        drawCashDTO.setOrderId(withdrawOrderPO.getOrderId());
        drawCashDTO.setAppId(withdrawOrderPO.getAppId());
        drawCashDTO.setBizAppId(withdrawOrderPO.getBizAppId());
        drawCashDTO.setOutTradeNo(StringUtils.defaultString(withdrawOrderPO.getOutTradeNo()));
        drawCashDTO.setTradeNo(StringUtils.defaultString(withdrawOrderPO.getTradeNo()));
        drawCashDTO.setKugouId(withdrawOrderPO.getKugouId());
        drawCashDTO.setStatus(withdrawOrderPO.getStatus());
        drawCashDTO.setReqTime(withdrawOrderPO.getReqTime());
        drawCashDTO.setDrawTime(withdrawOrderPO.getDrawTime());
        drawCashDTO.setErrorCode(StringUtils.defaultString(withdrawOrderPO.getErrorCode()));
        drawCashDTO.setErrorMsg(StringUtils.defaultString(withdrawOrderPO.getErrorMsg()));
        drawCashDTO.setErrorReason(StringUtils.defaultString(withdrawOrderPO.getErrorReason()));
        return Optional.of(drawCashDTO);
    }

    /**
     * 触发延迟提现订单
     */
    public Map<String, Object> triggerDelayWithdrawOrder() {
        Map<String, Object> dataMap = Maps.newHashMap();
        // 检索提现中的订单数据
        int limit = apolloConfigService.getDelayWithdrawOrderPageSize();
        int maxRetryNum = apolloConfigService.getDelayWithdrawOrderMaxRetryNum();
        // 查询处于初始化状态的订单
        List<WithdrawOrderPO> withdrawOrderPOList = this.withdrawOrderDao.getInitialByPeriod(DrawTypeEnum.getWechatDrawTypeCode(), INITIAL.getValue(), maxRetryNum, limit);
        if (CollectionUtils.isEmpty(withdrawOrderPOList)) {
            withdrawOrderPOList = Lists.newArrayList();
        }
        withdrawOrderPOList.stream().filter(this::filterNonRiskOrder).forEach(withdrawOrderPO -> {
            try {
                WithdrawClientParams withdrawClientParams = JSON.parseObject(withdrawOrderPO.getExt(), WithdrawClientParams.class);
                CashService cashService = cashServiceFactory.createCashService(withdrawOrderPO.getDrawType());
                Optional<AlipayResp> optionalAlipayResp = cashService.withdraw(withdrawOrderPO, withdrawClientParams, null);
                optionalAlipayResp.ifPresent(alipayResp -> handleAlipayResp(withdrawOrderPO.getOrderId(), alipayResp));
            } catch (Exception e) {
                log.error("触发延迟提现订单，触发提现异常。withdrawOrderPO: {}", withdrawOrderPO, e);
                Cat.logError(e);
            }
        });
        return dataMap;
    }

    private boolean filterNonRiskOrder(WithdrawOrderPO withdrawOrderPO) {
        WithdrawClientParams withdrawClientParams = JSON.parseObject(withdrawOrderPO.getExt(), WithdrawClientParams.class);
        boolean isRisk = remoteStrategyService.strategyVerifyForWithdraw(withdrawOrderPO, withdrawClientParams);
        if (isRisk) {
            log.warn("支付网关提现风控拦截，风险交易。orderId: {}", withdrawOrderPO.getOrderId());
            DrawCashUpdater updater = DrawCashUpdater.build(withdrawOrderPO.getOrderId(), WITHDRAW_ORDER_RISK_LIMIT);
            int affected = withdrawOrderDao.updateOrderStatus(WithdrawOrderStatusEnum.FAILURE.getValue(), updater);
            if (affected < 1) {
                log.warn("支付网关提现风控拦截，更新提现订单状态失败。orderId: {}", withdrawOrderPO.getOrderId());
            }
            return false;
        }
        return true;
    }

    /**
     * 确认延迟提现订单
     */
    public Map<String, Object> confirmDelayWithdrawOrder() {
        Map<String, Object> dataMap = Maps.newHashMap();
        // 检索提现中的订单数据
        int limit = apolloConfigService.getDelayWithdrawOrderPageSize();
        int maxRetryNum = apolloConfigService.getDelayWithdrawOrderMaxRetryNum();
        // 查询超过提现时间1小时的订单，状态仍为处理中的异常订单
        List<WithdrawOrderPO> withdrawOrderPOList = this.withdrawOrderDao.getProcessByPeriod(PROCESS.getValue(), maxRetryNum, limit);
        if (CollectionUtils.isEmpty(withdrawOrderPOList)) {
            withdrawOrderPOList = Lists.newArrayList();
        }
        // 处理提现中的订单数据
        withdrawOrderPOList.forEach(withdrawOrder -> {
            try {
                // 检查提现订单是否已经处理
                long orderId = withdrawOrder.getOrderId();
                CashService cashService = cashServiceFactory.createCashService(withdrawOrder.getDrawType());
                Optional<AlipayResp> optionalAlipayResp = cashService.withdrawOrderQuery(withdrawOrder);
                // 调用提现响应超时或者异常
                if (!optionalAlipayResp.isPresent()) {
                    log.warn("查询网关提现接口响应超时或者异常, withdrawOrder: {}", withdrawOrder);
                    withdrawOrderDao.increaseRetryNum(orderId, new Date());
                    return;
                }
                // 调用提现正常返回响应结果
                AlipayResp alipayResp = optionalAlipayResp.get();
                if (handleAlipayResp(orderId, alipayResp)) {
                    log.warn("查询网关提现接口响应结果处理成功, alipayResp: {}", alipayResp);
                    return;
                }
                Cat.logEvent("withdraw.errorOrders", "orderId: " + orderId);
                log.error("处理提现中的订单数据，响应内容无法处理, alipayResp: {}, withdrawOrder: {}", alipayResp, withdrawOrder);
            } catch (Exception e) {
                log.error("处理提现中的订单数据，出现异常情况! withdrawOrder:{}", withdrawOrder, e);
                Cat.logError("处理延迟提现订单失败", e);
            }
        });

        dataMap.put("limit", limit);
        dataMap.put("maxRetryNum", maxRetryNum);
        dataMap.put("foundNum", withdrawOrderPOList.size());
        return dataMap;
    }

    /**
     * 取消提现
     *
     * @param orderId      兑换订单号
     * @param cancelReason 取消原因
     * @return 取消结果
     */
    public SysResultCode cancelDrawCash(long orderId, String cancelReason) {
        try {
            // 检查取消原因要求非空
            if (StringUtils.isBlank(cancelReason)) {
                log.warn("取消提现失败, 请求参数不合法, orderId: {}, cancelReason: {}", orderId, cancelReason);
                return SysResultCode.RECHARGE_PARAM_ERROR;
            }
            // 检查提现订单是否存在
            Optional<WithdrawOrderPO> optionalWithdrawOrderPO = Optional.ofNullable(this.withdrawOrderDao.getDrawCashOrderById(orderId));
            if (!optionalWithdrawOrderPO.isPresent()) {
                log.warn("取消提现失败, 提现订单不存在, orderId: {}, cancelReason: {}", orderId, cancelReason);
                return SysResultCode.WITHDRAW_ORDER_NOT_EXISTS;
            }
            // 检查订单是否已经提现
            WithdrawOrderPO withdrawOrderPO = optionalWithdrawOrderPO.get();
            if (WithdrawOrderStatusEnum.SUCCESS.getValue() == withdrawOrderPO.getStatus()) {
                log.warn("订单已经提现, 不允许再取消, orderId: {}, cancelReason: {}", orderId, cancelReason);
                return SysResultCode.WITHDRAW_CANCEL_FORBIDDEN;
            }
            // 检查订单是否已经取消
            if (WithdrawOrderStatusEnum.CANCEL.getValue() == withdrawOrderPO.getStatus()) {
                log.warn("订单已经取消, 无需重复取消, orderId: {}, cancelReason: {}", orderId, cancelReason);
                return SysResultCode.SUCCESS;
            }
            // 检查提现时间即将到期（提现前5分钟禁止提现，防止与定时任务产生并发问题）
            if (IntMath.checkedSubtract(DateHelper.getCurrentSeconds(), withdrawOrderPO.getDrawTime()) > 300) {
                log.warn("订单即将提现, 不允许再取消, orderId: {}, currTime: {}, drawTime: {}, cancelReason: {}", orderId, DateHelper.getCurrentSeconds(), withdrawOrderPO.getDrawTime(), cancelReason);
                return SysResultCode.WITHDRAW_CANCEL_FORBIDDEN;
            }
            // 调用网关取消订单提现
            Optional<AlipayCancelDrawCashResp> response = alipayCashService.cancelDrawCash(withdrawOrderPO.setCancelReason(cancelReason));
            if (!response.isPresent() || response.get().isCancelForbidden() || !response.get().isCancelSuccess()) {
                log.warn("取消提现失败, 网关返回为空, orderId: {}, cancelReason: {}, response: {}", orderId, cancelReason, response);
                return SysResultCode.WITHDRAW_CANCEL_FAILURE;
            }
            // 更新订单取消提现状态
            this.withdrawOrderDao.updateCanceledOrderStatus(orderId, WithdrawOrderStatusEnum.CANCEL.getValue(), new Date());
        } catch (Exception e) {
            log.error("取消提现异常, orderId: {}, cancelReason: {}, errorMsg: {}", orderId, cancelReason, ExceptionUtils.getMessage(e), e);
        }
        return SysResultCode.SUCCESS;
    }

    public Optional<List<WithdrawAccountPO>> queryKugouIdAccount(String accountFingerprint){
        return Optional.ofNullable(this.withdrawAccountDao.getAccountNumByFingerprint(accountFingerprint));
    }

    public SysResultCode drawCashV2(CreateWithdrawOrderV2Request request) {
        // 检查提现金额是否非法（注意：支付网关限制提现金额最少0.1元，微信提现最少1元）
        long amount = request.getTotalAmount();
        long amountLimit = apolloConfigService.getWithdrawMaxAmount();
        if ((DrawTypeEnum.isAlipayDrawCash(request.getDrawType()) && amount < 10) || (DrawTypeEnum.isWechatDrawCash(request.getDrawType()) && amount < 100) || amount > amountLimit) {
            log.warn("创建提现订单，提现金额非法。request: {}, amountLimit: {}", request, amountLimit);
            return SysResultCode.WITHDRAW_ORDER_ILLEGAL_AMOUNT;
        }
        // 检查支付网关业务ID
        int bizAppId = request.getBizAppId();
        if (!apolloConfigService.permitWithdrawBizAppId(bizAppId)) {
            log.warn("创建提现订单，支付网关业务ID非法。request: {}", request);
            return SysResultCode.WITHDRAW_ORDER_NOT_REGISTER;
        }
        // 检查提现类型合法性
        if (!DrawTypeEnum.isSupportDrawCash(request.getDrawType())) {
            log.warn("创建提现订单，不支持的提现类型。request: {}", request);
            return SysResultCode.WITHDRAW_ORDER_ILLEGAL_TYPE;
        }
        // 检查订单是否已经受理
        long orderId = request.getOrderId();
        Optional<WithdrawOrderPO> optionalWithdrawOrderPO = Optional.ofNullable(this.withdrawOrderDao.getDrawCashOrderById(orderId));
        if (optionalWithdrawOrderPO.isPresent()) {
            return SysResultCode.SUCCESS;
        }
        SysResultCode sysResultCode = SysResultCode.WITHDRAW_ORDER_EXCEPTION;
        // 检查提现预算是否充足
        boolean isBudgetEnough = this.budgetPoolCheck.tryDecreaseBudgetOrder(request.getAppId(), 0, BigDecimal.valueOf(amount), request.getOrderId(), request.getReqTime());
        if (!isBudgetEnough) {
            log.warn("创建提现订单，提现超过预算限制。request: {}", request);
            return SysResultCode.WITHDRAW_ORDER_NO_BUDGET;
        }
        try {
            // 处理Alipay提现
            if (DrawTypeEnum.isAlipayDrawCash(request.getDrawType())) {
                sysResultCode = handleAlipayDrawCash(request, orderId);
            }
            // 处理Wechat提现
            if (DrawTypeEnum.isWechatDrawCash(request.getDrawType()) || DrawTypeEnum.isStWechatDrawCash(request.getDrawType())) {
                sysResultCode = handleWechatDrawCash(request);
            }
            // 提交或者回滚预算
            sysResultCode.postHandler(
                    sysResultCode1 -> budgetPoolCheck.confirmDecreaseBudgetOrder(request.getAppId(), request.getOrderId(), request.getReqTime()),
                    sysResultCode1 -> budgetPoolCheck.cancelDecreaseBudgetOrder(request.getAppId(), request.getOrderId(), request.getReqTime())
            );
            return sysResultCode;
        } catch (Exception e) {
            budgetPoolCheck.cancelDecreaseBudgetOrder(request.getAppId(), request.getOrderId(), request.getReqTime());
            throw new AckException(SysResultCode.WITHDRAW_ORDER_EXCEPTION, e);
        }
    }

    private SysResultCode handleWechatDrawCash(CreateWithdrawOrderV2Request request) {
        String wxAppId = apolloConfigService.getWxAppIdByBizAppId(request.getBizAppId());
        Optional<WithdrawAccountWechatPO> optionalWithdrawAccountWechatPO = Optional.ofNullable(withdrawAccountWechatDao.selectByKugouId(request.getKugouId(), wxAppId));
        if (!optionalWithdrawAccountWechatPO.isPresent() || StringUtils.isBlank(optionalWithdrawAccountWechatPO.get().getOpenid())) {
            log.warn("创建提现订单，酷狗ID未绑定微信提现账号，request: {}", request);
            return SysResultCode.WITHDRAW_ACCOUNT_NOT_BIND;
        }
        // 微信的延迟提现由充值服务实现，因此待到用户实际提现时间调用网关
        WithdrawAccountWechatPO withdrawAccountWechatPO = optionalWithdrawAccountWechatPO.get();
        WithdrawOrderPO withdrawOrderPO = buildWechatWithdrawOrder(request, withdrawAccountWechatPO, buildWithdrawClientParamsV2(request));
        int affected = this.withdrawOrderDao.insert(withdrawOrderPO);
        if (affected < 1) {
            log.warn("创建提现订单，保存用户提现订单失败，request: {}", request);
            return SysResultCode.WITHDRAW_ORDER_SAVE_FAIL;
        }
        return SysResultCode.SUCCESS;
    }

    private SysResultCode handleAlipayDrawCash(CreateWithdrawOrderV2Request request, long orderId) {
        // 检查是否绑定提现账号
        Optional<WithdrawAccountPO> optionalWithdrawAccountPO = Optional.ofNullable(withdrawAccountDao.selectByKugouId(request.getKugouId()));
        if (!optionalWithdrawAccountPO.isPresent() || StringUtils.isBlank(optionalWithdrawAccountPO.get().getAccountEncrypted())) {
            log.warn("创建提现订单，酷狗ID未绑定提现账号。request: {}", request);
            return SysResultCode.WITHDRAW_ACCOUNT_NOT_BIND;
        }
        // 保存用户提现订单记录
        WithdrawAccountPO withdrawAccountPO = optionalWithdrawAccountPO.get();
        WithdrawClientParams withdrawCommonParams = buildWithdrawClientParamsV2(request);
        WithdrawOrderPO withdrawOrderPO = buildAlipayWithdrawOrder(request, withdrawAccountPO, withdrawCommonParams);
        int affected = this.withdrawOrderDao.insert(withdrawOrderPO);
        if (affected < 1) {
            log.warn("创建提现订单，保存用户提现订单失败，request: {}", request);
            return SysResultCode.WITHDRAW_ORDER_SAVE_FAIL;
        }
        // 调用酷狗支付网关提现（Tips：由于禁止存储用户Token信息，调用失败的订单无法在定时器中重试）
        Optional<AlipayResp> optionalAlipayResp = alipayCashService.withdraw(withdrawOrderPO, withdrawCommonParams, request.getToken());
        log.warn("创建提现订单，调用酷狗支付网关提现，request: {}, optionalAlipayResp: {}", request, optionalAlipayResp);
        optionalAlipayResp.ifPresent(alipayResp -> this.handleAlipayResp(orderId, alipayResp));
        return SysResultCode.SUCCESS;
    }

    private WithdrawClientParams buildWithdrawClientParamsV2(CreateWithdrawOrderV2Request request) {
        return new WithdrawClientParams()
                .setApplicationId(request.getApplicationId())
                .setClientip(request.getClientIp())
                .setClientver(request.getClientver())
                .setDfid(request.getDfid())
                .setMid(request.getMid())
                .setUuid(request.getUuid());
    }

    private WithdrawOrderPO buildWechatWithdrawOrder(CreateWithdrawOrderV2Request request, WithdrawAccountWechatPO withdrawAccountWechatPO, WithdrawClientParams withdrawCommonParams) {
        return new WithdrawOrderPO()
                .setAppId(request.getAppId())
                .setBizAppId(request.getBizAppId())
                .setOrderId(request.getOrderId())
                .setKugouId(request.getKugouId())
                .setTotalAmount(BigDecimal.valueOf(request.getTotalAmount()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                .setAccountEncrypted("")
                .setOpenid(withdrawAccountWechatPO.getOpenid())
                .setRealName(withdrawAccountWechatPO.getRealName())
                .setStatus(INITIAL.getValue())
                .setDrawType(request.getDrawType())
                .setReqTime(request.getReqTime())
                .setDrawTime(request.getDrawTime())
                .setExt(JSON.toJSONString(withdrawCommonParams))
                .setCreateTime(new Date())
                .setUpdateTime(new Date());
    }

    private WithdrawOrderPO buildAlipayWithdrawOrder(CreateWithdrawOrderV2Request request, WithdrawAccountPO withdrawAccountPO, WithdrawClientParams withdrawCommonParams) {
        return new WithdrawOrderPO()
                .setAppId(request.getAppId())
                .setBizAppId(request.getBizAppId())
                .setOrderId(request.getOrderId())
                .setKugouId(request.getKugouId())
                .setTotalAmount(BigDecimal.valueOf(request.getTotalAmount()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                .setAccountEncrypted(withdrawAccountPO.getAccountEncrypted())
                .setOpenid("")
                .setRealName(withdrawAccountPO.getRealName())
                // 支付宝提现需要提供token无法重复触发，因此调整状态为提现处理中
                .setStatus(PROCESS.getValue())
                .setDrawType(request.getDrawType())
                .setReqTime(request.getReqTime())
                .setDrawTime(request.getDrawTime())
                .setExt(JSON.toJSONString(withdrawCommonParams))
                .setCreateTime(new Date())
                .setUpdateTime(new Date());
    }

}
