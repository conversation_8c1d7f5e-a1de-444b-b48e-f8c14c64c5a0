package com.kugou.fanxing.recharge.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.model.bo.KugouOpenBusinessBO;
import com.kugou.fanxing.recharge.model.bo.KugouOpenServerBO;
import com.kugou.fanxing.recharge.model.request.KugouOpenDispatchParam;
import com.kugou.fanxing.recharge.model.request.KugouOpenParam;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.fanxing.recharge.util.SignUtils;
import com.kugou.springcloud.http.KugouHttpRequest;
import com.kugou.springcloud.http.KugouHttpResponse;
import com.kugou.springcloud.http.template.KugouHttpTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URISyntaxException;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;

/**
 * 酷狗开放平台服务
 */
@Slf4j
@Service
public class KugouOpenService {

    @Autowired
    private KugouHttpTemplate kgrpcProxy;
    @Autowired
    private ApolloConfigService apolloConfigService;

    /**
     * 解析酷狗开放平台TOKEN获取酷狗ID
     * http://doc.kugou.net/showdoc-master/web/#/7?page_id=3787
     *
     * @param kugouOpenDispatchParam 酷狗开放平台转发参数
     * @return 酷狗ID
     */
    public Optional<Long> getKugouIdByOpenToken(KugouOpenDispatchParam kugouOpenDispatchParam) {
        try {
            Map<String, String> bodyParams = Maps.newHashMap();
            bodyParams.put("openappid", StringUtils.defaultString(kugouOpenDispatchParam.getOpenappid()));
            bodyParams.put("method", StringUtils.defaultString(kugouOpenDispatchParam.getMethod()));
            bodyParams.put("user_openid", StringUtils.defaultString(kugouOpenDispatchParam.getUser_openid()));
            bodyParams.put("access_token", StringUtils.defaultString(kugouOpenDispatchParam.getAccess_token()));
            String jsonBody = JSON.toJSONString(bodyParams);
            String uri = buildRequestUri(kugouOpenDispatchParam, jsonBody);
            KugouHttpRequest kugouHttpRequest = KugouHttpRequest.build()
                    .setUrl(uri)
                    .addHeader("KgrpcHost", "apiopen.kgidc.cn")
                    .setBody(jsonBody.getBytes());
            KugouHttpResponse kugouHttpResponse = kgrpcProxy.post(kugouHttpRequest);
            if (Objects.isNull(kugouHttpResponse) || !kugouHttpResponse.isSuccess() || Objects.isNull(kugouHttpResponse.getContent())) {
                log.warn("酷狗开放平台令牌合法性校验，返回为空。uri: {}, bodyParams: {}, kugouHttpResponse: {}", uri, bodyParams, kugouHttpResponse);
                return Optional.empty();
            }
            String json = new String(kugouHttpResponse.getContent());
            int status = JsonUtils.parseJsonPath(json, "$.status", Integer.class).orElse(0);
            int right = JsonUtils.parseJsonPath(json, "$.data.right", Integer.class).orElse(0);
            long kugouId = JsonUtils.parseJsonPath(json, "$.data.userid", Long.class).orElse(0L);
            if (status != 1 || right != 1 || kugouId <= 0) {
                log.warn("酷狗开放平台令牌合法性校验，校验失败。uri: {}, bodyParams: {}, content: {}", uri, bodyParams, new String(kugouHttpResponse.getContent()));
                return Optional.empty();
            }
            log.warn("酷狗开放平台令牌合法性校验，校验成功。uri: {}, bodyParams: {}, content: {}", uri, bodyParams, new String(kugouHttpResponse.getContent()));
            return Optional.of(kugouId);
        } catch (Exception e) {
            log.warn("酷狗开放平台令牌合法性校验，校验异常。kugouOpenDispatchParam: {}", kugouOpenDispatchParam);
            return Optional.empty();
        }
    }

    public String buildRequestUri(KugouOpenDispatchParam kugouOpenDispatchParam, String jsonBody) throws URISyntaxException {
        URIBuilder uriBuilder = new URIBuilder("/oauth2/introspect");
        KugouOpenServerBO kugouOpenServerBO = this.apolloConfigService.getKugouOpenServerConfig();
        Map<String, String> uriParams = Maps.newHashMap();
        uriParams.put("serverid", kugouOpenServerBO.getServerId());
        uriParams.put("servertime", kugouOpenDispatchParam.getServertime());
        uriParams.put("appid", kugouOpenDispatchParam.getAppid());
        uriParams.put("clientver", kugouOpenDispatchParam.getClientver());
        uriParams.put("mid", kugouOpenDispatchParam.getMid());
        uriParams.put("uuid", kugouOpenDispatchParam.getUuid());
        uriParams.put("dfid", kugouOpenDispatchParam.getDfid());
        uriParams.forEach(uriBuilder::addParameter);
        uriBuilder.addParameter("signature", SignUtils.buildSignByKugouOpenIntrospect(uriParams, jsonBody, kugouOpenServerBO.getServerKey()));
        return uriBuilder.build().toString();
    }

    /**
     * 签名合法性校验
     *
     * @param kugouOpenParam 酷狗开放平台参数
     * @return 签名合法性校验
     */
    public boolean checkKugouOpenParamSign(KugouOpenParam kugouOpenParam) {
        String actualSign = kugouOpenParam.getSignature();
        String expectSign = this.createKugouOpenParamSign(kugouOpenParam);
        if (!actualSign.equals(expectSign)) {
            log.warn("酷狗开放平台入口，签名合法性校验失败。param: {}, expectSign: {}, actualSign: {}", kugouOpenParam, expectSign, actualSign);
            return false;
        }
        return true;
    }

    /**
     * 生成合法性签名
     *
     * @param kugouOpenParam 酷狗开放平台参数
     * @return 签名合法性校验
     */
    public String createKugouOpenParamSign(KugouOpenParam kugouOpenParam) {
        KugouOpenBusinessBO openRechargeBusinessBO = this.apolloConfigService.getOpenRechargeBusinessConfig(kugouOpenParam.getOpenappid());
        TreeMap<String, String> param = new TreeMap<>();
        param.put("nonce", kugouOpenParam.getNonce());
        param.put("access_token", kugouOpenParam.getAccess_token());
        param.put("openappid", kugouOpenParam.getOpenappid());
        param.put("v", kugouOpenParam.getV());
        param.put("user_openid", kugouOpenParam.getUser_openid());
        param.put("timestamp", kugouOpenParam.getTimestamp());
        param.put("business_data", kugouOpenParam.getBusiness_data());
        param.put("method", kugouOpenParam.getMethod());
        return SignUtils.buildSignByKugouOpen(param, openRechargeBusinessBO.getSecretKey());
    }
}