package com.kugou.fanxing.recharge.service.withdraw.transfer;

import com.kugou.fanxing.recharge.constant.WithdrawOrderStatusEnum;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawOrderDao;
import com.kugou.fanxing.recharge.model.DrawCashUpdater;
import com.kugou.fanxing.recharge.service.withdraw.AlipayResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 提现取消
 */
@Slf4j
@Service
public class TransferCanceledService implements TransferService {

    @Autowired
    private WithdrawOrderDao withdrawOrderDao;

    @Override
    public boolean handleAlipayResp(long orderId, AlipayResp alipayResp) {
        if (alipayResp.isTransferCanceled()) {
            log.warn("支付网关提现已经取消, orderId: {}, alipayResp: {}", orderId, alipayResp);
            DrawCashUpdater updater = DrawCashUpdater.build(orderId, alipayResp.getError_code(), alipayResp.getData());
            int affected = this.withdrawOrderDao.updateOrderStatus(WithdrawOrderStatusEnum.CANCEL.getValue(), updater);
            if (affected < 1) {
                log.error("支付网关提现已经取消, 更新提现订单状态失败, orderId: {}, alipayResp: {}", orderId, alipayResp);
                throw new ContextedRuntimeException("支付网关提现已经取消, 更新提现订单失败").setContextValue("orderId", orderId);
            }
            return true;
        }
        return false;
    }
}