package com.kugou.fanxing.recharge.service.callback;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.bo.AccountChangeTypeBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.vo.UserEntity;
import com.kugou.fanxing.recharge.service.stat.UserEverRechargeStatService;
import com.kugou.fanxing.recharge.thrift.callback.PurchaseCoinForIosRequest;
import com.kugou.fanxing.recharge.thrift.callback.PurchaseForIosRequest;
import com.kugou.fanxing.recharge.thrift.callback.RenewalsForIosRequest;
import com.kugou.fanxing.recharge.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;
import java.util.function.UnaryOperator;

@Slf4j
@Service
public class AppleCallbackService extends AbstractCallbackService {

    @Autowired
    private UserEverRechargeStatService userEverRechargeStatService;

    public JsonResult<Map<String, String>> purchaseCoin(PurchaseCoinForIosRequest request) {
        Map<String, String> data = Maps.newHashMap();
        // 检查前置条件
        SysResultCode sysResultCode = checkPrerequisitesForIos(request);
        if (!sysResultCode.isSuccess()) {
            log.warn("苹果充值购买星币回调，检查条件不通过。request: {}", request);
            return JsonResult.result(sysResultCode, data);
        }
        // 绑定直播单号
        Optional<String> optionalRechargeOrderNum = this.rechargeOrderService.specialRechargeOrderNumDeal(request.getOrderNum(), PayTypeIdEnum.PAY_TYPE_ID_1006.getPayTypeId());
        if (!optionalRechargeOrderNum.isPresent()) {
            log.warn("苹果充值购买星币回调，繁星订单号获取失败。request: {}", request);
            return JsonResult.result(SysResultCode.RECHARGE_SYS_ERROR, data);
        }
        String rechargeOrderNum = optionalRechargeOrderNum.get();
        // 读取下单记录
        Optional<RechargeAcrossPO> optionalRechargeAcrossPO = this.rechargeOrderService.queryByRechargeOrderNum(rechargeOrderNum);
        if (!optionalRechargeAcrossPO.isPresent()) {
            log.warn("苹果充值购买星币回调，模拟下单。rechargeOrderNum: {}", rechargeOrderNum);
            RechargeAcrossPO rechargeAcrossPO = mockCreateOrder(request, rechargeOrderNum);
            Map<String, Object> extendParam = Maps.newHashMap();
            extendParam.put("areaId", String.valueOf(request.getAreaId()));
            extendParam.put("businessExt", StringUtils.defaultString(request.getBusinessExt()));
            rechargeAcrossPO.setExtend(this.rechargeCommonService.buildExtendStr(rechargeAcrossPO, extendParam));
            int affected = this.rechargeOrderService.addRechargeOrder(rechargeAcrossPO);
            if (affected < 1) {
                // 考虑客户端收据并发上报，使用insert ignore忽略并发写入
                log.warn("苹果充值购买星币回调，保存下单。rechargeOrderNum: {}, affected: {}", rechargeOrderNum, affected);
            }
            optionalRechargeAcrossPO = Optional.of(rechargeAcrossPO);
        }
        // 检查下单记录
        final int coinType = CoinTypeEnum.getByProductType(request.getProductType());
        RechargeAcrossPO sourceOrder = optionalRechargeAcrossPO.orElseThrow(() -> new AckException(SysResultCode.E_30000007)
                .addContextValue("orderNo", request.getOrderNum()));
        if (sourceOrder.getStatus() == 1) {
            log.warn("苹果充值购买星币回调，订单已经处理过。rechargeOrderNum: {}", rechargeOrderNum);
            //查询星币余额
            data.put("coin", String.valueOf(this.queryUserCoin(request.getKugouId(), coinType)));
            data.put("rechargeOrderNum", StringUtils.defaultString(rechargeOrderNum));
            return JsonResult.result(SysResultCode.SUCCESS, data);
        }
        if (sourceOrder.getKugouId() != request.kugouId) {
            log.warn("苹果充值购买星币回调，充值下单与回调酷狗ID不一致。rechargeOrderNum: {}", rechargeOrderNum);
            return JsonResult.result(SysResultCode.RECHARGE_SYS_ERROR, data);
        }
        // 设置到账记录
        UnaryOperator<RechargeAcrossPO> map2TargetOrder = source -> {
            RechargeAcrossPO target = ModelUtils.fromUnchecked(source, RechargeAcrossPO.class);
            target.setCoin(new BigDecimal(request.getCoin()));
            target.setRealAmount(new BigDecimal(request.getCoin()));
            target.setStatus(1);
            target.setRechargeTime(DateHelper.getCurrentSeconds());
            target.setReType(ReTypeEnum.RETYPE_RECHARGE.getReTypeId());
            target.setRechargeId(orderIdService.generateGlobalId());
            target.setTradeNo(StringUtils.defaultString(request.getTradeNo()));
            target.setTradeTime(DateHelper.parseData(request.getTradeTime()));
            target.setPartner(StringUtils.defaultString(request.getPartner()));
            target.setAreaId(request.getAreaId());
            target.setProductId(StringUtils.defaultString(request.getGoodsId()));
            target.setAreaCode(StringUtils.defaultString(request.getAreaCode()));
            target.setTimeZone(StringUtils.defaultString(request.getTimeZone()));
            target.setCurrency(StringUtils.defaultString(request.getCurrency()));
            target.setCurrencyAmount(ParseUtils.tryParseBigDecimal(request.getCurrencyAmount(), BigDecimal.ZERO));
            target.setUsdAmount(ParseUtils.tryParseBigDecimal(request.getUsdAmount(), BigDecimal.ZERO));
            target.setClientIp(StringUtils.defaultString(request.getClientIp()));
            target.setConsumeOrderNum(StringUtils.defaultString(request.getOutTradeNo()));
            target.setCoinType(coinType);
            return target;
        };
        if (!super.executeIdempotent(sourceOrder, map2TargetOrder)) {
            log.warn("苹果充值购买星币回调，充值调用消费加星币失败。request: {}", request);
            return JsonResult.result(SysResultCode.RECHARGE_SYS_ERROR, data);
        }
        data.put("coin", String.valueOf(this.queryUserCoin(request.getKugouId(), coinType)));
        data.put("rechargeOrderNum", StringUtils.defaultString(rechargeOrderNum));
        log.warn("苹果充值购买星币回调，处理成功。request: {}, data: {}", request, data);
        return JsonResult.result(SysResultCode.SUCCESS, data);
    }

    public double queryUserCoin(long kugouId, int coinType) {
        double userCoin = 0.0;
        try {
            Optional<UserEntity> optionalUserEntity = userEverRechargeStatService.getUserEntity(kugouId,10020 , coinType);
            userCoin = optionalUserEntity.map(UserEntity::getCoin).orElse(userCoin);
        } catch (Exception e) {
            log.error("查询用户星币余额，查询异常。kugouId: {}", kugouId, e);
        }
        return userCoin;
    }

    public RechargeAcrossPO mockCreateOrder(PurchaseCoinForIosRequest request, String rechargeOrderNum) {
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setRechargeId(orderIdService.generateGlobalId());
        rechargeAcrossPO.setAddTime(request.getTime());
        rechargeAcrossPO.setKugouId(request.getKugouId());
        rechargeAcrossPO.setFromKugouId(request.getKugouId());
        rechargeAcrossPO.setAmount(new BigDecimal(request.getMoney()));
        rechargeAcrossPO.setMoney(new BigDecimal(request.getMoney()));
        rechargeAcrossPO.setCoupon(BigDecimal.ZERO);
        rechargeAcrossPO.setCouponId(0);
        rechargeAcrossPO.setCouponOrderId(0);
        rechargeAcrossPO.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_1006.getPayTypeId());
        rechargeAcrossPO.setStatus(0);
        rechargeAcrossPO.setRechargeOrderNum(rechargeOrderNum);
        rechargeAcrossPO.setCoin(new BigDecimal(request.getCoin()));
        rechargeAcrossPO.setRealAmount(new BigDecimal(request.getCoin()));
        rechargeAcrossPO.setCoinBefore(BigDecimal.ZERO);
        rechargeAcrossPO.setCoinAfter(BigDecimal.ZERO);
        rechargeAcrossPO.setAgentKugouId(0);
        rechargeAcrossPO.setRefer(0);
        rechargeAcrossPO.setCFrom(request.getPid());
        rechargeAcrossPO.setChannelId(request.getChannelId());
        rechargeAcrossPO.setExtraJsonData(this.buildExtraJsonData(request.getSignBook(), request.getGoodsId(), Maps.newHashMap()));
        rechargeAcrossPO.setIsSandbox(request.getIsSandbox());
        rechargeAcrossPO.setCoinType(CoinTypeEnum.getByProductType(request.getProductType()));
        return rechargeAcrossPO;
    }

    public String buildExtraJsonData(String signBook, String goodsId, Map<String, String> moreParams) {
        Map<String, String> params = Maps.newHashMap();
        params.put("signBook", StringUtils.defaultString(signBook));
        params.put("goodsId", StringUtils.defaultString(goodsId));
        params.put("fromType", "moblie7");
        params.putAll(moreParams);
        return JSON.toJSONString(params);
    }

    public JsonResult<Map<String, String>> purchaseProducts(PurchaseForIosRequest request) {
        Map<String, String> data = Maps.newHashMap();
        // 绑定苹果与直播交易单号
        Optional<String> optionalRechargeOrderNum = this.rechargeOrderService.specialRechargeOrderNumDeal(request.getOrderNo(), PayTypeIdEnum.PAY_TYPE_ID_1006.getPayTypeId());
        if (!optionalRechargeOrderNum.isPresent()) {
            log.warn("苹果充值购买物品回调，繁星订单号获取失败。request: {}", request);
            return JsonResult.result(SysResultCode.RECHARGE_SYS_ERROR, data);
        }
        String rechargeOrderNum = optionalRechargeOrderNum.get();
        // 读取下单记录（如果下单记录不存在，则模拟下单一次）
        long consumeOrderNo = orderIdService.generateGlobalId();
        Optional<RechargeAcrossPO> optionalRechargeAcrossPO = this.rechargeOrderService.queryByRechargeOrderNum(rechargeOrderNum);
        if (!optionalRechargeAcrossPO.isPresent()) {
            log.warn("苹果充值购买物品回调，模拟购买物品下单。rechargeOrderNum: {}, consumeOrderNo: {}", rechargeOrderNum, consumeOrderNo);
            RechargeAcrossPO rechargeAcrossPO = mockPurchaseOrder(request, rechargeOrderNum, consumeOrderNo);
            int affected = this.rechargeOrderService.addRechargeOrder(rechargeAcrossPO);
            if (affected < 1) {
                // 考虑客户端收据并发上报，使用insert ignore忽略并发写入
                log.warn("苹果充值购买物品回调，保存下单。rechargeOrderNum: {}, affected: {}", rechargeOrderNum, affected);
            }
            optionalRechargeAcrossPO = Optional.of(rechargeAcrossPO);
        }
        // 检查下单记录
        RechargeAcrossPO sourceOrder = optionalRechargeAcrossPO.orElseThrow(() -> new AckException(SysResultCode.E_30000007).addContextValue("orderNo", request.getOrderNo()));
        consumeOrderNo = JsonUtils.parseJsonPathChecked(sourceOrder.getExtraJsonData(), "$.businessId", Long.class);
        data.put("businessId", String.valueOf(consumeOrderNo));
        data.put("rechargeOrderNum", StringUtils.defaultString(rechargeOrderNum));
        if (sourceOrder.getStatus() == 1) {
            log.warn("苹果充值购买物品回调，订单已经处理过。rechargeOrderNum: {}, data: {}", rechargeOrderNum, data);
            data.put("coin", String.valueOf(this.queryUserCoin(request.getKugouId(), CoinTypeEnum.STAR_COIN.getCoinType())));
            return JsonResult.result(SysResultCode.SUCCESS, data);
        }
        if (sourceOrder.getKugouId() != request.getKugouId()) {
            log.warn("苹果充值购买星币回调，充值下单与回调酷狗ID不一致。rechargeOrderNum: {}", rechargeOrderNum);
            return JsonResult.result(SysResultCode.RECHARGE_SYS_ERROR, data);
        }
        // 设置到账记录
        RechargeAcrossPO targetOrder = buildTargetOrder(request, sourceOrder);
        // 调用消费充扣
        boolean flag = consumeRpcService.rechargeFee(targetOrder, request.getConsumeParam(), request.getClientIp());
        if (!flag) {
            log.warn("苹果充值购买物品回调，调用消费充扣失败。targetOrder: {}, callbackOrder: {}", targetOrder, targetOrder);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        // 保存成功流水
        boolean isSuccess = this.saveCallbackSuccessOrder(targetOrder);
        if (!isSuccess) {
            log.warn("苹果充值购买物品回调，保存成功流水失败。targetOrder: {}, callbackOrder: {}", targetOrder, targetOrder);
            return JsonResult.result(SysResultCode.SUCCESS, data);
        }
        AccountChangeTypeBO accountChangeTypeBO = apolloConfigService.getAccountChangeTypeById(request.getConsumeParam().getAccountChangeType());
        if (StringUtils.isNotBlank(accountChangeTypeBO.getTopic()) && !this.afterRechargeService.sendPurchaseTopicForIos(accountChangeTypeBO.getTopic(), targetOrder, request)) {
            log.warn("苹果充值购买物品回调，通知业务到账失败。originalOrder: {}, callbackOrder: {}", sourceOrder, targetOrder);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        afterRechargeService.afterRechargeSuccess(targetOrder);
        return JsonResult.result(SysResultCode.SUCCESS, data);
    }

    /**
     * \Fx\BLL\Recharge\RenewalBL::callbackByIOS($_REQUEST)
     */
    public JsonResult<Map<String, String>> renewalsProducts(RenewalsForIosRequest request) {
        Map<String, String> data = Maps.newHashMap();
        // 绑定苹果与直播交易单号
        Optional<String> optionalRechargeOrderNum = this.rechargeOrderService.specialRechargeOrderNumDeal(request.getOrderNo(), PayTypeIdEnum.PAY_TYPE_ID_1006.getPayTypeId());
        if (!optionalRechargeOrderNum.isPresent()) {
            log.warn("苹果充值续费物品回调，繁星订单号获取失败。request: {}", request);
            return JsonResult.result(SysResultCode.RECHARGE_SYS_ERROR, data);
        }
        String rechargeOrderNum = optionalRechargeOrderNum.get();
        // 读取下单记录（如果下单记录不存在，则模拟下单一次）
        long consumeOrderNo = orderIdService.generateGlobalId();
        Optional<RechargeAcrossPO> optionalRechargeAcrossPO = this.rechargeOrderService.queryByRechargeOrderNum(rechargeOrderNum);
        if (!optionalRechargeAcrossPO.isPresent()) {
            log.warn("苹果充值续费物品回调，模拟购买物品下单。rechargeOrderNum: {}, consumeOrderNo: {}", rechargeOrderNum, consumeOrderNo);
            RechargeAcrossPO rechargeAcrossPO = mockPurchaseOrder(request, rechargeOrderNum, consumeOrderNo);
            int affected = this.rechargeOrderService.addRechargeOrder(rechargeAcrossPO);
            if (affected < 1) {
                // 考虑客户端收据并发上报，使用insert ignore忽略并发写入
                log.warn("苹果充值续费物品回调，保存下单。rechargeOrderNum: {}, affected: {}", rechargeOrderNum, affected);
            }
            optionalRechargeAcrossPO = Optional.of(rechargeAcrossPO);
        }
        // 检查下单记录
        RechargeAcrossPO sourceOrder = optionalRechargeAcrossPO.orElseThrow(() -> new AckException(SysResultCode.E_30000007)
                .addContextValue("orderNo", request.getOrderNo()));
        consumeOrderNo = JsonUtils.parseJsonPathChecked(sourceOrder.getExtraJsonData(), "$.businessId", Long.class);
        data.put("businessId", String.valueOf(consumeOrderNo));
        data.put("rechargeOrderNum", StringUtils.defaultString(rechargeOrderNum));
        if (sourceOrder.getStatus() == 1) {
            log.warn("苹果充值续费物品回调，订单已经处理过。rechargeOrderNum: {}, data: {}", rechargeOrderNum, data);
            data.put("coin", String.valueOf(this.queryUserCoin(request.getKugouId(), CoinTypeEnum.STAR_COIN.getCoinType())));
            return JsonResult.result(SysResultCode.SUCCESS, data);
        }
        if (sourceOrder.getKugouId() != request.getKugouId()) {
            log.warn("苹果充值续费物品回调，充值下单与回调酷狗ID不一致。rechargeOrderNum: {}", rechargeOrderNum);
            return JsonResult.result(SysResultCode.RECHARGE_SYS_ERROR, data);
        }
        // 设置到账记录
        RechargeAcrossPO targetOrder = buildTargetOrder(request, sourceOrder);
        // 调用消费充扣
        boolean flag = consumeRpcService.rechargeFee(targetOrder, request.getConsumeParam(), request.getClientIp());
        if (!flag) {
            log.warn("苹果充值续费物品回调，调用消费充扣失败。targetOrder: {}, callbackOrder: {}", targetOrder, targetOrder);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        // 保存成功流水
        boolean isSuccess = this.saveCallbackSuccessOrder(targetOrder);
        if (!isSuccess) {
            log.warn("苹果充值续费物品回调，保存成功流水失败。targetOrder: {}, callbackOrder: {}", targetOrder, targetOrder);
            return JsonResult.result(SysResultCode.SUCCESS, data);
        }
        if (StringUtils.isNotBlank(request.getTopic()) && !this.afterRechargeService.sendRenewalsTopicForIos(request.getTopic(), targetOrder, request)) {
            log.warn("苹果充值续费物品回调，通知业务到账失败。originalOrder: {}, callbackOrder: {}", sourceOrder, targetOrder);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        afterRechargeService.afterRechargeSuccess(targetOrder);
        return JsonResult.result(SysResultCode.SUCCESS, data);
    }

    public RechargeAcrossPO buildTargetOrder(PurchaseForIosRequest request, RechargeAcrossPO sourceOrder) {
        UnaryOperator<RechargeAcrossPO> map2TargetOrder = source -> {
            RechargeAcrossPO target = ModelUtils.fromUnchecked(source, RechargeAcrossPO.class);
            target.setRechargeId(orderIdService.generateGlobalId());
            target.setCoin(new BigDecimal(request.getCoin()));
            target.setRealAmount(new BigDecimal(request.getCoin()));
            target.setAmount(new BigDecimal(request.getMoney()));
            target.setMoney(new BigDecimal(request.getMoney()));
            target.setStatus(1);
            target.setRechargeTime(DateHelper.getCurrentSeconds());
            target.setTradeNo(StringUtils.defaultString(request.getTradeNo()));
            target.setTradeTime(DateHelper.parseData(request.getTradeTime()));
            target.setPartner(StringUtils.defaultString(request.getPartner()));
            target.setAreaId(request.getAreaId());
            String extend = this.rechargeCommonService.buildExtendStrForIosPurchase(target, request.getConsumeParam(), request.getBusinessExt(), request.getClientIp());
            target.setExtend(extend);
            target.setProductId(StringUtils.defaultString(request.getGoodsId()));
            target.setAreaCode(StringUtils.defaultString(request.getAreaCode()));
            target.setTimeZone(StringUtils.defaultString(request.getTimeZone()));
            target.setCurrency(StringUtils.defaultString(request.getCurrency()));
            target.setCurrencyAmount(ParseUtils.tryParseBigDecimal(request.getCurrencyAmount(), BigDecimal.ZERO));
            target.setUsdAmount(ParseUtils.tryParseBigDecimal(request.getUsdAmount(), BigDecimal.ZERO));
            target.setClientIp(StringUtils.defaultString(request.getClientIp()));
            target.setConsumeOrderNum(StringUtils.defaultString(request.getOutTradeNo()));
            return target;
        };
        return map2TargetOrder.apply(sourceOrder);
    }

    public RechargeAcrossPO buildTargetOrder(RenewalsForIosRequest request, RechargeAcrossPO sourceOrder) {
        UnaryOperator<RechargeAcrossPO> map2TargetOrder = source -> {
            RechargeAcrossPO target = ModelUtils.fromUnchecked(source, RechargeAcrossPO.class);
            target.setRechargeId(orderIdService.generateGlobalId());
            target.setCoin(new BigDecimal(request.getCoin()));
            target.setRealAmount(new BigDecimal(request.getCoin()));
            target.setAmount(new BigDecimal(request.getMoney()));
            target.setMoney(new BigDecimal(request.getMoney()));
            target.setStatus(1);
            target.setReType(ReTypeEnum.RETYPE_RENEWALS.getReTypeId());
            target.setRechargeTime(DateHelper.getCurrentSeconds());
            target.setTradeNo(StringUtils.defaultString(request.getTradeNo()));
            target.setTradeTime(DateHelper.parseData(request.getTradeTime()));
            target.setPartner(StringUtils.defaultString(request.getPartner()));
            target.setAreaId(request.getAreaId());
            String extend = this.rechargeCommonService.buildExtendStrForIosPurchase(target, request.getConsumeParam(), request.getBusinessExt(), request.getClientIp());
            target.setExtend(extend);
            target.setProductId(StringUtils.defaultString(request.getGoodsId()));
            target.setAreaCode(StringUtils.defaultString(request.getAreaCode()));
            target.setTimeZone(StringUtils.defaultString(request.getTimeZone()));
            target.setCurrency(StringUtils.defaultString(request.getCurrency()));
            target.setCurrencyAmount(ParseUtils.tryParseBigDecimal(request.getCurrencyAmount(), BigDecimal.ZERO));
            target.setUsdAmount(ParseUtils.tryParseBigDecimal(request.getUsdAmount(), BigDecimal.ZERO));
            target.setClientIp(StringUtils.defaultString(request.getClientIp()));
            target.setConsumeOrderNum(StringUtils.defaultString(request.getOutTradeNo()));
            return target;
        };
        return map2TargetOrder.apply(sourceOrder);
    }

    public RechargeAcrossPO mockPurchaseOrder(PurchaseForIosRequest request, String rechargeOrderNum, long consumeOrderNo) {
        Map<String, String> moreParams = Maps.newHashMap();
        moreParams.put("businessId", String.valueOf(consumeOrderNo));
        String extraJsonData = this.buildExtraJsonData(request.getSignBook(), request.getGoodsId(), moreParams);
        return new RechargeAcrossPO()
                .setRechargeId(orderIdService.generateGlobalId())
                .setConsumeOrderNum("")
                .setBusinessId(StringUtils.defaultString(request.getBusinessId()))
                .setReType(ReTypeEnum.RETYPE_PURCHASE.getReTypeId())
                .setAddTime(request.getTime())
                .setKugouId(request.getKugouId())
                .setFromKugouId(request.getKugouId())
                .setAmount(new BigDecimal(request.getMoney()))
                .setMoney(new BigDecimal(request.getMoney()))
                .setCoupon(BigDecimal.ZERO)
                .setCouponId(0)
                .setCouponOrderId(0)
                .setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_1006.getPayTypeId())
                .setStatus(0)
                .setRechargeOrderNum(rechargeOrderNum)
                .setCoin(new BigDecimal(request.getCoin()))
                .setRealAmount(new BigDecimal(request.getCoin()))
                .setCoinBefore(BigDecimal.ZERO)
                .setCoinAfter(BigDecimal.ZERO)
                .setAgentKugouId(0)
                .setRefer(0)
                .setIsSandbox(request.getIsSandbox())
                .setCFrom(request.getPid())
                .setChannelId(request.getChannelId())
                .setExtraJsonData(extraJsonData);
    }

    public RechargeAcrossPO mockPurchaseOrder(RenewalsForIosRequest request, String rechargeOrderNum, long consumeOrderNo) {
        Map<String, String> moreParams = Maps.newHashMap();
        moreParams.put("businessId", String.valueOf(consumeOrderNo));
        String extraJsonData = this.buildExtraJsonData(request.getSignBook(), request.getGoodsId(), moreParams);
        return new RechargeAcrossPO()
                .setRechargeId(orderIdService.generateGlobalId())
                .setConsumeOrderNum("")
                .setBusinessId(StringUtils.defaultString(request.getBusinessId()))
                .setReType(ReTypeEnum.RETYPE_RENEWALS.getReTypeId())
                .setAddTime(request.getTime())
                .setKugouId(request.getKugouId())
                .setFromKugouId(request.getKugouId())
                .setAmount(new BigDecimal(request.getMoney()))
                .setMoney(new BigDecimal(request.getMoney()))
                .setCoupon(BigDecimal.ZERO)
                .setCouponId(0)
                .setCouponOrderId(0)
                .setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_1006.getPayTypeId())
                .setStatus(0)
                .setRechargeOrderNum(rechargeOrderNum)
                .setCoin(new BigDecimal(request.getCoin()))
                .setRealAmount(new BigDecimal(request.getCoin()))
                .setCoinBefore(BigDecimal.ZERO)
                .setCoinAfter(BigDecimal.ZERO)
                .setAgentKugouId(0)
                .setRefer(0)
                .setIsSandbox(request.getIsSandbox())
                .setCFrom(request.getPid())
                .setChannelId(request.getChannelId())
                .setExtraJsonData(extraJsonData);
    }
}