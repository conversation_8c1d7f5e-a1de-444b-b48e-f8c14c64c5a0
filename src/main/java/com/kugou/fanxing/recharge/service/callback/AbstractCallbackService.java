package com.kugou.fanxing.recharge.service.callback;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.*;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.AppStoreReceiptDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeOpenDao;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.BaseCallbackDTO;
import com.kugou.fanxing.recharge.model.bo.CouponBO;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.bo.ReceiptInfoBO;
import com.kugou.fanxing.recharge.model.dto.CoinCallbackDTO;
import com.kugou.fanxing.recharge.model.dto.RechargeExtendDTO;
import com.kugou.fanxing.recharge.model.po.AppStoreReceiptPO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.mq.PulsarRechargeSuccessProducer;
import com.kugou.fanxing.recharge.service.*;
import com.kugou.fanxing.recharge.service.after.AfterRechargeService;
import com.kugou.fanxing.recharge.service.common.ConsumeRpcService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.service.recharge.GooglePlayRechargeService;
import com.kugou.fanxing.recharge.thrift.callback.PurchaseCoinForIosRequest;
import com.kugou.fanxing.recharge.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import java.math.BigDecimal;
import java.util.Enumeration;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.UnaryOperator;

import static com.kugou.fanxing.recharge.constant.SysResultCode.E_40000212;

@Slf4j
public class AbstractCallbackService implements CallbackService {

    @Autowired
    protected RechargeConfig rechargeConfig;
    @Autowired
    protected ValidatingService validatingService;
    @Autowired
    protected RechargeAcrossDao rechargeAcrossDao;
    @Autowired
    protected RechargeOpenDao rechargeOpenDao;
    @Autowired
    protected OrderIdService orderIdService;
    @Autowired
    protected PayPalRechargeService payPalRechargeService;
    @Autowired
    protected UserFacadeService userFacadeService;
    @Autowired
    protected RechargeCommonService rechargeCommonService;
    @Autowired
    protected RechargeCouponService rechargeCouponService;
    @Autowired
    protected ConsumeRpcService consumeRpcService;
    @Autowired
    protected AfterRechargeService afterRechargeService;
    @Autowired
    protected ApolloConfigService apolloConfigService;
    @Autowired
    protected RechargeOrderService rechargeOrderService;
    @Autowired
    protected AppStoreReceiptDao appStoreReceiptDao;
    @Autowired
    protected GooglePlayRechargeService googlePlayRechargeService;
    @Autowired
    protected PulsarRechargeSuccessProducer pulsarRechargeSuccessProducer;

    public SysResultCode handleOpenCallbackOrder(CoinCallbackDTO coinCallbackDTO, Function<RechargeAcrossPO, SysResultCode> function) {
        // 充值用户
        long kugouId = coinCallbackDTO.getUserid();
        // 充值金额，单位：分
        BigDecimal realAmount = coinCallbackDTO.getTotal_fee().multiply(BigDecimal.valueOf(100));
        RechargeAcrossPO callbackOrder = makeOrderByKugouPayRequest(kugouId, realAmount, coinCallbackDTO);
        SysResultCode sysResultCode = function.apply(callbackOrder);
        if (!sysResultCode.isSuccess()) {
            log.warn("酷狗支付网关充值回调，通知业务发货失败。coinCallbackDTO: {}", coinCallbackDTO);
            return sysResultCode;
        }
        callbackOrder.setRechargeTime(DateHelper.getCurrentSeconds());
        String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(callbackOrder.getRechargeOrderNum());
        RechargeAcrossPO rechargeAcrossPO = this.rechargeOpenDao.queryByRechargeOrderNum(month, callbackOrder.getRechargeOrderNum());
        if (Objects.nonNull(rechargeAcrossPO) && rechargeAcrossPO.getStatus() == 1) {
            log.warn("酷狗支付网关充值回调，重复回调返回成功。coinCallbackDTO: {}", coinCallbackDTO);
            return SysResultCode.SUCCESS;
        }
        int affected = this.rechargeOpenDao.add(month, callbackOrder);
        if (affected < 1) {
            log.warn("酷狗支付网关充值回调，保存订单失败。month: {}，callbackOrder: {}", month, callbackOrder);
            return SysResultCode.RECHARGE_SYS_ERROR;
        }
        log.warn("酷狗支付网关充值回调，保存订单成功。coinCallbackDTO: {}", coinCallbackDTO);
        return SysResultCode.SUCCESS;
    }

    public RechargeAcrossPO makeOrderByKugouPayRequest(long kugouId, BigDecimal realAmount, CoinCallbackDTO callback) {
        String base64Extend = this.parseBase64Extend(callback.getExtend());
        String extend = new String(Base64.decodeBase64(StringUtils.defaultString(base64Extend)));
        log.warn("酷狗支付网关充值回调，透传扩展参数内容。kugouId: {}, extend: {}", kugouId, extend);
        RechargeExtendDTO rechargeExtendDTO = parseRechargeExtendDTO(extend);
        log.warn("酷狗支付网关充值回调，透传扩展参数内容。kugouId: {}, rechargeExtendDTO: {}", kugouId, rechargeExtendDTO);
        // 注意⚠️：酷狗支付网关存储的amount单位为美元，酷狗直播存储的amount为人民币，因此需要按照比例转换为人民币后存储。
        int payTypeId = rechargeExtendDTO.getPayTypeId();
        BigDecimal amount = rechargeExtendDTO.getAmount();
        BigDecimal rmbAmount = PayTypeIdEnum.isPayPalRecharge(payTypeId) ? payPalRechargeService.getRmbAmount(amount) : amount;
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setRechargeId(orderIdService.generateGlobalId());
        rechargeAcrossPO.setRechargeOrderNum(callback.getOrder_no());
        rechargeAcrossPO.setConsumeOrderNum(callback.getOut_trade_no());
        rechargeAcrossPO.setAddTime(rechargeExtendDTO.getAddTime());
        rechargeAcrossPO.setKugouId(kugouId);
        rechargeAcrossPO.setFromKugouId(callback.getUserid());
        rechargeAcrossPO.setAgentKugouId(rechargeExtendDTO.getAgentKugouId());
        rechargeAcrossPO.setCoinBefore(BigDecimal.ZERO);
        rechargeAcrossPO.setCoin(realAmount);
        rechargeAcrossPO.setCoinAfter(BigDecimal.ZERO);
        rechargeAcrossPO.setAmount(rmbAmount);
        rechargeAcrossPO.setRealAmount(realAmount);
        rechargeAcrossPO.setMoney(rechargeExtendDTO.getMoney());
        rechargeAcrossPO.setPayTypeId(payTypeId);
        rechargeAcrossPO.setStatus(1);
        rechargeAcrossPO.setRefer(rechargeExtendDTO.getRefer());
        rechargeAcrossPO.setCFrom(rechargeExtendDTO.getCFrom());
        rechargeAcrossPO.setChannelId(rechargeExtendDTO.getChannelId());
        rechargeAcrossPO.setReType(ReTypeEnum.RETYPE_RECHARGE.getReTypeId());
        rechargeAcrossPO.setServerRoom(rechargeConfig.getDataCenterZoneId());
        rechargeAcrossPO.setCoupon(BigDecimal.ZERO);
        rechargeAcrossPO.setCouponId(rechargeExtendDTO.getCouponId());
        rechargeAcrossPO.setCouponOrderId(rechargeExtendDTO.getCouponOrderId());
        rechargeAcrossPO.setCoupon(rechargeExtendDTO.getCoupon());
        rechargeAcrossPO.setExtend(StringUtils.defaultString(base64Extend));
        rechargeAcrossPO.setPartner(StringUtils.defaultString(callback.getPartner()));
        rechargeAcrossPO.setTradeNo(StringUtils.defaultString(callback.getTrade_no()));
        rechargeAcrossPO.setTradeTime(parseTradeTime(callback.getTrade_time()));
        rechargeAcrossPO.setBusinessId(StringUtils.defaultString(rechargeExtendDTO.getBusinessId()));
        rechargeAcrossPO.setBusinessOrderNo(StringUtils.defaultString(rechargeExtendDTO.getBusinessOrderNo()));
        rechargeAcrossPO.setAreaId(rechargeExtendDTO.getAreaId());
        if(!StringUtils.isBlank(rechargeExtendDTO.getProductId())){
            rechargeAcrossPO.setProductId(rechargeExtendDTO.getProductId());
        }
        rechargeAcrossPO.setAreaCode(rechargeExtendDTO.getAreaCode());
        rechargeAcrossPO.setTimeZone(rechargeExtendDTO.getTimeZone());
        rechargeAcrossPO.setCurrency(rechargeExtendDTO.getCurrency());
        rechargeAcrossPO.setCurrencyAmount(rechargeExtendDTO.getCurrencyAmount());
        rechargeAcrossPO.setUsdAmount(rechargeExtendDTO.getUsdAmount());
        rechargeAcrossPO.setClientIp(rechargeExtendDTO.getClientIp());
        rechargeAcrossPO.setCoinType(rechargeExtendDTO.getCoinType());
        rechargeAcrossPO.setIsSandbox(rechargeExtendDTO.getIsSandbox());
        rechargeAcrossPO.setAppVersion(rechargeExtendDTO.getAppVersion());
        if (PayTypeIdEnum.isAppleApp(payTypeId)) {
            Map<String, String> extraData = Maps.newHashMap();
            extraData.put("fromType", "moblie7");
            extraData.put("goodsId", rechargeExtendDTO.getProductId());
            extraData.put("bundleId", rechargeExtendDTO.getBundleId());
            rechargeAcrossPO.setExtraJsonData(JsonUtils.toJSONString(extraData));
        }
        if(PayTypeIdEnum.isGooglePlayRecharge(payTypeId)){
            String extraData = new String(Base64.decodeBase64(StringUtils.defaultString(callback.getExtra_data())));
            log.warn("酷狗支付网关充值回调，gp扩展参数内容。kugouId: {}, extend: {}", kugouId, extraData);
            String  regionCode = JsonUtils.parseJsonPath(extraData, "$.region_code", String.class, "");
            rechargeAcrossPO.setPaymentAreaCode(regionCode);
            String  actCurrency = JsonUtils.parseJsonPath(extraData, "$.act_currency", String.class, "");
            if(!StringUtils.isBlank(actCurrency)){
                rechargeAcrossPO.setCurrency(actCurrency);
            }
            BigDecimal  actPrice = JsonUtils.parseJsonPath(extraData, "$.act_price", BigDecimal.class, BigDecimal.ZERO);
            if(actPrice.compareTo(BigDecimal.ZERO) > 0){
                rechargeAcrossPO.setCurrencyAmount(actPrice);
            }
            Boolean isSanBox = JsonUtils.parseJsonPathChecked(extraData, "$.is_sanbox", Boolean.class);
            rechargeAcrossPO.setIsSandbox(isSanBox ? 1 : 0);
        }
        if (PayTypeIdEnum.isAirwallexRecharge(payTypeId)) {
            String extraData = new String(Base64.decodeBase64(StringUtils.defaultString(callback.getExtra_data())));
            rechargeAcrossPO.setExtraJsonData(extraData);
        }
        log.warn("酷狗支付网关充值回调，初始化回调订单。rechargeAcrossPO: {}", rechargeAcrossPO);
        return rechargeAcrossPO;
    }

    public RechargeExtendDTO parseRechargeExtendDTO(String extend) {
        long kugouId = JsonUtils.parseJsonPath(extend, "$.callBackArg.kugouId", Long.class, 0L);
        long agentKugouId = JsonUtils.parseJsonPath(extend, "$.callBackArg.agentKugouId", Long.class, 0L);
        BigDecimal money = JsonUtils.parseJsonPathChecked(extend, "$.callBackArg.money", BigDecimal.class);
        BigDecimal coupon = JsonUtils.parseJsonPath(extend, "$.callBackArg.coupon", BigDecimal.class, BigDecimal.ZERO);
        String couponId = StringUtils.defaultIfBlank(JsonUtils.parseJsonPath(extend, "$.callBackArg.couponId", String.class, "0"), "0");
        long couponOrderId = JsonUtils.parseJsonPath(extend, "$.callBackArg.couponOrderId", Long.class, 0L);
        int payTypeId = JsonUtils.parseJsonPathChecked(extend, "$.callBackArg.payTypeId", Integer.class);
        int addTime = JsonUtils.parseJsonPathChecked(extend, "$.callBackArg.addTime", Integer.class);
        int refer = JsonUtils.parseJsonPath(extend, "$.callBackArg.refer", Integer.class, 0);
        int cFrom = JsonUtils.parseJsonPath(extend, "$.callBackArg.cFrom", Integer.class, 0);
        int channelId = JsonUtils.parseJsonPath(extend, "$.callBackArg.channelId", Integer.class, 0);
        BigDecimal amount = JsonUtils.parseJsonPathChecked(extend, "$.callBackArg.amount", BigDecimal.class);
        String businessId = JsonUtils.parseJsonPath(extend, "$.callBackArg.businessId", String.class, "");
        String userOpenid = JsonUtils.parseJsonPath(extend, "$.callBackArg.userOpenid", String.class, "");
        String productId = JsonUtils.parseJsonPath(extend, "$.callBackArg.productId", String.class, "");
        String areaCode = JsonUtils.parseJsonPath(extend, "$.callBackArg.areaCode", String.class, "");
        String timeZone = JsonUtils.parseJsonPath(extend, "$.callBackArg.timeZone", String.class, "");
        String currency = JsonUtils.parseJsonPath(extend, "$.callBackArg.currency", String.class, "");
        BigDecimal currencyAmount = JsonUtils.parseJsonPath(extend, "$.callBackArg.currencyAmount", BigDecimal.class,BigDecimal.ZERO);
        BigDecimal usdAmount = JsonUtils.parseJsonPath(extend, "$.callBackArg.usdAmount", BigDecimal.class,BigDecimal.ZERO);
        String clientIp = JsonUtils.parseJsonPath(extend, "$.callBackArg.clientIp", String.class, "");
        String version = JsonUtils.parseJsonPath(extend, "$.callBackArg.version", String.class, "");
        int areaId = JsonUtils.parseJsonPath(extend, "$.callBackArg.areaId", Integer.class, 0);
        BigDecimal coin = JsonUtils.parseJsonPath(extend, "$.callBackArg.coin", BigDecimal.class, BigDecimal.ZERO);
        int coinType = JsonUtils.parseJsonPath(extend, "$.callBackArg.coinType", Integer.class, 0);
        String businessOrderNo = JsonUtils.parseJsonPath(extend, "$.callBackArg.businessOrderNo", String.class, "");
        int isSandbox = JsonUtils.parseJsonPath(extend, "$.callBackArg.isSandbox", Integer.class, 0);
        String appVersion = JsonUtils.parseJsonPath(extend, "$.callBackArg.appVersion", String.class, "");
        String bundleId = JsonUtils.parseJsonPath(extend, "$.callBackArg.bundleId", String.class, "");
        RechargeExtendDTO rechargeExtendDTO = new RechargeExtendDTO();
        rechargeExtendDTO.setKugouId(kugouId);
        rechargeExtendDTO.setAgentKugouId(agentKugouId);
        rechargeExtendDTO.setMoney(money);
        rechargeExtendDTO.setCoupon(coupon);
        rechargeExtendDTO.setCouponId(Long.parseLong(couponId));
        rechargeExtendDTO.setCouponOrderId(couponOrderId);
        rechargeExtendDTO.setPayTypeId(payTypeId);
        rechargeExtendDTO.setAddTime(addTime);
        rechargeExtendDTO.setRefer(refer);
        rechargeExtendDTO.setCFrom(cFrom);
        rechargeExtendDTO.setChannelId(channelId);
        rechargeExtendDTO.setAmount(amount);
        rechargeExtendDTO.setBusinessId(businessId);
        rechargeExtendDTO.setBusinessOrderNo(businessOrderNo);
        rechargeExtendDTO.setUserOpenid(userOpenid);
        rechargeExtendDTO.setProductId(productId);
        rechargeExtendDTO.setAreaId(areaId);
        rechargeExtendDTO.setAreaCode(areaCode);
        rechargeExtendDTO.setTimeZone(timeZone);
        rechargeExtendDTO.setCurrency(currency);
        rechargeExtendDTO.setCurrencyAmount(currencyAmount);
        rechargeExtendDTO.setUsdAmount(usdAmount);
        rechargeExtendDTO.setClientIp(clientIp);
        rechargeExtendDTO.setVersion(version);
        rechargeExtendDTO.setCoin(coin);
        rechargeExtendDTO.setCoinType(coinType);
        rechargeExtendDTO.setIsSandbox(isSandbox);
        rechargeExtendDTO.setAppVersion(appVersion);
        rechargeExtendDTO.setBundleId(bundleId);
        return rechargeExtendDTO;
    }

    /**
     * 保存充值成功订单
     *
     * @param callbackOrder 充值成功订单
     * @return              是否保存成功
     */
    public boolean saveCallbackSuccessOrder(RechargeAcrossPO callbackOrder) {
        String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(callbackOrder.getRechargeOrderNum());
        boolean rs = this.rechargeAcrossDao.insertIgnore(month, callbackOrder) > 0;
        if(rs) {
            sendPulsarMessage(callbackOrder);
        }
        return rs;
    }

    public boolean sendPulsarMessage(RechargeAcrossPO data) {
        try {
            return pulsarRechargeSuccessProducer.sendPostRechargeMsg(data);
        }catch (Exception e) {
            log.error("发送充值后pulsar消息, 发送消息失败。data: {},error:", data,e);
            return false;
        }
    }

    @Transactional(transactionManager = "transactionManager_d_fanxing_recharge", propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean saveGameCallbackSuccessOrder(RechargeAcrossPO targetOrder, String receiptData, ReceiptInfoBO receiptInfoBO) {
        ReceiptInfoBO.Receipts receipts = receiptInfoBO.getReceipt();
        AppStoreReceiptPO source = new AppStoreReceiptPO();
        source.setGlobalId(orderIdService.generateGlobalId());
        source.setRechargeOrderNum(targetOrder.getRechargeOrderNum());
        source.setReceiptMd5(DigestUtils.md5Hex(receiptData));
        source.setReceiptData(receiptData);
        source.setBid(receipts.getBid());
        source.setProductId(receipts.getProduct_id());
        source.setStatus(1);
        source.setOriginalTransactionId(receipts.getOriginal_transaction_id());
        source.setTransactionId(receipts.getTransaction_id());
        source.setOriginalPurchaseDateMs(ParseUtils.tryParseLong(receipts.getOriginal_purchase_date_ms(), 0));
        source.setPurchaseDateMs(ParseUtils.tryParseLong(receipts.getPurchase_date_ms(), 0));
        source.setAddTime(DateHelper.getCurrentSeconds());
        this.appStoreReceiptDao.insertIgnore(source);
        String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(targetOrder.getRechargeOrderNum());
        return this.rechargeOpenDao.insertIgnore(month, targetOrder) >= 0;
    }

    /**
     * 支付网关回调检查前置条件
     *
     * @param callbackDTO 业务请求对象
     * @return 系统返回代码
     */
    public SysResultCode checkPrerequisites(BaseCallbackDTO callbackDTO) {
        // 检查网关参数
        Optional<ConstraintViolation<BaseCallbackDTO>> optionalConstraintViolation = this.validatingService.checkViolation(callbackDTO);
        if (optionalConstraintViolation.isPresent()) {
            log.warn("酷狗支付网关充值回调，支付网关参数错误。callbackDTO: {}, constraintViolation: {}", callbackDTO, optionalConstraintViolation.get());
            return SysResultCode.RECHARGE_PARAM_ERROR;
        }
        // 检查参数签名
        boolean isValidCallbackSign = checkSignOfPcCallBack(callbackDTO);
        if (!isValidCallbackSign) {
            log.warn("酷狗支付网关充值回调，支付网关签名错误。callbackDTO: {}", callbackDTO);
            return SysResultCode.E_30000009;
        }
        // 回查网关订单
        boolean isValidNotify = checkCallbackNotify(callbackDTO);
        if (!isValidNotify) {
            log.warn("酷狗支付网关充值回调，支付网关订单错误。callbackDTO: {}", callbackDTO);
            return SysResultCode.E_30000009;
        }
        // 检查透传签名
        String base64Extend = this.parseBase64Extend(callbackDTO.getExtend());
        boolean isValidExtendSign = checkCallbackExtendSign(callbackDTO.getUserid(), callbackDTO.getOrder_no(), base64Extend);
        if (!isValidExtendSign) {
            log.warn("酷狗支付网关充值回调，检查透传签名错误。callbackDTO: {}", callbackDTO);
            return SysResultCode.E_30000009;
        }
        return SysResultCode.SUCCESS;
    }

    /**
     * 注意⚠️：支付网关的 extend 参数为业务透传参数，即下单传递的业务参数在支付回调时原封不动的返回。
     * 由于支付网关研发没有遵守协议，对extend的内容进行了修改和追加，导致需要做如下的兼容处理。
     * 如果extend为json格式，按被追加内容的extend进行解析处理，否则，使用原始内容。
     */
    protected String parseBase64Extend(String extend) {
        boolean isValidJson = JsonUtils.isValidJson(extend);
        if (isValidJson) {
            return JsonUtils.parseJsonPath(extend, "$.extend", String.class).orElse(extend);
        }
        return extend;
    }

    /**
     * 检查酷狗支付网关回调签名
     *
     * @param callbackDTO 业务参数
     * @return 是否合法签名
     */
    public boolean checkSignOfPcCallBack(BaseCallbackDTO callbackDTO) {
        return this.checkSignOfPcCallBack(callbackDTO.getAppid(), callbackDTO.getSign());
    }

    /**
     * 检查酷狗支付网关回调签名
     *
     * @param kupayAppId 支付网关分配业务ID
     * @param actualSign 支付网关回调签名
     * @return
     */
    public boolean checkSignOfPcCallBack(int kupayAppId, String actualSign) {
        HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
        KupayAppInfoBO kupayAppInfoBO = this.rechargeConfig.getKupayAppIdByAppId(kupayAppId);
        Enumeration<String> parameterNames = request.getParameterNames();
        Map<String, String> params = Maps.newHashMap();
        while (parameterNames.hasMoreElements()) {
            String name = parameterNames.nextElement();
            if (!Sets.newHashSet("sign", "action").contains(name)) {
                params.put(name, request.getParameter(name));
            }
        }
        String expectSign = SignUtils.buildSign(params, kupayAppInfoBO.getKupayAppKey());
        log.warn("酷狗支付网关充值回调，检查酷狗支付网关回调签名。isValidSign: {}, actualSign: {}, expectSign: {}, signParams: {}",
                actualSign.equalsIgnoreCase(expectSign), actualSign, expectSign, params);
        return actualSign.equalsIgnoreCase(expectSign);
    }

    /**
     * 回查校验酷狗支付网关订单
     *
     * @param callbackDTO 回调信息
     * @return 是否合法
     */
    public boolean checkCallbackNotify(BaseCallbackDTO callbackDTO) {
        // 如果是已经成功执行了回调的单，不需要再调用网关那边的签名校验
        if (callbackDTO.getCallback_status() == 1) {
            log.warn("酷狗支付网关充值回调，成功回调订单不再调用网关签名校验。coinCallbackDTO: {}", callbackDTO);
            return true;
        }
        int appId = callbackDTO.getAppid();
        String appKey = this.rechargeConfig.getKupayAppIdByAppId(appId).getKupayAppKey();
        Map<String, String> params = Maps.newHashMap();
        params.put("appid", String.valueOf(appId));
        params.put("areaid", String.valueOf(callbackDTO.getAreaid()));
        params.put("time", String.valueOf(callbackDTO.getTime()));
        params.put("notify_id", callbackDTO.getNotify_id());
        params.put("sign", SignUtils.buildSign(params, appKey));
        String url = this.rechargeConfig.getKupayIntranet() + "/v1/verifnotify?";
        Optional<String> optionalJson = HttpClientUtils.doSyncGet(url, params);
        if (optionalJson.isPresent() && StringUtils.trim(optionalJson.get()).equals("success")) {
            return true;
        }
        log.warn("酷狗支付网关充值回调，回查校验酷狗支付网关订单校验失败。url: {}, params: {}, optionalJson: {}, callbackDTO: {}",
                url, params, optionalJson, callbackDTO);
        return false;
    }

    /**
     * 检查回调Extend字段签名
     *
     * @return 是否合法签名
     */
    public boolean checkCallbackExtendSign(long kugouId, String orderNo, String base64Extend) {
        String extend = new String(Base64.decodeBase64(StringUtils.defaultString(base64Extend)));
        if (StringUtils.isBlank(extend)) {
            log.warn("酷狗支付网关充值回调，检查回调Extend字段为空。kugouId: {}, orderNo: {}, extend: {}", kugouId, orderNo, extend);
            return false;
        }
        String callBackSign = JsonUtils.parseJson(extend, "$.callBackSign");
        if (StringUtils.isBlank(callBackSign)) {
            log.warn("酷狗支付网关充值回调，检查回调Extend字段签名callBackSign为空。kugouId: {}, orderNo: {}, extend: {}", kugouId, orderNo, extend);
            return false;
        }
        String callBackKey = this.rechargeConfig.getCallBackKey();
        Map<String, String> callBackSignArg = Maps.newHashMap();
        callBackSignArg.put("order_no", orderNo);
        callBackSignArg.put("userid", String.valueOf(kugouId));
        String actualSign = DigestUtils.md5Hex(JSON.toJSONString(callBackSignArg) + callBackKey);
        if (!callBackSign.equalsIgnoreCase(actualSign)) {
            log.warn("酷狗支付网关充值回调，检查回调Extend字段签名非法。callBackSignArg: {}, actualSign: {}, callBackSign: {}",
                    callBackSignArg, actualSign, callBackSign);
            return false;
        }
        return true;
    }

    public SysResultCode checkPrerequisitesForIos(PurchaseCoinForIosRequest request) {
        if (StringUtils.isBlank(request.getOrderNum())) {
            log.warn("苹果充值购买星币回调，orderNum不存在。request: {}", request);
            return SysResultCode.RECHARGE_PARAM_ERROR;
        }
        if (StringUtils.isBlank(request.getKey())) {
            log.warn("苹果充值购买星币回调，key不存在。request: {}", request);
            return SysResultCode.RECHARGE_PARAM_ERROR;
        }
        if (request.getKugouId() < 1L) {
            log.warn("苹果充值购买星币回调，酷狗用户ID不存在。request: {}", request);
            return SysResultCode.RECHARGE_PARAM_ERROR;
        }
        Optional<Long> optionalUserId = userFacadeService.getUserIdByKugouId(request.getKugouId(), false);
        if (!optionalUserId.isPresent() || optionalUserId.get() < 1L) {
            log.warn("苹果充值购买星币回调，繁星用户ID不存在。request: {}", request);
            return SysResultCode.RECHARGE_PARAM_ERROR;
        }
        String actualKey = DigestUtils.md5Hex(DigestUtils.md5Hex("K7<XB4$!58$380" + request.getOrderNum()) + request.getKugouId());
        if (!actualKey.equals(request.getKey())) {
            log.warn("苹果充值购买星币回调，key不合法。request: {}, actualKey: {}", request, actualKey);
            return SysResultCode.RECHARGE_PARAM_ERROR;
        }
        return SysResultCode.SUCCESS;
    }

    public boolean executeIdempotent(RechargeAcrossPO sourceOrder, UnaryOperator<RechargeAcrossPO> map2TargetOrder) {
        String rechargeOrderNum = sourceOrder.getRechargeOrderNum();
        // 酷狗ID转繁星ID
        Optional<Long> optionalUserId = userFacadeService.getUserIdByKugouId(sourceOrder.getKugouId(), false);
        if (!optionalUserId.isPresent() || optionalUserId.get() < 1) {
            log.error("执行充值加星币，酷狗ID转繁星ID失败。originalOrder: {}", sourceOrder);
            throw new BizException(E_40000212);
        }
        // 构建到账订单
        RechargeAcrossPO targetOrder = map2TargetOrder.apply(sourceOrder);
        // 处理充值代金券
        targetOrder = handleRechargeCoupon(rechargeOrderNum, sourceOrder, targetOrder);
        // 调用消费加星币
        boolean flag = this.consumeRpcService.rechargeCoin(targetOrder);
        if (!flag) {
            log.warn("执行充值加星币，调用消费加星币失败。originalOrder: {}, callbackOrder: {}", sourceOrder, targetOrder);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        // 保存成功流水
        boolean isSuccess = this.saveCallbackSuccessOrder(targetOrder);
        if (!isSuccess) {
            log.warn("执行充值加星币，保存成功流水失败。targetOrder: {}, callbackOrder: {}", targetOrder, targetOrder);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        afterRechargeService.afterRechargeSuccess(targetOrder);
        log.warn("执行充值加星币，开始执行充值后逻辑。originalOrder: {}, callbackOrder: {}", sourceOrder, targetOrder);
        return true;
    }

    public boolean makeDeal(RechargeAcrossPO sourceOrder, UnaryOperator<RechargeAcrossPO> map2TargetOrder, Predicate<RechargeAcrossPO> deliveryProduct) {
        String rechargeOrderNum = sourceOrder.getRechargeOrderNum();
        // 酷狗ID转繁星ID
        Optional<Long> optionalUserId = userFacadeService.getUserIdByKugouId(sourceOrder.getKugouId(), false);
        if (!optionalUserId.isPresent() || optionalUserId.get() < 1) {
            log.error("执行充值加星币，酷狗ID转繁星ID失败。originalOrder: {}", sourceOrder);
            throw new BizException(E_40000212);
        }
        // 构建到账订单
        RechargeAcrossPO targetOrder = map2TargetOrder.apply(sourceOrder);
        // 处理充值代金券
        targetOrder = handleRechargeCoupon(rechargeOrderNum, sourceOrder, targetOrder);
        // 调用消费加星币
        boolean flag = deliveryProduct.test(targetOrder);
        if (!flag) {
            log.warn("执行充值加星币，调用消费加星币失败。originalOrder: {}, callbackOrder: {}", sourceOrder, targetOrder);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        // 保存成功流水
        boolean isSuccess = this.saveCallbackSuccessOrder(targetOrder);
        if (!isSuccess) {
            log.warn("执行充值加星币，保存成功流水失败。targetOrder: {}, callbackOrder: {}", targetOrder, targetOrder);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        afterRechargeService.afterRechargeSuccess(targetOrder);
        log.warn("执行充值加星币，开始执行充值后逻辑。originalOrder: {}, callbackOrder: {}", sourceOrder, targetOrder);
        return true;
    }

    public RechargeAcrossPO handleRechargeCoupon(String rechargeOrderNum, RechargeAcrossPO originalOrder, RechargeAcrossPO callbackOrder) {
        RechargeAcrossPO callbackOrderCopy = ModelUtils.from(callbackOrder, RechargeAcrossPO.class).orElseThrow(() -> new ContextedRuntimeException("对象拷贝失败"));
        Optional<CouponBO> optionalCouponBO = rechargeCouponService.extractValidCoupon(callbackOrderCopy);
        if (optionalCouponBO.isPresent()) {
            Optional<BigDecimal> optionalUsedCoupon = rechargeCouponService.useCoupon(originalOrder.getKugouId(), rechargeOrderNum, optionalCouponBO.get());
            if (!optionalUsedCoupon.isPresent()) {
                // 使用代金券失败，中断回调
                log.warn("执行充值加星币，使用充值代金券失败。rechargeOrderNum: {}", rechargeOrderNum);
                throw new BizException(SysResultCode.RECHARGE_COUPON_FREEZE);
            }
            //这个状态只是展示给前端看的， 1代表消费成功，-1代表消费失败
            BigDecimal usedCoupon = optionalUsedCoupon.get();
            callbackOrderCopy.setCouponStatus(usedCoupon.compareTo(BigDecimal.ZERO) > 0 ? 1 : -1);
            //重新计算 amount, 等于 现金 + 代金券
            callbackOrderCopy.setAmount(callbackOrderCopy.getMoney().add(usedCoupon));
            //重新计算 coin, 等于 现金星币 + 代金券星币
            callbackOrderCopy.setCoin(callbackOrderCopy.getCoin().add(usedCoupon.multiply(BigDecimal.valueOf(100))));
            log.warn("执行充值加星币，使用充值代金券成功。rechargeOrderNum: {}", rechargeOrderNum);
        }
        return callbackOrderCopy;
    }

    /**
     * 解析用户第三方支付付款时间（注意⚠️：由于酷狗支付网关返回的数据可能存在异常需要兼容处理）
     *
     * 正常：02202105101111250100017815，trade_time=20210510111139
     * 异常：02202105101111200100010449，trade_time=-00011130000000
     *
     * @param tradeTime 用户第三方支付时间
     * @return 解析用户第三方支付时间戳
     */
    public int parseTradeTime(String tradeTime) {
        try {
            return Math.toIntExact(DateUtils.parseDate(tradeTime, "yyyyMMddHHmmss").getTime() / 1000);
        } catch (Exception e) {
            log.warn("酷狗支付网关充值回调，无法解析用户第三方付款时间。tradeTime: {}, errMessage: {}", tradeTime, ExceptionUtils.getMessage(e));
            return 0;
        }
    }

    protected RechargeAcrossPO mockCreateOrder(PayTypeIdEnum payTypeIdEnum, long kugouId, String rechargeOrderNum, String outTradeNo, BigDecimal totalFee) {
        // 实际充值金额，单位：元
        BigDecimal amount = totalFee;
        // 实际充值金额，单位：分
        BigDecimal realAmount = amount.multiply(BigDecimal.valueOf(100));
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setRechargeId(orderIdService.generateGlobalId());
        rechargeAcrossPO.setRechargeOrderNum(rechargeOrderNum);
        rechargeAcrossPO.setConsumeOrderNum(outTradeNo);
        rechargeAcrossPO.setAddTime(DateHelper.getCurrentSeconds());
        rechargeAcrossPO.setKugouId(kugouId);
        rechargeAcrossPO.setFromKugouId(kugouId);
        rechargeAcrossPO.setAmount(amount);
        rechargeAcrossPO.setMoney(amount);
        rechargeAcrossPO.setRealAmount(realAmount);
        rechargeAcrossPO.setCoupon(BigDecimal.ZERO);
        rechargeAcrossPO.setCouponId(0);
        rechargeAcrossPO.setCouponOrderId(0);
        rechargeAcrossPO.setPayTypeId(payTypeIdEnum.getPayTypeId());
        rechargeAcrossPO.setStatus(0);
        rechargeAcrossPO.setCoin(amount.multiply(BigDecimal.valueOf(100)));
        rechargeAcrossPO.setExtraJsonData("");
        rechargeAcrossPO.setIsSandbox(0);
        rechargeAcrossPO.setCoinBefore(BigDecimal.ZERO);
        rechargeAcrossPO.setCoinAfter(BigDecimal.ZERO);
        rechargeAcrossPO.setAgentKugouId(0);
        rechargeAcrossPO.setRefer(0);
        rechargeAcrossPO.setCFrom(0);
        rechargeAcrossPO.setChannelId(0);
        return rechargeAcrossPO;
    }
}
