package com.kugou.fanxing.recharge.service.callback;

import com.dianping.cat.Cat;
import com.kugou.fanxing.recharge.constant.AreaIdEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.dto.CoinCallbackDTO;
import com.kugou.fanxing.recharge.model.dto.RechargeExtendDTO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Optional;
import java.util.function.UnaryOperator;

/**
 * 充值服务回调通知
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DefaultCallbackService extends AbstractCallbackService {

    /**
     * 通用充值购买商品回调
     *
     * @param coinCallbackDTO 请求参数
     * @return 处理响应码
     */
    public SysResultCode callBackGoods(CoinCallbackDTO coinCallbackDTO) {
        SysResultCode sysResultCode = checkPrerequisites(coinCallbackDTO);
        if (!sysResultCode.isSuccess()) {
            log.warn("充值购买物品回调，前置条件检查不通过。coinCallbackDTO: {}", coinCallbackDTO);
            return sysResultCode;
        }
        // 检查下单记录
        String rechargeOrderNum = coinCallbackDTO.getOrder_no();
        Optional<RechargeAcrossPO> optionalRechargeAcrossPO = this.rechargeOrderService.queryByRechargeOrderNum(rechargeOrderNum);
        if (optionalRechargeAcrossPO.isPresent() && optionalRechargeAcrossPO.get().getStatus() == 1) {
            log.warn("充值购买物品回调，订单已经处理过。rechargeOrderNum: {}", rechargeOrderNum);
            return SysResultCode.SUCCESS;
        }
        int addTime = DateHelper.getCurrentSeconds();
        String extend = coinCallbackDTO.getExtend();
        log.warn("充值回调返回报文:orderNo:{}, {}", rechargeOrderNum, ExtendParseUtils.decodeExtend(extend));
        // 调用消费充扣
        boolean result = consumeRpcService.rechargeFee(rechargeOrderNum, addTime, extend, ExtendParseUtils.getCoinType(extend));
        if (!result) {
            log.warn("充值购买物品回调，调用消费充扣失败。coinCallbackDTO: {}", coinCallbackDTO);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        // 保存成功流水
        RechargeAcrossPO targetOrder = buildSuccessOrder(rechargeOrderNum, addTime, coinCallbackDTO);
        boolean isSuccess = this.saveCallbackSuccessOrder(targetOrder);
        if (!isSuccess) {
            log.warn("充值购买物品回调，保存成功流水失败。targetOrder: {}, coinCallbackDTO: {}", targetOrder, coinCallbackDTO);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        // 通知业务成功
        String topic = ExtendParseUtils.getCallbackArgTopic(extend);
        if (StringUtils.isNotBlank(topic) && !this.afterRechargeService.sendPurchaseTopic(topic, targetOrder, coinCallbackDTO)) {
            log.warn("充值购买物品回调，通知业务到账失败。targetOrder: {}, coinCallbackDTO: {}", targetOrder, coinCallbackDTO);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        // 统一充值消息
        afterRechargeService.afterRechargeSuccess(targetOrder);
        log.warn("充值购买物品回调，执行成功。coinCallbackDTO: {}", coinCallbackDTO);
        return SysResultCode.SUCCESS;
    }

    /**
     * {
     * "appid":"1084",
     * "areaid":"02",
     * "time":"1614182426",
     * "notify_id":"f417bc913743cc8cc920b792fbf77d28",
     * "order_no":"R092021022500001019783079",
     * "total_fee":"10.00",
     * "out_trade_no":"02202102250000100100012507",
     * "trade_status":"1",
     * "sign_type":"md5",
     * "trade_time":"20210225000025",
     * "trade_no":"4200000943202102253066954917",
     * "partner":"1316427701",
     * "userid":"**********",
     * "extend":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
     * "sign":"7fde63b63a0a9742a99bdbf6bc47ceff"
     * }
     *
     * @param rechargeOrderNum 直播充值订单号
     * @param addTime          支付网关回调时间
     * @param coinCallbackDTO  回调参数
     * @return 充值成功记录
     */
    public RechargeAcrossPO buildSuccessOrder(String rechargeOrderNum, int addTime, CoinCallbackDTO coinCallbackDTO) {
        HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
        String extend = coinCallbackDTO.getExtend();
        BigDecimal coin = ExtendParseUtils.getCallbackArgAmount(extend).multiply(BigDecimal.valueOf(100)).stripTrailingZeros();
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO()
                .setRechargeId(orderIdService.generateGlobalId())
                .setRechargeOrderNum(rechargeOrderNum)
                .setConsumeOrderNum(coinCallbackDTO.getOut_trade_no())
                .setAddTime(ExtendParseUtils.getCallbackArgPayTime(extend))
                .setRechargeTime(addTime)
                .setKugouId(ExtendParseUtils.getCallbackArgFromKugouId(extend))
                .setFromKugouId(ExtendParseUtils.getCallbackArgFromKugouId(extend))
                .setCoin(coin)
                .setAmount(ExtendParseUtils.getCallbackArgAmount(extend))
                .setMoney(ExtendParseUtils.getCallbackArgAmount(extend))
                .setCoupon(BigDecimal.ZERO)
                .setCouponId(0)
                .setCouponOrderId(0)
                .setRealAmount(coin)
                .setPayTypeId(ExtendParseUtils.getCallbackArgPayTypeId(extend))
                .setStatus(1)
                .setCoinBefore(BigDecimal.ZERO)
                .setCoinAfter(BigDecimal.ZERO)
                .setAgentKugouId(0)
                .setRefer(ExtendParseUtils.getCallbackArgRefer(extend))
                .setCFrom(ExtendParseUtils.getCallbackArgCFrom(extend))
                .setChannelId(ExtendParseUtils.getCallbackArgChannelId(extend))
                .setExtend(extend)
                .setReType(ExtendParseUtils.getCallbackArgReType(extend))
                .setTradeTime(parseTradeTime(coinCallbackDTO.getTrade_time()))
                .setTradeNo(StringUtils.defaultString(coinCallbackDTO.getTrade_no()))
                .setPartner(StringUtils.defaultString(coinCallbackDTO.getPartner()))
                .setClientIp(ExtendParseUtils.getCallbackArgClientIp(extend))
                .setCoinType(ExtendParseUtils.getCoinType(extend));
        if (rechargeAcrossPO.getAddTime() < 1) {
            rechargeAcrossPO.setAddTime(ExtendParseUtils.getCallbackArgAddTime(extend));
            log.warn("充值购买物品回调，根据addTime设置下单时间。addTime: {}", rechargeAcrossPO.getAddTime());
        }
        String businessType = ExtendParseUtils.getCallbackArgBusinessType(extend);
        if (StringUtils.isNotBlank(businessType)) {
            rechargeAcrossPO.setBusinessId(businessType);
        }
        String extraJsonData = request.getParameter("extraJsonData");
        if (StringUtils.isNotBlank(extraJsonData)) {
            rechargeAcrossPO.setExtraJsonData(extraJsonData);
        }
        return rechargeAcrossPO;
    }

    /**
     * 通用充值购买货币回调
     *
     * @param coinCallbackDTO 请求参数
     * @return 处理响应码
     */
    public SysResultCode purchaseCurrencyCallback(CoinCallbackDTO coinCallbackDTO) {
        try {
            SysResultCode sysResultCode = checkPrerequisites(coinCallbackDTO);
            if (!sysResultCode.isSuccess()) {
                log.warn("充值购买星币回调，前置条件检查不通过。coinCallbackDTO: {}", coinCallbackDTO);
                return sysResultCode;
            }
            // 充值用户
            long kugouId = coinCallbackDTO.getUserid();
            String base64Extend = parseBase64Extend(coinCallbackDTO.getExtend());
            String extend = new String(Base64.decodeBase64(StringUtils.defaultString(base64Extend)));
            log.warn("酷狗支付网关充值回调，透传扩展参数内容。kugouId: {}, extend: {}", kugouId, extend);
            RechargeExtendDTO rechargeExtendDTO = parseRechargeExtendDTO(extend);
            log.warn("酷狗支付网关充值回调，透传扩展参数内容。kugouId: {}, rechargeExtendDTO: {}", kugouId, rechargeExtendDTO);
            // 计算星币价值
            BigDecimal realAmount = calculateCoinValue(coinCallbackDTO, rechargeExtendDTO);
            // 检查回调参数
            checkKupayCallbackParams(coinCallbackDTO, rechargeExtendDTO);

            RechargeAcrossPO sourceOrder = makeOrderByKugouPayRequest(kugouId, realAmount,coinCallbackDTO);
            // 检查下单记录
            Optional<RechargeAcrossPO> optionalRechargeAcrossPO = this.rechargeOrderService.queryByRechargeOrderNum(sourceOrder.getRechargeOrderNum());
            if (optionalRechargeAcrossPO.isPresent() && optionalRechargeAcrossPO.get().getStatus() == 1) {
                log.warn("充值购买星币回调，订单已经处理过。rechargeOrderNum: {}", sourceOrder.getRechargeOrderNum());
                return SysResultCode.SUCCESS;
            }
            Optional<SysResultCode> checkRs = checkCallbackOrder(sourceOrder,rechargeExtendDTO);
            if(checkRs.isPresent() && checkRs.get().getCode() != SysResultCode.SUCCESS.getCode()){
                return checkRs.get();
            }
            // 设置到账记录
            UnaryOperator<RechargeAcrossPO> map2TargetOrder = source -> {
                RechargeAcrossPO target = ModelUtils.fromUnchecked(source, RechargeAcrossPO.class);
                target.setCoin(realAmount);
                target.setRealAmount(realAmount);
                target.setStatus(1);
                target.setRechargeTime(DateHelper.getCurrentSeconds());
                target.setReType(ReTypeEnum.RETYPE_RECHARGE.getReTypeId());
                target.setRechargeId(orderIdService.generateGlobalId());
                target.setTradeNo(StringUtils.defaultString(coinCallbackDTO.getTrade_no()));
                target.setTradeTime(parseTradeTime(coinCallbackDTO.getTrade_time()));
                target.setPartner(StringUtils.defaultString(coinCallbackDTO.getPartner()));
                return target;
            };
            boolean result = executeIdempotent(sourceOrder, map2TargetOrder);
            if (!result) {
                log.error("充值购买星币回调，执行失败。coinCallbackDTO: {}", coinCallbackDTO);
                return SysResultCode.E_30000001;
            }
            log.warn("充值购买星币回调，执行成功。coinCallbackDTO: {}", coinCallbackDTO);
            return SysResultCode.SUCCESS;
        } catch (Exception e) {
            log.error("充值购买星币回调，执行失败。coinCallbackDTO: {}", coinCallbackDTO, e);
            return SysResultCode.E_30000001;
        }
    }

    public BigDecimal calculateCoinValue(CoinCallbackDTO coinCallbackDTO, RechargeExtendDTO rechargeExtendDTO) {
        String rechargeOrderNum = coinCallbackDTO.getOrder_no();
        BigDecimal coin = coinCallbackDTO.getTotal_fee().multiply(BigDecimal.valueOf(100));
        int payTypeId = rechargeExtendDTO.getPayTypeId();
        if (PayTypeIdEnum.isGooglePlayRecharge(payTypeId)) {
            coin = googlePlayRechargeService.getProductCoinById(rechargeExtendDTO.getProductId());
        }
        if (PayTypeIdEnum.isAirwallexRecharge(payTypeId) || PayTypeIdEnum.isAppleApp(payTypeId)) {
            coin = rechargeExtendDTO.getCoin();
        }
        if (coin.compareTo(BigDecimal.ZERO) < 1) {
            log.error("充值购买星币回调，星币价值异常。rechargeOrderNum: {}", rechargeOrderNum);
            throw new ContextedRuntimeException("充值购买星币回调，星币价值异常。")
                    .addContextValue("rechargeOrderNum", rechargeOrderNum)
                    .addContextValue("payTypeId", payTypeId);
        }
        log.warn("充值购买星币回调，计算星币价值。rechargeOrderNum: {}, coin: {}",
                rechargeOrderNum, coin.stripTrailingZeros().toPlainString());
        return coin;
    }

    public Optional<SysResultCode> checkCallbackOrder(RechargeAcrossPO sourceOrder,RechargeExtendDTO rechargeExtendDTO) {
        if (PayTypeIdEnum.isGooglePlayRecharge(rechargeExtendDTO.getPayTypeId())) {
            if (sourceOrder.getIsSandbox() > 0 && (!googlePlayRechargeService.isInGooglePlaySandboxWhitelist(sourceOrder.getKugouId()) || googlePlayRechargeService.isOverLimit(sourceOrder, rechargeExtendDTO.getVersion()))) {
                log.error("checkCallbackOrder error,sand box limit:{}",sourceOrder.getRechargeOrderNum());
                return Optional.of(SysResultCode.E_30000014);
            }
            if (googlePlayRechargeService.isInGooglePlayBlackCurrencylist(sourceOrder.getCurrency())) {
                log.error("checkCallbackOrder error,currency is limit:{}",sourceOrder.getRechargeOrderNum());
                return Optional.of(SysResultCode.GP_BLACK_CURRENCY);
            }
        }
        return Optional.empty();
    }

    /**
     * 检查支付网关回调参数
     *
     * @param coinCallbackDTO   支付网关回调数据
     * @param rechargeExtendDTO 充值下单透传数据
     */
    public void checkKupayCallbackParams(CoinCallbackDTO coinCallbackDTO, RechargeExtendDTO rechargeExtendDTO) {
        try {
            long kugouId = rechargeExtendDTO.getKugouId();
            String orderNo = coinCallbackDTO.getOrder_no();
            if (AreaIdEnum.isOverseas(rechargeExtendDTO.getAreaId())) {
                boolean isSameOverseasAmount = rechargeExtendDTO.getUsdAmount().compareTo(coinCallbackDTO.getTotal_fee()) == 0;
                String nameValuePairs = String.format("kugouId=%s&orderNo=%s&usdAmount=%s&totalFee=%s", kugouId, orderNo,
                        rechargeExtendDTO.getUsdAmount().stripTrailingZeros().toPlainString(),
                        coinCallbackDTO.getTotal_fee().stripTrailingZeros().toPlainString());
                Cat.logEvent("RechargeCallback", "CheckOverseasAmount", isSameOverseasAmount ? "0" : "1", nameValuePairs);
                if (!isSameOverseasAmount) {
                    log.warn("酷狗支付网关充值回调，海外下单金额与网关金额不一致。nameValuePairs: {}", nameValuePairs);
                }
            } else {
                boolean isSameDomesticAmount = rechargeExtendDTO.getMoney().compareTo(coinCallbackDTO.getTotal_fee()) == 0;
                String nameValuePairs = String.format("kugouId=%s&orderNo=%s&amount=%s&money=%s&totalFee=%s", kugouId, orderNo,
                        rechargeExtendDTO.getAmount().stripTrailingZeros().toPlainString(),
                        rechargeExtendDTO.getMoney().stripTrailingZeros().toPlainString(),
                        coinCallbackDTO.getTotal_fee().stripTrailingZeros().toPlainString());
                Cat.logEvent("RechargeCallback", "CheckDomesticAmount", isSameDomesticAmount ? "0" : "1", nameValuePairs);
                if (!isSameDomesticAmount) {
                    log.warn("酷狗支付网关充值回调，国内下单金额与网关金额不一致。nameValuePairs: {}", nameValuePairs);
                }
            }
        } catch (Exception e) {
            log.error("酷狗支付网关充值回调，检查支付网关回调参数异常。", e);
        }
    }
}
