package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.service.common.OrderIdService;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractRechargeService implements RechargeService {
    @Autowired
    protected KupayService kupayService;
    @Autowired
    protected OrderIdService orderIdService;
    @Autowired
    protected AgentRechargeService agentRechargeService;
    @Autowired
    protected RechargeCommonService rechargeCommonService;
    @Autowired
    protected RechargeOrderService rechargeOrderService;
    @Autowired
    protected ApolloConfigService apolloConfigService;
    @Autowired
    protected RemoteStrategyService remoteStrategyService;
}
