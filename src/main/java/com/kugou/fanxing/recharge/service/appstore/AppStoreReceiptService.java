package com.kugou.fanxing.recharge.service.appstore;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.ReceiptStatusEnum;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.AppStoreReceiptDao;
import com.kugou.fanxing.recharge.model.bo.ProxyConfigBO;
import com.kugou.fanxing.recharge.model.bo.ReceiptInfoBO;
import com.kugou.fanxing.recharge.model.po.AppStoreReceiptPO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import com.kugou.fanxing.recharge.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 苹果收据服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AppStoreReceiptService {

    private static final String APP_STORE_SANDBOX = "https://sandbox.itunes.apple.com/verifyReceipt";
    private static final String APP_STORE_PRODUCT = "https://buy.itunes.apple.com/verifyReceipt";

    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private OrderIdService orderIdService;
    @Autowired
    private AppStoreReceiptDao appStoreReceiptDao;

    public Optional<ReceiptInfoBO> verifyReceipt(String receiptData) {
        Optional<ReceiptInfoBO> optionalReceiptInfoBO = Optional.empty();
        ProxyConfigBO proxyConfigBO = this.apolloConfigService.getProxyConfig();
        Map<String, String> payload = Maps.newHashMap();
        payload.put("receipt-data", receiptData);
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyConfigBO.getProxyHost(), proxyConfigBO.getProxyPort()));
        Optional<String> optionalJson = HttpClientUtils.doPostJsonBody(proxy, APP_STORE_PRODUCT, Maps.newHashMap(), JSON.toJSONString(payload));
        if (optionalJson.isPresent()) {
            optionalReceiptInfoBO = this.parseReceipt(optionalJson.get());
        }
        // 沙盒环境校验
        if (optionalReceiptInfoBO.isPresent() && optionalReceiptInfoBO.get().getStatus() == ReceiptStatusEnum.STATUS_21007.getCode()) {
            optionalJson = HttpClientUtils.doPostJsonBody(proxy, APP_STORE_SANDBOX, Maps.newHashMap(), JSON.toJSONString(payload));
            if (optionalJson.isPresent()) {
                optionalReceiptInfoBO = this.parseReceipt(optionalJson.get());
                if (optionalReceiptInfoBO.isPresent()) {
                    ReceiptInfoBO receiptInfoBO = optionalReceiptInfoBO.get();
                    receiptInfoBO.setIsSandbox((short) 1);
                    optionalReceiptInfoBO = Optional.of(receiptInfoBO);
                }
            }
        }
        return optionalReceiptInfoBO;
    }

    public Optional<ReceiptInfoBO> parseReceipt(String receiptJson) {
        return Optional.ofNullable(JsonUtils.readValue(receiptJson, ReceiptInfoBO.class));
    }

    /**
     * <pre>
     * {
     *     "receipt": {
     *         "original_purchase_date_pst": "2021-08-15 22:16:42 America/Los_Angeles",
     *         "purchase_date_ms": "1629091002646",
     *         "unique_identifier": "29d4b2f55e05a4b602e296be6a8e3f7940eb18e6",
     *         "original_transaction_id": "1000000859849549",
     *         "bvrs": "********",
     *         "transaction_id": "1000000859849549",
     *         "quantity": "1",
     *         "in_app_ownership_type": "PURCHASED",
     *         "unique_vendor_identifier": "68393F18-BC96-41C2-978C-8A088B0EA439",
     *         "item_id": "1146327362",
     *         "original_purchase_date": "2021-08-16 05:16:42 Etc/GMT",
     *         "is_in_intro_offer_period": "false",
     *         "product_id": "com.fanxing.fxappstore.66coin",
     *         "purchase_date": "2021-08-16 05:16:42 Etc/GMT",
     *         "is_trial_period": "false",
     *         "purchase_date_pst": "2021-08-15 22:16:42 America/Los_Angeles",
     *         "bid": "com.kugou.fanxingappstore",
     *         "original_purchase_date_ms": "1629091002646"
     *     },
     *     "status": 0
     * }
     * </pre>
     *
     * @param rechargeOrderNum  充值单号
     * @param receiptData       苹果收据
     */
    public void addSourceReceipt(String rechargeOrderNum, String receiptData) {
        AppStoreReceiptPO source = new AppStoreReceiptPO();
        source.setGlobalId(orderIdService.generateGlobalId());
        source.setRechargeOrderNum(rechargeOrderNum);
        source.setReceiptMd5(DigestUtils.md5Hex(receiptData));
        source.setReceiptData(receiptData);
        source.setBid("");
        source.setProductId("");
        source.setStatus(0);
        source.setOriginalTransactionId("");
        source.setTransactionId("");
        source.setOriginalPurchaseDateMs(0);
        source.setPurchaseDateMs(0);
        source.setAddTime(DateHelper.getCurrentSeconds());
        this.appStoreReceiptDao.insertIgnore(source);
    }

    public String parseReceiptDataEnvironment(String receiptData) {
        String environment = "";
        String content = new String(Base64.decodeBase64(receiptData));
        Matcher matcher1 = Pattern.compile("\"environment\"\\s=\\s\\\"(.+)\\\"").matcher(content);
        if (matcher1.find()) {
            environment = matcher1.group(1);
        }
        return environment;
    }

    public boolean isSandboxReceipt(String receiptData) {
        String environment = this.parseReceiptDataEnvironment(receiptData);
        return "Sandbox".equalsIgnoreCase(environment);
    }

}
