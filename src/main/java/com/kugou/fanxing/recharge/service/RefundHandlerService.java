package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.constant.HandlerTypeEnum;
import com.kugou.fanxing.recharge.model.vo.RefundHandlerVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @Author: yuzhaopeng
 * @Description:
 * @Date: 2024/2/26 18:28
 */
@Component
@Slf4j
public class RefundHandlerService {

    @Autowired
    private ApolloConfigService apolloConfigService;


    public int getHandlerType(String businessId) {
        Optional<RefundHandlerVO> refundHandlerVOOptional = apolloConfigService.getRefundHandler(businessId);
        int handlerType = HandlerTypeEnum.DEFAULT.getCode();
        if(refundHandlerVOOptional.isPresent()){
            handlerType = refundHandlerVOOptional.get().getHandlerType();
        }
        return handlerType;
    }

}
