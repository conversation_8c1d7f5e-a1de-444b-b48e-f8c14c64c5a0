package com.kugou.fanxing.recharge.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.model.request.FapiaoCallbackRequest;
import com.kugou.fanxing.recharge.model.request.KupayCommonRequest;
import com.kugou.fanxing.recharge.model.vo.FapiaoRechargeInfoVO;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.recharge.util.ModelUtils;
import com.kugou.fanxing.recharge.util.Pagination;
import com.kugou.fanxing.thrift.pay.v2.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FapiaoService {

    @Autowired
    private KupayService kupayService;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private PlatformPayV2Service.Iface platformPayV2Service;

    public JsonResult<Map<String, Object>> getFapiaoRechargeList(int pid, long kugouId, long lastRechargeId) {
        try {
            long pageSize = apolloConfigService.getFapiaoRechargeListPageSize();
            Map<String, Object> payload = Maps.newHashMap();
            Date date = new Date();
            QueryFapiaoRechargeListV2Request request = new QueryFapiaoRechargeListV2Request();
            request.setKugouId(kugouId);
            request.setStartRechargeTime(DateUtils.addMonths(date, -6).getTime() / 1000);
            request.setEndRechargeTime(date.getTime() / 1000);
            request.setLastRechargeId(lastRechargeId == -1 ? Long.MAX_VALUE : lastRechargeId);
            request.setLimit(pageSize);
            request.setPayTypeId(apolloConfigService.isAppStoreRecharge(pid) ? PayTypeIdEnum.PAY_TYPE_ID_1006.getPayTypeId() : 0);
            log.warn("查询可开发票充值记录, 请求参数。kugouId: {}, startRechargeTime: {}, endRechargeTime: {}, pageSize: {}",
                    kugouId, DateHelper.format(DateUtils.addMonths(date, -6)), DateHelper.format(date.getTime()), pageSize);
            QueryFapiaoRechargeListResponse response = this.platformPayV2Service.queryFapiaoRechargeListV2(request);
            if (Objects.isNull(response) || response.getCode() != 0 || Objects.isNull(response.getData())) {
                log.error("查询可开发票充值记录，查询失败。request: {}, response: {}", request, response);
                return JsonResult.result(SysResultCode.RECHARGE_SYS_ERROR, payload);
            }
            List<FapiaoRechargeInfo> fapiaoRechargeInfoList = response.getData();
            List<FapiaoRechargeInfoVO> fapiaoRechargeInfoVOList = fapiaoRechargeInfoList.stream().map(fapiaoRechargeInfo -> {
                Optional<FapiaoRechargeInfoVO> optionalFapiaoRechargeInfoVO = ModelUtils.from(fapiaoRechargeInfo, FapiaoRechargeInfoVO.class);
                if (optionalFapiaoRechargeInfoVO.isPresent()) {
                    FapiaoRechargeInfoVO fapiaoRechargeInfoVO = optionalFapiaoRechargeInfoVO.get();
                    fapiaoRechargeInfoVO.setOrderNo(StringUtils.defaultIfBlank(fapiaoRechargeInfoVO.getOrderNo(), fapiaoRechargeInfoVO.getRechargeOrderNum()));
                    if (PayTypeIdEnum.isTmallRecharge(fapiaoRechargeInfoVO.getPayTypeId()) || PayTypeIdEnum.isPddRecharge(fapiaoRechargeInfoVO.getPayTypeId())) {
                        fapiaoRechargeInfoVO.setOrderNo(fapiaoRechargeInfoVO.getTradeNo());
                    }
                    fapiaoRechargeInfoVO.setPayTypeIdLabel(PayTypeIdEnum.payTypeLabelOf(fapiaoRechargeInfoVO.getPayTypeId()));
                    return fapiaoRechargeInfoVO;
                }
                return null;
            })
                    .filter(Objects::nonNull)
                    .filter(fapiaoRechargeInfoVO -> !PayTypeIdEnum.isOfflineRecharge(fapiaoRechargeInfoVO.getPayTypeId()))
                    .filter(fapiaoRechargeInfo -> StringUtils.isNotBlank(fapiaoRechargeInfo.getOutTradeNo()) && StringUtils.isNotBlank(fapiaoRechargeInfo.getOrderNo()))
                    .collect(Collectors.toList());
            long minRechargeId = fapiaoRechargeInfoVOList.stream().mapToLong(FapiaoRechargeInfoVO::getRechargeId).min().orElse(0L);
            payload.put("minRechargeId", String.valueOf(minRechargeId));
            payload.put("rechargeInfos", fapiaoRechargeInfoVOList);
            payload.put("pageSize", pageSize);
            return JsonResult.result(SysResultCode.SUCCESS, payload);
        } catch (Exception e) {
            log.error("查询可开发票充值记录，查询异常。kugouId: {}, lastRechargeId: {}", kugouId, lastRechargeId);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR, e)
                    .addContextValue("kugouId", kugouId)
                    .addContextValue("lastRechargeId", lastRechargeId);
        }
    }

    public boolean handleFapiaoCallback(KupayCommonRequest kupayCommonRequest, FapiaoCallbackRequest fapiaoCallbackRequest) {
        try {
            log.warn("开票状态同步回调，请求参数。kupayCommonRequest: {}, fapiaoCallbackRequest: {}", kupayCommonRequest, fapiaoCallbackRequest);
            SyncFapiaoInfoRequest request = new SyncFapiaoInfoRequest();
            request.setKugouId(fapiaoCallbackRequest.getUserid());
            request.setOutTradeNo(fapiaoCallbackRequest.getOut_trade_no());
            request.setOrderNo(fapiaoCallbackRequest.getOrder_no());
            request.setCanReceipt(fapiaoCallbackRequest.isCan_receipt());
            request.setReceiptStatus(fapiaoCallbackRequest.getReceipt_status());
            request.setReceiptType(fapiaoCallbackRequest.getReceipt_type());
            request.setOperationType(fapiaoCallbackRequest.getOperation_type());
            SyncFapiaoInfoResponse response = this.platformPayV2Service.syncFapiaoInfo(request);
            if (Objects.isNull(response) || response.getCode() != 0 || !response.isSyncSuccess()) {
                log.warn("开票状态同步回调，处理失败。kupayCommonRequest: {}, fapiaoCallbackRequest: {}, request: {}, response: {}",
                        kupayCommonRequest, fapiaoCallbackRequest, fapiaoCallbackRequest, response);
                return false;
            }
            log.warn("开票状态同步回调，处理成功。kupayCommonRequest: {}, fapiaoCallbackRequest: {}, request: {}, response: {}",
                    kupayCommonRequest, fapiaoCallbackRequest, fapiaoCallbackRequest, response);
            return true;
        } catch (Exception e) {
            log.warn("开票状态同步回调，处理异常。kupayCommonRequest: {}, fapiaoCallbackRequest: {}", kupayCommonRequest, fapiaoCallbackRequest);
        }
        return false;
    }

    public boolean showHistoryFapiao(long kugouId) {
        Optional<String> optionalContent = this.kupayService.showHisTagV1(kugouId);
        if (!optionalContent.isPresent()) {
            return false;
        }
        String content = optionalContent.get();
        KupayService.ShowHisTagResp showHisTagResp = JSON.parseObject(content, KupayService.ShowHisTagResp.class);
        if (showHisTagResp.getStatus() == 1 && showHisTagResp.getError_code() == 0 && Objects.nonNull(showHisTagResp.getData())) {
            return showHisTagResp.getData().is_show();
        }
        return false;
    }

    public KupayService.RcptOrderWrapper historyFapiaoList(long kugouId, Pagination pagination) {
        KupayService.RcptOrderWrapper defaultWrapper = KupayService.RcptOrderWrapper.builder().total_count(0).list(Collections.emptyList()).build();
        Optional<String> optionalContent = this.kupayService.hisCanRcptOrdersV1(kugouId, pagination);
        String content = optionalContent.orElse("");
        KupayService.CanRcptOrders canRcptOrders = JSON.parseObject(content, KupayService.CanRcptOrders.class);
        if (canRcptOrders.getStatus() == 0 && canRcptOrders.getError_code() == 30015) {
            return defaultWrapper;
        }
        if (canRcptOrders.getStatus() != 1 || canRcptOrders.getError_code() != 0) {
            return defaultWrapper;
        }
        return canRcptOrders.getData();

    }
}
