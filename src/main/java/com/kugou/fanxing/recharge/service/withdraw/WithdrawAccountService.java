package com.kugou.fanxing.recharge.service.withdraw;

import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawAccountDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawAccountWechatDao;
import com.kugou.fanxing.recharge.model.dto.CertificationDTO;
import com.kugou.fanxing.recharge.model.po.WithdrawAccountPO;
import com.kugou.fanxing.recharge.model.po.WithdrawAccountWechatPO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.common.DencryptService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.common.PlatformCertificationServiceImpl;
import com.kugou.fanxing.recharge.withdraw.thrift.AccountChangeRequest;
import com.kugou.fanxing.recharge.withdraw.thrift.AccountChangeV2Request;
import com.kugou.fanxing.recharge.withdraw.thrift.WithdrawAccountDTO;
import com.kugou.fanxing.recharge.withdraw.thrift.WithdrawAccountWechatDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 提现账号服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class WithdrawAccountService {

    private static final int ACCOUNT_NUM_THRESHOLD = 3;

    @Autowired
    private OrderIdService orderIdService;
    @Autowired
    private PlatformCertificationServiceImpl platformCertificationService;
    @Autowired
    private WithdrawAccountDao withdrawAccountDao;
    @Autowired
    private WithdrawAccountWechatDao withdrawAccountWechatDao;
    @Autowired
    private DencryptService dencryptService;
    @Autowired
    private ApolloConfigService apolloConfigService;

    /**
     * 酷狗ID绑定提现账号
     *
     * @param request 提现账号信息
     * @return 系统响应码
     */
    public SysResultCode saveAccountInfo(AccountChangeRequest request) {
        // 检查账号至多允许绑定3个酷狗ID
        long kugouId = request.getKugouId();
        String account = request.getAccount();
        String accountEncrypted = dencryptService.encrypt(account);
        String accountFingerprint = dencryptService.fingerprint(account);
        List<WithdrawAccountPO> withdrawAccountPOList = withdrawAccountDao.getAccountNumByFingerprint(accountFingerprint);
        boolean isUpdate = withdrawAccountPOList.stream().anyMatch(withdrawAccountPO -> withdrawAccountPO.getKugouId() == kugouId);
        if (withdrawAccountPOList.size() >= ACCOUNT_NUM_THRESHOLD && !isUpdate) {
            log.error("绑定提现账号，账号至多允许绑定3个酷狗ID, request: {}", request);
            return SysResultCode.WITHDRAW_ACCOUNT_LIMITED_BIND;
        }

        // 检查实名信息与实名认证是否一致
        String realName = request.getRealName();
        if (apolloConfigService.needCheckRealName(request.getAppId())) {
            boolean isMatch = this.checkAccountNameIsMatchRealName(kugouId, realName);
            if (!isMatch) {
                log.error("绑定提现账号，实名信息与实名认证不匹配, request: {}", request);
                return SysResultCode.WITHDRAW_ACCOUNT_NOT_MATCH;
            }
        }

        // 保存绑定提现账号信息
        long globalId = orderIdService.generateGlobalId();
        WithdrawAccountPO withdrawAccountPO = new WithdrawAccountPO()
                .setGlobalId(globalId)
                .setKugouId(request.getKugouId())
                .setAccountEncrypted(accountEncrypted)
                .setAccountFingerprint(accountFingerprint)
                .setRealName(realName)
                .setUpdateTime(new Date())
                .setCreateTime(new Date());
        int affected = withdrawAccountDao.saveOrUpdate(withdrawAccountPO);
        if (affected < 1) {
            log.error("绑定提现账号，保存酷狗ID绑定提现账号失败, request: {}", request);
            return SysResultCode.WITHDRAW_ACCOUNT_BIND_FAIL;
        }
        return SysResultCode.SUCCESS;
    }

    /**
     * 查询提现账号信息
     *
     * @param kugouId 酷狗ID
     * @return 提现账号信息
     */
    public Optional<WithdrawAccountDTO> queryAccountInfoByKugouId(long kugouId) {
        Optional<WithdrawAccountPO> optionalWithdrawAccountPO = Optional.ofNullable(withdrawAccountDao.selectByKugouId(kugouId));
        if (!optionalWithdrawAccountPO.isPresent()) {
            return Optional.empty();
        }
        WithdrawAccountPO withdrawAccountPO = optionalWithdrawAccountPO.get();
        String account = this.dencryptService.decrypt(withdrawAccountPO.getAccountEncrypted());
        WithdrawAccountDTO withdrawAccountDTO = new WithdrawAccountDTO();
        withdrawAccountDTO.setKugouId(withdrawAccountPO.getKugouId());
        withdrawAccountDTO.setAccount(account);
        withdrawAccountDTO.setRealName(withdrawAccountPO.getRealName());
        withdrawAccountDTO.setCreateTime(Math.toIntExact(withdrawAccountPO.getCreateTime().getTime() / 1000));
        return Optional.of(withdrawAccountDTO);
    }

    /**
     * 查询微信提现账号信息
     *
     * @param kugouId 酷狗ID
     * @return 提现账号信息
     */
    public Optional<WithdrawAccountWechatDTO> queryWechatAccountInfoByKugouId(long kugouId, int bizAppId) {
        String wxAppId = apolloConfigService.getWxAppIdByBizAppId(bizAppId);
        Optional<WithdrawAccountWechatPO> optionalWithdrawAccountWechatPO = Optional.ofNullable(this.withdrawAccountWechatDao.selectByKugouId(kugouId, wxAppId));
        if (!optionalWithdrawAccountWechatPO.isPresent()) {
            log.warn("查询微信提现账号信息，账号信息不存在。kugouId: {}, bizAppId: {}", kugouId, bizAppId);
            return Optional.empty();
        }
        WithdrawAccountWechatPO accountWechatPO = optionalWithdrawAccountWechatPO.get();
        WithdrawAccountWechatDTO withdrawAccountWechatDTO = new WithdrawAccountWechatDTO();
        withdrawAccountWechatDTO.setKugouId(accountWechatPO.getKugouId());
        withdrawAccountWechatDTO.setOpenid(StringUtils.defaultString(accountWechatPO.getOpenid()));
        withdrawAccountWechatDTO.setRealName(accountWechatPO.getRealName());
        withdrawAccountWechatDTO.setNickname(StringUtils.defaultString(accountWechatPO.getNickname()));
        withdrawAccountWechatDTO.setAvatar(StringUtils.defaultString(accountWechatPO.getAvatar()));
        withdrawAccountWechatDTO.setCreateTime(Math.toIntExact(accountWechatPO.getCreateTime().getTime() / 1000));
        return Optional.of(withdrawAccountWechatDTO);
    }

    /**
     * 判断提现账号姓名是否跟实名认证账号一致
     *
     * @param kugouId  酷狗ID
     * @param realName 用户实名
     * @return 是否匹配
     */
    public boolean checkAccountNameIsMatchRealName(long kugouId, String realName) {
        Optional<String> optionalRealName = this.getRealNameByKugouId(kugouId);
        if (!optionalRealName.isPresent() || !optionalRealName.get().equals(realName)) {
            log.error("实名信息与实名认证信息不一致, kugouId: {}, realName: {}, optionalRealName: {}", kugouId, realName, optionalRealName);
            return false;
        }
        return true;
    }

    /**
     * 根据酷狗ID获取用户实名信息
     *
     * @param kugouId 酷狗ID
     * @return 用户实名信息
     */
    public Optional<String> getRealNameByKugouId(long kugouId) {
        Optional<CertificationDTO> optionalCertificationDTO = platformCertificationService.getRealNameStatus(kugouId);
        if (!optionalCertificationDTO.isPresent() || optionalCertificationDTO.get().getStatus() != 2) {
            log.warn("调用实名认证服务，获取实名认证信息失败。kugouId: {}, optionalCertificationDTO: {}", kugouId, optionalCertificationDTO);
            return Optional.empty();
        }
        CertificationDTO certificationDTO = optionalCertificationDTO.get();
        log.warn("调用实名认证服务，获取实名认证信息成功。kugouId: {}, certificationDTO: {}", kugouId, certificationDTO);
        return Optional.of(certificationDTO.getName());
    }

    public SysResultCode saveAccountInfoV2(AccountChangeV2Request request) {
        long kugouId = request.getKugouId();
        // 检查绑定提现账号
        if (StringUtils.isBlank(request.getAccount()) && StringUtils.isBlank(request.getOpenid())) {
            log.error("绑定提现账号，请求参数不合法。request: {}", request);
            return SysResultCode.RECHARGE_PARAM_ERROR;
        }
        // 校验绑定支付宝提现账号
        if (StringUtils.isNotBlank(request.getAccount())) {
            // 检查用户实名认证信息
            if (apolloConfigService.needCheckRealName(request.getAppId())) {
                boolean isMatch = this.checkAccountNameIsMatchRealName(kugouId, request.getRealName());
                if (!isMatch) {
                    log.error("绑定提现账号，实名信息与实名认证不匹配, request: {}", request);
                    return SysResultCode.WITHDRAW_ACCOUNT_NOT_MATCH;
                }
            }
            // 支付宝账号至多允许绑定3个酷狗ID
            String accountFingerprint = dencryptService.fingerprint(request.getAccount());
            List<WithdrawAccountPO> withdrawAccountPOList = withdrawAccountDao.getAccountNumByFingerprint(accountFingerprint);
            boolean isUpdate = withdrawAccountPOList.stream().anyMatch(withdrawAccountPO -> withdrawAccountPO.getKugouId() == kugouId);
            if (withdrawAccountPOList.size() >= ACCOUNT_NUM_THRESHOLD && !isUpdate) {
                log.error("绑定提现账号，同一支付宝账号至多允许绑定3个酷狗ID, request: {}", request);
                return SysResultCode.WITHDRAW_ACCOUNT_LIMITED_BIND;
            }
            return this.bindAccountForAlipay(request);
        }
        // 校验绑定微信提现账号
        if (StringUtils.isNotBlank(request.getOpenid())) {
            boolean isInvalidOpenidPatterns = this.apolloConfigService.isInvalidOpenidPatterns(request.getOpenid());
            if (isInvalidOpenidPatterns) {
                log.error("绑定提现账号，非法的openid模式。request: {}", request);
                return SysResultCode.RECHARGE_PARAM_ERROR;
            }
            // 检查用户实名认证信息
            Optional<String> optionalRealName = this.getRealNameByKugouId(kugouId);
            if (!optionalRealName.isPresent() || StringUtils.isBlank(optionalRealName.get())) {
                log.error("绑定提现账号，实名信息与实名认证不匹配, request: {}, optionalRealName: {}", request, optionalRealName);
                return SysResultCode.WITHDRAW_ACCOUNT_NOT_MATCH;
            }
            request.setRealName(optionalRealName.get());
            // 微信提现绑定多账号
            String wxAppId = apolloConfigService.getWxAppIdByBizAppId(request.getBizAppId());
            List<WithdrawAccountPO> withdrawAccountPOList = withdrawAccountWechatDao.getAccountsByOpenid(wxAppId, request.getOpenid());
            if (CollectionUtils.isNotEmpty(withdrawAccountPOList)) {
                log.error("绑定提现账号，同一微信账号至多允许绑定1个酷狗ID, request: {}", request);
                return SysResultCode.WITHDRAW_ACCOUNT_ALREADY_BIND;
            }
            return this.bindAccountForWechat(request);
        }
        return SysResultCode.RECHARGE_PARAM_ERROR;
    }

    /**
     * 保存微信提现账号
     *
     * @param request 绑定请求
     * @return 绑定结果
     */
    public SysResultCode bindAccountForWechat(AccountChangeV2Request request) {
        // 保存绑定提现账号信息
        long globalId = orderIdService.generateGlobalId();
        String wxAppId = apolloConfigService.getWxAppIdByBizAppId(request.getBizAppId());
        WithdrawAccountWechatPO accountWechatPO = new WithdrawAccountWechatPO()
                .setGlobalId(globalId)
                .setKugouId(request.getKugouId())
                .setWxAppId(wxAppId)
                .setOpenid(request.getOpenid())
                .setRealName(request.getRealName())
                .setNickname(request.getNickname())
                .setAvatar(request.getAvatar())
                .setUpdateTime(new Date())
                .setCreateTime(new Date());
        int affected = withdrawAccountWechatDao.saveOrUpdate(accountWechatPO);
        if (affected < 1) {
            log.error("绑定微信提现账号，保存酷狗ID与提现账号失败。request: {}", request);
            return SysResultCode.WITHDRAW_ACCOUNT_BIND_FAIL;
        }
        return SysResultCode.SUCCESS;
    }

    /**
     * 保存支付宝提现账号
     *
     * @param request 绑定请求
     * @return 绑定结果
     */
    public SysResultCode bindAccountForAlipay(AccountChangeV2Request request) {
        // 保存绑定提现账号信息
        long globalId = orderIdService.generateGlobalId();
        String accountEncrypted = dencryptService.encrypt(request.getAccount());
        String accountFingerprint = dencryptService.fingerprint(request.getAccount());
        WithdrawAccountPO withdrawAccountPO = new WithdrawAccountPO()
                .setGlobalId(globalId)
                .setKugouId(request.getKugouId())
                .setAccountEncrypted(accountEncrypted)
                .setAccountFingerprint(accountFingerprint)
                .setRealName(request.getRealName())
                .setUpdateTime(new Date())
                .setCreateTime(new Date());
        int affected = withdrawAccountDao.saveOrUpdate(withdrawAccountPO);
        if (affected < 1) {
            log.error("绑定支付宝提现账号，保存酷狗ID与提现账号失败。request: {}", request);
            return SysResultCode.WITHDRAW_ACCOUNT_BIND_FAIL;
        }
        return SysResultCode.SUCCESS;
    }
}
