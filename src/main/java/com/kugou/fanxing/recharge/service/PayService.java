package com.kugou.fanxing.recharge.service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.math.LongMath;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.jayway.jsonpath.TypeRef;
import com.jayway.jsonpath.spi.json.JacksonJsonProvider;
import com.jayway.jsonpath.spi.mapper.JacksonMappingProvider;
import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.biz.commons.util.GrayTools;
import com.kugou.fanxing.commons.util.JsonUtils;
import com.kugou.fanxing.recharge.common.SpringContextHolder;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.*;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeCouponDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeOpenDao;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.bo.AccountChangeTypeBO;
import com.kugou.fanxing.recharge.model.bo.GoodsConfigBO;
import com.kugou.fanxing.recharge.model.bo.RechargeAcrossBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.po.RechargeCouponPO;
import com.kugou.fanxing.recharge.model.request.AndroidCommonParam;
import com.kugou.fanxing.recharge.model.vo.*;
import com.kugou.fanxing.recharge.pay.thrift.GetRechargeBusinessOrderRequest;
import com.kugou.fanxing.recharge.pay.thrift.RechargeAllocateQueryService;
import com.kugou.fanxing.recharge.pay.thrift.RechargeBusinessOrderDTO;
import com.kugou.fanxing.recharge.pay.thrift.RechargeBusinessOrderResponse;
import com.kugou.fanxing.recharge.service.command.GetRechargePresentInfoCommand;
import com.kugou.fanxing.recharge.service.command.ListUserRechargeRebateInfosCommand;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.service.handler.IPayTypeHandler;
import com.kugou.fanxing.recharge.service.handler.PayType42Handler;
import com.kugou.fanxing.recharge.thrift.PurchaseOrder;
import com.kugou.fanxing.recharge.thrift.PurchaseProductRequest;
import com.kugou.fanxing.recharge.thrift.PurchaseProductRequestV2;
import com.kugou.fanxing.recharge.thrift.RechargeAcrossDTO;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import com.kugou.fanxing.recharge.util.ModelUtils;
import com.kugou.fanxing.recharge.util.Pagination;
import com.kugou.fanxing.userbaseinfo.vo.UserVO;
import com.kugou.platform.after.recharge.asset.allocate.thrift.AfterRechargeAssetAllocateReadService;
import com.kugou.platform.after.recharge.asset.allocate.thrift.GoodsType;
import com.kugou.platform.after.recharge.asset.allocate.thrift.RechargePresentItem;
import com.kugou.platform.recharge.queryservice.thrift.RechargeInfoQueryThriftService;
import com.kugou.platform.recharge.queryservice.thrift.RechargeRebateInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * 充值查询服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PayService {

    @Autowired
    private OrderIdService orderIdService;
    @Autowired
    private RechargeConfig rechargeConfig;
    @Autowired
    private RechargeAcrossDao rechargeAcrossDao;
    @Autowired
    private RechargeOpenDao rechargeOpenDao;
    @Autowired
    private RechargeCouponDao rechargeCouponDao;
    @Autowired
    private RechargeInfoQueryThriftService.Iface rechargeInfoQueryThriftService;
    @Autowired
    private AfterRechargeAssetAllocateReadService.Iface afterRechargeAsserAllocateReadService;
    @Autowired
    private RechargeAllocateQueryService.Iface rechargeAllocateQueryService;
    @Autowired
    private RemoteStrategyService remoteStrategyService;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private GiftInfoService giftInfoService;
    @Resource
    private UserFacadeService userFacadeService;

    private static final GrayTools agentRecordGrayTools = GrayTools.module("agentRecord");

    public List<RechargeAcrossDTO> getRechargeListForPC(String month, long kugouId, Pagination pagination) {
        int[] reTypes = {ReTypeEnum.RETYPE_RECHARGE.getReTypeId()};
        List<RechargeAcrossPO> rechargeAcrossPOList = rechargeAcrossDao.getRechargeAcrossList(month, kugouId, reTypes, 0, pagination);
        // 根据充值订单获取代金券使用记录
        List<String> rechargeOrderNums = rechargeAcrossPOList.stream()
                .map(RechargeAcrossPO::getRechargeOrderNum)
                .distinct()
                .collect(Collectors.toList());
        List<RechargeCouponPO> rechargeCouponPOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(rechargeOrderNums)) {
            int totalCount = rechargeAcrossDao.getRechargeAcrossListCount(month, kugouId, reTypes, 0);
            pagination.setTotalCount(totalCount);
            rechargeCouponPOList = this.rechargeCouponDao.queryByRechargeOrderNums(rechargeOrderNums);
        }
        final Map<String, RechargeCouponPO> rechargeCouponMap = rechargeCouponPOList.stream()
                .collect(Collectors.toMap(RechargeCouponPO::getRechargeOrderNum, Function.identity()));
        // 构建返回数据
        return rechargeAcrossPOList.stream()
                .filter(rechargeAcrossPO -> rechargeAcrossPO.getReType() == ReTypeEnum.RETYPE_RECHARGE.getReTypeId())
                .map(rechargeAcrossPO -> buildRechargeAcrossDTO(rechargeAcrossPO, rechargeCouponMap))
                .collect(Collectors.toList());
    }

    public List<RechargeAcrossDTO> getRechargeListByUserWithTime(String month,long kugouId, long startTime ,long endTime, Pagination pagination) {
        int[] reTypes = {ReTypeEnum.RETYPE_RECHARGE.getReTypeId(),ReTypeEnum.RETYPE_PURCHASE.getReTypeId(),ReTypeEnum.RETYPE_RENEWALS.getReTypeId()};
        if(startTime > endTime){
            return Collections.emptyList();
        }
        if(Long.parseLong(apolloConfigService.getRecordremierMonth()) > Long.parseLong(month)){
            return Collections.emptyList();
        }
        String currentMonth = DateFormatUtils.format(System.currentTimeMillis(), "yyyyMM");
        if(Long.parseLong(currentMonth) < Long.parseLong(month)){
            return Collections.emptyList();
        }

        List<RechargeAcrossPO> rechargeAcrossPOList = rechargeAcrossDao.getRechargeAcrossListWithTime(month, kugouId, reTypes, 0, startTime,endTime,pagination);
        if (CollectionUtils.isNotEmpty(rechargeAcrossPOList)) {
            int totalCount = rechargeAcrossDao.getRechargeAcrossListCountWithTime(month, kugouId, reTypes, 0,startTime,endTime);
            pagination.setTotalCount(totalCount);
        }
        // 构建返回数据
        return rechargeAcrossPOList.stream()
                .filter(rechargeAcrossPO -> rechargeAcrossPO.getReType() == ReTypeEnum.RETYPE_RECHARGE.getReTypeId())
                .map(rechargeAcrossPO -> buildRechargeAcrossDTO(rechargeAcrossPO, new HashMap<>()))
                .collect(Collectors.toList());
    }

    private RechargeAcrossDTO buildRechargeAcrossDTO(RechargeAcrossPO rechargeAcrossPO, Map<String, RechargeCouponPO> rechargeCouponMap) {
        RechargeAcrossDTO rechargeAcrossDTO = new RechargeAcrossDTO();
        String rechargeOrderNum = rechargeAcrossPO.getRechargeOrderNum();
        rechargeAcrossDTO.setRechargeOrderNum(rechargeOrderNum);
        rechargeAcrossDTO.setPayTypeId(rechargeAcrossPO.getPayTypeId());
        rechargeAcrossDTO.setPayTypeLabel(PayTypeIdEnum.payTypeLabelOf(rechargeAcrossPO.getPayTypeId()));
        rechargeAcrossDTO.setStatus(rechargeAcrossPO.getStatus());
        rechargeAcrossDTO.setStatusLabel("成功");
        rechargeAcrossDTO.setCoin(rechargeAcrossPO.getCoin().toPlainString());
        rechargeAcrossDTO.setCouponId(String.valueOf(rechargeAcrossPO.getCouponOrderId()));
        rechargeAcrossDTO.setCoupon(rechargeAcrossPO.getCoupon().toPlainString());
        rechargeAcrossDTO.setMoney(rechargeAcrossPO.getMoney().toPlainString());
        rechargeAcrossDTO.setRechargeTime(rechargeAcrossPO.getRechargeTime());
        rechargeAcrossDTO.setTradeTime(rechargeAcrossPO.getTradeTime());
        rechargeAcrossDTO.setCouponStatus(rechargeAcrossPO.getCouponStatus());
        rechargeAcrossDTO.setCouponStatusLabel(RechargeCouponStatusEnum.labelOf(rechargeAcrossPO.getCouponStatus()));
        rechargeAcrossDTO.setCouponErrorCode(0);
        rechargeAcrossDTO.setCouponErrorMsg("");
        Optional<RechargeCouponPO> optionalRechargeCouponPO = Optional.ofNullable(rechargeCouponMap.get(rechargeOrderNum));
        if (optionalRechargeCouponPO.isPresent()) {
            int couponStatus = optionalRechargeCouponPO.get().getStatus();
            rechargeAcrossDTO.setCouponErrorCode(couponStatus);
            rechargeAcrossDTO.setCouponErrorMsg(CouponStatusEnum.labelOf(couponStatus));
        }
        return rechargeAcrossDTO;
    }

    /**
     * 查询用户充值记录
     *
     * @param kugouId    酷狗ID
     * @param index      月份偏移
     * @param payTypeId  支付类型
     * @param pagination 分页信息
     * @return 用户充值记录
     */
    public RechargeAcrossListVO getRechargeList(long kugouId, int index, int payTypeId, Pagination pagination) {
        // 获取充值月份（index: 0 本月；1 上个月；）
        RechargeAcrossListVO rechargeAcrossListVO = new RechargeAcrossListVO();
        String month = DateHelper.formatYearMonth(DateUtils.addMonths(new Date(), index == 1 ? -1 : 0));
        // 普通充值记录
        int[] reTypes = {ReTypeEnum.RETYPE_RECHARGE.getReTypeId(), ReTypeEnum.RETYPE_PURCHASE.getReTypeId(), ReTypeEnum.RETYPE_RENEWALS.getReTypeId()};
        List<RechargeAcrossPO> rechargeAcrossPOList = rechargeAcrossDao.getRechargeAcrossList(month, kugouId, reTypes, payTypeId, pagination);
        List<RechargeAcrossBO> rechargeAcrossBOList = ModelUtils.fromList(rechargeAcrossPOList, RechargeAcrossBO.class);
        int listCount = rechargeAcrossDao.getRechargeAcrossListCount(month, kugouId, reTypes, payTypeId);

        // 获取返点赠品（赠币、摇一摇、座驾、勋章等）
        List<Long> moneyList = rechargeAcrossBOList.stream().map(rechargeAcrossBO -> rechargeAcrossBO.getAmount().longValue()).distinct().collect(Collectors.toList());
        Map<Long, List<RechargePresentItem>> rechargePresentMap = new GetRechargePresentInfoCommand(afterRechargeAsserAllocateReadService, moneyList, kugouId).execute();

        // 获取充值返点
        final Map<String, RechargeRebateInfo> rechargeRebateInfoMap = getRechargeRebateInfoMap(month, rechargeAcrossBOList);

        // 组装数据处理
        List<RechargeRecordVO> rechargeRecordVOList = rechargeAcrossBOList.stream()
                .map(rechargeAcrossBO -> buildRechargeRecordVO(BizLineEnum.LIVE, rechargePresentMap, rechargeRebateInfoMap, rechargeAcrossBO))
                .sorted(Comparator.comparingLong(RechargeRecordVO::getAddTime).reversed())
                .collect(toList());

        rechargeAcrossListVO.setCount(listCount);
        rechargeAcrossListVO.setList(rechargeRecordVOList);
        return rechargeAcrossListVO;
    }

    /**
     * 我为他人代充
     *
     * @param kugouId 酷狗ID
     * @param index 获取充值月份（index: 0 本月；1 上个月；）
     * @param pagination 分页信息
     * @return 代充记录
     */
    public AgentRechargeAcrossListVO getRechargeForOtherList(long kugouId, int index, Pagination pagination) {
        AgentRechargeAcrossListVO agentRechargeAcrossListVO = new AgentRechargeAcrossListVO();
        if (!agentRecordGrayTools.isGray(kugouId)) {
            log.warn("我为他人代充，灰度未开启。kugouId: {}", kugouId);
            return agentRechargeAcrossListVO;
        }
        String month = DateHelper.formatYearMonth(DateUtils.addMonths(new Date(), index == 1 ? -1 : 0));
        int[] reTypes = {ReTypeEnum.RETYPE_RECHARGE.getReTypeId(), ReTypeEnum.RETYPE_PURCHASE.getReTypeId(), ReTypeEnum.RETYPE_RENEWALS.getReTypeId()};
        List<AgentRechargeRecordVO> agentRechargeRecordVOList = rechargeAcrossDao.getRechargeAcrossForOtherList(month, kugouId, reTypes, pagination);
        int listCount = rechargeAcrossDao.getRechargeAcrossForOtherListCount(month, kugouId, reTypes);
        buildAgentRechargeRecord(agentRechargeRecordVOList);
        agentRechargeAcrossListVO.setCount(listCount);
        agentRechargeAcrossListVO.setList(agentRechargeRecordVOList);
        return agentRechargeAcrossListVO;
    }

    /**
     * 他人为我代充
     *
     * @param kugouId 酷狗ID
     * @param index 获取充值月份（index: 0 本月；1 上个月；）
     * @param pagination 分页信息
     * @return 代充记录
     */
    public AgentRechargeAcrossListVO getRechargeOtherForMeList(long kugouId, int index, Pagination pagination) {
        AgentRechargeAcrossListVO agentRechargeAcrossListVO = new AgentRechargeAcrossListVO();
        if (!agentRecordGrayTools.isGray(kugouId)) {
            log.warn("他人为我代充，灰度未开启。kugouId: {}", kugouId);
            return agentRechargeAcrossListVO;
        }
        String month = DateHelper.formatYearMonth(DateUtils.addMonths(new Date(), index == 1 ? -1 : 0));
        int[] reTypes = {ReTypeEnum.RETYPE_RECHARGE.getReTypeId(), ReTypeEnum.RETYPE_PURCHASE.getReTypeId(), ReTypeEnum.RETYPE_RENEWALS.getReTypeId()};
        List<AgentRechargeRecordVO> agentRechargeRecordVOList = rechargeAcrossDao.getRechargeOtherForMeList(month, kugouId, reTypes, pagination);
        int listCount = rechargeAcrossDao.getRechargeOtherForMeListCount(month, kugouId, reTypes);
        buildAgentRechargeRecord(agentRechargeRecordVOList);
        agentRechargeAcrossListVO.setCount(listCount);
        agentRechargeAcrossListVO.setList(agentRechargeRecordVOList);
        return agentRechargeAcrossListVO;
    }

    private void buildAgentRechargeRecord(List<AgentRechargeRecordVO> agentRechargeRecordVOList) {
        Set<Long> kugouIdSet = new HashSet<>();
        // 设置充值描述和充值大类描述
        for (AgentRechargeRecordVO agentRechargeRecordVO : agentRechargeRecordVOList) {
            agentRechargeRecordVO.setPayTypeLabel(PayTypeIdEnum.payTypeLabelOf(agentRechargeRecordVO.getPayTypeId()));
            agentRechargeRecordVO.setReTypeLabel(ReTypeEnum.labelOf(agentRechargeRecordVO.getReType()));
            agentRechargeRecordVO.setRechargeTimeStr(DateHelper.getDateTimeForamtFroUser(agentRechargeRecordVO.getRechargeTime()));
            kugouIdSet.add(agentRechargeRecordVO.getAgentKugouId());
            kugouIdSet.add(agentRechargeRecordVO.getKugouId());
        }
        if (kugouIdSet.size() == 0) {
            return;
        }
        List<Long> kugouIdList = new ArrayList<>(kugouIdSet);
        Map<Long, UserVO> userMap = userFacadeService.getUserByKugouIds(kugouIdList);
        Map<Long, Long> kugouId2KuwoIdMap  = userFacadeService.getKuwoIdByKugouId(kugouIdList);
        for (AgentRechargeRecordVO agentRechargeRecordVO : agentRechargeRecordVOList) {
            UserVO userVo = userMap.get(agentRechargeRecordVO.getKugouId());
            UserVO agentUserVo = userMap.get(agentRechargeRecordVO.getAgentKugouId());
            if (userVo != null) {
                agentRechargeRecordVO.setUserId(userVo.getUserId());
                agentRechargeRecordVO.setNickName(userVo.getNickName());
            }
            if (agentUserVo != null) {
                agentRechargeRecordVO.setAgentUserId(agentUserVo.getUserId());
                agentRechargeRecordVO.setAgentNickName(agentUserVo.getNickName());
            }
            agentRechargeRecordVO.setKuwoId(kugouId2KuwoIdMap.getOrDefault(agentRechargeRecordVO.getKugouId(), 0L));
            agentRechargeRecordVO.setAgentKuwoId(kugouId2KuwoIdMap.getOrDefault(agentRechargeRecordVO.getAgentKugouId(), 0L));
        }
    }

    /**
     * 构建充值记录
     *
     * @param rechargePresentMap    充值赠品（赠币、摇一摇、座驾、勋章等）
     * @param rechargeRebateInfoMap 充值返点
     * @param rechargeAcrossBO      充值订单
     * @return 充值记录
     */
    private RechargeRecordVO buildRechargeRecordVO(BizLineEnum bizLine, Map<Long, List<RechargePresentItem>> rechargePresentMap, Map<String, RechargeRebateInfo> rechargeRebateInfoMap, RechargeAcrossBO rechargeAcrossBO) {
        // 设置充值信息
        RechargeRecordVO rechargeRecordVO = new RechargeRecordVO();
        rechargeRecordVO.setRechargeOrderNum(rechargeAcrossBO.getRechargeOrderNum());
        rechargeRecordVO.setKugouId(rechargeAcrossBO.getKugouId());
        rechargeRecordVO.setReType(rechargeAcrossBO.getReType());
        rechargeRecordVO.setPayTypeId(rechargeAcrossBO.getPayTypeId());
        rechargeRecordVO.setAddTime(rechargeAcrossBO.getAddTime());
        rechargeRecordVO.setRechargeTime(rechargeAcrossBO.getRechargeTime());
        rechargeRecordVO.setAmount(rechargeAcrossBO.getAmount());
        rechargeRecordVO.setMoney(rechargeAcrossBO.getMoney());
        rechargeRecordVO.setCoupon(rechargeAcrossBO.getCoupon());
        rechargeRecordVO.setCoin(rechargeAcrossBO.getCoin());
        rechargeRecordVO.setReTypeLabel(ReTypeEnum.labelOf(rechargeAcrossBO.getReType()));
        rechargeRecordVO.setPayTypeLabel(PayTypeIdEnum.payTypeLabelOf(rechargeAcrossBO.getPayTypeId()));
        rechargeRecordVO.setRebateDelay(0);
        rechargeRecordVO.setRebateInfo("");
        rechargeRecordVO.setAreaId(rechargeAcrossBO.getAreaId());
        rechargeRecordVO.setCurrency(StringUtils.defaultString(rechargeAcrossBO.getCurrency()));
        rechargeRecordVO.setCurrencyAmount(rechargeAcrossBO.getCurrencyAmount());
        rechargeRecordVO.setUsdAmount(rechargeAcrossBO.getUsdAmount());
        rechargeRecordVO.setCoinType(rechargeAcrossBO.getCoinType());

        // 设置充值正常返点
        CoinTypeEnum coinTypeEnum = CoinTypeEnum.of(rechargeAcrossBO.getCoinType());
        Optional<RechargeRebateInfo> optionalRechargeRebateInfo = Optional.ofNullable(rechargeRebateInfoMap.get(rechargeAcrossBO.getRechargeOrderNum()));
        if (optionalRechargeRebateInfo.isPresent()) {
            RechargeRebateInfo rechargeRebateInfo = optionalRechargeRebateInfo.get();
            rechargeRecordVO.setRebateCoin(Math.max(rechargeRebateInfo.getNum(), 0));
        }
        String rebateInfo = rechargeRecordVO.getRebateCoin() > 0 ? String.format("(含赠送的%s%s)", rechargeRecordVO.getRebateCoin(), this.getCoinDesc(bizLine, coinTypeEnum)) : StringUtils.EMPTY;
        if (rechargeRecordVO.getRebateCoin() > 0) {
            rechargeRecordVO.setRebateInfo(rebateInfo);
        }

        // 设置充值返点异常
        if(apolloConfigService.isCheckLittleRebateWhenLost()){
            BigDecimal amount = rechargeRecordVO.getAmount();
            Optional<RechargePresentItem> optionalRechargePresentItem = rechargePresentMap.getOrDefault(amount.longValue(), Lists.newArrayList())
                    .stream()
                    .filter(rechargePresentItem -> GoodsType.PRESENT.getValue() == rechargePresentItem.getGoodsType() && rechargePresentItem.getAssetId() == 0)
                    .findAny();
            if (optionalRechargePresentItem.isPresent() && rechargeRecordVO.getRebateCoin() <= 0) {
                RechargePresentItem rechargePresentItem = optionalRechargePresentItem.get();
                long presentCoin = rechargePresentItem.getNum();
                rechargeRecordVO.setRebateDelay(1);
                rechargeRecordVO.setRebateInfo(String.format("(赠送%s%s暂未到账，请稍后查看)", presentCoin, this.getCoinDesc(bizLine, coinTypeEnum)));
            }
        }


        // 设置普通充值
        if (rechargeAcrossBO.getReType() == ReTypeEnum.RETYPE_RECHARGE.getReTypeId()) {
            rechargeRecordVO.setBizTypeLabel(AccountChangeTypeEnum.ACCOUNT_CHANGE_TYPE_100001.getLabel());
            if (BizLineEnum.isGameLine(bizLine.getCode())) {
                rechargeRecordVO.setBizTypeLabel("O币");
            }
            if (BizLineEnum.isLiveLine(bizLine.getCode())) {
                rechargeRecordVO.setBizTypeLabel(coinTypeEnum.getDesc());
            }
            String sumCoin = rechargeRecordVO.getCoin().add(BigDecimal.valueOf(rechargeRecordVO.getRebateCoin())).stripTrailingZeros().toPlainString();
            String bizInfo = String.format("已到账%s%s", sumCoin, this.getCoinDesc(bizLine, coinTypeEnum));
            rechargeRecordVO.setBizInfo(bizInfo);
            // 处理一元萌脸
            Optional<String> optionalConsumeType = parseArgumentInExtend(rechargeAcrossBO.getExtend(), "$.callBackArg.consumeArgs.consumeType");
            if (optionalConsumeType.isPresent() && optionalConsumeType.get().equalsIgnoreCase("teaseAnchor")) {
                rechargeRecordVO.setBizInfo("购买“一元赞主播”");
            }
        }
        // 设置开通充值
        if (rechargeAcrossBO.getReType() == ReTypeEnum.RETYPE_PURCHASE.getReTypeId()) {
            Optional<String> optionalAccountChangeType = parseArgumentInExtend(rechargeAcrossBO.getExtend(), "$.callBackArg.userFundPlatParam.accountChangeType");
            optionalAccountChangeType.ifPresent(s -> rechargeRecordVO.setBizTypeLabel(AccountChangeTypeEnum.bizTypeLabelOf(s)));
            String bizInfo = String.format("已开通%s", rechargeRecordVO.getBizTypeLabel());
            // 特殊处理赠送VIP充值
            if (optionalAccountChangeType.isPresent() && AccountChangeTypeEnum.ACCOUNT_CHANGE_TYPE_110032.getValue().equals(optionalAccountChangeType.get())) {
                bizInfo = bizInfo.replace("开通", "赠送");
            }
            rechargeRecordVO.setBizInfo(bizInfo);
        }
        // 设置续费充值
        if (rechargeAcrossBO.getReType() == ReTypeEnum.RETYPE_RENEWALS.getReTypeId()) {
            Optional<String> optionalAccountChangeType = parseArgumentInExtend(rechargeAcrossBO.getExtend(), "$.callBackArg.userFundPlatParam.accountChangeType");
            optionalAccountChangeType.ifPresent(s -> rechargeRecordVO.setBizTypeLabel(AccountChangeTypeEnum.bizTypeLabelOf(s)));
            String bizInfo = String.format("已续费%s", rechargeRecordVO.getBizTypeLabel());
            rechargeRecordVO.setBizInfo(bizInfo);
        }
        return rechargeRecordVO;
    }

    private String getCoinDesc(BizLineEnum bizLine, CoinTypeEnum coinTypeEnum) {
        return BizLineEnum.isLiveLine(bizLine.getCode()) ? coinTypeEnum.getDesc() : bizLine.getCoinDesc();
    }

    /**
     * 获取充值赠币信息
     *
     * @param month                月份
     * @param rechargeAcrossVOList 充值订单列表
     * @return 充值赠币信息
     */
    private Map<String, RechargeRebateInfo> getRechargeRebateInfoMap(String month, List<RechargeAcrossBO> rechargeAcrossVOList) {
        List<String> rechargeOrderNumList = rechargeAcrossVOList.stream()
                .map(RechargeAcrossBO::getRechargeOrderNum).distinct().collect(toList());
        ListUserRechargeRebateInfosCommand command = new ListUserRechargeRebateInfosCommand(rechargeInfoQueryThriftService, month, rechargeOrderNumList);
        return command.execute();
    }

    /**
     * 解析充值流水extend中的财务变更类型
     *
     * @param extend 扩展信息
     * @return 财务变更类型
     */
    public Optional<String> parseArgumentInExtend(String extend, String path) {
        try {
            if (StringUtils.isBlank(extend)) {
                return Optional.empty();
            }
            Base64.Decoder decoder = Base64.getDecoder();
            Configuration conf = Configuration.defaultConfiguration().addOptions(Option.SUPPRESS_EXCEPTIONS);
            String jsonData = new String(decoder.decode(extend), StandardCharsets.UTF_8);
            return Optional.ofNullable(JsonPath.using(conf).parse(jsonData).read(path, String.class));
        } catch (Exception e) {
            log.error("通过充值流水扩展字段extend，解析属性异常, extend: {}, path: {}", extend, path, e);
            return Optional.empty();
        }
    }

    public RechargeAcrossPO getSuccessRechargeOrder(BizLineEnum bizLineEnum, String rechargeOrderNum) {
        Optional<String> optionalYearMonth = orderIdService.getYearMonthFromRechargeOrderNum(rechargeOrderNum);
        if (!optionalYearMonth.isPresent()) {
            log.error("查询充值成功流水，月份解析异常。rechargeOrderNum: {}", rechargeOrderNum);
            return null;
        }
        if (BizLineEnum.isGameLine(bizLineEnum.getCode())) {
            return this.rechargeOpenDao.getSuccessOrderByRechargeOrderNum(optionalYearMonth.get(), rechargeOrderNum);
        }
        return rechargeAcrossDao.getSuccessOrderByRechargeOrderNum(optionalYearMonth.get(), rechargeOrderNum);
    }

    public RechargeBusinessOrderDTO getRechargePayOrder(RechargeAcrossPO rechargeOrder) {
        List<String> rechargeOrders = new ArrayList<>();
        String rechargeOrderNum = rechargeOrder.getRechargeOrderNum();
        rechargeOrders.add(rechargeOrderNum);
        String orderMonth = DateHelper.formatYearMonth(LongMath.checkedMultiply(rechargeOrder.getAddTime(), 1000));
        List<RechargeBusinessOrderDTO> rechargeBusinessOrderDTOS = getRechargePayOrderByRpc(orderMonth, rechargeOrders);
        if (!CollectionUtils.isEmpty(rechargeBusinessOrderDTOS)) {
            Optional<RechargeBusinessOrderDTO> result = rechargeBusinessOrderDTOS.stream().filter(p -> p.getRechargeOrderNum().equals(rechargeOrderNum)).findFirst();
            if (result.isPresent()) {
                return result.get();
            }
        }
        String rechargeMonth = DateHelper.formatYearMonth(LongMath.checkedMultiply(rechargeOrder.getRechargeTime(), 1000));
        if (rechargeMonth.equals(orderMonth)) {
            return null;
        }
        List<RechargeBusinessOrderDTO> newRechargeBusinessOrderDTOS = getRechargePayOrderByRpc(orderMonth, rechargeOrders);
        if (CollectionUtils.isEmpty(newRechargeBusinessOrderDTOS)) {
            return null;
        }
        Optional<RechargeBusinessOrderDTO> result = newRechargeBusinessOrderDTOS.stream().filter(p -> p.getRechargeOrderNum().equals(rechargeOrderNum)).findFirst();
        return result.orElse(null);
    }

    private List<RechargeBusinessOrderDTO> getRechargePayOrderByRpc(String month, List<String> rechargeOrders) {
        try {
            GetRechargeBusinessOrderRequest getRechargeBusinessOrderRequest = new GetRechargeBusinessOrderRequest();
            getRechargeBusinessOrderRequest.setAppId(1000_0001);
            getRechargeBusinessOrderRequest.setMonth(month);
            getRechargeBusinessOrderRequest.setRechargeOrderIds(rechargeOrders);
            getRechargeBusinessOrderRequest.setSign(FinanceSignUtils.makeSign(getRechargeBusinessOrderRequest, "q4Ya9B7MVq5CYBXs"));
            RechargeBusinessOrderResponse rechargeBusinessOrderResponse = rechargeAllocateQueryService.getRechargeBusinessOrder(getRechargeBusinessOrderRequest);
            if (rechargeBusinessOrderResponse.getCode() != 0) {
                return Collections.emptyList();
            }
            if (CollectionUtils.isEmpty(rechargeBusinessOrderResponse.getData())) {
                return Collections.emptyList();
            }
            return rechargeBusinessOrderResponse.getData();
        } catch (Exception e) {
            log.error("getBusinessOrderRpcFail, month: {}, rechargeOrders: {}", month, rechargeOrders, e);
            return Collections.emptyList();
        }
    }

    public Map<String, Object> callPhpRecharge(PurchaseProductRequest request) {
        String businessId = String.valueOf(orderIdService.generateGlobalId());
        Map<String, String> bodyParams = buildPurchaseProductParams(businessId, request);
        int payTypeId = request.getPayTypeId();
        String method = "";
        if (PayTypeIdEnum.PAY_TYPE_ID_3.getPayTypeId() == payTypeId) {
            method = "getPCAliPay";
        }
        if (PayTypeIdEnum.PAY_TYPE_ID_32.getPayTypeId() == payTypeId) {
            method = "getH5WxPay";
        }
        if (PayTypeIdEnum.PAY_TYPE_ID_33.getPayTypeId() == payTypeId) {
            method = "getH5AliPay";
        }
        if (PayTypeIdEnum.PAY_TYPE_ID_39.getPayTypeId() == payTypeId) {
            method = "getPCWxPay";
        }
        if (PayTypeIdEnum.PAY_TYPE_ID_41.getPayTypeId() == payTypeId) {
            method = "getGzhWxPay";
        }
        if (PayTypeIdEnum.PAY_TYPE_ID_42.getPayTypeId() == request.getPayTypeId()) {
            IPayTypeHandler handler = SpringContextHolder.getBean(PayType42Handler.class);
            return handler.handle(businessId, request);
        }
        if (PayTypeIdEnum.PAY_TYPE_ID_1006.getPayTypeId() == payTypeId) {
            method = "getMobileIOSPay";
        }
        if (StringUtils.isBlank(method)) {
            throw new ContextedRuntimeException("支付类型暂不支持").setContextValue("payTypeId", payTypeId);
        }
        String url = String.format("http://%s/RechargePlat/RechargeService/PaymentService/%s", this.rechargeConfig.getPhpRechargeIntranet(), method);
        Map<String, String> urlParams = ImmutableMap.of("args", "[]");
        log.warn("调用PHP充扣下单接口，请求参数。url: {}, urlParams: {}, bodyParams: {}", url, urlParams, bodyParams);
        Optional<String> optionalJson = HttpClientUtils.doPostJSON(url, urlParams, bodyParams);
        if (!optionalJson.isPresent() || StringUtils.isBlank(optionalJson.get())) {
            log.warn("调用PHP充扣下单接口，调用失败。request: {}, optionalJson: {}", request, optionalJson);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        String json = optionalJson.get();
        log.warn("调用PHP充扣下单接口，PHP接口返回。json: {}", json);
        int status = JsonPath.parse(json).read("$.status", Integer.class);
        int errorNo = JsonPath.parse(json).read("$.errorno", Integer.class);
        if (status == 0 || errorNo != 0) {
            log.warn("调用PHP充扣下单接口，PHP接口返回失败。json: {}", json);
            String errorMsg = JsonPath.parse(json).read("$.errorcode", String.class);
            throw new BizException(errorNo, StringUtils.defaultString(errorMsg));
        }
        return JsonPath.parse(json).read("$.data");
    }

    public void purchaseOrderListCheck(PurchaseProductRequestV2 request) {
        if(!apolloConfigService.isOrderAmountCheck()){
            return;
        }
        if(CollectionUtils.isEmpty(request.getOrderList())){
            throw new BizException(SysResultCode.E_10000020);
        }
        BigDecimal total = new BigDecimal("0");
        for (PurchaseOrder order:request.getOrderList()
             ) {
            BigDecimal goodsNum = new BigDecimal(order.getGoodsNum());
            if(order.getGoodsType() == 2){
                total = total.add(goodsNum);
            }
            if(order.getGoodsType() == 3 || order.getGoodsType() == 54){
                GoodsInfoVO goodsInfo = giftInfoService.getById(order.getGoodsId());
                if(Objects.isNull(goodsInfo)){
                    throw new BizException(SysResultCode.E_10000021);
                }
                total = total.add(goodsInfo.getCoin().multiply(goodsNum));
            }

        }
        BigDecimal payAmount = new BigDecimal(request.amount).multiply(new BigDecimal("100"));
        if(total.compareTo(payAmount) > 0){
            throw new BizException(SysResultCode.E_10000022);
        }
        // 检查充值平台
        if (this.apolloConfigService.isForbiddenStdPlat(request.getPid())) {
            log.warn("调用充扣V2下单接口，充值平台暂停服务。request: {}", request);
            throw new BizException(SysResultCode.FORBIDDEN_RECHARGE_PLAT);
        }
    }

    public String getGiftName(String businessId, PurchaseOrder purchaseOrder) {
        String giftName = apolloConfigService.businessDescOf(businessId);
        // 星际战队船票
        if ("********".equals(businessId) && StringUtils.isNoneBlank(purchaseOrder.getExt())) {
            try {
                giftName = com.kugou.fanxing.recharge.util.JsonUtils.parseJsonPath(purchaseOrder.getExt(), "$.giftName", String.class, giftName);
            } catch (Exception e) {
                log.error("解析礼物名称，解析异常。businessId: {}", businessId, e);
            }
        }
        return giftName;
    }

    public String getActionIdByAccountChangeType(int accountChangeType) {
        AccountChangeTypeBO accountChangeTypeBO = apolloConfigService.getAccountChangeTypeById(accountChangeType);
        if (accountChangeTypeBO != null && accountChangeTypeBO.getActionId() >= 0) {
            return String.valueOf(accountChangeTypeBO.getActionId());
        }
        throw new BizException(SysResultCode.E_10000023);
    }

    public String buildReqExt(PurchaseProductRequestV2 requestV2) {
        try {
            Map<String,Object> reqExt = null;
            if(requestV2.getExtJson() != null){
                reqExt = com.alibaba.fastjson.JSONObject.parseObject(requestV2.getExtJson());
            }

            List<Map<String,Integer>> goodsList = new ArrayList<>();
            for (PurchaseOrder p:requestV2.getOrderList()) {
                Map<String,Integer> goodsMap = new HashMap<>();
                goodsMap.put("goodsId",(int)p.getGoodsId());
                goodsMap.put("goodsNum",p.getGoodsNum());
                goodsMap.put("goodsType",p.getGoodsType());
                goodsList.add(goodsMap);
            }
            if(reqExt == null){
                reqExt = new HashMap<>();
            }
            reqExt.put("goodsList",goodsList);
            if(!MapUtils.isEmpty(reqExt)){
                return JsonUtils.toJsonString(reqExt);
            }
        }catch (Exception e) {
            log.error("buildReqExt Error:",e);
        }
        return null;
    }

    private Map<String, String> buildPurchaseProductParams(String businessId, PurchaseProductRequest request) {
        Map<String, String> bodyParams = Maps.newHashMap();
        bodyParams.put("businessType", request.getBusinessId());
        bodyParams.put("accessToken", "94cd44b3ab99c0b89b5fbfb9b44fb18f");
        bodyParams.put("amount", String.valueOf(request.getAmount()));
        bodyParams.put("fromKugouId", String.valueOf(request.getKugouId()));
        bodyParams.put("toKugouId", String.valueOf(request.getToKugouId()));
        bodyParams.put("businessId", businessId);
        bodyParams.put("businessTime", String.valueOf(request.getBusinessTime()));
        bodyParams.put("subject", request.getSubject());
        bodyParams.put("syncUrl", StringUtils.defaultString(request.getSyncUrl()));
        bodyParams.put("redirectUrl", StringUtils.defaultString(request.getRedirectUrl()));
        bodyParams.put("pid", String.valueOf(request.getPid()));
        bodyParams.put("topic", apolloConfigService.businessNotifyTopicOf(request.getBusinessId()));
        bodyParams.put("clientIp", String.valueOf(request.getClientIp()));
        bodyParams.put("cFrom", String.valueOf(request.getPid()));
        bodyParams.put("refer", "0");
        bodyParams.put("channelId", "0");
        bodyParams.put("businessExt", request.getExtJson());
        bodyParams.put("userFundPlatParam[senderDepartmentId]", "0");
        bodyParams.put("userFundPlatParam[senderProductId]", "0");
        bodyParams.put("userFundPlatParam[senderMinorProductId]", "0");
        bodyParams.put("userFundPlatParam[senderHardwarePlatform]", "0");
        bodyParams.put("userFundPlatParam[senderChannelId]", "0");
        bodyParams.put("userFundPlatParam[senderSubChannelId]", "0");
        bodyParams.put("userFundPlatParam[receiverDepartmentId]", "0");
        bodyParams.put("userFundPlatParam[fromKugouId]", String.valueOf(request.getKugouId()));
        bodyParams.put("userFundPlatParam[toKugouId]", String.valueOf(request.getToKugouId()));
        bodyParams.put("userFundPlatParam[accountChangeType]", String.valueOf(request.getConsumeParam().getAccountChangeType()));
        bodyParams.put("userFundPlatParam[fxcChangeDesc]", String.format("用户kugouId:%s，%s", request.getSubject(), request.getKugouId()));
        bodyParams.put("userFundPlatParam[roomId]", String.valueOf(request.getConsumeParam().getRoomId()));
        bodyParams.put("userFundPlatParam[giftId]", String.valueOf(request.getConsumeParam().getGiftId()));
        bodyParams.put("userFundPlatParam[giftName]", String.valueOf(request.getConsumeParam().getGiftName()));
        bodyParams.put("userFundPlatParam[giftNum]", String.valueOf(request.getConsumeParam().getGiftNum()));
        // TODO 兼容消费的BUG
        String actionId = isBuyConcertTicketBusiness(request.getBusinessId()) ? "293" : "0";
        bodyParams.put("userFundPlatParam[actionId]", actionId);
        bodyParams.put("userFundPlatParam[coin]", new BigDecimal(request.getAmount()).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString());
        bodyParams.put("userFundPlatParam[ext]", request.getConsumeParam().getExt());
        // 微信服务号 openId
        if (StringUtils.isNotEmpty(request.getOpenId())) {
            bodyParams.put("openId", request.getOpenId());
        }
        // 针对演唱会门票业务
        String businessExt = StringUtils.defaultString(request.getExtJson());
        if (isBuyConcertTicketBusiness(request.getBusinessId()) && StringUtils.isNotBlank(businessExt) && businessExt.contains("goodsList")) {
            Configuration conf = Configuration.builder().mappingProvider(new JacksonMappingProvider()).jsonProvider(new JacksonJsonProvider()).build();
            TypeRef<List<GoodsConfigBO>> type = new TypeRef<List<GoodsConfigBO>>() {
            };
            List<GoodsConfigBO> goodsConfigList = JsonPath.using(conf).parse(businessExt).read("$.goodsList", type);
            Optional<GoodsConfigBO> optionalGoodsConfig = goodsConfigList.stream().findFirst();
            if (optionalGoodsConfig.isPresent()) {
                bodyParams.put("userFundPlatParam[giftId]", String.valueOf(optionalGoodsConfig.get().getGoodsId()));
                bodyParams.put("userFundPlatParam[giftNum]", String.valueOf(optionalGoodsConfig.get().getGoodsNum()));
            }
        }
        return bodyParams;
    }

    /**
     * 是否演唱会门票业务
     *
     * @param businessId 充值业务编号
     * @return 是否演唱会门票业务
     */
    private boolean isBuyConcertTicketBusiness(String businessId) {
        return "10000001".equals(businessId);
    }

    public List<PayTypeItem> getMPayList(long kugouId, AndroidCommonParam androidCommonParam) {
        int stdPlat = androidCommonParam.getStd_plat();
        int version = androidCommonParam.getVersion();
        List<Integer> payTypeList = rechargeConfig.getMPayList(stdPlat, version);
        payTypeList = payTypeList.stream().filter(payTypeId -> !SourceIdEnum.isCcType(androidCommonParam.getSourceId())
                || payTypeId != PayTypeIdEnum.PAY_TYPE_ID_35.getPayTypeId()).collect(toList());
        payTypeList = remoteStrategyService.strategyForPayTypeList(kugouId, payTypeList, androidCommonParam);
        log.warn("获取Android手机充值渠道列表, kugouId: {}, payTypeList: {}", kugouId, payTypeList);
        return mergePayChannelInfo(payTypeList);
    }

    private List<PayTypeItem> mergePayChannelInfo(List<Integer> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        List<PayTypeItem> result = new ArrayList<>();
        Map<Integer,PayChannelActivityItem> configMap = apolloConfigService.getPayChannelActivityConfig();
        for (Integer id:ids
             ) {
            PayTypeItem payTypeItem = new PayTypeItem();
            payTypeItem.setPayTypeId(id);
            if(!configMap.containsKey(id)){
                continue;
            }
            if (DateHelper.getCurrentSeconds() > configMap.get(id).getStartTime() && DateHelper.getCurrentSeconds() < configMap.get(id).getEndTime()){
                payTypeItem.setActivity(configMap.get(id));
            }
            result.add(payTypeItem);
        }
        return result;
    }

    public RechargeAcrossListVO getGameRechargeList(long kugouId, int index, int payTypeId, Pagination pagination) {
        RechargeAcrossListVO rechargeAcrossListVO = new RechargeAcrossListVO();
        String month = DateHelper.formatYearMonth(DateUtils.addMonths(new Date(), index == 1 ? -1 : 0));
        int[] reTypes = {ReTypeEnum.RETYPE_RECHARGE.getReTypeId(), ReTypeEnum.RETYPE_PURCHASE.getReTypeId(), ReTypeEnum.RETYPE_RENEWALS.getReTypeId()};
        List<RechargeAcrossPO> rechargeAcrossPOList = rechargeOpenDao.getRechargeAcrossList(month, kugouId, reTypes, payTypeId, pagination);
        List<RechargeAcrossBO> rechargeAcrossBOList = ModelUtils.fromList(rechargeAcrossPOList, RechargeAcrossBO.class);
        int listCount = rechargeOpenDao.getRechargeAcrossListCount(month, kugouId, reTypes, payTypeId);
        List<RechargeRecordVO> rechargeRecordVOList = rechargeAcrossBOList.stream()
                .map(rechargeAcrossBO -> buildRechargeRecordVO(BizLineEnum.GAME, Maps.newHashMap(), Maps.newHashMap(), rechargeAcrossBO))
                .sorted(Comparator.comparingLong(RechargeRecordVO::getAddTime).reversed())
                .collect(toList());
        rechargeAcrossListVO.setCount(listCount);
        rechargeAcrossListVO.setList(rechargeRecordVOList);
        return rechargeAcrossListVO;
    }

}
