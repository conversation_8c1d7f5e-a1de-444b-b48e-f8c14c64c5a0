package com.kugou.fanxing.recharge.service.recharge;

import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.AreaIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.model.ConfirmAirwallexV1;
import com.kugou.fanxing.recharge.model.OrderAirwallexV1;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.*;
import com.kugou.fanxing.recharge.model.vo.BankNameListDto;
import com.kugou.fanxing.recharge.model.vo.LocalAmountVo;
import com.kugou.fanxing.recharge.model.vo.LocalCurrencyConfigPo;
import com.kugou.fanxing.recharge.model.vo.PaymentMethodTypesDto;
import com.kugou.fanxing.recharge.service.AbstractRechargeService;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.recharge.util.Pagination;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class AirwallexRechargeService extends AbstractRechargeService {

    public JsonResult<Map<String, Object>> appCreateOrder(WebCommonParam commonParam, AirwallexCreateRequest request) {
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        boolean isValidCountryCode = this.rechargeCommonService.isValidCountryCode(request.getCountry());
        if (!isValidCountryCode) {
            log.warn("Airwallex手机充值下单接口，国家代码非法。commonParam: {}, request: {}, rechargeOrderNum: {}", commonParam, request, rechargeOrderNum);
            return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, Maps.newHashMap());
        }
        List<LocalCurrencyConfigPo> localCurrencyConfigList = this.rechargeCommonService.queryLocalCurrencyList(request.getCountry());
        Optional<LocalCurrencyConfigPo> optionalLocalCurrencyConfig = localCurrencyConfigList.stream()
                .filter(localCurrencyVo -> localCurrencyVo.getCurrency().equalsIgnoreCase(request.getCurrency()) &&
                        localCurrencyVo.getCurrencyAmount().compareTo(request.getCurrencyAmount()) == 0).findFirst();
        if (!optionalLocalCurrencyConfig.isPresent()) {
            log.warn("Airwallex手机充值下单接口，本地货币非法。commonParam: {}, request: {}, rechargeOrderNum: {}", commonParam, request, rechargeOrderNum);
            return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, Maps.newHashMap());
        }
        LocalCurrencyConfigPo localCurrencyConfigPo = optionalLocalCurrencyConfig.get();
        request.setAmount(localCurrencyConfigPo.getCnyAmount());
        // 下单前置校验
        SysResultCode resultCode = rechargeCommonService.checkPreconditionForGetOrder(rechargeOrderNum, commonParam, request);
        if (!resultCode.isSuccess()) {
            log.warn("Airwallex手机充值下单接口，前置校验失败。commonParam: {}, request: {}, rechargeOrderNum: {}", commonParam, request, rechargeOrderNum);
            return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, Maps.newHashMap());
        }
        boolean force3DS = this.remoteStrategyService.forceAirwallexCard3DS(commonParam.getKugouId(), rechargeOrderNum, request);
        RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(commonParam.getStdPlat(),
                commonParam.getKugouId(), rechargeOrderNum, request.getPayTypeIdEnum(), request.getAmount(), commonParam.getIp());
        rechargeAcrossPO.setCurrency(request.getCurrency());
        rechargeAcrossPO.setCurrencyAmount(request.getCurrencyAmount());
        rechargeAcrossPO.setAreaId(AreaIdEnum.OUT_SEA.getAreaId());
        rechargeAcrossPO.setCoin(localCurrencyConfigPo.getCoin());
        rechargeAcrossPO.setUsdAmount(localCurrencyConfigPo.getUsdAmount());
        rechargeAcrossPO.setAreaCode(commonParam.getAreaCode());
        rechargeAcrossPO.setTimeZone(commonParam.getTimeZone());
        // 支付参数
        Map<String, String> kupaysParam = Maps.newHashMap();
        kupaysParam.put("return_url", request.getReturnUrl());
        kupaysParam.put("username", StringUtils.defaultString(request.getBusinessName()));
        kupaysParam.put("email", StringUtils.defaultString(request.getEmail()));
        kupaysParam.put("is_card", String.valueOf(request.getIsCard()));
        kupaysParam.put("force_3DS", Boolean.toString(force3DS));
        Map<String, Object> extendParam = Maps.newHashMap();
        OrderAirwallexV1 orderAirwallexV1 = this.kupayService.orderAirwallexV1(rechargeAcrossPO, extendParam, kupaysParam);
        rechargeAcrossPO.setConsumeOrderNum(orderAirwallexV1.getOut_trade_no());
        // 保存充值订单
        int affected = rechargeOrderService.addRechargeOrder(rechargeAcrossPO);
        if (affected < 1) {
            log.warn("Airwallex手机充值下单接口，保存订单失败。commonParam: {}, request: {}, rechargeOrderNum: {}", commonParam, request, rechargeOrderNum);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        Map<String, Object> payload = Maps.newHashMap();
        payload.put("rechargeOrderNum", rechargeAcrossPO.getRechargeOrderNum());
        payload.put("outTradeNo", rechargeAcrossPO.getConsumeOrderNum());
        payload.put("intent", orderAirwallexV1.getIntent());
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

    public JsonResult<Map<String, Object>> appConfirmOrder(WebCommonParam commonParam, AirwallexConfirmRequest request) {
        SysResultCode sysResultCode = this.rechargeCommonService.checkParameter(commonParam, request);
        if (!sysResultCode.isSuccess()) {
            return JsonResult.result(sysResultCode, Maps.newHashMap());
        }
        Optional<RechargeAcrossPO> optionalRechargeAcrossPO = this.rechargeOrderService.queryByRechargeOrderNum(request.getRechargeOrderNum());
        // 注意⚠️：通过ACK触发前端跨机房重试，避免极端场景跨机房确认
        if (!optionalRechargeAcrossPO.isPresent()) {
            log.warn("Airwallex手机下单确认接口，未发现充值下单记录。commonParam: {}, request: {}", commonParam, request);
            throw new AckException(SysResultCode.DORETRY);
        }
        // 检查下单用户与网关订单号
        RechargeAcrossPO rechargeAcrossPO = optionalRechargeAcrossPO.get();
        if (rechargeAcrossPO.getKugouId() != commonParam.getKugouId() || !rechargeAcrossPO.getConsumeOrderNum().equals(request.getOutTradeNo())) {
            log.warn("Airwallex手机下单确认接口，下单信息校验不一致。commonParam: {}, request: {}", commonParam, request);
            return JsonResult.result(SysResultCode.E_100048, Maps.newHashMap());
        }
        ConfirmAirwallexV1 confirmAirwallexV1 = this.kupayService.confirmAirwallexV1(rechargeAcrossPO, request.getPaymentType(), request.getPaymentMethod());
        Map<String, Object> payload = Maps.newHashMap();
        payload.put("nextAction", confirmAirwallexV1);
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

    public JsonResult<Map<String, Object>> getPaymentMethodTypes(WebCommonParam webCommonParam, GetPaymentMethodTypesRequest request) {
        SysResultCode sysResultCode = this.rechargeCommonService.checkParameter(webCommonParam, request);
        if (!sysResultCode.isSuccess()) {
            return JsonResult.result(sysResultCode, Maps.newHashMap());
        }
        long kugouId = webCommonParam.getKugouId();
        Pagination pagination = new Pagination.Builder(request.getPage(), request.getPageSize()).build();
        PaymentMethodTypesDto paymentMethodTypesDto = this.rechargeCommonService.getPaymentMethodTypes(kugouId, request.getCountry(), pagination);
        List<LocalAmountVo> localAmountVoList = this.rechargeCommonService.queryLocalAmountVoList(request.getCountry());
        Map<String, Object> payload = Maps.newHashMap();
        payload.put("amountList", localAmountVoList);
        payload.put("methodList", paymentMethodTypesDto);
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

    public JsonResult<Map<String, Object>> getBankNameList(WebCommonParam webCommonParam, GetBankNameListRequest request) {
        SysResultCode sysResultCode = this.rechargeCommonService.checkParameter(webCommonParam, request);
        if (!sysResultCode.isSuccess()) {
            return JsonResult.result(sysResultCode, Maps.newHashMap());
        }
        Pagination pagination = new Pagination.Builder(request.getPage(), request.getPageSize()).build();
        BankNameListDto bankNameList = this.kupayService.bankListsAirwallexV1(request.getCountry(), request.getPaymentMethodType(), pagination);
        Map<String, Object> payload = Maps.newHashMap();
        payload.put("bankNameList", bankNameList);
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }
}
