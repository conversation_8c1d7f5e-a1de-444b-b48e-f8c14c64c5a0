package com.kugou.fanxing.recharge.service.common;

import com.kugou.fanxing.ip.api.FXIpService;
import com.kugou.fanxing.ip.api.SimpleIpInfo;
import com.kugou.fanxing.recharge.service.command.GetSimpleIPInfoByIpCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class IpLocationService {

    private static final String COUNTRY_CHINA = "中国";
    private static final String PROVINCE_TAIWAN = "台湾";
    private static final String INTRANET = "局域网";

    @Autowired
    private FXIpService.Iface fxIpService;

    /**
     * 判断是否为海外IP地址
     */
    public boolean isOverseasIp(String ip) {
        Optional<SimpleIpInfo> optionalSimpleIpInfo = new GetSimpleIPInfoByIpCommand(fxIpService, ip).execute();
        if (optionalSimpleIpInfo.isPresent()) {
            SimpleIpInfo simpleIpInfo = optionalSimpleIpInfo.get();
            // 局域网
            if (INTRANET.equalsIgnoreCase(simpleIpInfo.getCountry())) {
                return false;
            }
            // 中国（排除台湾省）
            return !COUNTRY_CHINA.equalsIgnoreCase(simpleIpInfo.getCountry()) || PROVINCE_TAIWAN.equalsIgnoreCase(simpleIpInfo.getProvice());
        }
        return true;
    }
}
