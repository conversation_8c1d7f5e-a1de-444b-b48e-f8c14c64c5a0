package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.risk.sdk.RiskStrategyService;
import com.kugou.fanxing.risk.sdk.model.RiskResult;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class RiskStrategyServiceConcludeCommand extends HystrixCommand<Optional<RiskResult>> {

    private final HttpServletRequest request;
    private final RiskStrategyService riskStrategyService;
    private final Map<String, Object> paramsMap;

    public RiskStrategyServiceConcludeCommand(final RiskStrategyService riskStrategyService, final HttpServletRequest request, final Map<String, Object> paramsMap) {
        super(HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("RiskStrategyServiceConcludeCommand")).
                andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(500)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(30))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(350)));
        this.request = request;
        this.riskStrategyService = riskStrategyService;
        this.paramsMap = paramsMap;
    }

    @Override
    protected Optional<RiskResult> run() throws Exception {
        return Optional.ofNullable(riskStrategyService.conclude(request, paramsMap));
    }

    @Override
    protected Optional<RiskResult> getFallback() {
        Optional<RiskResult> fallback = Optional.empty();
        log.warn("RiskStrategyServiceConcludeCommand服务降级! 风控服务校验异常, paramsMap: {}, 降级返回数据: {}, 降级原因: {}",
                paramsMap, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }
}

