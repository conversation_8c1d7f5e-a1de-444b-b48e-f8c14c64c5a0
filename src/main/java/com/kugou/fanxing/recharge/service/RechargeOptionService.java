package com.kugou.fanxing.recharge.service;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.google.common.collect.Lists;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeBigRebateConfigDao;
import com.kugou.fanxing.recharge.model.po.RechargeBigRebateConfigPO;
import com.kugou.fanxing.recharge.model.vo.*;
import com.kugou.platform.after.recharge.asset.allocate.thrift.AfterRechargeAssetAllocateReadService;
import com.kugou.platform.after.recharge.asset.allocate.thrift.GetRechargePresentRequest;
import com.kugou.platform.after.recharge.asset.allocate.thrift.PresentInfo;
import com.kugou.platform.after.recharge.asset.allocate.thrift.RechargePresentItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RechargeOptionService {

    /**
     * 唱币充值列表
     */
    private static final int RECHARGE_OPTIONS_SOURCE_SING_COIN = 100002;

    @Autowired
    private AfterRechargeAssetAllocateReadService.Iface afterRechargeAssetAllocateReadService;

    @ApolloConfig("PLATFORM.recharge.present.logic")
    private Config config;

    @Autowired
    private RechargeBigRebateConfigDao rechargeBigRebateConfigDao;

    @Autowired
    private NoviceRechargeBusService noviceRechargeBusService;

    public RechargeOptionItem getBySpecialNum(Long num, Long kugouId, int coinType) {
        if (num == null) {
            return null;
        }
        RechargeOptionItem rechargeOptionItem = new RechargeOptionItem();
        rechargeOptionItem.setMoney(num);
        rechargeOptionItem.setCoins(num * 100);
        rechargeOptionItem.setPresentList(new ArrayList<>());
        if (CoinTypeEnum.isSingCoinType(coinType)) {
            return rechargeOptionItem;
        }
        List<Long> moneys = new ArrayList<>();
        moneys.add(num);
        //获取所有的充值赠礼
        try {
            GetRechargePresentRequest rechargePresentRequest = new GetRechargePresentRequest();
            rechargePresentRequest.setMoneys(moneys);
            rechargePresentRequest.setKgId(Optional.ofNullable(kugouId).orElse(0L));
            PresentInfo presentInfo = afterRechargeAssetAllocateReadService.getRechargePresentInfo(rechargePresentRequest);
            if (presentInfo.getRet() == 0 && presentInfo.getData() != null && presentInfo.getData().containsKey(num)) {
                rechargeOptionItem.setPresentList(convertPresentItemList(presentInfo.getData().get(num)));
            }
            NoviceRechargeVO noviceRechargeVO = noviceRechargeBusService.getNoviceRechargeConfigNew(Optional.ofNullable(kugouId).orElse(0L));
            PresentItem presentItem = getFirstRechargePresentByAmount(num,noviceRechargeVO);
            List<PresentItem> firstPack = new ArrayList<>();
            if(presentItem != null) {
                firstPack.add(presentItem);
            }
            rechargeOptionItem.setFirstRechargePack(firstPack);
        } catch (Exception e) {
            log.error("getAfterRechargeSettingFail param:{},exception:", moneys, e);
        }
        //简单做个大额配置过滤,后面应该统一做在充值后
        if (num >= config.getIntProperty("maybe.recharge.big.rebate.config", 100000)) {
            rechargeOptionItem.setPresentList(mergeBigRebate(num, rechargeOptionItem.getPresentList()));
        }
        return rechargeOptionItem;
    }

    private List<PresentItem> mergeBigRebate(Long money, List<PresentItem> list) {
        PresentItem bigRebate = getBigRebate(money);
        if (bigRebate == null) {
            return list;
        }
        list = list.stream().filter(presentItem -> !presentItem.getType().equals("coin")).collect(Collectors.toList());
        list.add(bigRebate);
        return list;
    }

    private PresentItem getBigRebate(Long money) {
        if (money == null) {
            return null;
        }
        BigDecimal newMoney = new BigDecimal(money);
        RechargeBigRebateConfigPO rechargeBigRebateConfigPO = rechargeBigRebateConfigDao.getConfig(newMoney);
        if (rechargeBigRebateConfigPO == null) {
            return null;
        }
        PresentItem presentItem = new PresentItem();
        long rebateCoins = rechargeBigRebateConfigPO.getRebateRate().multiply(new BigDecimal(100)).multiply(newMoney).longValue();
        presentItem.setNum(rebateCoins);
        presentItem.setType("coin");
        presentItem.setText(String.format("+送%s星币", rebateCoins));
        return presentItem;
    }

    public List<RechargeAmountGear> getRechargeAmountGear(int stdPlat) {
        List<RechargeAmountGear> defaultGearList = Lists.newArrayList(
                RechargeAmountGear.builder().money(10).coins(1000).build(),
                RechargeAmountGear.builder().money(30).coins(3000).build(),
                RechargeAmountGear.builder().money(100).coins(10000).build(),
                RechargeAmountGear.builder().money(300).coins(30000).build(),
                RechargeAmountGear.builder().money(600).coins(60000).build()
        );
        try {
            String configStr = config.getProperty(String.format("android.recharge.gearList.list_%d", stdPlat), "");
            if (StringUtils.isBlank(configStr)) {
                log.warn("充值金额档位，无指定配置返回默认值。stdPlat: {}", stdPlat);
                return defaultGearList;
            }
            List<RechargeAmountGear> newGearList = JSON.parseArray(configStr, RechargeAmountGear.class);
            return CollectionUtils.isNotEmpty(newGearList) ? newGearList : defaultGearList;
        } catch (Exception e) {
            log.error("充值金额档位，获取异常。stdPlat: {}", stdPlat, e);
            return defaultGearList;
        }
    }

    public RechargeAmountPresent getRechargePresentList(int stdPlat) {
        RechargeAmountPresent defaultPresent = RechargeAmountPresent.builder()
                .ruleUrl("").banners(Lists.newArrayList()).presents(Lists.newArrayList()).build();
        try {
            String configStr = config.getProperty(String.format("recharge.present.template_%d", stdPlat), "");
            RechargeAmountPresent rechargeAmountPresent = JSON.parseObject(configStr, RechargeAmountPresent.class);
            return Objects.nonNull(rechargeAmountPresent) ? rechargeAmountPresent : defaultPresent;
        } catch (Exception e) {
            log.error("充值金额赠品，获取异常。stdPlat: {}", stdPlat, e);
            return defaultPresent;
        }
    }

    public List<RechargeOptionItem> getDefaultList() {
        return getDefaultListByPid(null, null, null, 0, Optional.empty());
    }

    public List<RechargeOptionItem> getDefaultListByPid(PayTypeIdEnum payTypeIdEnum, Integer pid, Long kugouId, int source, Optional<String> optionalActFlag) {
        String configStr = getConfigStr(payTypeIdEnum, pid, source);
        if (configStr == null) {
            return Collections.emptyList();
        }
        List<RechargeOptionItem> result = JSON.parseArray(configStr, RechargeOptionItem.class);
        if (result == null || result.isEmpty()) {
            return Collections.emptyList();
        }
        // 唱币充值页面定制化处理
        if (source == RECHARGE_OPTIONS_SOURCE_SING_COIN) {
            return result;
        }
        List<Long> moneys = result.stream().map(RechargeOptionItem::getMoney).collect(Collectors.toList());
        try {
            GetRechargePresentRequest rechargePresentRequest = new GetRechargePresentRequest();
            rechargePresentRequest.setMoneys(moneys);
            rechargePresentRequest.setKgId(Optional.ofNullable(kugouId).orElse(0L));
            rechargePresentRequest.setActFlag(optionalActFlag.orElse(""));
            PresentInfo presentInfo = afterRechargeAssetAllocateReadService.getRechargePresentInfo(rechargePresentRequest);
            NoviceRechargeVO noviceRechargeVO = noviceRechargeBusService.getNoviceRechargeConfigNew(Optional.ofNullable(kugouId).orElse(0L));
            fillFirstRechargePack(result,noviceRechargeVO);
            if (presentInfo.getRet() == 0 && presentInfo.getData() != null) {
                return fillPresentList(result, presentInfo.getData());
            }
        } catch (Exception e) {
            log.error("getAfterRechargeSettingFail param:{},exception:", moneys, e);
        }
        return result;
    }

    private String getConfigStr(PayTypeIdEnum payTypeIdEnum, Integer pid, int source) {
        String configStr;
        if (source > 0) {
            return config.getProperty(String.format("recharge.option.source.%s", source), "[]");
        }
        if (payTypeIdEnum != null) {
            // 这里需求是 针对paypal做特殊处理
            configStr = config.getProperty(String.format("recharge.option.payType.%s", payTypeIdEnum.getPayTypeId()), "[]");
        } else {
            if (pid != null) {
                configStr = config.getProperty(String.format("default.recharge.option.list_%d", pid), "[]");
            } else {
                //默认的时候用这个key,兼容旧版，确认旧版无流量可以下掉
                configStr = config.getProperty("android.default.recharge.option.list", "[]");
            }
        }
        return configStr;
    }

    private List<RechargeOptionItem> fillFirstRechargePack(List<RechargeOptionItem> rechargeOptionItems, NoviceRechargeVO noviceRechargeVO) {
       for (RechargeOptionItem rechargeOptionItem : rechargeOptionItems) {
           PresentItem presentItem = getFirstRechargePresentByAmount(rechargeOptionItem.getMoney(),noviceRechargeVO);
           List<PresentItem> firstPack = new ArrayList<>();
           if(presentItem != null) {
               firstPack.add(presentItem);
           }
           rechargeOptionItem.setFirstRechargePack(firstPack);
        }
        return rechargeOptionItems;
    }

    private PresentItem getFirstRechargePresentByAmount(long money, NoviceRechargeVO noviceRechargeVO) {
        if (noviceRechargeVO == null || CollectionUtils.isEmpty(noviceRechargeVO.getAwardConfig())) {
            return null;
        }
        noviceRechargeVO.setAwardConfig(noviceRechargeVO.getAwardConfig().stream().sorted(Comparator.comparing(NoviceRechargeBaseVO::getRechargeAmount).reversed()).collect(Collectors.toList()));
        for (NoviceRechargeBaseVO noviceRechargeBaseVO : noviceRechargeVO.getAwardConfig()) {
            if(money >= noviceRechargeBaseVO.getRechargeAmount().longValue()) {
                PresentItem presentItem = new PresentItem();
                presentItem.setText(String.format("送%d元首充礼包",noviceRechargeBaseVO.getTotalCoin()/100));
                presentItem.setNum(1);
                presentItem.setType("firstRechargePack");
                return presentItem;
            }
        }
        return null;
    }

    private List<RechargeOptionItem> fillPresentList(List<RechargeOptionItem> rechargeOptionItems, Map<Long, List<RechargePresentItem>> presentMap) {
        if (presentMap.size() != rechargeOptionItems.size()) {
            return rechargeOptionItems;
        }
        for (RechargeOptionItem rechargeOptionItem : rechargeOptionItems) {
            if (presentMap.containsKey(rechargeOptionItem.getMoney())) {
                rechargeOptionItem.setPresentList(convertPresentItemList(presentMap.get(rechargeOptionItem.getMoney())));
            }
        }
        return rechargeOptionItems;
    }

    private List<PresentItem> convertPresentItemList(List<RechargePresentItem> rechargePresentItems) {
        List<PresentItem> presentItems = new ArrayList<>();
        for (RechargePresentItem rechargePresentItem : rechargePresentItems) {
            PresentItem presentItem = convertPresentItem(rechargePresentItem);
            if (presentItem != null) {
                presentItems.add(presentItem);
            }
        }
        return presentItems;
    }

    private PresentItem convertPresentItem(RechargePresentItem rechargePresentItem) {
        String configString = config.getProperty("recharge.present.out.setting", "[]");
        if (configString == null) {
            return null;
        }
        List<RechargePresentSettingItem> result = JSON.parseArray(configString, RechargePresentSettingItem.class);
        if (result == null || result.isEmpty()) {
            return null;
        }
        for (RechargePresentSettingItem rechargePresentSettingItem : result) {
            if (rechargePresentSettingItem.getGoodsType() != rechargePresentItem.getGoodsType() || rechargePresentSettingItem.getAssertId() != rechargePresentItem.getAssetId()) {
                continue;
            }
            if (rechargePresentSettingItem.getType() != null && rechargePresentSettingItem.getTextTemplate() != null && !rechargePresentSettingItem.getType().isEmpty() && !rechargePresentSettingItem.getTextTemplate().isEmpty()) {
                PresentItem presentItem = new PresentItem();
                presentItem.setNum(rechargePresentItem.getNum());
                presentItem.setType(rechargePresentSettingItem.getType());
                presentItem.setText(String.format(rechargePresentSettingItem.getTextTemplate(), rechargePresentItem.getNum()));
                presentItem.setTips(StringUtils.isNotBlank(rechargePresentSettingItem.getTipsTemplate())
                        ? String.format(rechargePresentSettingItem.getTipsTemplate(), rechargePresentItem.getNum()) : "");
                presentItem.setTipsDetail(rechargePresentSettingItem.getTipsDetail());
                return presentItem;
            }

        }
        return null;
    }
}
