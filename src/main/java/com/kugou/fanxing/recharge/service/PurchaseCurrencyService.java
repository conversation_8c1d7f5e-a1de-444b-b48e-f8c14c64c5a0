package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.factory.PaymentInfo;
import com.kugou.fanxing.recharge.factory.PaymentServiceFactory;
import com.kugou.fanxing.recharge.factory.PurchaseRequest;
import com.kugou.fanxing.recharge.thrift.PurchaseCurrencyDto;
import com.kugou.fanxing.recharge.thrift.PurchaseCurrencyRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.ConstraintViolation;
import java.util.Optional;

@Slf4j
@Service
public class PurchaseCurrencyService {

    @Autowired
    private RechargeCommonService rechargeCommonService;
    @Autowired
    private ValidatingService validatingService;
    @Autowired
    private PaymentServiceFactory paymentServiceFactory;
    @Autowired
    private RechargeOrderService rechargeOrderService;

    public PurchaseCurrencyDto purchase(PurchaseCurrencyRequest purchaseCurrencyRequest) {
        String rechargeOrderNum = rechargeOrderService.mappingRechargeOrderNum(
                purchaseCurrencyRequest.getBusinessId(), purchaseCurrencyRequest.getOrderNo());
        PurchaseRequest purchaseRequest = PurchaseRequest.from(purchaseCurrencyRequest, rechargeOrderNum);
        checkPurchaseRequest(purchaseRequest);
        PaymentInfo paymentInfo = this.paymentServiceFactory.create(purchaseCurrencyRequest.getPayTypeId())
                .purchase(purchaseRequest);
        PurchaseCurrencyDto purchaseCurrencyDto = new PurchaseCurrencyDto();
        purchaseCurrencyDto.setRechargeOrderNum(paymentInfo.getRechargeOrderNum());
        purchaseCurrencyDto.setOutTradeNo(paymentInfo.getOutTradeNo());
        purchaseCurrencyDto.setPaymentJson(paymentInfo.getPaymentJson());
        return purchaseCurrencyDto;
    }

    private void checkPurchaseRequest(PurchaseRequest purchaseRequest) {
        // 检查币种（为保证唱唱切服正常，临时限制传参必须coinType=2)
        if (!CoinTypeEnum.isSingCoinType(purchaseRequest.getCoinType())) {
            log.warn("充值购买虚拟货币下单接口，接口仅支持唱币充值。purchaseRequest: {}", purchaseRequest);
            throw new BizException(SysResultCode.RECHARGE_INVALID_COIN_TYPE);
        }
        // 唱币充值开关
        if (this.rechargeCommonService.isForbiddenSingCoin(purchaseRequest.getCoinType())) {
            log.warn("充值购买虚拟货币下单接口，已经禁止唱币充值。purchaseRequest: {}", purchaseRequest);
            throw new BizException(SysResultCode.RECHARGE_FORBIDDEN_SING_COIN);
        }
        // 禁止description参数包含空格
        if (StringUtils.containsWhitespace(purchaseRequest.getDescription())) {
            log.warn("充值购买虚拟货币下单接口，description参数不允许包含空格。purchaseRequest: {}", purchaseRequest);
            throw new BizException(SysResultCode.RECHARGE_INVALID_DESCRIPTION);
        }
        // 参数校验
        Optional<ConstraintViolation<PurchaseRequest>> optionalConstraint = this.validatingService.checkViolation(purchaseRequest);
        if (optionalConstraint.isPresent()) {
            log.warn("充值购买虚拟货币下单接口，请求参数非法。purchaseRequest: {}, optionalConstraint: {}",
                    purchaseRequest, optionalConstraint);
            throw new BizException(SysResultCode.RECHARGE_PARAM_ERROR);
        }
        // 外部校验
        SysResultCode sysResultCode = this.rechargeCommonService.checkThirdPart(purchaseRequest.getStdPlat(), purchaseRequest.getKugouId(),
                purchaseRequest.getRechargeOrderNum(), purchaseRequest.getPayTypeId(), purchaseRequest.getAmount());
        if (!sysResultCode.isSuccess()) {
            log.warn("充值购买虚拟货币下单接口，处理外部检查拦截。purchaseRequest: {}", purchaseRequest);
            throw new BizException(sysResultCode);
        }
    }
}
