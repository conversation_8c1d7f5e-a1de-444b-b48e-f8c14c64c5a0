package com.kugou.fanxing.recharge.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.model.vo.GatewayPayCallbackParam;
import com.kugou.fanxing.recharge.model.vo.GatewayPayRecordVo;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;

/**
 * 返回JSON格式:
 * {
 * error_code: 0, //返回码，0成功，其他失败
 * error_msg:""
 * data:[
 * {
 * order_no:"string", //业务订单号
 * out_trade_no: "string", //网关订单号
 * total_fee: "string",//单位：元，最多保留小数点后两位
 * trade_status: int, //支付状态：1成功，0未回调，2失败
 * callback_status: int, //yyyy-mm-dd业务回调状态：1成功，0未回调，2失败
 * order_time:int, //下单时间
 * pay_time:int //支付成功时间
 * },{
 * order_no:"string", //业务订单号
 * out_trade_no: "string", //网关订单号
 * total_fee: "string",//单位：元，最多保留小数点后两位
 * trade_status: int, //支付状态：1成功，0未回调，2失败
 * callback_status: int, //yyyy-mm-dd业务回调状态：1成功，0未回调，2失败
 * order_time:int, //下单时间
 * pay_time:int //支付成功时间
 * }
 * ]
 * }
 */
@Slf4j
@Component
public class GatewayPaymentBasicService {

    @Value("${kupay.success.orders.url}")
    private String SUCCESS_ORDERS_URL;

    @Value("${kupay.orderstatus.getbyorderno.url}")
    private String GET_ORDER_URL;

    @Value("${kupay.orderstatus.getbyorderno.token}")
    private String TOKEN;
    
    @Autowired
    private ApolloConfigService apolloConfigService;
    
    private String getSuccessOrdersFromRpcWithStrategy(long fromTime, long toTime) {
        return getConfAppidSuccessOrdersFromRpc(fromTime, toTime);
    }

    private String getConfAppidSuccessOrdersFromRpc(long fromTime, long toTime) {
    	List<GatewayPayRecordVo> list = Lists.newArrayList();
        for (String appid : apolloConfigService.getKupayOrderAppids()) {
        	List<GatewayPayRecordVo> subList = getAppidSuccessOrdersFromRpc(appid, fromTime, toTime);
       		if (CollectionUtils.isNotEmpty(subList)) {
       			list.addAll(subList);
       		}
        }
        return (CollectionUtils.isNotEmpty(list) ? JSON.toJSONString(list) : null);
    }

    private List<GatewayPayRecordVo> getAppidSuccessOrdersFromRpc(String appid, long fromTime, long toTime) {
    	if (StringUtils.isBlank(appid)) {
    		return Lists.newArrayList();
    	}
    	
        try {
            String startTime = String.valueOf(fromTime);
            String endTime = String.valueOf(toTime);
            String time = String.valueOf(System.currentTimeMillis() / 1000);
            String sign = DigestUtils.md5Hex(startTime + endTime + time + TOKEN);

            Map<String, String> params = Maps.newTreeMap();
            params.put("appid", appid);
            params.put("startTime", startTime);
            params.put("endTime", endTime);
            params.put("time", time);
            params.put("sign", sign);

            Optional<String> resp = HttpClientUtils.doSyncGet(SUCCESS_ORDERS_URL, params);
            // 校验网关响应
            if (!resp.isPresent()) {
                log.error("GatewayPaymentBasicService.getSingleAppidSuccessOrdersFromRpc,网关接口调用失败 url: {}, params: {}, resp: {}", SUCCESS_ORDERS_URL, params, resp);
        		return Lists.newArrayList();
            }
            JSONObject jsonObj = JSON.parseObject(resp.get());
            Integer recode = jsonObj.getInteger("error_code");
            if (recode != 0) {
                log.error("GatewayPaymentBasicService.getSingleAppidSuccessOrdersFromRpc, 网关接口调用失败, responseBody: {}", resp.get());
        		return Lists.newArrayList();
            }
            return JSON.parseArray(jsonObj.getString("data"), GatewayPayRecordVo.class);
        } catch (Exception e) {
            log.error("GatewayPaymentBasicService.getSingleAppidSuccessOrdersFromRpc exception", e);
    		return Lists.newArrayList();
        }
    }

    private String getSuccessOrdersFromRpc(long fromTime, long toTime) {
        try {
            String startTime = String.valueOf(fromTime);
            String endTime = String.valueOf(toTime);
            String time = String.valueOf(System.currentTimeMillis() / 1000);
            String sign = DigestUtils.md5Hex(startTime + endTime + time + TOKEN);

            Map<String, String> params = Maps.newTreeMap();
            params.put("startTime", startTime);
            params.put("endTime", endTime);
            params.put("time", time);
            params.put("sign", sign);

            Optional<String> resp = HttpClientUtils.doSyncGet(SUCCESS_ORDERS_URL, params);
            // 校验网关响应
            if (!resp.isPresent()) {
                log.error("GatewayPaymentBasicService.getSuccessOrders,网关接口调用失败 url: {}, params: {}, resp: {}", SUCCESS_ORDERS_URL, params, resp);
                return null;
            }
            JSONObject jsonObj = JSON.parseObject(resp.get());
            Integer recode = jsonObj.getInteger("error_code");
            if (recode != 0) {
                log.error("GatewayPaymentBasicService.getSuccessOrders, 网关接口调用失败, responseBody: {}", resp.get());
                return null;
            }
            return jsonObj.getString("data");
        } catch (Exception e) {
            log.error("GatewayPaymentBasicService.getSuccessOrders exception", e);
            return null;
        }
    }

    public List<GatewayPayRecordVo> getSuccessOrders(long fromTime, long toTime) {
        GetSuccessOrdersCommand command = new GetSuccessOrdersCommand(fromTime, toTime, this);
        try {
            String jsonArray = command.execute();
            if (jsonArray == null) {
                return Collections.emptyList();
            }
            return JSON.parseArray(jsonArray, GatewayPayRecordVo.class);
        }catch (Exception e){
            log.error("GatewayPaymentBasicService.getSuccessOrders exception", e);
        }
        return Collections.emptyList();
    }

    public GatewayPayCallbackParam getOrderByNo(String orderNo) {
        try {
            String time = String.valueOf(System.currentTimeMillis() / 1000);
            String sign = DigestUtils.md5Hex(orderNo + time + TOKEN);
            Map<String, String> params = Maps.newTreeMap();
            params.put("time", time);
            params.put("sign", sign);
            params.put("order_no", orderNo);

            Optional<String> resp = HttpClientUtils.doSyncGet(GET_ORDER_URL, params);

            // 校验网关响应
            if (!resp.isPresent()) {
                log.error("GatewayPaymentBasicService.getSuccessOrders,网关接口调用失败 url: {}, params: {}, resp: {}", SUCCESS_ORDERS_URL, params, resp);
                return null;
            }
            JSONObject jsonObj = JSON.parseObject(resp.get());
            Integer recode = jsonObj.getInteger("error_code");
            // 订单不存在
            if (recode == 30926) {
                return null;
            } else if (recode != 0) {
                log.error("GatewayPaymentBasicService.getOrderByNo, 网关接口调用失败, responseBody: {}", resp.get());
                return null;
            }
            return JSONObject.parseObject(jsonObj.getString("data"), GatewayPayCallbackParam.class);
        } catch (Exception e) {
            log.error("GatewayPaymentBasicService.getOrderByNo exception", e);
            return null;
        }
    }



    /**
     * 站内信断路器
     */
    public static class GetSuccessOrdersCommand extends HystrixCommand<String> {

        private final Long startTime;
        private final Long endTime;
        private final GatewayPaymentBasicService gatewayPaymentBasicService;

        private GetSuccessOrdersCommand(Long startTime,Long endTime,GatewayPaymentBasicService gatewayPaymentBasicService) {
            super(HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("GetSuccessOrdersCommand")).
                    andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                            .withMaxQueueSize(5)
                            .withQueueSizeRejectionThreshold(3)
                            .withCoreSize(3))
                    .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                            .withExecutionTimeoutInMilliseconds(5000)));

            this.startTime = startTime;
            this.endTime = endTime;
            this.gatewayPaymentBasicService = gatewayPaymentBasicService;
        }

        @Override
        protected String run() throws Exception {
            return gatewayPaymentBasicService.getSuccessOrdersFromRpcWithStrategy(startTime,endTime);
        }

        @Override
        protected String getFallback() {
            log.error("GatewayPaymentBasicService.getSuccessOrders,网关接口调用降级");
            return null;
        }
    }

}
