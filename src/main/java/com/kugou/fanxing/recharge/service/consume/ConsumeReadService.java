package com.kugou.fanxing.recharge.service.consume;

import com.google.gson.Gson;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.factory.ConsumeServiceFactory;
import com.kugou.fanxing.recharge.model.vo.UserLogVO;
import com.kugou.fanxing.thrift.consume.service.ConsumeResp;
import com.taobao.api.internal.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
public class ConsumeReadService {

    @Autowired
    private ConsumeServiceFactory consumeServiceFactory;

    public Optional<UserLogVO> checkUserLog(int coinType, String month, long gid, int appId) {
        ConsumeResp consumeResp;
        try {
            consumeResp = consumeServiceFactory.getConsumeReadService(coinType).queryLogByUniqId(month,gid + "_coin",appId);
        }catch (Exception e){
            log.error("回查调整星币订单 exception: ",e);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        if(consumeResp == null){
            log.error("回查调整星币订单 return is null ,month:{},gid:{}",month,gid);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        log.warn("回查调整星币订单 month:{},gid:{},return object:{}",month,gid,consumeResp);
        if(consumeResp.getRet() != 0){
            log.error("回查调整星币订单 return code not right ,month:{},gid:{},return code:{}",month,gid,consumeResp.getData());
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        String data = consumeResp.getData();
        if(StringUtils.isEmpty(data)){
            return Optional.empty();
        }
        UserLogVO userLogVO = new Gson().fromJson(data,UserLogVO.class);
        if(userLogVO == null){
            log.error("回查调整星币订单 return data not right,month:{},gid:{},return data:{}",month,gid,data);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        return Optional.of(userLogVO);
    }
}
