package com.kugou.fanxing.recharge.service.command;

import com.google.common.collect.Lists;
import com.kugou.fanxing.userbaseinfo.service.UserModuleV2BizService;
import com.kugou.fanxing.userbaseinfo.vo.UserListResponse;
import com.kugou.fanxing.userbaseinfo.vo.UserVO;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.List;
import java.util.Optional;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@Slf4j
public class GetUserListByKugouIdListCommand extends HystrixCommand<List<UserVO>> {

    private final UserModuleV2BizService.Iface userModuleV2BizService;
    private final List<Long> kugouIdList;

    public GetUserListByKugouIdListCommand(final UserModuleV2BizService.Iface userModuleV2BizService, final List<Long> kugouIdList) {
        super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("GetUserListByKugouIdListCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(300)));
        this.userModuleV2BizService = userModuleV2BizService;
        this.kugouIdList = kugouIdList;
    }

    @Override
    protected List<UserVO> run() throws Exception {
        Optional<UserListResponse> optionalUserListResponse = Optional.ofNullable(this.userModuleV2BizService.getUserListByKugouIdList(kugouIdList));
        if (!optionalUserListResponse.isPresent() || optionalUserListResponse.get().getRet() != 0) {
            log.error("批量查询用户列表信息失败, kugouIdList: {}, optionalUserListResponse: {}", kugouIdList, optionalUserListResponse);
            return Lists.newArrayList();
        }
        UserListResponse userListResponse = optionalUserListResponse.get();
        List<UserVO> userVOList = userListResponse.getData();
        return CollectionUtils.isNotEmpty(userVOList) ? userVOList : Lists.newArrayList();
    }

    @Override
    protected List<UserVO> getFallback() {
        List<UserVO> fallback = Lists.newArrayList();
        log.warn("GetUserListByKugouIdListCommand 服务降级! 通过酷狗ID查询用户信息出错, kugouIdList: {}! 降级返回数据: {}, 降级原因: {}",
                kugouIdList, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }
}
