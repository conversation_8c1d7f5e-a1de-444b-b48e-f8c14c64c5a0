package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.recharge.service.command.GetFamilyControlCommand;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.thrift.operation.FamilyControllRequest;
import com.kugou.fanxing.thrift.operation.FamilyControllResult;
import com.kugou.fanxing.thrift.operation.FamilyControllService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * 家长控制模式
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteFamilyControlService {

    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private FamilyControllService.Iface familyControllService;

    public boolean checkFamilyControl(long kugouId, int pid) {
        try {
            GetFamilyControlCommand controlCommand = new GetFamilyControlCommand(familyControllService, buildFamilyControlRequest(kugouId));
            Optional<FamilyControllResult> optionalResult = controlCommand.execute();
            if (optionalResult.isPresent()) {
                FamilyControllResult result = optionalResult.get();
                boolean needToBeControl = apolloConfigService.pidNeedToBeControl(pid);
                if (result.getRet() == 0 && Objects.nonNull(result.getData()) && result.getData().getControllStatus() == 1 && needToBeControl) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.warn("调用家长控制服务异常, kugouId: {}, pid: {}", kugouId, pid, e);
        }
        return false;
    }

    public FamilyControllRequest buildFamilyControlRequest(long kugouId) {
        FamilyControllRequest request = new FamilyControllRequest();
        request.setAppId(1003);
        request.setKugouId(kugouId);
        request.setTimestamp(DateHelper.getCurrentSeconds());
        request.setSign(FinanceSignUtils.makeSign(request, "P00jVQxG"));
        return request;
    }
}
