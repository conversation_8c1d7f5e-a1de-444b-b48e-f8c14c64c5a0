package com.kugou.fanxing.recharge.service.refund.v2;

import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.model.dto.GooglePayRefundNotifyDto;
import com.kugou.fanxing.recharge.model.dto.RefundNotifyDto;
import com.kugou.fanxing.recharge.model.po.RefundOrderV2Po;
import com.kugou.fanxing.recharge.util.ParseUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

@Slf4j
@Service
public class GooglePayRefundService extends AbstractRefundService {

    @Override
    protected <T extends RefundNotifyDto> Optional<RefundOrderV2Po> convertRefundNotifyDtoToRefundOrder(T t) {
        if (t instanceof GooglePayRefundNotifyDto) {
            GooglePayRefundNotifyDto googlePayRefundNotifyDto = (GooglePayRefundNotifyDto) t;
            RefundOrderV2Po refundOrderV2Po = buildRefundOrderV2Po(googlePayRefundNotifyDto);
            refundOrderV2Po.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_521.getPayTypeId());
            return Optional.of(refundOrderV2Po);
        }
        throw new IllegalArgumentException("退款对象类型错误");
    }

    private RefundOrderV2Po buildRefundOrderV2Po(GooglePayRefundNotifyDto googlePayRefundNotifyDto) {
        return RefundOrderV2Po.builder()
                .refundId(orderIdService.generateGlobalId())
                .kupayAppId(googlePayRefundNotifyDto.getBiz_appid())
                .outTradeNo(googlePayRefundNotifyDto.getOut_trade_no())
                .tradeNo(googlePayRefundNotifyDto.getTrade_no())
                .kupayOrderNo(googlePayRefundNotifyDto.getOrder_no())
                .originOrderFee(new BigDecimal(googlePayRefundNotifyDto.getOrigin_order_fee()))
                .requestRefundFee(new BigDecimal(googlePayRefundNotifyDto.getRequest_refund_fee()))
                .actualRefundFee(new BigDecimal(googlePayRefundNotifyDto.getActual_refund_fee()))
                .kugouId(0)
                .rechargeOrderNum("")
                .payTypeId(0)
                .status(0)
                .errorReason("")
                .coin(BigDecimal.ZERO)
                .debtCoin(BigDecimal.ZERO)
                .addTime(ParseUtils.tryParseLong(googlePayRefundNotifyDto.getAdd_time(), 0))
                .refundTime(ParseUtils.tryParseLong(googlePayRefundNotifyDto.getRefund_time(), 0))
                .refundType(googlePayRefundNotifyDto.getRefund_type())
                .refundReason(googlePayRefundNotifyDto.getRefund_reason())
                .createTime(new Date())
                .updateTime(new Date())
                .build();
    }
}
