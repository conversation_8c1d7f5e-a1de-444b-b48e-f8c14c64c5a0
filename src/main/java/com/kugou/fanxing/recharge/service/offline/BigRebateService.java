package com.kugou.fanxing.recharge.service.offline;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.kugou.fanxing.recharge.alert.AlerterFacade;
import com.kugou.fanxing.recharge.constant.ApplyTypeEnum;
import com.kugou.fanxing.recharge.constant.ApproveStatusEnum;
import com.kugou.fanxing.recharge.constant.RebateStatusEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.BigRebateConfigDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.BigRebateUserLogDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.BigRechargeApplyDao;
import com.kugou.fanxing.recharge.model.po.offline.BigRebateConfig;
import com.kugou.fanxing.recharge.model.po.offline.BigRebateUserLog;
import com.kugou.fanxing.recharge.model.po.offline.BigRechargeApply;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.model.vo.offline.ApplyRebateVO;
import com.kugou.fanxing.recharge.service.command.PlatformStrategyServiceCommand;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.util.IpUtils;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.Measure;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.PlatformStrategyService;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.StrategyResult;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.StrategyVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class BigRebateService {

    @Autowired
    PlatformStrategyService.Iface platformStrategyService;

    @Autowired
    private OrderIdService orderIdService;

    @Autowired
    private UserFacadeService userFacadeService;

    @Autowired
    private BigRebateConfigDao bigRebateConfigDao;

    @Autowired
    private BigRebateUserLogDao bigRebateUserLogDao;

    @Autowired
    private BigRechargeApplyDao bigRechargeApplyDao;

    @Autowired
    private AlerterFacade alerterFacade;

    /**
     * 获取制定金额大额充值配置
     *
     * @param bigRebateConfigList 大额充值配置
     * @param sumAmount           合并订单金额
     * @return
     */
    public Optional<BigRebateConfig> findBigRebateConfig(List<BigRebateConfig> bigRebateConfigList, BigDecimal sumAmount) {
        return bigRebateConfigList.stream().filter(bigRebateConfig -> {
            BigDecimal startAmount = bigRebateConfig.getStartAmount();
            BigDecimal endAmount = bigRebateConfig.getEndAmount();
            if (startAmount.compareTo(sumAmount) <= 0 && endAmount.compareTo(sumAmount) > 0) {
                return true;
            }
            return startAmount.compareTo(sumAmount) <= 0 && endAmount.compareTo(BigDecimal.ZERO) == 0;
        }).findAny();
    }

    private BigRechargeApply buildBigRechargeApply(WebCommonParam webCommonParam, ApplyRebateVO rebateApplyVO) {
        BigRechargeApply bigRechargeApply = new BigRechargeApply();
        bigRechargeApply.setBatchNo(orderIdService.generateGlobalId());
        bigRechargeApply.setKugouId(rebateApplyVO.getKugouId());
        bigRechargeApply.setUserId(this.userFacadeService.getUserIdByKugouId(rebateApplyVO.getKugouId())
                .orElseThrow(() -> new ContextedRuntimeException("获取繁星ID失败").setContextValue("userId", rebateApplyVO.getKugouId())));
        bigRechargeApply.setQq(rebateApplyVO.getQq());
        bigRechargeApply.setPhone(rebateApplyVO.getPhone());
        bigRechargeApply.setOrderNums(rebateApplyVO.getRechargeOrders());
        bigRechargeApply.setApplyType(ApplyTypeEnum.APPLY_TYPE_1.getStatus());
        bigRechargeApply.setApplyKugouId(webCommonParam.getKugouId());
        bigRechargeApply.setApplyUserId(webCommonParam.getUserId());
        bigRechargeApply.setApproveStatus(ApproveStatusEnum.APPROVE_STATUS_1.getStatus());
        bigRechargeApply.setImagePath(StringUtils.EMPTY);
        bigRechargeApply.setJsonExtend("{}");
        bigRechargeApply.setAlipayAccount(StringUtils.EMPTY);
        return bigRechargeApply;
    }

    private boolean strategyVerify(WebCommonParam webCommonParam, ApplyRebateVO rebateApplyVO) {
        try {
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("webCommonParam", webCommonParam);
            dataMap.put("rebateApplyVO", rebateApplyVO);
            StrategyVO strategyVO = new StrategyVO()
                    .setAppid("0")
                    .setBiz("BigRebate")
                    .setDeviceId("")
                    .setEndtype(StringUtils.EMPTY)
                    .setTs(System.currentTimeMillis())
                    .setData(JSON.toJSONString(dataMap))
                    .setKugouId(webCommonParam.getKugouId())
                    .setIp(IpUtils.getClientIpAddress())
                    .setSid("0");
            PlatformStrategyServiceCommand command = new PlatformStrategyServiceCommand(platformStrategyService, strategyVO);
            Future<StrategyResult> strategyResultFuture = command.observe().toBlocking().toFuture();
            StrategyResult strategyResult = strategyResultFuture.get();
            log.warn("big rebate strategy verify -> strategyVO:{} result:{}", strategyVO, strategyResult);
            if (strategyResult.getRecode() == 0 && strategyResult.getData() != null && strategyResult.getData().getMeasure() == Measure.FREEZE) {
                return true;
            }
        } catch (Exception e) {
            log.error("大额返点调用风控服务失败", e);
        }
        return false;
    }

    /**
     * 处理大额合并返点，补偿发放星币
     *
     * @param webCommonParam 通用参数
     * @param rebateApplyVO  返点申请
     * @return
     */
    @Transactional(transactionManager = "transactionManager_d_fanxing_recharge", propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public SysResultCode handleBigCombineRebate(WebCommonParam webCommonParam, ApplyRebateVO rebateApplyVO) {
        // 大额返点申请流程接入风控服务校验
        boolean verifyResult = this.strategyVerify(webCommonParam, rebateApplyVO);
        if (verifyResult) {
            log.error("风控服务阻断大额返点，webCommonParam：{}, rebateApplyVO: {}", webCommonParam, rebateApplyVO);
            return SysResultCode.E_40000021;
        }

        // 校验填写的用户酷狗号是否存在
        Set<Long> toKugouIdSet = Sets.newHashSet(rebateApplyVO.getKugouId());
        Optional<Boolean> optionalInvalid = toKugouIdSet.parallelStream()
                .map(kugouId -> userFacadeService.isValidKugouId(kugouId))
                .filter(isValidKugouId -> !isValidKugouId)
                .findAny();
        if (optionalInvalid.isPresent()) {
            log.warn("填写的用户酷狗ID不存在, toKugouIdSet: {}", toKugouIdSet);
            return SysResultCode.E_40000222;
        }

        Set<String> rechargeOrderNumSet = rebateApplyVO.getRechargeOrderNumSet();
        if (rechargeOrderNumSet.size() < 2) {
            log.error("申请合并返点至少填写两笔交易，webCommonParam：{}, rebateApplyVO: {}", webCommonParam, rebateApplyVO);
            return SysResultCode.E_40000022;
        }
        List<BigRebateUserLog> bigRebateUserLogList = this.bigRebateUserLogDao.query(rebateApplyVO.getKugouId(), rechargeOrderNumSet);

        // 校验申请交易号存在且繁星账号匹配
        Set<String> rechargeOrderNumSetExists = bigRebateUserLogList.stream()
                .map(BigRebateUserLog::getRechargeOrderNum)
                .collect(Collectors.toSet());
        if (!rechargeOrderNumSetExists.containsAll(rechargeOrderNumSet)) {
            Set<String> rechargeOrderNumSetNotExists = new HashSet<>(rechargeOrderNumSet);
            rechargeOrderNumSetNotExists.removeAll(rechargeOrderNumSetExists);
            log.error("填写的返点交易号不存，交易号：{}", rechargeOrderNumSetNotExists);
            return SysResultCode.E_40000015;
        }

        // 校验申请交易号的返点状态为已返点且未合并
        rechargeOrderNumSetExists = bigRebateUserLogList.stream()
                .filter(bigRebateUserLog -> bigRebateUserLog.getStatus() == RebateStatusEnum.REBATE_STATUS_3.getStatus())
                .filter(bigRebateUserLog -> bigRebateUserLog.getPid() == 0)
                .filter(bigRebateUserLog -> bigRebateUserLog.getCombineNum() == 1)
                .map(BigRebateUserLog::getRechargeOrderNum)
                .collect(Collectors.toSet());
        if (!rechargeOrderNumSetExists.containsAll(rechargeOrderNumSet)) {
            Set<String> rechargeOrderNumSetNotExists = new HashSet<>(rechargeOrderNumSet);
            rechargeOrderNumSetNotExists.removeAll(rechargeOrderNumSetExists);
            log.error("填写的返点交易号已返点，交易号：{}", rechargeOrderNumSetNotExists);
            return SysResultCode.E_40000016;
        }

        // 校验申请交易号人民币必须至少10万元起
        rechargeOrderNumSetExists = bigRebateUserLogList.stream()
                .filter(bigRebateUserLog -> bigRebateUserLog.getAmount().compareTo(BigDecimal.valueOf(100_000L)) >= 0)
                .map(BigRebateUserLog::getRechargeOrderNum)
                .collect(Collectors.toSet());
        if (!rechargeOrderNumSetExists.containsAll(rechargeOrderNumSet)) {
            Set<String> rechargeOrderNumSetNotExists = new HashSet<>(rechargeOrderNumSet);
            rechargeOrderNumSetNotExists.removeAll(rechargeOrderNumSetExists);
            log.error("填写的返点交易号金额少于10万，交易号：{}", rechargeOrderNumSetNotExists);
            return SysResultCode.E_40000017;
        }

        // 计算合并返点后的人民币数量
        BigDecimal sumAmount = bigRebateUserLogList.stream()
                .map(BigRebateUserLog::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        List<BigRebateConfig> bigRebateConfigList = this.bigRebateConfigDao.queryAll();
        if (CollectionUtils.isEmpty(bigRebateConfigList)) {
            log.error("获取大额返点配置失败, 配置列表为空");
            return SysResultCode.E_40000018;
        }

        // 校验合并返点记录时间间隔不超过24h
        int max = bigRebateUserLogList.stream()
                .map(BigRebateUserLog::getAddTime)
                .reduce(0, (a, b) -> a > b ? a : b);
        int min = bigRebateUserLogList.stream()
                .map(BigRebateUserLog::getAddTime)
                .reduce(Integer.MAX_VALUE, (a, b) -> a > b ? b : a);
        if (max - min > 24 * 60 * 60) {
            log.error("填写的返点交易号时间间隔超过24h, 不允许合并返点");
            return SysResultCode.E_40000019;
        }

        // 获取新的大额赠币返点配置
        Optional<BigRebateConfig> bigRebateConfigOptional = findBigRebateConfig(bigRebateConfigList, sumAmount);
        if (!bigRebateConfigOptional.isPresent()) {
            log.error("无法获取合并金额对应的大额返点配置信息, sumAmount: {}, bigRebateConfigList: {}", sumAmount, bigRebateConfigList);
            return SysResultCode.E_40000018;
        }
        BigRebateConfig bigRebateConfig = bigRebateConfigOptional.get();
        log.warn("成功获取大额返点配置, sumAmount: {}, bigRebateConfig: {}", sumAmount, bigRebateConfig);

        // 计算合并后的大额补偿赠币
        int combineNum = bigRebateUserLogList.size();
        BigDecimal oldCoinAward = bigRebateUserLogList.stream().map(BigRebateUserLog::getCoinAward).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal newCoinAward = sumAmount.multiply(BigDecimal.valueOf(100)).multiply(bigRebateConfig.getRebateRate());
        BigDecimal coinAward = newCoinAward.subtract(oldCoinAward);
        if (coinAward.compareTo(BigDecimal.ZERO) < 1) {
            log.error("申请的交易号无需额外返点, oldCoinAward: {}, newCoinAward: {}, bigRebateConfigList: {}, rebateApplyVO: {}",
                    oldCoinAward, newCoinAward, bigRebateConfigList, rebateApplyVO);
            return SysResultCode.E_40000023;
        }

        BigRebateUserLog bigRebateUserLog = buildBigCombineRebateUserLog(rebateApplyVO, combineNum, coinAward);
        int rows = this.bigRebateUserLogDao.saveRecord(bigRebateUserLog);
        if (rows < 1) {
            log.error("保存合并后的大额返点记录失败, rows: {}", rows);
            throw new ContextedRuntimeException("保存合并后的大额返点记录失败");
        }

        // 更新合并前的大额赠币订单状态为：4-已合并
        long pid = bigRebateUserLog.getId();
        rows = this.bigRebateUserLogDao.updateCombinedRecord(rebateApplyVO.getKugouId(), rechargeOrderNumSet, pid);
        if (rows < rechargeOrderNumSet.size()) {
            log.error("更新合并前的大额赠币记录失败, rows: {}", rows);
            throw new ContextedRuntimeException("更新合并前的大额赠币记录失败");
        }

        // 添加大额赠币用户申请记录
        BigRechargeApply bigRechargeApply = buildBigRechargeApply(webCommonParam, rebateApplyVO);
        rows = this.bigRechargeApplyDao.addRecord(bigRechargeApply);
        if (rows < 1) {
            log.error("添加大额赠币用户申请记录失败, rows: {}", rows);
            throw new ContextedRuntimeException("添加大额赠币用户申请记录失败");
        }

        // 发送用户大额充值合并返点结果提醒
        try {
            String content = String.format("尊敬的用户：您的大额充值合并返点申请已完成，共补发%s星币。如有问题请前往客服中心联系客服。", coinAward.stripTrailingZeros().toPlainString());
            alerterFacade.sendMSG("大额充值合并返点结果提醒！ ", content, webCommonParam.getKugouId());
            alerterFacade.sendSMS("【酷狗直播】", content, webCommonParam.getKugouId());
        } catch (Exception e) {
            log.error("发送大额充值合并返点结果提醒失败", e);
        }

        return SysResultCode.SUCCESS;
    }

    /**
     * 构建大额合并返点记录
     * <p>
     * 产品要求不补偿座驾与勋章，因此设置勋章ID与座驾ID为0；
     * 合并补偿返点记录的父节点pid标记为自身，避免使用合并返点记录进行返点操作；
     * 合并补偿返点记录使用为非充值返点记录，因此设置 amount = 0 与 coin = 0;
     *
     * @param rebateApplyVO 返点申请信息
     * @param combineNum    合并记录条数
     * @param coinAward     合并补偿星币
     * @return
     */
    private BigRebateUserLog buildBigCombineRebateUserLog(ApplyRebateVO rebateApplyVO, int combineNum, BigDecimal coinAward) {
        long globalId = orderIdService.generateGlobalId();
        BigRebateUserLog bigRebateUserLog = new BigRebateUserLog();
        bigRebateUserLog.setId(globalId);
        bigRebateUserLog.setPid(globalId);
        bigRebateUserLog.setKugouId(rebateApplyVO.getKugouId());
        bigRebateUserLog.setUserId(this.userFacadeService.getUserIdByKugouId(rebateApplyVO.getKugouId())
                .orElseThrow(() -> new ContextedRuntimeException("获取繁星ID失败").setContextValue("userId", rebateApplyVO.getKugouId())));
        bigRebateUserLog.setAddTime(Math.toIntExact(System.currentTimeMillis() / 1000));
        bigRebateUserLog.setMedalId(0);
        bigRebateUserLog.setMedalExpire(0);
        bigRebateUserLog.setMountId(0);
        bigRebateUserLog.setMountExpire(0);
        bigRebateUserLog.setCombineNum(combineNum);
        bigRebateUserLog.setRechargeOrderNum(orderIdService.generateRechargeOrderNumForAcross());
        bigRebateUserLog.setStatus((short) 1);
        bigRebateUserLog.setAmount(BigDecimal.ZERO);
        bigRebateUserLog.setCoin(BigDecimal.ZERO);
        bigRebateUserLog.setCoinAward(coinAward);
        bigRebateUserLog.setCoinFee(BigDecimal.ZERO);
        return bigRebateUserLog;
    }
}
