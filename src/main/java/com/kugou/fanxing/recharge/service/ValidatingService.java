package com.kugou.fanxing.recharge.service;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.Optional;
import java.util.Set;

@Service
@Validated
public class ValidatingService {

    @Autowired
    private Validator validator;

    public <T> String validateParams(T param) {
        Set<ConstraintViolation<T>> violations = validator.validate(param);
        Optional<ConstraintViolation<T>> optional = violations.stream().findFirst();
        if (optional.isPresent()) {
            return optional.get().getMessage();
        } else {
            return StringUtils.EMPTY;
        }
    }

    public <T> Optional<ConstraintViolation<T>> checkViolation(T param) {
        Set<ConstraintViolation<T>> violations = validator.validate(param);
        return violations.stream().findFirst();
    }

}
