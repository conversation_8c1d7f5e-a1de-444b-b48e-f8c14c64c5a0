package com.kugou.fanxing.recharge.service.withdraw;

import com.kugou.fanxing.recharge.constant.DrawTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CashServiceFactory {

    @Autowired
    private ApplicationContext applicationContext;

    public CashService createCashService(int drawType) {
        if (DrawTypeEnum.isWechatDrawCash(drawType)) {
            return applicationContext.getBean(WechatCashService.class);
        }
        if (DrawTypeEnum.isStWechatDrawCash(drawType)) {
            return applicationContext.getBean(StWechatCashService.class);
        }
        if (DrawTypeEnum.isAlipayDrawCash(drawType)) {
            return applicationContext.getBean(AlipayCashService.class);
        }
        log.error("创建[CashService]失败，不支持的提现类型。drawType: {}", drawType);
        throw new UnsupportedOperationException("不支持的提现类型");
    }
}
