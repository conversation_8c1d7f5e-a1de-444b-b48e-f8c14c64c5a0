package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.biz.commons.util.GrayTools;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.request.GetRechargeUserInfoRequest;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.model.vo.AgentRechargeUserInfoVO;
import com.kugou.fanxing.recharge.model.vo.IntimacyVO;
import com.kugou.fanxing.recharge.service.command.GetFanGroupInfoCommand;
import com.kugou.fanxing.recharge.service.command.GetStarInfoCommand;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.star.api.StarInfo;
import com.kugou.fanxing.star.api.StarInfoResp;
import com.kugou.fanxing.star.api.StarQueryService;
import com.kugou.fanxing.thrift.fangroup.service.FanGroupService;
import com.kugou.fanxing.thrift.fangroup.service.GetFanGroupInfoRequest;
import com.kugou.fanxing.thrift.fangroup.service.GetFanGroupInfoResp;
import com.kugou.fanxing.thrift.fangroup.service.GetFanGroupInfoResponse;
import com.kugou.fanxing.userbaseinfo.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
public class AgentRechargeService {

    @Autowired
    private StarQueryService.Iface starQueryService;
    @Autowired
    private FanGroupService.Iface fanGroupService;
    @Autowired
    private UserFacadeService userFacadeService;

    private static final GrayTools agentRechargeGrayTools = GrayTools.module("agentRecharge");

    public AgentRechargeUserInfoVO getUserInfo(WebCommonParam webCommonParam, GetRechargeUserInfoRequest request) {
        if (Math.max(request.getRechargeFxId(), request.getRechargeKuwoId()) <= 0) {
            log.warn("获取被充值用户信息，参数错误。webCommonParam: {}, request: {}", webCommonParam, request);
            throw new BizException(SysResultCode.RECHARGE_PARAM_ERROR);
        }
        Optional<Long> optionalKugouId = request.getRechargeKuwoId() > 0 ? userFacadeService.getKugouIdByKuwoId(request.getRechargeKuwoId()) :
                userFacadeService.getKugouIdByUserId(request.getRechargeFxId(), false);
        if (!optionalKugouId.isPresent() || optionalKugouId.get() < 1) {
            log.warn("获取被充值用户信息，转酷狗ID失败。webCommonParam: {}, request: {}", webCommonParam, request);
            throw new BizException(SysResultCode.E_100042);
        }
        long rechargedKugouId = optionalKugouId.get();
        if (webCommonParam.getKugouId() == rechargedKugouId) {
            log.warn("获取被充值用户信息，禁止为自己代充。webCommonParam: {}, request: {}", webCommonParam, request);
            throw new BizException(SysResultCode.E_100043);
        }
        Optional<UserVO> optionalUser = userFacadeService.getUserVOByKugouId(rechargedKugouId);
        if (!optionalUser.isPresent()) {
            log.warn("获取被充值用户信息，查询用户信息失败。webCommonParam: {}, request: {}", webCommonParam, request);
            throw new BizException(SysResultCode.E_100042);
        }
        UserVO user = optionalUser.get();
        Optional<StarInfo> optionalStarInfo = getStarInfo(rechargedKugouId);
        boolean isStar = optionalStarInfo.isPresent() && optionalStarInfo.get().getIsSign() == 1;
        AgentRechargeUserInfoVO agentRechargeUserInfoVO = new AgentRechargeUserInfoVO();
        agentRechargeUserInfoVO.setKugouId(user.getKugouId());
        agentRechargeUserInfoVO.setNickName(user.getNickName());
        agentRechargeUserInfoVO.setUserLogo(user.getUserLogo());
        agentRechargeUserInfoVO.setStar(isStar);
        if (isStar) {
            GetFanGroupInfoResponse response = getFanGroupInfo(webCommonParam.getKugouId(), rechargedKugouId, optionalStarInfo.get().getRoomId());
            if (response != null && response.getCode() == 0 && response.getData() != null) {
                GetFanGroupInfoResp data = response.getData();
                IntimacyVO intimacyVO = new IntimacyVO();
                BeanUtils.copyProperties(data, intimacyVO);
                agentRechargeUserInfoVO.setIntimacyData(intimacyVO);
            }
        }
        agentRechargeUserInfoVO.setUserId(request.getRechargeFxId() > 0 ? request.getRechargeFxId() : request.getRechargeKuwoId());
        return agentRechargeUserInfoVO;
    }

    /**
     * 查询主播信息
     */
    private Optional<StarInfo> getStarInfo(long kugouId) {
        try {
            GetStarInfoCommand command = new GetStarInfoCommand(starQueryService, kugouId);
            Optional<StarInfoResp> optionalResult = command.execute();
            if (optionalResult.isPresent()) {
                StarInfoResp result = optionalResult.get();
                return Optional.ofNullable(result.getData());
            }
        } catch (Exception e) {
            log.warn("获取被充值用户信息，调用主播信息服务异常。kugouId: {}", kugouId, e);
        }
        return Optional.empty();
    }

    /**
     * 查询亲密度信息
     */
    private GetFanGroupInfoResponse getFanGroupInfo(long kugouId, long starKugouId, long roomId) {
        try {
            GetFanGroupInfoRequest request = new GetFanGroupInfoRequest()
                    .setKugouId(kugouId).setStarKugouId(starKugouId).setRoomId(roomId);
            GetFanGroupInfoCommand command = new GetFanGroupInfoCommand(fanGroupService, request);
            Optional<GetFanGroupInfoResponse> optionalResult = command.execute();
            if (optionalResult.isPresent()) {
                return optionalResult.get();
            }
        } catch (Exception e) {
            log.warn("获取被充值用户信息，调用粉丝组服务异常。kugouId: {}, starKugouId: {}, starKugouId: {}", kugouId, starKugouId, roomId, e);
        }
        return null;
    }

    /**
     * 代充检查
     *
     * @param webCommonParam  通用请求参数
     * @param rechargeKugouId 被充值用户酷狗ID
     * @param couponId        代金券ID
     */
    public SysResultCode checkAgentRecharge(WebCommonParam webCommonParam, long rechargeKugouId, long couponId) {
        if (rechargeKugouId < 1) {
            return SysResultCode.SUCCESS;
        }
        if (couponId > 0) {
            log.warn("代充检查，禁止使用代金券。webCommonParam: {}, rechargeKugouId: {}", webCommonParam, rechargeKugouId);
            return SysResultCode.E_100041;
        }
        if (!agentRechargeGrayTools.isGray(webCommonParam.getKugouId())) {
            log.warn("代充检查，代充功能尚未开启。webCommonParam: {}, rechargeKugouId: {}", webCommonParam, rechargeKugouId);
            return SysResultCode.E_100044;
        }
        if (webCommonParam.getKugouId() == rechargeKugouId) {
            log.warn("代充检查，不允许给自己代充。webCommonParam: {}, rechargeKugouId: {}", webCommonParam, rechargeKugouId);
            return SysResultCode.E_100043;
        }
        Optional<Long> optionalUserId = this.userFacadeService.getUserIdByKugouId(rechargeKugouId, false);
        if (!optionalUserId.isPresent() || optionalUserId.get() < 1L) {
            log.warn("代充检查，获取被充值账户繁星ID失败。webCommonParam: {}, rechargeKugouId: {}", webCommonParam, rechargeKugouId);
            return SysResultCode.E_100042;
        }
        boolean isRevokedAccount = this.userFacadeService.isRevokedAccount(rechargeKugouId);
        if (isRevokedAccount) {
            log.warn("代充检查，代充账号已注销。webCommonParam: {}, rechargeKugouId: {}", webCommonParam, rechargeKugouId);
            return SysResultCode.E_100046;
        }
        boolean isBannedAccount = this.userFacadeService.isBannedAccount(rechargeKugouId);
        if (isBannedAccount) {
            log.warn("代充检查，代充账号已封禁。webCommonParam: {}, rechargeKugouId: {}", webCommonParam, rechargeKugouId);
            return SysResultCode.E_100045;
        }
        return SysResultCode.SUCCESS;
    }

    /**
     * 是否展示前端代充入口
     */
    public boolean enableAgentRecharge(long kugouId) {
        return agentRechargeGrayTools.isGray(kugouId);
    }
}
