package com.kugou.fanxing.recharge.service;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeRebateDao;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.model.po.RechargeRebatePO;
import com.kugou.fanxing.recharge.model.vo.RechargeRebateVO;
import com.kugou.fanxing.recharge.util.ModelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RechargeRebateService {

    private final LoadingCache<String, List<RechargeRebateVO>> rechargeRebateInfoCache = CacheBuilder.newBuilder()
            .maximumSize(20000)
            .refreshAfterWrite(5, TimeUnit.MINUTES)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<RechargeRebateVO>>() {
                @Override
                public List<RechargeRebateVO> load(String key) throws Exception {
                    List<RechargeRebatePO> rechargeRebatePOList = rechargeRebateDao.getNowRechargeRebate();
                    return rechargeRebatePOList.stream()
                            .map(rechargeRebatePO -> {
                                RechargeRebateVO rechargeRebateVO = ModelUtils.fromUnchecked(rechargeRebatePO, RechargeRebateVO.class);
                                rechargeRebateVO.setMedalName("");
                                rechargeRebateVO.setMedalIcon("");
                                rechargeRebateVO.setMountName("");
                                rechargeRebateVO.setMountIcon("");
                                return rechargeRebateVO;
                            })
                            .collect(Collectors.toList());
                }
            });

    @Autowired
    private RechargeRebateDao rechargeRebateDao;

    /**
     * 10W以内充值无座驾勋章新消息
     *
     * @return 返点信息
     */
    public List<RechargeRebateVO> getRechargeRebateInfo() {
        String key = "rechargeRebateInfoCache";
        try {
            return rechargeRebateInfoCache.get(key);
        } catch (Exception e) {
            log.error("加载[rechargeRebateInfoCache]缓存，加载异常。key:{}", key, e);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR, e);
        }
    }

}
