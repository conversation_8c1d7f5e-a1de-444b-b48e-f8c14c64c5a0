package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.activity.register.recharge.GenGrayPlanReq;
import com.kugou.fanxing.activity.register.recharge.GrayPlan;
import com.kugou.fanxing.activity.register.recharge.GrayPlanResponse;
import com.kugou.fanxing.activity.register.recharge.RechargeGrayPlanService;
import com.kugou.fanxing.recharge.config.GrayPlanConfig;
import com.kugou.fanxing.recharge.model.vo.*;
import com.kugou.fanxing.recharge.service.stat.UserEverRechargeStatService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/13
 */
@Service
public class RechargeGrayService {

    @Autowired
    private RechargeOptionService rechargeOptionService;

    @Autowired
    private RechargeGrayPlanService.Iface rechargeGrayPlanService;

    @Autowired
    private GrayPlanConfig grayPlanConfig;

    @Autowired
    private UserEverRechargeStatService userEverRechargeStatService;

    private static final Logger logger = LoggerFactory.getLogger(RechargeGrayService.class);


    public GrayRechargeOption getRechargeOptions(long kugouId, String deviceId, int platform) {
        int planId = createGrayPlan(kugouId, deviceId, platform);
        Map<Integer, Integer> defaultCoinConfig = grayPlanConfig.grayDefaultCoin();

        List<GrayRechargeOptionItem> rechargeOptionItems = rechargeOptionService.getDefaultList().stream().map(v -> {
            GrayRechargeOptionItem item = new GrayRechargeOptionItem();
            item.setCoins(v.getCoins());
            item.setMoney(v.getMoney());
            item.setPresentList(v.getPresentList());
            if (planId > 0) {
                item.setChecked(defaultCoinConfig.getOrDefault(planId, 0) == v.getMoney());
            }
            return item;
        }).collect(Collectors.toList());

        GrayRechargeOption grayRechargeOption = new GrayRechargeOption();
        grayRechargeOption.setOptions(rechargeOptionItems);
        Map<Integer, Integer> minCoinConfig = grayPlanConfig.grayMinCoin();
        grayRechargeOption.setCustomMinimm(minCoinConfig.getOrDefault(planId, 1));

        return grayRechargeOption;
    }


    public GrayRechargePage grayRechargePage(long kugouId, String deviceId, int platform) {
        GrayRechargePage grayRechargePage = new GrayRechargePage();
        int planId = createGrayPlan(kugouId, deviceId, platform);
        grayRechargePage.setStyleType(planId > 0 ? 1 : 0);
        return grayRechargePage;
    }

    /**
     * 根据灰度方案返回对应的充值页配置
     * 分为（0.1|6|10）、20、50、100、300 五个档位，第一个档位返回0.1、6、10元中的一种
     * 不满足灰度条件或远程调用失败时，最小充值金额使用0.1元
     */
    public GrayNewPage grayNewPage(long kugouId, int platform) {
        int planId = queryPlanId(kugouId, platform);
        Map<Integer, GrayNewPageOption> planOptionMap = grayPlanConfig.grayNewPagePlanOption();
        List<GrayNewPageOption> options = new ArrayList<>();
        options.add(planOptionMap.getOrDefault(planId, grayPlanConfig.grayNewPageDefaultOption()));
        options.addAll(grayPlanConfig.grayNewPageGeneralOptions());
        options.sort(Comparator.comparing(GrayNewPageOption::getMoney));
        GrayNewPage grayNewPage = new GrayNewPage();
        grayNewPage.setOptions(options);
        grayNewPage.setRule(grayPlanConfig.grayNewPageRule());
        int defaultTab = grayPlanConfig.defaultTabConfig().getOrDefault(planId, 0);
        grayNewPage.setDefaultTab(defaultTab);

        return grayNewPage;
    }

    /**
     * 创建灰度方案，不同方案对应不同的充值金额
     */
    private int createGrayPlan(long kugouId, String deviceId, int platform) {
        if (!grayPlanConfig.noviceRechargeGraySwitch()) {
            return 0;
        }
        if (kugouId <= 0 || StringUtils.isBlank(deviceId)) {
            return 0;
        }
        if (!grayPlanConfig.noviceRechargeGrayPlatform().contains(platform)) {
            return 0;
        }
        boolean everRecharge = userEverRechargeStatService.hasUserEverRecharge(kugouId);
        if (everRecharge) {
            return 0;
        }
        GenGrayPlanReq req = new GenGrayPlanReq();
        req.setKugouId(kugouId);
        req.setDeviceId(deviceId);
        try {
            GrayPlanResponse response = rechargeGrayPlanService.generatePlan(req);
            if (response.getCode() == 0 && response.getData() != null) {
                GrayPlan grayPlan = response.getData();
                return grayPlan.getPlanId();
            }
        } catch (Exception e) {
            logger.error("查询首充灰度方案出错, kugouId:{} ", kugouId, e);
        }

        return 0;
    }


    private int queryPlanId(long kugouId, int platform) {
        if (!grayPlanConfig.noviceRechargeGraySwitch()) {
            return 0;
        }
        if (kugouId <= 0 ) {
            return 0;
        }
        if (!grayPlanConfig.noviceRechargeGrayPlatform().contains(platform)) {
            return 0;
        }
        try {
            GrayPlanResponse response = rechargeGrayPlanService.queryPlan(kugouId);
            if (response.getCode() == 0 && response.getData() != null) {
                return response.getData().getPlanId();
            }
        } catch (Exception e) {
            logger.error("query planId error, kugouId:{} ", kugouId, e);
        }
        return 0;
    }
}
