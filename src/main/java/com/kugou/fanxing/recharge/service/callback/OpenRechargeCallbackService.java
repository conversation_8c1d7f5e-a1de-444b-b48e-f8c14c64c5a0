package com.kugou.fanxing.recharge.service.callback;

import com.google.common.collect.Maps;
import com.kugou.config.Env;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.model.bo.KugouOpenBusinessBO;
import com.kugou.fanxing.recharge.model.dto.RechargeExtendDTO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.fanxing.recharge.util.SignUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class OpenRechargeCallbackService extends AbstractCallbackService {

    @Autowired
    private Env env;

    public SysResultCode notifyGameServed(RechargeAcrossPO targetOrder) {
        log.warn("酷狗开放平台充值回调，开始调用业务方发货。targetOrder: {}", targetOrder);
        String openAppId = targetOrder.getBusinessId();
        KugouOpenBusinessBO kugouOpenBusinessBO = this.apolloConfigService.getOpenRechargeBusinessConfig(openAppId);
        String extend = new String(Base64.decodeBase64(StringUtils.defaultString(targetOrder.getExtend())));
        RechargeExtendDTO rechargeExtendDTO = parseRechargeExtendDTO(extend);
        String businessNotifyUrlForIos = kugouOpenBusinessBO.getBusinessNotifyUrlForIos();
        log.warn("酷狗开放平台充值回调，苹果充值通知发货地址。businessNotifyUrlForIos: {}", businessNotifyUrlForIos);
        Map<String, String> bodyParams = Maps.newHashMap();
        bodyParams.put("openAppId", openAppId);
        bodyParams.put("businessId", rechargeExtendDTO.getBusinessId());
        bodyParams.put("rechargeOrderNum", targetOrder.getRechargeOrderNum());
        bodyParams.put("kugouId", String.valueOf(targetOrder.getKugouId()));
        bodyParams.put("tradeNo", targetOrder.getTradeNo());
        bodyParams.put("tradeTime", String.valueOf(targetOrder.getTradeTime()));
        bodyParams.put("partner", targetOrder.getPartner());
        bodyParams.put("money", targetOrder.getMoney().stripTrailingZeros().toPlainString());
        bodyParams.put("addTime", String.valueOf(targetOrder.getAddTime()));
        bodyParams.put("userOpenid", rechargeExtendDTO.getUserOpenid());
        bodyParams.put("productId", rechargeExtendDTO.getProductId());
        bodyParams.put("signature", SignUtils.buildSignByKugouOpen(bodyParams, kugouOpenBusinessBO.getSecretKey()));
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(env.isProd() ? "hfp.fanxing.kgidc.cn" : "forward.proxy.kgidc.cn", 3128));
        Optional<String> optionalJson = HttpClientUtils.doPostJSONProxy(proxy, businessNotifyUrlForIos, Maps.newHashMap(), bodyParams);
        if (!optionalJson.isPresent()) {
            log.warn("酷狗开放平台充值业务回调失败。optionalJson: {}, targetOrder: {}", optionalJson, targetOrder);
            return SysResultCode.E_50000004;
        }
        int code = JsonUtils.parseJsonPathChecked(optionalJson.get(), "$.code", Integer.class);
        if (code != 0) {
            log.warn("酷狗开放平台充值业务回调失败。optionalJson: {}, targetOrder: {}", optionalJson, targetOrder);
            return SysResultCode.E_50000004;
        }
        log.warn("酷狗支付网关充值回调，通知业务发货成功。optionalJson: {}, targetOrder: {}", optionalJson, targetOrder);
        return SysResultCode.SUCCESS;
    }

}
