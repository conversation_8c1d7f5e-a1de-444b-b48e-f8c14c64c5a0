package com.kugou.fanxing.recharge.service.withdraw;

import lombok.Data;

import java.util.Date;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 **/
@Data
public class AlipayResp {

    private int status;

    private int error_code;

    private String error_msg;

    private AlipayResult data;

    @Data
    public static class AlipayResult {
        /**
         * 支付网关提现订单号
         */
        private String out_trade_no;
        /**
         * 业务方提现订单号
         */
        private String order_no;
        /**
         * 原生提现交易号
         */
        private String trade_no;
        /**
         * 原生提现成功时间
         */
        private Date trade_endtime;
        /**
         * 0提现中，1提现成功，2提现失败
         */
        private int transfer_status;
        /**
         * 发起提现kugouid
          */
        private long userid;
        /**
         * 提现失败原因，第三方透传，勿透传给客户端显示
         */
        private String error_reason;
        /**
         * 原生微信提现账号
         */
        private String openid;
        /**
         * 第三方提现交易号
         */
        private String payment_no;
        /**
         * 第三方提现成功时间
         */
        private String payment_time;
    }

    /**
     * 是否提现中
     *
     * @return 是否提现中
     */
    public boolean isTransferProcess() {
        return Objects.nonNull(this.getData()) && 0 == this.getData().getTransfer_status();
    }

    /**
     * 是否提现成功
     *
     * @return 是否提现成功
     */
    public boolean isTransferSuccess() {
        return Objects.nonNull(this.getData()) && 1 == this.getData().getTransfer_status();
    }

    /**
     * 是否提现失败
     *
     * @return 是否提现失败
     */
    public boolean isTransferFailure() {
        return Objects.nonNull(this.getData()) && 2 == this.getData().getTransfer_status();
    }

    /**
     * 是否提现取消
     *
     * @return 是否提现取消
     */
    public boolean isTransferCanceled() {
        return Objects.nonNull(this.getData()) && 4 == this.getData().getTransfer_status();
    }

    /**
     * 是否提现订单不存在
     *
     * @return 是否提现订单不存在
     */
    public boolean isTransferNotExists() {
        return this.getStatus() == 1 && this.getError_code() == 0 && Objects.isNull(this.getData());
    }

}