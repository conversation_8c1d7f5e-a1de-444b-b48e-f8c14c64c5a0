package com.kugou.fanxing.recharge.service.wxpay;

import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.google.common.collect.Maps;
import com.kugou.fanxing.commons.util.JsonUtils;
import com.kugou.fanxing.recharge.model.request.WxAddContractAndPayRequest;
import com.kugou.fanxing.recharge.model.response.WxAddContractAndPayResp;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/3/19 16:13
 */
@Slf4j
@Component
public class WxPayRpcClient {
    @ApolloConfig
    private com.ctrip.framework.apollo.Config config;
    /**
     * 支付并签约
     */
    public static String CONTRACT_AND_PAY_URL = "/v1/wxpreauthorized/addcontract_and_pay";

    /**
     * 微信支付【内网】
     */
    public static final String WX_PAY_DOMAIN_INNER = "http://pay-service.kgidc.cn";




    /**
     * 微信支付中签约
     * http://kapi.kugou.net/project/343/interface/api/103760
     * @param request
     * @return
     */
    public WxAddContractAndPayResp addContractAndPay(WxAddContractAndPayRequest request) {
        String url = getInnerPrefix() + CONTRACT_AND_PAY_URL;
        String requestStr = "";
        try {
            requestStr = JsonUtils.toJsonString(request);
            Map<String,String> bodyParams = JsonUtils.parseMap(requestStr, String.class);
            Optional<String> result = HttpClientUtils.doPostJSON(url, Maps.newHashMap(),bodyParams);
            log.warn("调用平台微信支付中签约接口,请求:{},返回:{}",requestStr,result);
            if (result.isPresent() && !result.get().trim().equalsIgnoreCase("null")) {
                WxAddContractAndPayResp resp = JsonUtils.parseObject(result.get(),WxAddContractAndPayResp.class);
                if(Objects.isNull(resp) || resp.getError_code() !=0){
                    return null;
                }
                return resp;
            }
        }catch (Exception e) {
            log.warn("调用平台微信支付中签约接口异常,请求:{},",requestStr,e);
        }
        return null;
    }

    /**
     * 获取平台地址前缀
     * @return
     */
    public String getInnerPrefix(){
        return config.getProperty("wx.pay.domain.inner", WX_PAY_DOMAIN_INNER);
    }



}
