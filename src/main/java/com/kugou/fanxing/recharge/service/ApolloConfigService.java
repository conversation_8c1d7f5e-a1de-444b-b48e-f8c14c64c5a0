package com.kugou.fanxing.recharge.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.bo.*;
import com.kugou.fanxing.recharge.model.vo.*;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.fanxing.recharge.util.ParseUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Slf4j
@Service
public class ApolloConfigService {

    @ApolloConfig
    private Config config;

    @ApolloConfig("PLATFORM.recharge.kupay")
    private Config kupayConfig;

    @ApolloConfig("PLATFORM.recharge.business")
    private Config businessConfig;

    @ApolloConfig("PLATFORM.recharge.appstore")
    private Config appstoreConfig;

    @ApolloConfig("PLATFORM.recharge.googleplay")
    private Config googlePlayConfig;

    @ApolloConfig("PLATFORM.recharge.airwallex")
    private Config airwallexConfig;

    public Optional<ServerOptionBO> getServerOptionByKey(String serverKey) {
        try {
            String json = config.getProperty("serverOption", "[]");
            List<ServerOptionBO> serverOptionBOList = JSON.parseArray(json, ServerOptionBO.class);
            Map<String, ServerOptionBO> serverOptionBOMap = serverOptionBOList.stream()
                    .collect(Collectors.toMap(ServerOptionBO::getServerKey, Function.identity()));
            Optional<ServerOptionBO> optionalServerOptionBO = Optional.ofNullable(serverOptionBOMap.get(serverKey));
            log.warn("获取服务配置项成功, serverKey: {}, serverOptionBO: {}", serverKey, optionalServerOptionBO);
            return optionalServerOptionBO;
        } catch (Exception e) {
            log.error("获取服务配置项异常, serverKey: {}, serverOptionBO: {}", serverKey, Optional.empty(), e);
            return Optional.empty();
        }
    }

    public List<String> getTmallKugouBrandIds() {
        String key = "recharge.tmall.kugou.brandIds";
        String[] kugouBrandIds = new String[]{};
        try {
            kugouBrandIds = config.getArrayProperty(key, ",", new String[]{});
        } catch (Exception e) {
            log.error("解析天猫酷狗直播品牌配置异常。key: {}", key, e);
        }
        return Arrays.asList(kugouBrandIds);
    }

    public List<String> getTmallFanxingBrandIds() {
        String key = "recharge.tmall.fanxing.brandIds";
        String[] fanxingBrandIds = new String[]{};
        try {
            fanxingBrandIds = config.getArrayProperty(key, ",", new String[]{});
        } catch (Exception e) {
            log.error("解析天猫繁星直播品牌配置异常。key: {}", key, e);
        }
        return Arrays.asList(fanxingBrandIds);
    }

    /**
     * 根据cardId区分酷狗繁星账号体系
     * 获取区分酷狗账号体系匹配规则
     */
    public String getTmallKugouAccountRulePattern() {
        String key = "recharge.tmall.kugou.accountRulePattern";
        String kuGouAccountPattern = "comKugouPrice";
        try {
            kuGouAccountPattern = config.getProperty(key, "comKugouPrice");
        } catch (Exception e) {
            log.error("解析Apollo配置异常, 获取酷狗账号匹配规则, key: {}", key, e);
        }
        return kuGouAccountPattern;
    }

    /**
     * 根据cardId区分酷狗繁星账号体系
     * 获取区分繁星账号体系匹配规则
     */
    public String getTmallFanxingAccountRulePattern() {
        String key = "recharge.tmall.fanxing.accountRulePattern";
        String fanxingAccountPattern = "comFanxingPrice";
        try {
            fanxingAccountPattern = config.getProperty(key, "comFanxingPrice");
        } catch (Exception e) {
            log.error("解析Apollo配置异常, 获取繁星账号匹配规则, key: {}", key, e);
        }
        return fanxingAccountPattern;
    }

    /**
     * 获取PayPal账号白名单（不受金额和地域限制）
     *
     * @return PayPal繁星账号白名单列表
     */
    public List<String> getPayPalWhitelist() {
        String key = "paypal.whiteList";
        String[] payPalWhitelist = new String[]{};
        try {
            payPalWhitelist = config.getArrayProperty(key, ",", payPalWhitelist);
        } catch (Exception e) {
            log.error("解析Apollo配置异常, key: {}", key, e);
        }
        return Arrays.asList(payPalWhitelist);
    }

    /**
     * 充值服务调用充值网关南北机房切换开关（0:通用域名，1：广州域名，2：北京域名）
     * changeKugouPayService	0	充值服务调用充值网关南北机房切换开关（0:通用域名，1：广州域名，2：北京域名）	foxzhu	2017-08-10 16:56:41
     */
    public int getChangeKugouPayService() {
        return config.getIntProperty("common.rpc.changeKugouPayService", 0);
    }

    /**
     * 获取小程序允许的充值金额列表
     */
    public List<BigDecimal> getWxMiniProgramPermitRechargeMoneyList() {
        String key = "wxMinProgram.permit.recharge.money.list";
        String[] permitRechargeMoneyArr = new String[]{"6", "30", "100", "300", "1000", "2000"};
        try {
            permitRechargeMoneyArr = config.getArrayProperty(key, ",", permitRechargeMoneyArr);
        } catch (Exception e) {
            log.error("解析Apollo配置异常, key: {}", key, e);
        }
        return Arrays.stream(permitRechargeMoneyArr).map(BigDecimal::new).collect(toList());
    }

    /**
     * 是否需要判断家长模式
     *
     * @param pid 平台编号
     * @return true: 需要判断家长模式；false: 不需要判断家长模式；
     */
    public boolean pidNeedToBeControl(int pid) {
        String key = "user.family.control.web.pay.pid";
        String[] pidStrArr = config.getArrayProperty(key, ",", new String[]{});
        if (pidStrArr.length < 1) {
            return true;
        }
        Optional<String> optionalPid = Arrays.stream(pidStrArr)
                .filter(s -> s.trim().equalsIgnoreCase(String.valueOf(pid)))
                .findAny();
        return !optionalPid.isPresent();
    }

    /**
     * 每次调用网关对账接口，最大时间间隔
     *
     */
    public int getCheckOrderWithGatewaySecondsPerTime() {
        String key = "check.order.with.gateway.max.seconds";
        return config.getIntProperty(key, 60);
    }

    /**
     * 每次执行对账任务，对账时间范围不能超过多少天
     *
     */
    public int getCheckOrderWithGatewayMaxDays() {
        String key = "check.order.with.gateway.max.days";
        return config.getIntProperty(key, 1);
    }

    /** 获取延迟提现订单页面大小
     *
     * @return 页面大小
     */
    public int getDelayWithdrawOrderPageSize() {
        return config.getIntProperty("withdraw.delay.orders.pageSize", 500);
    }

    /**
     * 获取延迟提现订单最大重试次数
     *
     * @return 最大重试次数
     */
    public int getDelayWithdrawOrderMaxRetryNum() {
        return config.getIntProperty("withdraw.delay.orders.retryNum", 5);
    }

    /**
     * 检查业务ID是否需要检查实名认证
     *
     * @param appId 业务ID
     * @return 是否检查实名认证
     */
    public boolean needCheckRealName(int appId) {
        String[] pidStrArr = config.getArrayProperty("withdraw.account.checkRealName.whitelist", ",", new String[]{});
        if (pidStrArr.length < 1) {
            return true;
        }
        Optional<String> optionalPid = Arrays.stream(pidStrArr)
                .filter(s -> s.trim().equalsIgnoreCase(String.valueOf(appId)))
                .findAny();
        return !optionalPid.isPresent();
    }

    /**
     * 单笔最大提现金额限制
     *
     * @return 单笔最大提现金额
     */
    public long getWithdrawMaxAmount() {
        return config.getIntProperty("withdraw.maxAmount", 100);
    }

    /**
     * 检查支付网关业务ID许可
     *
     * 1084：直播业务
     * 3134：交友业务
     *
     * @return 是否允许支付网关业务ID
     */
    public boolean permitWithdrawBizAppId(int bizAppId) {
        String key = "withdraw.bizAppIds";
        String[] bizAppIdStrArr = config.getArrayProperty(key, ",", new String[]{"1084", "3134"});
        if (bizAppIdStrArr.length < 1) {
            return false;
        }
        Optional<String> optionalBizAppId = Arrays.stream(bizAppIdStrArr)
                .filter(s -> s.trim().equalsIgnoreCase(String.valueOf(bizAppId)))
                .findAny();
        return optionalBizAppId.isPresent();
    }

    /**
     * 充值代金券特性开关
     *
     * @return 是否开启
     */
    public boolean isCouponSwitchOpen() {
        String key = "recharge.coupon.switch";
        boolean rechargeCouponSwitch = config.getBooleanProperty(key, true);
        log.warn("充值代金券功能特性开关是否开启, rechargeCouponSwitch: {}", rechargeCouponSwitch);
        return rechargeCouponSwitch;
    }

    /**
     * 获取充值代金券过期隐藏期限
     *
     * @return 过期隐藏期限
     */
    public int getCouponExpireHideDays() {
        String key = "recharge.coupon.expireHideDays";
        int expireHideDays = config.getIntProperty(key, 7);
        log.warn("获取充值代金券过期隐藏期限, expireHideDays: {}", expireHideDays);
        return expireHideDays;
    }

    /**
     * 是否禁止充值方式
     *
     * @param payTypeId 支付方式
     * @return 是否禁止充值方式
     */
    public boolean isForbiddenPayType(int payTypeId) {
        try {
            String key = "recharge.forbidden.payTypeIds";
            String[] payTypeIdStrArr = config.getArrayProperty(key, ",", new String[]{});
            if (payTypeIdStrArr.length < 1) {
                return false;
            }
            Optional<String> optionalPid = Arrays.stream(payTypeIdStrArr)
                    .filter(s -> s.trim().equalsIgnoreCase(String.valueOf(payTypeId)))
                    .findAny();
            return optionalPid.isPresent();
        } catch (Exception e) {
            log.error("是否禁止充值方式，调用异常。payTypeId: {}", payTypeId, e);
            return false;
        }
    }

    /**
     * 是否禁止充值平台
     *
     * @param stdPlat 支付平台
     * @return 是否禁止充值平台
     */
    public boolean isForbiddenStdPlat(int stdPlat) {
        try {
            String key = "recharge.forbidden.stdPlats";
            String[] stdPlatsStrArr = config.getArrayProperty(key, ",", new String[]{});
            OptionalInt optionalStdPlat = Arrays.stream(stdPlatsStrArr)
                    .mapToInt(Integer::parseInt)
                    .filter(item -> item == stdPlat)
                    .findAny();
            return optionalStdPlat.isPresent();
        } catch (Exception e) {
            log.error("是否禁止充值平台，调用异常。stdPlat: {}", stdPlat, e);
            return false;
        }
    }

    /**
     * 是否禁止充值应用
     *
     * @param appId 支付应用
     * @return 是否禁止充值应用
     */
    public boolean isForbiddenAppId(int appId) {
        try {
            String key = "recharge.forbidden.appIds";
            String[] appIdsStrArr = config.getArrayProperty(key, ",", new String[]{});
            OptionalInt optionalStdPlat = Arrays.stream(appIdsStrArr)
                    .mapToInt(Integer::parseInt)
                    .filter(item -> item == appId)
                    .findAny();
            return optionalStdPlat.isPresent();
        } catch (Exception e) {
            log.error("是否禁止充值应用，调用异常。appId: {}", appId, e);
            return false;
        }
    }

    /**
     * 充值测试模式，测试环境充值金额转为特定金额
     *
     * @return 是否开启充值测试模式
     */
    public boolean getRechargePoorMode() {
        return config.getBooleanProperty("recharge.poor.mode", true);
    }

    /**
     * 充值测试模式，测试环境充值金额转为特定金额
     *
     * @return  测试充值金额
     */
    public BigDecimal getRechargePoorModeAmount() {
        try {
            String amountStr = config.getProperty("recharge.poor.mode.amount", "0.01");
            return new BigDecimal(amountStr);
        } catch (Exception e) {
            Cat.logError("充值测试模式，金额配置解析异常", e);
            return new BigDecimal("0.01");
        }
    }

    /**
     * 是否开启充值服务调试模式
     *
     * @return 是否开启充值服务调试模式
     */
    public boolean enableDebugMode() {
        return config.getBooleanProperty("recharge.debug.mode", false);
    }

    /**
     * 获取代金券解冻开始时间
     * @return 开始时间
     */
    public Date getCouponUnFreezeStartTime() {
        Date sTime = DateUtils.addMinutes(new Date(), -30);
        try {
            sTime = config.getDateProperty("coupon.unfreeze.sTime", "yyyy-MM-dd HH:mm:ss", sTime);
        } catch (Exception e) {
            log.error("获取代金券解冻开始时间，获取失败。", e);
        }
        return sTime;
    }

    /**
     * 获取代金券解冻结束时间
     * @return 开始时间
     */
    public Date getCouponUnFreezeEndTime() {
        Date eTime = DateUtils.addMinutes(new Date(), -5);
        try {
            eTime = config.getDateProperty("coupon.unfreeze.eTime", "yyyy-MM-dd HH:mm:ss", eTime);
        } catch (Exception e) {
            log.error("获取代金券解冻结束时间，获取失败。", e);
        }
        return eTime;
    }

    /**
     * 充值渠道ID列表，json array 格式，30 手机支付宝; 35 手机银联; 40 手机微信
     *
     * @return 充值渠道id列表
     */
    public List<Integer> getPayTypeList() {
        try {
            String json = config.getProperty("payTypeList", "[30,40,35]");
            List<Integer> result = JSON.parseObject(json, new TypeReference<List<Integer>>() {});
            //不允许为空，默认保留微信跟支付宝
            if (CollectionUtils.isEmpty(result)){
                result = new ArrayList<>();
                result.add(40);
                result.add(30);
            }
            return result;
        } catch (Exception e) {
            log.error("获取充值渠道列表异常", e);
            Cat.logError("获取充值渠道列表异常", e);
            return Lists.newArrayList(30, 40, 35);
        }
    }

    public Map<Integer, PayChannelActivityItem> getPayChannelActivityConfig() {
        try {
            String json = config.getProperty("payChannelActivityConfig", "{30:{\"tips\":\"充100送100\",\"startTime\":100,\"endTime\":2198996619}}");
            return JSON.parseObject(json, new TypeReference<Map<Integer, PayChannelActivityItem>>() {});
        } catch (Exception e) {
            log.error("获取充值渠道活动列表异常", e);
            Cat.logError("获取充值渠道活动列表异常", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 读取酷狗支付网关AppId配置
     */
    public Map<Integer, KupayAppInfoBO> getKupayAppId() {
        String key = "recharge.kupay.appId";
        try {
            String json = kupayConfig.getProperty(key, "");
            TypeReference<List<KupayAppInfoBO>> typeReference = new TypeReference<List<KupayAppInfoBO>>() {};
            List<KupayAppInfoBO> kupayAppInfoPOList = JSON.parseObject(json, typeReference);
            return kupayAppInfoPOList.stream().collect(Collectors.toMap(KupayAppInfoBO::getKupayAppId, Function.identity()));
        } catch (Exception e) {
            log.error("读取酷狗支付网关配置，加载异常。key: {}", key, e);
            Cat.logError("读取酷狗支付网关配置，加载异常。", e);
        }
        return Maps.newHashMap();
    }

    /**
     * 读取酷狗支付网关ServerId配置
     */
    public Map<Integer, KupayServerInfoBO> getKupayServerId() {
        String key = "recharge.kupay.serverId";
        try {
            String json = kupayConfig.getProperty(key, "");
            TypeReference<List<KupayServerInfoBO>> typeReference = new TypeReference<List<KupayServerInfoBO>>() {};
            List<KupayServerInfoBO> kupayServerInfoPOList = JSON.parseObject(json, typeReference);
            return kupayServerInfoPOList.stream().collect(Collectors.toMap(KupayServerInfoBO::getKupayServerId, Function.identity()));
        } catch (Exception e) {
            log.error("读取酷狗支付网关配置，加载异常。key: {}", key, e);
            Cat.logError("读取酷狗支付网关配置，加载异常。", e);
        }
        return Maps.newHashMap();
    }

    /**
     * 获取平台ID对应酷狗支付网关微信AppType配置
     */
    public List<KupayAppTypeInfoBO> getKupayAppTypeInfoList() {
        String key = "recharge.kupay.wechat.appType";
        try {
            String json = kupayConfig.getProperty(key, "");
            TypeReference<List<KupayAppTypeInfoBO>> typeReference = new TypeReference<List<KupayAppTypeInfoBO>>() {};
            return JSON.parseObject(json, typeReference);
        } catch (Exception e) {
            log.error("读取酷狗支付网关配置，加载异常。key: {}", key, e);
            Cat.logError("读取酷狗支付网关配置，加载异常。", e);
        }
        return Lists.newArrayList();
    }

    public boolean isOrderAmountCheck() {
        return config.getBooleanProperty("is.open.order.amount.check",false);
    }

    public boolean isCheckLittleRebateWhenLost() {
        return config.getBooleanProperty("is.open.little.rebate.check",false);
    }

    /**
     * 提现业务中根据bizAppId获取微信APPID
     *
     * @param bizAppId 支付网关分配提现业务的APPID
     * @return 微信APPID
     */
    public String getWxAppIdByBizAppId(int bizAppId) {
        String wxAppId = config.getProperty("withdraw.bizAppId2wxAppId-" + bizAppId, "");
        if (StringUtils.isBlank(wxAppId)) {
            throw new BizException(SysResultCode.WITHDRAW_ACCOUNT_EXCEPTION);
        }
        return wxAppId;
    }

    /**
     * 根据酷狗开放平台业务ID获取支付配置
     *
     * @param openAppId 酷狗开放平台分配业务ID
     * @return 酷狗开放平台业务支付配置
     */
    public KugouOpenBusinessBO getOpenRechargeBusinessConfig(String openAppId) {
        String key = "kugouOpen.business.config";
        try {
            String json = config.getProperty(key, "");
            TypeReference<List<KugouOpenBusinessBO>> typeReference = new TypeReference<List<KugouOpenBusinessBO>>() {
            };
            List<KugouOpenBusinessBO> kupayAppInfoPOList = JSON.parseObject(json, typeReference);
            Map<String, KugouOpenBusinessBO> openRechargeBusinessBOMap = kupayAppInfoPOList.stream().collect(Collectors.toMap(KugouOpenBusinessBO::getBusinessId, Function.identity()));
            Optional<KugouOpenBusinessBO> optionalOpenRechargeBusinessBO = Optional.ofNullable(openRechargeBusinessBOMap.get(openAppId));
            return optionalOpenRechargeBusinessBO.orElseThrow(() -> new BizException(SysResultCode.E_50000001));
        } catch (Exception e) {
            log.error("根据酷狗开放平台业务ID获取支付配置，加载异常。key: {}, openappid: {}", key, openAppId, e);
            Cat.logError("根据酷狗开放平台业务ID获取支付配置，加载异常。", e);
            throw new BizException(SysResultCode.E_50000001).addContextValue("key", key).addContextValue("openappid", openAppId);
        }
    }

    /**
     * 获取酷狗开发平台服务端配置
     *
     * @return 酷狗开发平台服务端配置
     */
    public KugouOpenServerBO getKugouOpenServerConfig() {
        String key = "kugouOpen.server.config";
        try {
            String json = config.getProperty(key, "");
            return JSON.parseObject(json, KugouOpenServerBO.class);
        } catch (Exception e) {
            log.error("获取酷狗开发平台服务端配置，加载异常。key: {}", key, e);
            Cat.logError("获取酷狗开发平台服务端配置，加载异常。", e);
            throw new BizException(SysResultCode.E_50000005).addContextValue("key", key);
        }
    }

    /**
     * 获取消费财务变更类型配置
     *
     * @param accountChangeType 消费财务变更类型
     * @return 消费财务变更类型配置
     */
    public AccountChangeTypeBO getAccountChangeTypeById(int accountChangeType) {
        try {
            String json = this.businessConfig.getProperty("accountChangeTypeList", "[]");
            List<AccountChangeTypeBO> accountChangeTypeList = JSON.parseArray(json, AccountChangeTypeBO.class);
            accountChangeTypeList = CollectionUtils.isNotEmpty(accountChangeTypeList) ? accountChangeTypeList : Lists.newArrayList();
            return accountChangeTypeList.stream()
                    .filter(accountChangeTypeBO -> accountChangeTypeBO.getAccountChangeType() == accountChangeType)
                    .findFirst()
                    .orElseThrow(() -> new BizException(SysResultCode.RECHARGE_SYS_ERROR));
        } catch (Exception e) {
            log.error("读取配置中心配置，加载配置【PLATFORM.recharge.business】accountChangeTypeList 异常。", e);
            throw new ContextedRuntimeException("读取配置中心配置，加载配置【PLATFORM.recharge.business】accountChangeTypeList 异常。", e)
                    .addContextValue("accountChangeType", accountChangeType);
        }
    }

    /**
     * 是否直接使用客户端传递的AppType参数
     *
     * @param appType 酷狗支付网关微信配置类型
     * @return 是否直接使用客户端传递的AppType参数
     */
    public boolean useKupayAppTypeDirectly(String appType) {
        String[] appTypeArr = this.kupayConfig.getArrayProperty("recharge.kupay.wechat.appType.direct", ",", new String[] {"player2"});
        log.warn("直接使用客户端传递的AppType参数。appTypeArr: {}", Arrays.toString(appTypeArr));
        return ArrayUtils.contains(appTypeArr, appType);
    }

    public String getRecordremierMonth() {
        return config.getProperty("record.premier.month","202001");
    }

    /**
     * 获取微信二维码过期分钟数，默认：3分钟
     * @return 微信二维码过期分钟数
     */
    public int getWechatQrCodeExpireMinutes() {
        int wechatQrCodeExpireMinutes = this.config.getIntProperty("recharge.wechat.qrcode.expire.minutes", 3);
        // 注意：过期时间最短为1分钟
        return Math.max(wechatQrCodeExpireMinutes, 1);
    }

    public int getRechargeOrderExpireMinutes() {
        int expireMinutes = this.config.getIntProperty("recharge.order.expire.minutes", 3);
        // 注意：过期时间最短为1分钟
        return Math.max(expireMinutes, 1);
    }

    /**
     * 获取微信二维码过期前端提前刷新秒数，默认：30秒
     * @return 微信二维码过期前端提前刷新秒数
     */
    public int getWechatQrCodeRefreshSecond() {
        int wechatQrCodeRefreshSecond = this.config.getIntProperty("recharge.wechat.qrcode.refresh.second", 30);
        // 注意：提现刷新时间最短为5秒钟
        return Math.max(wechatQrCodeRefreshSecond, 5);
    }

    public List<String> getReproduceRechargeOrderNums() {
        String[] arr = this.config.getArrayProperty("recharge.ReproduceRechargeOrderNums", ",", new String[]{});
        return Arrays.asList(arr);
    }

    public List<String> getAdjustTradeTimeRechargeOrderNums() {
        String[] arr = this.config.getArrayProperty("recharge.adjustTradeTime.RechargeOrderNums", ",", new String[]{});
        return Arrays.asList(arr);
    }

    /**
     * 加载充值代金券冻结确认错误码列表
     *
     * @return 充值代金券冻结确认错误码列表
     */
    public List<Integer> getCouponFailureResultCodeList() {
        List<Integer> failureResultCodeList = Lists.newArrayList(16);
        try {
            String failureResultCode = this.config.getProperty("recharge.coupon.failureResultCodeList", "16");
            failureResultCodeList = Arrays.stream(StringUtils.split(failureResultCode, ","))
                    .map(StringUtils::trim)
                    .map(Integer::parseInt).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("加载充值代金券冻结确认错误码列表，加载异常。", e);
        }
        return failureResultCodeList;
    }

    /**
     * 是否明确失败的错误码
     *
     * @param errorCode 支付网关提现错误码
     * @return 是否明确提现失败错误码
     */
    public boolean isWithdrawErrorCode(int errorCode) {
        List<Integer> withdrawErrorCodeList = Lists.newArrayList(20006, 20010, 30966, 30968, 30972);
        try {
            String withdrawErrorCode = this.config.getProperty("withdraw.errorCode", "20006,20010,30966,30968,30972");
            withdrawErrorCodeList = Arrays.stream(StringUtils.split(withdrawErrorCode, ","))
                    .map(StringUtils::trim)
                    .map(Integer::parseInt).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("加载提现支付网关明确失败错误码列表，加载异常。", e);
        }
        return withdrawErrorCodeList.contains(errorCode);
    }

    public long getFapiaoRechargeListPageSize() {
        return config.getLongProperty("fapiao.rechargeList.pageSize", 10L);
    }

    public String getRechargeZuulUrlPrefix() {
        return this.config.getProperty("recharge.zuul.url", "");
    }

    public boolean enableRechargeZuulUrlPrefix() {
        return this.config.getBooleanProperty("recharge.zuul.url.enable", false);
    }

    public String getFirstRechargeJump() {
        return config.getProperty("FirstRechargeJump.Link", "https://mfanxing.kugou.com/cterm/recharge_weekly_acts/m/views/index.html/First");
    }

    /**
     * 获取代金券服务签名密钥
     *
     * @return 签名密钥
     */
    public String getCouponSignKey() {
        return config.getProperty("recharge.getCoupon.signKey", "17vmgkymub");
    }

    /**
     * 代金券赠送星币签名密钥
     *
     * @return 签名密钥
     */
    public String couponPresentSignKey() {
        return config.getProperty("recharge.couponPresent.signKey", "83yp4730k6");
    }

    public String getFapiaoMaintenanceToasts() {
        return this.config.getProperty("fapiao.maintenance.toast", "系统升级中，预计在2021年8月7日上午10点完成升级，感谢您的理解！");
    }

    public boolean getFapiaoMaintenanceSwitch() {
        return this.config.getBooleanProperty("fapiao.maintenance.enable", false);
    }

    public String getAppStoreProductList(String openAppId, int pid) {
        return this.config.getProperty(String.format("appstore.game-%s.pid-%s.productList", openAppId, pid), "[]");
    }

    public ProxyConfigBO getProxyConfig() {
        String host = this.appstoreConfig.getProperty("appstore.proxy.host", "forward.proxy.kgidc.cn");
        int port = this.appstoreConfig.getIntProperty("appstore.proxy.port", 3128);
        ProxyConfigBO proxyConfigBO = new ProxyConfigBO();
        proxyConfigBO.setProxyHost(host);
        proxyConfigBO.setProxyPort(port);
        return proxyConfigBO;
    }

    public int getOCoinKupayAppId() {
        return this.config.getIntProperty("recharge.ocoin.kupayAppId", 10058);
    }

    public int getNewMinPid() {
        return this.config.getIntProperty("new.kupay.info.pid.min", 1000);
    }

    public boolean permitSandboxVersion(String version) {
        String versions = this.config.getProperty("appstore.sandbox.version", "");
        return Arrays.stream(StringUtils.split(versions, ",")).anyMatch(s -> s.equalsIgnoreCase(version));
    }

    public String getThirdPartyPaymentUrl() {
        return this.appstoreConfig.getProperty("appstore.thirdPartyPaymentUrl", "https://mfanxing.kugou.com/staticPub/mobile/payment/views/index.html");
    }

    /**
     * 第三方支付全屏H5页面
     */
    public String getThirdPartyFullPaymentUrl() {
        return this.appstoreConfig.getProperty("appstore.thirdPartyFullPaymentUrl", "https://mfanxing.kugou.com/staticPub/mobile/payment/views/index.html");
    }

    /**
     * 第三方支付半屏H5页面
     */
    public String getThirdPartyHalfPaymentUrl() {
        return this.appstoreConfig.getProperty("appstore.thirdPartyHalfPaymentUrl", "https://mfanxing.kugou.com/staticPub/mobile/payment/views/index.html");
    }
    public String getBjZuulAddress() {
        return this.config.getProperty("bj.zuul.address", "fxbj.kgidc.cn/gwphp");
    }

    public String getGzZuulAddress() {
        return this.config.getProperty("gz.zuul.address", "fxgz.kgidc.cn/gwphp");
    }

    public boolean isKuwoEnv() {
        return this.config.getBooleanProperty("recharge.offline.kuwo.enable", false);
    }

    public boolean isDryRun() {
        return config.getBooleanProperty("recharge.isDryRun", true);
    }

    public boolean isAppStoreRecharge(int pid) {
        String[] pidList = this.config.getArrayProperty("appstore.pidList", ",", new String[]{"2", "6"});
        if (pidList.length < 1) {
            return false;
        }
        Optional<String> optionalPid = Arrays.stream(pidList)
                .filter(s -> s.trim().equalsIgnoreCase(String.valueOf(pid)))
                .findAny();
        return optionalPid.isPresent();
    }

    /**
     * 判断是否非法的openid模式
     * @return 是否非法的openid模式
     */
    public boolean isInvalidOpenidPatterns(String openid) {
        try {
            String[] invalidOpenidPatterns = this.config.getArrayProperty("recharge.invalid.openid.patterns", ",", new String[] {"undefine"});
            return Arrays.stream(invalidOpenidPatterns).anyMatch(invalidOpenidPattern -> invalidOpenidPattern.equalsIgnoreCase(StringUtils.trim(openid)));
        } catch (Exception e) {
            log.warn("判断是否非法的openid模式，解析异常。openid: {}", openid);
            return false;
        }
    }

    /**
     * 是否允许通过平台号获取支付网关配置
     *
     * @param pid 终端平台号
     * @return 是否允许通过平台号获取支付网关配置
     */
    public boolean allowGetKupayConfigByPid(int pid) {
        try {
            String[] pidList = this.kupayConfig.getArrayProperty("recharge.kupay.allowGetKupayConfigByPid", ",", new String[]{"96", "97"});
            Optional<String> optionalPid = Arrays.stream(pidList)
                    .filter(s -> s.trim().equalsIgnoreCase(String.valueOf(pid)))
                    .findAny();
            return optionalPid.isPresent();
        } catch (Exception e) {
            log.warn("是否允许通过平台号获取支付网关配置，解析配置异常。pid: {}", pid, e);
            return false;
        }
    }

    public boolean hasDisableFansWxRenewal(int stdPlat) {
        try {
            String[] pidList = this.kupayConfig.getArrayProperty("recharge.kupay.disableFansWxRenewalPids", ",", new String[]{"96", "97"});
            Optional<String> optionalPid = Arrays.stream(pidList)
                    .filter(s -> s.trim().equalsIgnoreCase(String.valueOf(stdPlat)))
                    .findAny();
            return optionalPid.isPresent();
        } catch (Exception e) {
            log.warn("是否允许展示豆粉微信续费渠道，解析配置异常。stdPlat: {}", stdPlat, e);
            return false;
        }
    }

    public boolean allowKugouOpenRecharge() {
        return config.getBooleanProperty("allowKugouOpenRecharge", true);
    }

    /**
     * 当前国家列表代码
     */
    public List<CountryInfoVo> getOverseasCountryList() {
        String key = "overseas.countryList";
        List<CountryInfoVo> countryInfoVoList = Lists.newArrayList();
        try {
            String currentCountryStr = StringUtils.defaultString(airwallexConfig.getProperty(key, "{}"));
            countryInfoVoList = JsonUtils.parseList(currentCountryStr, CountryInfoVo.class);
        } catch (Exception e) {
            log.error("获取国家代码列表，国家列表配置错误。key: {}", key, e);
            Cat.logError("获取国家代码列表，国家列表配置错误。key: " + key, e);
        }
        return countryInfoVoList;
    }

    public List<LocalCurrencyConfig> getOverseasCurrencyConfigByCountry(String country) {
        try {
            String json = airwallexConfig.getProperty(String.format("overseas.currencyConfig.country-%s", StringUtils.trim(country)), "[]");
            return JSON.parseArray(json, LocalCurrencyConfig.class);
        } catch (Exception e) {
            log.error("获取当地充值金额表，解析配置异常。country: {}", country, e);
            return Lists.newArrayList();
        }
    }

    public String getConfigSnapshotHashByCountry(String country) {
        String configSnapshotHash = airwallexConfig.getProperty(String.format("overseas.country-%s.configSnapshotHash", country), "");
        if (!configSnapshotHash.startsWith(StringUtils.upperCase(country))) {
            log.warn("获取当地充值金额与方式，配置错误忽略。country: {}, configSnapshotHash: {}", country, configSnapshotHash);
            return StringUtils.EMPTY;
        }
        return configSnapshotHash;
    }

    public PaymentMethodTypesDto getMMCardConfig() {
        try {
            String defaultJson = "{\n" +
                    "    \"has_more\":false,\n" +
                    "    \"items\":[\n" +
                    "        {\n" +
                    "            \"name\":\"card\",\n" +
                    "            \"display_name\":\"Card\",\n" +
                    "            \"transaction_mode\":\"oneoff\",\n" +
                    "            \"active\":true,\n" +
                    "            \"resources\":{\n" +
                    "                \"has_schema\":false,\n" +
                    "                \"logos\":{\n" +
                    "                    \"png\":\"https://checkout-demo.airwallex.com/static/media/cardPlaceholder.abcb5ee7.png\",\n" +
                    "                    \"svg\":\"https://checkout-demo.airwallex.com/static/media/cardPlaceholder.abcb5ee7.svg\"\n" +
                    "                }\n" +
                    "            }\n" +
                    "        }\n" +
                    "    ]\n" +
                    "}";
            String json = this.airwallexConfig.getProperty("airwallex.MM.cardConfig", defaultJson);
            return JsonUtils.readValue(json, PaymentMethodTypesDto.class);
        } catch (Exception e) {
            log.error("Airwallex获取支付金额与方式列表，解析缅甸支付方式异常。", e);
        }
        return null;
    }

    public int getDatabusPersistThreshold() {
        return this.config.getIntProperty("databus.persistThreshold", 200);
    }

    public boolean enableClearDatabusFile() {
        return this.config.getBooleanProperty("databus.enableClearDatabusFile", true);
    }

    public boolean enableAirwallexBiData() {
        return this.config.getBooleanProperty("databus.enableAirwallexBiData", false);
    }

    public boolean isAirwallexAllowAccount(long kugouId) {
        try {
            String[] airwallexAccountArr = this.airwallexConfig.getArrayProperty("airwallex.allowAccount", ",", new String[]{""});
            return Arrays.stream(airwallexAccountArr)
                    .mapToLong(value -> ParseUtils.tryParseLong(StringUtils.trim(value), 0))
                    .anyMatch(value -> value == kugouId);
        } catch (Exception e) {
            log.warn("获取Airwallex允许访问账号，解析配置异常。kugouId: {}", kugouId, e);
            return false;
        }
    }

    public boolean isAirwallexBlockAccount(long kugouId) {
        try {
            String[] airwallexAccountArr = this.airwallexConfig.getArrayProperty("airwallex.blockAccount", ",", new String[]{""});
            return Arrays.stream(airwallexAccountArr)
                    .mapToLong(value -> ParseUtils.tryParseLong(StringUtils.trim(value), 0))
                    .anyMatch(value -> value == kugouId);
        } catch (Exception e) {
            log.warn("获取Airwallex禁止访问账号，解析配置异常。kugouId: {}", kugouId, e);
            return false;
        }
    }

    /**
     * 拼多多商品价格列表
     */
    public List<PddProductBo> getPddProductPriceList() {
        try {
            String property = this.config.getProperty("recharge.pdd.productList", "[]");
            return JsonUtils.parseList(property, PddProductBo.class);
        } catch (Exception e) {
            log.error("读取拼多多商品价格列表，解析配置异常。", e);
        }
        return Lists.newArrayList();
    }

    /**
     * iOS内购沙盒充值金额限制（按版本）
     */
    public BigDecimal getAppstoreSandboxLimitAmount() {
        int limitAmount = this.appstoreConfig.getIntProperty("appstore.sandbox.limitAmount", 15000);
        return BigDecimal.valueOf(limitAmount);
    }

    /**
     * 获取允许使用沙盒充值的客户端版本
     *
     * @return 允许使用沙盒充值的客户端版本
     */
    public Set<String> getAppstoreSandboxAllowVersions() {
        String versions = this.appstoreConfig.getProperty("appstore.sandbox.allowVersions", "");
        return Arrays.stream(versions.split(",")).map(String::trim).collect(Collectors.toSet());
    }

    public boolean isForbiddenSingCoin() {
        return this.appstoreConfig.getBooleanProperty("recharge.forbidden.singCoin", false);
    }

    @Getter
    @Setter
    public static class AppIdConfig {
        private int appId;
        private String appKey;
        private String appDesc;
    }

    /**
     * 获取接口验签配置列表
     *
     * @return 配置列表
     */
    public String appKeyOf(int appId) {
        String property = this.config.getProperty("auth.appid.salt.config", "[]");
        try {
            List<AppIdConfig> list = JsonUtils.parseList(property, AppIdConfig.class);
            Optional<AppIdConfig> first = list.stream().filter(c -> c.getAppId() == appId).findFirst();
            if (first.isPresent()) {
                return first.get().getAppKey();
            }
        } catch (Exception e) {
            log.error("getAppIdConfigList error {}", property, e);
            Cat.logError(e);
        }
        throw new BizException(SysResultCode.RECHARGE_SIGN_INVALID).addContextValue("appId", appId);
    }


    /**
     * 按不同渠道配置提现备注
     */
    public String matchDrawCashRemark(long bizAppid, String defaultRemark) {
        String property = this.config.getProperty("withdraw.remarks", "[]");
        try {
            List<DrawCashRemark> list = JsonUtils.parseList(property, DrawCashRemark.class);
            // 匹配bizAppid 提现渠道匹配备注
            Optional<DrawCashRemark> first = list.stream().filter(d -> d.getBizAppId() == bizAppid).findFirst();
            if (first.isPresent()) {
                String remark = first.get().getRemark();
                return StringUtils.isBlank(remark) ? defaultRemark : remark;
            }
        } catch (Exception e) {
            log.error("matchDrawCashRemark {} error", property, e);
            Cat.logError(e);
        }
        return defaultRemark;
    }

    /**
     * 获取酷狗支付网关订单appid配置
     */
    public List<String> getKupayOrderAppids() {
        String key = "recharge.kupay.order.appids";
        String[] kupayOrderAppids = new String[]{};
        try {
            kupayOrderAppids = kupayConfig.getArrayProperty(key, ",", new String[]{ "1084" });
        } catch (Exception e) {
            log.error("解析酷狗支付网关订单appid配置异常。key: {}", key, e);
        }
        return Arrays.asList(kupayOrderAppids);
    }

    public Optional<BusinessInfoBo> getBusinessInfoById(String businessId) {
        try {
            String config = businessConfig.getProperty("businessList", "[]");
            List<BusinessInfoBo> businessInfoBOList = JSON.parseArray(config, BusinessInfoBo.class);
            businessInfoBOList = CollectionUtils.isNotEmpty(businessInfoBOList) ? businessInfoBOList : Lists.newArrayList();
            return businessInfoBOList.stream()
                    .filter(businessInfoBO -> businessInfoBO.getBusinessId().equalsIgnoreCase(String.valueOf(businessId)))
                    .findFirst();
        } catch (Exception e) {
            log.error("读取配置中心配置，加载配置【PLATFORM.recharge.business】businessList 异常。", e);
            throw new ContextedRuntimeException("读取配置中心配置，加载配置【PLATFORM.recharge.business】businessList 异常。", e);
        }
    }

    public String businessKeyOf(String businessId) {
        Optional<BusinessInfoBo> optional = this.getBusinessInfoById(businessId);
        return optional.isPresent() ? optional.get().getBusinessKey() : StringUtils.EMPTY;
    }

    public String businessKeyOf(int businessId) {
        return businessKeyOf(String.valueOf(businessId));
    }

    public String businessNotifyTopicOf(String businessId) {
        Optional<BusinessInfoBo> optional = this.getBusinessInfoById(businessId);
        return optional.isPresent() ? optional.get().getBusinessNotifyTopic() : StringUtils.EMPTY;
    }

    public String businessDescOf(String businessId) {
        Optional<BusinessInfoBo> optional = this.getBusinessInfoById(businessId);
        return optional.isPresent() ? optional.get().getBusinessDesc() : StringUtils.EMPTY;
    }

    public List<SeaOutProductVo> getGooglePlayProductList() {
        String property = this.googlePlayConfig.getProperty("product.list", "[]");
        try {
            return JsonUtils.parseList(property, SeaOutProductVo.class);
        } catch (Exception e) {
            log.error("getGooglePlayProductList error:", e);
            Cat.logError(e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取google沙箱充值账号白名单
     *
     * @return google沙箱充值账号白名单列表
     */
    public List<String> getGooglePlaySanBoxWhiteKugouId() {
        String key = "sandbox.whiteList";
        String[] whiteList = new String[]{};
        try {
            whiteList = googlePlayConfig.getArrayProperty(key, ",", whiteList);
        } catch (Exception e) {
            log.error("解析Apollo配置异常, key: {}", key, e);
        }
        return Arrays.asList(whiteList);
    }

    /**
     * google沙箱充值账号白名单校验是否开启
     */
    public boolean isGooglePlaySandboxWhiteListCheckOpen() {
        String key = "sandbox.whiteList.open";
        boolean value = googlePlayConfig.getBooleanProperty(key, true);
        log.warn("google沙箱充值账号白名单校验是否开启, value: {}", value);
        return value;
    }

    /**
     * 获取google沙箱充值开启中的版本号
     *
     */
    public List<String> getGooglePlaySandboxSupportVersion() {
        String key = "sandbox.version.support.list";
        String[] whiteList = new String[]{};
        try {
            whiteList = googlePlayConfig.getArrayProperty(key, ",", whiteList);
        } catch (Exception e) {
            log.error("解析Apollo配置异常, key: {}", key, e);
        }
        return Arrays.asList(whiteList);
    }

    public long getGooglePlaySandboxVersionTotalAmountLimit() {
        return this.config.getLongProperty("sandbox.version.amount.limit", 10000L);
    }

    /**
     * google play 禁止充值的币种-防止汇率波动
     *
     */
    public List<String> getGooglePlayBlackCurrency() {
        String key = "black.currency.list";
        String[] whiteList = new String[]{};
        try {
            whiteList = googlePlayConfig.getArrayProperty(key, ",", whiteList);
        } catch (Exception e) {
            log.error("解析Apollo配置异常, key: {}", key, e);
        }
        return Arrays.asList(whiteList);
    }

    /**
     * google充值是否只能在白名单地区使用，默认关闭
     */
    public boolean isGooglePlayOnlySupportInAreaWhiteList() {
        String key = "area.white.list.open";
        boolean value = googlePlayConfig.getBooleanProperty(key, false);
        log.warn("google充值是否只能在白名单地区使用, value: {}", value);
        return value;
    }

    /**
     * google充值白名单地区
     *
     */
    public List<String> getGooglePlaySupportAreaWhiteList() {
        String key = "area.white.list";
        String[] whiteList = new String[]{};
        try {
            whiteList = googlePlayConfig.getArrayProperty(key, ",", whiteList);
        } catch (Exception e) {
            log.error("解析Apollo配置异常, key: {}", key, e);
        }
        return Arrays.asList(whiteList);
    }

    public int pulsarScanJobRunSecondsPerTime() {
        return config.getIntProperty("pulsar.scan.job.run.seconds.per.time",60);
    }

    public int pulsarScanJobRunRecordsPerTime() {
        return config.getIntProperty("pulsar.scan.job.run.records.per.time",1000);
    }
    
    public Optional<RechargeAwardConfig> getRechargeAwardConfig() {
        String key = "rechargeAward.config";
        Optional<RechargeAwardConfig> optionalRechargeBagConfig = Optional.empty();
        try {
            String jsonArr = this.businessConfig.getProperty(key, "{}");
            RechargeAwardConfig rechargeAwardConfig = JSON.parseObject(jsonArr, RechargeAwardConfig.class);
            if (Objects.isNull(rechargeAwardConfig) || StringUtils.isBlank(rechargeAwardConfig.getBizCode())) {
                return Optional.empty();
            }
            optionalRechargeBagConfig = Optional.of(rechargeAwardConfig);
        } catch (Exception e) {
            log.error("解析服务配置，解析[getRechargeBagConfig]异常。key: {}", key, e);
        }
        return optionalRechargeBagConfig;
    }

    public boolean isValidateSignWithPurchaseProductV2() {
        return this.config.getBooleanProperty("open.validate.sign.with.purchase.product", Boolean.FALSE);
    }

    public Optional<RefundHandlerVO> getRefundHandler(String businessId){
        if(StringUtils.isEmpty(businessId)){
            return Optional.empty();
        }

        String config = this.config.getProperty("refund.handler.config", "");
        if(StringUtils.isEmpty(config)){
            return Optional.empty();
        }

        try {
            List<RefundHandlerVO> refundHandlerVOS = JSONArray.parseArray(config, RefundHandlerVO.class);
            if(CollectionUtils.isEmpty(refundHandlerVOS)){
                return Optional.empty();
            }
            Map<String, RefundHandlerVO> map = refundHandlerVOS.stream()
                    .collect(Collectors.toMap(RefundHandlerVO::getBusinessId, Function.identity(), (existing, replacement) -> existing));
            return Optional.ofNullable(map.get(businessId));
        }catch (Exception e){
            log.error("getRefundHandler error {}", businessId, e);
            return Optional.empty();
        }
    }
}