package com.kugou.fanxing.recharge.service.common;

import com.kugou.api.springcloud.GlobalIdServiceThrift.GlobalIdService;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.service.command.GlobalIdServiceCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class OrderIdService {

    @Autowired
    private GlobalIdService.Iface globalIdService;
    @Autowired
    private RechargeConfig rechargeConfig;

    /**
     * 生成跨机房订单号[R09(3) + YmdHis(14) + 时间戳(6) + 10-99 随机数(2)]
     *
     * @return 生成跨机房充值订单号
     */
    public String generateRechargeOrderNumForAcross() {
        String serverZonePart = String.format("R%s9", rechargeConfig.getDataCenterZoneId());
        String dateTimePart = DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHHmmss");
        String nanoTimePart = StringUtils.right(String.valueOf(System.nanoTime()), 6);
        String randomNumPart = String.valueOf(RandomUtils.nextInt(10, 99));
        return serverZonePart + dateTimePart + nanoTimePart + randomNumPart;
    }

    /**
     * 生成GlobalId
     *
     * @return globalId
     */
    public long generateGlobalId() {
        return new GlobalIdServiceCommand(globalIdService).execute();
    }

    /**
     * 根据订单号获取该订单的生成年月（跨机房订单）
     *
     * @param rechargeOrderNum 充值订单号
     * @return 月份
     */
    public Optional<String> getYearMonthFromRechargeOrderNum(String rechargeOrderNum) {
        final String regex = "^R(\\d)9(\\d{6})";
        final Pattern pattern = Pattern.compile(regex);
        final Matcher matcher = pattern.matcher(rechargeOrderNum);
        if (matcher.find()) {
            return Optional.of(matcher.group(2));
        }
        return Optional.empty();
    }

    /**
     * 根据订单号获取该订单的生成年月（跨机房订单）
     *
     * @param rechargeOrderNum 充值订单号
     * @return 月份
     */
    public String getYearMonthFromRechargeOrderNumSilent(String rechargeOrderNum) {
        final String regex = "^R(\\d)9(\\d{6})";
        final Pattern pattern = Pattern.compile(regex);
        final Matcher matcher = pattern.matcher(rechargeOrderNum);
        if (!matcher.find() || StringUtils.isBlank(matcher.group(2))) {
            log.error("根据直播充值订单号获取该订单的生成年月，解析失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        return matcher.group(2);
    }

    public Optional<String> extractTransactionId(String rechargeOrderNum) {
        String regex = "^R\\d+S\\dTID(.+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(rechargeOrderNum);
        if (matcher.find() && matcher.groupCount() == 1) {
            return Optional.of(matcher.group(1));
        }
        return Optional.empty();
    }

}
