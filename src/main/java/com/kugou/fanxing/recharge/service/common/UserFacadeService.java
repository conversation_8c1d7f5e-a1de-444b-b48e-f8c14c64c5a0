package com.kugou.fanxing.recharge.service.common;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.model.vo.UserEntity;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.command.*;
import com.kugou.fanxing.recharge.service.stat.UserEverRechargeStatService;
import com.kugou.fanxing.recharge.util.IpUtils;
import com.kugou.fanxing.thrift.banaccount.service.BanAccountService;
import com.kugou.fanxing.thrift.idmapping.user.UserIdMappingService;
import com.kugou.fanxing.thrift.plat.user.UserPlatBizService;
import com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq;
import com.kugou.fanxing.userbaseinfo.service.UserModuleV2BizService;
import com.kugou.fanxing.userbaseinfo.vo.UserCoreInfoResponse;
import com.kugou.fanxing.userbaseinfo.vo.UserCoreInfoVO;
import com.kugou.fanxing.userbaseinfo.vo.UserResponse;
import com.kugou.fanxing.userbaseinfo.vo.UserVO;
import com.kugou.kw.idservice.api.struct.BatchIdResponse;
import com.kugou.kw.idservice.api.struct.IdInfo;
import com.kugou.kw.idservice.api.struct.IdResponse;
import com.kugou.kw.idservice.api.struct.KuwoIdMappingService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

@Slf4j
@Service
public class UserFacadeService {

    @Autowired
    private UserIdMappingService.Iface userIdMappingService;
    @Autowired
    private UserModuleV2BizService.Iface userModuleV2BizService;
    @Autowired
    private UserPlatBizService.Iface userPlatBizService;
    @Autowired
    private BanAccountService.Iface banAccountService;
    @Autowired
    private KuwoIdMappingService.Iface kuwoIdMappingService;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private UserEverRechargeStatService userEverRechargeStatService;

    /**
     * 批量获取用户昵称
     *
     * @param kugouIds 酷狗ID列表
     * @return 昵称列表
     */
    public Map<Long, String> getNicknameByKugouIds(List<Long> kugouIds) {
        GetUserListByKugouIdListCommand command = new GetUserListByKugouIdListCommand(userModuleV2BizService, kugouIds);
        List<UserVO> userVOList = command.execute();
        final Map<Long, String> nicknameMap = userVOList.stream().collect(toMap(UserVO::getKugouId, UserVO::getNickName));
        kugouIds.forEach(kugouId -> nicknameMap.putIfAbsent(kugouId, ""));
        return nicknameMap;
    }

    /**
     * 批量获取用戶信息接口
     *
     * @param kugouIds 酷狗ID列表
     * @return 繁星id列表
     */
    public Map<Long, UserVO> getUserByKugouIds(List<Long> kugouIds) {
        GetUserListByKugouIdListCommand command = new GetUserListByKugouIdListCommand(userModuleV2BizService, kugouIds);
        List<UserVO> userVOList = command.execute();
        return userVOList.stream().collect(toMap(UserVO::getKugouId, userVO -> userVO));
    }

    /**
     * 繁星ID=>酷狗ID（降级）
     *
     * @param userId 繁星ID
     * @return 酷狗ID
     */
    public Optional<Long> getKugouIdByUserId(long userId) {
        return this.getKugouIdByUserId(userId, true);
    }

    /**
     * 繁星ID=>酷狗ID
     *
     * @param userId 繁星ID
     * @param allowDegradation 是否允许降级
     * @return 酷狗ID
     */
    public Optional<Long> getKugouIdByUserId(long userId, boolean allowDegradation) {
        if (allowDegradation) {
            return new GetKugouIdByUserIdCommand(userIdMappingService, userId).execute();
        }
        try {
            long kugouId = userIdMappingService.getKugouIdByUserId(userId);
            return Optional.of(kugouId);
        } catch (Exception e) {
            log.error("繁星ID=>酷狗ID，转换异常。userId: {}", userId);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR, e);
        }
    }

    /**
     * 酷狗ID=>繁星ID
     *
     * @param kugouId 酷狗ID
     * @return 繁星ID
     */
    public Optional<Long> getUserIdByKugouId(long kugouId) {
        return this.getUserIdByKugouId(kugouId, true);
    }

    /**
     * 酷狗ID=>繁星ID
     *
     * @param kugouId 酷狗ID
     * @param allowDegradation 是否允许降级
     * @return 繁星ID
     */
    public Optional<Long> getUserIdByKugouId(long kugouId, boolean allowDegradation) {
        if (allowDegradation) {
            return new GetUserIdByKugouIdCommand(userIdMappingService, kugouId).execute();
        }
        try {
            long userId = userIdMappingService.getUserIdByKugouId(kugouId);
            return Optional.of(userId);
        } catch (Exception e) {
            log.error("酷狗ID=>繁星ID，转换异常。kugouId: {}", kugouId);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR, e);
        }
    }

    /**
     * 获取用户基本信息
     */
    public Optional<UserVO> getUserVOByKugouId(long kugouId) {
        try {
            UserResponse userResponse = userModuleV2BizService.getUserByKugouId(kugouId);
            if (userResponse != null && userResponse.getRet() == 0 && userResponse.getData() != null) {
                return Optional.of(userResponse.getData());
            }
        } catch (Exception e) {
            log.error("根据酷狗id获取用户信息失败,kugouId: {} ", kugouId);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR, e);
        }
        return Optional.empty();
    }

    /**
     * 繁星ID=>酷狗ID（批量）
     *
     * @param userIds 繁星ID
     * @return 繁星ID与酷狗ID对应关系
     */
    public Map<Long, Long> getKugouIdsByUserIds(List<Long> userIds) {
        GetKugouIdMappingByUserIdsCommand command = new GetKugouIdMappingByUserIdsCommand(userModuleV2BizService, userIds);
        return command.execute();
    }

    /**
     * 检查是否合法的酷狗ID账号
     *
     * @param kugouId 酷狗ID
     * @return 是否合法的酷狗ID账号
     */
    public boolean isValidKugouId(long kugouId) {
        Optional<String> optionalData = new GetUserInfoFromKugouV1Command(userPlatBizService, kugouId).execute();
        return optionalData.isPresent();
    }

    /**
     * 检查用户是否封禁
     * @param kugouId 酷狗ID
     * @return 是否封禁用户
     */
    public boolean isBannedAccount(long kugouId) {
        return new IsBanAccountCommand(banAccountService, kugouId).execute();
    }

    /**
     * 检查用户是否注销
     * @param kugouId 酷狗ID
     * @return 是否注销用户
     */
    public boolean isRevokedAccount(long kugouId) {
        return new IsUserCancelCommand(userPlatBizService, kugouId).execute();
    }

    /**
     * 发送短信
     *
     * @param kugouId  短信接收用户
     * @param msg      短信内容
     */
    public boolean sendSmsByKugouId(long kugouId, String msg) {
        SendSmsByKugouIdReq request = new SendSmsByKugouIdReq();
        request.setAppId(1011);
        request.setChannel("soa");
        request.setClientIp(IpUtils.getServerIpAddress());
        request.setKugouId(kugouId);
        request.setMsg(msg);
        request.setSign(FinanceSignUtils.makeSign(request, "WoD353RU"));
        return new SendSmsByKugouIdCommand(userPlatBizService, request).execute();
    }

    /**
     * 发送短信
     *
     * @param kugouIds 短信接收用户
     * @param msg      短信内容
     */
    public void sendSmsByKugouIds(List<Long> kugouIds, String msg) {
        kugouIds.forEach(kugouId -> sendSmsByKugouId(kugouId, msg));
    }

    /**
     * 酷我ID批量转酷狗ID
     * @param kuwoIdList    酷我ID列表
     * @return              酷狗ID列表
     */
    @SneakyThrows
    public List<Long> getKugouIdsByKuwoIds(List<Long> kuwoIdList) {
        if (!apolloConfigService.isKuwoEnv()) {
            log.warn("批量酷我ID转酷狗ID，酷狗环境忽略。kuwoIdList: {}", kuwoIdList);
            return Lists.newArrayList();
        }
        BatchIdResponse response = this.kuwoIdMappingService.batchGetIdInfoByKuwoId(kuwoIdList);
        if (Objects.isNull(response) || response.getCode() != 0 || Objects.isNull(response.getData())) {
            log.error("批量酷我ID转酷狗ID，转换失败。kuwoIdList: {}, response: {}", kuwoIdList, response);
            return Lists.newArrayList();
        }
        List<IdInfo> idInfoList = response.getData();
        Map<Long, Long> kuwoId2KugouIdMap = idInfoList.stream().collect(toMap(IdInfo::getKuwoId, IdInfo::getKugouId, (a1, a2) -> a2));
        return kuwoIdList.stream().map(kuwoId -> kuwoId2KugouIdMap.getOrDefault(kuwoId, 0L))
                .filter(kugouId -> kugouId > 0).collect(Collectors.toList());
    }

    @SneakyThrows
    public Map<Long, Long> getKuwoIdByKugouId(List<Long> kugouIdList) {
        if (!apolloConfigService.isKuwoEnv()) {
            log.warn("酷狗ID转酷我ID，酷狗环境忽略。kugouIdList: {}", kugouIdList);
            return Maps.newHashMap();
        }
        BatchGetIdInfoByKugouIdCommand command = new BatchGetIdInfoByKugouIdCommand(kuwoIdMappingService, kugouIdList);
        return command.execute();
    }

    @SneakyThrows
    public Optional<Long> getKuwoIdByKugouId(long kugouId) {
        if (!apolloConfigService.isKuwoEnv()) {
            log.warn("酷狗ID转酷我ID，酷狗环境忽略。kugouId: {}", kugouId);
            return Optional.empty();
        }
        IdResponse response = this.kuwoIdMappingService.getIdInfoByKugouId(kugouId);
        if (Objects.isNull(response) || response.getCode() != 0 || Objects.isNull(response.getData())) {
            log.error("酷狗ID转酷我ID，转换失败。kugouId: {}, response: {}", kugouId, response);
            return Optional.empty();
        }
        return Optional.of(response.getData().getKuwoId());
    }

    @SneakyThrows
    public Optional<Long> getKugouIdByKuwoId(long kuwoId) {
        if (!apolloConfigService.isKuwoEnv()) {
            log.warn("酷我ID转酷狗ID，酷狗环境忽略。kugouId: {}", kuwoId);
            return Optional.empty();
        }
        IdResponse response = this.kuwoIdMappingService.getIdInfoByKuwoId(kuwoId);
        if (Objects.isNull(response) || response.getCode() != 0 || Objects.isNull(response.getData())) {
            log.error("酷我ID转酷狗ID，转换失败。kuwoId: {}, response: {}", kuwoId, response);
            return Optional.empty();
        }
        return Optional.of(response.getData().getKugouId());
    }

    public BigDecimal getUserCoinBalance(long kugouId) {
        try {
            Optional<UserEntity> userEntity = userEverRechargeStatService.getUserEntity(kugouId, 10018);
            if (userEntity.isPresent()) {
                return BigDecimal.valueOf(userEntity.get().getCoin());
            }
        } catch (Exception e) {
            log.error("获取用户星币余额，获取异常。kugouId: {}", kugouId, e);
        }
        return BigDecimal.ZERO;
    }

    @SneakyThrows
    public boolean isOverseasRegisterUser(long kugouId) {
        UserCoreInfoResponse response = this.userModuleV2BizService.getUserCoreInfoByKugouId(kugouId);
        if (Objects.isNull(response) || response.getRet() != 0 || Objects.isNull(response.getData())) {
            log.warn("获取用户注册来源，获取失败。kugouId: {}, response: {}", kugouId, response);
            throw new ContextedRuntimeException("获取用户注册来源，获取失败。").addContextValue("kugouId", kugouId);
        }
        UserCoreInfoVO userCoreInfoVO = response.getData();
        int registerFrom = userCoreInfoVO.getRegisterFrom();
        boolean isOverseasRegisterUser = registerFrom == 1;
        log.warn("获取用户注册来源，是否海外注册用户。kugouId: {}, isOverseasRegisterUser: {}", kugouId, isOverseasRegisterUser);
        return isOverseasRegisterUser;
    }
}
