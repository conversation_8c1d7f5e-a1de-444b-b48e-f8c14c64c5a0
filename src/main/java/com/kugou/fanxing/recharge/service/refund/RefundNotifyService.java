package com.kugou.fanxing.recharge.service.refund;

import com.alibaba.fastjson.JSON;
import com.kugou.fanxing.recharge.constant.NotifyTypeEnum;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundOrderDao;
import com.kugou.fanxing.recharge.model.RefundNotifyMsgDTO;
import com.kugou.fanxing.recharge.model.po.refund.RefundOrderPO;
import com.kugou.fanxing.recharge.model.vo.RefundHandlerVO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.mq.api.exception.MQException;
import com.kugou.mq.api.producer.ProducerMessage;
import com.kugou.mq.pulsar.core.PulsarTemplate;
import com.kugou.mq.pulsar.core.SafePulsarTemplate;
import com.kugou.springcloud.http.KugouHttpRequest;
import com.kugou.springcloud.http.KugouHttpResponse;
import com.kugou.springcloud.http.template.KugouHttpTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;

/**
 * @Author: yuzhaopeng
 * @Description:
 * @Date: 2024/2/26 15:34
 */
@Slf4j
@Service
public class RefundNotifyService {

    @Autowired
    private KugouHttpTemplate kgrpcProxy;

    @Autowired
    private RefundOrderDao refundOrderDao;

    @Autowired
    private ApolloConfigService apolloConfigService;

    @Autowired
    private PulsarTemplate<String> pulsarTemplate;

    @Value("${kugou.pulsar.producer.recharge.refund.topic-name}")
    private String pulsarTopic;

    public String execute(){
        long startId = 0;
        int limit = 500;
        int total = 0;
        int successTotal = 0;
        long startTime = 0L;
        long endTime = DateHelper.getCurrentSeconds();
        while (true) {
            List<RefundOrderPO> refundOrderPOList = refundOrderDao.getUnNotifyList(startTime, endTime, startId, limit);
            if (refundOrderPOList == null || refundOrderPOList.isEmpty()) {
                break;
            }
            total += refundOrderPOList.size();
            for (RefundOrderPO refundOrderPO : refundOrderPOList) {
                successTotal += notifyRefund(refundOrderPO);
                startId = refundOrderPO.getId();
            }
            if (refundOrderPOList.size() < limit) {
                break;
            }
        }
        return String.format("退款通知总数量:%d, 成功通知数量:%d", total, successTotal);
    }

    public int notifyRefund(RefundOrderPO refundOrderPO) {
        RefundNotifyMsgDTO refundNotifyMsgDTO = getRefundNotifyMsgDTO(refundOrderPO);
        String req = JsonUtils.toJSONString(refundNotifyMsgDTO);
        boolean flag = true;
        Optional<RefundHandlerVO> refundHandlerVOOptional = apolloConfigService.getRefundHandler(refundOrderPO.getBusinessId());
        if (refundHandlerVOOptional.isPresent()) {
            RefundHandlerVO refundHandlerVO = refundHandlerVOOptional.get();
            //获取通知类型
            if (refundHandlerVO.getNotifyType() == NotifyTypeEnum.KGRPC.getCode()) {
                flag = kgRpc(refundHandlerVO.getCallBackUrl(), req);
            }
        }

        //默认都会发PULSAR
        if (flag) {
            try {
                pulsarTemplate.send(pulsarTopic, req);
                int i = refundOrderDao.notifySuccess(refundOrderPO.getRechargeOrderNum());
                if (i != 1) {
                    throw new RuntimeException("update notifySuccess error!,rechargeOrderNum:" + refundOrderPO.getRechargeOrderNum());
                }
                return 1;
            } catch (Exception e) {
                log.error("notifySuccess error!{}", refundOrderPO.getRechargeOrderNum(), e);
            }
        }
        return 0;
    }

    private static RefundNotifyMsgDTO getRefundNotifyMsgDTO(RefundOrderPO refundOrderPO) {
        RefundNotifyMsgDTO refundNotifyMsgDTO = new RefundNotifyMsgDTO();
        refundNotifyMsgDTO.setRechargeOrderNum(refundOrderPO.getRechargeOrderNum());
        refundNotifyMsgDTO.setCoin(refundOrderPO.getCoin());
        refundNotifyMsgDTO.setPrize(refundOrderPO.getPrice());
        refundNotifyMsgDTO.setCoinType(refundOrderPO.getCoinType());
        refundNotifyMsgDTO.setBusinessId(refundOrderPO.getBusinessId());
        refundNotifyMsgDTO.setCancelTime(refundOrderPO.getCancelTime());
        return refundNotifyMsgDTO;
    }

    public boolean kgRpc(String bizNotifyUrl, String req){
        try{
            URL url = new URL(bizNotifyUrl);
            String kgRpcHost = url.getHost();
            String path = url.getPath();
            KugouHttpRequest kugouHttpRequest = KugouHttpRequest.build()
                    .setUrl(path)
                    .addHeader("KgrpcHost", kgRpcHost)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("x-rpc-send-timeout", "500ms")
                    .addHeader("x-rpc-read-timeout", "1000ms")
                    .addHeader("x-rpc-next-upstream-timeout", "1500ms")
                    .setBody(req.getBytes(StandardCharsets.UTF_8));
            KugouHttpResponse res = kgrpcProxy.post(kugouHttpRequest);
            log.warn("kgRpc notify kgRpcHost:{}, path:{}, req:{}, res is {} ", kgRpcHost, path, req, JSON.toJSONString(res, true));
            if(res.isSuccess() && res.getContent() != null){
                String content = new String(res.getContent());
                int errorCode = JsonUtils.parseJsonPath(content, "$.code", Integer.class, -1);
                String errorMsg = JsonUtils.parseJsonPath(content, "$.msg", String.class, "");
                if (errorCode != 0) {
                    log.warn("退款通知回调失败响应!req:{}, errorCode:{}, errorMsg:{}", req, errorCode, errorMsg);
                    return false;
                }else{
                    // 明确成功
                    log.warn("退款通知回调成功响应!req:{}, errorCode:{}, errorMsg:{}", req, errorCode, errorMsg);
                    return true;
                }
            }
        }catch (Exception e){
            log.error("kgRpc notify error bizNotifyUrl:{}, req:{},", bizNotifyUrl, req, e);
        }
        return false;
    }


}
