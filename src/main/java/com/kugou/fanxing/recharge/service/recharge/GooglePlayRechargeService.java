package com.kugou.fanxing.recharge.service.recharge;

import com.google.common.collect.Maps;
import com.kugou.config.Env;
import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.commons.json.JSON;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.AreaIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.dto.GwFinishGpOrderV3Dto;
import com.kugou.fanxing.recharge.model.dto.GwGpOrderV3Dto;
import com.kugou.fanxing.recharge.model.dto.RechargeExtendDTO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.*;
import com.kugou.fanxing.recharge.model.vo.SeaOutProductVo;
import com.kugou.fanxing.recharge.service.*;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.fanxing.thrift.pay.v2.PlatformPayV2Service;
import com.kugou.fanxing.thrift.pay.v2.SandboxRechargeLimitReq;
import com.kugou.fanxing.thrift.pay.v2.SandboxRechargeLimitResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Supplier;

@Slf4j
@Service
public class GooglePlayRechargeService {

    @Autowired
    ApolloConfigService apolloConfigService;

    @Autowired
    RechargeCommonService rechargeCommonService;
    @Autowired
    OrderIdService orderIdService;
    @Autowired
    RechargeConfig rechargeConfig;
    @Autowired
    KupayService kupayService;
    @Autowired
    RechargeOrderService rechargeOrderService;
    @Autowired
    PlatformPayV2Service.Iface platformPayV2Service;
    @Autowired
    private Env env;
    @Autowired
    private RemoteStrategyService remoteStrategyService;

    @SneakyThrows
    public Map<String, Object> createOrderForGp(WebCommonParam commonParam, CreateGpRequest createIapRequest) {
        Optional<SeaOutProductVo> optionalAppStoreProductVo = this.getProductById(createIapRequest.getProductId());
        if (!optionalAppStoreProductVo.isPresent()) {
            log.warn("google play应用内充值下单接口，货品码参数非法。webCommonParam: {}, createIapRequest: {}", commonParam, createIapRequest);
            throw new BizException(SysResultCode.GP_PRODUCT_ID_INVALID).addContextValue("productId", createIapRequest.getProductId());
        }
        if(isInGooglePlayBlackCurrencylist(createIapRequest.getCurrency())){
            throw new BizException(SysResultCode.GP_BLACK_CURRENCY);
        }
        if(!checkWhiteArea(commonParam.getAreaCode())){
            throw new BizException(SysResultCode.GP_BLACK_CURRENCY);
        }
        SeaOutProductVo appStoreProductVo = optionalAppStoreProductVo.get();

        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        SysResultCode resultCode = rechargeCommonService.checkPreconditionForGetOrder(rechargeOrderNum, commonParam, createIapRequest);
        if (!resultCode.isSuccess()) {
            log.warn("google play应用内充值下单接口，处理外部检查失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(resultCode);
        }
        RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(commonParam.getStdPlat(), commonParam.getKugouId(), rechargeOrderNum, createIapRequest.getPayTypeIdEnum(), new BigDecimal(appStoreProductVo.getRmbMoney()), commonParam.getIp(), Optional.empty());
        fillRechargeOutSeaEntity(rechargeAcrossPO,commonParam);
        setGooglePlayEntity(rechargeAcrossPO,appStoreProductVo,createIapRequest);
        Map<String, String> kupaysParam = Maps.newHashMap();
        Map<String, Object> extendParam = Maps.newHashMap();
        extendParam.put("version",commonParam.getVersion());
        GwGpOrderV3Dto gwiosOrderV3Dto = kupayService.gwGpOrder(rechargeAcrossPO, kupaysParam, extendParam)
                .orElseThrow((Supplier<Throwable>) () -> new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE));
        rechargeAcrossPO.setConsumeOrderNum(gwiosOrderV3Dto.getOut_trade_no());
        log.warn("google play应用内充值下单,最后订单信息：{}", JSON.toJSONString(rechargeAcrossPO));
        int affected = rechargeOrderService.addRechargeOrder(rechargeAcrossPO);
        if (affected < 1) {
            log.warn("google play应用内充值下单接口，保存订单失败。rechargeAcrossPO: {}", rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        Map<String, Object> payload = Maps.newHashMap();
        payload.put("rechargeOrderNum", rechargeOrderNum);
        payload.put("outTradeNo", rechargeAcrossPO.getConsumeOrderNum());
        log.warn("google play应用内充值下单接口，保存订单成功。rechargeAcrossPO: {}", rechargeOrderNum);
        return payload;
    }

    @SneakyThrows
    public Map<String, Object> finishOrderForGp(WebCommonParam commonParam, FinishGpRequest finishIapRequest) {
        Map<String, Object> payload = Maps.newHashMap();
        Optional<RechargeAcrossPO> optionalRechargeAcrossPO = this.rechargeOrderService.queryByRechargeOrderNum(finishIapRequest.getRechargeOrderNum());
        if (!optionalRechargeAcrossPO.isPresent()) {
            log.warn("google play应用内充值上报接口，处理外部检查失败。commonParam: {}, finishIapRequest: {}", commonParam, finishIapRequest);
            throw new BizException(SysResultCode.E_100047);
        }
        RechargeAcrossPO rechargeAcrossPO = optionalRechargeAcrossPO.get();
        if(rechargeAcrossPO.getStatus() == 1){
            payload.put("rechargeOrderNum", rechargeAcrossPO.getRechargeOrderNum());
            return payload;
        }
        remoteStrategyService.strategyVerifyForGpFinishOrder(rechargeAcrossPO.getRechargeOrderNum(),rechargeAcrossPO.getKugouId(),rechargeAcrossPO.getAmount(),commonParam);
        // 校验内购收据
        Map<String, String> kupaysParam = Maps.newHashMap();
        kupaysParam.put("receipt_data", finishIapRequest.getReceiptData());
        kupaysParam.put("is_ack", finishIapRequest.getIsConsume() == 0 ? "0" : "1");
        Map<String, Object> extendParam = Maps.newHashMap();
        Optional<GwFinishGpOrderV3Dto> gwiosCheckNewReceiptLocalV1Dto = kupayService.gwFinishGpOrder(rechargeAcrossPO, kupaysParam, extendParam);
        boolean isValidReceipt = gwiosCheckNewReceiptLocalV1Dto.isPresent() && GwFinishGpOrderV3Dto.ReceiptStatusEnum.isValidReceipt(gwiosCheckNewReceiptLocalV1Dto.get().getReceipt_status());
        if (!isValidReceipt) {
            log.warn("google play应用内充值上报接口，用户收据格式非法。commonParam: {}, finishIapRequest: {}", commonParam, finishIapRequest);
            throw new BizException(SysResultCode.GP_RECEIPT_FORMAT_INVALID);
        }
        payload.put("rechargeOrderNum", gwiosCheckNewReceiptLocalV1Dto.get().getRechargeOrderNum());
        payload.put("transactionId", gwiosCheckNewReceiptLocalV1Dto.get().getTransaction_id());
        log.warn("google play应用内充值上报接口，保存订单成功。rechargeAcrossPO: {}, payload: {}", rechargeAcrossPO, payload);
        return payload;
    }

    public List<SeaOutProductVo> getProductList(WebCommonParam commonParam, RechargeAmountGearRequest rechargeAmountGearRequest) {
        return getGooglePlayProductList();
    }

    public List<SeaOutProductVo> getGooglePlayProductList() {
        return apolloConfigService.getGooglePlayProductList();
    }

    public Optional<SeaOutProductVo> getProductById(String productId) {
        List<SeaOutProductVo> productList = getGooglePlayProductList();
        return productList.stream().filter(p -> p.getProductId().equals(productId)).findFirst();
    }

    public BigDecimal getProductCoinById(String productId) {
        Optional<SeaOutProductVo> seaOutProductVo = getProductById(productId);
        if(!seaOutProductVo.isPresent()){
            return null;
        }
        if(seaOutProductVo.get().getCoin() == null){
            return null;
        }
        return new BigDecimal(seaOutProductVo.get().getCoin());
    }

    public void fillRechargeOutSeaEntity(RechargeAcrossPO rechargeAcrossPO,WebCommonParam webCommonParam) {
        rechargeAcrossPO.setTimeZone(webCommonParam.getTimeZone());
        rechargeAcrossPO.setAreaCode(webCommonParam.getAreaCode());
    }

    public void setGooglePlayEntity(RechargeAcrossPO rechargeAcrossPO,SeaOutProductVo seaOutProductVo,CreateGpRequest createGpRequest) {
        rechargeAcrossPO.setCurrency(createGpRequest.getCurrency());
        rechargeAcrossPO.setUsdAmount(new BigDecimal(seaOutProductVo.getMoney()));
        rechargeAcrossPO.setProductId(seaOutProductVo.getProductId());
        rechargeAcrossPO.setCurrencyAmount(createGpRequest.getAmount());
        rechargeAcrossPO.setAreaId(AreaIdEnum.OUT_SEA.getAreaId());
    }

    /**
     * 检查是否在白名单
     *
     * @param kugouId 酷狗ID
     * @return 是否白名单账户
     */
    public boolean isInGooglePlaySandboxWhitelist(long kugouId) {
        if(!apolloConfigService.isGooglePlaySandboxWhiteListCheckOpen()){
            return true;
        }
        List<String> whiteList = this.apolloConfigService.getGooglePlaySanBoxWhiteKugouId();
        log.warn("google沙箱充值, googlePlaySandboxWhitelist: {}", whiteList);
        if (CollectionUtils.isEmpty(whiteList)) {
            return false;
        }
        return whiteList.stream().anyMatch(s -> s.equals(String.valueOf(kugouId)));
    }

    /**
     * 检查版本号充值控制
     *
     * @return 是否满足限制
     */
    public boolean isOverLimit(RechargeAcrossPO rechargeAcrossPO,String version) {
        if(env.isDev() || env.isTest()){
            return false;
        }
        List<String> whiteList = this.apolloConfigService.getGooglePlaySandboxSupportVersion();
        log.warn("google沙箱充值, googlePlaySandboxSupportVersion: {}", whiteList);
        if (CollectionUtils.isEmpty(whiteList)) {
            log.warn("google沙箱充值,isOverLimit, googlePlaySandboxSupportVersion empty: {}", whiteList);
            return true;
        }
        if(version == null){
            log.warn("google沙箱充值,isOverLimit, version is null empty: {}", version);
            return true;
        }
        if(whiteList.stream().noneMatch(s -> s.equals(version))){
            log.warn("google沙箱充值,isOverLimit, version not open: {}", version);
            return true;
        }
        if(rechargeAcrossPO.getRechargeOrderNum() == null || rechargeAcrossPO.getUsdAmount() == null){
            log.warn("google沙箱充值,isOverLimit, order not right: {}", rechargeAcrossPO);
            return true;
        }
        SandboxRechargeLimitReq sandboxRechargeLimitReq = new SandboxRechargeLimitReq();
        sandboxRechargeLimitReq.setAppId(1000_0002);
        sandboxRechargeLimitReq.setOrderNum(rechargeAcrossPO.getRechargeOrderNum());
        sandboxRechargeLimitReq.setCalKey(version);
        sandboxRechargeLimitReq.setCalValue(rechargeAcrossPO.getUsdAmount().toPlainString());
        long limitValue = apolloConfigService.getGooglePlaySandboxVersionTotalAmountLimit();
        sandboxRechargeLimitReq.setLimitValue(limitValue);
        sandboxRechargeLimitReq.setSign(FinanceSignUtils.makeSign(sandboxRechargeLimitReq, "q4Ya9B7MVq5CYBXt"));
        try {
            SandboxRechargeLimitResponse sandboxRechargeLimitResponse = platformPayV2Service.sandboxRechargeLimit(sandboxRechargeLimitReq);
            if(sandboxRechargeLimitResponse.getCode() != 0 || sandboxRechargeLimitResponse.getTotalValue() == null || new BigDecimal(sandboxRechargeLimitResponse.getTotalValue()).longValue() > limitValue){
                log.error("sandboxRechargeLimit request over limit,rechargeOrderNum:{},version:{},result:",rechargeAcrossPO.getRechargeOrderNum(),version,sandboxRechargeLimitResponse);
                return true;
            }else {
                return false;
            }
        }catch (Exception e){
            log.error("sandboxRechargeLimit request fail,rechargeOrderNum:{},version:{},error:",rechargeAcrossPO.getRechargeOrderNum(),version,e);
            return true;
        }
    }

    /**
     * 检查currency是否在黑名单
     *
     * @param currency 酷狗ID
     * @return 是否白名单账户
     */
    public boolean isInGooglePlayBlackCurrencylist(String currency) {
        List<String> whiteList = this.apolloConfigService.getGooglePlayBlackCurrency();
        log.warn("google沙箱充值, googlePlaySandboxWhitelist: {}", whiteList);
        if (CollectionUtils.isEmpty(whiteList)) {
            return false;
        }
        return whiteList.stream().anyMatch(s -> s.equals(currency));
    }

    public boolean checkWhiteArea(String areaCode) {
        if(!apolloConfigService.isGooglePlayOnlySupportInAreaWhiteList()){
            return true;
        }
        List<String> whiteList = this.apolloConfigService.getGooglePlaySupportAreaWhiteList();
        log.warn("google沙箱充值, googlePlaySandboxWhitelist: {}", whiteList);
        if (CollectionUtils.isEmpty(whiteList)) {
            return false;
        }
        return whiteList.stream().anyMatch(s -> s.equals(areaCode));
    }
}
