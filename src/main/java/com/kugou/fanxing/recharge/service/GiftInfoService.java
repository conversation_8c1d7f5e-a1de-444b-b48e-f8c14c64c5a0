package com.kugou.fanxing.recharge.service;

import com.google.gson.Gson;
import com.kugou.cache.rediscluster.RedisClusterInterface;
import com.kugou.config.Env;
import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.finance.goods.gift.*;
import com.kugou.fanxing.recharge.alert.AlerterFacade;
import com.kugou.fanxing.recharge.model.vo.GoodsInfoVO;
import com.kugou.fanxing.recharge.util.RedisKeys;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * PayPal Service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class GiftInfoService {

    private static final int TTL=86400 * 30;
    private static final String GET_GIFT_LIST_FROM_GOODS_CONTENT = "充值服务调用Goods接口,拉取物品列表";

    @Autowired
    private GoodsGiftService.Iface goodsService;
    @Autowired
    RedisClusterInterface redisClusterInterface;
    @Autowired
    private AlerterFacade alerterFacade;

    @Autowired
    private Env env;

    @Value("${finance.goods.appKey}")
    private String appKey;


    /**
     * 每5分钟同步一次
     */
    public void cacheGoodsInfo() {
        GetAllGiftInfoRequest request = new GetAllGiftInfoRequest();
        request.setAppId(env.getProjectName());

        request.setGiftMd5(StringUtils.EMPTY);
        request.setSign(FinanceSignUtils.makeSign(request, appKey));
        log.warn("开始拉取礼物列表，param：{}",request);
        try {
            AllSimpleGiftInfoResult allGoodsInfoResult = goodsService.getAllSimpleGiftInfo(request);
            if (Objects.isNull(allGoodsInfoResult)) {

                log.error(GET_GIFT_LIST_FROM_GOODS_CONTENT + "，结果为空");
                alerterFacade.sendRTX(GET_GIFT_LIST_FROM_GOODS_CONTENT + "，结果为空");
                return;
            }
            log.warn("拉取礼物列表，结果为: {}", allGoodsInfoResult.getRet());
            if (allGoodsInfoResult.getRet() != 0) {
                alerterFacade.sendRTX(GET_GIFT_LIST_FROM_GOODS_CONTENT + "返回代码不正确：" + allGoodsInfoResult.getRet());
                return;
            }
            if (Objects.isNull(allGoodsInfoResult.getData())) {
                log.error(GET_GIFT_LIST_FROM_GOODS_CONTENT + "礼物列表为空");
                alerterFacade.sendRTX(GET_GIFT_LIST_FROM_GOODS_CONTENT + "礼物列表为空");
                return;
            }
            if (CollectionUtils.isEmpty(allGoodsInfoResult.getData().getData())) {
                return;
            }
            allGoodsInfoResult.getData().getData().forEach(this::cacheGoods);
        }catch (Exception e){
            log.error(GET_GIFT_LIST_FROM_GOODS_CONTENT + "发生异常:",e);
            alerterFacade.sendRTX(GET_GIFT_LIST_FROM_GOODS_CONTENT + "异常退出");
        }
    }

    /**
     * 缓存礼物信息到redis
     * @param giftInfo
     */
    private void cacheGoods(SimpleGiftInfo giftInfo) {
        GoodsInfoVO goodsInfoVO = new GoodsInfoVO();
        goodsInfoVO.setId(giftInfo.getId());
        goodsInfoVO.setType(1);
        goodsInfoVO.setName(giftInfo.getName());
        goodsInfoVO.setDesc(giftInfo.getName());
        goodsInfoVO.setCoin(new BigDecimal(giftInfo.getPrice()));
        long id = giftInfo.getId();
        String key = getGiftCacheKey(id);
        redisClusterInterface.setex(key, TTL, new Gson().toJson(goodsInfoVO));
    }

    private String getGiftCacheKey(long id) {
        return RedisKeys.GOODS_GIFT_INFO+id;
    }

    public GoodsInfoVO getById(long id) {
        GoodsInfoVO goodsInfo = null;
        String key = getGiftCacheKey(id);
        String value = redisClusterInterface.get(key);
        if(!StringUtils.isEmpty(value)){
            goodsInfo = new Gson().fromJson(value,GoodsInfoVO.class);
        }
        if(goodsInfo != null){
            return goodsInfo;
        }
        goodsInfo = getFromRpc(id);
        if(Objects.nonNull(goodsInfo)){
            redisClusterInterface.setex(key, TTL, new Gson().toJson(goodsInfo));
        }
        return goodsInfo;
    }

    public GoodsInfoVO getFromRpc(long id) {
        try {
            GetGiftInfoRequest getGiftInfoRequest = new GetGiftInfoRequest();
            getGiftInfoRequest.setAppId(env.getProjectName());
            getGiftInfoRequest.setGiftId((int)id);
            getGiftInfoRequest.setSign(FinanceSignUtils.makeSign(getGiftInfoRequest, appKey));

            SimpleGiftInfoResult simpleGiftInfoResult = goodsService.getSimpleGiftInfo(getGiftInfoRequest);
            if(Objects.isNull(simpleGiftInfoResult) || simpleGiftInfoResult.getRet() != 0 || Objects.isNull(simpleGiftInfoResult.getData())){
                log.error("getGiftFromRpc Fail,param: {}", getGiftInfoRequest);
                return null;
            }
            SimpleGiftInfo giftInfo = simpleGiftInfoResult.getData();
            GoodsInfoVO goodsInfoVO = new GoodsInfoVO();
            goodsInfoVO.setId(giftInfo.getId());
            goodsInfoVO.setType(1);
            goodsInfoVO.setName(giftInfo.getName());
            goodsInfoVO.setDesc(giftInfo.getName());
            goodsInfoVO.setCoin(new BigDecimal(giftInfo.getPrice()));
            return goodsInfoVO;
        }catch (Exception e){
            log.error("getGiftFromRpc id: {}", id, e);
            return null;
        }
    }
}
