package com.kugou.fanxing.recharge.service.bi;

import com.google.common.collect.Lists;
import com.kugou.fanxing.commons.lock.DLock;
import com.kugou.fanxing.commons.lock.DLockFactory;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.DatabusDataDao;
import com.kugou.fanxing.recharge.model.po.DatabusDataPo;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import com.kugou.fanxing.recharge.util.ParseUtils;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.plexus.util.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 调用BI数据总线服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DatabusService {

    @Autowired
    private DatabusDataDao databusDataDao;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private OrderIdService orderIdService;
    @Autowired
    private DLockFactory dLockFactory;

    public boolean isAirwallexAllowKugouId(long kugouId) {
        if (!this.apolloConfigService.enableAirwallexBiData()) {
            log.warn("BI数据总线服务，暂不读取BI数据。kugouId: {}", kugouId);
            return false;
        }
        Optional<DatabusDataPo> optionalDatabusDataPo = Optional.ofNullable(this.databusDataDao.query(kugouId % 10, 468, kugouId));
        return optionalDatabusDataPo.isPresent();
    }

    public boolean fetchData(long dataId, String dt, String databusUrl) {
        String lockKey = StringUtils.joinWith("", dataId, dt);
        try (DLock lock = dLockFactory.getLock(lockKey)) {
            if (!lock.acquire(1)) {
                log.warn("BI数据总线服务，其他机器执行中。dataId: {}, dt: {}, databusUrl: {}", dataId, dt, databusUrl);
                return false;
            }
            // eg.work/data/databus-468-2022-08-18.txt
            String filename = StringUtils.joinWith("-", "databus", dataId, dt).concat(".txt");
            String filepath = "work/data/" + filename;
            log.warn("BI数据总线服务，开始处理数据文件。dataId: {}, dt: {}, databusUrl: {}, filepath: {}", dataId, dt, databusUrl, filepath);
            if (!FileUtils.fileExists(filepath)) {
                boolean result = HttpClientUtils.download(databusUrl, filepath);
                if (!result) {
                    log.warn("BI数据总线服务，下载数据文件失败。filepath: {}", filepath);
                    return false;
                }
            }
            processDataFile(dataId, dt, filepath);
            return true;
        } catch (Exception e) {
            log.error("BI数据总线服务，处理数据文件异常。dataId: {}, dt: {}, databusUrl: {}", dataId, dt, databusUrl, e);
        }
        return false;
    }

    @SneakyThrows
    private void processDataFile(long dataId, String dt, String filepath) {
        if (!FileUtils.fileExists(filepath)) {
            log.warn("BI数据总线服务，数据文件不存在。filepath: {}", filepath);
            return;
        }
        int persistThreshold = this.apolloConfigService.getDatabusPersistThreshold();
        try (Scanner scanner = new Scanner(new File(filepath))) {
            Set<Data468> dataSet = new HashSet<>(300);
            while (scanner.hasNext()) {
                String line = StringUtils.trim(scanner.nextLine());
                String[] columns = StringUtils.split(line, "\t");
                if (columns.length < 2) {
                    log.warn("BI数据总线服务，数据行解析失败。line: {}", line);
                    continue;
                }
                long kugouId = ParseUtils.tryParseLong(columns[0], 0);
                String opType = columns[1];
                if (kugouId < 1 || !Lists.newArrayList("add", "del").contains(opType)) {
                    log.warn("BI数据总线服务，数据行解析失败。line: {}", scanner.next());
                    continue;
                }
                Data468 data468 = Data468.builder()
                        .dataId(dataId)
                        .kugouId(kugouId)
                        .opType(opType)
                        .build();
                dataSet.add(data468);
                if (dataSet.size() >= persistThreshold || !scanner.hasNext()) {
                    persist(dataId, dt, dataSet);
                }
            }
        }
        boolean enableClearDatabusFile = this.apolloConfigService.enableClearDatabusFile();
        if (FileUtils.fileExists(filepath) && enableClearDatabusFile) {
            FileUtils.forceDelete(filepath);
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    private static class Data468 {
        private long dataId;
        private long kugouId;
        private String opType;
    }

    private void persist(long dataId, String dt, Set<Data468> dataSet) {
        Map<Long, List<Long>> addGroupKugouId = dataSet.stream()
                .filter(data468 -> data468.getOpType().equalsIgnoreCase("add"))
                .map(Data468::getKugouId)
                .collect(Collectors.groupingBy(kugouId -> kugouId % 10));
        Map<Long, List<Long>> delGroupKugouId = dataSet.stream()
                .filter(data468 -> data468.getOpType().equalsIgnoreCase("del"))
                .map(Data468::getKugouId)
                .collect(Collectors.groupingBy(kugouId -> kugouId % 10));
        addGroupKugouId.forEach((index, kugouIds) -> {
            List<DatabusDataPo> databusDataPos = kugouIds.stream()
                    .map(kugouId -> DatabusDataPo.builder()
                            .globalId(orderIdService.generateGlobalId())
                            .dataId(dataId)
                            .kugouId(kugouId)
                            .extraJson("")
                            .createTime(new Date())
                            .updateTime(new Date())
                            .build())
                    .collect(Collectors.toList());
            int affected = databusDataDao.batchInsert(index, databusDataPos);
            log.warn("BI数据总线服务，数据新增处理完毕。dataId: {}, dt: {}, index: {}, affected: {}, kugouIds: {}",
                    dataId, dt, index, affected, kugouIds);
        });
        delGroupKugouId.forEach((index, kugouIds) -> {
            int affected = databusDataDao.batchDelete(index, dataId, kugouIds);
            log.warn("BI数据总线服务，数据删除处理完毕。dataId: {}, dt: {}, index: {}, affected: {}, kugouIds: {}",
                    dataId, dt, index, affected, kugouIds);
        });
        dataSet.clear();
    }
}
