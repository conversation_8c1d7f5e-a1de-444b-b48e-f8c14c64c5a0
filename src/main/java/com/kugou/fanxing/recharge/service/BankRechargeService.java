package com.kugou.fanxing.recharge.service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.bo.CouponInfoBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.BankMobileRequest;
import com.kugou.fanxing.recharge.model.request.BankRequest;
import com.kugou.fanxing.recharge.model.request.GetOrderRequest;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class BankRechargeService extends AbstractRechargeService {

    @Autowired
    private RechargeConfig rechargeConfig;
    @Autowired
    private RechargeCouponService rechargeCouponService;
    @Autowired
    private RechargeAcrossDao rechargeAcrossDao;

    public Map<String, Object> getOrderForBank(WebCommonParam webCommonParam, BankRequest bankRequest) {
        long kugouId = webCommonParam.getKugouId();
        int pid = webCommonParam.getPid();
        BigDecimal amount = bankRequest.getAmount();
        long couponId = bankRequest.getCouponId();
        PayTypeIdEnum payTypeIdEnum = bankRequest.getPayTypeIdEnum();
        // 解析订单月份
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        Optional<String> optionalMonth = orderIdService.getYearMonthFromRechargeOrderNum(rechargeOrderNum);
        if (!optionalMonth.isPresent()) {
            log.warn("网页银行卡充值下单接口，解析订单月份失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        // 处理外部检查
        SysResultCode resultCode = this.rechargeCommonService.checkThirdPart(rechargeOrderNum, webCommonParam, bankRequest);
        if (!resultCode.isSuccess()) {
            log.warn("网页银行卡充值下单接口，处理外部检查失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(resultCode);
        }
        resultCode = this.agentRechargeService.checkAgentRecharge(webCommonParam, bankRequest.getRechargeKugouId(), bankRequest.getCouponId());
        if (!resultCode.isSuccess()) {
            log.warn("网页银行卡充值下单接口V2，处理代充检查失败。webCommonParam: {}, bankRequest: {}", webCommonParam, bankRequest);
            throw new BizException(resultCode);
        }
        // 处理代金券
        CoinTypeEnum coinTypeEnum = CoinTypeEnum.of(bankRequest.getCoinType());
        Optional<CouponInfoBO> optionalCouponInfoBO = this.rechargeCouponService.handleCouponForOrder(kugouId, couponId, amount, coinTypeEnum);
        try {
            // 保存充值订单
            RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(pid, kugouId, 0,
                    rechargeOrderNum, payTypeIdEnum, amount, webCommonParam.getIp(), coinTypeEnum, optionalCouponInfoBO);
            String month = optionalMonth.get();
            int affected = rechargeAcrossDao.add(month, rechargeAcrossPO);
            if (affected < 1) {
                log.warn("网页银行卡充值下单接口，保存订单失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            // 生成充值链接
            Map<String, String> extParam = this.createRechargeExParam(bankRequest);
            Map<String, Object> extendParam = Maps.newHashMap();
            extendParam.put("businessExt", StringUtils.defaultString(webCommonParam.getExt()));
            Map<String, Object> dataMap = this.rechargeCommonService.createRechargeResultAlipayPC(rechargeAcrossPO,webCommonParam.getIp(), extParam, extendParam);
            if (dataMap.isEmpty()) {
                log.warn("网页银行卡充值下单接口，生成链接失败。rechargeAcrossPO: {}", rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            return dataMap;
        } catch (Exception e) {
            log.error("网页银行卡充值下单接口，创建订单异常。webCommonParam: {}, bankRequest: {}", webCommonParam, bankRequest, e);
            optionalCouponInfoBO.ifPresent(couponInfoBO -> this.rechargeCouponService.unFreezeCouponQuietly(kugouId, rechargeOrderNum, couponInfoBO));
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    public Map<String, String> createRechargeExParam(GetOrderRequest request) {
        Map<String, String> extParam = Maps.newHashMap();
        if(request instanceof BankRequest) {
            BankRequest bankRequest = (BankRequest) request;
            String bankCode = this.rechargeConfig.getBankCodeByAlipayBankId(bankRequest.getAlipayBankId());
            extParam.put("defaultbank", bankCode);
            return extParam;
        }
        return extParam;
    }

    public Map<String, Object> createRechargeExtendParam(WebCommonParam webCommonParam) {
        String businessExt = webCommonParam.getExt();
        Map<String, Object> extendParam = Maps.newHashMap();
        extendParam.put("businessExt", StringUtils.defaultString(businessExt));
        return extendParam;
    }

    public Map<String, Object> getBankMobile(WebCommonParam webCommonParam, BankMobileRequest wechatRequest) {
        long kugouId = webCommonParam.getKugouId();
        int pid = webCommonParam.getPid();
        BigDecimal amount = wechatRequest.getAmount();
        long couponId = wechatRequest.getCouponId();
        PayTypeIdEnum payTypeIdEnum = wechatRequest.getPayTypeIdEnum();
        // 解析订单月份
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
        // 处理外部检查
        SysResultCode resultCode = this.rechargeCommonService.checkPreconditionForGetOrder(rechargeOrderNum, webCommonParam, wechatRequest);
        if (!resultCode.isSuccess()) {
            log.warn("创建支付宝订单失败，处理外部检查失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(resultCode);
        }
        // 处理代金券
        CoinTypeEnum coinTypeEnum = CoinTypeEnum.of(wechatRequest.getCoinType());
        Optional<CouponInfoBO> optionalCouponInfoBO = Optional.empty();
        try {
            optionalCouponInfoBO = this.rechargeCouponService.handleCouponForOrder(kugouId, couponId, amount, coinTypeEnum);
            RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(pid, kugouId, 0,
                    rechargeOrderNum, payTypeIdEnum, amount, webCommonParam.getIp(), coinTypeEnum, optionalCouponInfoBO);
            int affected = rechargeAcrossDao.add(month, rechargeAcrossPO);
            if (affected < 1) {
                log.warn("创建支付宝订单失败，保存订单失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            // 生成充值链接
            Map<String, String> extParam = Maps.newHashMap();
            extParam.put("cardType", wechatRequest.getCardType());
            Map<String, Object> extendParam = Maps.newHashMap();
            extendParam.put("businessExt", StringUtils.defaultString(webCommonParam.getExt()));
            Map<String, Object> dataMap = this.rechargeCommonService.createRechargeResultForMobileByPost(rechargeAcrossPO, extParam, extendParam);
            if (dataMap.isEmpty()) {
                log.warn("创建支付宝订单失败，生成链接失败。rechargeAcrossPO: {}", rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            dataMap.put("orderInfo", ImmutableMap.of("rechargeOrderNum", rechargeOrderNum));
            return dataMap;
        } catch (Exception e) {
            log.error("创建支付宝订单失败，生成链接异常。webCommonParam: {}, wechatRequest: {}", webCommonParam, wechatRequest, e);
            optionalCouponInfoBO.ifPresent(couponInfoBO -> this.rechargeCouponService.unFreezeCouponQuietly(kugouId, rechargeOrderNum, couponInfoBO));
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    public Map<String, Object> getOrderForBankV2(WebCommonParam webCommonParam, BankRequest bankRequest) {
        long kugouId = webCommonParam.getKugouId();
        long rechargeKugouId = bankRequest.getRechargeKugouId();
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        SysResultCode resultCode = this.rechargeCommonService.checkPreconditionForGetOrder(rechargeOrderNum, webCommonParam, bankRequest);
        if (!resultCode.isSuccess()) {
            log.warn("网页银行卡充值下单接口V2，处理前置检查失败。webCommonParam: {}, bankRequest: {}", webCommonParam, bankRequest);
            throw new BizException(resultCode);
        }
        resultCode = this.agentRechargeService.checkAgentRecharge(webCommonParam, bankRequest.getRechargeKugouId(), bankRequest.getCouponId());
        if (!resultCode.isSuccess()) {
            log.warn("网页银行卡充值下单接口V2，处理代充检查失败。webCommonParam: {}, bankRequest: {}", webCommonParam, bankRequest);
            throw new BizException(resultCode);
        }
        CoinTypeEnum coinTypeEnum = CoinTypeEnum.of(bankRequest.getCoinType());
        Optional<CouponInfoBO> optionalCouponInfoBO = this.rechargeCouponService.handleCouponForOrder(kugouId, bankRequest.getCouponId(), bankRequest.getAmount(), coinTypeEnum);
        try {
            RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(webCommonParam.getPid(), kugouId, rechargeKugouId, rechargeOrderNum, bankRequest.getPayTypeIdEnum(), bankRequest.getAmount(), webCommonParam.getIp(),coinTypeEnum, optionalCouponInfoBO);
            String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
            int affected = rechargeAcrossDao.add(month, rechargeAcrossPO);
            if (affected < 1) {
                log.warn("网页银行卡充值下单接口V2，保存订单失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            // 生成充值链接
            Map<String, String> kupaysParam = this.createRechargeExParam(bankRequest);
            Map<String, Object> extendParam = this.createRechargeExtendParam(webCommonParam);
            Map<String, Object> dataMap = kupayService.alipayV2(rechargeAcrossPO, webCommonParam.getIp(), extendParam, kupaysParam);
            if (dataMap.isEmpty()) {
                log.warn("网页银行卡充值下单接口V2，生成链接失败。rechargeAcrossPO: {}", rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            return dataMap;
        } catch (Exception e) {
            log.error("网页银行卡充值下单接口V2，创建订单异常。webCommonParam: {}, bankRequest: {}", webCommonParam, bankRequest, e);
            optionalCouponInfoBO.ifPresent(couponInfoBO -> this.rechargeCouponService.unFreezeCouponQuietly(webCommonParam.getKugouId(), rechargeOrderNum, couponInfoBO));
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

}
