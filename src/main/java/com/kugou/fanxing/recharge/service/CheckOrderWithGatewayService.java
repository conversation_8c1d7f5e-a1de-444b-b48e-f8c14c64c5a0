package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.GatewayPaySuccessRecordDao;
import com.kugou.fanxing.recharge.model.po.GatewayPayRecordPo;
import com.kugou.fanxing.recharge.model.vo.GatewayPayRecordVo;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.fanxing.recharge.util.ModelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CheckOrderWithGatewayService {

    @Autowired
    private GatewayPaymentBasicService gatewayPaymentBasicService;

    @Autowired
    private ApolloConfigService apolloConfigService;

    @Autowired
    private GatewayPaySuccessRecordDao gatewayPaySuccessRecordDao;

    public String checkOrderWithGateway(long startTime, long endTime) {
        return checkOrderWithGateway(startTime, endTime, apolloConfigService.getCheckOrderWithGatewaySecondsPerTime());
    }

    public String checkOrderWithGateway(long startTime, long endTime, long secondsPerTime) {
        //网关最大只支持同时跑60秒
        int maxPerTime = apolloConfigService.getCheckOrderWithGatewaySecondsPerTime();
        if (secondsPerTime > maxPerTime) {
            secondsPerTime = maxPerTime;
        }
        if ((endTime - startTime) > 86400 * apolloConfigService.getCheckOrderWithGatewayMaxDays()) {
            return String.format("每次执行对账任务，对账时间范围不能超过%d天", apolloConfigService.getCheckOrderWithGatewayMaxDays());
        }
        long handleTime;
        int totalNum = 0;
        while (startTime <= endTime) {
            handleTime = startTime + secondsPerTime;
            if (handleTime > endTime) {
                handleTime = endTime;
            }
            int foundNum = doCheck(startTime, handleTime);
            totalNum += foundNum;
            startTime = handleTime + 1;
            sleepCanBeDownGrade();
        }
        return String.format("共扫描了%d条记录", totalNum);
    }

    private void sleepCanBeDownGrade() {
        try {
            Thread.sleep(500);
        } catch (Exception e) {
            log.error("sleep fail:", e);
        }
    }

    private int doCheck(long startTime, long endTime) {
        int foundNum = 0;
        List<GatewayPayRecordVo> gatewayPayRecordVos = gatewayPaymentBasicService.getSuccessOrders(startTime, endTime);
        if (CollectionUtils.isEmpty(gatewayPayRecordVos)) {
            return foundNum;
        }
        List<GatewayPayRecordPo> gatewayPayRecordPoList = gatewayPayRecordVos.stream()
                .filter(gatewayPayRecordVo -> gatewayPayRecordVo.getTradeStatus() == 1)
                .filter(gatewayPayRecordVo -> gatewayPayRecordVo.getCallbackStatus() == 1)
                .map(gatewayPayRecordVo -> {
                    GatewayPayRecordPo gatewayPayRecordPo = ModelUtils.fromUnchecked(gatewayPayRecordVo, GatewayPayRecordPo.class);
                    String extraData3 = gatewayPayRecordVo.getExtraData3();
                    String thirdUid = StringUtils.isNotBlank(extraData3) ? JsonUtils.parseJsonPath(extraData3,
                            "$.third_uid", String.class, "") : "";
                    gatewayPayRecordPo.setThirdUid(thirdUid);
                    gatewayPayRecordPo.setAddTime(new Date());
                    return gatewayPayRecordPo;
                })
                .collect(Collectors.toList());
        foundNum = gatewayPayRecordPoList.size();
        Map<String, List<GatewayPayRecordPo>> divideByMonth = gatewayPayRecordPoList.stream()
                .collect(Collectors.groupingBy(gatewayPayRecordVo -> DateHelper.formatYearMonth(gatewayPayRecordVo.getPayTime())));
        for (Map.Entry<String, List<GatewayPayRecordPo>> entry : divideByMonth.entrySet()) {
            recordToDb(entry.getKey(), entry.getValue());
        }
        return foundNum;
    }

    private void recordToDb(String month, List<GatewayPayRecordPo> gatewayPayRecordPoList) {
        gatewayPaySuccessRecordDao.batchInsert(month, gatewayPayRecordPoList);
    }

}
