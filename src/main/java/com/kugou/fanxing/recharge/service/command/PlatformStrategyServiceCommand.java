package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.risk.sdk.thrift.strategy.service.PlatformStrategyService;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.StrategyResult;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.StrategyVO;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

/**
 * 风控服务
 *
 * <AUTHOR>
 */
@Slf4j
public class PlatformStrategyServiceCommand extends HystrixCommand<StrategyResult> {

    private final PlatformStrategyService.Iface platformStrategyService;
    private final StrategyVO strategyVO;

    public PlatformStrategyServiceCommand(final PlatformStrategyService.Iface platformStrategyService, final StrategyVO strategyVO) {
        super(HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("PlatformStrategyServiceCommand")).
                andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(500)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(30))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(350)));
        this.platformStrategyService = platformStrategyService;
        this.strategyVO = strategyVO;
    }

    @Override
    protected StrategyResult run() throws Exception {
        return platformStrategyService.conclude(strategyVO);
    }

    @Override
    protected StrategyResult getFallback() {
        StrategyResult strategyResult = new StrategyResult().setRecode(0).setMessage("");
        log.warn("StrategyServiceCommand服务降级! 风控服务校验异常, strategyVO: {}, 降级返回数据: {}, 降级原因: {}",
                strategyVO, strategyResult, ExceptionUtils.getStackTrace(getExecutionException()));
        return strategyResult;
    }
}
