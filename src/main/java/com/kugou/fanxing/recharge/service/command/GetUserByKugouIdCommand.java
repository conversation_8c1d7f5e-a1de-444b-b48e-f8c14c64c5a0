package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.userbaseinfo.service.UserModuleV2BizService;
import com.kugou.fanxing.userbaseinfo.vo.UserResponse;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Optional;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@Slf4j
public class GetUserByKugouIdCommand extends HystrixCommand<Optional<UserResponse>> {

    private final UserModuleV2BizService.Iface userModuleV2BizService;
    private final long kugouId;

    public GetUserByKugouIdCommand(final UserModuleV2BizService.Iface userModuleV2BizService, final long kugouId) {
        super(HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("GetUserByKugouIdCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(500)));
        this.userModuleV2BizService = userModuleV2BizService;
        this.kugouId = kugouId;
    }

    @Override
    protected Optional<UserResponse> run() throws Exception {
        return Optional.of(this.userModuleV2BizService.getUserByKugouId(kugouId));
    }

    @Override
    protected Optional<UserResponse> getFallback() {
        Optional<UserResponse> fallback = Optional.empty();
        log.warn("GetUserByKugouIdCommand 服务降级! 通过酷狗ID查询用户信息出错, kugouId: {}! 降级返回数据: {}, 降级原因: {}",
                kugouId, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }
}
