package com.kugou.fanxing.recharge.service.withdraw;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AlipayCancelDrawCashResp {
    private int status;
    private int error_code;
    private String error_msg;

    /**
     * 是否取消提现成功
     *
     * @return 是否提现成功
     */
    public boolean isCancelSuccess() {
        return 1 == this.getStatus() && 0 == this.getError_code();
    }

    /**
     * 是否禁止取消提现
     *
     * @return 是否禁止取消提现
     */
    public boolean isCancelForbidden() {
        return 0 == this.getStatus() && 30970 == this.getError_code();
    }
}
