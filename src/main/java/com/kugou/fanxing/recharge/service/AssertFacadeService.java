package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.platform.asset.service.thrift.AssetService;
import com.kugou.fanxing.platform.asset.service.thrift.IncrementRequest;
import com.kugou.fanxing.platform.asset.service.thrift.IncrementResponse;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Slf4j
@Service
public class AssertFacadeService {

    @Autowired
    private AssetService.Iface assetService;

    @SneakyThrows
    public void addCoin(RechargeAcrossPO rechargeAcrossPO) {
        IncrementRequest incrementRequest = buildIncrementRequest(rechargeAcrossPO.getKugouId(), rechargeAcrossPO.getRechargeOrderNum(), rechargeAcrossPO.getCoin());
        CommonParameter commonParameter = buildCommonParameter(rechargeAcrossPO.getCFrom());
        IncrementResponse response = this.assetService.recharge(incrementRequest, commonParameter);
        if (!isSuccessIncrementResponse(response)) {
            log.error("调用虚拟资产服务，充值虚拟货币失败。commonParameter: {}, incrementRequest: {}, response: {}", commonParameter, incrementRequest, response);
            throw new AckException(SysResultCode.E_10000036).addContextValue("rechargeOrderNum", rechargeAcrossPO.getRechargeOrderNum());
        }
        log.warn("调用虚拟资产服务，充值虚拟货币成功。commonParameter: {}, incrementRequest: {}, response: {}", commonParameter, incrementRequest, response);
    }

    private boolean isSuccessIncrementResponse(IncrementResponse response) {
        return response.getCode() == 0 && StringUtils.isNotBlank(response.getOrderId());
    }

    private CommonParameter buildCommonParameter(int pid) {
        CommonParameter commonParameter = new CommonParameter();
        commonParameter.setPid(pid);
        commonParameter.setSource(1);
        commonParameter.setVersion("1");
        commonParameter.setClientIp("127.0.0.1");
        commonParameter.setAppId("1010");
        commonParameter.setToken("");
        return commonParameter;
    }

    private IncrementRequest buildIncrementRequest(long kugouId, String rechargeOrderNum, BigDecimal coin) {
        IncrementRequest request = new IncrementRequest();
        request.setOrderId(rechargeOrderNum);
        request.setKugouId(kugouId);
        request.setTimestamp(DateHelper.getCurrentSeconds());
        request.setBusinessId(2000000227);
        request.setAssetId(2000000218);
        request.setAssetType(61);
        request.setNum(coin.stripTrailingZeros().toPlainString());
        request.setExt("");
        request.setSign(FinanceSignUtils.makeSign(request, "n6t87p38fc"));
        return request;
    }
}
