package com.kugou.fanxing.recharge.service.command;

import com.google.common.collect.Maps;
import com.kugou.kw.idservice.api.struct.BatchIdResponse;
import com.kugou.kw.idservice.api.struct.IdInfo;
import com.kugou.kw.idservice.api.struct.KuwoIdMappingService;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static java.util.stream.Collectors.toMap;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@Slf4j
public class BatchGetIdInfoByKugouIdCommand extends HystrixCommand<Map<Long, Long>> {

    private final KuwoIdMappingService.Iface kuwoIdMappingService;
    private final List<Long> kugouIdList;

    public BatchGetIdInfoByKugouIdCommand(final KuwoIdMappingService.Iface kuwoIdMappingService, final List<Long> kugouIdList) {
        super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("BatchGetIdInfoByKugouIdCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(300)));
        this.kuwoIdMappingService = kuwoIdMappingService;
        this.kugouIdList = kugouIdList;
    }

    @Override
    protected Map<Long, Long> run() throws Exception {
        BatchIdResponse response = this.kuwoIdMappingService.batchGetIdInfoByKugouId(kugouIdList);
        if (Objects.isNull(response) || response.getCode() != 0 || Objects.isNull(response.getData())) {
            log.error("批量酷狗ID转酷我ID，转换失败。kugouIdList: {}, response: {}", kugouIdList, response);
            return Maps.newHashMap();
        }
        List<IdInfo> idInfoList = response.getData();
        return idInfoList.stream().collect(toMap(IdInfo::getKugouId, IdInfo::getKuwoId, (a1, a2) -> a2));
    }

    @Override
    protected Map<Long, Long> getFallback() {
        Map<Long, Long> fallback = Maps.newHashMap();
        log.warn("BatchGetIdInfoByKugouIdCommand 服务降级! 通过酷狗id查询酷我id出错, kugouIdList: {}! 降级返回数据: {}, 降级原因: {}",
                kugouIdList, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }
}
