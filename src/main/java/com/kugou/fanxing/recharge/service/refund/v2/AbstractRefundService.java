package com.kugou.fanxing.recharge.service.refund.v2;

import com.kugou.fanxing.recharge.alert.AlerterFacade;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundDebtV2Dao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundDeductOrderV2Dao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundOrderV2Dao;
import com.kugou.fanxing.recharge.model.dto.RefundNotifyDto;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.po.RefundDebtV2Po;
import com.kugou.fanxing.recharge.model.po.RefundOrderV2Po;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Optional;

@Slf4j
public abstract class AbstractRefundService implements RefundService {

    @Autowired
    protected OrderIdService orderIdService;
    @Autowired
    protected RefundOrderV2Dao refundOrderV2Dao;
    @Autowired
    protected RefundDebtV2Dao refundDebtV2Dao;
    @Autowired
    protected RefundDeductOrderV2Dao refundDeductOrderV2Dao;
    @Autowired
    protected AlerterFacade alerterFacade;

    @Override
    public <T extends RefundNotifyDto> boolean receiveNotify(T t) {
        log.warn("处理退款通知，退款通知参数。refundNotifyDto: {}", t);
        Optional<RefundOrderV2Po> optionalRefundOrderV2Po = convertRefundNotifyDtoToRefundOrder(t);
        optionalRefundOrderV2Po.ifPresent(refundOrderV2Po -> {
            String outTradeNo = refundOrderV2Po.getOutTradeNo();
            Optional<RefundOrderV2Po> optionalRefund = Optional.ofNullable(refundOrderV2Dao.findByOutTradeNo(outTradeNo));
            if (!optionalRefund.isPresent()) {
                int affected = refundOrderV2Dao.insertIgnore(refundOrderV2Po);
                log.warn("处理退款通知，保存退款订单。affected: {}, refundOrderV2Po: {}", affected, refundOrderV2Po);
            }
        });
        return true;
    }

    protected abstract <T extends RefundNotifyDto> Optional<RefundOrderV2Po> convertRefundNotifyDtoToRefundOrder(T t);

    @Override
    @Transactional(transactionManager = "transactionManager_d_fanxing_recharge", propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean associateOriginOrder(RefundOrderV2Po refundOrderV2Po, Optional<RechargeAcrossPO> optionalRechargeAcrossPO) {
        if (!isValidRefundOrder(refundOrderV2Po) || !optionalRechargeAcrossPO.isPresent()) {
            refundOrderV2Po.setStatus(2);
            refundOrderV2Po.setErrorReason("关联原始交易不存在或参数非法");
            refundOrderV2Po.setUpdateTime(new Date());
            int affected = this.refundOrderV2Dao.updateFailureStatus(refundOrderV2Po);
            log.warn("关联原始交易，关联原始交易不存在或参数非法。affected: {}", affected);
            return false;
        }
        RechargeAcrossPO rechargeAcrossPO = optionalRechargeAcrossPO.get();
        refundOrderV2Po.setStatus(1);
        refundOrderV2Po.setErrorReason("关联原始交易成功");
        refundOrderV2Po.setKugouId(rechargeAcrossPO.getKugouId());
        refundOrderV2Po.setRechargeOrderNum(rechargeAcrossPO.getRechargeOrderNum());
        refundOrderV2Po.setCoin(rechargeAcrossPO.getCoin());
        refundOrderV2Po.setDebtCoin(calculateDebtCoin(refundOrderV2Po, rechargeAcrossPO));
        refundOrderV2Po.setUpdateTime(new Date());
        RefundDebtV2Po refundDebtV2Po = RefundDebtV2Po.builder()
                .kugouId(refundOrderV2Po.getKugouId())
                .debtCoin(refundOrderV2Po.getDebtCoin())
                .updateTime(new Date())
                .createTime(new Date())
                .build();
        int affected = this.refundOrderV2Dao.updateSuccessStatus(refundOrderV2Po);
        if (affected > 0) {
            affected = this.refundDebtV2Dao.insertUpdate(refundDebtV2Po);
            if (affected < 1) {
                throw new ContextedRuntimeException("更新用户欠费余额表失败。")
                        .addContextValue("outTradeNo", refundOrderV2Po.getOutTradeNo())
                        .addContextValue("tradeNo", refundOrderV2Po.getTradeNo())
                        .addContextValue("rechargeOrderNum", refundOrderV2Po.getRechargeOrderNum());
            }
        }
        log.warn("关联原始交易，关联成功。refundOrderV2Po: {}", refundOrderV2Po);
        return true;
    }

    private boolean isValidRefundOrder(RefundOrderV2Po refundOrderV2Po) {
        return refundOrderV2Po.getOriginOrderFee().compareTo(BigDecimal.ZERO) > 0
                && refundOrderV2Po.getActualRefundFee().compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 计算欠费星币
     */
    private BigDecimal calculateDebtCoin(RefundOrderV2Po refundOrderV2Po, RechargeAcrossPO rechargeAcrossPO) {
        BigDecimal originOrderFee = refundOrderV2Po.getOriginOrderFee();
        BigDecimal actualRefundFee = refundOrderV2Po.getActualRefundFee();
        BigDecimal coin = rechargeAcrossPO.getCoin();
        BigDecimal debtCoin = actualRefundFee.divide(originOrderFee, 2, RoundingMode.HALF_UP).multiply(coin).setScale(0, RoundingMode.HALF_UP);
        log.warn("关联原始交易，计算欠费星币。kugouId: {}, rechargeOrderNum: {}, originOrderFee: {}, actualRefundFee: {}, coin: {}, debtCoin: {}",
                rechargeAcrossPO.getKugouId(), rechargeAcrossPO.getRechargeOrderNum(), originOrderFee, actualRefundFee, coin, debtCoin.stripTrailingZeros().toPlainString());
        return debtCoin;
    }



}
