package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.thrift.banaccount.dto.Result;
import com.kugou.fanxing.thrift.banaccount.service.BanAccountService;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Objects;

@Slf4j
public class IsBanAccountCommand extends HystrixCommand<Boolean> {

    private final BanAccountService.Iface banAccountService;
    private final long kugouId;

    public IsBanAccountCommand(final BanAccountService.Iface banAccountService, final long kugouId) {
        super(HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("IsBanAccountCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(300)));
        this.banAccountService = banAccountService;
        this.kugouId = kugouId;
    }

    @Override
    protected Boolean run() throws Exception {
        Result result = banAccountService.isBanAccount(kugouId);
        if (Objects.nonNull(result) && result.isResult()) {
            log.warn("调用用户的封禁状态接口, 调用成功且账号是封禁状态。kugouId: {}, result: {}", kugouId, result);
            return Boolean.TRUE;
        }
        log.warn("调用用户的封禁状态接口, 调用完成。kugouId: {}, result: {}", kugouId, result);
        return Boolean.FALSE;
    }

    @Override
    protected Boolean getFallback() {
        Boolean fallback = Boolean.FALSE;
        log.warn("IsBanAccountCommand 服务降级! 调用用户的封禁状态接口，降级处理。kugouId: {}! 降级返回数据: {}, 降级原因: {}",
                kugouId, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }
}
