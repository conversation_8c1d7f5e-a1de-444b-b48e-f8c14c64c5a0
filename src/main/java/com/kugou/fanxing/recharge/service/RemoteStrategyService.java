package com.kugou.fanxing.recharge.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.model.po.WithdrawOrderPO;
import com.kugou.fanxing.recharge.model.request.*;
import com.kugou.fanxing.recharge.service.command.PlatformStrategyServiceCommand;
import com.kugou.fanxing.recharge.service.command.RiskStrategyServiceConcludeCommand;
import com.kugou.fanxing.recharge.service.command.RiskStrategyServiceConcludePlainCommand;
import com.kugou.fanxing.recharge.service.withdraw.WithdrawClientParams;
import com.kugou.fanxing.recharge.util.IpUtils;
import com.kugou.fanxing.recharge.util.SpringContextUtils;
import com.kugou.fanxing.risk.sdk.RiskStrategyService;
import com.kugou.fanxing.risk.sdk.model.RiskResult;
import com.kugou.fanxing.risk.sdk.thrift.strategy.service.*;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 风控服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class RemoteStrategyService {

    private static final String BIZ_RECHARGE = "recharge";
    private static final String BIZ_RECHARGE_PAYPAL = "paypalRecharge";
    private static final String BIZ_RECHARGE_TMALL = "tmallRecharge";
    private static final String BIZ_GOOGLE_PAY_FINISH = "googlePlayFinish";
    private static final String BIZ_WITHDRAW = "withdraw";

    @Autowired
    private RiskStrategyService riskStrategyService;
    @Autowired
    private PlatformStrategyService.Iface platformStrategyService;

    public boolean strategyVerifyForGetOrder(long kugouId, String rechargeOrderNum, int payTypeId, BigDecimal amount) {
        try {
            JSONObject extJSON = new JSONObject();
            extJSON.put("payType", payTypeId);
            extJSON.put("amount", amount);
            StrategyVO strategyVO = buildStrategyVO(BIZ_RECHARGE, rechargeOrderNum, kugouId, "", extJSON);
            PlatformStrategyServiceCommand command = new PlatformStrategyServiceCommand(platformStrategyService, strategyVO);
            StrategyResult strategyResult = command.execute();
            log.warn("风控服务调用结果, strategyVO:{}, strategyResult:{}", strategyVO, strategyResult);
            return assertStrategy(strategyResult);
        } catch (Exception e) {
            log.error("风控服务调用异常", e);
        }
        return false;
    }

    public boolean strategyVerifyForWxMiniProgram(PayTypeIdEnum payTypeIdEnum, String rechargeOrderNum, long kugouId, String deviceId, WxMiniProgramRequest wxMiniProgramRequest) {
        try {
            JSONObject extJSON = new JSONObject();
            extJSON.put("payType", payTypeIdEnum.getPayTypeId());
            extJSON.put("openId", wxMiniProgramRequest.getOpenId());
            extJSON.put("money", wxMiniProgramRequest.getMoney());
            StrategyVO strategyVO = buildStrategyVO(BIZ_RECHARGE, rechargeOrderNum, kugouId, deviceId, extJSON);
            PlatformStrategyServiceCommand command = new PlatformStrategyServiceCommand(platformStrategyService, strategyVO);
            StrategyResult strategyResult = command.execute();
            log.warn("风控服务调用结果, strategyVO:{}, strategyResult:{}", strategyVO, strategyResult);
            if (strategyResult.getRecode() == 0 && strategyResult.getData() != null && strategyResult.getData().getMeasure() == Measure.FREEZE) {
                return true;
            }
        } catch (Exception e) {
            log.error("风控服务调用异常", e);
        }
        return false;
    }

    public boolean strategyVerifyForPayPal(String rechargeOrderNum, long kugouId, PayPalRequest payPalRequest) {
        try {
            JSONObject extJSON = new JSONObject();
            extJSON.put("amount", payPalRequest.getAmount());
            extJSON.put("refer", payPalRequest.getRefer());
            extJSON.put("payType", PayTypeIdEnum.PAY_TYPE_ID_520.getPayTypeId());
            StrategyVO strategyVO = buildStrategyVO(BIZ_RECHARGE_PAYPAL, rechargeOrderNum, kugouId, payPalRequest.getDevice(), extJSON);
            PlatformStrategyServiceCommand command = new PlatformStrategyServiceCommand(platformStrategyService, strategyVO);
            StrategyResult strategyResult = command.execute();
            log.warn("风控服务调用结果, strategyVO:{}, strategyResult:{}", strategyVO, strategyResult);
            return assertStrategy(strategyResult);
        } catch (Exception e) {
            log.error("风控服务调用异常, rechargeOrderNum:{}, kugouId:{}, payPalRequest:{}", rechargeOrderNum, kugouId, payPalRequest, e);
        }
        return false;
    }

    public boolean strategyVerifyForGpFinishOrder(String rechargeOrderNum, long kugouId, BigDecimal amount,WebCommonParam webCommonParam) {
        try {
            JSONObject extJSON = new JSONObject();
            extJSON.put("amount", amount);
            extJSON.put("payType", PayTypeIdEnum.PAY_TYPE_ID_521.getPayTypeId());
            StrategyVO strategyVO = buildStrategyVO(BIZ_GOOGLE_PAY_FINISH, rechargeOrderNum, kugouId, webCommonParam.getKfd(), extJSON);
            strategyVO.setPlatform(webCommonParam.getPlatform());
            PlatformStrategyServiceCommand command = new PlatformStrategyServiceCommand(platformStrategyService, strategyVO);
            StrategyResult strategyResult = command.execute();
            log.warn("风控服务调用结果, strategyVO:{}, strategyResult:{}", strategyVO, strategyResult);
            return assertStrategy(strategyResult);
        } catch (Exception e) {
            log.error("风控服务调用异常, rechargeOrderNum:{}, kugouId:{},", rechargeOrderNum, kugouId, e);
        }
        return false;
    }

    private boolean assertStrategy(StrategyResult strategyResult) {
        return strategyResult.getRecode() == 0 && strategyResult.getData() != null
                && strategyResult.getData().getMeasure() == Measure.FREEZE;
    }

    private StrategyVO buildStrategyVO(String biz, String rechargeOrderNum, long kugouId, String deviceId, JSONObject extJSON) {
        return new StrategyVO()
                .setBiz(biz)
                .setAppid("1010")
                .setEndtype(Strings.EMPTY)
                .setSid(rechargeOrderNum)
                .setKugouId(kugouId)
                .setIp(IpUtils.getClientIpAddress())
                .setDeviceId(StringUtils.defaultString(deviceId))
                .setTs(System.currentTimeMillis())
                .setData(extJSON.toJSONString());
    }

    /**
     * Android充值渠道列表风控校验
     *
     * @param kugouId 酷狗ID
     * @param payTypeIdList Android充值渠道列表
     * @return Android充值渠道列表
     */
    public List<Integer> strategyForPayTypeList(long kugouId, List<Integer> payTypeIdList, AndroidCommonParam androidCommonParam) {
        try {
            JSONObject extJSON = new JSONObject();
            StrategyVO strategyVO = new StrategyVO()
                    .setAppid(StringUtils.defaultString(androidCommonParam.getAppid()))
                    .setBiz("rechargeType")
                    .setDeviceId(StringUtils.defaultString(androidCommonParam.getDevice()))
                    .setEndtype(Strings.EMPTY)
                    .setTs(System.currentTimeMillis())
                    .setData(extJSON.toJSONString())
                    .setKugouId(kugouId)
                    .setIp(IpUtils.getClientIpAddress())
                    .setSid("0");
            PlatformStrategyServiceCommand command = new PlatformStrategyServiceCommand(platformStrategyService, strategyVO);
            Future<StrategyResult> strategyResultFuture = command.observe().toBlocking().toFuture();
            StrategyResult strategyResult = strategyResultFuture.get();
            log.warn("风控手机端充值渠道列表, strategyVO:{} result:{}", strategyVO, strategyResult);
            if (strategyResult.getRecode() == 0 && strategyResult.getData() != null && StringUtils.isNotBlank(strategyResult.getData().getBizRecode())) {
                Result result = strategyResult.getData();
                Set<Integer> riskPayTypeIdSet = JSON.parseObject(result.getBizRecode(), new TypeReference<Set<Integer>>() {});
                log.warn("风险手机端充值渠道列表, riskPayTypeIdSet: {}", riskPayTypeIdSet);
                return payTypeIdList.stream().filter(payTypeId -> !riskPayTypeIdSet.contains(payTypeId)).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("调用风控服务检查支付列表异常, kugouId:{}, payTypeIdList: {}", kugouId, payTypeIdList, e);
        }
        return payTypeIdList;
    }

    public boolean strategyVerifyForTmall(long kugouId, String cardId) {
        try {
            JSONObject extJSON = new JSONObject();
            extJSON.put("payType", PayTypeIdEnum.PAY_TYPE_ID_1012.getPayTypeId());
            extJSON.put("cardId", StringUtils.defaultString(cardId));
            StrategyVO strategyVO = new StrategyVO()
                    .setBiz(BIZ_RECHARGE_TMALL)
                    .setAppid("0")
                    .setEndtype(Strings.EMPTY)
                    .setSid(Strings.EMPTY)
                    .setKugouId(kugouId)
                    .setIp(Strings.EMPTY)
                    .setDeviceId(Strings.EMPTY)
                    .setTs(System.currentTimeMillis())
                    .setData(extJSON.toJSONString());
            StrategyResult strategyResult = new PlatformStrategyServiceCommand(platformStrategyService, strategyVO).execute();
            log.warn("天猫充值调用风控服务，风控服务调用结果。strategyVO:{}, strategyResult:{}", strategyVO, strategyResult);
            return assertStrategy(strategyResult);
        } catch (Exception e) {
            log.error("天猫充值调用风控服务，服务调用异常。kugouId:{}, cardId:{}", kugouId, cardId, e);
        }
        return false;
    }

    public boolean strategyVerifyForX(PayTypeIdEnum payTypeIdEnum, long kugouId, Map<String, String> params) {
        try {
            JSONObject extJSON = new JSONObject();
            extJSON.put("payType", payTypeIdEnum.getPayTypeId());
            extJSON.putAll(params);
            StrategyVO strategyVO = new StrategyVO()
                    .setBiz(BIZ_RECHARGE)
                    .setAppid("0")
                    .setEndtype(Strings.EMPTY)
                    .setSid(Strings.EMPTY)
                    .setKugouId(kugouId)
                    .setIp(Strings.EMPTY)
                    .setDeviceId(Strings.EMPTY)
                    .setTs(System.currentTimeMillis())
                    .setData(extJSON.toJSONString());
            StrategyResult strategyResult = new PlatformStrategyServiceCommand(platformStrategyService, strategyVO).execute();
            log.warn("充值调用风控服务，风控服务调用结果。strategyVO:{}, strategyResult:{}", strategyVO, strategyResult);
            return assertStrategy(strategyResult);
        } catch (Exception e) {
            log.error("充值调用风控服务，服务调用异常。payTypeIdEnum: {}, kugouId:{}, params:{}", payTypeIdEnum, kugouId, params, e);
        }
        return false;
    }

    public boolean strategyVerifyForWithdraw(WithdrawOrderPO withdrawOrderPO, WithdrawClientParams withdrawClientParams) {
        try {
            Map<String, Object> data = Maps.newHashMap();
            data.put("drawType", withdrawOrderPO.getDrawType());
            data.put("bizAppId", withdrawOrderPO.getBizAppId());
            data.put("totalAmount", withdrawOrderPO.getTotalAmount());
            RiskStrategyServiceConcludePlainCommand.RiskRequestDto riskRequestDto = RiskStrategyServiceConcludePlainCommand.RiskRequestDto.builder()
                    .appid("0")
                    .biz(BIZ_WITHDRAW)
                    .endtype("")
                    .sid(String.valueOf(withdrawOrderPO.getOrderId()))
                    .kugouId(withdrawOrderPO.getKugouId())
                    .ip(StringUtils.defaultString(withdrawClientParams.getClientip()))
                    .deviceId(StringUtils.defaultString(withdrawClientParams.getMid()))
                    .platform(0)
                    .roomId(0)
                    .clientver(0)
                    .clienttype(-1)
                    .ts(System.currentTimeMillis())
                    .data(data)
                    .build();
            Optional<RiskResult> optionalRiskResult = new RiskStrategyServiceConcludePlainCommand(riskStrategyService, riskRequestDto).execute();
            if (!optionalRiskResult.isPresent()) {
                log.warn("提现调用风控服务，响应为空。request: {}, optionalRiskResult: {}", riskRequestDto, optionalRiskResult);
                return false;
            }
            RiskResult riskResult = optionalRiskResult.get();
            log.warn("提现调用风控服务，请求与响应。request: {}, riskResult: {}", riskRequestDto, JSON.toJSONString(riskResult));
            return riskResult.getMeasure() == Measure.FREEZE;
        } catch (Exception e) {
            log.error("提现调用风控服务，服务调用异常。withdrawOrderPO:{}, withdrawClientParams:{}", withdrawOrderPO, withdrawClientParams, e);
            return false;
        }
    }

    public boolean forceAirwallexCard3DS(long kugouId, String rechargeOrderNum, AirwallexCreateRequest request) {
        Map<String, Object> paramsMap = Maps.newHashMap();
        paramsMap.put("kugouId", kugouId);
        paramsMap.put("rechargeOrderNum", rechargeOrderNum);
        paramsMap.put("payType", request.getPayTypeIdEnum().getPayTypeId());
        paramsMap.put("amount", request.getAmount());
        paramsMap.put("currency", request.getCurrency());
        paramsMap.put("currencyAmount", request.getCurrencyAmount());
        paramsMap.put("country", request.getCountry());
        HttpServletRequest httpServletRequest = SpringContextUtils.getHttpServletRequest();
        Optional<RiskResult> optionalRiskResult = new RiskStrategyServiceConcludeCommand(riskStrategyService, httpServletRequest, paramsMap).execute();
        log.warn("调用风控服务是否强制Airwallex卡支付3DS安全验证，请求响应。request: {}, paramsMap: {}, optionalRiskResult: {}",
                request, paramsMap, JSON.toJSONString(optionalRiskResult));
        if (!optionalRiskResult.isPresent()) {
            log.warn("调用风控服务是否强制Airwallex卡支付3DS安全验证，降级放行。rechargeOrderNum: {}", rechargeOrderNum);
            return false;
        }
        if ("force3DS".equalsIgnoreCase(optionalRiskResult.get().getBizRecode())) {
            log.warn("调用风控服务是否强制Airwallex卡支付3DS安全验证，强制验证。rechargeOrderNum: {}", rechargeOrderNum);
            return true;
        }
        return false;
    }
}