package com.kugou.fanxing.recharge.service.command;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kugou.mfx.activity.infiltrate.thrift.service.Coupon;
import com.kugou.mfx.activity.infiltrate.thrift.service.CouponListResult;
import com.kugou.mfx.activity.infiltrate.thrift.service.CouponListService;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 查询充值代金券配置信息
 *
 * <AUTHOR>
 */
@Slf4j
public class ListCouponCommand extends HystrixCommand<Map<Long, Coupon>> {

    private final CouponListService.Iface couponListService;

    public ListCouponCommand(final CouponListService.Iface couponListService) {
        super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("ListCouponCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(1000)));
        this.couponListService = couponListService;
    }

    @Override
    protected Map<Long, Coupon> run() throws Exception {
        log.warn("调用充值代金券配置查询接口");
        CouponListResult couponListResult = couponListService.listCoupon();
        if (Objects.isNull(couponListResult) || couponListResult.getCode() != 0) {
            log.warn("调用充值代金券配置查询接口, 调用失败, response: {}", couponListResult);
            return Maps.newHashMap();
        }
        List<Coupon> couponList = CollectionUtils.isNotEmpty(couponListResult.getData()) ? couponListResult.getData() : Lists.newArrayList();
        return couponList.stream()
                .filter(couponInfoVO -> couponInfoVO.getCategory() == 2)
                .collect(Collectors.toMap(Coupon::getId, Function.identity()));
    }

    @Override
    protected Map<Long, Coupon> getFallback() {
        final Map<Long, Coupon> fallback = Maps.newHashMap();
        log.warn("ListCouponCommand 服务降级! 调用充值代金券配置查询接口异常! 降级返回数据: {}, 降级原因: {}",
                fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }
}
