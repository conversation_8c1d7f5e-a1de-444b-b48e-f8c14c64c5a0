package com.kugou.fanxing.recharge.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.jayway.jsonpath.TypeRef;
import com.kugou.fanxing.biz.commons.util.GrayTools;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.exception.KupayException;
import com.kugou.fanxing.recharge.factory.PurchaseRequest;
import com.kugou.fanxing.recharge.model.ConfirmAirwallexResponse;
import com.kugou.fanxing.recharge.model.ConfirmAirwallexV1;
import com.kugou.fanxing.recharge.model.OrderAirwallexV1;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.bo.KupayAppTypeInfoBO;
import com.kugou.fanxing.recharge.model.bo.OfflinePayDataBO;
import com.kugou.fanxing.recharge.model.dto.*;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.vo.AlipayQrCodeUrlResponseObject;
import com.kugou.fanxing.recharge.model.vo.BankNameListDto;
import com.kugou.fanxing.recharge.model.vo.PaymentMethodTypesDto;
import com.kugou.fanxing.recharge.util.*;
import com.kugou.springcloud.http.KugouHttpRequest;
import com.kugou.springcloud.http.KugouHttpResponse;
import com.kugou.springcloud.http.template.KugouHttpTemplate;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;

@Slf4j
@Service
public class KupayService {

    private static final GrayTools getKupayAppInfoGrayForAll = GrayTools.module("getKupayInfoGrayForAll");

    @Value("${kupay.withdraw.serverId:1870}")
    private String serverId;
    @Value("${kupay.withdraw.serverKey:rYyNI4G7BdzzCYfXww3eHaW1U0haNcqE}")
    private String serverKey;
    @Autowired
    private RechargeConfig rechargeConfig;
    @Autowired
    private RechargeCommonService rechargeCommonService;

    public Map<String, String> commonUrlParams(String kupayAppId) {
        Map<String, String> params = Maps.newTreeMap();
        params.put("serverid", serverId);
        params.put("servertime", String.valueOf(DateHelper.getCurrentSeconds()));
        params.put("appid", kupayAppId);
        params.put("clientver", "-");
        params.put("mid", "-");
        params.put("uuid", "-");
        params.put("dfid", "-");
        return params;
    }

    public String getCommandSign(Map<String, String> urlParams, String toJSONString) {
        StringBuilder source = new StringBuilder(serverKey);
        urlParams.forEach((k, v) -> {
            source.append(k);
            source.append("=");
            source.append(v);
        });
        source.append(toJSONString);
        source.append(serverKey);
        log.warn("raw: {}", source);
        return DigestUtils.md5Hex(source.toString());
    }

    public String getCommandSign(Map<String, String> urlParams, Map<String, String> bodyParam) {
        StringBuilder source = new StringBuilder(serverKey);
        urlParams.forEach((k, v) -> {
            source.append(k);
            source.append("=");
            source.append(v);
        });
        source.append(buildSignParams(bodyParam));
        source.append(serverKey);
        log.warn("raw: {}", source);
        return DigestUtils.md5Hex(source.toString());
    }

    @SneakyThrows
    public static String buildSignParams(Map<String, String> params) {
        if (MapUtils.isEmpty(params)) {
            return null;
        }
        Iterator<Map.Entry<String, String>> iterator = params.entrySet().iterator();
        StringBuilder builder = new StringBuilder();
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            if (entry.getValue() != null) {
                builder.append(entry.getKey()).append("=").append(URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8.name()));
            }
            if (iterator.hasNext()) {
                builder.append("&");
            }
        }
        return builder.toString();
    }

    /**
     * http://doc.kugou.net/showdoc-master/web/#/8?page_id=9851
     */
    public Map<String, Object> alipayV2(RechargeAcrossPO rechargeAcrossPO, String clientIp, Map<String, Object> extendParam) {
        return this.alipayV2(rechargeAcrossPO, clientIp, extendParam, Maps.newHashMap());
    }

    public Map<String, Object> alipayV2(RechargeAcrossPO rechargeAcrossPO, String clientIp, Map<String, Object> extendParam, Map<String, String> kupaysParam) {
        PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
        KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(payTypeIdEnum);
        String kupayAppId = String.valueOf(kupayAppInfoPO.getKupayAppId());
        String url = this.rechargeConfig.getKupayIntranet().concat("/v2/alipay");
        String subject = this.rechargeCommonService.getSubject(rechargeAcrossPO.getCoinType());
        Map<String, String> bodyParams = Maps.newHashMap();
        bodyParams.put("biz_appid", kupayAppId);
        bodyParams.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
        bodyParams.put("clientip", clientIp);
        bodyParams.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
        bodyParams.put("subject", subject);
        bodyParams.put("desc", subject);
        bodyParams.put("total_fee", rechargeAcrossPO.getMoney().stripTrailingZeros().toPlainString());
        bodyParams.put("notify_url", rechargeConfig.getRechargeNotifyUrl(ReTypeEnum.RETYPE_RECHARGE));
        bodyParams.put("sync_url", rechargeConfig.getSyncUrl());
        bodyParams.put("expire_time", this.rechargeCommonService.getOrderExpireTime());
        bodyParams.put("extend", this.rechargeCommonService.buildExtendStr(rechargeAcrossPO, extendParam));
        bodyParams.putAll(kupaysParam);
        Map<String, String> urlParams = this.commonUrlParams(kupayAppId);
        urlParams.put("signature", getCommandSign(urlParams, JSON.toJSONString(bodyParams)));
        Optional<String> optionalJson = HttpClientUtils.doPostJsonBody(url, urlParams, JSON.toJSONString(bodyParams));
        log.warn("调用酷狗支付网关接口，请求结束。url: {}, urlParams: {}, bodyParams: {}, optionalJson: {}", url, urlParams, bodyParams, optionalJson);
        String jsonContent = optionalJson.orElse("");
        if (!isKupaySuccess(jsonContent)) {
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        Map<String, Object> dataMap = JsonUtils.parseJsonPathMap(jsonContent, "$.data", new TypeRef<Map<String, Object>>() {
        });
        dataMap.put("rechargeOrderNum", rechargeAcrossPO.getRechargeOrderNum());
        return dataMap;
    }

    /**
     * 支付宝二维码支付
     * @see <a href="http://doc.kugou.net/showdoc-master/web/#/8?page_id=2034">支付宝二维码支付</a>
     */
    public Map<String, Object> alipayqrcodeV1(RechargeAcrossPO rechargeAcrossPO, Map<String, Object> extendParam, Map<String, String> kupaysParam) {
        PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
        KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(payTypeIdEnum);
        if(getKupayAppInfoGrayForAll.isGray(rechargeAcrossPO.getKugouId())) {
            kupayAppInfoPO = rechargeConfig.getKupayAppInfoWithDefault(rechargeAcrossPO.getCFrom(),kupayAppInfoPO);
        }
        String kupayAppId = String.valueOf(kupayAppInfoPO.getKupayAppId());
        String url = this.rechargeConfig.getKupayIntranet().concat("/v1/alipayqrcode");
        String subject = this.rechargeCommonService.getSubject(rechargeAcrossPO.getCoinType());
        Map<String, String> urlParams = Maps.newHashMap();
        Map<String, String> bodyParams = Maps.newHashMap();
        bodyParams.put("appid", kupayAppId);
        bodyParams.put("time", String.valueOf(System.currentTimeMillis()));
        bodyParams.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
        bodyParams.put("subject", subject);
        bodyParams.put("desc", subject);
        bodyParams.put("total_fee", rechargeAcrossPO.getMoney().stripTrailingZeros().toPlainString());
        bodyParams.put("sign_type", rechargeConfig.getSignType());
        bodyParams.put("notify_url", rechargeConfig.getNotifyUrl(ReTypeEnum.of(rechargeAcrossPO.getReType())));
        bodyParams.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
        bodyParams.put("extend", this.rechargeCommonService.buildExtendStr(rechargeAcrossPO, extendParam));
        bodyParams.put("clientip", rechargeAcrossPO.getClientIp());
        bodyParams.put("qr", "1");
        bodyParams.put("pay_mode", "4");
        bodyParams.putAll(kupaysParam);
        bodyParams.put("sign", SignUtils.buildSign(bodyParams, kupayAppInfoPO.getKupayAppKey()));
        log.warn("调用酷狗支付网关接口，请求开始。url: {}, urlParams: {}, bodyParams: {}", url, urlParams, bodyParams);
        Optional<String> optionalJson = HttpClientUtils.doPostJSON(url, urlParams, bodyParams);
        log.warn("调用酷狗支付网关接口，请求结束。url: {}, urlParams: {}, bodyParams: {}, optionalJson: {}", url, urlParams, bodyParams, optionalJson);
        //当没有异常时，直接返回一个url，所以如果返回的不是url，就是有问题
        if (!optionalJson.isPresent()) {
            log.error("调用酷狗支付网关接口，响应为空。optionalJson: {}", optionalJson);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        String jsonContent = optionalJson.get();
        AlipayQrCodeUrlResponseObject alipayQrCodeUrlResponseObject = new Gson().fromJson(jsonContent, AlipayQrCodeUrlResponseObject.class);
        if (alipayQrCodeUrlResponseObject == null || alipayQrCodeUrlResponseObject.getError_code() != 0 || !alipayQrCodeUrlResponseObject.getError_msg().startsWith("http")) {
            log.error("调用酷狗支付网关接口，响应失败。jsonContent: {}", jsonContent);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("qrCodeUrl", alipayQrCodeUrlResponseObject.getError_msg());
        dataMap.put("qrUrl", alipayQrCodeUrlResponseObject.getError_msg());
        dataMap.put("rechargeOrderNum", rechargeAcrossPO.getRechargeOrderNum());
        return dataMap;
    }

    /**
     * 线下支付订单查询
     * http://doc.kugou.net/showdoc-master/web/#/8?page_id=9278
     */
    public OfflinePayDataBO queryOfflinepayV1(String rechargeOrderNum) {
        String url = StringUtils.join(rechargeConfig.getKupayIntranet(), "/v1/offlinepay/query");
        Map<String, String> urlParams = this.commonUrlParams("1084");
        Map<String, String> bodyParams = Maps.newHashMap();
        bodyParams.put("biz_appid", "1084");
        bodyParams.put("order_no", rechargeOrderNum);
        urlParams.put("signature", getCommandSign(urlParams, JSON.toJSONString(bodyParams)));
        Optional<String> optionalJson = HttpClientUtils.doPostJsonBody(url, urlParams, JSON.toJSONString(bodyParams));
        log.warn("线下支付订单查询，请求结束。url: {}, urlParams: {}, bodyParams: {}, optionalJson: {}", url, urlParams, bodyParams, optionalJson);
        String jsonContent = optionalJson.orElse("");
        if (!isKupaySuccess(jsonContent)) {
            log.error("线下支付订单查询，响应失败。jsonContent: {}", jsonContent);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        return JsonUtils.parseJsonPathObject(jsonContent, "$.data", OfflinePayDataBO.class);
    }

    /**
     * 线下支付下单
     * http://doc.kugou.net/showdoc-master/web/#/8?page_id=9278
     */
    public OfflinePayDataBO orderOfflinepayV1(long kugouId, String rechargeOrderNum, String tradeNo, long rechargeTime, BigDecimal money) {
        String url = StringUtils.join(rechargeConfig.getKupayIntranet(), "/v1/offlinepay/order");
        Map<String, String> urlParams = this.commonUrlParams("1084");
        Map<String, String> bodyParams = Maps.newHashMap();
        bodyParams.put("biz_appid", "1084");
        bodyParams.put("userid", String.valueOf(kugouId));
        bodyParams.put("auserid", String.valueOf(kugouId));
        bodyParams.put("clientip", "127.0.0.1");
        bodyParams.put("order_no", rechargeOrderNum);
        bodyParams.put("trade_no", tradeNo);
        bodyParams.put("bank_name", "");
        bodyParams.put("bank_no", "");
        bodyParams.put("pay_time", DateHelper.format(rechargeTime * 1000L));
        bodyParams.put("total_fee", money.stripTrailingZeros().toPlainString());
        bodyParams.put("notify_url", "");
        bodyParams.put("subject", "星币充值服务");
        bodyParams.put("desc", "星币充值服务");
        urlParams.put("signature", getCommandSign(urlParams, JSON.toJSONString(bodyParams)));
        Optional<String> optionalJson = HttpClientUtils.doPostJsonBody(url, urlParams, JSON.toJSONString(bodyParams));
        log.warn("线下支付下单，请求结束。url: {}, urlParams: {}, bodyParams: {}, optionalJson: {}", url, urlParams, bodyParams, optionalJson);
        String jsonContent = optionalJson.orElse("");
        if (!isKupaySuccess(jsonContent)) {
            log.error("线下支付下单，响应失败。jsonContent: {}", jsonContent);
            return this.queryOfflinepayV1(rechargeOrderNum);
        }
        return JsonUtils.parseJsonPathObject(jsonContent, "$.data", OfflinePayDataBO.class);
    }

    public boolean notifyIospayV1(String rechargeOrderNum, String outTradeNo, String tradeNo, String totalFee, Date payTime) {
        KupayAppInfoBO kupayAppInfoBO = rechargeConfig.getKupayAppIdByAppId(1084);
        String url = StringUtils.join(rechargeConfig.getKupayIntranet(), "/v1/iospay/notify");
        Map<String, String> urlParams = Maps.newHashMap();
        Map<String, String> argParams = Maps.newHashMap();
        argParams.put("order_no", rechargeOrderNum);
        argParams.put("out_trade_no", outTradeNo);
        argParams.put("trade_no", tradeNo);
        argParams.put("total_fee", totalFee);
        argParams.put("time_end", DateFormatUtils.format(payTime, "yyyyMMddHHmmss"));
        argParams.put("trade_state", String.valueOf(1));
        argParams.put("sign_type", rechargeConfig.getSignType());
        argParams.put("appid", String.valueOf(kupayAppInfoBO.getKupayAppId()));
        argParams.put("time", String.valueOf(System.currentTimeMillis()));
        argParams.put("sign", SignUtils.buildSign(argParams, rechargeConfig.getKupayAppIdByAppId(1084).getKupayAppKey()));
        Optional<String> optionalJsonContent = HttpClientUtils.doPostJSON(url, urlParams, argParams);
        log.warn("ios订单成功回调，请求结束。url: {}, urlParams: {}, argParams: {}, optionalJson: {}", url, urlParams, argParams, optionalJsonContent);
        String jsonContent = optionalJsonContent.orElse("");
        if (!isKupaySuccessByErrorCode(jsonContent)) {
            log.error("ios订单成功回调，响应失败。jsonContent: {}", jsonContent);
            return false;
        }
        return true;
    }

    public boolean isKupaySuccess(String jsonContent) {
        if (StringUtils.isBlank(jsonContent)) {
            log.error("调用酷狗支付网关接口，响应为空。jsonContent: {}", jsonContent);
            return false;
        }
        int status = JsonUtils.parseJsonPath(jsonContent, "$.status", Integer.class).orElse(0);
        int errorCode = JsonUtils.parseJsonPath(jsonContent, "$.error_code", Integer.class).orElse(0);
        if (status != 1 || errorCode != 0) {
            log.error("调用酷狗支付网关接口，响应错误。jsonContent: {}", jsonContent);
            return false;
        }
        return true;
    }

    public boolean isKupaySuccessByErrorCode(String jsonContent) {
        if (StringUtils.isBlank(jsonContent)) {
            log.error("调用酷狗支付网关接口，响应为空。jsonContent: {}", jsonContent);
            return false;
        }
        int errorCode = JsonUtils.parseJsonPath(jsonContent, "$.error_code", Integer.class).orElse(0);
        if (errorCode != 0) {
            log.error("调用酷狗支付网关接口，响应错误。jsonContent: {}", jsonContent);
            return false;
        }
        return true;
    }

    /**
     * 是否酷狗支付网关指定错误码
     */
    public boolean isKupaySpecificErrorCode(String jsonContent, Set<Integer> errorCodes) {
        Optional<Integer> optionalErrorCode = JsonUtils.parseJsonPath(jsonContent, "$.error_code", Integer.class);
        return optionalErrorCode.map(errorCodes::contains).orElse(false);
    }
    /**
     * gp下单接口
     * http://doc.kugou.net/showdoc-master/web/#/8?page_id=12775
     */
    public Optional<GwGpOrderV3Dto> gwGpOrder(RechargeAcrossPO rechargeAcrossPO, Map<String, String> kupaysParam, Map<String, Object> extendParam) {
        PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
        KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(payTypeIdEnum);
        String kupayAppId = String.valueOf(kupayAppInfoPO.getKupayAppId());
        String url = rechargeConfig.getKupayIntranetOutSea().concat("/v1/gp_order");
        Map<String, String> bodyParams = Maps.newHashMap();
        bodyParams.put("biz_appid", kupayAppId);
        bodyParams.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
        bodyParams.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
        bodyParams.put("subject", "星币充值服务");
        bodyParams.put("desc", "星币充值服务");
        bodyParams.put("total_fee", rechargeAcrossPO.getMoney().stripTrailingZeros().toPlainString());
        bodyParams.put("clientip", rechargeAcrossPO.getClientIp());
        bodyParams.put("sign_type", "md5");
        bodyParams.put("notify_url", this.rechargeConfig.getRechargeNotifyUrl(ReTypeEnum.RETYPE_RECHARGE));
        bodyParams.put("extend", this.rechargeCommonService.buildExtendStr(rechargeAcrossPO, extendParam));
        bodyParams.put("clientappid", rechargeConfig.getSyncUrl());
        bodyParams.put("useridtype", "0");
        bodyParams.putAll(kupaysParam);
        Map<String, String> urlParams = this.commonUrlParams(kupayAppId);
        urlParams.put("signature", getCommandSign(urlParams, JSON.toJSONString(bodyParams)));
        log.warn("调用酷狗支付网关接口，请求开始。url: {}, urlParams: {}, bodyParams: {}", url, urlParams, bodyParams);
        Optional<String> optionalJson = HttpClientUtils.doPostJsonBody(url, urlParams, JSON.toJSONString(bodyParams));
        log.warn("调用酷狗支付网关接口，请求结束。url: {}, urlParams: {}, bodyParams: {}, optionalJson: {}", url, urlParams, bodyParams, optionalJson);
        String jsonContent = optionalJson.orElse("");
        if (!isKupaySuccessV3(jsonContent)) {
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        GwGpOrderV3Dto gwiosOrderV3Dto = JsonUtils.parseJsonPathObject(jsonContent, "$.data", GwGpOrderV3Dto.class);
        return Optional.ofNullable(gwiosOrderV3Dto);
    }

    public boolean isKupaySuccessV3(String jsonContent) {
        if (StringUtils.isBlank(jsonContent)) {
            log.error("调用酷狗支付网关接口，响应为空。jsonContent: {}", jsonContent);
            return false;
        }
        int errorCode = JsonUtils.parseJsonPath(jsonContent, "$.error_code", Integer.class).orElse(0);
        if (errorCode != 0) {
            log.error("调用酷狗支付网关接口，响应错误。jsonContent: {}", jsonContent);
            return false;
        }
        return true;
    }

    /**
     * gp完成接口
     * http://doc.kugou.net/showdoc-master/web/#/8?page_id=12775
     */
    public Optional<GwFinishGpOrderV3Dto> gwFinishGpOrder(RechargeAcrossPO rechargeAcrossPO, Map<String, String> kupaysParam, Map<String, Object> extendParam) {
        PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
        KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(payTypeIdEnum);
        String kupayAppId = String.valueOf(kupayAppInfoPO.getKupayAppId());
        String url = rechargeConfig.getKupayIntranetOutSea().concat("/v1/gp_upload_receipt");
        Map<String, String> bodyParams = Maps.newHashMap();
        bodyParams.put("biz_appid", kupayAppId);
        bodyParams.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
        bodyParams.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
        bodyParams.put("out_trade_no", rechargeAcrossPO.getConsumeOrderNum());
        bodyParams.put("product_id", rechargeAcrossPO.getProductId());
        bodyParams.put("total_fee", rechargeAcrossPO.getMoney().stripTrailingZeros().toPlainString());
        bodyParams.put("clientip", rechargeAcrossPO.getClientIp());
        bodyParams.put("sign_type", "md5");
        bodyParams.put("notify_url", this.rechargeConfig.getRechargeNotifyUrl(ReTypeEnum.RETYPE_RECHARGE));
        bodyParams.put("extend", this.rechargeCommonService.buildExtendStr(rechargeAcrossPO, extendParam));
        bodyParams.put("clientappid", rechargeConfig.getSyncUrl());
        bodyParams.put("useridtype", "0");
        bodyParams.putAll(kupaysParam);
        Map<String, String> urlParams = this.commonUrlParams(kupayAppId);
        urlParams.put("signature", getCommandSign(urlParams, JSON.toJSONString(bodyParams)));
        Optional<String> optionalJson = HttpClientUtils.doPostJsonBody(url, urlParams, JSON.toJSONString(bodyParams));
        log.warn("调用酷狗支付网关接口，请求结束。url: {}, urlParams: {}, bodyParams: {}, optionalJson: {}", url, urlParams, bodyParams, optionalJson);
        String jsonContent = optionalJson.orElse("");
        if (!isKupaySuccessV3(jsonContent)) {
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        GwFinishGpOrderV3Dto result = JsonUtils.parseJsonPathObject(jsonContent, "$.data", GwFinishGpOrderV3Dto.class);
        result.setRechargeOrderNum(rechargeAcrossPO.getRechargeOrderNum());
        return Optional.ofNullable(result);
    }

    /**
     * Airwallex充值下单接口
     * @see <a>http://kapi.kugou.net/project/343/interface/api/103832</a>
     */
    public OrderAirwallexV1 orderAirwallexV1(RechargeAcrossPO rechargeAcrossPO, Map<String, Object> extendParam, Map<String, String> kupaysParam) {
        PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
        KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(payTypeIdEnum);
        String kupayAppId = String.valueOf(kupayAppInfoPO.getKupayAppId());
        String url = this.rechargeConfig.getKupayIntranet().concat("/v1/airwallex/order");
        Map<String, String> bodyParams = Maps.newHashMap();
        bodyParams.put("biz_appid", "1084");
        bodyParams.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
        bodyParams.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
        bodyParams.put("subject", "星币充值服务");
        bodyParams.put("desc", "星币充值服务");
        bodyParams.put("total_fee", rechargeAcrossPO.getCurrencyAmount().stripTrailingZeros().toPlainString());
        bodyParams.put("currency", rechargeAcrossPO.getCurrency());
        bodyParams.put("clientip", rechargeAcrossPO.getClientIp());
        bodyParams.put("sign_type", rechargeConfig.getSignType());
        bodyParams.put("notify_url", this.rechargeConfig.getRechargeNotifyUrl(ReTypeEnum.RETYPE_RECHARGE));
        bodyParams.put("extend", this.rechargeCommonService.buildExtendStr(rechargeAcrossPO, extendParam));
        bodyParams.putAll(kupaysParam);
        Map<String, String> urlParams = this.commonUrlParams(kupayAppId);
        urlParams.put("signature", getCommandSign(urlParams, JSON.toJSONString(bodyParams)));
        Optional<String> optionalJson = HttpClientUtils.doPostJsonBody(url, urlParams, JSON.toJSONString(bodyParams));
        log.warn("调用酷狗支付网关接口orderAirwallexV1，请求结束。url: {}, urlParams: {}, bodyParams: {}, optionalJson: {}", url, urlParams, bodyParams, optionalJson);
        //当没有异常时，直接返回一个url，所以如果返回的不是url，就是有问题
        if (!optionalJson.isPresent() || !isKupaySuccess(optionalJson.get())) {
            log.error("调用酷狗支付网关接口orderAirwallexV1，响应失败。optionalJson: {}", optionalJson);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        String jsonContent = optionalJson.get();
        OrderAirwallexV1 response = JsonUtils.parseJsonPathChecked(jsonContent, "$.data", OrderAirwallexV1.class);
        if (StringUtils.isAnyBlank(response.getOut_trade_no(), response.getIntent())) {
            log.error("调用酷狗支付网关接口orderAirwallexV1，响应内容缺失。optionalJson: {}", optionalJson);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        return response;
    }

    /**
     * Airwallex充值下单确认
     * http://kapi.kugou.net/project/343/interface/api/103860
     */
    public ConfirmAirwallexV1 confirmAirwallexV1(RechargeAcrossPO rechargeAcrossPO, String paymentType, String paymentMethod) {
        KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(PayTypeIdEnum.PAY_TYPE_ID_522);
        String kupayAppId = String.valueOf(kupayAppInfoPO.getKupayAppId());
        String url = this.rechargeConfig.getKupayIntranet().concat("/v1/airwallex/confirm");
        Map<String, String> bodyParams = Maps.newHashMap();
        bodyParams.put("biz_appid", String.valueOf(kupayAppInfoPO.getKupayAppId()));
        bodyParams.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
        bodyParams.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
        bodyParams.put("out_trade_no", rechargeAcrossPO.getConsumeOrderNum());
        bodyParams.put("payment_type", paymentType);
        bodyParams.put("payment_method", paymentMethod);
        Map<String, String> urlParams = this.commonUrlParams(kupayAppId);
        urlParams.put("signature", getCommandSign(urlParams, JSON.toJSONString(bodyParams)));
        Optional<String> optionalJson = HttpClientUtils.doPostJsonBody(url, urlParams, JSON.toJSONString(bodyParams));
        log.warn("调用酷狗支付网关接口confirmAirwallexV1，请求结束。url: {}, urlParams: {}, bodyParams: {}, optionalJson: {}", url, urlParams, bodyParams, optionalJson);
        if (!optionalJson.isPresent()) {
            log.error("调用酷狗支付网关接口confirmAirwallexV1，响应为空。optionalJson: {}", optionalJson);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        String jsonContent = optionalJson.get();
        Optional<String> optionalKupaySpecificError = hasKupaySpecificError(jsonContent, 20001, 20010);
        if (optionalKupaySpecificError.isPresent()) {
            log.error("调用酷狗支付网关接口confirmAirwallexV1，明确失败。optionalJson: {}", optionalJson);
            throw new KupayException(SysResultCode.KUPAY_CHANNEL_PROBLEM, optionalKupaySpecificError.get());
        }
        if (!isKupaySuccess(jsonContent)) {
            log.error("调用酷狗支付网关接口confirmAirwallexV1，未知失败。optionalJson: {}", optionalJson);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        ConfirmAirwallexResponse response = JsonUtils.readValue(jsonContent, ConfirmAirwallexResponse.class);
        if (Objects.isNull(response.getData()) || StringUtils.isBlank(response.getData().getType())) {
            log.error("调用酷狗支付网关接口confirmAirwallexV1，数据缺失。optionalJson: {}", optionalJson);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        return response.getData();
    }

    private Optional<String> hasKupaySpecificError(String jsonContent, int... specificErrorCodes) {
        int errorCode = JsonUtils.parseJsonPath(jsonContent, "$.error_code", Integer.class, 0);
        String errorMsg = JsonUtils.parseJsonPath(jsonContent, "$.error_msg", String.class, "");
        boolean anyMatch = Arrays.stream(specificErrorCodes).anyMatch(specificErrorCode -> errorCode == specificErrorCode);
        return anyMatch ? Optional.of(StringUtils.join(errorCode, "-", errorMsg)) : Optional.empty();
    }

    /**
     *
     * http://kapi.kugou.net/project/343/interface/api/104112
     */
    public PaymentMethodTypesDto paymentMethodsAirwallexV1(String country, String currency, Pagination pagination) {
        KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(PayTypeIdEnum.PAY_TYPE_ID_522);
        String kupayAppId = String.valueOf(kupayAppInfoPO.getKupayAppId());
        String url = this.rechargeConfig.getKupayIntranet().concat("/v1/airwallex/payment_methods");
        Map<String, String> bodyParams = Maps.newHashMap();
        bodyParams.put("biz_appid", String.valueOf(kupayAppInfoPO.getKupayAppId()));
        bodyParams.put("country_code", country);
        bodyParams.put("transaction_currency", currency);
        bodyParams.put("page_num", String.valueOf(pagination.getCurrPage() - 1));
        bodyParams.put("page_size", String.valueOf(pagination.getPageSize()));
        Map<String, String> urlParams = this.commonUrlParams(kupayAppId);
        urlParams.put("signature", getCommandSign(urlParams, JSON.toJSONString(bodyParams)));
        Optional<String> optionalJson = HttpClientUtils.doPostJsonBody(url, urlParams, JSON.toJSONString(bodyParams));
        log.warn("调用酷狗支付网关接口payAirwallexV1，请求结束。url: {}, urlParams: {}, bodyParams: {}, optionalJson: {}", url, urlParams, bodyParams, optionalJson);
        //当没有异常时，直接返回一个url，所以如果返回的不是url，就是有问题
        if (!optionalJson.isPresent() || !isKupaySuccess(optionalJson.get())) {
            log.error("调用酷狗支付网关接口payAirwallexV1，响应失败。optionalJson: {}", optionalJson);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        String json = optionalJson.get();
        return JsonUtils.parseJsonPathObject(json, "$.data", PaymentMethodTypesDto.class);
    }

    public BankNameListDto bankListsAirwallexV1(String country, String paymentMethodType, Pagination pagination) {
        KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(PayTypeIdEnum.PAY_TYPE_ID_522);
        String kupayAppId = String.valueOf(kupayAppInfoPO.getKupayAppId());
        String url = this.rechargeConfig.getKupayIntranet().concat("/v1/airwallex/bank_lists");
        Map<String, String> bodyParams = Maps.newHashMap();
        bodyParams.put("biz_appid", String.valueOf(kupayAppInfoPO.getKupayAppId()));
        bodyParams.put("country_code", country);
        bodyParams.put("payment_method_type", paymentMethodType);
        bodyParams.put("page_num", String.valueOf(pagination.getCurrPage() - 1));
        bodyParams.put("page_size", String.valueOf(pagination.getPageSize()));
        Map<String, String> urlParams = this.commonUrlParams(kupayAppId);
        urlParams.put("signature", getCommandSign(urlParams, JSON.toJSONString(bodyParams)));
        Optional<String> optionalJson = HttpClientUtils.doPostJsonBody(url, urlParams, JSON.toJSONString(bodyParams));
        log.warn("调用酷狗支付网关接口bankListsAirwallexV1，请求结束。url: {}, urlParams: {}, bodyParams: {}, optionalJson: {}", url, urlParams, bodyParams, optionalJson);
        //当没有异常时，直接返回一个url，所以如果返回的不是url，就是有问题
        if (!optionalJson.isPresent() || !isKupaySuccess(optionalJson.get())) {
            log.error("调用酷狗支付网关接口bankListsAirwallexV1，响应失败。optionalJson: {}", optionalJson);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        String json = optionalJson.get();
        return JsonUtils.parseJsonPathObject(json, "$.data", BankNameListDto.class);
    }

    public String wxnativepayV1(RechargeAcrossPO rechargeAcrossPO, Map<String, String> kupaysParam, Map<String, Object> extendParam) {
        try {
            String subject = this.rechargeCommonService.getSubject(rechargeAcrossPO.getCoinType());
            PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
            // 根据平台号获取微信配置
            KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(payTypeIdEnum);
            Optional<KupayAppTypeInfoBO> optionalKupayAppTypeInfoBO = this.rechargeConfig.getKupayAppTypeInfoBO(rechargeAcrossPO.getCFrom());
            log.warn("调用酷狗支付网关接口[wxnativepayV1]，获取网关支付配置ID。rechargeAcrossPO: {}, optionalKupayAppTypeInfoBO: {}", rechargeAcrossPO, optionalKupayAppTypeInfoBO);
            if (optionalKupayAppTypeInfoBO.isPresent()) {
                KupayAppTypeInfoBO kupayAppTypeInfoBO = optionalKupayAppTypeInfoBO.get();
                kupayAppInfoPO = rechargeConfig.getKupayAppIdByAppId(kupayAppTypeInfoBO.getKupayAppId());
            }
            Map<String, String> params = Maps.newHashMap();
            params.put("appid", String.valueOf(kupayAppInfoPO.getKupayAppId()));
            params.put("time", String.valueOf(System.currentTimeMillis()));
            params.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
            params.put("subject", subject);
            params.put("desc", subject);
            params.put("total_fee", rechargeAcrossPO.getMoney().stripTrailingZeros().toPlainString());
            params.put("sign_type", rechargeConfig.getSignType());
            params.put("notify_url", rechargeConfig.getNotifyUrl(ReTypeEnum.RETYPE_RECHARGE));
            params.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
            HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
            if (StringUtils.isNotBlank(request.getParameter("roomId"))) {
                extendParam.put("roomId", request.getParameter("roomId"));
            }
            params.put("extend", this.rechargeCommonService.buildExtendStr(rechargeAcrossPO, extendParam));
            params.put("sync_url", rechargeConfig.getSyncUrl());
            params.put("clientip", rechargeAcrossPO.getClientIp());
            params.putAll(kupaysParam);
            params.put("sign", SignUtils.buildSign(params, kupayAppInfoPO.getKupayAppKey()));

            // 注意⚠️：酷狗支付网关对GET+POST参数汇总后签名
            String url = this.rechargeConfig.getKupayIntranet().concat("/v1/wxnativepay");
            Map<String, String> urlParams = ImmutableMap.of("appid", params.get("appid"), "time", params.get("time"));
            Map<String, String> bodyParams = Maps.newHashMap(params);
            bodyParams.remove("appid");
            bodyParams.remove("time");
            Optional<String> optionalContent = HttpClientUtils.doPostJSON(url, urlParams, bodyParams);
            // 当没有异常时，直接返回一个url，所以如果返回的不是url，就是有问题
            if (!optionalContent.isPresent() || !StringUtils.startsWithAny(optionalContent.get(), "http", "weixin")) {
                log.error("调用酷狗支付网关接口[wxnativepayV1]，获取付款二维码链接异常。url: {}, urlParams: {}, bodyParams: {}, optionalContent: {}",
                        url, urlParams, bodyParams, optionalContent);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            String qrCodeUrl = optionalContent.get();
            if (qrCodeUrl.startsWith("http")) {
                qrCodeUrl = qrCodeUrl.concat("?t=" + DateHelper.getCurrentSeconds());
            }
            log.warn("调用酷狗支付网关接口[wxnativepayV1]，请求成功。url: {}, urlParams: {}, bodyParams: {}, optionalContent: {}",
                    url, urlParams, bodyParams, optionalContent);
            return qrCodeUrl;
        } catch (Exception e) {
            log.error("调用酷狗支付网关接口[wxnativepayV1]，构建第三方支付信息异常。rechargeAcrossPO: {}", rechargeAcrossPO, e);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    @Autowired
    private KugouHttpTemplate kgrpcProxy;

    @SneakyThrows
    public Optional<String> hisCanRcptOrdersV1(long kugouId, Pagination pagination) {
        URIBuilder uriBuilder = new URIBuilder("/api/receipt/v1/his_can_rcpt_orders");
        Map<String, String> uriParams = this.commonUrlParams("1084");
        uriParams.put("kugouid", String.valueOf(kugouId));
        uriParams.put("biz_appid", "1084");
        uriParams.put("rcpt_status", "1");
        uriParams.put("page", String.valueOf(pagination.getCurrPage() - 1));
        uriParams.put("page_size", String.valueOf(pagination.getPageSize()));
        uriParams.put("signature", getCommandSign(uriParams, ""));
        uriParams.forEach(uriBuilder::addParameter);
        URI uri = uriBuilder.build();
        KugouHttpRequest kugouHttpRequest = KugouHttpRequest.build()
                .setUrl(uri.toString())
                .addHeader("KgrpcHost", "receipt.kgidc.cn");
        KugouHttpResponse kugouHttpResponse = kgrpcProxy.execute(KugouHttpTemplate.GET, kugouHttpRequest);
        if (Objects.isNull(kugouHttpResponse) || !kugouHttpResponse.isSuccess() || Objects.isNull(kugouHttpResponse.getContent())) {
            log.warn("调用平台支付网关接口【hisCanRcptOrdersV1】，返回为空。uri: {}, kugouHttpResponse: {}", uri, kugouHttpResponse);
            return Optional.empty();
        }
        String content = new String(kugouHttpResponse.getContent());
        log.warn("content: {}", content);
        return Optional.of(content);
    }

    @SneakyThrows
    public Optional<String> showHisTagV1(long kugouId) {
        URIBuilder uriBuilder = new URIBuilder("/api/receipt/v1/show_his_tag");
        Map<String, String> uriParams = this.commonUrlParams("1084");
        uriParams.put("kugouid", String.valueOf(kugouId));
        uriParams.put("biz_appid", "1084");
        uriParams.put("signature", getCommandSign(uriParams, ""));
        uriParams.forEach(uriBuilder::addParameter);
        URI uri = uriBuilder.build();
        KugouHttpRequest kugouHttpRequest = KugouHttpRequest.build()
                .setUrl(uri.toString())
                .addHeader("KgrpcHost", "receipt.kgidc.cn");
        KugouHttpResponse kugouHttpResponse = kgrpcProxy.execute(KugouHttpTemplate.GET, kugouHttpRequest);
        if (Objects.isNull(kugouHttpResponse) || !kugouHttpResponse.isSuccess() || Objects.isNull(kugouHttpResponse.getContent())) {
            log.warn("调用平台支付网关接口【showHisTagV1】，返回为空。uri: {}, kugouHttpResponse: {}", uri, kugouHttpResponse);
            return Optional.empty();
        }
        String content = new String(kugouHttpResponse.getContent());
        log.warn("content: {}", content);
        return Optional.of(content);
    }

    /**
     * /v1/wxgzhpay
     */
    @SneakyThrows
    public Optional<WxgzhpayV1Dto> wxgzhpayV1(RechargeAcrossPO rechargeAcrossPO, Map<String, Object> extendParam,
                                              PurchaseRequest purchaseRequest) {
        String url = this.rechargeConfig.getKupayIntranet().concat("/v1/wxgzhpay");
        KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppInfo(rechargeAcrossPO.getCFrom());
        Map<String, String> params = Maps.newHashMap();
        params.put("appid", String.valueOf(kupayAppInfoPO.getKupayAppId()));
        params.put("time", String.valueOf(rechargeAcrossPO.getAddTime()));
        params.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
        params.put("subject", purchaseRequest.getDescription());
        params.put("desc", purchaseRequest.getDescription());
        params.put("total_fee", rechargeAcrossPO.getMoney().stripTrailingZeros().toPlainString());
        params.put("sign_type", rechargeConfig.getSignType());
        params.put("notify_url", rechargeConfig.getNotifyUrl(ReTypeEnum.RETYPE_RECHARGE));
        params.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
        params.put("extend", this.rechargeCommonService.buildExtendStr(rechargeAcrossPO, extendParam));
        params.put("sync_url", rechargeConfig.getSyncUrl());
        params.put("clientip", rechargeAcrossPO.getClientIp());
        params.put("openid", purchaseRequest.getOpenid());
        params.put("return_js", "1");
        params.put("sign", SignUtils.buildSign(params, kupayAppInfoPO.getKupayAppKey()));
        Optional<String> optionalJson = HttpClientUtils.doPostJSON(url, Maps.newHashMap(), params);
        log.warn("微信公众号充值下单，请求响应。rechargeAcrossPO: {}, extendParam: {}, purchaseRequest: {}, optionalJson: {}",
                rechargeAcrossPO, extendParam, purchaseRequest, optionalJson);
        if (optionalJson.isPresent()) {
            WxgzhpayV1Dto wxgzhpayV1Dto = JsonUtils.parseObject(optionalJson.get(), WxgzhpayV1Dto.class);
            return Optional.ofNullable(wxgzhpayV1Dto);
        }
        return Optional.empty();
    }

    /**
     * v3版本iOS下单接口
     * /v3/gwios_order
     */
    public Optional<GwiosOrderV3Dto> gwiosOrderV3(RechargeAcrossPO rechargeAcrossPO, Map<String, Object> extendParam,
                                                  PurchaseRequest purchaseRequest) {
        int stdPlat = rechargeAcrossPO.getCFrom();
        KupayAppInfoBO kupayAppInfoPO = this.rechargeConfig.getKupayAppInfo(stdPlat);
        String url = this.rechargeConfig.getKupayIntranet().concat("/v3/gwios_order");
        Map<String, String> bodyParams = Maps.newHashMap();
        bodyParams.put("biz_appid", String.valueOf(kupayAppInfoPO.getKupayAppId()));
        bodyParams.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
        bodyParams.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
        bodyParams.put("subject", purchaseRequest.getDescription());
        bodyParams.put("desc", purchaseRequest.getDescription());
        bodyParams.put("total_fee", rechargeAcrossPO.getMoney().stripTrailingZeros().toPlainString());
        bodyParams.put("clientip", rechargeAcrossPO.getClientIp());
        bodyParams.put("sign_type", rechargeConfig.getSignType());
        bodyParams.put("notify_url", this.rechargeConfig.getRechargeNotifyUrl(ReTypeEnum.RETYPE_RECHARGE));
        bodyParams.put("extend", this.rechargeCommonService.buildExtendStr(rechargeAcrossPO, extendParam));
        bodyParams.put("clientappid", rechargeConfig.getSyncUrl());
        bodyParams.put("useridtype", "0");
        Map<String, String> urlParams = this.commonUrlParams(String.valueOf(kupayAppInfoPO.getKupayAppId()));
        urlParams.put("signature", getCommandSign(urlParams, bodyParams));
        Optional<String> optionalJson = HttpClientUtils.doPostJSON(url, urlParams, bodyParams);
        log.warn("调用酷狗支付网关接口，请求结束。url: {}, urlParams: {}, bodyParams: {}, optionalJson: {}", url, urlParams, bodyParams, optionalJson);
        String jsonContent = optionalJson.orElse("");
        if (!isKupaySuccessV3(jsonContent)) {
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        GwiosOrderV3Dto gwiosOrderV3Dto = JsonUtils.parseJsonPathObject(jsonContent, "$.data", GwiosOrderV3Dto.class);
        return Optional.ofNullable(gwiosOrderV3Dto);
    }

    /**
     * iOS旧收据预开通
     * /v1/gwios_check_old_receipt_local
     */
    public GwiosCheckNewReceiptLocalV1Dto gwiosCheckOldReceiptLocalV1(RechargeAcrossPO rechargeAcrossPO, Map<String, Object> extendParam,
                                                                      String bundleId, String receiptData) {
        int stdPlat = rechargeAcrossPO.getCFrom();
        KupayAppInfoBO kupayAppInfoPO = this.rechargeConfig.getKupayAppInfo(stdPlat);
        String kupayAppId = String.valueOf(kupayAppInfoPO.getKupayAppId());
        String url = this.rechargeConfig.getKupayIntranet().concat("/v1/gwios_check_old_receipt_local");
        Map<String, String> bodyParams = Maps.newTreeMap();
        bodyParams.put("biz_appid", kupayAppId);
        bodyParams.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
        bodyParams.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
        bodyParams.put("product_id", rechargeAcrossPO.getProductId());
        bodyParams.put("clientip", rechargeAcrossPO.getClientIp());
        bodyParams.put("total_fee", rechargeAcrossPO.getMoney().stripTrailingZeros().toPlainString());
        bodyParams.put("bid", bundleId);
        bodyParams.put("notify_url", this.rechargeConfig.getRechargeNotifyUrl(ReTypeEnum.RETYPE_RECHARGE));
        bodyParams.put("date_after", DateHelper.format(DateUtils.addDays(new Date(), -20)));
        bodyParams.put("extend", this.rechargeCommonService.buildExtendStr(rechargeAcrossPO, extendParam));
        bodyParams.put("receipt_data", receiptData);
        Map<String, String> urlParams = this.commonUrlParams(kupayAppId);
        urlParams.put("signature", getCommandSign(urlParams, JsonUtils.toJSONString(bodyParams)));
        Optional<String> optionalJson = HttpClientUtils.doPostJsonBody(url, urlParams, JsonUtils.toJSONString(bodyParams));
        log.warn("调用酷狗支付网关接口，请求结束。url: {}, urlParams: {}, bodyParams: {}, optionalJson: {}", url, urlParams, bodyParams, optionalJson);
        String jsonContent = optionalJson.orElse("");
        // 处理收据重复上报
        if (isRepeatedReceipt(jsonContent)) {
            String data = JsonUtils.parseJsonPathChecked(jsonContent, "$.data", String.class);
            String transactionId = JsonUtils.parseJsonPathChecked(data, "$.extend.transaction_id", String.class);
            log.error("调用酷狗支付网关接口，存在重复上报收据。transactionId: {}", transactionId);
            Optional<GwiosQueryOrderV1Dto> optionalGwiosQueryOrderV1Dto = this.gwiosQueryOrderV1(
                    stdPlat, bundleId, transactionId);
            if (!optionalGwiosQueryOrderV1Dto.isPresent()) {
                log.error("调用酷狗支付网关接口，查询重复收据交易信息失败。transactionId: {}, optionalGwiosQueryOrderV1Dto: {}",
                        transactionId, optionalGwiosQueryOrderV1Dto);
                throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
            }
            GwiosQueryOrderV1Dto gwiosQueryOrderV1Dto = optionalGwiosQueryOrderV1Dto.get();
            GwiosCheckNewReceiptLocalV1Dto gwiosCheckNewReceiptLocalV1Dto = new GwiosCheckNewReceiptLocalV1Dto();
            gwiosCheckNewReceiptLocalV1Dto.setRechargeOrderNum(gwiosQueryOrderV1Dto.getOrder_no());
            gwiosCheckNewReceiptLocalV1Dto.setReceipt_status(1);
            gwiosCheckNewReceiptLocalV1Dto.setTransaction_id(transactionId);
            gwiosCheckNewReceiptLocalV1Dto.setOut_trade_no(gwiosQueryOrderV1Dto.getOut_trade_no());
            return gwiosCheckNewReceiptLocalV1Dto;
        }
        if (!isKupaySuccess(jsonContent)) {
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        GwiosCheckNewReceiptLocalV1Dto gwiosCheckNewReceiptLocalV1Dto = JsonUtils.parseJsonPathObject(jsonContent,
                "$.data", GwiosCheckNewReceiptLocalV1Dto.class);
        gwiosCheckNewReceiptLocalV1Dto.setRechargeOrderNum(rechargeAcrossPO.getRechargeOrderNum());
        return gwiosCheckNewReceiptLocalV1Dto;
    }

    public boolean isRepeatedReceipt(String jsonContent) {
        if (StringUtils.isNoneBlank(jsonContent)) {
            int status = JsonUtils.parseJsonPath(jsonContent, "$.status", Integer.class).orElse(0);
            int errorCode = JsonUtils.parseJsonPath(jsonContent, "$.error_code", Integer.class).orElse(0);
            return status == 0 && errorCode == 60058;
        }
        return false;
    }

    /**
     * 业务单号查苹果订单
     * /v1/gwios_query_order
     */
    public Optional<GwiosQueryOrderV1Dto> gwiosQueryOrderV1(int stdPlat, String bid, String transactionId) {
        KupayAppInfoBO kupayAppInfoPO = this.rechargeConfig.getKupayAppInfo(stdPlat);
        String kupayAppId = String.valueOf(kupayAppInfoPO.getKupayAppId());
        String url = this.rechargeConfig.getKupayIntranet().concat("/v1/gwios_query_order");
        Map<String, String> bodyParams = Maps.newHashMap();
        bodyParams.put("biz_appid", kupayAppId);
        bodyParams.put("bid", bid);
        bodyParams.put("transaction_id", transactionId);
        Map<String, String> urlParams = this.commonUrlParams(kupayAppId);
        urlParams.put("signature", getCommandSign(urlParams, JsonUtils.toJSONString(bodyParams)));
        Optional<String> optionalJson = HttpClientUtils.doPostJsonBody(url, urlParams, JsonUtils.toJSONString(bodyParams));
        log.warn("调用酷狗支付网关接口，请求结束。url: {}, urlParams: {}, bodyParams: {}, optionalJson: {}", url, urlParams, bodyParams, optionalJson);
        String jsonContent = optionalJson.orElse("");
        if (!isKupaySuccess(jsonContent)) {
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        GwiosQueryOrderV1Dto gwiosQueryOrderV1Dto = JsonUtils.parseJsonPathObject(jsonContent, "$.data", GwiosQueryOrderV1Dto.class);
        return Optional.ofNullable(gwiosQueryOrderV1Dto);
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShowHisTagResp {
        private int status;
        private int error_code;
        private String error_msg;
        private ShowHisTag data;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShowHisTag {
        private boolean is_show;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CanRcptOrders {
        private int status;
        private int error_code;
        private String error_msg;
        private RcptOrderWrapper data;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RcptOrderWrapper {
        private int total_count;
        @Builder.Default
        private List<RcptOrder> list = Collections.emptyList();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RcptOrder {
        private String order_no;
        private String trade_no;
        private String out_trade_no;
        private BigDecimal total_fee;
        private String subject;
        private String desc;
        private int rcpt_status;
        private String trade_time;
    }
}
