package com.kugou.fanxing.recharge.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Message;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.kugou.fanxing.biz.commons.util.GrayTools;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.LocalCurrencyConfigDao;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.bo.AccountChangeTypeBO;
import com.kugou.fanxing.recharge.model.bo.CouponInfoBO;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.bo.KupayAppTypeInfoBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.GetOrderRequest;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.model.vo.*;
import com.kugou.fanxing.recharge.service.common.ConsumeRpcService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.thrift.callback.ConsumeParam;
import com.kugou.fanxing.recharge.util.*;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 充值服务通用功能
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class RechargeCommonService {

    /**
     * 充值下单版本(线上)
     */
    public static final String RECHARGE_VERSION_ONLINE = "20170111";
    private static final GrayTools getKupayAppInfoGray = GrayTools.module("getKupayInfoGrayForAll");

    @Autowired
    private OrderIdService orderIdService;
    @Autowired
    private RechargeConfig rechargeConfig;
    @Autowired
    private ValidatingService validatingService;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private RemoteStrategyService remoteStrategyService;
    @Autowired
    private RemoteFamilyControlService remoteFamilyControlService;
    @Autowired
    private ConsumeRpcService consumeRpcService;
    @Autowired
    private KupayService kupayService;
    @Autowired
    private UserFacadeService userFacadeService;

    /**
     * 充值方式是否维护中
     */
    public boolean isForbiddenPayType(PayTypeIdEnum payTypeIdEnum) {
        int payTypeId = payTypeIdEnum.getPayTypeId();
        return this.apolloConfigService.isForbiddenPayType(payTypeId);
    }

    /**
     * 是否禁止唱币充值
     */
    public boolean isForbiddenSingCoin(int coinType) {
        if (CoinTypeEnum.isSingCoinType(coinType)) {
            return this.apolloConfigService.isForbiddenSingCoin();
        }
        return false;
    }

    /**
     * 充值平台是否禁用
     */
    public boolean isForbiddenStdPlat(int stdPlat) {
        return this.apolloConfigService.isForbiddenStdPlat(stdPlat);
    }

    /**
     * 充值应用是否禁用
     */
    public boolean isForbiddenAppId(int appId) {
        return this.apolloConfigService.isForbiddenAppId(appId);
    }

    public String getSubject(int coinType) {
        boolean isSingCoinType = CoinTypeEnum.isSingCoinType(coinType);
        return isSingCoinType ? "唱币充值服务" : "星币充值服务";
    }

    /**
     * 第三方检查
     *
     * @param rechargeOrderNum  充值订单
     * @param webCommonParam    通用参数
     * @param getOrderRequest   下单参数
     */
    public SysResultCode checkThirdPart(String rechargeOrderNum, WebCommonParam webCommonParam, GetOrderRequest getOrderRequest) {
        int payTypeId = getOrderRequest.getPayTypeIdEnum().getPayTypeId();
        BigDecimal amount = getOrderRequest.getAmount();
        return this.checkThirdPart(webCommonParam.getStdPlat(), webCommonParam.getKugouId(), rechargeOrderNum, payTypeId, amount);
    }

    public SysResultCode checkThirdPart(int stdPlat, long kugouId, String rechargeOrderNum, int payTypeId, BigDecimal amount) {
        // 校验家长控制
        if (this.remoteFamilyControlService.checkFamilyControl(kugouId, stdPlat)) {
            log.warn("充值下单接口，家长控制拦截下单。kugouId: {}, rechargeOrderNum: {}", kugouId, rechargeOrderNum);
            return SysResultCode.RECHARGE_FAMILY_CONTROL;
        }
        // 校验风控服务
        if (this.remoteStrategyService.strategyVerifyForGetOrder(kugouId, rechargeOrderNum, payTypeId, amount)) {
            log.warn("充值下单接口，风控服务拦截下单。kugouId: {}, rechargeOrderNum: {}", kugouId, rechargeOrderNum);
            return SysResultCode.RECHARGE_RISK_STRATEGY;
        }
        // 校验繁星账户
        Optional<Long> optionalUserId = this.userFacadeService.getUserIdByKugouId(kugouId, false);
        if (!optionalUserId.isPresent() || optionalUserId.get() < 1) {
            log.warn("充值下单接口，繁星账户缺失拦截。kugouId: {}, rechargeOrderNum: {}", kugouId, rechargeOrderNum);
            return SysResultCode.RECHARGE_NOT_FANXING;
        }
        // 校验酷我账户（仅针对酷我环境）
        if (apolloConfigService.isKuwoEnv()) {
            Optional<Long> optionalKuwoId = this.userFacadeService.getKuwoIdByKugouId(kugouId);
            if (!optionalKuwoId.isPresent() || optionalKuwoId.get() < 1) {
                log.warn("充值下单接口，酷我账户缺失拦截。kugouId: {}, rechargeOrderNum: {}, optionalKuwoId: {}", kugouId, rechargeOrderNum, optionalKuwoId);
                return SysResultCode.RECHARGE_NOT_KUWO_ID;
            }
        }
        return SysResultCode.SUCCESS;
    }

    public <T> SysResultCode checkParameter(WebCommonParam webCommonParam, T request) {
        Optional<ConstraintViolation<WebCommonParam>> optionalConstraintViolation1 = validatingService.checkViolation(webCommonParam);
        Optional<ConstraintViolation<T>> optionalConstraintViolation2 = validatingService.checkViolation(request);
        if (optionalConstraintViolation1.isPresent() || optionalConstraintViolation2.isPresent()) {
            log.warn("检查前置条件，请求参数不合法。webCommonParam: {}, getOrderRequest: {}, optionalConstraintViolation1: {}, request: {}",
                    webCommonParam, request, optionalConstraintViolation1, optionalConstraintViolation2);
            throw new BizException(SysResultCode.RECHARGE_PARAM_ERROR);
        }
        return SysResultCode.SUCCESS;
    }

    /**
     * 检查前置条件
     *
     * @param rechargeOrderNum  充值订单
     * @param webCommonParam    通用参数
     * @param getOrderRequest   下单参数
     */
    public SysResultCode checkPreconditionForGetOrder(String rechargeOrderNum, WebCommonParam webCommonParam, GetOrderRequest getOrderRequest) {
        // 请求前端参数校验
        Optional<ConstraintViolation<GetOrderRequest>> optionalConstraintViolation = validatingService.checkViolation(getOrderRequest);
        if (optionalConstraintViolation.isPresent()) {
            log.warn("检查前置条件，请求参数不合法。rechargeOrderNum: {}, webCommonParam: {}, getOrderRequest: {}, optionalConstraintViolation: {}",
                    rechargeOrderNum, webCommonParam, getOrderRequest, optionalConstraintViolation);
            throw new BizException(SysResultCode.RECHARGE_PARAM_ERROR);
        }
        // 检查充值方式配置
        if (!this.rechargeConfig.isRechargeWithChange(getOrderRequest.getPayTypeIdEnum().getPayTypeId())) {
            log.warn("检查前置条件，充值渠道尚未配置。rechargeOrderNum: {}, webCommonParam: {}, getOrderRequest: {}", rechargeOrderNum, webCommonParam, getOrderRequest);
            throw new BizException(SysResultCode.RECHARGE_PAY_TYPE_NOT_SUPPORT);
        }
        // 检查充值渠道开关
        if (this.isForbiddenPayType(getOrderRequest.getPayTypeIdEnum())) {
            log.warn("检查前置条件，充值渠道暂停服务。rechargeOrderNum: {}, webCommonParam: {}, getOrderRequest: {}", rechargeOrderNum, webCommonParam, getOrderRequest);
            throw new BizException(SysResultCode.RECHARGE_PAUSE_SERVICE);
        }
        // 检查充值平台开关
        if (this.isForbiddenStdPlat(webCommonParam.getStdPlat())) {
            log.warn("检查前置条件，充值平台暂停服务。rechargeOrderNum: {}, webCommonParam: {}, getOrderRequest: {}", rechargeOrderNum, webCommonParam, getOrderRequest);
            throw new BizException(SysResultCode.FORBIDDEN_RECHARGE_PLAT);
        }
        // 检查充值应用开关
        if (this.isForbiddenAppId(webCommonParam.getAppId())) {
            log.warn("检查前置条件，充值应用暂停服务。rechargeOrderNum: {}, webCommonParam: {}, getOrderRequest: {}", rechargeOrderNum, webCommonParam, getOrderRequest);
            throw new BizException(SysResultCode.FORBIDDEN_RECHARGE_PLAT);
        }
        // 唱币充值开关
        if (this.isForbiddenSingCoin(getOrderRequest.getCoinType())) {
            log.warn("充值购买虚拟货币下单接口，已经禁止唱币充值。rechargeOrderNum: {}, webCommonParam: {}, getOrderRequest: {}", rechargeOrderNum, webCommonParam, getOrderRequest);
            throw new BizException(SysResultCode.RECHARGE_FORBIDDEN_SING_COIN);
        }
        // 检查外部依赖服务
        SysResultCode resultCode = checkThirdPart(rechargeOrderNum, webCommonParam, getOrderRequest);
        if (!resultCode.isSuccess()) {
            log.warn("检查前置条件，外部服务检查失败。rechargeOrderNum: {}, webCommonParam: {}, getOrderRequest: {}", rechargeOrderNum, webCommonParam, getOrderRequest);
            throw new BizException(resultCode);
        }
        return SysResultCode.SUCCESS;
    }

    /**
     * 获取手机客户端拉起支付参数（需要POST）
     *
     * 28, 手机充值卡（手机）
     * 30, 支付宝（手机）
     * 35, 银行卡（手机）
     * 40, 微信支付（手机）
     *
     *
     * @param rechargeAcrossPO 充值交易订单
     * @param extParam         扩展参数
     * @return
     */
    public Map<String, Object> createRechargeResultForMobileByPost(RechargeAcrossPO rechargeAcrossPO, Map<String, String> extParam, Map<String, Object> extendParam, int... kupayAppIds) {
        long currentSeconds = DateHelper.getCurrentSeconds();
        PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
        KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(payTypeIdEnum);
        // 注意⚠️：如果apptype=share，需要重置
        if ("share".equals(extParam.get("apptype")) || this.apolloConfigService.allowGetKupayConfigByPid(rechargeAcrossPO.getCFrom())) {
            Optional<KupayAppTypeInfoBO> optionalKupayAppTypeInfoBO = this.rechargeConfig.getKupayAppTypeInfoBO(rechargeAcrossPO.getCFrom());
            int kupayAppId = optionalKupayAppTypeInfoBO.orElseThrow(() -> new ContextedRuntimeException("kupayAppId不存在").addContextValue("apptype", "share")).getKupayAppId();
            kupayAppInfoPO = rechargeConfig.getKupayAppIdByAppId(kupayAppId);
        }
        // 注意⚠️：如果显示设置了kupayAppId信息，需要重置
        if (kupayAppIds.length == 1) {
            kupayAppInfoPO = rechargeConfig.getKupayAppIdByAppId(Arrays.stream(kupayAppIds).findFirst().orElseThrow(() -> new ContextedRuntimeException("kupayAppId不存在")));
        }
        Map<String, String> bodyParams = buildRechargeParamForMobileByPost(kupayAppInfoPO, rechargeAcrossPO,extParam, currentSeconds, extendParam);
        String actionUrl = buildRechargeUrlForMobileByPost(kupayAppInfoPO, rechargeAcrossPO, currentSeconds);
        Optional<String> optionalJson = HttpClientUtils.doPostJSON(actionUrl, Maps.newHashMap(), bodyParams);
        log.warn("获取手机客户端拉起支付参数，调用网关请求参数。kupayAppInfoPO: {}, actionUrl: {}, bodyParams: {}，optionalJson: {}", kupayAppInfoPO, actionUrl, bodyParams, optionalJson);
        if (!optionalJson.isPresent() || !KupayUtils.isSuccessResponse(payTypeIdEnum, optionalJson.get())) {
            log.warn("获取手机客户端拉起支付参数，请求接口响应失败。rechargeOrderNum: {}, response: {}", rechargeAcrossPO.getRechargeOrderNum(), optionalJson);
            return Maps.newHashMap();
        }
        if (PayTypeIdEnum.PAY_TYPE_ID_40.equals(payTypeIdEnum)) {
            Map<String, String> param = JSON.parseObject(optionalJson.get(), new TypeReference<Map<String, String>>(){} );
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("status", 1);
            dataMap.put("webMethod", "POST");
            dataMap.put("actionUrl", param);
            return dataMap;
        }
        String param = JsonUtils.parseJson(optionalJson.get(), "$.param");
        if (StringUtils.isBlank(param)) {
            log.warn("获取手机客户端拉起支付参数，获取支付参数失败。rechargeOrderNum: {}, response: {}", rechargeAcrossPO.getRechargeOrderNum(), optionalJson);
            return Maps.newHashMap();
        }
        if (PayTypeIdEnum.PAY_TYPE_ID_35.equals(payTypeIdEnum)) {
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("status", 1);
            dataMap.put("tn", param);
            return dataMap;
        }
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("status", 1);
        dataMap.put("webMethod", "POST");
        dataMap.put("actionUrl", param);
        return dataMap;
    }

    public Map<String, String> buildRechargeParamForMobileByPost(KupayAppInfoBO kupayAppInfoPO, RechargeAcrossPO rechargeAcrossPO, Map<String, String> extParam, long currentSeconds, Map<String, Object> extendParam) {
        String subject = getSubject(rechargeAcrossPO.getCoinType());
        PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
        Map<String, String> params = Maps.newTreeMap();
        params.put("appid", String.valueOf(kupayAppInfoPO.getKupayAppId()));
        params.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
        params.put("time", String.valueOf(currentSeconds));
        params.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
        params.put("subject", subject);
        params.put("desc", subject);
        params.put("total_fee", rechargeAcrossPO.getMoney().stripTrailingZeros().toPlainString());
        params.put("sign_type", rechargeConfig.getSignType());
        params.put("notify_url", rechargeConfig.getNotifyUrl(ReTypeEnum.RETYPE_RECHARGE));
        params.put("extend", this.buildExtendStr(rechargeAcrossPO, extendParam));
        params.put("sync_url", rechargeConfig.getSyncUrl());
        params.put("clientip", IpUtils.getClientIpAddress());
        params.putAll(extParam);
        // 兼容网关的BUG，clientip不参与签名
        Map<String, String> signParams = Maps.newHashMap(params);
        if (PayTypeIdEnum.PAY_TYPE_ID_30.equals(payTypeIdEnum) || PayTypeIdEnum.PAY_TYPE_ID_35.equals(payTypeIdEnum)) {
            signParams.remove("clientip");
            signParams.remove("cardType");
        }
        String sign = SignUtils.buildSign(signParams, kupayAppInfoPO.getKupayAppKey());
        params.put("sign", sign);
        return params;
    }

    public String buildRechargeUrlForMobileByPost(KupayAppInfoBO kupayAppInfoPO, RechargeAcrossPO rechargeAcrossPO, long currentSeconds) {
        try {
            PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
            String actionUrl = this.rechargeConfig.getActionUrlPrefix(payTypeIdEnum);
            Map<String, String> params = Maps.newHashMap();
            params.put("appid", String.valueOf(kupayAppInfoPO.getKupayAppId()));
            params.put("time", String.valueOf(currentSeconds));
            HttpUrl httpUrl = HttpClientUtils.makeHttpUrlBuilder(actionUrl, params).build();
            String rechargeUrl = URLDecoder.decode(String.valueOf(httpUrl), StandardCharsets.UTF_8.name());
            log.warn("actionUrl: {}", rechargeUrl);
            return rechargeUrl;
        } catch (Exception e) {
            log.error("充值下单，构建第三方支付信息异常。rechargeAcrossPO: {}", rechargeAcrossPO, e);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    /**
     * 获取手机客户端拉起支付参数（需要POST）
     *
     * 31, 支付宝wap
     * 32, 微信wap
     * 41, 微信公众号
     *
     * @return 返回的充值链接
     */
    public Map<String, Object> createRechargeResultForMobile(RechargeAcrossPO rechargeAcrossPO, String businessExt, String clientIp, Map<String, String> extParam) {
        Map<String, Object> dataMap = Maps.newHashMap();
        long currentSeconds = DateHelper.getCurrentSeconds();
        Map<String, Object> extendParam = Maps.newHashMap();
        extendParam.put("roomId", String.valueOf(SpringContextUtils.getAsInt("roomId", 0)));
        extendParam.put("businessExt", StringUtils.defaultString(businessExt));
        dataMap.put("param", buildRechargeParamForMobile(rechargeAcrossPO, clientIp, extParam, currentSeconds, extendParam));
        dataMap.put("actionUrl", buildRechargeUrlForMobile(rechargeAcrossPO, currentSeconds));
        log.warn("recharge result: {}", dataMap);
        return dataMap;
    }

    public Map<String, String> buildRechargeParamForMobile(RechargeAcrossPO rechargeAcrossPO, String clientIp, Map<String, String> extParam, long currentSeconds, Map<String, Object> extendParam) {
        String subject = getSubject(rechargeAcrossPO.getCoinType());
        PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
        KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(payTypeIdEnum);
        Map<String, String> params = Maps.newHashMap();
        params.put("appid", String.valueOf(kupayAppInfoPO.getKupayAppId()));
        params.put("time", String.valueOf(currentSeconds));
        params.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
        params.put("subject", subject);
        params.put("desc", subject);
        params.put("total_fee", rechargeAcrossPO.getMoney().stripTrailingZeros().toPlainString());
        params.put("sign_type", rechargeConfig.getSignType());
        params.put("notify_url", rechargeConfig.getNotifyUrl(ReTypeEnum.RETYPE_RECHARGE));
        params.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
        params.put("extend", this.buildExtendStr(rechargeAcrossPO, extendParam));
        params.put("sync_url", rechargeConfig.getSyncUrl());
        params.put("clientip", clientIp);
        params.putAll(extParam);
        params.put("sign", SignUtils.buildSign(params, kupayAppInfoPO.getKupayAppKey()));
        return params;
    }

    public String buildRechargeUrlForMobile(RechargeAcrossPO rechargeAcrossPO, long currentSeconds) {
        try {
            PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
            KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(payTypeIdEnum);
            String actionUrl = this.rechargeConfig.getActionUrlPrefix(payTypeIdEnum);
            Map<String, String> params = Maps.newHashMap();
            params.put("appid", String.valueOf(kupayAppInfoPO.getKupayAppId()));
            params.put("time", String.valueOf(currentSeconds));
            HttpUrl httpUrl = HttpClientUtils.makeHttpUrlBuilder(actionUrl, params).build();
            String rechargeUrl = URLDecoder.decode(String.valueOf(httpUrl), StandardCharsets.UTF_8.name());
            log.warn("actionUrl: {}", rechargeUrl);
            return rechargeUrl;
        } catch (Exception e) {
            log.error("充值下单，构建第三方支付信息异常。rechargeAcrossPO: {}", rechargeAcrossPO, e);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    /**
     * 制造充值返回结果
     *
     * @return 返回的充值链接
     */
    public Map<String, Object> createRechargeResultAlipayPC(RechargeAcrossPO rechargeAcrossPO, String clientIp, Map<String, String> extParam, Map<String, Object> extendParam) {
        Map<String, Object> dataMap = Maps.newHashMap();
        String actionUrl = buildRechargeUrlForPC(rechargeAcrossPO, clientIp, extParam, extendParam);
        dataMap.put("rechargeOrderNum", String.valueOf(rechargeAcrossPO.getRechargeOrderNum()));
        dataMap.put("orderId", String.valueOf(rechargeAcrossPO.getRechargeOrderNum()));
        dataMap.put("actionUrl", actionUrl);
        dataMap.put("pwdUrl", actionUrl);
        dataMap.put("webMethod", rechargeConfig.getWebMethod());
        return dataMap;
    }

    /**
     * 制造充值返回结果
     *
     * @return 返回的充值链接
     */
    public Map<String, Object> createRechargeResult(RechargeAcrossPO rechargeAcrossPO, String clientIp, Map<String, String> extParam, Map<String, Object> extendParam) {
        Map<String, Object> dataMap = Maps.newHashMap();
        String actionUrl = buildRechargeUrlForPC(rechargeAcrossPO,clientIp, extParam, extendParam);
        dataMap.put("rechargeOrderNum",String.valueOf(rechargeAcrossPO.getRechargeOrderNum()));
        dataMap.put("orderId",String.valueOf(rechargeAcrossPO.getRechargeOrderNum()));
        dataMap.put("actionUrl", actionUrl);
        dataMap.put("pwdUrl", actionUrl);
        extParam.put("qr","1");
        extParam.put("pay_mode","4");
        dataMap.put("qrUrl", buildQrCodeUrlForAlipay(rechargeAcrossPO,clientIp, extParam, extendParam));
        dataMap.put("webMethod", rechargeConfig.getWebMethod());
        return dataMap;
    }

    /**
     * 制造充值返回结果
     *
     * @return 返回的充值链接
     */
    public Map<String, Object> createRechargeResultWechatH5(RechargeAcrossPO rechargeAcrossPO, Map<String, String> kupayParam, Map<String, Object> extendParam, Optional<KupayAppInfoBO> optionalKupayAppInfoBO) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("jumpUrl", buildRechargeUrlForWechatH5(rechargeAcrossPO, kupayParam, extendParam, optionalKupayAppInfoBO));
        dataMap.put("orderId", rechargeAcrossPO.getRechargeOrderNum());
        dataMap.put("businessId", rechargeAcrossPO.getRechargeOrderNum());
        dataMap.put("rechargeOrderNum", rechargeAcrossPO.getRechargeOrderNum());
        return dataMap;
    }

    public Map<String, Object> createRechargeResultAlipayH5(RechargeAcrossPO rechargeAcrossPO, Map<String, String> kupayParam, Map<String, Object> extendParam, Optional<KupayAppInfoBO> optionalKupayAppInfoBO) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("jumpUrl", buildRechargeUrlForAlipayH5(rechargeAcrossPO, kupayParam, extendParam, optionalKupayAppInfoBO));
        dataMap.put("orderId", rechargeAcrossPO.getRechargeOrderNum());
        dataMap.put("businessId", rechargeAcrossPO.getRechargeOrderNum());
        return dataMap;
    }

    public Map<String, Object> createWxRechargeResult(RechargeAcrossPO rechargeAcrossPO, String clientIp, Map<String, String> extParam, boolean isFrontQrCode, Map<String, Object> extendParam) {
        Map<String, Object> dataMap = Maps.newHashMap();
        if (isFrontQrCode) {
            String codeUrl = buildRechargeQrCodeUrl(rechargeAcrossPO,clientIp, extParam, extendParam);
            dataMap.put("codeUrl", codeUrl);
            dataMap.put("rechargeOrderNum", rechargeAcrossPO.getRechargeOrderNum());
            return dataMap;
        }
        String actionUrl = buildRechargeUrlForPC(rechargeAcrossPO,clientIp, extParam, extendParam);
        dataMap.put("actionUrl", actionUrl);
        dataMap.put("pwdUrl", actionUrl);
        String qrUrl = buildRechargeQrCodeUrl(rechargeAcrossPO,clientIp, extParam, extendParam);
        dataMap.put("qrCodeUrl", qrUrl);
        dataMap.put("qrUrl", qrUrl);
        dataMap.put("webMethod", rechargeConfig.getWebMethod());
        dataMap.put("rechargeOrderNum",String.valueOf(rechargeAcrossPO.getRechargeOrderNum()));
        dataMap.put("orderId",String.valueOf(rechargeAcrossPO.getRechargeOrderNum()));
        return dataMap;
    }

    public String buildRechargeQrCodeUrl(RechargeAcrossPO rechargeAcrossPO,String clientIp,Map<String, String> extParam, Map<String, Object> extendParam) {
        try {
            String subject = getSubject(rechargeAcrossPO.getCoinType());
            PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
            // 根据平台号获取微信配置
            KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(payTypeIdEnum);
            Optional<KupayAppTypeInfoBO> optionalKupayAppTypeInfoBO = this.rechargeConfig.getKupayAppTypeInfoBO(rechargeAcrossPO.getCFrom());
            if (optionalKupayAppTypeInfoBO.isPresent()) {
                KupayAppTypeInfoBO kupayAppTypeInfoBO = optionalKupayAppTypeInfoBO.get();
                kupayAppInfoPO = rechargeConfig.getKupayAppIdByAppId(kupayAppTypeInfoBO.getKupayAppId());
            }
            Map<String, String> params = Maps.newHashMap();
            params.put("appid", String.valueOf(kupayAppInfoPO.getKupayAppId()));
            params.put("time", String.valueOf(System.currentTimeMillis()));
            params.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
            params.put("subject", subject);
            params.put("desc", subject);
            params.put("total_fee", rechargeAcrossPO.getMoney().stripTrailingZeros().toPlainString());
            params.put("sign_type", rechargeConfig.getSignType());
            params.put("notify_url", rechargeConfig.getNotifyUrl(ReTypeEnum.RETYPE_RECHARGE));
            params.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
            params.put("extend", this.buildExtendStr(rechargeAcrossPO, extendParam));
            params.put("sync_url", rechargeConfig.getSyncUrl());
            params.put("clientip", clientIp);
            params.putAll(extParam);
            params.put("sign", SignUtils.buildSign(params, kupayAppInfoPO.getKupayAppKey()));

            // 注意⚠️：酷狗支付网关对GET+POST参数汇总后签名
            String url = this.rechargeConfig.getActionQrUrlPrefix(payTypeIdEnum).orElseThrow(() -> new ContextedRuntimeException("生产支付二维码地址配置不存在")
                    .addContextValue("payTypeId", payTypeIdEnum.getPayTypeId()));
            Map<String, String> urlParams = ImmutableMap.of("appid", params.get("appid"), "time", params.get("time"));
            Map<String, String> bodyParams = Maps.newHashMap(params);
            bodyParams.remove("appid");
            bodyParams.remove("time");
            Optional<String> optionalQrCodeUrl = HttpClientUtils.doPostJSON(url, urlParams, bodyParams);
            //当没有异常时，直接返回一个url，所以如果返回的不是url，就是有问题
            if (!optionalQrCodeUrl.isPresent() || !StringUtils.startsWithAny(optionalQrCodeUrl.get(), "http", "weixin")) {
                log.error("充值下单，获取付款二维码链接异常。optionalQrCodeUrl: {}", optionalQrCodeUrl);
                throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
            }
            String qrCodeUrl = optionalQrCodeUrl.get();
            if (qrCodeUrl.startsWith("http")) {
                qrCodeUrl = qrCodeUrl.concat("?t=" + DateHelper.getCurrentSeconds());
            }
            log.warn("qrCodeUrl: {}", qrCodeUrl);
            return qrCodeUrl;
        } catch (Exception e) {
            log.error("充值下单，构建第三方支付信息异常。rechargeAcrossPO: {}", rechargeAcrossPO, e);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    public String buildRechargeUrlForPC(RechargeAcrossPO rechargeAcrossPO, String clientIp, Map<String, String> extParam, Map<String, Object> extendParam) {
        try {
            String subject = getSubject(rechargeAcrossPO.getCoinType());
            PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
            KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(payTypeIdEnum);
            String actionUrl = this.rechargeConfig.getActionUrlPrefix(payTypeIdEnum);
            Map<String, String> params = Maps.newHashMap();
            params.put("appid", String.valueOf(kupayAppInfoPO.getKupayAppId()));
            params.put("time", String.valueOf(System.currentTimeMillis()));
            params.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
            params.put("subject", subject);
            params.put("desc", subject);
            params.put("total_fee", rechargeAcrossPO.getMoney().stripTrailingZeros().toPlainString());
            params.put("sign_type", rechargeConfig.getSignType());
            params.put("notify_url", rechargeConfig.getNotifyUrl(ReTypeEnum.RETYPE_RECHARGE));
            params.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
            params.put("extend", this.buildExtendStr(rechargeAcrossPO,extendParam));
            params.put("sync_url", rechargeConfig.getSyncUrl());
            params.put("clientip", clientIp);
            params.putAll(extParam);
            params.put("sign", SignUtils.buildSign(params, kupayAppInfoPO.getKupayAppKey()));
            if (!ArrayUtils.contains(new int[] {31, 32, 39, 41}, rechargeAcrossPO.getPayTypeId()) ) {
                params.put("extend", URLEncoder.encode(params.get("extend"), StandardCharsets.UTF_8.name()));
            }
            HttpUrl httpUrl = HttpClientUtils.makeHttpUrlBuilder(actionUrl, params).build();
            String rechargeUrl = URLDecoder.decode(String.valueOf(httpUrl), StandardCharsets.UTF_8.name());
            log.warn("actionUrl: {}", rechargeUrl);
            return rechargeUrl;
        } catch (Exception e) {
            log.error("充值下单，构建第三方支付信息异常。rechargeAcrossPO: {}", rechargeAcrossPO, e);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    /**
     * appid	是	int	业务appid
     * time	是	string	时间戳
     * order_no	是	string	业务订单号(调用方订购商品生成的订单号,限制长度不超过30）
     * subject	是	string	商品名称
     * desc	是	string	商品描述
     * total_fee	是	string	交易金额 如：100.00 单位:RMB，精确到分
     * clientip	是	string	客户端IP(不允许 127.0.0.1)
     * sign_type	是	string	签名算法，只支持md5
     * sign	是	string	签名，参与签名的参数是这里列出的除sign外的参数
     * notify_url	是	string	异步回调地址
     * sync_url	是	string	同步回调地址, 支付成功，微信不会跳转这个地址
     * expire_time	否	string	订单的过期时间，格式为yyyyMMddHHmmss，20091227091010，注意：过期时间 - now > 5分钟，否则该参数作废
     * userid	否	int	被充值帐号的kugou用户ID,默认为0
     * auserid	否	int	操作充值者kugou用户ID,默认为0
     * extend	否	string	扩展字段,做为回调时需返回该参数
     * wxurl	否	int	有传此参数则返回拉起微信支付收银台的中间页面的url，否则返回拉起微信支付的生成中间页代码
     * @param rechargeAcrossPO
     * @param kupayParam
     * @param extendParam
     * @param optionalKupayAppInfoBO
     * @return
     */
    public String buildRechargeUrlForWechatH5(RechargeAcrossPO rechargeAcrossPO, Map<String, String> kupayParam, Map<String, Object> extendParam, Optional<KupayAppInfoBO> optionalKupayAppInfoBO) {
        try {
            String subject = getSubject(rechargeAcrossPO.getCoinType());
            PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
            KupayAppInfoBO kupayAppInfoPO = optionalKupayAppInfoBO.orElseGet(() -> rechargeConfig.getKupayAppIdByPayType(payTypeIdEnum));
            if(getKupayAppInfoGray.isGray(rechargeAcrossPO.getKugouId())) {
                kupayAppInfoPO = rechargeConfig.getKupayAppInfoWithDefault(rechargeAcrossPO.getCFrom(),kupayAppInfoPO);
            }
            String actionUrl = this.rechargeConfig.getActionUrlPrefix(payTypeIdEnum);
            Map<String, String> params = Maps.newHashMap();
            params.put("appid", String.valueOf(kupayAppInfoPO.getKupayAppId()));
            params.put("time", String.valueOf(System.currentTimeMillis()));
            params.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
            params.put("subject", subject);
            params.put("desc", subject);
            params.put("total_fee", rechargeAcrossPO.getMoney().stripTrailingZeros().toPlainString());
            params.put("sign_type", rechargeConfig.getSignType());
            params.put("notify_url", rechargeConfig.getNotifyUrl(ReTypeEnum.of(rechargeAcrossPO.getReType())));
            params.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
            params.put("extend", this.buildExtendStr(rechargeAcrossPO,extendParam));
            params.put("clientip", rechargeAcrossPO.getClientIp());
            String syncUrl = rechargeConfig.getSyncUrl();
            if(kupayParam.containsKey("sync_url") && !StringUtils.isEmpty(kupayParam.get("sync_url"))){
                syncUrl = kupayParam.get("sync_url");
            }
            params.put("sync_url", syncUrl);
            params.putAll(kupayParam);
            params.put("sign", SignUtils.buildSign(params, kupayAppInfoPO.getKupayAppKey()));
            Optional<String> optionalUrl = HttpClientUtils.doPostJSON(actionUrl, new HashMap<>(), params);
            //当没有异常时，直接返回一个url，所以如果返回的不是url，就是有问题
            if(!optionalUrl.isPresent()){
                log.error("qrCodeUrl fail, result is null。url: {}, urlParam: {}, bodyParam: {}, optionalUrl: {}", actionUrl, new HashMap<>(), params, optionalUrl);
                throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
            }
            if(!optionalUrl.get().startsWith("http")){
                log.error("qrCodeUrl fail, result url: {}, urlParam: {}, bodyParam: {}, optionalUrl: {}", actionUrl, new HashMap<>(), params, optionalUrl);
                throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
            }
            log.warn("actionUrl: {}", optionalUrl.get());
            return optionalUrl.get() + "&redirect_url=" + URLEncoder.encode(syncUrl,"UTF-8");
        } catch (Exception e) {
            log.error("充值下单，构建第三方支付信息异常。rechargeAcrossPO: {}", rechargeAcrossPO, e);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    public String buildQrCodeUrlForAlipay(RechargeAcrossPO rechargeAcrossPO, String clientIp, Map<String, String> extParam, Map<String, Object> extendParam) {
        try {
            String subject = getSubject(rechargeAcrossPO.getCoinType());
            PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
            KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(payTypeIdEnum);
            Optional<String> optionalActionUrl = this.rechargeConfig.getActionQrUrlPrefix(payTypeIdEnum);
            if(!optionalActionUrl.isPresent()){
                log.error("qrCodeUrl fail, can not get url:{}",rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
            }
            String actionUrl = optionalActionUrl.get();
            Map<String, String> params = Maps.newHashMap();
            params.put("appid", String.valueOf(kupayAppInfoPO.getKupayAppId()));
            params.put("time", String.valueOf(System.currentTimeMillis()));
            params.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
            params.put("subject", subject);
            params.put("desc", subject);
            params.put("total_fee", rechargeAcrossPO.getMoney().stripTrailingZeros().toPlainString());
            params.put("sign_type", rechargeConfig.getSignType());
            params.put("notify_url", rechargeConfig.getNotifyUrl(ReTypeEnum.RETYPE_RECHARGE));
            params.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
            params.put("extend", this.buildExtendStr(rechargeAcrossPO, extendParam));
            params.put("clientip", clientIp);
            params.putAll(extParam);
            params.put("sign", SignUtils.buildSign(params, kupayAppInfoPO.getKupayAppKey()));
            Optional<String> optionalUrl = HttpClientUtils.doPostJSON(actionUrl, new HashMap<>(), params);
            //当没有异常时，直接返回一个url，所以如果返回的不是url，就是有问题
            if(!optionalUrl.isPresent()){
                log.error("qrCodeUrl fail, result is null");
                throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
            }
            AlipayQrCodeUrlResponseObject alipayQrCodeUrlResponseObject = new Gson().fromJson(optionalUrl.get(), AlipayQrCodeUrlResponseObject.class);
            if(alipayQrCodeUrlResponseObject == null || alipayQrCodeUrlResponseObject.getError_code() != 0 || !alipayQrCodeUrlResponseObject.getError_msg().startsWith("http")){
                log.error("qrCodeUrl fail, result is:{}",optionalUrl.get());
                throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
            }

            log.warn("actionUrl: {}", optionalUrl.get());
            return alipayQrCodeUrlResponseObject.getError_msg();
        } catch (Exception e) {
            log.error("充值下单，构建第三方支付信息异常。rechargeAcrossPO: {}", rechargeAcrossPO, e);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    public String buildRechargeUrlForAlipayH5(RechargeAcrossPO rechargeAcrossPO, Map<String, String> kupayParam, Map<String, Object> extendParam, Optional<KupayAppInfoBO> optionalKupayAppInfoBO) {
        try {
            String subject = getSubject(rechargeAcrossPO.getCoinType());
            PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
            KupayAppInfoBO kupayAppInfoPO = optionalKupayAppInfoBO.orElseGet(() -> rechargeConfig.getKupayAppIdByPayType(payTypeIdEnum));
            if(getKupayAppInfoGray.isGray(rechargeAcrossPO.getKugouId())) {
                kupayAppInfoPO = rechargeConfig.getKupayAppInfoWithDefault(rechargeAcrossPO.getCFrom(),kupayAppInfoPO);
            }
            String actionUrl = this.rechargeConfig.getActionUrlPrefix(payTypeIdEnum);
            Map<String, String> params = Maps.newHashMap();
            params.put("appid", String.valueOf(kupayAppInfoPO.getKupayAppId()));
            params.put("time", String.valueOf(System.currentTimeMillis()));
            params.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
            params.put("subject", subject);
            params.put("desc", subject);
            params.put("total_fee", rechargeAcrossPO.getMoney().stripTrailingZeros().toPlainString());
            params.put("sign_type", rechargeConfig.getSignType());
            params.put("notify_url", rechargeConfig.getNotifyUrl(ReTypeEnum.RETYPE_RECHARGE));
            params.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
            params.put("extend", this.buildExtendStr(rechargeAcrossPO,extendParam));
            params.put("clientip", rechargeAcrossPO.getClientIp());
            String syncUrl = rechargeConfig.getSyncUrl();
            if(kupayParam.containsKey("sync_url") && !StringUtils.isEmpty(kupayParam.get("sync_url"))){
                syncUrl = kupayParam.get("sync_url");
            }
            params.put("sync_url", syncUrl);
            params.putAll(kupayParam);
            params.put("sign", SignUtils.buildSign(params, kupayAppInfoPO.getKupayAppKey()));
            Optional<String> optionalUrl = HttpClientUtils.doPostJSON(actionUrl, new HashMap<>(), params);
            //当没有异常时，直接返回一个url，所以如果返回的不是url，就是有问题
            if(!optionalUrl.isPresent()){
                log.error("qrCodeUrl fail, result is null");
                throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
            }
            log.warn("支付宝H5内网充值下单。optionalUrl: {}", optionalUrl);
            AlipayH5UrlResponseObject alipayH5UrlResponseObject = new Gson().fromJson(optionalUrl.get(), AlipayH5UrlResponseObject.class);
            if(alipayH5UrlResponseObject == null || alipayH5UrlResponseObject.getError_code() != 0 || alipayH5UrlResponseObject.getData() == null || alipayH5UrlResponseObject.getData().getUrl() == null || !alipayH5UrlResponseObject.getData().getUrl().startsWith("http")){
                log.error("qrCodeUrl fail, result is:{}",optionalUrl.get());
                throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
            }

            log.warn("actionUrl: {}", optionalUrl.get());
            return alipayH5UrlResponseObject.getData().getUrl();
        } catch (Exception e) {
            log.error("充值下单，构建第三方支付信息异常。rechargeAcrossPO: {}", rechargeAcrossPO, e);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    public RechargeAcrossPO buildRechargeAcross(int pid, long kugouId, String rechargeOrderNum, PayTypeIdEnum payTypeIdEnum, BigDecimal amount, String clientIp, Optional<CouponInfoBO> optionalCouponInfoBO) {
        return this.buildRechargeAcross(pid, kugouId, 0, rechargeOrderNum, payTypeIdEnum, amount, clientIp, optionalCouponInfoBO);
    }

    public RechargeAcrossPO buildRechargeAcross(int pid, long kugouId, String rechargeOrderNum, PayTypeIdEnum payTypeIdEnum, BigDecimal amount, String clientIp) {
        return this.buildRechargeAcross(pid, kugouId, 0, rechargeOrderNum, payTypeIdEnum, amount, clientIp);
    }

    public RechargeAcrossPO buildRechargeAcross(int pid, long kugouId, long rechargeKugouId, String rechargeOrderNum, PayTypeIdEnum payTypeIdEnum, BigDecimal amount, String clientIp, Optional<CouponInfoBO> optionalCouponInfoBO) {
        return buildRechargeAcross(pid, kugouId, rechargeKugouId, rechargeOrderNum, payTypeIdEnum, amount, clientIp, CoinTypeEnum.STAR_COIN, optionalCouponInfoBO);
    }

    public RechargeAcrossPO buildRechargeAcross(int pid, long kugouId, long rechargeKugouId, String rechargeOrderNum, PayTypeIdEnum payTypeIdEnum, BigDecimal amount, String clientIp, CoinTypeEnum coinTypeEnum, Optional<CouponInfoBO> optionalCouponInfoBO) {
        RechargeAcrossPO rechargeAcrossPO = this.buildRechargeAcross(pid, kugouId, rechargeKugouId, rechargeOrderNum, payTypeIdEnum, amount, clientIp, coinTypeEnum);
        if (optionalCouponInfoBO.isPresent()) {
            CouponInfoBO couponInfoBO = optionalCouponInfoBO.get();
            rechargeAcrossPO.setMoney(amount.subtract(couponInfoBO.getValue()));
            rechargeAcrossPO.setCoupon(couponInfoBO.getValue());
            rechargeAcrossPO.setCouponId(couponInfoBO.getCouponId());
            rechargeAcrossPO.setCouponOrderId(couponInfoBO.getOrderId());
        }
        return rechargeAcrossPO;
    }

    public RechargeAcrossPO buildRechargeAcross(int pid, long kugouId, long rechargeKugouId, String rechargeOrderNum,
                                                PayTypeIdEnum payTypeIdEnum, BigDecimal amount, String clientIp) {
        return this.buildRechargeAcross(pid, kugouId, rechargeKugouId, rechargeOrderNum, payTypeIdEnum, amount, clientIp, CoinTypeEnum.STAR_COIN);
    }

    /**
     * 构建充值订单，支持代充
     */
    public RechargeAcrossPO buildRechargeAcross(int pid, long kugouId, long rechargeKugouId, String rechargeOrderNum,
                                                PayTypeIdEnum payTypeIdEnum, BigDecimal amount, String clientIp, CoinTypeEnum coinTypeEnum) {
        int channelId = 0;
        if (PayTypeIdEnum.PAY_TYPE_ID_3.getPayTypeId() == payTypeIdEnum.getPayTypeId() || PayTypeIdEnum.PAY_TYPE_ID_39.getPayTypeId() == payTypeIdEnum.getPayTypeId()) {
            channelId = 1;
        }
        return new RechargeAcrossPO()
                .setRechargeId(orderIdService.generateGlobalId())
                .setRechargeOrderNum(rechargeOrderNum)
                .setAddTime(DateHelper.getCurrentSeconds())
                .setKugouId(rechargeKugouId < 1 ? kugouId : rechargeKugouId)
                .setFromKugouId(rechargeKugouId < 1 ? kugouId : rechargeKugouId)
                .setAgentKugouId(rechargeKugouId < 1 ? 0 : kugouId)
                .setCoinBefore(BigDecimal.ZERO)
                .setCoin(BigDecimal.ZERO)
                .setCoinAfter(BigDecimal.ZERO)
                .setAmount(amount)
                .setRealAmount(BigDecimal.ZERO)
                .setMoney(amount)
                .setPayTypeId(payTypeIdEnum.getPayTypeId())
                .setStatus(0)
                .setRefer(0)
                .setCFrom(pid)
                .setChannelId(channelId)
                .setReType(ReTypeEnum.RETYPE_RECHARGE.getReTypeId())
                .setServerRoom(rechargeConfig.getDataCenterZoneId())
                .setCoupon(BigDecimal.ZERO)
                .setCouponId(0)
                .setCouponOrderId(0)
                .setCoinType(coinTypeEnum.getCoinType())
                .setClientIp(clientIp);
    }

    public String buildProductExtendStr(long kugouId, String rechargeOrderNum, Map<String, String> callBackArgMap, Map<String, String> userFundPlatParam) {
        Map<String, Object> extend = Maps.newHashMap();
        extend.put("callBackSign", createCallbackSign(rechargeOrderNum, kugouId));
        extend.put("callBackArg", createProductCallBackArg(callBackArgMap, userFundPlatParam));
        log.warn("创建网关回调透传扩展字段[extend], kugouId: {}, rechargeOrderNum: {}, extend: {}", kugouId, rechargeOrderNum, extend);
        return new String(Base64.encodeBase64(JSON.toJSONString(extend).getBytes()));
    }

    public String buildExtendStr(RechargeAcrossPO rechargeAcrossPO, Map<String, Object> extendParam) {
        String rechargeOrderNum = rechargeAcrossPO.getRechargeOrderNum();
        long kugouId = rechargeAcrossPO.getKugouId();
        Map<String, Object> extend = Maps.newHashMap();
        extend.put("callBackSign", createCallbackSign(rechargeOrderNum, kugouId));
        extend.put("callBackArg", createCallBackArg(rechargeAcrossPO, extendParam));
        log.warn("创建网关回调透传扩展字段[extend], rechargeOrderNum: {}, kugouId: {}, extend: {}", rechargeOrderNum, kugouId, extend);
        return new String(Base64.encodeBase64(JSON.toJSONString(extend).getBytes()));
    }

    public String buildExtendStrForIosPurchase(RechargeAcrossPO rechargeAcrossPO, ConsumeParam consumeParam, String businessExt, String clientIp) {
        String rechargeOrderNum = rechargeAcrossPO.getRechargeOrderNum();
        long kugouId = rechargeAcrossPO.getKugouId();
        Map<String, Object> extend = Maps.newHashMap();
        extend.put("callBackSign", createCallbackSign(rechargeOrderNum, kugouId));
        extend.put("callBackArg", createCallBackArgForIosPurchase(rechargeAcrossPO, consumeParam, businessExt, clientIp));
        log.warn("创建网关回调透传扩展字段[extend], rechargeOrderNum: {}, kugouId: {}, extend: {}", rechargeOrderNum, kugouId, extend);
        return new String(Base64.encodeBase64(JSON.toJSONString(extend).getBytes()));
    }

    public Map<String, Object> createCallBackArgForIosPurchase(RechargeAcrossPO rechargeAcrossPO, ConsumeParam consumeParam, String businessExt, String clientIp) {
        AccountChangeTypeBO accountChangeTypeBO = this.apolloConfigService.getAccountChangeTypeById(consumeParam.getAccountChangeType());
        long consumeGlobalId = JsonUtils.parseJsonPathChecked(rechargeAcrossPO.getExtraJsonData(), "$.businessId", Long.class);
        Map<String, Object> callBackArg = Maps.newLinkedHashMap();
        callBackArg.put("businessId", consumeGlobalId);
        callBackArg.put("businessType", rechargeAcrossPO.getBusinessId());
        callBackArg.put("businessTime", rechargeAcrossPO.getAddTime());
        callBackArg.put("payTime", rechargeAcrossPO.getAddTime());
        callBackArg.put("fromKugouId", rechargeAcrossPO.getKugouId());
        callBackArg.put("toKugouId", consumeParam.getToKugouId());
        callBackArg.put("topic", StringUtils.defaultString(accountChangeTypeBO.getTopic()));
        callBackArg.put("refer", rechargeAcrossPO.getRefer());
        callBackArg.put("cFrom", rechargeAcrossPO.getCFrom());
        callBackArg.put("channelId", rechargeAcrossPO.getChannelId());
        callBackArg.put("amount", rechargeAcrossPO.getAmount().stripTrailingZeros().toPlainString());
        callBackArg.put("payTypeId", rechargeAcrossPO.getPayTypeId());
        callBackArg.put("addTime", rechargeAcrossPO.getAddTime());
        callBackArg.put("kugouId", rechargeAcrossPO.getKugouId());
        callBackArg.put("version", RECHARGE_VERSION_ONLINE);
        callBackArg.put("rebate", 0);
        callBackArg.put("reType", rechargeAcrossPO.getReType());
        callBackArg.put("areaId", rechargeAcrossPO.getAreaId());
        callBackArg.put("userFundPlatParam", this.consumeRpcService.buildIosPurchaseUserFundPlatParam(rechargeAcrossPO, consumeParam, clientIp));
        if (StringUtils.isNotBlank(businessExt)) {
            callBackArg.put("businessExt", businessExt);
        }
        return callBackArg;
    }

    /**
     * 创建扩展字段充值回调签名
     *
     * @param rechargeOrderNum 充值订单号
     * @param kugouId          酷狗ID
     * @return 充值回调校签名
     */
    public String createCallbackSign(String rechargeOrderNum, long kugouId) {
        String callBackKey = rechargeConfig.getCallBackKey();
        Map<String, String> callBackSignArg = Maps.newHashMap();
        callBackSignArg.put("order_no", rechargeOrderNum);
        callBackSignArg.put("userid", String.valueOf(kugouId));
        return DigestUtils.md5Hex(JSON.toJSONString(callBackSignArg) + callBackKey);
    }

    /**
     * 创建扩展字段充值回调参数
     *
     * @param callBackArg       回调透传参数
     * @param userFundPlatParam 消费透传参数
     * @return 充值回调参数
     */
    public Map<String, Object> createProductCallBackArg(Map<String, String> callBackArg, Map<String, String> userFundPlatParam) {
        Map<String, Object> dataMap = Maps.newHashMap(callBackArg);
        dataMap.put("userFundPlatParam", userFundPlatParam);
        return dataMap;
    }

    public Map<String, Object> createCallBackArg(RechargeAcrossPO rechargeAcrossPO, Map<String, Object> extendParam) {
        Map<String, Object> callBackArg = Maps.newHashMap();
        callBackArg.put("refer", rechargeAcrossPO.getRefer());
        callBackArg.put("cFrom", rechargeAcrossPO.getCFrom());
        callBackArg.put("channelId", rechargeAcrossPO.getChannelId());
        callBackArg.put("amount", rechargeAcrossPO.getAmount().toPlainString());
        callBackArg.put("payTypeId", rechargeAcrossPO.getPayTypeId());
        callBackArg.put("addTime", String.valueOf(rechargeAcrossPO.getAddTime()));
        callBackArg.put("kugouId", rechargeAcrossPO.getKugouId());
        callBackArg.put("money", rechargeAcrossPO.getMoney().toPlainString());
        callBackArg.put("coupon", rechargeAcrossPO.getCoupon().toPlainString());
        callBackArg.put("couponId", rechargeAcrossPO.getCouponId());
        callBackArg.put("couponOrderId", rechargeAcrossPO.getCouponOrderId());
        callBackArg.put("version", RECHARGE_VERSION_ONLINE);
        callBackArg.put("agentKugouId", rechargeAcrossPO.getAgentKugouId());
        callBackArg.put("productId", StringUtils.defaultString(rechargeAcrossPO.getProductId()));
        callBackArg.put("areaCode", StringUtils.defaultString(rechargeAcrossPO.getAreaCode()));
        callBackArg.put("timeZone", StringUtils.defaultString(rechargeAcrossPO.getTimeZone()));
        callBackArg.put("currency", StringUtils.defaultString(rechargeAcrossPO.getCurrency()));
        callBackArg.put("clientIp", StringUtils.defaultString(rechargeAcrossPO.getClientIp()));
        callBackArg.put("currencyAmount", rechargeAcrossPO.getCurrencyAmount() == null?"0":rechargeAcrossPO.getCurrencyAmount());
        callBackArg.put("usdAmount", rechargeAcrossPO.getUsdAmount() == null?"0":rechargeAcrossPO.getUsdAmount());
        callBackArg.put("areaId", rechargeAcrossPO.getAreaId());
        callBackArg.put("coin", rechargeAcrossPO.getCoin().toPlainString());
        callBackArg.put("coinType", rechargeAcrossPO.getCoinType());
        callBackArg.put("businessOrderNo", StringUtils.defaultString(rechargeAcrossPO.getBusinessOrderNo()));
        callBackArg.put("appVersion", StringUtils.defaultString(rechargeAcrossPO.getAppVersion()));
        callBackArg.putAll(extendParam);
        HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
        //充值返点版本
        callBackArg.put("rebate", this.getRechargeRebateVersion(request));
        //购买撩主播
        callBackArg.put("consumeArgs", MapUtils.isNotEmpty(this.getRechargeConsumeArg(request)) ? this.getRechargeConsumeArg(request) : new JSONArray());
        return callBackArg;
    }

    /**
     * 获取充值返点信息
     */
    public int getRechargeRebateVersion(HttpServletRequest request) {
        return IntHelper.tryParse(request.getParameter("rebate"), 0);
    }

    /**
     * 获取充值相关业务的参数,扩展(后续改成透传)
     *
     * @return array
     */
    public Map<String, Object> getRechargeConsumeArg(HttpServletRequest request) {
        // 获取消费类型
        Map<String, Object> consumeArgs = Maps.newHashMap();
        String consumeType = StringEscapeUtils.escapeHtml(StringUtils.defaultString(request.getParameter("consumeType")));
        if (StringUtils.isBlank(consumeType)) {
            log.warn("调用getRechargeConsumeArg(), 获取消费类型为空, consumeType: {}", consumeType);
            return consumeArgs;
        }
        // 处理撩主播
        if ("teaseAnchor".equalsIgnoreCase(consumeType)) {
            int appId = IntHelper.tryParse(request.getParameter("appId"), 0);
            // 网站前端传Cookie
            if (appId == 0) {
                Map<String, Cookie> cookieMap = Arrays.stream(Optional.ofNullable(request.getCookies())
                        .orElse(new Cookie[]{}))
                        .collect(Collectors.toMap(Cookie::getName, Function.identity(), (oldValue, newValue) -> oldValue));
                Optional<KugooCookieInfo> optionalKugooCookieInfo = WebUserInfoUtils.getUserFromKugouCookie(cookieMap);
                if (optionalKugooCookieInfo.isPresent()) {
                    appId = NumberUtils.toInt(optionalKugooCookieInfo.get().getAppid());
                }
            }
            consumeArgs = ImmutableMap.<String, Object>builder()
                    .put("consumeType", consumeType)
                    .put("appId", appId)
                    .put("roomId", IntHelper.tryParse(request.getParameter("roomId"), 0))
                    .put("giftId", IntHelper.tryParse(request.getParameter("giftId"), 0))
                    .put("giftNum", IntHelper.tryParse(request.getParameter("giftNum"), 0))
                    .put("pid", IntHelper.tryParse(request.getParameter("pid"), 0))
                    .build();
            log.warn("调用getRechargeConsumeArg(), 构建消费参数, consumeArgs: {}", consumeArgs);
        }
        return consumeArgs;
    }

    public String getOrderExpireTime() {
        int expireMinutes = apolloConfigService.getRechargeOrderExpireMinutes();
        return DateHelper.getKupayExpireTime(expireMinutes);
    }

    public String getSyncUrl(String syncUrl, String referer) {
        return StringEscapeUtils.unescapeXml(StringUtils.defaultString(syncUrl, referer));
    }

    @Autowired
    private LocalCurrencyConfigDao localCurrencyConfigDao;

    public Optional<String> parseCountryCode(String changedKey) {
        final String regex = "overseas\\.currencyConfig\\.country-(.+)";
        final Pattern pattern = Pattern.compile(regex);
        final Matcher matcher = pattern.matcher(changedKey);
        if (matcher.find() && StringUtils.isNoneBlank(matcher.group(1))) {
            String countryCode = StringUtils.trim(matcher.group(1));
            if (isValidCountryCode(countryCode)) {
                return Optional.of(countryCode);
            }
        }
        return Optional.empty();
    }

    public boolean isValidCountryCode(String countryCode) {
        return this.apolloConfigService.getOverseasCountryList().stream()
                .anyMatch(countryInfoVo -> countryInfoVo.getCountry().equalsIgnoreCase(countryCode));
    }

    /**
     * 处理本地币种
     * @return 本地币种配置
     */
    public int generateLocalCurrencyList(CountryInfoVo countryInfoVo) {
        String country = countryInfoVo.getCountry();
        String displayCountry = countryInfoVo.getDisplayCountry();
        List<LocalCurrencyConfig> currencyConfigList = this.apolloConfigService.getOverseasCurrencyConfigByCountry(country);
        if (CollectionUtils.isEmpty(currencyConfigList)) {
            log.error("获取当地充值金额表，无对应配置。country: {}", country);
            return 0;
        }
        final Date current = new Date();
        final String configSnapshot = JSON.toJSONString(currencyConfigList);
        final String configSnapshotHash = StringUtils.upperCase(country) + "-" + DigestUtils.md5Hex(configSnapshot);
        List<LocalCurrencyConfigPo> localCurrencyConfigPos = currencyConfigList.stream()
                .map(localCurrencyConfig -> LocalCurrencyConfigPo.builder()
                        .globalId(orderIdService.generateGlobalId())
                        .country(country)
                        .displayCountry(displayCountry)
                        .currency(localCurrencyConfig.getCurrency())
                        .currencyAmount(localCurrencyConfig.getCurrencyAmount())
                        .usdAmount(localCurrencyConfig.calculateUsdAmount())
                        .coin(localCurrencyConfig.calculateCoin())
                        .cnyAmount(localCurrencyConfig.calculateCnyAmount())
                        .local2usdRates(localCurrencyConfig.getLocal2usdRates())
                        .usd2cnyRates(localCurrencyConfig.getUsd2cnyRates())
                        .volatilityCoefficient(localCurrencyConfig.getVolatilityCoefficient())
                        .configSnapshot(configSnapshot)
                        .configSnapshotHash(configSnapshotHash)
                        .createTime(current)
                        .updateTime(current)
                        .build())
                .collect(Collectors.toList());
        return this.localCurrencyConfigDao.batchInsert(localCurrencyConfigPos);
    }

    public List<LocalCurrencyConfigPo> queryLocalCurrencyList(String country) {
        String configSnapshotHash = this.apolloConfigService.getConfigSnapshotHashByCountry(country);
        if (StringUtils.isBlank(configSnapshotHash)) {
            log.error("获取当地充值金额与方式，配置缺失。country: {}", country);
            return Lists.newArrayList();
        }
        return this.localCurrencyConfigDao.query(country, configSnapshotHash);
    }

    public List<LocalAmountVo> queryLocalAmountVoList(String country) {
        List<LocalCurrencyConfigPo> localCurrencyConfigList = this.queryLocalCurrencyList(country);
        return localCurrencyConfigList.stream()
                .distinct()
                .map(localAmountConfig -> LocalAmountVo.builder()
                        .currency(localAmountConfig.getCurrency())
                        .currencyAmount(localAmountConfig.getCurrencyAmount().stripTrailingZeros().toPlainString())
                        .coin(localAmountConfig.getCoin().stripTrailingZeros().stripTrailingZeros().toPlainString())
                        .build())
                .collect(Collectors.toList());
    }

    public Optional<String> getCurrencyByCountry(String country) {
        List<LocalCurrencyConfigPo> localCurrencyConfigList = this.queryLocalCurrencyList(country);
        return localCurrencyConfigList.stream().map(LocalCurrencyConfigPo::getCurrency).findFirst();
    }

    public PaymentMethodTypesDto getPaymentMethodTypes(long kugouId, String country, Pagination pagination) {
        Optional<String> optionalCurrency = this.getCurrencyByCountry(country);
        if (!optionalCurrency.isPresent()) {
            log.warn("Airwallex获取支付金额与方式列表，获取支付方式列表失败。country: {}, pagination: {}", country, pagination);
            return null;
        }
        String currency = optionalCurrency.get();
        // 注意⚠️：兼容Airwallex不支持缅甸国家代码MM
        if ("MM".equalsIgnoreCase(country)) {
            return this.apolloConfigService.getMMCardConfig();
        }
        PaymentMethodTypesDto paymentMethodTypesDto = this.kupayService.paymentMethodsAirwallexV1(country, currency, pagination);
        List<CountryInfoVo> countryInfoVoList = this.apolloConfigService.getOverseasCountryList();
        Optional<CountryInfoVo> optionalCountryInfoVo = countryInfoVoList.stream()
                .filter(countryInfoVo -> StringUtils.isNoneBlank(countryInfoVo.getCountry()))
                .filter(countryInfoVo -> countryInfoVo.getCountry().equalsIgnoreCase(country))
                .findFirst();
        if (!optionalCountryInfoVo.isPresent()) {
            log.warn("Airwallex获取支付金额与方式列表，获取国家配置列表失败 。country: {}, pagination: {}", country, pagination);
            return null;
        }
        List<PaymentMethodTypesDto.PaymentMethod> paymentMethods = paymentMethodTypesDto.getItems();
        List<String> paymentMethodNames = paymentMethods.stream()
                .map(PaymentMethodTypesDto.PaymentMethod::getName)
                .collect(Collectors.toList());
        Cat.logEvent("Airwallex", country + "-" + paymentMethodNames, Message.SUCCESS, null);
        log.warn("Airwallex获取支付金额与方式列表，过滤之前支付方式列表。country: {}, paymentMethodNames: {}", country, paymentMethodNames);
        CountryInfoVo countryInfoVo = optionalCountryInfoVo.get();
        if (this.apolloConfigService.isAirwallexAllowAccount(kugouId)) {
            log.warn("Airwallex获取支付金额与方式列表，验收账号返回全部支付方式。country: {}, paymentMethodNames: {}", country, paymentMethodNames);
            return paymentMethodTypesDto;
        }
        List<PaymentMethodTypesDto.PaymentMethod> filteredPaymentMethods = paymentMethods.stream()
                .filter(paymentMethod -> countryInfoVo.getAllowPaymentMethod().contains(paymentMethod.getName()))
                .collect(Collectors.toList());
        PaymentMethodTypesDto filtered = ModelUtils.fromUnchecked(paymentMethodTypesDto, PaymentMethodTypesDto.class);
        filtered.setItems(filteredPaymentMethods);
        return filtered;
    }


}
