package com.kugou.fanxing.recharge.service.refund;

import com.google.gson.JsonObject;
import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.recharge.alert.AlerterFacade;
import com.kugou.fanxing.recharge.alert.config.AlertConfig;
import com.kugou.fanxing.recharge.config.RefundConfig;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.DeductCoinSuccessCodeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.AppStoreRefundOrderDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossOrderNumDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundDeductCoinOrderDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundOrderDao;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.factory.ConsumeServiceFactory;
import com.kugou.fanxing.recharge.model.bo.AccountChangeTypeBO;
import com.kugou.fanxing.recharge.model.po.AppStoreRefundOrderPO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossOrdernumPO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.po.refund.RefundDeductCoinOrderPO;
import com.kugou.fanxing.recharge.model.po.refund.RefundOrderPO;
import com.kugou.fanxing.recharge.model.vo.UserEntity;
import com.kugou.fanxing.recharge.model.vo.UserLogVO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.RechargeOrderService;
import com.kugou.fanxing.recharge.service.RefundHandlerService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.consume.ConsumeReadService;
import com.kugou.fanxing.recharge.service.stat.UserEverRechargeStatService;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.thrift.consume.service.ConsumeResp;
import com.kugou.fanxing.thrift.freeze.service.CoinAdjustVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class RefundDeductCoinService {

    private static final int DEDUCT_COIN_ACCOUNT_CHANGE_TYPE = 980008;
    private static final int DEDUCT_COIN_CHECK_CONSUME_RESULT = 10018;
    @Autowired
    private ConsumeServiceFactory consumeServiceFactory;

    @Autowired
    private AppStoreRefundOrderDao appStoreRefundOrderDao;

    @Autowired
    private RechargeAcrossOrderNumDao rechargeAcrossOrderNumDao;

    @Autowired
    private OrderIdService orderIdService;

    @Autowired
    private RefundOrderDao refundOrderDao;

    @Autowired
    private AlerterFacade alerterFacade;

    @Autowired
    private UserEverRechargeStatService userEverRechargeStatService;

    @Autowired
    private RefundDeductCoinOrderDao refundDeductCoinOrderDao;

    @Autowired
    private RefundDeductCoinTransactionService refundDeductCoinTransactionService;

    @Autowired
    private ConsumeReadService consumeReadService;

    @Autowired
    private RefundConfig refundLogicService;

    @Autowired
    private ApolloConfigService apolloConfigService;

    @Autowired
    protected RechargeOrderService rechargeOrderService;

    @Autowired
    protected RefundHandlerService refundHandlerService;

    public int deductCoin(RefundDeductCoinOrderPO refundDeductCoinOrderPO) {
        log.warn("退款扣币开始，订单信息：{}",refundDeductCoinOrderPO);
        CoinAdjustVO coinAdjustVO = new CoinAdjustVO();
        coinAdjustVO.setAccountChangeType(DEDUCT_COIN_ACCOUNT_CHANGE_TYPE);
        JsonObject ext = new JsonObject();
        ext.addProperty("type",DEDUCT_COIN_ACCOUNT_CHANGE_TYPE);
        coinAdjustVO.setExt(ext.toString());
        coinAdjustVO.setFxcChangeDesc("退款扣币");
        coinAdjustVO.setGlobalId(refundDeductCoinOrderPO.getId());
        coinAdjustVO.setIp("0.0.0.0");
        coinAdjustVO.setKugouId(refundDeductCoinOrderPO.getKugouId());
        coinAdjustVO.setTimestamp((int) refundDeductCoinOrderPO.getAddTime());
        coinAdjustVO.setVirtualCoin(String.valueOf(refundDeductCoinOrderPO.getDeductCoin().negate()));
        AccountChangeTypeBO accountChangeTypeBO = apolloConfigService.getAccountChangeTypeById(DEDUCT_COIN_ACCOUNT_CHANGE_TYPE);
        if (accountChangeTypeBO == null || accountChangeTypeBO.getConsumeSalt() == null || accountChangeTypeBO.getConsumeSalt().isEmpty()) {
            log.error("退款扣币找不到accountChangeType salt，订单信息：{}",refundDeductCoinOrderPO);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        coinAdjustVO.setSign(FinanceSignUtils.makeSign(coinAdjustVO, accountChangeTypeBO.getConsumeSalt()));
        try {
            int coinType = refundDeductCoinOrderPO.getCoinType();
            ConsumeResp consumeResp = consumeServiceFactory.getAddCoinService(coinType).adminAdjustCoin(coinAdjustVO);
            log.warn("退款扣币，请求体：{},请求结果：{}",coinAdjustVO,consumeResp);
            return checkDeductResult(refundDeductCoinOrderPO,consumeResp);
        } catch (Exception e) {
            log.error("call adminAdjustCoin exception:", e);
        }
        return 2;
    }

    public int checkDeductResult(RefundDeductCoinOrderPO refundDeductCoinOrderPO,ConsumeResp consumeResp) {
        //明确成功
        if (DeductCoinSuccessCodeEnum.getByRet(consumeResp.getRet()) != null) {
            return 0;
        }else {
            //回查是否失败
            try {
                int coinType = refundDeductCoinOrderPO.getCoinType();
                Optional<UserLogVO> checkRs = consumeReadService.checkUserLog(coinType, DateHelper.formatYearMonth(refundDeductCoinOrderPO.getAddTime()*1000),refundDeductCoinOrderPO.getId(),DEDUCT_COIN_ACCOUNT_CHANGE_TYPE);
                //明确失败,无异常查不到日志
                if(!checkRs.isPresent() || checkRs.get().getUniqId().startsWith(String.valueOf(refundDeductCoinOrderPO.getId()))){
                    return 1;
                }
            }catch (Exception e){
                log.error("检查用户扣币记录失败：",e);
            }
            return 2;
        }
    }

    public boolean isBlackTransactionId(String id) {
        return refundLogicService.getBlackTransactionId().contains(id);
    }

    /**
     * 是否需要处理该退款订单：不在订单黑名单列表，开启灰度时，需要在灰度白名单kugouid列表
     *
     */
    public boolean needToHandleOrder(AppStoreRefundOrderPO appStoreRefundOrderPO) {
        if(isBlackTransactionId(appStoreRefundOrderPO.getTransactionId())){
            return false;
        }
        if(refundLogicService.isUserGrayLogicOpen() && !refundLogicService.getGrayKugouIdList().contains(String.valueOf(appStoreRefundOrderPO.getKugouId()))){
            return false;
        }
        return true;
    }

    public String makeRefundOrder(int scanBeforeDay) {
        try {
            scanBeforeDay = Math.abs(scanBeforeDay);
            Calendar calendar = Calendar.getInstance();//获取对日期操作的类对象
            //在当前时间的基础上获取前N天的日期
            calendar.add(Calendar.DATE, -scanBeforeDay);
            Date startDate = calendar.getTime();
            List<AppStoreRefundOrderPO> appStoreRefundOrderPOList = appStoreRefundOrderDao.selectByDate(startDate);
            String resultTemplate = "扫描了最近%d天的退款记录，共%d条退款记录,其中%d条找不到订单，%d条已经处理过，成功插入%d条，插入失败%d条";
            int orderNotFound = 0;
            int handled = 0;
            int success = 0;
            int fail = 0;
            for (AppStoreRefundOrderPO appStoreRefundOrderPO : appStoreRefundOrderPOList) {
                if(!needToHandleOrder(appStoreRefundOrderPO)){
                    log.warn("无需处理的退款订单：{}",appStoreRefundOrderPO);
                    continue;
                }
                Optional<String> rechargeOrderNum = getRechargeOrderNumByTransactionId(appStoreRefundOrderPO.getTransactionId());
                if (!rechargeOrderNum.isPresent()) {
                    alerterFacade.sendRTX(AlertConfig.RtxConfigEnum.DEFAULT_RTX_CONFIG, String.format("苹果退款订单找不到对应的充值订单号，苹果订单id：%s", appStoreRefundOrderPO.getTransactionId()));
                    orderNotFound++;
                    continue;
                }
                RefundOrderPO refundOrderPOOld = refundOrderDao.getByRechargeOrderNum(rechargeOrderNum.get());
                if (refundOrderPOOld != null) {
                    handled++;
                    continue;
                }
                RefundOrderPO refundOrderPO = new RefundOrderPO();
                refundOrderPO.setAddTime(DateHelper.getCurrentSeconds());
                refundOrderPO.setCoin(appStoreRefundOrderPO.getCoin());
                refundOrderPO.setDeductCoin(BigDecimal.ZERO);
                long globalId = orderIdService.generateGlobalId();
                refundOrderPO.setId(globalId);
                refundOrderPO.setRechargeOrderNum(rechargeOrderNum.get());
                refundOrderPO.setUpdateTime(DateHelper.getCurrentSeconds());
                refundOrderPO.setKugouId(appStoreRefundOrderPO.getKugouId());
                refundOrderPO.setCancelTime(appStoreRefundOrderPO.getCancellationDate().getTime()/1000);
                refundOrderPO.setPrice(appStoreRefundOrderPO.getPrice());
                refundOrderPO.setCoinType(CoinTypeEnum.getByProductType(appStoreRefundOrderPO.getProductType()));
                refundOrderPO.setNotifyStatus(0);
                //这里获取不到就按默认参数
                Optional<RechargeAcrossPO> optionalRechargeAcrossPO = rechargeOrderService.queryByRechargeOrderNum(refundOrderPO.getRechargeOrderNum());
                if(optionalRechargeAcrossPO.isPresent()){
                    RechargeAcrossPO rechargeAcrossPO = optionalRechargeAcrossPO.get();
                    refundOrderPO.setBusinessId(rechargeAcrossPO.getBusinessId());
                    refundOrderPO.setHandlerType(refundHandlerService.getHandlerType(rechargeAcrossPO.getBusinessId()));
                }else{
                    refundOrderPO.setBusinessId(null);
                    refundOrderPO.setHandlerType(0);
                }
                int rs = refundOrderDao.insert(refundOrderPO);
                if (rs > 0) {
                    success++;
                } else {
                    log.warn("苹果退款订单插入退款订单表失败，苹果订单:{}",appStoreRefundOrderPO);
                    alerterFacade.sendRTX(AlertConfig.RtxConfigEnum.DEFAULT_RTX_CONFIG, String.format("苹果退款订单插入退款订单表失败，苹果订单id：%s", appStoreRefundOrderPO.getTransactionId()));
                    fail++;
                }
            }
            return String.format(resultTemplate, scanBeforeDay, appStoreRefundOrderPOList.size(), orderNotFound, handled, success, fail);
        } catch (Exception e) {
            log.error("生成退款订单失败：", e);
            return "执行失败";
        }
    }

    public String handleUnfinishRefundOrder() {
        //先看看是否有未完成的订单
        List<RefundDeductCoinOrderPO> refundDeductCoinOrderPOList = refundDeductCoinOrderDao.getUnfinishOrder(0);
        if (refundDeductCoinOrderPOList != null && !refundDeductCoinOrderPOList.isEmpty()) {
            for (RefundDeductCoinOrderPO refundDeductCoinOrderPO : refundDeductCoinOrderPOList) {
                handleDeductCoinOrder(refundDeductCoinOrderPO);
            }
        }

        long startId = 0;
        int limit = 500;
        int totalRecord = 0;
        BigDecimal total = new BigDecimal("0");
        long startTime = 0L;
        long endTime = DateHelper.getCurrentSeconds();
        while (true) {
            List<RefundOrderPO> refundOrderPOList = refundOrderDao.getUnfinishList(startTime, endTime, startId, limit);
            if (refundOrderPOList == null || refundOrderPOList.isEmpty()) {
                break;
            }
            totalRecord += refundOrderPOList.size();
            for (RefundOrderPO refundOrderPO : refundOrderPOList) {
                total = total.add(handleRefundOrder(refundOrderPO));
                startId = refundOrderPO.getId();
            }
            if (refundOrderPOList.size() < limit) {
                break;
            }
        }
        return String.format("共处理%d条记录，共成功扣回%s星币", totalRecord, total);
    }

    public BigDecimal handleRefundOrder(RefundOrderPO refundOrderPO) {
        //再去取一次主库
        refundOrderPO = refundOrderDao.getByRechargeOrderNum(refundOrderPO.getRechargeOrderNum());

        if (refundOrderPO == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal needToDeduct = refundOrderPO.getCoin().subtract(refundOrderPO.getDeductCoin());

        if (needToDeduct.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal leftCoin = getUserLeftCoin(refundOrderPO.getKugouId(), refundOrderPO.getCoinType());
        if (leftCoin.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        if (needToDeduct.compareTo(leftCoin) > 0) {
            needToDeduct = leftCoin;
        }

        RefundDeductCoinOrderPO refundDeductCoinOrderPO = new RefundDeductCoinOrderPO();
        refundDeductCoinOrderPO.setAddTime(DateHelper.getCurrentSeconds());
        refundDeductCoinOrderPO.setDeductCoin(needToDeduct);
        refundDeductCoinOrderPO.setId(orderIdService.generateGlobalId());
        refundDeductCoinOrderPO.setKugouId(refundOrderPO.getKugouId());
        refundDeductCoinOrderPO.setRechargeOrderNum(refundOrderPO.getRechargeOrderNum());
        refundDeductCoinOrderPO.setCoinType(refundOrderPO.getCoinType());
        refundDeductCoinOrderPO.setRetryTimes(0);
        refundDeductCoinOrderPO.setStatus(0);
        refundDeductCoinOrderPO.setUpdateTime(DateHelper.getCurrentSeconds());
        boolean rs = refundDeductCoinTransactionService.createDeductCoinOrder(refundDeductCoinOrderPO);
        if (rs && handleDeductCoinOrder(refundDeductCoinOrderPO)) {
            return needToDeduct;
        }
        return BigDecimal.ZERO;
    }

    public boolean handleDeductCoinOrder(RefundDeductCoinOrderPO po) {
        int rs = deductCoin(po);
        if (rs == 0) {
            int updateRs = refundDeductCoinOrderDao.updateByPrimay(po.getId(), 1);
            if (updateRs > 0) {

                //发送站内信
                //发送短信
                sendTextAndSystemMsgToUser(po);
            }
        } else if (rs == 1) {
            refundDeductCoinTransactionService.deductCoinFail(po);
            return false;
        }
        return true;
    }

    public void sendTextAndSystemMsgToUser(RefundDeductCoinOrderPO po) {
        if(po == null){
            return;
        }
        RefundOrderPO refundOrderPO = refundOrderDao.getByRechargeOrderNum(po.getRechargeOrderNum());
        if(refundOrderPO == null){
            return;
        }
        try {
            String systemMsgContent = String.format("你于%s申请的%s元退款已受理，需扣除账户内%s星币，本次已扣除%s星币，还剩余%s星币后续扣除。",DateHelper.getDateTimeForamtFroUser(refundOrderPO.getCancelTime()),refundOrderPO.getPrice(),refundOrderPO.getCoin(),po.getDeductCoin(),refundOrderPO.getCoin().subtract(refundOrderPO.getDeductCoin()));
            if(refundOrderPO.getCoin().equals(po.getDeductCoin())){
                systemMsgContent = String.format("你于%s申请的%s元退款已受理，需扣除账户内%s星币，已扣除完成",DateHelper.getDateTimeForamtFroUser(refundOrderPO.getCancelTime()),refundOrderPO.getPrice(),refundOrderPO.getCoin());
            }
            if (CoinTypeEnum.isSingCoinType(po.getCoinType())) {
                systemMsgContent = systemMsgContent.replace("星币", "唱币");
            }
            alerterFacade.sendMSG("退款成功", systemMsgContent, refundOrderPO.getKugouId());
            alerterFacade.sendSMS("【酷狗直播】", systemMsgContent, refundOrderPO.getKugouId());
        }catch (Exception e){
            log.error("发送站内信及短信通知用户失败，具体异常：",e);
        }

    }

    public BigDecimal getUserLeftCoin(long kugouId, int coinType) {
        try {
            Optional<UserEntity> userEntity;
            if (CoinTypeEnum.isSingCoinType(coinType)) {
                userEntity = userEverRechargeStatService.getUserEntity(kugouId, 10020, CoinTypeEnum.SING_COIN.getCoinType());
            } else {
                userEntity = userEverRechargeStatService.getUserEntity(kugouId, DEDUCT_COIN_CHECK_CONSUME_RESULT);
            }
            if (userEntity.isPresent()) {
                return BigDecimal.valueOf(userEntity.get().getCoin());
            }
        } catch (Exception e) {
            log.error("获取用户星币余额异常：", e);
        }
        return BigDecimal.ZERO;
    }


    public Optional<String> getRechargeOrderNumByTransactionId(String transactionId) {
        if(!transactionId.startsWith("TID")){
            transactionId = "TID" + transactionId;
        }
        Optional<String> orderNum = rechargeOrderService.queryRechargeOrderNumByTradeNo(transactionId);
        if(orderNum.isPresent()) {
            return orderNum;
        }
        RechargeAcrossOrdernumPO rechargeAcrossOrdernumPO = rechargeAcrossOrderNumDao.getByPrimary(transactionId);
        if (rechargeAcrossOrdernumPO != null && rechargeAcrossOrdernumPO.getRechargeOrderNum() != null && !rechargeAcrossOrdernumPO.getRechargeOrderNum().isEmpty()) {
            return Optional.of(rechargeAcrossOrdernumPO.getRechargeOrderNum());
        }
        return Optional.empty();
    }
}
