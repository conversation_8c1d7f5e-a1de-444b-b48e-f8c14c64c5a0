package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.star.api.StarInfoResp;
import com.kugou.fanxing.star.api.StarQueryService;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Optional;

/**
 * 获取主播信息
 */
@Slf4j
public class GetStarInfoCommand extends HystrixCommand<Optional<StarInfoResp>> {

    private final StarQueryService.Iface starQueryService;
    private final long kugouId;

    public GetStarInfoCommand(final StarQueryService.Iface starQueryService, final long kugouId) {
        super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("GetStarInfoCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(300)));

        this.starQueryService = starQueryService;
        this.kugouId = kugouId;
    }

    @Override
    protected Optional<StarInfoResp> run() throws Exception {
        return Optional.of(this.starQueryService.getStarInfo(kugouId));
    }

    @Override
    protected Optional<StarInfoResp> getFallback() {
        Optional<StarInfoResp> fallback = Optional.empty();
        log.warn("GetStarInfoCommand 服务降级! 调用主播信息出错, kugouId: {}! 降级返回数据: {}, 降级原因: {}",
                kugouId, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }

}
