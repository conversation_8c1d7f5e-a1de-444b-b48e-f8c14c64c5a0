package com.kugou.fanxing.recharge.service.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.kugou.fanxing.biz.commons.consume.api.CostReturn;
import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.recharge.factory.ConsumeServiceFactory;
import com.kugou.fanxing.recharge.constant.ConsumeResponseEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.model.bo.AccountChangeTypeBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.po.RechargeCouponPO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.thrift.callback.ConsumeParam;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.ExtendParseUtils;
import com.kugou.fanxing.recharge.util.IpUtils;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.fanxing.thrift.consume.service.ConsumeResp;
import com.kugou.fanxing.thrift.consume.service.PlatformConsumeService;
import com.kugou.fanxing.thrift.consume.service.RechargeFeeVO;
import com.kugou.fanxing.thrift.freeze.service.CoinVO;
import com.kugou.fanxing.thrift.freeze.service.PlatformAddCoinService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 消费服务
 */
@Slf4j
@Service
public class ConsumeRpcService {

    @Autowired
    private OrderIdService orderIdService;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    @Qualifier("platformAddCoinService")
    private PlatformAddCoinService.Iface platformAddCoinService;
    @Autowired
    @Qualifier("platformConsumeService")
    private PlatformConsumeService.Iface platformConsumeService;
    @Autowired
    private ConsumeServiceFactory consumeServiceFactory;

    public boolean rechargeCoin(RechargeAcrossPO callbackOrder) {
        try {
            CoinVO coinVO = buildRechargeAddCoinVO(callbackOrder);
            log.warn("调用消费服务充值加星币，请求参数。coinVO: {}", coinVO);
            ConsumeResp consumeResp = consumeServiceFactory.getAddCoinService(callbackOrder.getCoinType()).recharge(coinVO);
            if (Objects.isNull(consumeResp) || !ConsumeResponseEnum.isSuccessRetCode(consumeResp.getRet()) || Objects.isNull(consumeResp.getData())) {
                log.error("调用消费服务充值加星币，调用失败。coinVO: {}, consumeResp: {}", coinVO, consumeResp);
                return false;
            }
            log.error("调用消费服务充值加星币，调用成功。coinVO: {}, consumeResp: {}", coinVO, consumeResp);
            return true;
        } catch (Exception e) {
            log.error("调用消费服务充值加星币接口，调用异常。callbackOrder: {}", callbackOrder, e);
        }
        return false;
    }

    /**
     * 代金券赠币
     *
     * @param rechargeOrderNum 充值交易号
     * @param rechargeCouponPO 代金券订单
     * @param coupon           代金券金额（元）
     * @return 是否赠币成功
     */
    public Optional<CostReturn> presentCouponCoin(String rechargeOrderNum, RechargeCouponPO rechargeCouponPO, BigDecimal coupon) {
        CoinVO coinVO = buildCouponPresentCoinVO(rechargeCouponPO, coupon.multiply(BigDecimal.valueOf(100)));
        try {
            ConsumeResp consumeResp = this.platformAddCoinService.present(coinVO);
            if (Objects.isNull(consumeResp) || !ConsumeResponseEnum.isSuccessRetCode(consumeResp.getRet()) || Objects.isNull(consumeResp.getData())) {
                log.error("调用消费发放代金券赠币，调用失败。rechargeOrderNum: {}, coinVO: {}, consumeResp: {}", rechargeOrderNum, coinVO, consumeResp);
                return Optional.empty();
            }
            log.warn("调用消费发放代金券赠币，调用成功。rechargeOrderNum: {}, coinVO: {}, consumeResp: {}", rechargeOrderNum, coinVO, consumeResp);
            CostReturn costReturn = JSON.parseObject(consumeResp.getData(), CostReturn.class);
            return Optional.of(costReturn);
        } catch (Exception e) {
            log.error("调用消费发放代金券赠币，调用异常。rechargeOrderNum: {}, coinVO: {}", rechargeOrderNum, coinVO, e);
            return Optional.empty();
        }
    }

    public boolean rechargeFee(String rechargeOrderNum, int addTime, String extend, int coinType) {
        return this.rechargeFee(parseRechargeFeeVOByExtend(rechargeOrderNum, addTime, extend), coinType);
    }

    public boolean rechargeFee(String rechargeOrderNum, int addTime, String extend) {
        return this.rechargeFee(parseRechargeFeeVOByExtend(rechargeOrderNum, addTime, extend));
    }

    public boolean rechargeFee(RechargeAcrossPO callbackOrder, ConsumeParam consumeParam, String clientIp) {
        return this.rechargeFee(buildRechargeFeeVO(callbackOrder, consumeParam, clientIp));
    }

    public boolean rechargeFee(RechargeFeeVO rechargeFeeVO, int coinType) {
        try {
            ConsumeResp consumeResp = consumeServiceFactory.getConsumeService(coinType).rechargeFee(rechargeFeeVO);
            if (Objects.nonNull(consumeResp) && ConsumeResponseEnum.isSuccessPayment(consumeResp.getRet())) {
                log.error("调用消费服务充扣购买接口，调用成功。rechargeFeeVO: {}, consumeResp: {}", rechargeFeeVO, consumeResp);
                return true;
            }
            log.error("调用消费服务充扣购买接口，调用失败。rechargeFeeVO: {}, consumeResp: {}", rechargeFeeVO, consumeResp);
        } catch (Exception e) {
            log.error("调用消费服务充扣购买接口，调用异常。rechargeFeeVO: {}", rechargeFeeVO, e);
        }
        return false;
    }

    public boolean rechargeFee(RechargeFeeVO rechargeFeeVO) {
        try {
            ConsumeResp consumeResp = this.platformConsumeService.rechargeFee(rechargeFeeVO);
            if (Objects.nonNull(consumeResp) && ConsumeResponseEnum.isSuccessPayment(consumeResp.getRet())) {
                log.error("调用消费服务充扣购买接口，调用成功。rechargeFeeVO: {}, consumeResp: {}", rechargeFeeVO, consumeResp);
                return true;
            }
            log.error("调用消费服务充扣购买接口，调用失败。rechargeFeeVO: {}, consumeResp: {}", rechargeFeeVO, consumeResp);
        } catch (Exception e) {
            log.error("调用消费服务充扣购买接口，调用异常。rechargeFeeVO: {}", rechargeFeeVO, e);
        }
        return false;
    }

    public RechargeFeeVO parseRechargeFeeVOByExtend(String rechargeOrderNum, int addTime, String extend) {
        int accountChangeType = ExtendParseUtils.getCallbackArgUserFundPlatParamAccountChangeType(extend);
        AccountChangeTypeBO accountChangeTypeBO = this.apolloConfigService.getAccountChangeTypeById(accountChangeType);
        BigDecimal coin = ExtendParseUtils.getCallbackArgUserFundPlatParamCoin(extend, BigDecimal.ZERO);
        String areaCode = ExtendParseUtils.getCallBackArgAreaCode(extend);
        String timeZone = ExtendParseUtils.getCallBackArgTimeZone(extend);
        String lang = ExtendParseUtils.getCallBackArgLang(extend);
        RechargeFeeVO rechargeFeeVO = new RechargeFeeVO();
        rechargeFeeVO.setGlobalId(ExtendParseUtils.getCallbackArgUserFundPlatParamGlobalId(extend));
        rechargeFeeVO.setTimestamp(addTime);
        rechargeFeeVO.setExt(relatedRechargeOrderNum(rechargeOrderNum, ExtendParseUtils.getCallbackArgUserFundPlatParamExt(extend), areaCode, timeZone, lang));
        rechargeFeeVO.setPid(ExtendParseUtils.getCallbackArgUserFundPlatParamPid(extend));
        rechargeFeeVO.setAccountChangeType(accountChangeType);
        rechargeFeeVO.setFxcChangeDesc(ExtendParseUtils.getCallbackArgUserFundPlatParamFxcChangeDesc(extend));
        rechargeFeeVO.setCoin(coin.stripTrailingZeros().doubleValue());
        rechargeFeeVO.setFromKugouId(ExtendParseUtils.getCallbackArgUserFundPlatParamFromKugouId(extend));
        rechargeFeeVO.setToKugouId(ExtendParseUtils.getCallbackArgUserFundPlatParamToKugouId(extend));
        rechargeFeeVO.setRoomId(ExtendParseUtils.getCallbackArgUserFundPlatParamRoomId(extend));
        rechargeFeeVO.setIp(ExtendParseUtils.getCallbackArgUserFundPlatParamIp(extend));
        rechargeFeeVO.setGiftId(ExtendParseUtils.getCallbackArgUserFundPlatParamGiftId(extend));
        rechargeFeeVO.setGiftName(ExtendParseUtils.getCallbackArgUserFundPlatParamGiftName(extend));
        rechargeFeeVO.setGiftNum(ExtendParseUtils.getCallbackArgUserFundPlatParamGiftNum(extend));
        rechargeFeeVO.setActionId(ExtendParseUtils.getCallbackArgUserFundPlatParamActionId(extend));
        rechargeFeeVO.setSenderDepartmentId(0);
        rechargeFeeVO.setSenderProductId(0);
        rechargeFeeVO.setSenderMinorProductId(0);
        rechargeFeeVO.setSenderHardwarePlatform(0);
        rechargeFeeVO.setSenderChannelId(0);
        rechargeFeeVO.setSenderSubChannelId(0);
        rechargeFeeVO.setReceiverDepartmentId(0);
        rechargeFeeVO.setSign(FinanceSignUtils.makeSign(rechargeFeeVO, accountChangeTypeBO.getConsumeSalt()));
        log.warn("充值购买物品，调用消费充扣参数。rechargeFeeVO：{}", rechargeFeeVO);
        return rechargeFeeVO;
    }

    private RechargeFeeVO buildRechargeFeeVO(RechargeAcrossPO callbackOrder, ConsumeParam consumeParam, String clientIp) {
        AccountChangeTypeBO accountChangeTypeBO = this.apolloConfigService.getAccountChangeTypeById(consumeParam.getAccountChangeType());
        long consumeGlobalId = JsonUtils.parseJsonPathChecked(callbackOrder.getExtraJsonData(), "$.businessId", Long.class);
        RechargeFeeVO rechargeFeeVO = new RechargeFeeVO();
        rechargeFeeVO.setGlobalId(consumeGlobalId);
        rechargeFeeVO.setPid(callbackOrder.getCFrom());
        rechargeFeeVO.setSenderDepartmentId(0);
        rechargeFeeVO.setSenderProductId(0);
        rechargeFeeVO.setSenderMinorProductId(0);
        rechargeFeeVO.setSenderHardwarePlatform(0);
        rechargeFeeVO.setSenderChannelId(0);
        rechargeFeeVO.setSenderSubChannelId(0);
        rechargeFeeVO.setReceiverDepartmentId(0);
        rechargeFeeVO.setAccountChangeType(consumeParam.getAccountChangeType());
        rechargeFeeVO.setFxcChangeDesc(consumeParam.getFxcChangeDesc());
        rechargeFeeVO.setCoin(callbackOrder.getCoin().doubleValue());
        rechargeFeeVO.setFromKugouId(consumeParam.getFromKugouId());
        rechargeFeeVO.setToKugouId(consumeParam.getToKugouId());
        rechargeFeeVO.setRoomId(consumeParam.getRoomId());
        rechargeFeeVO.setIp(clientIp);
        rechargeFeeVO.setGiftId(consumeParam.getGiftId());
        rechargeFeeVO.setGiftName(consumeParam.getGiftName());
        rechargeFeeVO.setGiftNum(consumeParam.getGiftNum());
        rechargeFeeVO.setActionId(accountChangeTypeBO.getActionId());
        rechargeFeeVO.setTimestamp(callbackOrder.getAddTime());
        rechargeFeeVO.setExt(relatedRechargeOrderNum(callbackOrder.getRechargeOrderNum(), consumeParam.getExt(), callbackOrder.getAreaCode(), callbackOrder.getTimeZone(), ""));
        rechargeFeeVO.setSign(FinanceSignUtils.makeSign(rechargeFeeVO, accountChangeTypeBO.getConsumeSalt()));
        return rechargeFeeVO;
    }

    /**
     * 构建EXTEND字段的UserFundPlatParam参数
     * <pre>
     * "receiverDepartmentId":"1",
     * "actionId":"300",
     * "giftId":"1979",
     * "senderDepartmentId":"1",
     * "coin":"66",
     * "senderMinorProductId":"3",
     * "senderProductId":"0",
     * "senderChannelId":"1009",
     * "accountChangeType":"110224",
     * "senderSubChannelId":"0",
     * "toKugouId":"0",
     * "fxcChangeDesc":"撩主播礼物",
     * "giftNum":"1",
     * "giftName":"撩主播礼物",
     * "fromKugouId":"*********",
     * "roomId":"1074292",
     * "senderHardwarePlatform":"4",
     * "ext":"{\"accountChangeType\":0,\"goodsList\":[{\"goodsId\":1979,\"goodsNum\":1,\"goodsType\":3},{\"goodsId\":0,\"goodsNum\":56,\"goodsType\":2}],\"activityType\":2,\"roomId\":1074292,\"rechargeId\":\"R09202105S1TID1000000811478007\"}",
     * "timestamp":**********,
     * "globalId":"1503796271234859008",
     * "pid":2,
     * "ip":"***************",
     * "sign":"f1bcd9201c6b785026ed531843242e97"
     * </pre>
     * @param callbackOrder 充值到账流水
     * @param consumeParam  消费参数
     * @param clientIp      客户端IP
     * @return  消费透传参数
     */
    public Map<String, String> buildIosPurchaseUserFundPlatParam(RechargeAcrossPO callbackOrder, ConsumeParam consumeParam, String clientIp) {
        RechargeFeeVO rechargeFeeVO = this.buildRechargeFeeVO(callbackOrder, consumeParam, clientIp);
        Map<String, String> userFundPlatParam = Maps.newLinkedHashMap();
        userFundPlatParam.put("receiverDepartmentId", String.valueOf(rechargeFeeVO.getReceiverDepartmentId()));
        userFundPlatParam.put("actionId", String.valueOf(rechargeFeeVO.getActionId()));
        userFundPlatParam.put("giftId", String.valueOf(rechargeFeeVO.getGiftId()));
        userFundPlatParam.put("senderDepartmentId", String.valueOf(rechargeFeeVO.getSenderDepartmentId()));
        userFundPlatParam.put("coin", BigDecimal.valueOf(rechargeFeeVO.getCoin()).stripTrailingZeros().toPlainString());
        userFundPlatParam.put("senderMinorProductId", String.valueOf(rechargeFeeVO.getSenderMinorProductId()));
        userFundPlatParam.put("senderProductId", String.valueOf(rechargeFeeVO.getSenderProductId()));
        userFundPlatParam.put("senderChannelId", String.valueOf(rechargeFeeVO.getSenderChannelId()));
        userFundPlatParam.put("accountChangeType", String.valueOf(rechargeFeeVO.getAccountChangeType()));
        userFundPlatParam.put("senderSubChannelId", String.valueOf(rechargeFeeVO.getSenderSubChannelId()));
        userFundPlatParam.put("toKugouId", String.valueOf(rechargeFeeVO.getToKugouId()));
        userFundPlatParam.put("fxcChangeDesc", String.valueOf(rechargeFeeVO.getFxcChangeDesc()));
        userFundPlatParam.put("giftNum", String.valueOf(rechargeFeeVO.getGiftNum()));
        userFundPlatParam.put("giftName", String.valueOf(rechargeFeeVO.getGiftName()));
        userFundPlatParam.put("fromKugouId", String.valueOf(rechargeFeeVO.getFromKugouId()));
        userFundPlatParam.put("roomId", String.valueOf(rechargeFeeVO.getRoomId()));
        userFundPlatParam.put("senderHardwarePlatform", String.valueOf(rechargeFeeVO.getSenderHardwarePlatform()));
        userFundPlatParam.put("ext", rechargeFeeVO.getExt());
        userFundPlatParam.put("timestamp", String.valueOf(rechargeFeeVO.getTimestamp()));
        userFundPlatParam.put("globalId", String.valueOf(rechargeFeeVO.getGlobalId()));
        userFundPlatParam.put("pid", String.valueOf(rechargeFeeVO.getPid()));
        userFundPlatParam.put("ip", String.valueOf(rechargeFeeVO.getIp()));
        userFundPlatParam.put("sign", rechargeFeeVO.getSign());
        return userFundPlatParam;
    }

    public String relatedRechargeOrderNum(String rechargeOrderNum, String ext, String areaCode, String timeZone, String lang) {
        JSONObject jsonObject = JSON.parseObject(ext);
        jsonObject = Objects.nonNull(jsonObject) ? jsonObject : new JSONObject();
        jsonObject.put("rechargeId", rechargeOrderNum);
        Map<String, String> istar = Maps.newHashMap();
        istar.put("area_code", StringUtils.defaultString(areaCode));
        istar.put("time_zone", StringUtils.defaultString(timeZone));
        istar.put("lang", StringUtils.defaultString(lang));
        jsonObject.put("istar", JSON.toJSONString(istar));
        return JSON.toJSONString(jsonObject);
    }

    private CoinVO buildCouponPresentCoinVO(RechargeCouponPO rechargeCouponPO, BigDecimal presentCoin) {
        String fxChangeDesc = String.format("充值代金券赠币|订单号:%s|代金券ID:%s|幂等ID:%s",
                rechargeCouponPO.getRechargeOrderNum(), rechargeCouponPO.getCouponId(), rechargeCouponPO.getGlobalId());
        CoinVO coinVO = new CoinVO();
        coinVO.setAccountChangeType(132041);
        coinVO.setFxcChangeDesc(fxChangeDesc);
        coinVO.setActualCoin("0");
        coinVO.setVirtualCoin(presentCoin.toPlainString());
        coinVO.setGlobalId(rechargeCouponPO.getCouponOrderId());
        coinVO.setToKugouId(rechargeCouponPO.getKugouId());
        coinVO.setAppid(0);
        coinVO.setToken("");
        coinVO.setIp(IpUtils.getClientIpAddress());
        coinVO.setUserAgent(coinVO.getAccountChangeType() + ";" + coinVO.getIp());
        coinVO.setExt(JSON.toJSONString(ImmutableMap.<String, Object>builder().put("type", 132041).build()));
        coinVO.setTimestamp(rechargeCouponPO.getAddTime() > 0 ? rechargeCouponPO.getAddTime() : DateHelper.getCurrentSeconds());
        coinVO.setSign(FinanceSignUtils.makeSign(coinVO, couponPresentSignKey()));
        return coinVO;
    }

    private String couponPresentSignKey() {
        return this.apolloConfigService.couponPresentSignKey();
    }

    public CoinVO buildRechargeAddCoinVO(RechargeAcrossPO callbackOrder) {
        int category = 10;
        int accountChangeType = 100001;
        String changeDesc = this.getFxcChangeDesc(category, accountChangeType, callbackOrder.getRechargeOrderNum(), callbackOrder.getConsumeOrderNum());
        CoinVO coinVO = new CoinVO();
        coinVO.setGlobalId(orderIdService.generateGlobalId());
        coinVO.setAccountChangeType(accountChangeType);
        coinVO.setFxcChangeDesc(changeDesc);
        coinVO.setActualCoin(callbackOrder.getRealAmount().toPlainString());
        coinVO.setToKugouId(callbackOrder.getKugouId());
        coinVO.setVirtualCoin("0");
        coinVO.setToken("");
        coinVO.setAppid(0);
        coinVO.setUserAgent(accountChangeType + ":" + IpUtils.getClientIpAddress());
        coinVO.setIp(IpUtils.getClientIpAddress());
        // 注意⚠️：充值调用消费幂等处理，必须保证(addTime、ext)参数合法性
        if (callbackOrder.getAddTime() <= 0 || StringUtils.isBlank(callbackOrder.getRechargeOrderNum())) {
            log.error("调用消费服务充值加星币，充值幂等参数非法。callbackOrder: {}", callbackOrder);
            throw new AckException(SysResultCode.E_10000032)
                    .addContextValue("rechargeOrderNum", callbackOrder.getRechargeOrderNum())
                    .addContextValue("addTime", callbackOrder.getAddTime());
        }
        coinVO.setTimestamp(callbackOrder.getAddTime());
        coinVO.setExt(JSON.toJSONString(ImmutableMap.<String, Object>builder().put("rechargeId", callbackOrder.getRechargeOrderNum()).build()));
        coinVO.setSign(FinanceSignUtils.makeSign(coinVO, "czjb"));
        return coinVO;
    }

    public String getFxcChangeDesc(int category, int accountChangeType, String rechargeOrderNum, String consumeOrderNum) {
        String changeDesc = "";
        Map<Integer, String> categoryList = this.getCategoryList();
        Map<Integer, String> typeList = this.getTypeList();
        Map<Integer, String> fxcChangeDescList = this.getFxcChangeDescList();

        if (categoryList.containsKey(category)) {
            changeDesc += categoryList.get(category);
        }

        if (typeList.containsKey(accountChangeType)) {
            if (StringUtils.isNotBlank(changeDesc)) {
                changeDesc = changeDesc + "|" + typeList.get(accountChangeType);
            } else {
                changeDesc = typeList.get(accountChangeType);
            }
        }
        if (fxcChangeDescList.containsKey(accountChangeType) && StringUtils.isNotBlank(fxcChangeDescList.get(accountChangeType))) {
            if (StringUtils.isNotBlank(changeDesc)) {
                changeDesc = changeDesc + "|" + String.format(fxcChangeDescList.get(accountChangeType), rechargeOrderNum, StringUtils.defaultString(consumeOrderNum));
            } else {
                changeDesc = String.format(fxcChangeDescList.get(accountChangeType), rechargeOrderNum, StringUtils.defaultString(consumeOrderNum));
            }
        }
        return changeDesc;
    }

    private Map<Integer, String> getCategoryList() {
        return ImmutableMap.<Integer, String>builder()
                .put(10, "充值")
                .put(11, "消费")
                .put(12, "退币")
                .put(13, "赠币")
                .put(20, "星豆")
                .put(21, "转账")
                .put(30, "冻结")
                .put(98, "管理员调整")
                .put(99, "其它")
                .build();
    }

    private Map<Integer, String> getTypeList() {
        return ImmutableMap.<Integer, String>builder()
                // 10起始，充值相关
                .put(100001, "充值")
                .build();
    }

    private Map<Integer, String> getFxcChangeDescList() {
        return ImmutableMap.<Integer, String>builder()
                // 10起始，充值相关
                .put(100001, "充值订单号:%s|消费订单号:%s")
                .build();
    }

}
