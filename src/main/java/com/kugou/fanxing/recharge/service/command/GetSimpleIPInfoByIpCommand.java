package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.ip.api.FXIpService;
import com.kugou.fanxing.ip.api.IPInfoReq;
import com.kugou.fanxing.ip.api.SimpleIPInfoResp;
import com.kugou.fanxing.ip.api.SimpleIpInfo;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Objects;
import java.util.Optional;

/**
 * 获取IP地域信息
 *
 * <AUTHOR>
 */
@Slf4j
public class GetSimpleIPInfoByIpCommand extends HystrixCommand<Optional<SimpleIpInfo>> {

    private final String ip;
    private final FXIpService.Iface fxIpService;

    public GetSimpleIPInfoByIpCommand(FXIpService.Iface fxIpService, String ip) {
        super(HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("GetSimpleIPInfoByIpCommand")).
                andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter().withMaxQueueSize(500)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(30))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(300)));
        this.fxIpService = fxIpService;
        this.ip = ip;
    }

    @Override
    protected Optional<SimpleIpInfo> run() throws Exception {
        IPInfoReq ipInfoReq = new IPInfoReq()
                .setAppId(1007)
                .setIp(ip);
        SimpleIPInfoResp simpleIPInfoResp = fxIpService.getSimpleIPInfoByIp(ipInfoReq);
        if (Objects.isNull(simpleIPInfoResp) || simpleIPInfoResp.getCode() != 0 || Objects.isNull(simpleIPInfoResp.getSimpleIpInfo())) {
            log.error("批量查询用户酷狗ID信息失败, ipInfoReq: {}, simpleIPInfoResp: {}", ipInfoReq, simpleIPInfoResp);
            return Optional.empty();
        }
        SimpleIpInfo simpleIpInfo = simpleIPInfoResp.getSimpleIpInfo();
        return Optional.of(simpleIpInfo);
    }

    @Override
    protected Optional<SimpleIpInfo> getFallback() {
        Optional<SimpleIpInfo> fallback = Optional.empty();
        log.warn("GetSimpleIPInfoByIpCommand服务降级! fallback: {}, ip: {}, 降级原因: {}",
                fallback, ip, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }
}
