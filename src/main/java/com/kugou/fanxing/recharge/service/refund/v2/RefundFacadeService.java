package com.kugou.fanxing.recharge.service.refund.v2;

import com.google.common.collect.Lists;
import com.kugou.fanxing.recharge.alert.AlerterFacade;
import com.kugou.fanxing.recharge.constant.AdminAdjustCoinResult;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundDebtV2Dao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundDeductOrderV2Dao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundOrderV2Dao;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.po.RefundDebtV2Po;
import com.kugou.fanxing.recharge.model.po.RefundDeductOrderV2Po;
import com.kugou.fanxing.recharge.model.po.RefundOrderV2Po;
import com.kugou.fanxing.recharge.service.RechargeOrderService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.Pagination;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class RefundFacadeService {

    @Autowired
    private RefundDebtV2Dao refundDebtV2Dao;
    @Autowired
    private RefundOrderV2Dao refundOrderV2Dao;
    @Autowired
    private RefundDeductOrderV2Dao refundDeductOrderV2Dao;
    @Autowired
    private RechargeOrderService rechargeOrderService;
    @Autowired
    private AlerterFacade alerterFacade;
    @Autowired
    protected OrderIdService orderIdService;
    @Autowired
    protected RefundDeductService refundDeductService;
    @Autowired
    protected UserFacadeService userFacadeService;

    /**
     * 各渠道退款订单关联原始交易
     *
     * @param limit 查询条数
     * @return 处理结果统计
     */
    public Pair<Integer, Integer> associatedRefundOrders(int limit) {
        List<RefundOrderV2Po> refundOrderV2PoList = refundOrderV2Dao.queryInitOrders(limit);
        int successCount = refundOrderV2PoList.stream().map(refundOrderV2Po -> {
            try {
                String tradeNo = refundOrderV2Po.getTradeNo();
                String kupayOrderNo = refundOrderV2Po.getKupayOrderNo();
                String rechargeOrderNum = kupayOrderNo;
                // 注意⚠️：天猫充值和苹果内购网关记录的充值单号不以R开头（即kupayOrderNo!=rechargeOrderNum)，需要通过tradeNo查出对应的的rechargeOrderNum
                if (!kupayOrderNo.startsWith("R")) {
                    List<String> tradeNoList = Lists.newArrayList(tradeNo, StringUtils.join("TID", tradeNo));
                    Optional<String> optionalRechargeOrderNum = tradeNoList.stream()
                            .map(tradeNo1 -> rechargeOrderService.queryRechargeOrderNumByTradeNo(tradeNo1).orElse(null))
                            .filter(Objects::nonNull)
                            .findFirst();
                    if (optionalRechargeOrderNum.isPresent()) {
                        rechargeOrderNum = optionalRechargeOrderNum.get();
                    }
                }
                Optional<RechargeAcrossPO> optionalRechargeAcrossPO = Optional.empty();
                if (StringUtils.isNoneBlank(rechargeOrderNum)) {
                    optionalRechargeAcrossPO = rechargeOrderService.queryByRechargeOrderNum(rechargeOrderNum);
                }
                return RefundServiceFactory.createRefundService(refundOrderV2Po.getPayTypeId()).associateOriginOrder(refundOrderV2Po, optionalRechargeAcrossPO);
            } catch (Exception e) {
                log.warn("各渠道退款订单关联原始交易，关联异常。refundOrderV2Po: {}", refundOrderV2Po, e);
                return false;
            }
        }).mapToInt(value -> Boolean.TRUE.equals(value) ? 1 : 0).reduce(0, Integer::sum);
        return Pair.of(refundOrderV2PoList.size(), successCount);
    }

    public Triple<Long, Long, Long> handleRefund(int limit, int coverDays) {
        // 处理历史未完成的退款扣费订单
        Pagination pagination = new Pagination.Builder(1, 200).build();
        List<RefundDeductOrderV2Po> unfinishedDeductOrderList = refundDeductOrderV2Dao.getUnfinishedDeductOrder(pagination);
        long historyDeduct = unfinishedDeductOrderList.stream()
                .mapToInt(refundDeductOrderV2Po -> invokeDeductOrder(refundDeductOrderV2Po) ? 1 : 0)
                .boxed().count();
        // 扫描欠费生成新的退款扣费订单
        long foundedRefund = 0;
        long successRefund = 0;
        Date endTime = new Date();
        Date startTime = DateUtils.addDays(endTime, -1 * Math.abs(Math.max(1, coverDays)));
        log.warn("扫描欠费用户，扫描时间范围。startTime: {}, endTime: {}, limit: {}", DateHelper.format(startTime), DateHelper.format(endTime), limit);
        long lastRefundId = 0;
        while (true) {
            List<RefundDebtV2Po> refundDebtV2PoList = refundDebtV2Dao.getDebtUserList(startTime, endTime, lastRefundId, limit);
            if (CollectionUtils.isEmpty(refundDebtV2PoList)) {
                break;
            }
            foundedRefund += refundDebtV2PoList.size();
            lastRefundId = refundDebtV2PoList.stream()
                    .mapToLong(RefundDebtV2Po::getKugouId)
                    .max().orElse(Long.MAX_VALUE);
            long handledRefundOrderCount = refundDebtV2PoList.stream()
                    .map(refundDebtV2Po -> createDeductOrder(refundDebtV2Po).orElse(null))
                    .filter(Objects::nonNull)
                    .map(refundDeductOrderV2Po -> invokeDeductOrder(refundDeductOrderV2Po) ? 1 : 0)
                    .count();
            successRefund += handledRefundOrderCount;
            if (refundDebtV2PoList.size() < limit) {
                break;
            }
        }
        String content = String.format("退款处理，退款订单数量：%d，处理退款数量：%d", foundedRefund, successRefund);
        alerterFacade.sendRTX(content);
        return Triple.of(foundedRefund, successRefund, historyDeduct);
    }

    public Optional<RefundDeductOrderV2Po> createDeductOrder(RefundDebtV2Po refundDebtV2Po) {
        Optional<RefundDeductOrderV2Po> optionalProcessingOrder = Optional.ofNullable(refundDeductOrderV2Dao.getProcessing(refundDebtV2Po.getKugouId()));
        if (optionalProcessingOrder.isPresent()) {
            log.warn("退款处理，扣费订单未结束暂不发起扣费订单。refundDebtV2Po: {}", refundDebtV2Po);
            return Optional.empty();
        }
        BigDecimal expectDeductCoin = refundDebtV2Po.getDebtCoin();
        if (expectDeductCoin.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("退款处理，欠费星币为0暂不发起扣费订单。refundDebtV2Po: {}", refundDebtV2Po);
            return Optional.empty();
        }
        BigDecimal userCoin = this.userFacadeService.getUserCoinBalance(refundDebtV2Po.getKugouId());
        if (userCoin.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("退款处理，查询余额失败或者星币不足不再发起扣费订单。refundDebtV2Po: {}", refundDebtV2Po);
            return Optional.empty();
        }
        BigDecimal deductCoin = refundDebtV2Po.getDebtCoin().compareTo(userCoin) > 0 ? userCoin : refundDebtV2Po.getDebtCoin();
        RefundDeductOrderV2Po refundDeductOrderV2Po = new RefundDeductOrderV2Po();
        refundDeductOrderV2Po.setDeductId(orderIdService.generateGlobalId());
        refundDeductOrderV2Po.setAddTime(System.currentTimeMillis());
        refundDeductOrderV2Po.setKugouId(refundDebtV2Po.getKugouId());
        refundDeductOrderV2Po.setAddTime(DateHelper.getCurrentSeconds());
        refundDeductOrderV2Po.setDeductCoin(deductCoin);
        refundDeductOrderV2Po.setStatus(0);
        refundDeductOrderV2Po.setUpdateTime(new Date());
        refundDeductOrderV2Po.setCreateTime(new Date());
        // 冻结欠费星币余额并创建扣减星币订单
        return this.refundDeductService.createDeductOrder(refundDeductOrderV2Po);
    }

    private boolean invokeDeductOrder(RefundDeductOrderV2Po refundDeductOrderV2Po) {
        AdminAdjustCoinResult adminAdjustCoinResult = null;
        if (AdminAdjustCoinResult.isInitial(refundDeductOrderV2Po.getStatus())) {
            adminAdjustCoinResult = this.refundDeductService.deductCoin(refundDeductOrderV2Po);
        }
        if (AdminAdjustCoinResult.isUnknown(refundDeductOrderV2Po.getStatus())) {
            adminAdjustCoinResult = this.refundDeductService.confirmAdminAdjustCoin(refundDeductOrderV2Po);
        }
        if (Objects.isNull(adminAdjustCoinResult)) {
            throw new ContextedRuntimeException("调用管理员星币调整退款扣币，未知的订单状态。")
                    .addContextValue("deductId", refundDeductOrderV2Po.getDeductId())
                    .addContextValue("status", refundDeductOrderV2Po.getStatus());
        }
        if (AdminAdjustCoinResult.isSuccess(adminAdjustCoinResult.getCode())) {
            log.warn("调用管理员星币调整退款扣币，根据消费服务响应更新订单状态成功。refundDeductOrderV2Po: {}", refundDeductOrderV2Po);
            return this.refundDeductService.successDeductOrder(refundDeductOrderV2Po);
        }
        if (AdminAdjustCoinResult.isUnknown(adminAdjustCoinResult.getCode())) {
            log.warn("调用管理员星币调整退款扣币，根据消费服务响应更新订单状态未知。refundDeductOrderV2Po: {}", refundDeductOrderV2Po);
            return this.refundDeductService.unknownDeductOrder(refundDeductOrderV2Po);
        }
        if (AdminAdjustCoinResult.isFailure(adminAdjustCoinResult.getCode())) {
            log.warn("调用管理员星币调整退款扣币，根据消费服务响应更新订单状态失败。refundDeductOrderV2Po: {}", refundDeductOrderV2Po);
            return this.refundDeductService.releaseDeductOrder(refundDeductOrderV2Po);
        }
        throw new ContextedRuntimeException("调用管理员星币调整退款扣币，未知的扣费结果。")
                .addContextValue("deductId", refundDeductOrderV2Po.getDeductId())
                .addContextValue("adminAdjustCoinResult", adminAdjustCoinResult);
    }
}
