package com.kugou.fanxing.recharge.service.callback;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.TmallCallbackRequest;
import com.kugou.fanxing.recharge.model.vo.RechargeRebateVO;
import com.kugou.fanxing.recharge.service.RechargeRebateService;
import com.kugou.fanxing.recharge.service.TmallRechargeService;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.recharge.util.ModelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.UnaryOperator;

@Slf4j
@Service
public class TmallCallbackService extends AbstractCallbackService {

    @Autowired
    private TmallRechargeService tmallRechargeService;
    @Autowired
    private RechargeRebateService rechargeRebateService;

    public JsonResult<Map<String, String>> purchaseCoin(TmallCallbackRequest tmallCallbackRequest) {
        PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.PAY_TYPE_ID_1012;
        // 请求签名校验
        boolean isValidSign = checkSignOfPcCallBack(tmallCallbackRequest);
        if (!isValidSign) {
            log.warn("天猫星币充值回调接口，参数签名错误。tmallCallbackRequest: {}", tmallCallbackRequest);
            return JsonResult.result(SysResultCode.E_30000009, Maps.newHashMap());
        }
        boolean isValidNotify = this.checkCallbackNotify(tmallCallbackRequest);
        if (!isValidNotify) {
            log.warn("天猫星币充值回调接口，回查网关notifyId。tmallCallbackRequest: {}", tmallCallbackRequest);
            return JsonResult.result(SysResultCode.E_30000010, Maps.newHashMap());
        }
        // 根据天猫店铺品牌的获取用户酷狗ID
        Optional<Long> optionalKugouId = this.tmallRechargeService.parseKugouIdByBrand(tmallCallbackRequest.getUserid(), tmallCallbackRequest.getExtend());
        return getMapJsonResult(tmallCallbackRequest, payTypeIdEnum, optionalKugouId);
    }

    public String buildExtendStr() {
        List<RechargeRebateVO> rechargeRebateVOList = this.rechargeRebateService.getRechargeRebateInfo();
        long rebateVersionId = rechargeRebateVOList.stream().findFirst().orElseThrow(() -> new ContextedRuntimeException("")).getVersion();
        Map<String, Object> extend = Maps.newHashMap();
        extend.put("callBackSign", Lists.newArrayList());
        extend.put("callBackArg", ImmutableMap.of("rebate", rebateVersionId));
        return new String(Base64.encodeBase64(JSON.toJSONString(extend).getBytes()));
    }

    public JsonResult<Map<String, String>> purchaseCoinKw(TmallCallbackRequest tmallCallbackRequest) {
        PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.PAY_TYPE_ID_1012;
        // 请求签名校验
        boolean isValidSign = checkSignOfPcCallBack(tmallCallbackRequest);
        if (!isValidSign) {
            log.warn("酷我直播天猫星币充值回调接口，参数签名错误。tmallCallbackRequest: {}", tmallCallbackRequest);
            return JsonResult.result(SysResultCode.E_30000009, Maps.newHashMap());
        }
        boolean isValidNotify = this.checkCallbackNotify(tmallCallbackRequest);
        if (!isValidNotify) {
            log.warn("酷我直播天猫星币充值回调接口，回查网关notifyId。tmallCallbackRequest: {}", tmallCallbackRequest);
            return JsonResult.result(SysResultCode.E_30000010, Maps.newHashMap());
        }
        // 根据天猫店铺品牌的获取用户酷狗ID
        Optional<Long> optionalKugouId = this.userFacadeService.getKugouIdByKuwoId(tmallCallbackRequest.getUserid());
        return getMapJsonResult(tmallCallbackRequest, payTypeIdEnum, optionalKugouId);
    }

    private JsonResult<Map<String, String>> getMapJsonResult(TmallCallbackRequest tmallCallbackRequest, PayTypeIdEnum payTypeIdEnum, Optional<Long> optionalKugouId) {
        if (!optionalKugouId.isPresent()) {
            log.warn("天猫星币充值回调接口，根据店铺品牌信息获取酷狗ID失败。tmallCallbackRequest: {}", tmallCallbackRequest);
            return JsonResult.result(SysResultCode.E_30000011, Maps.newHashMap());
        }
        // 建立第三方订单号与繁星订单号的对应关系
        Optional<String> optionalRechargeOrderNum = this.rechargeOrderService.specialRechargeOrderNumDeal(tmallCallbackRequest.getOrder_no(), payTypeIdEnum.getPayTypeId());
        if (!optionalRechargeOrderNum.isPresent()) {
            log.warn("天猫星币充值回调接口，直播充值订单号获取失败。tmallCallbackRequest: {}", tmallCallbackRequest);
            return JsonResult.result(SysResultCode.E_30000012, Maps.newHashMap());
        }
        // 读取下单记录
        long kugouId = optionalKugouId.get();
        String rechargeOrderNum = optionalRechargeOrderNum.get();
        Optional<RechargeAcrossPO> optionalRechargeAcrossPO = this.rechargeOrderService.queryByRechargeOrderNum(rechargeOrderNum);
        if (!optionalRechargeAcrossPO.isPresent()) {
            log.warn("天猫星币充值回调接口，模拟下单。tmallCallbackRequest: {}", tmallCallbackRequest);
            RechargeAcrossPO rechargeAcrossPO = mockCreateOrder(PayTypeIdEnum.PAY_TYPE_ID_1012, kugouId, rechargeOrderNum,
                    tmallCallbackRequest.getOut_trade_no(), new BigDecimal(tmallCallbackRequest.getTotal_fee()));
            int affected = this.rechargeOrderService.addRechargeOrder(rechargeAcrossPO);
            if (affected < 1) {
                log.warn("天猫星币充值回调接口，保存下单失败。tmallCallbackRequest: {}", tmallCallbackRequest);
                return JsonResult.result(SysResultCode.RECHARGE_SYS_ERROR, Maps.newHashMap());
            }
            optionalRechargeAcrossPO = Optional.of(rechargeAcrossPO);
        }
        // 检查下单记录
        RechargeAcrossPO sourceOrder = optionalRechargeAcrossPO.get();
        if (sourceOrder.getStatus() == 1) {
            log.warn("天猫星币充值回调接口，订单已经处理过。tmallCallbackRequest: {}", tmallCallbackRequest);
            return JsonResult.result(SysResultCode.SUCCESS, Maps.newHashMap());
        }
        if (sourceOrder.getKugouId() != kugouId) {
            log.warn("天猫星币充值回调接口，充值下单与回调酷狗ID不一致。tmallCallbackRequest: {}", tmallCallbackRequest);
            return JsonResult.result(SysResultCode.E_30000006, Maps.newHashMap());
        }
        // 设置到账记录
        UnaryOperator<RechargeAcrossPO> map2TargetOrder = source -> {
            // 实际充值金额，单位：元
            BigDecimal amount = new BigDecimal(tmallCallbackRequest.getTotal_fee());
            // 实际充值金额，单位：分
            BigDecimal realAmount = amount.multiply(BigDecimal.valueOf(100));
            RechargeAcrossPO target = ModelUtils.fromUnchecked(source, RechargeAcrossPO.class);
            target.setCoin(realAmount);
            target.setRealAmount(realAmount);
            target.setStatus(1);
            target.setRechargeTime(DateHelper.getCurrentSeconds());
            target.setReType(ReTypeEnum.RETYPE_RECHARGE.getReTypeId());
            target.setRechargeId(orderIdService.generateGlobalId());
            target.setTradeNo(StringUtils.defaultString(tmallCallbackRequest.getTrade_no()));
            target.setTradeTime(super.parseTradeTime(tmallCallbackRequest.getTrade_time()));
            target.setPartner(StringUtils.defaultString(tmallCallbackRequest.getPartner()));
            target.setExtend(this.buildExtendStr());
            return target;
        };
        if (!super.executeIdempotent(sourceOrder, map2TargetOrder)) {
            log.error("天猫星币充值回调接口，充值调用消费加星币失败。tmallCallbackRequest: {}", tmallCallbackRequest);
            return JsonResult.result(SysResultCode.E_30000013, Maps.newHashMap());
        }
        log.warn("天猫星币充值回调接口，处理成功。tmallCallbackRequest: {}, data: {}", tmallCallbackRequest, Maps.newHashMap());
        return JsonResult.result(SysResultCode.SUCCESS, Maps.newHashMap());
    }
}

