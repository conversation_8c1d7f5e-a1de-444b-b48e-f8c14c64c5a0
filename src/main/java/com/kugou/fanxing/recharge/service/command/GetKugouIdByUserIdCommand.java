package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.thrift.idmapping.user.UserIdMappingService;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Optional;

/**
 * 酷狗ID=>繁星ID
 *
 * <AUTHOR>
 */
@Slf4j
public class GetKugouIdByUserIdCommand extends HystrixCommand<Optional<Long>> {

    private final UserIdMappingService.Iface userIdMappingService;
    private final long userId;

    public GetKugouIdByUserIdCommand(final UserIdMappingService.Iface userIdMappingService, final long userId) {
        super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("GetKugouIdByUserIdCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(300)));
        this.userIdMappingService = userIdMappingService;
        this.userId = userId;
    }

    @Override
    protected Optional<Long> run() throws Exception {
        return Optional.of(this.userIdMappingService.getKugouIdByUserId(userId));
    }

    @Override
    protected Optional<Long> getFallback() {
        Optional<Long> fallback = Optional.empty();
        log.warn("GetKugouIdByUserIdCommand服务降级! 通过繁星ID查询酷狗ID信息出错, userId: {}! 降级返回数据: {}, 降级原因: {}",
                userId, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }
}
