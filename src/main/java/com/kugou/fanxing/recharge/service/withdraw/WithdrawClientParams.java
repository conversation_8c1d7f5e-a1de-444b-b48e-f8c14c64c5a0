package com.kugou.fanxing.recharge.service.withdraw;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 **/
@Data
@Accessors(chain = true)
public class WithdrawClientParams {

    /**
     * 终端应用编号
     */
    private String applicationId;

    /**
     * 客户端版本号
     */
    private String clientver;

    /**
     * 机器唯一码
     */
    private String mid;

    /**
     * 客户端设备唯一标识
     */
    private String uuid;

    /**
     * 设备指纹ID，如取失败，填'-'
     */
    private String dfid;

    /**
     * 客户端ip
     */
    private String clientip;

}
