package com.kugou.fanxing.recharge.service.refund.v2;

import com.kugou.fanxing.recharge.model.dto.RefundNotifyDto;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.po.RefundOrderV2Po;

import java.util.Optional;

public interface RefundService {
    /**
     * 接收退款通知
     */
    <T extends RefundNotifyDto> boolean receiveNotify(T t);

    /**
     * 关联原始交易
     */
    boolean associateOriginOrder(RefundOrderV2Po refundOrderV2Po, Optional<RechargeAcrossPO> optionalRechargeAcrossPO);
}
