package com.kugou.fanxing.recharge.service;

import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.PayPalRequest;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.service.common.IpLocationService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import com.kugou.fanxing.recharge.util.SignUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * PayPal Service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PayPalRechargeService {

    @Autowired
    private RechargeConfig rechargeConfig;
    @Autowired
    private RemoteFamilyControlService remoteFamilyControlService;
    @Autowired
    private OrderIdService orderIdService;
    @Autowired
    private RechargeAcrossDao rechargeAcrossDao;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private IpLocationService ipLocationService;
    @Autowired
    private RemoteStrategyService remoteStrategyService;
    @Autowired
    private RechargeCommonService rechargeCommonService;
    @Autowired
    private UserFacadeService userFacadeService;
    @Autowired
    private AgentRechargeService agentRechargeService;

    /**
     * PayPal下单接口
     *
     * @param webCommonParam 通用请求参数
     * @param payPalRequest  业务请求参数
     * @return
     */
    public Map<String, Object> getPayPalUrl(WebCommonParam webCommonParam, PayPalRequest payPalRequest) {
        // 检查用户繁星账号
        long kugouId = webCommonParam.getKugouId();
        Optional<Long> optionalUserId = this.getUserIdByKugouId(kugouId);
        if (!optionalUserId.isPresent()) {
            log.warn("创建PayPal订单，通过酷狗ID获取繁星ID失败, optionalUserId: {}", optionalUserId);
            throw new AckException(SysResultCode.RECHARGE_NOT_FANXING);
        }
        long userId = optionalUserId.get();

        // 检查充值金额区域（充值金额限制：10～100美元，充值区域限制：非中国）
        BigDecimal usdAmount = payPalRequest.getAmount();
        String ip = webCommonParam.getIp();
        boolean inPayPalWhitelist = isInPayPalWhitelist(kugouId);
        log.warn("创建PayPal订单，检查是否PayPal白名单酷狗账号。kugouId:{}, userId:{}, inPayPalWhitelist:{}", kugouId, userId, inPayPalWhitelist);
        if (!inPayPalWhitelist) {
            if (usdAmount.compareTo(BigDecimal.valueOf(100)) > 0) {
                log.warn("创建PayPal订单，单笔充值大于100美元。webCommonParam:{}, payPalRequest:{}", webCommonParam, payPalRequest);
                throw new BizException(SysResultCode.RECHARGE_PAYPAL_MAX_AMOUNT);
            }
            if (usdAmount.compareTo(BigDecimal.valueOf(10)) < 0) {
                log.warn("创建PayPal订单，单笔充值少于10美元。webCommonParam:{}, payPalRequest:{}", webCommonParam, payPalRequest);
                throw new BizException(SysResultCode.RECHARGE_PAYPAL_MIN_AMOUNT);
            }
            if (!ipLocationService.isOverseasIp(ip)) {
                log.warn("创建PayPal订单，充值区域必须为海外。webCommonParam:{}, payPalRequest:{}", webCommonParam, payPalRequest);
                throw new BizException(SysResultCode.RECHARGE_PAYPAL_OVERSEAS);
            }
        }

        // 检查家长控制模式
        if (remoteFamilyControlService.checkFamilyControl(kugouId, webCommonParam.getPid())) {
            log.warn("创建PayPal订单，家长控制模式开启, payPalRequest:{},kugouId:{}", payPalRequest, kugouId);
            throw new BizException(SysResultCode.RECHARGE_FAMILY_CONTROL);
        }

        // 检查用户代充账号
        SysResultCode sysResultCode = agentRechargeService.checkAgentRecharge(webCommonParam, payPalRequest.getRechargeKugouId(), 0);
        if (!sysResultCode.isSuccess()) {
            log.warn("创建PayPal订单，处理代充检查失败。webCommonParam: {}, bankRequest: {}", webCommonParam, payPalRequest);
            throw new BizException(sysResultCode);
        }

        // 美元对人民币汇率
        BigDecimal cnyAmount = getRmbAmount(usdAmount);

        // 生成充值订单编号
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);

        // 校验风控控制服务
        if (remoteStrategyService.strategyVerifyForPayPal(rechargeOrderNum, kugouId, payPalRequest)) {
            log.warn("创建PayPal订单，风控服务拦截下单。kugouId:{}, payPalRequest:{} ", kugouId, payPalRequest);
            throw new BizException(SysResultCode.RECHARGE_BY_OTHER_TYPE);
        }

        // 保存充值下单记录
        String clientIp = StringUtils.defaultString(webCommonParam.getIp());
        RechargeAcrossPO rechargeAcrossPO = createRechargeAcrossPO(webCommonParam, payPalRequest, kugouId, cnyAmount, rechargeOrderNum, clientIp);
        int addResult = rechargeAcrossDao.add(month, rechargeAcrossPO);
        if (addResult <= 0) {
            log.warn("创建PayPal订单，下单信息入库失败, month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }

        // 返回充值下单链接（调用酷狗支付网关的时候，需要按美元来算，所以传进去的金额为美元 usdAmount）
        Map<String, Object> dataMap = createRechargeResult(rechargeAcrossPO, webCommonParam.getExt(), usdAmount);
        if (dataMap.isEmpty()) {
            log.warn("创建PayPal订单，生成充值下单链接失败, rechargeAcrossPO: {}", rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        return dataMap;
    }

    public BigDecimal getRmbAmount(BigDecimal usdAmount) {
        // 人民币兑美元（考虑安全性代码写死）
        int currencyRate = 6;
        BigDecimal cnyAmount = usdAmount.multiply(BigDecimal.valueOf(currencyRate));
        log.warn("PayPal订单汇率计算, currencyRate: {}, usdAmount: {}, cnyAmount: {}",
                currencyRate, usdAmount.toPlainString(), cnyAmount.toPlainString());
        return cnyAmount;
    }

    public Optional<Long> getUserIdByKugouId(long kugouId) {
        Optional<Long> optionalUserId = this.userFacadeService.getUserIdByKugouId(kugouId, false);
        long userId = optionalUserId.orElse(0L);
        if (userId <= 0) {
            log.warn("创建PayPal订单，获取不到正常userId, kugouId:{}", kugouId);
            return Optional.empty();
        }
        return optionalUserId;
    }

    /**
     * 检查是否在白名单
     *
     * @param kugouId 酷狗ID
     * @return 是否白名单账户
     */
    public boolean isInPayPalWhitelist(long kugouId) {
        List<String> paypalWhiteList = this.apolloConfigService.getPayPalWhitelist();
        log.warn("PayPal白名单酷狗账号, paypalWhiteList: {}", paypalWhiteList);
        if (CollectionUtils.isEmpty(paypalWhiteList)) {
            return false;
        }
        return paypalWhiteList.stream().anyMatch(s -> s.equals(String.valueOf(kugouId)));
    }

    /**
     * 制造充值返回结果
     *
     * @return 返回的充值链接
     */
    public Map<String, Object> createRechargeResult(RechargeAcrossPO rechargeAcrossPO, String businessExt, BigDecimal usdAmount) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("actionUrl", buildRechargeUrlForPC(rechargeAcrossPO, businessExt, usdAmount));
        dataMap.put("webMethod", rechargeConfig.getWebMethod());
        return dataMap;
    }

    public String buildRechargeUrlForPC(RechargeAcrossPO rechargeAcrossPO, String businessExt, BigDecimal usdAmount) {
        try {
            PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
            KupayAppInfoBO kupayAppInfoBO = rechargeConfig.getKupayAppIdByPayType(payTypeIdEnum);
            String actionUrl = this.rechargeConfig.getActionUrlPrefix(PayTypeIdEnum.PAY_TYPE_ID_520);
            Map<String, String> params = Maps.newHashMap();
            params.put("appid", String.valueOf(kupayAppInfoBO.getKupayAppId()));
            params.put("time", String.valueOf(System.currentTimeMillis()));
            params.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
            params.put("subject", "星币充值服务");
            params.put("desc", "星币充值服务");
            params.put("total_fee", usdAmount.stripTrailingZeros().toPlainString());
            params.put("sign_type", rechargeConfig.getSignType());
            params.put("notify_url", rechargeConfig.getNotifyUrl(ReTypeEnum.RETYPE_RECHARGE));
            params.put("userid", String.valueOf(rechargeAcrossPO.getKugouId()));
            // 注意：通过网关透传的extend字段中的充值金额单位为美元，必须设置amount为美元金额
            Map<String, Object> extendParam = Maps.newHashMap();
            extendParam.put("businessExt", StringUtils.defaultString(businessExt));
            params.put("extend", rechargeCommonService.buildExtendStr(rechargeAcrossPO.setAmount(usdAmount),extendParam));
            params.put("sync_url", rechargeConfig.getSyncUrlPaypal());
            params.put("clientip", rechargeAcrossPO.getClientIp());
            params.put("sign", SignUtils.buildSign(params, kupayAppInfoBO.getKupayAppKey()));
            String rechargeUrl = buildRechargeUrl(payTypeIdEnum.getPayTypeId(), actionUrl, params);
            log.warn("rechargeUrl: {}", rechargeUrl);
            return rechargeUrl;
        } catch (Exception e) {
            log.error("充值下单，构建第三方支付信息异常。rechargeAcrossPO: {}", rechargeAcrossPO, e);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    /**
     *
     *
     * @param payTypeId
     * @param actionUrl
     * @param params
     * @return
     * @throws UnsupportedEncodingException
     */
    public String buildRechargeUrl(int payTypeId, String actionUrl, Map<String, String> params) throws UnsupportedEncodingException {
        // 注意⚠️：微信公众号、支付宝WAP(H5页面)、微信WAP(H5页面) 前端会自动处理BASE64末尾等号的编码
        // if(!in_array($payType, array(31, 32, 39, 41))){
        //     $param['extend'] = urlencode($param['extend']);
        // }
        if (!ArrayUtils.contains(new int[] {31, 32, 39, 41}, payTypeId) ) {
            params.put("extend", URLEncoder.encode(params.get("extend"), StandardCharsets.UTF_8.name()));
        }
        HttpUrl httpUrl = HttpClientUtils.makeHttpUrlBuilder(actionUrl, params).build();
        String rechargeUrl = URLDecoder.decode(String.valueOf(httpUrl), StandardCharsets.UTF_8.name());
        log.warn("rechargeUrl: {}", rechargeUrl);
        return rechargeUrl;
    }

    public RechargeAcrossPO createRechargeAcrossPO(WebCommonParam webCommonParam, PayPalRequest payPalRequest,
                                                   long kugouId, BigDecimal cnyAmount, String rechargeOrderNum, String clientIp) {
        RechargeAcrossPO rechargeAcrossPO = new RechargeAcrossPO();
        rechargeAcrossPO.setRechargeId(orderIdService.generateGlobalId());
        rechargeAcrossPO.setRechargeOrderNum(rechargeOrderNum);
        rechargeAcrossPO.setAddTime(DateHelper.getCurrentSeconds());
        rechargeAcrossPO.setKugouId(payPalRequest.getRechargeKugouId() == 0 ? kugouId : payPalRequest.getRechargeKugouId());
        rechargeAcrossPO.setAgentKugouId(payPalRequest.getRechargeKugouId() == 0 ? 0 : kugouId);
        rechargeAcrossPO.setFromKugouId(payPalRequest.getRechargeKugouId() == 0 ? kugouId : payPalRequest.getRechargeKugouId());
        rechargeAcrossPO.setCoinBefore(BigDecimal.ZERO);
        rechargeAcrossPO.setCoin(BigDecimal.ZERO);
        rechargeAcrossPO.setCoinAfter(BigDecimal.ZERO);
        rechargeAcrossPO.setAmount(cnyAmount);
        rechargeAcrossPO.setMoney(cnyAmount);
        rechargeAcrossPO.setCoupon(BigDecimal.ZERO);
        rechargeAcrossPO.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_520.getPayTypeId());
        rechargeAcrossPO.setStatus(0);
        rechargeAcrossPO.setRefer(payPalRequest.getRefer());
        rechargeAcrossPO.setCFrom(webCommonParam.getPid());
        rechargeAcrossPO.setChannelId(payPalRequest.getChannelId());
        rechargeAcrossPO.setReType(ReTypeEnum.RETYPE_RECHARGE.getReTypeId());
        rechargeAcrossPO.setServerRoom(rechargeConfig.getDataCenterZoneId());
        rechargeAcrossPO.setRealAmount(BigDecimal.ZERO);
        rechargeAcrossPO.setClientIp(clientIp);
        return rechargeAcrossPO;
    }

}
