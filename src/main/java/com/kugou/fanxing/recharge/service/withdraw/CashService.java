package com.kugou.fanxing.recharge.service.withdraw;

import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.model.po.WithdrawOrderPO;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import com.kugou.rpc.client.http.HttpService;
import com.kugou.rpc.client.http.Response;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;

import java.util.Map;
import java.util.Optional;

public interface CashService {

    Optional<AlipayResp> withdraw(WithdrawOrderPO withdrawOrderPO, WithdrawClientParams withdrawClientParams, String token);

    Optional<AlipayResp> withdrawOrderQuery(WithdrawOrderPO withdrawOrderPO);

    default String getSuffixUrl(Map<String, String> treeMap) {
        StringBuilder suffixUrlTemp = new StringBuilder("?");
        treeMap.forEach((k, v) -> {
            suffixUrlTemp.append(k);
            suffixUrlTemp.append("=");
            suffixUrlTemp.append(v);
            suffixUrlTemp.append("&");
        });
        return suffixUrlTemp.substring(0, suffixUrlTemp.length() - 1);
    }

    default Response postJson(HttpService httpService, String url, String body) {
        HttpPost httpPost = new HttpPost(url);
        StringEntity stringEntity = new StringEntity(body, ContentType.APPLICATION_JSON);
        httpPost.setHeader("Content-type", "application/json");
        httpPost.setEntity(stringEntity);
        String urlForCat = StringUtils.substringBefore(url, "?");
        return httpService.execute(httpPost, urlForCat);
    }

    default String getQuerySign(String withdrawServerKey, Map<String, String> treeMap) {
        StringBuilder source = new StringBuilder(withdrawServerKey);
        treeMap.forEach((k, v) -> {
            source.append(k);
            source.append("=");
            source.append(v);
        });
        source.append(withdrawServerKey);
        return DigestUtils.md5Hex(source.toString());
    }

    default String getCommandSign(String withdrawServerKey, Map<String, String> treeMap, String toJSONString) {
        StringBuilder source = new StringBuilder(withdrawServerKey);
        treeMap.forEach((k, v) -> {
            source.append(k);
            source.append("=");
            source.append(v);
        });
        source.append(toJSONString);
        source.append(withdrawServerKey);
        return DigestUtils.md5Hex(source.toString());
    }

    /**
     * 构建提现公共参数
     *
     * @param withdrawServerId 提现服务ID
     * @param withdrawClientParams 提现客户端参数
     * @return 提现公用参数
     */
    default Map<String, String> buildWithdrawCommonParams(String withdrawServerId, WithdrawClientParams withdrawClientParams) {
        Map<String, String> treeMap = Maps.newTreeMap();
        treeMap.put("serverid", withdrawServerId);
        treeMap.put("servertime", String.valueOf(DateHelper.getCurrentSeconds()));
        treeMap.put("appid", withdrawClientParams.getApplicationId());
        treeMap.put("clientver", withdrawClientParams.getClientver());
        treeMap.put("mid", "-");
        treeMap.put("uuid", "-");
        treeMap.put("dfid", "-");
        return treeMap;
    }

    /**
     * 构建提现查询公共参数
     *
     * @param withdrawServerId 提现服务ID
     * @return 构建提现查询公共参数
     */
    default Map<String, String> buildWithdrawQueryCommonParams(String withdrawServerId) {
        Map<String, String> params = Maps.newTreeMap();
        params.put("serverid", withdrawServerId);
        params.put("servertime", String.valueOf(DateHelper.getCurrentSeconds()));
        params.put("appid", "1010");
        params.put("clientver", "-");
        params.put("mid", "-");
        params.put("uuid", "-");
        params.put("dfid", "-");
        return params;
    }

}
