package com.kugou.fanxing.recharge.service.command;

import com.google.common.collect.Maps;
import com.kugou.platform.after.recharge.asset.allocate.thrift.AfterRechargeAssetAllocateReadService;
import com.kugou.platform.after.recharge.asset.allocate.thrift.GetRechargePresentRequest;
import com.kugou.platform.after.recharge.asset.allocate.thrift.PresentInfo;
import com.kugou.platform.after.recharge.asset.allocate.thrift.RechargePresentItem;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class GetRechargePresentInfoCommand extends HystrixCommand<Map<Long,List<RechargePresentItem>>> {

    private final List<Long> money;
    private final AfterRechargeAssetAllocateReadService.Iface afterRechargeAsserAllocateReadService;
    private final Long kugouId;

    public GetRechargePresentInfoCommand(final AfterRechargeAssetAllocateReadService.Iface afterRechargeAsserAllocateReadService, final List<Long> money, Long kugouId) {
        super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("GetRechargePresentByNumCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(300)));
        this.afterRechargeAsserAllocateReadService = afterRechargeAsserAllocateReadService;
        this.money = money;
        this.kugouId = kugouId;
    }

    @Override
    protected Map<Long,List<RechargePresentItem>> run() throws Exception {
        if (CollectionUtils.isEmpty(money)) {
            return Maps.newHashMap();
        }
        GetRechargePresentRequest rechargePresentRequest = new GetRechargePresentRequest();
        rechargePresentRequest.setMoneys(money);
        rechargePresentRequest.setKgId(Optional.ofNullable(kugouId).orElse(0L));
        PresentInfo presentInfo = this.afterRechargeAsserAllocateReadService.getRechargePresentInfo(rechargePresentRequest);
        if (presentInfo.getRet() != 0 || MapUtils.isEmpty(presentInfo.getData())) {
            log.warn("查询充值后赠送逻辑失败, money: {}, presentInfo: {}", money, presentInfo);
            return Maps.newHashMap();
        }
        Map<Long,List<RechargePresentItem>> rechargePresentMap = presentInfo.getData();
        log.warn("查询充值后赠送逻辑成功, money: {}, presentInfo: {}", money, presentInfo);
        return rechargePresentMap;
    }

    @Override
    protected Map<Long,List<RechargePresentItem>> getFallback() {
        Map<Long,List<RechargePresentItem>> rechargePresentMap = Maps.newHashMap();
        log.warn("GetRechargePresentByNumCommand服务降级! 查询充值后赠送逻辑出错, money: {}! 降级返回数据: {}, 降级原因: {}",
                money, rechargePresentMap, ExceptionUtils.getStackTrace(getExecutionException()));
        return rechargePresentMap;
    }
}
