package com.kugou.fanxing.recharge.service.withdraw;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawAccountWechatDao;
import com.kugou.fanxing.recharge.model.po.WithdrawAccountWechatPO;
import com.kugou.fanxing.recharge.model.po.WithdrawOrderPO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 微信提现相关接口
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class WechatCashService implements CashService {

    @Value("${kupay.intranet}")
    private String withdrawHostUrl;
    @Value("${kupay.withdraw.serverId}")
    private String withdrawServerId;
    @Value("${kupay.withdraw.serverKey}")
    private String withdrawServerKey;
    @Autowired
    private WithdrawAccountWechatDao withdrawAccountWechatDao;
    @Autowired
    private ApolloConfigService apolloConfigService;

    /**
     * 微信提现接口
     * http://doc.kugou.net/showdoc-master/web/#/8?page_id=4000
     *
     * @param withdrawOrderPO 提现订单
     * @return 提现信息
     */
    public Optional<AlipayResp> withdraw(WithdrawOrderPO withdrawOrderPO, WithdrawClientParams withdrawClientParams, String token) {
        try {
            StringBuilder url = new StringBuilder(withdrawHostUrl);
            url.append("/v1/wxqyzhcash/order");
            Map<String, String> urlParams = buildWithdrawCommonParams(withdrawServerId, withdrawClientParams);
            // 业务参数
            JSONObject params = new JSONObject();
            params.put("biz_appid", withdrawOrderPO.getBizAppId());
            params.put("clientip", withdrawClientParams.getClientip());
            params.put("order_no", withdrawOrderPO.getOrderId());
            params.put("openid", withdrawOrderPO.getOpenid());
            params.put("kugouid", withdrawOrderPO.getKugouId());
            // 支付网关微信提现金额单位是分
            String totalFee = withdrawOrderPO.getTotalAmount().multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString();
            params.put("total_fee", totalFee);
            params.put("remark", apolloConfigService.matchDrawCashRemark(withdrawOrderPO.getBizAppId(), "酷狗直播提现"));
            String sign = getCommandSign(withdrawServerKey, urlParams, params.toJSONString());
            urlParams.put("signature", sign);
            Optional<String> optionalJson = HttpClientUtils.doPostJsonBody(url.toString(), urlParams, params.toJSONString());
            if (!optionalJson.isPresent() || StringUtils.isBlank(optionalJson.get())) {
                log.warn("调用微信提现接口失败, optionalJson: {}, url: {}, urlParams: {}, jsonBody: {}, rechargeDrawCashPO: {}",
                        optionalJson, url, urlParams, params.toJSONString(), withdrawOrderPO);
                return Optional.empty();
            }
            AlipayResp alipayResp = JSON.parseObject(optionalJson.get(), AlipayResp.class);
            log.warn("调用微信提现接口成功, optionalJson: {}, url: {}, params: {}, rechargeDrawCashPO: {}", optionalJson, url, params, withdrawOrderPO);
            return Optional.of(alipayResp);
        } catch (Exception e) {
            log.error("调用微信提现接口异常, rechargeDrawCashPO: {}", withdrawOrderPO);
        }
        return Optional.empty();
    }

    /**
     * 微信提现查询接口
     * http://doc.kugou.net/showdoc-master/web/#/8?page_id=4004
     * @return 提现信息
     */
    @Override
    public Optional<AlipayResp> withdrawOrderQuery(WithdrawOrderPO withdrawOrderPO) {
        int bizAppId = withdrawOrderPO.getBizAppId();
        long kugouId = withdrawOrderPO.getKugouId();
        long orderId = withdrawOrderPO.getOrderId();
        String wxAppId = apolloConfigService.getWxAppIdByBizAppId(bizAppId);
        WithdrawAccountWechatPO withdrawAccountPO = this.withdrawAccountWechatDao.selectByKugouId(kugouId, wxAppId);
        if (Objects.isNull(withdrawAccountPO) || StringUtils.isBlank(withdrawAccountPO.getOpenid())) {
            log.error("调用微信提现查询接口，获取用户openid失败。kugouId: {}, orderId: {}", kugouId, orderId);
            throw new ContextedRuntimeException("调用微信提现查询接口，获取用户openid失败")
                    .addContextValue("kugouId", kugouId)
                    .addContextValue("orderId", orderId);
        }
        String openid = withdrawAccountPO.getOpenid();
        try {
            StringBuilder url = new StringBuilder(withdrawHostUrl);
            url.append("/v1/wxqyzhcash/query");

            Map<String, String> params = buildWithdrawQueryCommonParams(withdrawServerId);
            // 业务参数
            params.put("openid", openid);
            params.put("biz_appid", String.valueOf(bizAppId));
            params.put("order_no", String.valueOf(orderId));
            params.put("signature", getQuerySign(withdrawServerKey, params));
            log.warn("调用微信提现查询接口，请求参数。url: {}, params: {}", url, params);
            Optional<String> optionalJson = HttpClientUtils.doSyncGet(url.toString(), params);
            if (!optionalJson.isPresent() || StringUtils.isBlank(optionalJson.get())) {
                log.warn("调用微信提现查询接口失败, optionalJson: {}, url: {}, params: {}, kugouId: {}, openid: {}, orderId: {}",
                        optionalJson, url, params, kugouId, openid, orderId);
                return Optional.empty();
            }

            AlipayResp alipayResp = JSON.parseObject(optionalJson.get(), AlipayResp.class);
            log.warn("调用微信提现查询接口成功, optionalJson: {}, url: {}, params: {}, alipayResp: {}, kugouId: {}, openid: {}, orderId: {}",
                    optionalJson, url, params, alipayResp, kugouId, openid, orderId);
            return Optional.of(alipayResp);
        } catch (Exception e) {
            log.error("调用微信提现查询接口异常, kugouId: {}, openid: {}, orderId: {}", kugouId, openid, orderId, e);
        }
        return Optional.empty();
    }
}
