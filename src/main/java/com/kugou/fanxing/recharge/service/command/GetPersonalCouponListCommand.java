package com.kugou.fanxing.recharge.service.command;

import com.google.common.collect.Lists;
import com.kugou.fanxing.coupon.thrift.read.CouponInfoVO;
import com.kugou.fanxing.coupon.thrift.read.CouponListByParamsRequest;
import com.kugou.fanxing.coupon.thrift.read.CouponReadService;
import com.kugou.fanxing.coupon.thrift.read.PersonalCouponListResultV2;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 查询用户充值代金券
 *
 * <AUTHOR>
 */
@Slf4j
public class GetPersonalCouponListCommand extends HystrixCommand<List<CouponInfoVO>> {

    private final CouponReadService.Iface couponReadService;
    private final long kugouId;

    public GetPersonalCouponListCommand(final CouponReadService.Iface couponReadService, final long kugouId) {
        super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("GetPersonalCouponListCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(500)));
        this.couponReadService = couponReadService;
        this.kugouId = kugouId;
    }

    @Override
    protected List<CouponInfoVO> run() throws Exception {
        final CouponListByParamsRequest request = new CouponListByParamsRequest()
                .setActionId(1019)
                .setKugouId(kugouId)
                .setCouponCategory(2)
                .setTimestamp(DateHelper.getCurrentSeconds());
        log.warn("调用充值代金券查询接口, request: {}", request);
        PersonalCouponListResultV2 response = couponReadService.getPersonalCouponList(request);
        if (Objects.isNull(response) || response.getCode() != 0) {
            log.warn("调用充值代金券查询接口, 调用失败, request: {}, response: {}", request, response);
            return Lists.newArrayList();
        }
        List<CouponInfoVO> couponInfoVOList = CollectionUtils.isNotEmpty(response.getData()) ? response.getData() : Lists.newArrayList();
        couponInfoVOList = couponInfoVOList.stream()
                .filter(couponInfoVO -> couponInfoVO.getCategory() == 2)
                .collect(Collectors.toList());
        return couponInfoVOList;
    }

    @Override
    protected List<CouponInfoVO> getFallback() {
        final List<CouponInfoVO> fallback = Lists.newArrayList();
        log.warn("GetPersonalCouponListCommand 服务降级! 调用充值代金券查询接口异常, kugouId: {}! 降级返回数据: {}, 降级原因: {}",
                kugouId, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }
}
