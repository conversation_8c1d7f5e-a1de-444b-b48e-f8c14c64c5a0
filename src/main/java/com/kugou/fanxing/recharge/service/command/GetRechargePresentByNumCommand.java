package com.kugou.fanxing.recharge.service.command;

import com.google.common.collect.Maps;
import com.kugou.platform.after.recharge.asset.allocate.thrift.AfterRechargeAssetAllocateReadService;
import com.kugou.platform.after.recharge.asset.allocate.thrift.PresentInfo;
import com.kugou.platform.after.recharge.asset.allocate.thrift.RechargePresentItem;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Slf4j
public class GetRechargePresentByNumCommand extends HystrixCommand<Map<Long,List<RechargePresentItem>>> {

    private final List<Long> money;
    private final AfterRechargeAssetAllocateReadService.Iface afterRechargeAsserAllocateReadService;

    public GetRechargePresentByNumCommand(final AfterRechargeAssetAllocateReadService.Iface afterRechargeAsserAllocateReadService, final List<Long> money) {
        super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("GetRechargePresentByNumCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(300)));
        this.afterRechargeAsserAllocateReadService = afterRechargeAsserAllocateReadService;
        this.money = money;
    }

    @Override
    protected Map<Long,List<RechargePresentItem>> run() throws Exception {
        if (CollectionUtils.isEmpty(money)) {
            return Maps.newHashMap();
        }
        PresentInfo presentInfo = this.afterRechargeAsserAllocateReadService.getRechargePresentByNum(money);
        if (presentInfo.getRet() != 0 || MapUtils.isEmpty(presentInfo.getData())) {
            log.warn("查询充值后赠送逻辑失败, money: {}, presentInfo: {}", money, presentInfo);
            return Maps.newHashMap();
        }
        Map<Long,List<RechargePresentItem>> rechargePresentMap = presentInfo.getData();
        log.warn("查询充值后赠送逻辑成功, money: {}, presentInfo: {}", money, presentInfo);
        return rechargePresentMap;
    }

    @Override
    protected Map<Long,List<RechargePresentItem>> getFallback() {
        Map<Long,List<RechargePresentItem>> rechargePresentMap = Maps.newHashMap();
        log.warn("GetRechargePresentByNumCommand服务降级! 查询充值后赠送逻辑出错, money: {}! 降级返回数据: {}, 降级原因: {}",
                money, rechargePresentMap, ExceptionUtils.getStackTrace(getExecutionException()));
        return rechargePresentMap;
    }
}
