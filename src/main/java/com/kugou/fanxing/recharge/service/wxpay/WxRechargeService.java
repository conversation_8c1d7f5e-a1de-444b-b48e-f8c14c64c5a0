package com.kugou.fanxing.recharge.service.wxpay;

import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.google.common.collect.Lists;
import com.kugou.config.Env;
import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.commons.util.JsonUtils;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.WxRechargeConstant;
import com.kugou.fanxing.recharge.controller.thrift.RpcResultHelper;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.dto.WxSignCallBackDTO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.*;
import com.kugou.fanxing.recharge.model.response.WxAddContractAndPayResp;
import com.kugou.fanxing.recharge.model.response.WxAddContractAndPayResult;
import com.kugou.fanxing.recharge.service.RechargeOrderService;
import com.kugou.fanxing.recharge.service.command.GetFamilyControlCommand;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.thrift.WxPayAndContractRequest;
import com.kugou.fanxing.recharge.thrift.WxPayAndContractResponse;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.thrift.operation.FamilyControllRequest;
import com.kugou.fanxing.thrift.operation.FamilyControllResult;
import com.kugou.fanxing.thrift.operation.FamilyControllService;
import com.kugou.fanxing.thrift.operation.FamilyControllVo;
import com.kugou.fanxing.thrift.pay.v2.ConvertBizOrderNoRequest;
import com.kugou.fanxing.thrift.pay.v2.ConvertBizOrderNoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.kugou.fanxing.recharge.constant.WxRechargeConstant.*;



/**
 * 微信新续费接口（支付并签约）
 * <AUTHOR>
 * @Date 2024/3/12 10:21
 */
@Slf4j
@Service
public class WxRechargeService {
    @Autowired
    private Env env;
    @ApolloConfig
    private Config config;

    @Autowired
    RechargeAcrossDao rechargeAcrossDao;

    @Autowired
    WxPayRpcClient wxPayRpcClient;


    @Autowired
    protected OrderIdService orderIdService;

    @Autowired
    private FamilyControllService.Iface familyControllService;

    @Autowired
    protected RechargeConfig rechargeConfig;

    @Autowired
    private RechargeOrderService rechargeOrderService;


    public static final String UN_SIGN_NOTIFY_URL = "/RechargePlat/RechargeService/RenewalService/wxUnsignCallback";

    public static final String PAY_NOTIFY_URL = "/RechargePlat/RechargeService/RenewalService/wxPayCallback";

    public static final String BUSINESSID_PC_WX_PAY= "PCWxPay";

    /**
     * 支付并签约
     * @param request
     * @return
     */
    public WxPayAndContractResponse payAndContract(WxPayAndContractRequest request) throws Exception {
        String businessId = request.getBusinessId();
        //初始化充值数据
        int bizId = config.getIntProperty("wx_recharge_service.recharge_order_num.bizId",1);
        String orderNo = rechargeOrderService.mappingRechargeOrderNum(bizId,businessId);
        if (StringUtils.isBlank(orderNo)) {
            return RpcResultHelper.buildWxPayAndContractResponse(SysResultCode.WX_RECHARGE_GET_ORDER_FAILED, "");

        }
        //判别家长控制模式
        if (checkFamilyControl(request.getFromKugouId(), request.getPid())) {
            return RpcResultHelper.buildWxPayAndContractResponse(SysResultCode.WX_RECHARGE_FAMILY_CONTROL_FAILED, "");
        }

        int payType = BUSINESSID_PC_WX_PAY.equals(request.getBusinessType()) ? WxRechargeConstant.PAYTYPE_AGENT_PC_WXPAY : WxRechargeConstant.PAYTYPE_AGENT_APP_WXPAY;
        RechargeAcrossPO entity = tranRechargeAcrossPO(payType, request, orderNo);
        String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(orderNo);
        //充值订单入库
        boolean isSuccess = rechargeAcrossDao.insertIgnore(month, entity) > 0;
        if (!isSuccess) {
            return RpcResultHelper.buildWxPayAndContractResponse(SysResultCode.WX_RECHARGE_ORDER_INSERT_FAILED, "");
        }
        //续费调用支付接口
        WxAddContractAndPayRequest addContractAndPayRequest = tranWxAddContractAndPayRequest(request, orderNo, entity);
        WxAddContractAndPayResp resp = wxPayRpcClient.addContractAndPay(addContractAndPayRequest);
        String result = Objects.nonNull(resp) ? JsonUtils.toJsonString(resp) : "";
        if (Objects.isNull(resp) || resp.getError_code() != 0) {
            log.warn("微信支付并签约失败,调用支付接口失败,请求:{},返回:{}", JsonUtils.toJsonString(addContractAndPayRequest), result);
            return RpcResultHelper.buildWxPayAndContractResponse(SysResultCode.WX_RECHARGE_REQUEST_PAY_FAILED, "");
        }
        log.warn("微信支付并签约成功,调用支付接口成功,请求:{},返回:{}", JsonUtils.toJsonString(addContractAndPayRequest), result);
        WxAddContractAndPayResult wxAddContractAndPayResult = JsonUtils.parseObject(resp.getError_msg(),WxAddContractAndPayResult.class);
        //JSAPI:公众号,NATIVE:二维码,APP,MWEB:h5页面
        wxAddContractAndPayResult.setContractType(addContractAndPayRequest.getTrade_type());
        wxAddContractAndPayResult.setRechargeOrderId(orderNo);
        wxAddContractAndPayResult.setBusinessId(businessId);
        if(StringUtils.isNotBlank(wxAddContractAndPayResult.getCode_url())) {
            wxAddContractAndPayResult.setCode_url(URLDecoder.decode(wxAddContractAndPayResult.getCode_url(), StandardCharsets.UTF_8.toString()));
        }
        if(StringUtils.isNotBlank(wxAddContractAndPayResult.getMweb_url())) {
            wxAddContractAndPayResult.setMweb_url(URLDecoder.decode(wxAddContractAndPayResult.getMweb_url(), StandardCharsets.UTF_8.toString()));
        }
        return RpcResultHelper.buildWxPayAndContractResponse(JsonResult.DefaultResultCodeEnum.SUCCESS,JsonUtils.toJsonString(wxAddContractAndPayResult) );

    }
    /**
     * 签约回调，业务暂时不需要处理
     * @param wxSignCallBackDTO
     * @return
     */
    public String wxSignCallback(WxSignCallBackDTO wxSignCallBackDTO) {
        log.warn("微信支付并签约回调请求:{}", JsonUtils.toJsonString(wxSignCallBackDTO));
        return "success";
    }
    /**
     * 是否被家长模式阻断
     * @param fromKugouId
     * @param pid
     * @return
     */
    protected boolean checkFamilyControl(long fromKugouId, int pid) {
        FamilyControllRequest request = new FamilyControllRequest();
        try {
            boolean openFamilyControl = openFamilyControl();
            if (!openFamilyControl) {
                return false;
            }

            request.setAppId(getFamilyControlAppId());
            request.setKugouId(fromKugouId);
            request.setTimestamp(DateHelper.getCurrentSeconds());
            request.setSign(FinanceSignUtils.makeSign(request, getFamilyControlSalt()));
            GetFamilyControlCommand controlCommand = new GetFamilyControlCommand(familyControllService, request);
            Optional<FamilyControllResult> optionalResult = controlCommand.execute();
            if (!optionalResult.isPresent()) {
                return false;
            }
            FamilyControllResult familyControllResult =  optionalResult.get();
            List<Integer> userFamilyControlWebPayPidList = getFamilyControlAppIdPidList();
            if (familyControllResult.getRet() == 0
                    && familyControllResult.getData() != null
                    && familyControllResult.getData().getControllStatus() == 1
                    && userFamilyControlWebPayPidList.contains(pid)) {
                return true;
            }
        } catch (Exception e) {
            log.error("家长控制异常，request:{},fromKugouId:{}", JsonUtils.toJsonString(request), fromKugouId);
            return false;
        }
        return false;
    }
    /**
     * 转换WxAddContractAndPayRequest对象
     * @param request
     * @param orderNo
     * @return
     */
    public WxAddContractAndPayRequest tranWxAddContractAndPayRequest(WxPayAndContractRequest request, String orderNo, RechargeAcrossPO entity) throws Exception {
        WxAddContractAndPayRequest payRequest = new WxAddContractAndPayRequest();
        payRequest.setAppid(String.valueOf(request.getBizAppId()));
        payRequest.setTime(String.valueOf(DateHelper.getUnixtime(new Date())));
        payRequest.setUserid(String.valueOf(request.getFromKugouId()));
        payRequest.setBiz_sign(request.getBizSign());
        payRequest.setClientip(request.getClientIp());
        int addTime = entity.getAddTime();
        int payType = entity.getPayTypeId();
        BigDecimal amount = entity.getAmount();
        ExtendParam extendParam = ExtendParam.builder().bizSign(request.getBizSign())
                .businessId(request.getBusinessId()).businessTime(request.getBusinessTime())
                .addTime(addTime).payTime(addTime).kugouId(request.getFromKugouId())
                .fromKugouId(request.getFromKugouId()).toKugouId(request.getTokugouId())
                .payTypeId(payType).amount(amount).subject(request.getSubject())
                .refer(request.getRefer()).cFrom(request.getCFrom()).channelId(request.getChannelId())
                .version(RECHARGE_VERSION_ONLINE).rebate(0).reType(RETYPE_RENEWAL).businessExt(request.getBusinessExt())
                .userFundPlatParam(tranUserFundPlatParam(request.getUserFundPlatParam())).build();
        ExtendParamCallback extendParamCallback = ExtendParamCallback.builder()
                .callBackSign(createCallbackSign(entity.getRechargeOrderNum(), entity.getKugouId()))
                .callBackArg(extendParam).build();
        payRequest.setExtend(Base64Utils.encodeToString(JsonUtils.toJsonString(extendParamCallback).getBytes(StandardCharsets.UTF_8)));
        payRequest.setSubject(request.getSubject());
        payRequest.setDesc(request.getSubject());
        payRequest.setOrder_no(orderNo);
        payRequest.setTotal_fee(amount.toString());
        //新增
        payRequest.setProtocol(request.getProtocol());
        String tradeType = getTradeType(request.getBusinessType());
        payRequest.setTrade_type(tradeType);
        if("NATIVE".equals(tradeType)){
            payRequest.setProduct_id(entity.getRechargeOrderNum());
        }
        String prefixUrl = getPhpChargePrefix();
        payRequest.setSign_notify_url(getSignNotifyUrl());
        payRequest.setNotify_url(prefixUrl+PAY_NOTIFY_URL);
        payRequest.setUnsign_notify_url(prefixUrl+UN_SIGN_NOTIFY_URL);
        //生成签名
        String sign = generateGen(payRequest, payRequest.getAppid());
        payRequest.setSign(sign);
        return payRequest;
    }

    public UserFundPlatParam tranUserFundPlatParam(com.kugou.fanxing.recharge.thrift.UserFundPlatParam param){
        return new UserFundPlatParam().setSenderDepartmentId(param.getSenderDepartmentId())
                .setGlobalId(param.getGlobalId())
                .setPid(param.getPid())
                .setIp(param.getIp())
                .setSenderProductId(param.getSenderProductId()).setSenderMinorProductId(param.getSenderMinorProductId())
                .setSenderHardwarePlatform(param.getSenderHardwarePlatform()).setSenderChannelId(String.valueOf(param.getSenderChannelId()))
                .setSenderSubChannelId(param.getSenderSubChannelId()).setReceiverDepartmentId(String.valueOf(param.getReceiverDepartmentId()))
                .setAccountChangeType(param.getAccountChangeType()).setFxcChangeDesc(param.getFxcChangeDesc())
                .setCoin(param.getCoin()).setFromKugouId(String.valueOf(param.getFromKugouId()))
                .setToKugouId(String.valueOf(param.getToKugouId())).setRoomId(param.getRoomId()).setGiftId(param.getGiftId()).setGiftName(param.getGiftName())
                .setGiftNum(param.getGiftNum()).setActionId(param.getActionId()).setExt(param.getExt());
        
    }

    /**
     * 生成签名
     * @param request
     * @param appIdStr
     * @return
     */
    public String generateGen(WxAddContractAndPayRequest request, String appIdStr)  throws Exception {
        Map<String,Object> map = JsonUtils.parseMap(JsonUtils.toJsonString(request), Object.class);
        String appKey = getAppKey(appIdStr);
        return DigestUtils.md5Hex(URLDecoder.decode(HttpClientUtils.encodeParam(map), HttpClientUtils.CHARSET) + appKey);

    }
    /**
     * callback参数签名
     * @param orderNo
     * @param kugouId
     * @return
     * @throws Exception
     */
    public String createCallbackSign(String orderNo,long kugouId) throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("order_no",orderNo);
        jsonObject.put("userid",String.valueOf(kugouId));
        String appKey = getCallbackKey();
        return  DigestUtils.md5Hex(JsonUtils.toJsonString(jsonObject) + appKey);

    }


    /**
     * 获取appKey
     * @param appIdStr
     * @return
     */
    public String getAppKey(String appIdStr) {
        int appId = NumberUtils.toInt(appIdStr);
        KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByAppId(appId);
        String appKey = StringUtils.EMPTY;
        if(Objects.nonNull(kupayAppInfoPO)){
            appKey = kupayAppInfoPO.getKupayAppKey();
        }
        return appKey;
    }


    /**
     * 转换对象
     * @param payType
     * @param request
     * @param orderNo
     * @return
     */
    public RechargeAcrossPO tranRechargeAcrossPO(int payType,WxPayAndContractRequest request,String orderNo) {
        Date now = new Date();
        BigDecimal amount = new BigDecimal(request.getAmount());
        //线上取整,其他保留两位小数
        if(env.isProd()) {
            amount = amount.setScale(0, RoundingMode.HALF_UP);
        }else {
            amount = amount.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        RechargeAcrossPO entity = new RechargeAcrossPO();
        entity.setRechargeOrderNum(orderNo);
        entity.setAddTime(DateHelper.getUnixtime(now));
        entity.setKugouId(request.getFromKugouId());
        entity.setFromKugouId(request.getFromKugouId());
        entity.setAmount(amount);
        entity.setMoney(amount);
        entity.setCoupon(BigDecimal.ZERO);
        entity.setCouponId(0);
        entity.setPayTypeId(payType);
        entity.setStatus(STATUS_UNTREATED);
        entity.setRefer(request.getRefer());
        entity.setCFrom(request.getCFrom());
        entity.setChannelId(request.getChannelId());
        entity.setReType(RETYPE_RENEWAL);
        entity.setRechargeId(orderIdService.generateGlobalId());
        entity.setServerRoom(getServerRoom());
        if(StringUtils.isNotBlank(request.getClientIp())) {
            entity.setClientIp(request.getClientIp());
        }
        //数据库默认值为1
        entity.setCoinType(CoinTypeEnum.STAR_COIN.getCoinType());
        //数据库金额默认值为0
        entity.setRealAmount(BigDecimal.ZERO);
        entity.setCoinAfter(BigDecimal.ZERO);
        entity.setCurrencyAmount(BigDecimal.ZERO);
        entity.setUsdAmount(BigDecimal.ZERO);
        entity.setStatExt("{\"gid\":1}");
        return entity;
    }


    /**
     * 是否开启家长模式控制
     * @return
     */
    public boolean openFamilyControl(){
        return config.getBooleanProperty("wx_recharge_service.family_control_open", false);
    }
    /**
     * 家长模式pid列表
     * @return
     */
    public  List<Integer> getFamilyControlAppIdPidList(){
        Config config = ConfigService.getAppConfig();
        String json = config.getProperty("wx_recharge_service.family_control_pid_list", org.apache.commons.lang3.StringUtils.EMPTY);
        if(org.apache.commons.lang3.StringUtils.isBlank(json)){
            return Lists.newArrayList();
        }
        return JsonUtils.parseList(json, Integer.class);
    }
    /**
     * 充值家长模式控制appid
     * @return
     */
    public int getFamilyControlAppId(){
        return config.getIntProperty("wx_recharge_service.family_control_appId",1003);
    }

    /**
     * 充值家长模式控制加密salt
     * @return
     */
    public String getFamilyControlSalt(){
        return config.getProperty("wx_recharge_service.family_control_salt","P00jVQxG");
    }
    /**
     * 根据businessType获取支付方式 JSAPI:公众号,NATIVE:二维码,APP,MWEB:h5
     * @param businessType
     * @return
     */
    public String getTradeType(String businessType){
        return config.getProperty("wx_recharge_service.trade_type."+businessType,"NATIVE");
    }

    /**
     * 获取机房  北京兆维->0 北京亦庄->1 广州->2 , 默认为0
     * @return
     */
    public int getServerRoom(){
        return config.getIntProperty("wx_recharge_service.server_room",0);
    }

    /**
     * 获取php地址前缀
     * 测试环境：http://recharge55.fxwork.kugou.com
     * @return
     */
    public String getPhpChargePrefix(){
        return config.getProperty("wx_recharge_service.php_charge_prefix","http://service.fanxing.kgidc.cn");
    }

    /**
     * 获取签名通知地址
     * 测试环境：http://zuultest.fxwork.kugou.com/platform_recharge_service/intranet/api/v1/wxSignCallback
     * @return
     */
    public String getSignNotifyUrl(){
        return config.getProperty("wx_recharge_service.sign_notify_url","http://fxbj.kgidc.cn/gwphp/platform_recharge_service/intranet/api/v1/wxSignCallback");
    }

    /**
     * 回传秘钥
     * @return
     */

    public String getCallbackKey(){
        return config.getProperty("wx_recharge_service.callback.key","kugoufanxing2016");
    }


}
