package com.kugou.fanxing.recharge.service.refund.v2;

import com.kugou.fanxing.recharge.common.SpringContextHolder;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import org.apache.commons.lang3.exception.ContextedRuntimeException;

public class RefundServiceFactory {

    private RefundServiceFactory() {}

    public static RefundService createRefundService(int payTypeId) {
        if (PayTypeIdEnum.isGooglePlayRecharge(payTypeId)) {
            return SpringContextHolder.getBean(GooglePayRefundService.class);
        }
        if (PayTypeIdEnum.isTmallRecharge(payTypeId)) {
            return SpringContextHolder.getBean(TmallRefundService.class);
        }
        if (PayTypeIdEnum.isAirwallexRecharge(payTypeId)) {
            return SpringContextHolder.getBean(AirwallexRefundService.class);
        }
        throw new ContextedRuntimeException("构建退款实例失败，无法处理的支付渠道")
                .addContextValue("payTypeId", payTypeId);
    }
}
