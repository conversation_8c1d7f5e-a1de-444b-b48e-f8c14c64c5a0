package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.model.vo.NoviceRechargeBaseVO;
import com.kugou.fanxing.recharge.model.vo.NoviceRechargeVO;
import com.kugou.fanxing.thrift.pay.novicerecharge.NoviceRechargeConfigDTO;
import com.kugou.fanxing.thrift.pay.novicerecharge.NoviceRechargeConfigReq;
import com.kugou.fanxing.thrift.pay.novicerecharge.NoviceRechargeConfigResp;
import com.kugou.fanxing.thrift.pay.novicerecharge.NoviceRechargeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NoviceRechargeBusService {
    @Resource
    private NoviceRechargeService.Iface noviceRechargeService;

    public NoviceRechargeVO getNoviceRechargeConfigNew(long kugouId) {
        NoviceRechargeConfigReq req = new NoviceRechargeConfigReq().setKugouId(kugouId);
        NoviceRechargeVO vo = new NoviceRechargeVO();
        try {
            NoviceRechargeConfigResp resp = noviceRechargeService.getCfgNew(req);
            NoviceRechargeConfigDTO noviceRechargeConfigDTO = resp.getData();
            vo.setAwardConfig(noviceRechargeConfigDTO.getDataList()
                    .stream().map(NoviceRechargeBaseVO::convert)
                    .collect(Collectors.toList()));
            vo.setRules(noviceRechargeConfigDTO.getRules());
        } catch (Exception e) {
            log.error("调用getCfgNew接口出现异常", e);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        return vo;
    }
}
