package com.kugou.fanxing.recharge.service.withdraw;

import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.DrawTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawOrderDao;
import com.kugou.fanxing.recharge.model.po.WithdrawOrderPO;
import com.kugou.fanxing.recharge.model.request.QueryWithdrawOrderReq;
import com.kugou.fanxing.recharge.model.request.QueryWithdrawOrderWechatReq;
import com.kugou.fanxing.recharge.service.common.DencryptService;
import com.kugou.fanxing.recharge.util.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

/**
 * 提现网关回查校验
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class WithdrawOrderService {

    @Autowired
    private WithdrawOrderDao withdrawOrderDao;

    @Autowired
    private DencryptService dencryptService;

    /**
     * @param param:param
     * @return Json：data:exist :提现订单号是否存在：1 存在；0 不存在；
     */
    public JsonResult<Map<String, Object>> withdrawOrderVerifyWechat(QueryWithdrawOrderWechatReq param) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("exist", 0);
        try {
            long orderId = param.getOrder_no();
            WithdrawOrderPO withdrawOrder = withdrawOrderDao.getDrawCashOrderById(orderId);
            if (Objects.isNull(withdrawOrder)) {
                log.warn("网关回查校验提现订单，提现订单不存在。param: {}", param);
                return JsonResult.result(SysResultCode.SUCCESS, dataMap);
            }
            if (!isValidWithdrawOrderWechat(param, withdrawOrder)) {
                log.warn("网关回查校验提现订单，提现订单不合法。param: {}", param);
                return JsonResult.result(SysResultCode.SUCCESS, dataMap);
            }
            dataMap.put("exist", 1);
        } catch (Exception e) {
            log.error("网关回查校验提现订单，验证异常。params: {}, dataMap: {}", param, dataMap, e);
            return JsonResult.failure(SysResultCode.RECHARGE_SYS_ERROR, JsonResult.ResultCode::getMsg, dataMap);
        }
        return JsonResult.success("", dataMap);
    }

    public boolean isValidWithdrawOrderWechat(QueryWithdrawOrderWechatReq param, WithdrawOrderPO withdrawOrder) {
        return this.isValidWithdrawOrderWechat(withdrawOrder, param.getBiz_appid(), param.getOpenid(), param.getTotal_fee());
    }

    public boolean isValidWithdrawOrderWechat(WithdrawOrderPO withdrawOrder, int bizAppId, String openid, BigDecimal totalFee) {
        if (withdrawOrder.getBizAppId() == bizAppId && withdrawOrder.getOpenid().equalsIgnoreCase(openid) && (withdrawOrder.getTotalAmount().multiply(BigDecimal.valueOf(100)).compareTo(totalFee) == 0)) {
            return true;
        }
        log.error("网关回查校验提现订单，订单数据校对不一致。bizAppId: {}, openid: {}, totalFee: {}, withdrawOrder: {}", bizAppId, openid, totalFee, withdrawOrder);
        return false;
    }

    /**
     * @param param:param
     * @return Json：data:exist :提现订单号是否存在：1 存在；0 不存在；
     */
    public JsonResult<Map<String, Object>> withdrawOrderVerify(QueryWithdrawOrderReq param) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("exist", 0);
        try {
            long orderId = param.getOrder_no();
            WithdrawOrderPO withdrawOrder = withdrawOrderDao.getDrawCashOrderById(orderId);
            if (Objects.isNull(withdrawOrder)) {
                log.warn("网关回查校验提现订单，提现订单不存在。param: {}", param);
                return JsonResult.result(SysResultCode.SUCCESS, dataMap);
            }
            if (!isValidWithdrawOrder(param, withdrawOrder)) {
                log.warn("网关回查校验提现订单，提现订单不合法。param: {}", param);
                return JsonResult.result(SysResultCode.SUCCESS, dataMap);
            }
            dataMap.put("exist", 1);
        } catch (Exception e) {
            log.error("网关回查校验提现订单，验证异常。params: {}, dataMap: {}", param, dataMap, e);
            return JsonResult.failure(SysResultCode.RECHARGE_SYS_ERROR, JsonResult.ResultCode::getMsg, dataMap);
        }
        return JsonResult.success("", dataMap);
    }

    public boolean isValidWithdrawOrder(QueryWithdrawOrderReq param, WithdrawOrderPO withdrawOrder) {
        int bizAppId = withdrawOrder.getBizAppId();
        String accountEncrypted = withdrawOrder.getAccountEncrypted();
        String account = dencryptService.decrypt(accountEncrypted);
        BigDecimal totalAmount = withdrawOrder.getTotalAmount();
        long kugouId = withdrawOrder.getKugouId();
        if (bizAppId == param.getBiz_appid() && account.equalsIgnoreCase(param.getUser_account()) &&
                (totalAmount.compareTo(param.getTotal_fee()) == 0) && kugouId == param.getUserid()) {
            return true;
        }
        log.error("网关回查校验提现订单，订单数据校对不一致。param: {}, withdrawOrder: {}, account: {}", param, withdrawOrder, account);
        return false;
    }

    public boolean isValidOrder(QueryWithdrawOrderReq param) {
        try {
            WithdrawOrderPO withdrawOrder = withdrawOrderDao.getDrawCashOrderById(param.getOrder_no());
            if (Objects.isNull(withdrawOrder)) {
                log.warn("网关回查校验提现订单，提现订单不存在。param: {}", param);
                return false;
            }
            if (DrawTypeEnum.isStWechatDrawCash(withdrawOrder.getDrawType()) && isValidWithdrawOrderWechat(withdrawOrder, param.getBiz_appid(), param.getUser_account(), param.getTotal_fee())) {
                log.warn("网关回查校验提现订单，提现订单不合法。param: {}", param);
                return true;
            }
        } catch (Exception e) {
            log.error("网关回查校验提现订单，验证异常。params: {}", param, e);
        }
        return false;
    }
}
