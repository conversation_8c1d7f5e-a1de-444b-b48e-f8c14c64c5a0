package com.kugou.fanxing.recharge.service.offline;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.kugou.api.springcloud.GlobalIdServiceThrift.GlobalIdService;
import com.kugou.fanxing.commons.util.JsonUtils;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.BigRebateConfigChangeLogDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.BigRebateConfigDao;
import com.kugou.fanxing.recharge.model.po.offline.BigRebateConfig;
import com.kugou.fanxing.recharge.model.po.offline.BigRebateConfigChangeLog;
import com.kugou.fanxing.recharge.model.po.offline.BigRebateUpdateConfig;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/18
 */
@Service
public class BigRebateConfigService {

    @Autowired
    private BigRebateConfigDao bigRebateConfigDao;

    @Autowired
    private BigRebateConfigChangeLogDao bigRebateConfigChangeLogDao;

    @ApolloConfig
    private Config config;

    @Autowired
    private GlobalIdService.Iface globalIdService;

    @Autowired
    private BigRebateConfigService bigRebateConfigService;

    private static final Logger logger = LoggerFactory.getLogger(BigRebateConfigService.class);


    public int updateBigRebateConfig() {
        List<BigRebateUpdateConfig> configList = getBigRebateConfigList();
        int count = 0;
        List<BigRebateConfig> original = bigRebateConfigDao.queryAll();

        for (BigRebateUpdateConfig config: configList) {
            if (doUpdate(config, original)) {
                count ++;
            }
        }

        return count;
    }


    private List<BigRebateUpdateConfig> getBigRebateConfigList() {
        String value = config.getProperty("big.rebate.config.update", "");
        if (StringUtils.isBlank(value)) {
            return new ArrayList<>(0);
        }
        return JsonUtils.parseList(value, BigRebateUpdateConfig.class);
    }


    private boolean doUpdate(BigRebateUpdateConfig config, List<BigRebateConfig> original) {
        BigRebateConfig before = original.stream()
                .filter(v -> v.getStartAmount().compareTo(config.getStartAmount()) == 0
                        && v.getEndAmount().compareTo(config.getEndAmount()) == 0)
                .findFirst()
                .orElse(null);
        if (before == null) {
            logger.warn("更新返点配置失败，配置不存在，startAmount:{} endAmount:{} ", config.getStartAmount(), config.getEndAmount());
            return false;
        }

        try {
            BigRebateConfigChangeLog log = new BigRebateConfigChangeLog();
            log.setAfterRebateRate(config.getRebateRate());
            log.setBeforeRebateRate(before.getRebateRate());
            log.setStartAmount(config.getStartAmount());
            log.setEndAmount(config.getEndAmount());
            log.setId(globalIdService.get());
            log.setCreateTime(new Date());
            log.setRemark(config.getRemark());
            return bigRebateConfigService.save(log);
        } catch (Exception e) {
            logger.error("更新返点配置失败，startAmount:{} endAmount:{} ", config.getStartAmount(), config.getEndAmount(), e);
            return false;
        }

    }


    @Transactional(transactionManager = "transactionManager_d_fanxing_recharge", propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean save(BigRebateConfigChangeLog log) {
        if (bigRebateConfigDao.updateRebate(log.getStartAmount(), log.getEndAmount(), log.getAfterRebateRate(), log.getCreateTime()) > 0) {
            bigRebateConfigChangeLogDao.addChangeLog(log);
            return true;
        }

        return false;
    }
}
