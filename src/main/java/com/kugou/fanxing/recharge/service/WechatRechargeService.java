package com.kugou.fanxing.recharge.service;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.jayway.jsonpath.TypeRef;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.*;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeOpenDao;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.bo.CouponInfoBO;
import com.kugou.fanxing.recharge.model.bo.KugouOpenBusinessBO;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.bo.KupayAppTypeInfoBO;
import com.kugou.fanxing.recharge.model.dto.WxMiniProgramDTO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.*;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

import static com.kugou.fanxing.recharge.config.RechargeConfig.O_COIN_BUSINESS_ID;

/**
 * 微信支付服务
 * - 微信公众号支付 41
 * - 微信小程序支付 42
 * - 微信网页端支付 39
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class WechatRechargeService {

    @Autowired
    private RechargeConfig rechargeConfig;
    @Autowired
    private OrderIdService orderIdService;
    @Autowired
    private RechargeCouponService rechargeCouponService;
    @Autowired
    private RechargeAcrossDao rechargeAcrossDao;
    @Autowired
    private RechargeCommonService rechargeCommonService;
    @Autowired
    private RechargeOpenDao rechargeOpenDao;
    @Autowired
    private RechargeOrderService rechargeOrderService;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private ValidatingService validatingService;
    @Autowired
    private AgentRechargeService agentRechargeService;
    @Autowired
    private KupayService kupayService;

    /**
     * 微信小程序下单
     *
     * @param rechargeOrderNum     充值订单号
     * @param webCommonParam       通用参数
     * @param wxMiniProgramRequest 业务参数
     * @return
     */
    public Optional<WxMiniProgramDTO> getOrderForWxxcx(String rechargeOrderNum, WebCommonParam webCommonParam, WxMiniProgramRequest wxMiniProgramRequest) {
        try {
            // 调用网关接口
            long kugouId = webCommonParam.getKugouId();
            BigDecimal money = wxMiniProgramRequest.getMoney();
            String url = rechargeConfig.getActionUrlPrefix(PayTypeIdEnum.PAY_TYPE_ID_42);
            RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(webCommonParam.getPid(), kugouId, rechargeOrderNum, PayTypeIdEnum.PAY_TYPE_ID_42, money, webCommonParam.getIp());
            Map<String, Object> extendParam = Maps.newHashMap();
            extendParam.put("businessExt", StringUtils.defaultString(webCommonParam.getExt()));
            String extend = this.rechargeCommonService.buildExtendStr(rechargeAcrossPO,extendParam);
            Map<String, String> wxMiniProgramParams = buildWxMiniProgramParams(rechargeAcrossPO, wxMiniProgramRequest.getOpenId(), kugouId, money, extend, ReTypeEnum.RETYPE_RECHARGE);
            log.warn("调用酷狗支付网关小程序下单接口, url: {}, params: {}", url, wxMiniProgramParams);
            Optional<String> optionalJson = HttpClientUtils.doSyncGet(url, wxMiniProgramParams);

            // 校验网关响应
            if (!optionalJson.isPresent()) {
                log.error("调用酷狗支付网关小程序下单失败, url: {}, params: {}, optionalJson: {}", url, wxMiniProgramParams, optionalJson);
                return Optional.empty();
            }
            String json = optionalJson.get();
            Configuration conf = Configuration.defaultConfiguration().addOptions(Option.SUPPRESS_EXCEPTIONS);
            int errorCode = JsonPath.using(conf).parse(json).read("$.error_code", Integer.class);
            String errorMsg = JsonPath.using(conf).parse(json).read("$.error_msg", String.class);
            if (errorCode != 0) {
                log.error("调用酷狗支付网关小程序下单失败, url: {}, params: {}, errorCode: {}, errorMsg: {}", url, wxMiniProgramParams, errorCode, errorMsg);
                return Optional.empty();
            }

            // 解析有效载荷
            log.warn("调用酷狗支付网关小程序下单成功, json: {}", json);
            String orderNo = JsonPath.using(conf).parse(json).read("$.order_no", String.class);
            String outTradeNo = JsonPath.using(conf).parse(json).read("$.out_trade_no", String.class);
            String data = JsonPath.using(conf).parse(json).read("$.data", String.class);
            WxMiniProgramDTO wxMiniProgramDTO = JSON.parseObject(data, WxMiniProgramDTO.class);
            if (StringUtils.isBlank(orderNo) || StringUtils.isBlank(outTradeNo) || !rechargeOrderNum.equals(orderNo) || Objects.isNull(wxMiniProgramDTO)) {
                log.error("解析酷狗支付网关小程序订单失败, url: {}, params: {}, json: {}", url, wxMiniProgramParams, json);
                return Optional.empty();
            }

            // 保存充值订单
            Optional<String> optionalMonth = this.orderIdService.getYearMonthFromRechargeOrderNum(rechargeOrderNum);
            String month = optionalMonth.orElseThrow(() -> new ContextedRuntimeException("解析充值订单号月份信息失败").addContextValue("rechargeOrderNum", rechargeOrderNum));
            int affected = rechargeAcrossDao.add(month, rechargeAcrossPO);
            if (affected < 1) {
                log.error("记录小程序下单信息失败, rechargeAcrossPO: {}, affected: {}", rechargeAcrossPO, affected);
                return Optional.empty();
            }
            return Optional.of(wxMiniProgramDTO);
        } catch (Exception e) {
            log.error("微信小程序下单异常, webCommonParam: {}, wxMiniProgramRequest: {}", webCommonParam, wxMiniProgramRequest, e);
        }
        return Optional.empty();
    }

    /**
     * 构建小程序下单接口参数
     *
     * @param rechargeAcrossPO 直播充值订单
     * @param openId           微信OpenID
     * @param kugouId          酷狗ID
     * @param money            充值金额
     * @return
     */
    public Map<String, String> buildWxMiniProgramParams(RechargeAcrossPO rechargeAcrossPO, String openId, long kugouId, BigDecimal money, String extend, ReTypeEnum reTypeEnum) {
        Map<String, String> params = Maps.newTreeMap();
        PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(rechargeAcrossPO.getPayTypeId());
        KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(payTypeIdEnum);
        params.put("appid", String.valueOf(kupayAppInfoPO.getKupayAppId()));
        params.put("userid", String.valueOf(kugouId));
        params.put("auserid", String.valueOf(kugouId));
        params.put("order_no", rechargeAcrossPO.getRechargeOrderNum());
        params.put("subject", "星币充值服务");
        params.put("desc", "星币充值服务");
        params.put("total_fee", money.stripTrailingZeros().toPlainString());
        params.put("clientip", IpUtils.getClientIpAddress());
        params.put("sign_type", "md5");
        params.put("openid", openId);
        params.put("notify_url", rechargeConfig.getNotifyUrl(reTypeEnum));
        params.put("time", String.valueOf(System.currentTimeMillis()));
        params.put("extend", extend);
        params.put("sign", SignUtils.buildSign(params, kupayAppInfoPO.getKupayAppKey()));
        return params;
    }

    /**
     * 微信公众号下单
     *
     * @param webCommonParam 通用参数
     * @param wxgzhRequest   业务参数
     */
    public Map<String, Object> getOrderForWxgzh(WebCommonParam webCommonParam, WxgzhRequest wxgzhRequest) {
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        // 请求参数校验
        SysResultCode resultCode = this.rechargeCommonService.checkPreconditionForGetOrder(rechargeOrderNum, webCommonParam, wxgzhRequest);
        if (!resultCode.isSuccess()) {
            log.warn("微信公众号下单，前置校验失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(resultCode);
        }
        long kugouId = webCommonParam.getKugouId();
        int pid = webCommonParam.getPid();
        BigDecimal amount = wxgzhRequest.getAmount();
        long couponId = wxgzhRequest.getCouponId();
        // 解析订单月份
        String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
        // 使用代金券
        CoinTypeEnum coinTypeEnum = CoinTypeEnum.of(wxgzhRequest.getCoinType());
        Optional<CouponInfoBO> optionalCouponInfoBO = this.rechargeCouponService.handleCouponForOrder(kugouId, couponId, amount, coinTypeEnum);
        try {
            RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(pid, kugouId, 0,
                    rechargeOrderNum, wxgzhRequest.getPayTypeIdEnum(), amount, webCommonParam.getIp(), coinTypeEnum, optionalCouponInfoBO);
            int affected = rechargeAcrossDao.add(month, rechargeAcrossPO);
            if (affected < 1) {
                log.warn("创建微信公众号订单，保存订单失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            // 生成充值链接

            Map<String, String> extParam = Maps.newHashMap();
            extParam.put("sync_url", webCommonParam.getReferer());
            extParam.put("expire_time", rechargeCommonService.getOrderExpireTime());
            if (StringUtils.isNotBlank(wxgzhRequest.getOpenId())) {
                extParam.put("openid", wxgzhRequest.getOpenId());
            }
            Map<String, Object> dataMap = this.rechargeCommonService.createRechargeResultForMobile(rechargeAcrossPO, webCommonParam.getExt(),webCommonParam.getIp(), extParam);
            if (dataMap.isEmpty()) {
                log.warn("创建微信公众号订单，生成链接失败。rechargeAcrossPO: {}", rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            return dataMap;
        } catch (Exception e) {
            log.warn("创建微信公众号订单，生成链接失败。rechargeOrderNum: {}", rechargeOrderNum);
            optionalCouponInfoBO.ifPresent(couponInfoBO -> this.rechargeCouponService.unFreezeCouponQuietly(kugouId, rechargeOrderNum, couponInfoBO));
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    public Map<String, Object> getOrderForWechat(WebCommonParam webCommonParam, WechatRequest wechatRequest) {
        long kugouId = webCommonParam.getKugouId();
        long rechargeKugouId = wechatRequest.getRechargeKugouId();
        int pid = webCommonParam.getPid();
        BigDecimal amount = wechatRequest.getAmount();
        long couponId = wechatRequest.getCouponId();

        // 解析订单月份
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        Optional<String> optionalMonth = orderIdService.getYearMonthFromRechargeOrderNum(rechargeOrderNum);
        if (!optionalMonth.isPresent()) {
            log.warn("创建网页微信订单，解析订单月份失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        // 处理外部检查
        SysResultCode resultCode = this.rechargeCommonService.checkThirdPart(rechargeOrderNum, webCommonParam, wechatRequest);
        if (!resultCode.isSuccess()) {
            log.warn("创建网页微信订单，处理外部检查失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(resultCode);
        }
        // 检查用户代充账号
        resultCode = this.agentRechargeService.checkAgentRecharge(webCommonParam, wechatRequest.getRechargeKugouId(), wechatRequest.getCouponId());
        if (!resultCode.isSuccess()) {
            log.warn("创建网页微信订单，处理代充检查失败。webCommonParam: {}, bankRequest: {}", webCommonParam, wechatRequest);
            throw new BizException(resultCode);
        }
        // 使用代金券
        CoinTypeEnum coinTypeEnum = CoinTypeEnum.of(wechatRequest.getCoinType());
        Optional<CouponInfoBO> optionalCouponInfoBO = this.rechargeCouponService.handleCouponForOrder(kugouId, couponId, amount, coinTypeEnum);
        try {
            PayTypeIdEnum payTypeIdEnum = wechatRequest.getPayTypeIdEnum();
            RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(pid, kugouId, rechargeKugouId,
                    rechargeOrderNum, payTypeIdEnum, amount, webCommonParam.getIp(), coinTypeEnum, optionalCouponInfoBO);
            // 保存充值订单
            String month = optionalMonth.get();
            int affected = rechargeAcrossDao.add(month, rechargeAcrossPO);
            if (affected < 1) {
                log.warn("创建网页微信订单，保存订单失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            // 生成充值链接
            boolean isFrontQrCode = wechatRequest.getFrontQrCode() == 1;
            Map<String, String> extParam = isFrontQrCode ? Maps.newHashMap() : this.createRechargeExParam();
            Cat.logEvent("PLATFORM_RECHARGE_SERVICE", "FrontQrCode", isFrontQrCode ? "0" : "1", "");
            Map<String, Object> extendParam = Maps.newHashMap();
            HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
            if (StringUtils.isNotBlank(request.getParameter("roomId"))) {
                extendParam.put("roomId", request.getParameter("roomId"));
            }
            extendParam.put("businessExt", StringUtils.defaultString(webCommonParam.getExt()));
            Map<String, Object> dataMap = this.rechargeCommonService.createWxRechargeResult(rechargeAcrossPO,webCommonParam.getIp(), extParam, isFrontQrCode, extendParam);
            if (dataMap.isEmpty()) {
                log.warn("创建网页微信订单，生成链接失败。rechargeAcrossPO: {}", rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            return dataMap;
        } catch (Exception e) {
            log.warn("创建网页微信订单，生成链接异常。rechargeOrderNum: {}", rechargeOrderNum, e);
            optionalCouponInfoBO.ifPresent(couponInfoBO -> this.rechargeCouponService.unFreezeCouponQuietly(kugouId, rechargeOrderNum, couponInfoBO));
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    public Map<String, String> createRechargeExParam() {
        Map<String, String> extParam = Maps.newHashMap();
        // 二维码类型 0：返回短链接(充值地址) 1：自动跳到到二维码图片 2：二维码图片。注意：qr为0不参加签名验证
        extParam.put("qr", String.valueOf(2));
        extParam.put("size", String.valueOf(5));
        extParam.put("margin", String.valueOf(1));
        return extParam;
    }

    public Map<String, Object> getOrderForWechatMobile(WebCommonParam webCommonParam, WechatMobileRequest wechatRequest,
                                                       CoinTypeEnum coinTypeEnum, boolean useStdPlat) {
        long kugouId = webCommonParam.getKugouId();
        int pid = useStdPlat ? webCommonParam.getStdPlat() : webCommonParam.getPid();
        BigDecimal amount = wechatRequest.getAmount();
        long couponId = wechatRequest.getCouponId();
        PayTypeIdEnum payTypeIdEnum = wechatRequest.getPayTypeIdEnum();
        // 直播充值单号
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        // 检查前置条件
        SysResultCode resultCode = this.rechargeCommonService.checkPreconditionForGetOrder(rechargeOrderNum, webCommonParam, wechatRequest);
        if (!resultCode.isSuccess()) {
            log.warn("创建微信订单失败，检查前置条件失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(resultCode);
        }
        // 处理代金券
        Optional<CouponInfoBO> optionalCouponInfoBO = this.rechargeCouponService.handleCouponForOrder(kugouId, couponId, amount, coinTypeEnum);
        try {
            RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(pid,
                    kugouId, 0, rechargeOrderNum, payTypeIdEnum,
                    amount, webCommonParam.getIp(), coinTypeEnum, optionalCouponInfoBO);
            String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
            int affected = rechargeAcrossDao.add(month, rechargeAcrossPO);
            if (affected < 1) {
                log.warn("创建微信订单失败，保存订单失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            // 生成充值链接
            Map<String, Object> extendParam = Maps.newHashMap();
            extendParam.put("businessExt", StringUtils.defaultString(webCommonParam.getExt()));
            Map<String, String> extParam = Maps.newHashMap();
            extParam.putAll(createRechargeParamByPayType(wechatRequest.getApptype(), rechargeAcrossPO.getCFrom()));
            Map<String, Object> dataMap = this.rechargeCommonService.createRechargeResultForMobileByPost(rechargeAcrossPO, extParam, extendParam);
            if (dataMap.isEmpty()) {
                log.warn("创建微信订单失败，生成链接失败。rechargeAcrossPO: {}", rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            dataMap.put("orderInfo", ImmutableMap.of("rechargeOrderNum", rechargeOrderNum));
            return dataMap;
        } catch (Exception e) {
            log.error("创建微信订单失败，生成链接异常。webCommonParam: {}, wechatRequest: {}", webCommonParam, wechatRequest);
            optionalCouponInfoBO.ifPresent(couponInfoBO -> this.rechargeCouponService.unFreezeCouponQuietly(kugouId, rechargeOrderNum, couponInfoBO));
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    /**
     * 获取支付网关apptype参数
     * @param appType 前端传递的AppType参数
     * @param cFrom   客户端平台号
     * @return 支付网关apptype参数
     */
    public Map<String, String> createRechargeParamByPayType(String appType, int cFrom) {
        String kupayAppType = this.rechargeConfig.useKupayAppTypeDirectly(appType) ? appType : this.rechargeConfig.getKupayAppTypeInfoByPid(cFrom);
        Map<String, String> extParam = Maps.newHashMap();
        extParam.put("expire_time", rechargeConfig.getExpireTime());
        extParam.put("apptype", kupayAppType);
        return extParam;
    }

    public Map<String, Object> getOrderForWechatH5(WebCommonParam webCommonParam, GetOrderH5Request wechatRequest) {
        long kugouId = webCommonParam.getKugouId();
        int pid = webCommonParam.getPid();
        BigDecimal amount = wechatRequest.getAmount();
        long couponId = wechatRequest.getCouponId();

        // 解析订单月份
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
        // 处理外部检查
        SysResultCode resultCode = this.rechargeCommonService.checkPreconditionForGetOrder(rechargeOrderNum, webCommonParam, wechatRequest);
        if (!resultCode.isSuccess()) {
            log.warn("创建H5微信订单，处理外部检查失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(resultCode);
        }
        // 使用代金券
        CoinTypeEnum coinTypeEnum = CoinTypeEnum.of(wechatRequest.getCoinType());
        Optional<CouponInfoBO> optionalCouponInfoBO = this.rechargeCouponService.handleCouponForOrder(kugouId, couponId, amount, coinTypeEnum);
        try {
            PayTypeIdEnum payTypeIdEnum = wechatRequest.getPayTypeIdEnum();
            RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(pid, kugouId, 0,
                    rechargeOrderNum, payTypeIdEnum, amount, webCommonParam.getIp(), coinTypeEnum, optionalCouponInfoBO);
            // 保存充值订单
            int affected = rechargeAcrossDao.add(month, rechargeAcrossPO);
            if (affected < 1) {
                log.warn("创建H5微信订单，保存订单失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            // 生成充值链接
            Map<String, String> extParam = new HashMap<>();
            String syncUrl = StringEscapeUtils.unescapeXml(StringUtils.defaultString(wechatRequest.getSyncUrl(),webCommonParam.getReferer()));
            extParam.put("sync_url",syncUrl);
            extParam.put("wxurl","1");
            Map<String, Object> extendParam = Maps.newHashMap();
            extendParam.put("businessExt", StringUtils.defaultString(webCommonParam.getExt()));
            Map<String, Object> dataMap = this.rechargeCommonService.createRechargeResultWechatH5(rechargeAcrossPO, extParam, extendParam, Optional.empty());
            if (dataMap.isEmpty()) {
                log.warn("创建H5微信订单，生成链接失败。rechargeAcrossPO: {}", rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            return dataMap;
        } catch (Exception e) {
            log.warn("创建H5微信订单，生成链接异常。rechargeOrderNum: {}", rechargeOrderNum, e);
            optionalCouponInfoBO.ifPresent(couponInfoBO -> this.rechargeCouponService.unFreezeCouponQuietly(kugouId, rechargeOrderNum, couponInfoBO));
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    public Map<String, Object> getOrderWechatM(WebCommonParam webCommonParam, OpenWechatMRequest request, KugouOpenBusinessBO kugouOpenBusinessBO) {
        long kugouId = webCommonParam.getKugouId();
        int pid = webCommonParam.getPid();
        BigDecimal amount = request.getAmount();
        // 直播充值单号
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        // 检查前置条件
        SysResultCode resultCode = this.rechargeCommonService.checkPreconditionForGetOrder(rechargeOrderNum, webCommonParam, request);
        if (!resultCode.isSuccess()) {
            log.warn("创建微信订单失败，检查前置条件失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(resultCode);
        }
        // 处理代金券
        RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(pid, kugouId, rechargeOrderNum, request.getPayTypeIdEnum(), amount, webCommonParam.getIp());
        String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
        int affected = this.rechargeOpenDao.add(month, rechargeAcrossPO);
        if (affected < 1) {
            log.warn("创建微信订单失败，保存订单失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        // 生成充值链接
        Map<String, String> extParam = Maps.newHashMap();
        extParam.put("notify_url", this.rechargeConfig.getOpenNotifyUrl());
        extParam.putAll(createRechargeParamByPayType(kugouOpenBusinessBO.getAppType(), rechargeAcrossPO.getCFrom()));
        Map<String, Object> extendParam = Maps.newHashMap();
        extendParam.put("userOpenid", webCommonParam.getKugouOpenDispatchParam().getUser_openid());
        extendParam.put("businessId", request.getBusinessId());
        extendParam.put("businessExt", StringUtils.defaultString(webCommonParam.getExt()));
        Map<String, Object> dataMap = this.rechargeCommonService.createRechargeResultForMobileByPost(rechargeAcrossPO,extParam, extendParam, kugouOpenBusinessBO.getKupayAppId());
        if (dataMap.isEmpty()) {
            log.warn("创建微信订单失败，生成链接失败。rechargeAcrossPO: {}", rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        dataMap.put("orderInfo", ImmutableMap.of("rechargeOrderNum", rechargeOrderNum));
        return dataMap;
    }

    public JsonResult<Map<String, Object>> refreshWechatQrCode(WebCommonParam webCommonParam, String rechargeOrderNum) {
        Optional<RechargeAcrossPO> optionalRechargeAcrossPO = this.rechargeOrderService.queryByRechargeOrderNum(rechargeOrderNum);
        if (!optionalRechargeAcrossPO.isPresent() || webCommonParam.getKugouId() != optionalRechargeAcrossPO.get().getKugouId()) {
            log.warn("网页微信刷新二维码信息，下单记录不存在。webCommonParam: {}, rechargeOrderNum: {}", webCommonParam, rechargeOrderNum);
            return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, Maps.newHashMap());
        }
        int serverId = 1870;
        Date current = new Date();
        int wechatQrCodeExpireMinutes = this.apolloConfigService.getWechatQrCodeExpireMinutes();
        int wechatQrCodeRefreshSecond = this.apolloConfigService.getWechatQrCodeRefreshSecond();
        String url = rechargeConfig.getKupayIntranet() + "/v1/wxnativerefresh";
        int kupayAppId = 1084;
        Optional<KupayAppTypeInfoBO> optionalKupayAppTypeInfoBO = rechargeConfig.getKupayAppTypeInfoBO(webCommonParam.getPid());
        if (optionalKupayAppTypeInfoBO.isPresent()) {
            kupayAppId = optionalKupayAppTypeInfoBO.get().getKupayAppId();
        }
        Map<String, String> params = Maps.newHashMap();
        params.put("serverid", String.valueOf(serverId));
        params.put("servertime", String.valueOf(DateHelper.getCurrentSeconds()));
        params.put("appid", String.valueOf(kupayAppId));
        params.put("clientver", "-");
        params.put("mid", "-");
        params.put("uuid", "-");
        params.put("dfid", "-");
        params.put("clientip", webCommonParam.getIp());
        params.put("order_no", rechargeOrderNum);
        params.put("expire_time", DateFormatUtils.format(DateUtils.addMinutes(current, wechatQrCodeExpireMinutes), "yyyyMMddHHmmss"));
        params.put("signature", SignUtils.buildSignByKugouOpenIntrospect(params, "", rechargeConfig.getKupayServerInfoByServerId(serverId).getKupayServerKey()));
        Optional<String> optionalJson = HttpClientUtils.doSyncGet(url, params);
        if (!optionalJson.isPresent() || StringUtils.isBlank(optionalJson.get())) {
            log.warn("网页微信刷新二维码信息，支付网关响应为空。url: {}, params: {}, optionalJson: {}", url, params, optionalJson);
            return JsonResult.result(SysResultCode.RECHARGE_SYS_ERROR, Maps.newHashMap());
        }
        String json = optionalJson.get();
        int status = JsonUtils.parseJsonPath(json, "$.status", Integer.class, 0);
        int errorCode = JsonUtils.parseJsonPath(json, "$.error_code", Integer.class, 0);
        String errorMsg = JsonUtils.parseJsonPath(json, "$.error_msg", String.class, SysResultCode.RECHARGE_SYS_ERROR.getMsg());
        if (status != 1 || errorCode != 0) {
            log.warn("网页微信刷新二维码信息，支付网关响应失败。url: {}, params: {}, optionalJson: {}, status: {}, errorCode: {}", url, params, optionalJson, status, errorCode);
            return JsonResult.result(SysResultCode.RECHARGE_SYS_ERROR, errorMsg, Maps.newHashMap());
        }
        Map<String, Object> map = JsonUtils.parseJsonPathMap(json, "$.data", new TypeRef<Map<String, Object>>() {});
        // 返回二维码失效的时间戳，单位：秒
        map.put("serverTimeSec", String.valueOf(current.getTime() / 1000));
        map.put("refreshTimeSec", String.valueOf(Math.toIntExact(DateUtils.addMinutes(current, wechatQrCodeExpireMinutes).getTime() / 1000) - wechatQrCodeRefreshSecond));
        return JsonResult.result(SysResultCode.SUCCESS, map);
    }

    public Map<String, Object> gameWechatH5(WebCommonParam webCommonParam, GetOrderH5Request request) {
        long kugouId = webCommonParam.getKugouId();
        int pid = webCommonParam.getPid();
        BigDecimal amount = request.getAmount();
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
        // 处理外部检查
        SysResultCode resultCode = this.rechargeCommonService.checkThirdPart(rechargeOrderNum, webCommonParam, request);
        if (!resultCode.isSuccess()) {
            log.warn("游戏H5微信订单，处理外部检查失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(resultCode);
        }
        RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(pid, kugouId, rechargeOrderNum, request.getPayTypeIdEnum(), amount, webCommonParam.getIp());
        rechargeAcrossPO.setBusinessId(O_COIN_BUSINESS_ID);
        // 保存充值订单
        int affected = rechargeOpenDao.add(month, rechargeAcrossPO);
        if (affected < 1) {
            log.warn("游戏H5微信订单，保存订单失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        // 生成充值链接
        Map<String, String> kupayParam = Maps.newHashMap();
        kupayParam.put("sync_url", StringEscapeUtils.unescapeXml(StringUtils.defaultString(request.getSyncUrl(), webCommonParam.getReferer())));
        kupayParam.put("wxurl","1");
        kupayParam.put("notify_url", rechargeConfig.getRechargeZuulNotifyUrl(UrlConstants.GAME_PURCHASE_CURRENCY_CALLBACK));
        Map<String, Object> extendParam = Maps.newHashMap();
        extendParam.put("businessExt", StringUtils.defaultString(webCommonParam.getExt()));
        extendParam.put("businessId", O_COIN_BUSINESS_ID);
        Optional<KupayAppInfoBO> optionalKupayAppInfoBO = Optional.ofNullable(this.rechargeConfig.getKupayAppIdByAppId(this.apolloConfigService.getOCoinKupayAppId()));
        Map<String, Object> dataMap = this.rechargeCommonService.createRechargeResultWechatH5(rechargeAcrossPO, kupayParam, extendParam, optionalKupayAppInfoBO);
        if (dataMap.isEmpty()) {
            log.warn("游戏H5微信订单，生成链接失败。rechargeAcrossPO: {}", rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        return dataMap;

    }

    public Map<String, Object> purchaseProductsForH5(RechargeAcrossPO rechargeAcrossPO, Map<String, String> kupaysParam, Map<String, Object> extendParam) {
        // 保存充值订单
        int affected = rechargeOrderService.addRechargeOrder(rechargeAcrossPO);
        if (affected < 1) {
            log.warn("微信H5购买商品，保存订单失败。rechargeAcrossPO: {}", rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        Map<String, Object> payload = this.rechargeCommonService.createRechargeResultWechatH5(rechargeAcrossPO, kupaysParam, extendParam, Optional.empty());
        if (payload.isEmpty()) {
            log.warn("微信H5购买商品，生成链接失败。rechargeAcrossPO: {}", rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        payload.put("rechargeOrderNum", rechargeAcrossPO.getRechargeOrderNum());
        return payload;
    }

    public Map<String, Object> purchaseProductsForQr(RechargeAcrossPO rechargeAcrossPO, Map<String, String> kupaysParam, Map<String, Object> extendParam) {
        // 保存充值订单
        int affected = rechargeOrderService.addRechargeOrder(rechargeAcrossPO);
        if (affected < 1) {
            log.warn("创建网页微信订单，保存订单失败。rechargeAcrossPO: {}", rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        // 注意⚠️：传递qr、size、margin会由支付网关生成二维码，否则由前端根据微信短链生成二维码
        Map<String, Object> payload = Maps.newHashMap();
        if (kupaysParam.containsKey("qr") && kupaysParam.containsKey("size") && kupaysParam.containsKey("margin")) {
            String qrUrl = this.kupayService.wxnativepayV1(rechargeAcrossPO, kupaysParam, extendParam);
            payload.put("qrUrl", qrUrl);
        } else {
            String codeUrl = this.kupayService.wxnativepayV1(rechargeAcrossPO, kupaysParam, extendParam);
            payload.put("codeUrl", codeUrl);
        }
        return payload;
    }
}
