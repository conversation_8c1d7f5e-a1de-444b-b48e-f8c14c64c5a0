package com.kugou.fanxing.recharge.service.refund.v2;

import com.kugou.fanxing.recharge.model.dto.RefundNotifyDto;
import com.kugou.fanxing.recharge.model.po.RefundOrderV2Po;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class ApplePayRefundService extends AbstractRefundService {

    @Override
    protected <T extends RefundNotifyDto> Optional<RefundOrderV2Po> convertRefundNotifyDtoToRefundOrder(T t) {
        throw new UnsupportedOperationException();
    }
}
