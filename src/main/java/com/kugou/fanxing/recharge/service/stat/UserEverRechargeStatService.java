package com.kugou.fanxing.recharge.service.stat;

import com.google.gson.Gson;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.factory.ConsumeServiceFactory;
import com.kugou.fanxing.recharge.model.vo.UserEntity;
import com.kugou.fanxing.recharge.service.common.CacheService;
import com.kugou.fanxing.recharge.thrift.UserEverRechargeDTO;
import com.kugou.fanxing.thrift.consume.service.ConsumeResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class UserEverRechargeStatService {

    @Autowired
    private CacheService cacheService;
    @Autowired
    private ConsumeServiceFactory consumeServiceFactory;

    public boolean hasUserEverRecharge(long kugouId) {
        UserEverRechargeDTO userEverRechargeDTO = this.getUserEverRecharge(kugouId);
        return Objects.nonNull(userEverRechargeDTO) && userEverRechargeDTO.getCoins() > 0;
    }

    public UserEverRechargeDTO getUserEverRecharge(long kugouId) {
        UserEverRechargeDTO userEverRechargeDTO = null;
        String cacheKey = "user.ever.recharge:" + kugouId;
        String value = cacheService.get(cacheKey);
        if (!StringUtils.isEmpty(value)) {
            userEverRechargeDTO = new Gson().fromJson(value, UserEverRechargeDTO.class);
        }
        if (userEverRechargeDTO != null) {
            return userEverRechargeDTO;
        }
        Optional<UserEntity> optionalUserEntity = getUserEntity(kugouId, 10018);
        if (!optionalUserEntity.isPresent()) {
            return null;
        }
        UserEntity userEntity = optionalUserEntity.get();
        userEverRechargeDTO = new UserEverRechargeDTO();
        userEverRechargeDTO.setCoins(userEntity.getCzTotal().intValue());
        userEverRechargeDTO.setKugouId(kugouId);
        int cacheTime = 120;
        if (userEverRechargeDTO.getCoins() == 0) {
            cacheTime = 5;
        }
        cacheService.setex(cacheKey, cacheTime, new Gson().toJson(userEverRechargeDTO));
        return userEverRechargeDTO;
    }

    public Optional<UserEntity> getUserEntity(long kugouId, int appId) {
        return this.getUserEntity(kugouId, appId, CoinTypeEnum.STAR_COIN.getCoinType());
    }

    public Optional<UserEntity> getUserEntity(long kugouId, int appId, int coinType) {
        try {
            ConsumeResp consumeResp = consumeServiceFactory.getConsumeReadService(coinType).getUserMoney(kugouId, appId);
            if (Objects.isNull(consumeResp) || consumeResp.getRet() != 0 || StringUtils.isBlank(consumeResp.getData())) {
                log.error("查询用户历史充值总额，调用消费查询失败。kugouId: {}, appId: {}, consumeResp: {}", kugouId, appId, consumeResp);
                throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
            }
            UserEntity userEntity = new Gson().fromJson(consumeResp.getData(), UserEntity.class);
            if (userEntity == null) {
                log.error("查询用户历史充值总额，解析消费响应失败。kugouId: {}, appId: {}, consumeResp: {}", kugouId, appId, consumeResp);
                throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
            }
            return Optional.of(userEntity);
        } catch (Exception e) {
            log.error("查询用户历史充值总额，调用消费查询异常。kugouId: {}, appId: {}", kugouId, appId, e);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR, e);
        }
    }
}
