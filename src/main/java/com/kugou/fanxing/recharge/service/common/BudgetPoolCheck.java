package com.kugou.fanxing.recharge.service.common;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.kugou.fanxing.budgetservice.call.BudgetServiceHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;

@Slf4j
@Component
public class BudgetPoolCheck {

    @ApolloConfig
    private Config config;

    @Autowired
    private BudgetServiceHelper budgetServiceHelper;

    public boolean isBudgetCheckOpen(long accountChangeType) {
        boolean budgeCheckOpen = config.getBooleanProperty("budget.check.open", true);
        if (budgeCheckOpen) {
            return true;
        }
        try {
            String[] string = config.getArrayProperty("need.budget.check.business.id", ",", new String[]{});
            return Arrays.asList(string).contains(String.valueOf(accountChangeType));
        } catch (Exception e) {
            log.info("budgetOpenCheckFail", e);
            return false;
        }
    }


    /**
     * 发送nsq confirm预算,异步线程池执行
     */
    public void confirmDecreaseBudgetOrder(int businessId, long orderId, long orderTime) {
        try {
            if (!isBudgetCheckOpen(businessId)) {
                return;
            }
            budgetServiceHelper.confirmDecreaseBudget(businessId, 0, String.valueOf(orderId), orderTime);
            log.warn("预算扣减确认。businessId: {}, orderId: {}", businessId, orderId);
        } catch (Exception e) {
            log.warn("confirmDecreaseBudgetOrder exception", e);
        }
    }

    /**
     * 发送nsq cancel预算,异步线程池执行
     */
    public void cancelDecreaseBudgetOrder(int businessId, long orderId, long orderTime) {
        try {
            if (!isBudgetCheckOpen(businessId)) {
                return;
            }
            budgetServiceHelper.cancelDecreaseBudget(businessId, 0, String.valueOf(orderId), orderTime);
        } catch (Exception e) {
            log.warn("cancelDecreaseBudgetOrder exception", e);
        }
    }

    /**
     * 发送nsq increase预算,异步线程池执行
     */
    public void increaseBudgetOrder(int businessId, int assertId, BigDecimal coin, long orderId, long orderTime) {
        try {
            if (!isBudgetCheckOpen(businessId)) {
                return;
            }
            budgetServiceHelper.increaseBudget(businessId, 0, assertId, coin.toString(), String.valueOf(orderId), orderTime);
        } catch (Exception e) {
            log.warn("increaseBudgetOrder exception", e);
        }
    }

    public boolean tryDecreaseBudgetOrder(int businessId, int assertId, BigDecimal coin, long orderId, long orderTime) {
        try {
            if (!isBudgetCheckOpen(businessId)) {
                return true;
            }
            log.warn("预算扣减冻结。businessId: {}, orderId: {}", businessId, orderId);
            return budgetServiceHelper.tryDecreaseBudget(businessId, 0, assertId, coin.toString(), String.valueOf(orderId), orderTime);
        } catch (Exception e) {
            log.warn("tryDecreaseBudgetOrder exception", e);
            return true;
        }
    }
}
