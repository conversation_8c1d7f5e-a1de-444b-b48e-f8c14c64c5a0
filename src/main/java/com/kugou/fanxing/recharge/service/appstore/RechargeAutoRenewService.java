package com.kugou.fanxing.recharge.service.appstore;

import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeRenewalDao;
import com.kugou.fanxing.recharge.model.po.RechargeRenewalPO;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;


@Slf4j
@Service
public class RechargeAutoRenewService {

    @Autowired
    private RechargeRenewalDao rechargeRenewalDao;

    public Optional<RechargeRenewalPO> getLatestUserRenewal(long kugouId, int renewalType, long bizId) {
        RechargeRenewalPO latestUserRenewal = this.rechargeRenewalDao.getLatestUserRenewal(kugouId, renewalType, bizId);
        return Optional.ofNullable(latestUserRenewal);
    }
}
