package com.kugou.fanxing.recharge.service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeOpenDao;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.bo.CouponInfoBO;
import com.kugou.fanxing.recharge.model.bo.KugouOpenBusinessBO;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.AlipayRequest;
import com.kugou.fanxing.recharge.model.request.GetOrderH5Request;
import com.kugou.fanxing.recharge.model.request.OpenAlipayMRequest;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

import static com.kugou.fanxing.recharge.config.RechargeConfig.O_COIN_BUSINESS_ID;

@Slf4j
@Service
public class AlipayRechargeService extends AbstractRechargeService implements RechargeService {

    @Autowired
    private RechargeCouponService rechargeCouponService;
    @Autowired
    private RechargeAcrossDao rechargeAcrossDao;
    @Autowired
    private RechargeOpenDao rechargeOpenDao;
    @Autowired
    private RechargeConfig rechargeConfig;

    /**
     * 支付宝网站下单
     *
     * @param webCommonParam 通用参数
     * @param alipayRequest  业务参数
     * @param apiVersion     接口版本
     * @return 支付宝网站下单参数
     */
    public Map<String, Object> getOrderForAlipay(WebCommonParam webCommonParam, AlipayRequest alipayRequest, String apiVersion) {
        // 解析订单月份
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        SysResultCode resultCode = this.rechargeCommonService.checkPreconditionForGetOrder(rechargeOrderNum, webCommonParam, alipayRequest);
        if (!resultCode.isSuccess()) {
            log.warn("支付宝网站下单，处理外部检查失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(resultCode);
        }
        resultCode = this.agentRechargeService.checkAgentRecharge(webCommonParam, alipayRequest.getRechargeKugouId(), alipayRequest.getCouponId());
        if (!resultCode.isSuccess()) {
            log.warn("支付宝网站下单，处理代充检查失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(resultCode);
        }
        // 处理代金券
        CoinTypeEnum coinTypeEnum = CoinTypeEnum.of(alipayRequest.getCoinType());
        Optional<CouponInfoBO> optionalCouponInfoBO = this.rechargeCouponService.handleCouponForOrder(
                webCommonParam.getKugouId(), alipayRequest.getCouponId(), alipayRequest.getAmount(), coinTypeEnum);
        try {
            RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(webCommonParam.getPid(), webCommonParam.getKugouId(), alipayRequest.getRechargeKugouId(), rechargeOrderNum, alipayRequest.getPayTypeIdEnum(), alipayRequest.getAmount(), webCommonParam.getIp(), coinTypeEnum, optionalCouponInfoBO);
            String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
            int affected = rechargeAcrossDao.add(month, rechargeAcrossPO);
            if (affected < 1) {
                log.warn("支付宝网站下单，保存订单失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            if ("v2".equalsIgnoreCase(apiVersion)) {
                Map<String, Object> extendParam = Maps.newHashMap();
                extendParam.put("businessExt", StringUtils.defaultString(webCommonParam.getExt()));
                extendParam.put("roomId", String.valueOf(alipayRequest.getRoomId()));
                return kupayService.alipayV2(rechargeAcrossPO, webCommonParam.getIp(), extendParam);
            }
            // 生成充值链接
            Map<String, String> extParam = Maps.newHashMap();
            Map<String, Object> extendParam = Maps.newHashMap();
            if (StringUtils.isNotBlank(SpringContextUtils.getHttpServletRequest().getParameter("roomId"))) {
                extendParam.put("roomId", SpringContextUtils.getHttpServletRequest().getParameter("roomId"));
            }
            extendParam.put("businessExt", StringUtils.defaultString(webCommonParam.getExt()));
            Map<String, Object> dataMap = this.rechargeCommonService.createRechargeResult(rechargeAcrossPO, webCommonParam.getIp(), extParam, extendParam);
            if (dataMap.isEmpty()) {
                log.warn("支付宝网站下单，生成链接失败。rechargeAcrossPO: {}", rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            return dataMap;
        } catch (Exception e) {
            log.error("支付宝网站下单，生成链接异常。", e);
            optionalCouponInfoBO.ifPresent(couponInfoBO -> this.rechargeCouponService.unFreezeCouponQuietly(webCommonParam.getKugouId(), rechargeOrderNum, couponInfoBO));
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    /**
     * 支付宝安卓下单
     *
     * @param webCommonParam 通用参数
     * @param alipayRequest  业务参数
     * @return 支付网关下单参数
     */
    public Map<String, Object> getOrderForAlipayMobile(WebCommonParam webCommonParam, AlipayRequest alipayRequest,
                                                       CoinTypeEnum coinTypeEnum, boolean useStdPlat) {
        // 解析订单月份
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        Optional<String> optionalMonth = orderIdService.getYearMonthFromRechargeOrderNum(rechargeOrderNum);
        if (!optionalMonth.isPresent()) {
            log.warn("支付宝安卓下单，解析订单月份失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        // 处理外部检查
        SysResultCode resultCode = this.rechargeCommonService.checkPreconditionForGetOrder(rechargeOrderNum, webCommonParam, alipayRequest);
        if (!resultCode.isSuccess()) {
            log.warn("支付宝安卓下单，处理外部检查失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(resultCode);
        }
        // 处理代金券
        Optional<CouponInfoBO> optionalCouponInfoBO = this.rechargeCouponService.handleCouponForOrder(webCommonParam.getKugouId(),
                alipayRequest.getCouponId(), alipayRequest.getAmount(), coinTypeEnum);
        try {
            int pid = useStdPlat ? webCommonParam.getStdPlat() : webCommonParam.getPid();
            RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(pid,
                    webCommonParam.getKugouId(), 0, rechargeOrderNum, alipayRequest.getPayTypeIdEnum(),
                    alipayRequest.getAmount(), webCommonParam.getIp(), coinTypeEnum, optionalCouponInfoBO);
            String month = optionalMonth.get();
            int affected = rechargeAcrossDao.add(month, rechargeAcrossPO);
            if (affected < 1) {
                log.warn("支付宝安卓下单，保存订单失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            // 生成充值链接
            Map<String, String> extParam = Maps.newHashMap();
            Map<String, Object> extendParam = Maps.newHashMap();
            extendParam.put("businessExt", StringUtils.defaultString(webCommonParam.getExt()));
            Map<String, Object> dataMap = this.rechargeCommonService.createRechargeResultForMobileByPost(rechargeAcrossPO, extParam, extendParam);
            if (dataMap.isEmpty()) {
                log.warn("支付宝安卓下单，生成链接失败。rechargeAcrossPO: {}", rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            dataMap.put("orderInfo", ImmutableMap.of("rechargeOrderNum", rechargeOrderNum));
            return dataMap;
        } catch (Exception e) {
            log.error("支付宝安卓下单，生成链接异常。", e);
            optionalCouponInfoBO.ifPresent(couponInfoBO -> this.rechargeCouponService.unFreezeCouponQuietly(webCommonParam.getKugouId(), rechargeOrderNum, couponInfoBO));
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    /**
     * 支付宝H5下单
     *
     * @param webCommonParam 通用参数
     * @param alipayRequest  业务参数
     * @return 支付网关下单参数
     */
    public Map<String, Object> getOrderForAlipayH5(WebCommonParam webCommonParam, GetOrderH5Request alipayRequest) {
        // 处理外部检查
        String rechargeOrderNum = this.orderIdService.generateRechargeOrderNumForAcross();
        SysResultCode resultCode = this.rechargeCommonService.checkPreconditionForGetOrder(rechargeOrderNum, webCommonParam, alipayRequest);
        if (!resultCode.isSuccess()) {
            log.warn("支付宝H5充值下单接口，处理外部检查失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(resultCode);
        }
        // 处理代金券
        CoinTypeEnum coinTypeEnum = CoinTypeEnum.of(alipayRequest.getCoinType());
        Optional<CouponInfoBO> optionalCouponInfoBO = this.rechargeCouponService.handleCouponForOrder(webCommonParam.getKugouId(), alipayRequest.getCouponId(), alipayRequest.getAmount(), coinTypeEnum);
        try {
            // 记录充值订单
            RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(webCommonParam.getPid(), webCommonParam.getKugouId(), 0,
                    rechargeOrderNum, alipayRequest.getPayTypeIdEnum(), alipayRequest.getAmount(), webCommonParam.getIp(), coinTypeEnum, optionalCouponInfoBO);
            String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
            int affected = rechargeAcrossDao.add(month, rechargeAcrossPO);
            if (affected < 1) {
                log.warn("支付宝H5充值下单接口，保存订单失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            // 生成充值链接
            Map<String, String> kupayParam = Maps.newHashMap();
            String showUrl = StringEscapeUtils.unescapeXml(StringUtils.defaultString(alipayRequest.getShowUrl(), webCommonParam.getReferer()));
            kupayParam.put("sync_url", StringEscapeUtils.unescapeXml(StringUtils.defaultString(alipayRequest.getSyncUrl(), webCommonParam.getReferer())));
            if (StringUtils.isNotBlank(showUrl)) {
                kupayParam.put("show_url", showUrl);
            }
            Map<String, Object> extendParam = Maps.newHashMap();
            extendParam.put("businessExt", StringUtils.defaultString(webCommonParam.getExt()));
            Map<String, Object> dataMap = this.rechargeCommonService.createRechargeResultAlipayH5(rechargeAcrossPO, kupayParam, extendParam, Optional.empty());
            if (dataMap.isEmpty()) {
                log.warn("支付宝H5充值下单接口，生成链接失败。rechargeAcrossPO: {}", rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            return dataMap;
        } catch (Exception e) {
            log.error("支付宝H5充值下单接口，生成链接异常。", e);
            optionalCouponInfoBO.ifPresent(couponInfoBO -> this.rechargeCouponService.unFreezeCouponQuietly(webCommonParam.getKugouId(), rechargeOrderNum, couponInfoBO));
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    public Map<String, Object> gameAlipayH5(WebCommonParam webCommonParam, GetOrderH5Request alipayRequest) {
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        SysResultCode resultCode = this.rechargeCommonService.checkPreconditionForGetOrder(rechargeOrderNum, webCommonParam, alipayRequest);
        if (!resultCode.isSuccess()) {
            log.warn("游戏支付宝H5下单，处理外部检查失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(resultCode);
        }
        RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(webCommonParam.getPid(), webCommonParam.getKugouId(), rechargeOrderNum, alipayRequest.getPayTypeIdEnum(), alipayRequest.getAmount(), webCommonParam.getIp());
        rechargeAcrossPO.setBusinessId(O_COIN_BUSINESS_ID);
        String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
        int affected = rechargeOpenDao.add(month, rechargeAcrossPO);
        if (affected < 1) {
            log.warn("游戏支付宝H5下单，保存订单失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        Map<String, String> kupayParam = Maps.newHashMap();
        kupayParam.put("notify_url", rechargeConfig.getRechargeZuulNotifyUrl(UrlConstants.GAME_PURCHASE_CURRENCY_CALLBACK));
        kupayParam.put("sync_url", StringEscapeUtils.unescapeXml(StringUtils.defaultString(alipayRequest.getSyncUrl(), webCommonParam.getReferer())));
        kupayParam.put("show_url", StringEscapeUtils.unescapeXml(StringUtils.defaultString(alipayRequest.getShowUrl(), webCommonParam.getReferer())));
        Map<String, Object> extendParam = Maps.newHashMap();
        extendParam.put("businessExt", StringUtils.defaultString(webCommonParam.getExt()));
        extendParam.put("businessId", O_COIN_BUSINESS_ID);
        Optional<KupayAppInfoBO> optionalKupayAppInfoBO = Optional.ofNullable(this.rechargeConfig.getKupayAppIdByAppId(this.apolloConfigService.getOCoinKupayAppId()));
        Map<String, Object> dataMap = this.rechargeCommonService.createRechargeResultAlipayH5(rechargeAcrossPO, kupayParam, extendParam, optionalKupayAppInfoBO);
        if (MapUtils.isEmpty(dataMap)) {
            log.warn("游戏支付宝H5下单，生成链接失败。rechargeAcrossPO: {}", rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        return dataMap;
    }

    public Map<String, Object> getOrderAlipayM(WebCommonParam webCommonParam, OpenAlipayMRequest request, KugouOpenBusinessBO kugouOpenBusinessBO) {
        // 解析订单月份
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        Optional<String> optionalMonth = orderIdService.getYearMonthFromRechargeOrderNum(rechargeOrderNum);
        if (!optionalMonth.isPresent()) {
            log.warn("创建支付宝订单失败，解析订单月份失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        // 处理外部检查
        SysResultCode resultCode = this.rechargeCommonService.checkPreconditionForGetOrder(rechargeOrderNum, webCommonParam, request);
        if (!resultCode.isSuccess()) {
            log.warn("创建支付宝订单失败，处理外部检查失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(resultCode);
        }
        RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(webCommonParam.getPid(), webCommonParam.getKugouId(), rechargeOrderNum, request.getPayTypeIdEnum(), request.getAmount(), webCommonParam.getIp(), Optional.empty());
        String month = optionalMonth.get();
        int affected = rechargeOpenDao.add(month, rechargeAcrossPO);
        if (affected < 1) {
            log.warn("创建支付宝订单失败，保存订单失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        // 生成充值链接
        Map<String, String> extParam = Maps.newHashMap();
        extParam.put("notify_url", this.rechargeConfig.getOpenNotifyUrl());
        Map<String, Object> extendParam = Maps.newHashMap();
        extendParam.put("userOpenid", webCommonParam.getKugouOpenDispatchParam().getUser_openid());
        extendParam.put("businessId", request.getBusinessId());
        extendParam.put("businessExt", StringUtils.defaultString(webCommonParam.getExt()));
        Map<String, Object> dataMap = this.rechargeCommonService.createRechargeResultForMobileByPost(rechargeAcrossPO, extParam, extendParam, kugouOpenBusinessBO.getKupayAppId());
        if (dataMap.isEmpty()) {
            log.warn("创建支付宝订单失败，生成链接失败。rechargeAcrossPO: {}", rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        dataMap.put("orderInfo", ImmutableMap.of("rechargeOrderNum", rechargeOrderNum));
        return dataMap;
    }

    public Map<String, Object> getOrderForAlipayQr(WebCommonParam webCommonParam, AlipayRequest alipayRequest) {
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        SysResultCode resultCode = this.rechargeCommonService.checkPreconditionForGetOrder(rechargeOrderNum, webCommonParam, alipayRequest);
        if (!resultCode.isSuccess()) {
            log.warn("网页支付宝充值二维码下单接口，处理外部检查失败。rechargeOrderNum: {}", rechargeOrderNum);
            throw new BizException(resultCode);
        }
        // 处理代金券
        CoinTypeEnum coinTypeEnum = CoinTypeEnum.of(alipayRequest.getCoinType());
        Optional<CouponInfoBO> optionalCouponInfoBO = Optional.empty();
        try {
            optionalCouponInfoBO = this.rechargeCouponService.handleCouponForOrder(webCommonParam.getKugouId(),
                    alipayRequest.getCouponId(), alipayRequest.getAmount(), coinTypeEnum);
            RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(webCommonParam.getPid(),
                    webCommonParam.getKugouId(), 0, rechargeOrderNum, alipayRequest.getPayTypeIdEnum(),
                    alipayRequest.getAmount(), webCommonParam.getIp(), coinTypeEnum, optionalCouponInfoBO);
            String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
            int affected = rechargeAcrossDao.add(month, rechargeAcrossPO);
            if (affected < 1) {
                log.warn("网页支付宝充值二维码下单接口，保存订单失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            Map<String, String> kupaysParam = Maps.newHashMap();
            Map<String, Object> extendParam = Maps.newHashMap();
            extendParam.put("businessExt", StringUtils.defaultString(webCommonParam.getExt()));
            extendParam.put("roomId", String.valueOf(alipayRequest.getRoomId()));
            return kupayService.alipayqrcodeV1(rechargeAcrossPO, extendParam, kupaysParam);
        } catch (Exception e) {
            log.error("网页支付宝充值二维码下单接口，生成链接异常。", e);
            optionalCouponInfoBO.ifPresent(couponInfoBO -> this.rechargeCouponService.unFreezeCouponQuietly(webCommonParam.getKugouId(), rechargeOrderNum, couponInfoBO));
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }

    public Map<String, Object> purchaseProductsForQr(RechargeAcrossPO rechargeAcrossPO, Map<String, String> kupaysParam, Map<String, Object> extendParam) {
        // 保存充值订单
        int affected = rechargeOrderService.addRechargeOrder(rechargeAcrossPO);
        if (affected < 1) {
            log.warn("内网微信二维码购买下单，保存订单失败。rechargeAcrossPO: {}", rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        Map<String, Object> payload = this.kupayService.alipayqrcodeV1(rechargeAcrossPO, extendParam, kupaysParam);
        log.warn("内网微信二维码购买下单，请求响应。rechargeAcrossPO: {}, payload: {}", rechargeAcrossPO, payload);
        return payload;
    }

    public Map<String, Object> purchaseProductsForH5(RechargeAcrossPO rechargeAcrossPO, Map<String, String> kupaysParam, Map<String, Object> extendParam) {
        int affected = rechargeOrderService.addRechargeOrder(rechargeAcrossPO);
        if (affected < 1) {
            log.warn("直播支付宝H5下单，保存订单失败。rechargeAcrossPO: {}", rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        Map<String, Object> payload = this.rechargeCommonService.createRechargeResultAlipayH5(rechargeAcrossPO, kupaysParam, extendParam, Optional.empty());
        if (MapUtils.isEmpty(payload)) {
            log.warn("直播支付宝H5下单，生成链接失败。rechargeAcrossPO: {}", rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        return payload;
    }
}
