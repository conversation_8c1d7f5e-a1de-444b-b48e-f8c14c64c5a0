package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.thrift.operation.FamilyControllRequest;
import com.kugou.fanxing.thrift.operation.FamilyControllResult;
import com.kugou.fanxing.thrift.operation.FamilyControllService;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Optional;

/**
 * 家长控制模式
 *
 * <AUTHOR>
 */
@Slf4j
public class GetFamilyControlCommand extends HystrixCommand<Optional<FamilyControllResult>> {

    private final FamilyControllService.Iface familyControlService;
    private final FamilyControllRequest request;

    public GetFamilyControlCommand(final FamilyControllService.Iface familyControlService, final FamilyControllRequest request) {
        super(HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("GetFamilyControlCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(300)));
        this.familyControlService = familyControlService;
        this.request = request;
    }

    @Override
    protected Optional<FamilyControllResult> run() throws Exception {
        return Optional.of(this.familyControlService.getFamilyControll(request));
    }

    @Override
    protected Optional<FamilyControllResult> getFallback() {
        Optional<FamilyControllResult> fallback = Optional.empty();
        log.warn("GetFamilyControlCommand 服务降级! 调用家长控制模式出错, request: {}! 降级返回数据: {}, 降级原因: {}",
                request, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }

}
