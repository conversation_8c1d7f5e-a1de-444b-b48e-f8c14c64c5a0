package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.coupon.thrift.CouponService;
import com.kugou.fanxing.coupon.thrift.OperResult;
import com.kugou.fanxing.coupon.thrift.ReturnResult;
import com.kugou.fanxing.coupon.thrift.UnFreezeVO;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Objects;

/**
 * 充代金券解冻接口
 *
 * <AUTHOR>
 */
@Slf4j
public class UnFreezeCouponCommand extends HystrixCommand<Boolean> {

    private final CouponService.Iface couponService;
    private final UnFreezeVO unFreezeVO;

    public UnFreezeCouponCommand(final CouponService.Iface couponService, final UnFreezeVO unFreezeVO) {
        super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("UnFreezeCouponCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(300)));
        this.couponService = couponService;
        this.unFreezeVO = unFreezeVO;
    }

    @Override
    protected Boolean run() throws Exception {
        log.warn("调用充代金券解冻接口, unFreezeVO: {}", unFreezeVO);
        ReturnResult returnResult = couponService.cancel(unFreezeVO);
        if (Objects.isNull(returnResult) || returnResult.getCode() != 0 || Objects.isNull(returnResult.getData())) {
            log.warn("调用充代金券解冻接口, 调用失败, unFreezeVO: {}, response: {}", unFreezeVO, returnResult);
            return false;
        }
        OperResult operResult = returnResult.getData();
        return operResult.getResult() == 0 || operResult.getResult() == 8;
    }

    @Override
    protected Boolean getFallback() {
        final boolean fallback = false;
        log.warn("UnFreezeCouponCommand 服务降级! 调用充代金券解冻接口! 请求参数:{}, 降级返回数据: {}, 降级原因: {}",
                unFreezeVO, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }
}
