package com.kugou.fanxing.recharge.service.callback;

import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeOpenDao;
import com.kugou.fanxing.recharge.model.dto.CoinCallbackDTO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.service.AssertFacadeService;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.ModelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.function.UnaryOperator;

/**
 * 游戏充值服务回调通知
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class GameCallbackService extends AbstractCallbackService {

    @Autowired
    private RechargeOpenDao rechargeOpenDao;
    @Autowired
    private AssertFacadeService assertFacadeService;

    public SysResultCode purchaseCurrencyCallback(CoinCallbackDTO coinCallbackDTO) {
        SysResultCode sysResultCode = checkPrerequisites(coinCallbackDTO);
        if (!sysResultCode.isSuccess()) {
            log.warn("游戏充值购买货币回调，前置条件检查不通过。coinCallbackDTO: {}", coinCallbackDTO);
            return sysResultCode;
        }
        long kugouId = coinCallbackDTO.getUserid();
        BigDecimal realAmount = coinCallbackDTO.getTotal_fee().multiply(BigDecimal.valueOf(100));
        RechargeAcrossPO sourceOrder = makeOrderByKugouPayRequest(kugouId, realAmount, coinCallbackDTO);
        Optional<RechargeAcrossPO> optionalRechargeAcrossPO = this.queryGameRechargeOrder(sourceOrder.getRechargeOrderNum());
        if (optionalRechargeAcrossPO.isPresent() && optionalRechargeAcrossPO.get().getStatus() == 1) {
            log.warn("游戏充值购买货币回调，订单已经处理过。rechargeOrderNum: {}", sourceOrder.getRechargeOrderNum());
            return SysResultCode.SUCCESS;
        }
        // 设置到账记录
        UnaryOperator<RechargeAcrossPO> source2Target = source -> {
            RechargeAcrossPO target = ModelUtils.fromUnchecked(source, RechargeAcrossPO.class);
            target.setCoin(realAmount);
            target.setRealAmount(realAmount);
            target.setStatus(1);
            target.setRechargeTime(DateHelper.getCurrentSeconds());
            target.setReType(ReTypeEnum.RETYPE_RECHARGE.getReTypeId());
            target.setRechargeId(orderIdService.generateGlobalId());
            target.setTradeNo(StringUtils.defaultString(coinCallbackDTO.getTrade_no()));
            target.setTradeTime(parseTradeTime(coinCallbackDTO.getTrade_time()));
            target.setPartner(StringUtils.defaultString(coinCallbackDTO.getPartner()));
            return target;
        };
        RechargeAcrossPO targetOrder = source2Target.apply(sourceOrder);
        assertFacadeService.addCoin(targetOrder);
        boolean isSuccess = this.saveTargetOrder(targetOrder);
        if (!isSuccess) {
            log.error("游戏充值购买货币回调，执行失败。coinCallbackDTO: {}", coinCallbackDTO);
            return SysResultCode.E_30000001;
        }
        log.warn("游戏充值购买货币回调，执行成功。coinCallbackDTO: {}", coinCallbackDTO);
        return SysResultCode.SUCCESS;
    }

    /**
     * 保存充值到账流水
     */
    public boolean saveTargetOrder(RechargeAcrossPO callbackOrder) {
        String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(callbackOrder.getRechargeOrderNum());
        return this.rechargeOpenDao.insertIgnore(month, callbackOrder) > 0;
    }

    /**
     * 查询充值下单流水
     */
    public Optional<RechargeAcrossPO> queryGameRechargeOrder(String rechargeOrderNum) {
        String month = this.orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
        return Optional.ofNullable(this.rechargeOpenDao.queryByRechargeOrderNum(month, rechargeOrderNum));
    }
}
