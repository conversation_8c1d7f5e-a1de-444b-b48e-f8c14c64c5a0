package com.kugou.fanxing.recharge.service;

import com.dianping.cat.Cat;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kugou.config.Env;
import com.kugou.fanxing.biz.commons.consume.api.CostReturn;
import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.coupon.thrift.CouponService;
import com.kugou.fanxing.coupon.thrift.FreezeParamsVO;
import com.kugou.fanxing.coupon.thrift.UnFreezeVO;
import com.kugou.fanxing.coupon.thrift.read.CouponInfoVO;
import com.kugou.fanxing.coupon.thrift.read.CouponReadService;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.CouponStatusEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeCouponDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeCouponUnfreezeDao;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.bo.CouponBO;
import com.kugou.fanxing.recharge.model.bo.CouponInfoBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.po.RechargeCouponPO;
import com.kugou.fanxing.recharge.model.po.RechargeCouponUnfreezePO;
import com.kugou.fanxing.recharge.model.vo.RechargeCouponInfoVO;
import com.kugou.fanxing.recharge.service.command.GetPersonalCouponListCommand;
import com.kugou.fanxing.recharge.service.command.ListCouponCommand;
import com.kugou.fanxing.recharge.service.command.SimpleFreezeCouponCommand;
import com.kugou.fanxing.recharge.service.command.UnFreezeCouponCommand;
import com.kugou.fanxing.recharge.service.command.*;
import com.kugou.fanxing.recharge.service.common.ConsumeRpcService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.recharge.util.ModelUtils;
import com.kugou.mfx.activity.infiltrate.thrift.service.Coupon;
import com.kugou.mfx.activity.infiltrate.thrift.service.CouponListService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 充值代金券服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class RechargeCouponService {

    @Autowired
    private Env env;
    @Autowired
    private OrderIdService orderIdService;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private CouponService.Iface couponService;
    @Autowired
    private CouponReadService.Iface couponReadService;
    @Autowired
    private CouponListService.Iface couponListService;
    @Autowired
    private RechargeCouponUnfreezeDao rechargeCouponUnfreezeDao;
    @Autowired
    private RechargeAcrossDao rechargeAcrossDao;
    @Autowired
    private RechargeCouponDao rechargeCouponDao;
    @Autowired
    private ConsumeRpcService consumeRpcService;

    private final LoadingCache<String, Map<Long, Coupon>> couponListCache = CacheBuilder.newBuilder()
            .maximumSize(20000)
            .refreshAfterWrite(5, TimeUnit.MINUTES)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Map<Long, Coupon>>() {
                @Override
                public Map<Long, Coupon> load(String key) throws Exception {
                    log.warn("RPC加载代金券配置列表信息，开始加载数据。key: {}", key);
                    Map<Long, Coupon> couponConfigMap = Maps.newHashMap();
                    for(int i=0; i<2; i++) {
                        couponConfigMap = new ListCouponCommand(couponListService).execute();
                        if (MapUtils.isNotEmpty(couponConfigMap)) {
                            return couponConfigMap;
                        }
                    }
                    log.warn("RPC加载代金券配置列表信息，加载数据失败。key: {}", key);
                    return couponConfigMap;
                }
            });

    /**
     * 处理充值代金券
     *
     * @param kugouId  酷狗ID
     * @param couponId 代金券ID
     * @param amount   充值金额
     * @return 充值代金券
     */
    public Optional<CouponInfoBO> handleCouponForOrder(long kugouId, long couponId, BigDecimal amount, CoinTypeEnum coinTypeEnum) {
        // 检查代金券请求参数
        if (couponId <= 0) {
            log.warn("处理充值代金券，用户未使用充值代金券。kugouId: {}, couponId: {}", kugouId, couponId);
            return Optional.empty();
        }
        // 检查代金券特效开关
        boolean couponSwitch = apolloConfigService.isCouponSwitchOpen();
        if (!couponSwitch) {
            log.warn("处理充值代金券，代金券特性功能关闭。kugouId: {}, couponId: {}", kugouId, couponId);
            throw new BizException(SysResultCode.RECHARGE_COUPON_OFFLINE);
        }
        // 检查币种是否支持充值代金券
        if (!CoinTypeEnum.allowCoupon(coinTypeEnum)) {
            log.warn("处理充值代金券，当前币种不支持使用代金券。kugouId: {}, couponId: {}", kugouId, couponId);
            throw new BizException(SysResultCode.RECHARGE_COUPON_NOT_ALLOW);
        }
        //检查代金券是否存在
        Optional<CouponInfoBO> optionalCouponInfoBO = this.checkCoupon(kugouId, couponId);
        if (!optionalCouponInfoBO.isPresent()) {
            log.warn("处理充值代金券，代金券已使用或正在使用中。kugouId: {}, couponId: {}", kugouId, couponId);
            throw new BizException(SysResultCode.RECHARGE_COUPON_USED);
        }
        //检查代金券是否可用
        CouponInfoBO couponInfoBO = optionalCouponInfoBO.get();
        if (couponInfoBO.getCouponStatus() != 1) {
            log.warn("处理充值代金券，代金券状态不可用。kugouId: {}, couponInfoBO: {}", kugouId, couponInfoBO);
            throw new BizException(SysResultCode.RECHARGE_COUPON_ILLEGAL);
        }
        //检查代金券的使用范围
        if (this.isCouponLimit(couponInfoBO, amount)) {
            log.warn("处理充值代金券，充值金额未达券额要求。kugouId: {}, couponInfoBO: {}", kugouId, couponInfoBO);
            throw new BizException(SysResultCode.RECHARGE_COUPON_CONDITION);
        }
        // 调用代金券冻结接口
        long couponOrderId = orderIdService.generateGlobalId();
        couponInfoBO.setOrderId(couponOrderId);
        FreezeParamsVO freezeParamsVO = new FreezeParamsVO()
                .setOrderId(couponOrderId)
                .setKugouId(kugouId)
                .setCouponId(couponId)
                .setCouponTypeId(couponInfoBO.getCategoryId())
                .setCfromId(4)
                .setPId(0)
                .setActionId(1019)
                .setReqTimestamp(DateHelper.getCurrentSeconds());
        freezeParamsVO.setSign(FinanceSignUtils.makeSign(freezeParamsVO, getSignKey()));
        boolean freezeFlag = new SimpleFreezeCouponCommand(couponService, freezeParamsVO).execute();
        if (!freezeFlag) {
            log.warn("处理充值代金券，冻结代金券失败。kugouId: {}, couponInfoBO: {}", kugouId, couponInfoBO);
            throw new BizException(SysResultCode.RECHARGE_COUPON_FREEZE);
        }
        return Optional.of(couponInfoBO);
    }

    private String getSignKey() {
        return apolloConfigService.getCouponSignKey();
    }

    public boolean isCouponLimit(CouponInfoBO couponInfoBO, BigDecimal amount) {
        BigDecimal value = couponInfoBO.getValue();
        BigDecimal upperLimit = couponInfoBO.getUpperLimit();
        BigDecimal lowerLimit = couponInfoBO.getLowerLimit();
        // 代金券金额不能大于等于充值金额
        if (value.compareTo(amount) >= 0) {
            return true;
        }
        // 代金券上下限检查
        return (upperLimit.compareTo(BigDecimal.ZERO) != 0 && upperLimit.compareTo(amount) <= 0) ||
                (lowerLimit.compareTo(BigDecimal.ZERO) != 0 && lowerLimit.compareTo(amount) > 0);
    }

    /**
     * 检查用户代金券是否有效
     *
     * @param kugouId  酷狗ID
     * @param couponId 代金券ID
     * @return 用户代金券是否有效
     */
    public Optional<CouponInfoBO> checkCoupon(long kugouId, long couponId) {
        List<CouponInfoBO> couponInfoBOList = getCouponList(kugouId);
        return couponInfoBOList.stream()
                .filter(couponInfoBO -> couponInfoBO.getCouponId() == couponId)
                .findFirst();
    }

    public List<CouponInfoBO> getCouponList(long kugouId) {
        //检查代金券是否存在
        List<CouponInfoVO> couponInfoVOList = new GetPersonalCouponListCommand(couponReadService, kugouId).execute();
        Map<Long, Coupon> couponConfigMap = Maps.newHashMap();
        try {
            couponConfigMap = couponListCache.get("couponConfigList");
            log.warn("加载代金券配置列表信息，加载成功。kugouId: {}, couponConfigMapSize: {}", kugouId, couponConfigMap.size());
        } catch (ExecutionException e) {
            log.error("加载代金券配置列表信息，加载异常。kugouId: {}", kugouId, e);
        }
        final Map<Long, Coupon> finalCouponConfigMap = ImmutableMap.copyOf(couponConfigMap);
        final int expireHideDays = apolloConfigService.getCouponExpireHideDays();
        log.warn("加载用户代金券列表，加载成功。couponInfoVOList: {}", couponInfoVOList);
        return couponInfoVOList.stream().filter(couponInfoVO -> {
            long couponTypeId = couponInfoVO.getCouponTypeId();
            Optional<Coupon> optionalCouponConfig = Optional.ofNullable(finalCouponConfigMap.get(couponTypeId));
            if (optionalCouponConfig.isPresent()) {
                Coupon couponConfig = optionalCouponConfig.get();
                log.warn("加载用户代金券列表，加载配置。couponConfig: {}", couponConfig);
                // 判断代金券配置是否可用
                if (couponConfig.getUpperLimit() >= 0 && couponConfig.getLowerLimit() >= 0 && couponConfig.getCouponStatus() == 1) {
                    long expireDate = couponInfoVO.getExpireDate();
                    // 过期超过7天的代金券不展示
                    return expireDate + TimeUnit.DAYS.convert(expireHideDays, TimeUnit.SECONDS) >= DateHelper.getCurrentSeconds();
                }
            }
            log.warn("加载用户代金券列表，代金券配置缺失。couponTypeId: {}, finalCouponConfigMap: {}", couponTypeId, finalCouponConfigMap);
            return false;
        }).map(couponInfoVO -> {
            String couponImg = couponInfoVO.getCouponImg();
            String couponImgPath = parseCouponImgPath(couponImg);
            Coupon couponConfig = finalCouponConfigMap.get(couponInfoVO.getCouponTypeId());
            BigDecimal upperLimit = BigDecimal.valueOf(couponConfig.getUpperLimit()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).stripTrailingZeros();
            BigDecimal lowerLimit = BigDecimal.valueOf(couponConfig.getLowerLimit()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).stripTrailingZeros();
            BigDecimal value = BigDecimal.valueOf(couponInfoVO.getCoin()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).stripTrailingZeros();
            String expireDate = DateFormatUtils.format(couponInfoVO.getExpireDate() * 1000, "yyyy-MM-dd");
            return new CouponInfoBO()
                    .setCategoryId(couponConfig.getId())
                    .setCouponId(couponInfoVO.getCouponId())
                    .setCouponName(couponInfoVO.getCouponName())
                    .setCouponStatus(couponInfoVO.getCouponStatus())
                    .setCouponImg(couponInfoVO.getCouponImg())
                    .setCouponImgPath(StringUtils.defaultString(couponImgPath, couponImg))
                    .setCouponColor(StringUtils.defaultString(couponInfoVO.getCouponColor()))
                    .setValue(value)
                    .setCouponDoc(couponInfoVO.getCouponDoc())
                    .setExpireTime(Math.toIntExact(couponInfoVO.getExpireDate()))
                    .setExpireDate(expireDate)
                    .setUpperLimit(upperLimit)
                    .setLowerLimit(lowerLimit);
        })
                // 按代金券状态比较，状态较小的排前面
                .sorted(Comparator.comparingInt(CouponInfoBO::getCouponStatus))
                // 按代金券金额比较，金额较大的排前面
                .sorted(Comparator.comparing(CouponInfoBO::getValue))
                // 按过期时间比较，过期时间近的排前面
                .sorted(Comparator.comparingInt(CouponInfoBO::getExpireTime))
                .collect(Collectors.toList());
    }

    public String parseCouponImgPath(String couponImg) {
        String couponImgPath = "";
        if (StringUtils.isBlank(couponImg)) {
            return couponImgPath;
        }
        try {
            couponImgPath = new URI(couponImg).getPath();
        } catch (Exception e) {
            log.error("获取用户代金券列表，解析券图片路径异常。couponImg: {}", couponImg, e);
        }
        return StringUtils.defaultString(couponImgPath);
    }

    /**
     * 解冻已经成功冻结的充值代金券
     *  @param kugouId           酷狗ID
     * @param rechargeOrderNum  充值订单号
     * @param couponInfoBO      充值代金券
     */
    public void unFreezeCouponQuietly(long kugouId, String rechargeOrderNum, CouponInfoBO couponInfoBO) {
        unFreezeCoupon(kugouId, rechargeOrderNum, couponInfoBO.getOrderId(), couponInfoBO.getCouponId());
    }

    /**
     * 解冻已经成功冻结的充值代金券
     *
     * @param unfreezePO 待解冻充值代金券
     */
    public boolean unFreezeCouponQuietly(RechargeCouponUnfreezePO unfreezePO) {
        return unFreezeCoupon(unfreezePO.getKugouId(), unfreezePO.getRechargeOrderNum(), unfreezePO.getCouponOrderId(), unfreezePO.getCouponId());
    }

    private boolean unFreezeCoupon(long kugouId, String rechargeOrderNum, long couponOrderId, long couponId) {
        try {
            UnFreezeVO unFreezeVO = new UnFreezeVO()
                    .setOrderId(orderIdService.generateGlobalId())
                    .setOriginOrderId(couponOrderId)
                    .setKugouId(kugouId)
                    .setPId(0)
                    .setActionId(1019)
                    .setReqTimestamp(DateHelper.getCurrentSeconds());
            unFreezeVO.setSign(FinanceSignUtils.makeSign(unFreezeVO, getSignKey()));
            boolean unfreezeFlag = new UnFreezeCouponCommand(couponService, unFreezeVO).execute();
            int status = unfreezeFlag ? 1 : -1;
            RechargeCouponUnfreezePO couponUnfreezePO = new RechargeCouponUnfreezePO()
                    .setRechargeOrderNum(rechargeOrderNum)
                    .setKugouId(kugouId)
                    .setCouponOrderId(couponOrderId)
                    .setCouponId(couponId)
                    .setStatus(status)
                    .setAddTime(DateHelper.getCurrentSeconds())
                    .setUpdateTime(DateHelper.getCurrentSeconds());
            int affected = rechargeCouponUnfreezeDao.addRecord(couponUnfreezePO);
            if (affected < 1) {
                throw new ContextedRuntimeException("保存充值代金券解冻数据失败")
                        .addContextValue("couponUnfreezePO", couponUnfreezePO);
            }
            log.warn("充值代金券解冻，解冻成功。rechargeOrderNum: {}, kugouId: {}, couponOrderId: {}, couponId: {}",
                    rechargeOrderNum, kugouId, couponOrderId, couponId);
            return true;
        } catch (Exception e) {
            Cat.logError("充值代金券解冻，解冻异常。", e);
            log.error("充值代金券解冻，解冻异常。rechargeOrderNum: {}, kugouId: {}, couponOrderId: {}, couponId: {}",
                    kugouId, rechargeOrderNum, couponOrderId, couponId, e);
        }
        return false;
    }

    public long unFreezeCouponByAddTime(Date sTime, Date eTime) {
        String month = DateFormatUtils.format(sTime, "yyyyMM");
        List<RechargeAcrossPO> rechargeAcrossPOList = this.rechargeAcrossDao.getRechargeAcrossUsingCoupon(month, (int) (sTime.getTime() / 1000), (int) (eTime.getTime() / 1000));
        return rechargeAcrossPOList.stream()
                .filter(rechargeAcrossPO -> rechargeAcrossPO.getStatus() == 0)
                .filter(rechargeAcrossPO -> rechargeAcrossPO.getCouponId() > 0)
                .filter(rechargeAcrossPO -> rechargeAcrossPO.getCouponOrderId() > 0)
                .filter(rechargeAcrossPO -> rechargeAcrossPO.getCouponStatus() == 0)
                .filter(rechargeAcrossPO -> {
                    Optional<RechargeCouponPO> optionalRechargeCouponPO = Optional.ofNullable(rechargeCouponDao.getByCouponOrderId(rechargeAcrossPO.getCouponOrderId()));
                    return !optionalRechargeCouponPO.isPresent();
                })
                .map(rechargeAcrossPO -> {
                    Optional<RechargeCouponUnfreezePO> optionalRechargeCouponUnfreezePO = Optional.ofNullable(rechargeCouponUnfreezeDao.getByCouponOrderId(rechargeAcrossPO.getCouponOrderId()));
                    return optionalRechargeCouponUnfreezePO.orElseGet(() -> {
                        RechargeCouponUnfreezePO rechargeCouponUnfreezePO = new RechargeCouponUnfreezePO()
                                .setRechargeOrderNum(rechargeAcrossPO.getRechargeOrderNum())
                                .setKugouId(rechargeAcrossPO.getKugouId())
                                .setCouponId(rechargeAcrossPO.getCouponId())
                                .setStatus(0)
                                .setCouponOrderId(rechargeAcrossPO.getCouponOrderId())
                                .setAddTime(DateHelper.getCurrentSeconds())
                                .setUpdateTime(DateHelper.getCurrentSeconds());
                        int affected = rechargeCouponUnfreezeDao.addRecord(rechargeCouponUnfreezePO);
                        if (affected < 1) {
                            log.warn("保存需要解冻的充值代金券，保存数据失败。rechargeCouponUnfreezePO: {}", rechargeCouponUnfreezePO);
                            return null;
                        }
                        return rechargeCouponUnfreezePO;
                    });
                })
                .filter(Objects::nonNull)
                .filter(rechargeCouponUnfreezePO -> rechargeCouponUnfreezePO.getStatus() != 1)
                .mapToInt(unfreezePO -> unFreezeCouponQuietly(unfreezePO) ? 1 : 0)
                .count();
    }

    public JsonResult<List<RechargeCouponInfoVO>> getCouponListResult(long kugouId) {
        if (!apolloConfigService.isCouponSwitchOpen()) {
            log.warn("获取充值代金券列表，代金券业务关闭。kugouId: {}", kugouId);
            return JsonResult.result(SysResultCode.E_10000024, Lists.newArrayList());
        }
        List<CouponInfoBO> couponInfoBOList = this.getCouponList(kugouId);
        List<RechargeCouponInfoVO> rechargeCouponInfoVOList = couponInfoBOList.stream().map(couponInfoBO -> {
            RechargeCouponInfoVO rechargeCouponInfoVO = ModelUtils.from(couponInfoBO, RechargeCouponInfoVO.class).orElse(null);
            if (Objects.nonNull(rechargeCouponInfoVO)) {
                rechargeCouponInfoVO.setCouponId(String.valueOf(couponInfoBO.getCouponId()));
                rechargeCouponInfoVO.setCategoryId(String.valueOf(couponInfoBO.getCategoryId()));
            }
            return rechargeCouponInfoVO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        log.warn("获取充值代金券列表，获取成功。rechargeCouponInfoVOListSize: {}", rechargeCouponInfoVOList.size());
        return JsonResult.result(SysResultCode.SUCCESS, rechargeCouponInfoVOList);
    }

    /**
     * 提取充值交易使用的充值代金券信息
     *
     * @param callbackOrder 充值订单
     * @return 是否使用充值代金券
     */
    public Optional<CouponBO> extractValidCoupon(RechargeAcrossPO callbackOrder) {
        CouponBO couponBO = new CouponBO()
                .setCouponOrderId(callbackOrder.getCouponOrderId())
                .setCouponId(callbackOrder.getCouponId())
                .setCoupon(callbackOrder.getCoupon());
        if (!isValidCoupon(couponBO)) {
            log.warn("提取充值交易使用的充值代金券信息，不存在可用充值代金券。couponBO: {}", couponBO);
            return Optional.empty();
        }
        return Optional.of(couponBO);
    }

    public boolean isValidCoupon(CouponBO couponBO) {
        return couponBO.getCouponOrderId() > 0 && couponBO.getCouponId() > 0
                && Objects.nonNull(couponBO.getCoupon()) && couponBO.getCoupon().compareTo(BigDecimal.ZERO) > 0;
    }

    public int consumeCoupon(long kugouId, long couponOrderId, long globalId, int addTime) {
        List<Integer> failureResultCodeList = apolloConfigService.getCouponFailureResultCodeList();
        UnFreezeVO unFreezeVO = new UnFreezeVO()
                .setOrderId(globalId)
                .setOriginOrderId(couponOrderId)
                .setKugouId(kugouId)
                .setPId(0)
                .setActionId(1019)
                .setReqTimestamp(addTime);
        unFreezeVO.setSign(FinanceSignUtils.makeSign(unFreezeVO, getSignKey()));
        CouponServiceConfirmCommand command = new CouponServiceConfirmCommand(couponService, unFreezeVO, failureResultCodeList);
        return command.execute();
    }

    /**
     * 使用充值代金券
     *
     * @param kugouId          酷狗ID
     * @param rechargeOrderNum 充值订单号
     * @param couponBO         充值代金券
     * @return 使用的代金券金额
     */
    public Optional<BigDecimal> useCoupon(long kugouId, String rechargeOrderNum, CouponBO couponBO) {
        try {
            //直接记录代金券状态表
            long globalId = orderIdService.generateGlobalId();
            int currentSeconds = DateHelper.getCurrentSeconds();
            RechargeCouponPO newRechargeCouponPO = new RechargeCouponPO()
                    .setGlobalId(globalId)
                    .setRechargeOrderNum(rechargeOrderNum)
                    .setCouponId(couponBO.getCouponId())
                    .setCouponOrderId(couponBO.getCouponOrderId())
                    .setKugouId(kugouId)
                    .setStatus(0)
                    .setAddTime(currentSeconds)
                    .setUpdateTime(currentSeconds);
            int affected = this.rechargeCouponDao.addIgnore(newRechargeCouponPO);
            if (affected < 1) {
                log.warn("使用充值代金券, 充值代金券数据已经存在。kugouId: {}, rechargeOrderNum: {}, couponBO: {}", kugouId, rechargeOrderNum, couponBO);
            }
            // 获取充值代金券订单
            Optional<RechargeCouponPO> optionalRechargeCouponPO = Optional.ofNullable(this.rechargeCouponDao.getByCouponOrderId(couponBO.getCouponOrderId()));
            if (!optionalRechargeCouponPO.isPresent()) {
                log.error("使用充值代金券, 无法获取充值代金券数据。kugouId: {}, rechargeOrderNum: {}, couponBO: {}", kugouId, rechargeOrderNum, couponBO);
                return Optional.empty();
            }
            RechargeCouponPO rechargeCouponPO = optionalRechargeCouponPO.get();
            // 使用代金券
            int couponResultCode = consumeCoupon(kugouId, couponBO.getCouponOrderId(), rechargeCouponPO.getGlobalId(), rechargeCouponPO.getAddTime());
            if (couponResultCode == 0) {
                affected = this.rechargeCouponDao.updateStatusByCouponOrderId(couponBO.getCouponOrderId(), CouponStatusEnum.STATUS_FAILED, DateHelper.getCurrentSeconds());
                if (affected < 1) {
                    log.error("使用充值代金券, 无法更新代金券状态为使用失败。kugouId: {}, rechargeOrderNum: {}, couponBO: {}", kugouId, rechargeOrderNum, couponBO);
                }
                log.error("使用充值代金券, 调用代金券服务失败。kugouId: {}, rechargeOrderNum: {}, couponBO: {}", kugouId, rechargeOrderNum, couponBO);
                return Optional.empty();
            }
            if (couponResultCode == 2) {
                affected = this.rechargeCouponDao.updateStatusByCouponOrderId(couponBO.getCouponOrderId(), CouponStatusEnum.STATUS_CANCEL, DateHelper.getCurrentSeconds());
                if (affected < 1) {
                    log.error("使用充值代金券, 无法更新代金券状态为超时支付。kugouId: {}, rechargeOrderNum: {}, couponBO: {}", kugouId, rechargeOrderNum, couponBO);
                }
                //加了0元的代金券，但实际上也是成功的
                return Optional.of(BigDecimal.ZERO);
            }
            //更新状态为成功
            affected = this.rechargeCouponDao.updateStatusByCouponOrderId(couponBO.getCouponOrderId(), CouponStatusEnum.STATUS_SUCCESS, DateHelper.getCurrentSeconds());
            if (affected < 1) {
                log.error("使用充值代金券, 更新代金券状态失败。kugouId: {}, rechargeOrderNum: {}, couponBO: {}", kugouId, rechargeOrderNum, couponBO);
            }
            Optional<CostReturn> optionalCostReturn = this.consumeRpcService.presentCouponCoin(rechargeOrderNum, rechargeCouponPO, couponBO.getCoupon());
            if (optionalCostReturn.isPresent()) {
                return Optional.of(couponBO.getCoupon());
            }
        } catch (Exception e) {
            log.error("使用充值代金券，使用异常。kugouId: {}, rechargeOrderNum: {}, couponBO: {}", kugouId, rechargeOrderNum, couponBO, e);
        }
        return Optional.empty();
    }
}
