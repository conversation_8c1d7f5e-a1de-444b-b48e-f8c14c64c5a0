package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.coupon.thrift.CouponService;
import com.kugou.fanxing.coupon.thrift.FreezeParamsVO;
import com.kugou.fanxing.coupon.thrift.OperResult;
import com.kugou.fanxing.coupon.thrift.ReturnResult;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Objects;

/**
 * 查询充值代金券配置信息
 *
 * <AUTHOR>
 */
@Slf4j
public class SimpleFreezeCouponCommand extends HystrixCommand<Boolean> {

    private final CouponService.Iface couponService;
    private final FreezeParamsVO freezeParamsVO;

    public SimpleFreezeCouponCommand(final CouponService.Iface couponService, final FreezeParamsVO freezeParamsVO) {
        super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("SimpleFreezeCouponCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(500)));
        this.couponService = couponService;
        this.freezeParamsVO = freezeParamsVO;
    }

    @Override
    protected Boolean run() throws Exception {
        log.warn("调用充代金券冻结接口, freezeParamsVO: {}", freezeParamsVO);
        ReturnResult returnResult = couponService.simpleFreeze(freezeParamsVO);
        if (Objects.isNull(returnResult) || returnResult.getCode() != 0 || Objects.isNull(returnResult.getData())) {
            log.warn("调用充代金券冻结接口, 调用失败, freezeParamsVO: {}, response: {}", freezeParamsVO, returnResult);
            return false;
        }
        OperResult operResult = returnResult.getData();
        return operResult.getResult() == 0 || operResult.getResult() == 8;
    }

    @Override
    protected Boolean getFallback() {
        final boolean fallback = false;
        log.warn("SimpleFreezeCouponCommand 服务降级! 调用充代金券冻结接口! 降级返回数据: {}, 降级原因: {}",
                fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }
}
