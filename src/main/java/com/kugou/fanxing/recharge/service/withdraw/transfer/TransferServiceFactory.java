package com.kugou.fanxing.recharge.service.withdraw.transfer;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.kugou.fanxing.recharge.service.withdraw.AlipayResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
public class TransferServiceFactory {

    @Autowired
    private ApplicationContext applicationContext;

    public Optional<TransferService> createTransferService(AlipayResp alipayResp) {
        // 提现不存在
        if (alipayResp.isTransferNotExists()) {
            return Optional.of(applicationContext.getBean(TransferNotExistsService.class));
        }
        // 提现成功
        if (alipayResp.isTransferSuccess()) {
            return Optional.of(applicationContext.getBean(TransferSuccessService.class));
        }
        // 提现中
        if (alipayResp.isTransferProcess()) {
            return Optional.of(applicationContext.getBean(TransferProcessService.class));
        }
        // 提现失败
        if (alipayResp.isTransferFailure() || alipayResp.getError_code() > 0) {
            return Optional.of(applicationContext.getBean(TransferFailureService.class));
        }
        // 提现取消
        if (alipayResp.isTransferCanceled()) {
            return Optional.of(applicationContext.getBean(TransferCanceledService.class));
        }
        Cat.logEvent("withdraw.errorResponse", JSON.toJSONString(alipayResp));
        log.error("处理支付网关提现状态，无法处理响应。alipayResp: {}", alipayResp);
        return Optional.empty();
    }
}
