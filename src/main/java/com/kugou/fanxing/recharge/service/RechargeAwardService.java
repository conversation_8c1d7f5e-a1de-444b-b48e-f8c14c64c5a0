package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.model.bo.RechargeAwardConfig;
import com.kugou.fanxing.recharge.model.bo.RechargeGear;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.RechargeAwardConfigRequest;
import com.kugou.fanxing.recharge.model.request.RechargeAwardRequest;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.thrift.pay.v2.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class RechargeAwardService {

    @Autowired
    private PlatformPayV2Service.Iface platformPayV2Service;
    @Autowired
    private RechargeOrderService rechargeOrderService;

    @SneakyThrows
    public Optional<RechargeGear> queryRechargeAward(WebCommonParam commonParam, RechargeAwardRequest rechargeAwardRequest, RechargeAwardConfig rechargeAwardConfig) {
        Optional<RechargeAcrossPO> optionalRechargeAcrossPO = rechargeOrderService.queryByRechargeOrderNum(rechargeAwardRequest.getRechargeOrderNum());
        if (!optionalRechargeAcrossPO.isPresent() || optionalRechargeAcrossPO.get().getStatus() != 1) {
            log.error("查询充值活动奖励信息，充值订单不存在。commonParam: {}, rechargeAwardRequest: {}, rechargeAwardConfig: {}",
                    commonParam, rechargeAwardRequest, rechargeAwardConfig);
            return Optional.empty();
        }
        RechargeAcrossPO rechargeAcrossPO = optionalRechargeAcrossPO.get();
        QueryRechargeAwardRequest request = new QueryRechargeAwardRequest()
                .setBizCode(rechargeAwardRequest.getBizCode())
                .setSourceId(rechargeAwardRequest.getSourceId())
                .setKugouId(commonParam.getKugouId())
                .setRechargeOrderNum(rechargeAcrossPO.getRechargeOrderNum())
                .setRechargeTime(rechargeAcrossPO.getRechargeTime());
        QueryRechargeAwardResponse response = this.platformPayV2Service.queryRechargeAward(request);
        if (Objects.isNull(response) || response.getCode() != 0 || Objects.isNull(response.getRechargeAwardDto())) {
            log.error("查询充值活动奖励信息，暂无奖励信息。commonParam: {}, rechargeAwardRequest: {}, rechargeAwardConfig: {}, request: {}, response: {}",
                    commonParam, rechargeAwardRequest, rechargeAwardConfig, request, response);
            return Optional.empty();
        }
        RechargeAwardDto rechargeAwardDto = response.getRechargeAwardDto();
        BigDecimal amount = new BigDecimal(rechargeAwardDto.getAmount());
        return rechargeAwardConfig.getRechargeGears().stream()
                .filter(rechargeGear -> amount.compareTo(rechargeGear.getStartAmount()) >= 0 && amount.compareTo(rechargeGear.getEndAmount()) < 0)
                .findFirst();
    }

    @SneakyThrows
    public boolean canParticipate(WebCommonParam commonParam, RechargeAwardConfigRequest rechargeAwardConfigRequest, RechargeAwardConfig rechargeAwardConfig) {
        CanParticipateAwardRequest request = new CanParticipateAwardRequest()
                .setBizCode(rechargeAwardConfigRequest.getBizCode())
                .setSourceId(rechargeAwardConfigRequest.getSourceId())
                .setKugouId(commonParam.getKugouId())
                .setStdPlat(commonParam.getStdPlat());
        CanParticipateAwardResponse response = this.platformPayV2Service.canParticipateAward(request);
        if (Objects.isNull(response) || response.getCode() != 0) {
            log.error("查询充值活动参与资格，查询失败。commonParam: {}, rechargeAwardConfigRequest: {}, rechargeAwardConfig: {}, request: {}, response: {}",
                    commonParam, rechargeAwardConfigRequest, rechargeAwardConfig, request, response);
            return false;
        }
        return response.isCanParticipate();
    }
}
