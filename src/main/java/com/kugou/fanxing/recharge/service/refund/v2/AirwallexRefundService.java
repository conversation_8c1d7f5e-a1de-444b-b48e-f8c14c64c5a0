package com.kugou.fanxing.recharge.service.refund.v2;

import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.model.dto.AirwallexRefundNotifyDto;
import com.kugou.fanxing.recharge.model.dto.RefundNotifyDto;
import com.kugou.fanxing.recharge.model.po.RefundOrderV2Po;
import com.kugou.fanxing.recharge.util.ParseUtils;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;
import java.util.stream.Stream;

@Slf4j
@Service
public class AirwallexRefundService extends AbstractRefundService {

    @Getter
    @ToString
    private enum AirwallexRefundType {
        DISPUTE_RFI_RECEIVED_BY_MERCHANT(11, "用户发起退款争议"),
        DISPUTE_LOST(17, "商户在退款争议中败诉退款"),
        ;

        private final int code;
        private final String desc;

        AirwallexRefundType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static Optional<AirwallexRefundType> of(int refundType) {
            return Stream.of(AirwallexRefundType.values())
                    .filter(airwallexRefundType -> airwallexRefundType.code == refundType)
                    .findFirst();
        }

        public static boolean hasRefund(int refundType) {
            return AirwallexRefundType.DISPUTE_LOST.getCode() == refundType;
        }

        public static boolean hasRefundRequest(int refundType) {
            return AirwallexRefundType.DISPUTE_RFI_RECEIVED_BY_MERCHANT.getCode() == refundType;
        }

    }

    @Override
    protected <T extends RefundNotifyDto> Optional<RefundOrderV2Po> convertRefundNotifyDtoToRefundOrder(T t) {
        if (t instanceof AirwallexRefundNotifyDto) {
            AirwallexRefundNotifyDto airwallexRefundNotifyDto = (AirwallexRefundNotifyDto) t;
            int refundType = airwallexRefundNotifyDto.getRefund_type();
            if (AirwallexRefundType.hasRefund(refundType)) {
                RefundOrderV2Po refundOrderV2Po = buildRefundOrderV2Po(airwallexRefundNotifyDto);
                refundOrderV2Po.setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_522.getPayTypeId());
                return Optional.of(refundOrderV2Po);
            }
            if (AirwallexRefundType.hasRefundRequest(refundType)) {
                log.warn("Airwallex获取用户退款通知，发生退款事情。airwallexRefundNotifyDto: {}", airwallexRefundNotifyDto);
            }
            log.warn("Airwallex获取用户退款通知，未知退款类型。airwallexRefundNotifyDto: {}", airwallexRefundNotifyDto);
            return Optional.empty();
        }
        throw new IllegalArgumentException("退款对象类型错误");
    }

    private RefundOrderV2Po buildRefundOrderV2Po(AirwallexRefundNotifyDto airwallexRefundNotifyDto) {
        return RefundOrderV2Po.builder()
                .refundId(orderIdService.generateGlobalId())
                .kupayAppId(airwallexRefundNotifyDto.getBiz_appid())
                .outTradeNo(airwallexRefundNotifyDto.getOut_trade_no())
                .tradeNo(airwallexRefundNotifyDto.getTrade_no())
                .kupayOrderNo(airwallexRefundNotifyDto.getOrder_no())
                .originOrderFee(new BigDecimal(airwallexRefundNotifyDto.getOrigin_order_fee()))
                .requestRefundFee(new BigDecimal(airwallexRefundNotifyDto.getRequest_refund_fee()))
                .actualRefundFee(new BigDecimal(airwallexRefundNotifyDto.getActual_refund_fee()))
                .kugouId(0)
                .rechargeOrderNum("")
                .payTypeId(0)
                .status(0)
                .errorReason("")
                .coin(BigDecimal.ZERO)
                .debtCoin(BigDecimal.ZERO)
                .addTime(ParseUtils.tryParseLong(airwallexRefundNotifyDto.getAdd_time(), 0))
                .refundTime(ParseUtils.tryParseLong(airwallexRefundNotifyDto.getRefund_time(), 0))
                .refundType(airwallexRefundNotifyDto.getRefund_type())
                .refundReason(airwallexRefundNotifyDto.getRefund_reason())
                .createTime(new Date())
                .updateTime(new Date())
                .build();
    }
}
