package com.kugou.fanxing.recharge.service.withdraw;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.WithdrawAccountWechatDao;
import com.kugou.fanxing.recharge.model.po.WithdrawAccountWechatPO;
import com.kugou.fanxing.recharge.model.po.WithdrawOrderPO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 微信提现相关接口
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class StWechatCashService implements CashService {

    private static final String DEFAULT_REMARK = "酷狗直播提现";

    @Value("${kupay.intranet}")
    private String withdrawHostUrl;
    @Value("${kupay.withdraw.serverId}")
    private String withdrawServerId;
    @Value("${kupay.withdraw.serverKey}")
    private String withdrawServerKey;
    @Autowired
    private WithdrawAccountWechatDao withdrawAccountWechatDao;
    @Autowired
    private ApolloConfigService apolloConfigService;

    /**
     * 深田-微信提现
     * http://doc.kugou.net/showdoc-master/web/#/8?page_id=8406
     *
     * @param withdrawOrderPO      提现订单
     * @param withdrawClientParams 客户端参数
     * @return 支付网关提现响应
     */
    public Optional<AlipayResp> withdraw(WithdrawOrderPO withdrawOrderPO, WithdrawClientParams withdrawClientParams, String token) {
        try {
            StringBuilder url = new StringBuilder(withdrawHostUrl);
            url.append("/v1/transfer/order");
            Map<String, String> urlParams = buildWithdrawCommonParams(withdrawServerId, withdrawClientParams);
            JSONObject params = new JSONObject();
            params.put("biz_appid", withdrawOrderPO.getBizAppId());
            params.put("kugouid", withdrawOrderPO.getKugouId());
            params.put("clientip", withdrawClientParams.getClientip());
            params.put("order_no", withdrawOrderPO.getOrderId());
            params.put("user_account", withdrawOrderPO.getOpenid());
            params.put("channel_type", 2);
            params.put("withdraw_type", 1);
            params.put("api_version", 2);
            params.put("total_fee", withdrawOrderPO.getTotalAmount().multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString());
            params.put("remark", apolloConfigService.matchDrawCashRemark(withdrawOrderPO.getBizAppId(), DEFAULT_REMARK));
            urlParams.put("signature", getCommandSign(withdrawServerKey, urlParams, params.toJSONString()));
            log.warn("调用深田微信提现接口，请求参数。url: {}, params: {}", url, params);
            Optional<String> optionalJson = HttpClientUtils.doPostJsonBody(url.toString(), urlParams, params.toJSONString());
            if (!optionalJson.isPresent() || StringUtils.isBlank(optionalJson.get())) {
                log.error("调用深田微信提现接口，调用失败。optionalJson: {}, url: {}, urlParams: {}, jsonBody: {}, rechargeDrawCashPO: {}",
                        optionalJson, url, urlParams, params.toJSONString(), withdrawOrderPO);
                return Optional.empty();
            }
            AlipayResp alipayResp = JSON.parseObject(optionalJson.get(), AlipayResp.class);
            log.warn("调用深田微信提现接口，调用成功。optionalJson: {}, url: {}, urlParams: {}, jsonBody: {}, rechargeDrawCashPO: {}",
                    optionalJson, url, urlParams, params.toJSONString(), withdrawOrderPO);
            return Optional.of(alipayResp);
        } catch (Exception e) {
            log.error("调用深田微信提现接口，调用异常。rechargeDrawCashPO: {}", withdrawOrderPO, e);
        }
        return Optional.empty();
    }

    /**
     * 深田-微信提现查询接口
     * http://doc.kugou.net/showdoc-master/web/#/8?page_id=8415
     *
     * @param withdrawOrderPO 提现订单
     * @return 提现信息
     */
    @Override
    public Optional<AlipayResp> withdrawOrderQuery(WithdrawOrderPO withdrawOrderPO) {
        int bizAppId = withdrawOrderPO.getBizAppId();
        long kugouId = withdrawOrderPO.getKugouId();
        long orderId = withdrawOrderPO.getOrderId();
        String wxAppId = apolloConfigService.getWxAppIdByBizAppId(bizAppId);
        WithdrawAccountWechatPO withdrawAccountPO = this.withdrawAccountWechatDao.selectByKugouId(kugouId, wxAppId);
        if (Objects.isNull(withdrawAccountPO) || StringUtils.isBlank(withdrawAccountPO.getOpenid())) {
            log.error("调用深田微信提现查询接口，获取用户openid失败。kugouId: {}, orderId: {}", kugouId, orderId);
            throw new ContextedRuntimeException("调用微信提现查询接口，获取用户openid失败")
                    .addContextValue("kugouId", kugouId)
                    .addContextValue("orderId", orderId);
        }
        String openid = withdrawAccountPO.getOpenid();
        try {
            StringBuilder url = new StringBuilder(withdrawHostUrl);
            url.append("/v1/transfer/query");
            Map<String, String> params = buildWithdrawQueryCommonParams(withdrawServerId);
            params.put("biz_appid", String.valueOf(bizAppId));
            params.put("kugouid", String.valueOf(kugouId));
            params.put("order_no", String.valueOf(orderId));
            params.put("out_trade_no", withdrawOrderPO.getOutTradeNo());
            params.put("signature", getQuerySign(withdrawServerKey, params));
            log.warn("调用深田微信提现查询接口，请求参数。url: {}, params: {}, kugouId: {}, openid: {}, orderId: {}",
                    url, params, kugouId, openid, orderId);
            Optional<String> optionalJson = HttpClientUtils.doSyncGet(url.toString() , params);
            if (!optionalJson.isPresent() || StringUtils.isBlank(optionalJson.get())) {
                log.warn("调用深田微信提现查询接口，调用失败。optionalJson: {}, url: {}, params: {}, kugouId: {}, openid: {}, orderId: {}",
                        optionalJson, url, params, kugouId, openid, orderId);
                return Optional.empty();
            }
            AlipayResp alipayResp = JSON.parseObject(optionalJson.get(), AlipayResp.class);
            log.warn("调用深田微信提现查询接口，调用成功。optionalJson: {}, url: {}, params: {}, alipayResp: {}, kugouId: {}, openid: {}, orderId: {}",
                    optionalJson, url, params, alipayResp, kugouId, openid, orderId);
            return Optional.of(alipayResp);
        } catch (Exception e) {
            log.error("调用深田微信提现查询接口，调用异常。kugouId: {}, openid: {}, orderId: {}", kugouId, openid, orderId, e);
        }
        return Optional.empty();
    }
}
