package com.kugou.fanxing.recharge.service;

import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossOrderNumDao;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossOrdernumPO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.thrift.*;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.thrift.pay.v2.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RechargeOrderService {

    @Autowired
    private OrderIdService orderIdService;
    @Autowired
    private RechargeAcrossOrderNumDao rechargeAcrossOrderNumDao;
    @Autowired
    private RechargeAcrossDao rechargeAcrossDao;
    @Autowired
    private PlatformPayV2Service.Iface platformPayV2Service;

    public int addRechargeOrder(RechargeAcrossPO rechargeAcrossPO) {
        String rechargeOrderNum = rechargeAcrossPO.getRechargeOrderNum();
        String month = this.orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
        return rechargeAcrossDao.insertIgnore(month, rechargeAcrossPO);
    }

    public Optional<RechargeAcrossPO> queryByRechargeOrderNum(String rechargeOrderNum) {
        String month = this.orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
        return Optional.ofNullable(this.rechargeAcrossDao.queryByRechargeOrderNum(month, rechargeOrderNum));
    }

    @SneakyThrows
    public Optional<String> queryRechargeOrderNumByTradeNo(String tradeNo) {
        QueryRechargeOrderNumByTradeNoRequest request = new QueryRechargeOrderNumByTradeNoRequest();
        request.setAppId(1000_0002);
        request.setTradeNo(tradeNo);
        request.setSign(FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXt"));
        QueryRechargeOrderNumByTradeNoResponse response = this.platformPayV2Service.queryRechargeOrderNumByTradeNo(request);
        if (Objects.isNull(response) || response.getCode() != 0 || Objects.isNull(response.getData())) {
            log.error("查询苹果充值成功交易信息，无对应rechargeOrderNum。request: {}, response: {}", request, response);
            return Optional.empty();
        }
        String rechargeOrderNum = response.getData().getRechargeOrderNum();
        return Optional.ofNullable(rechargeOrderNum);
    }

    public RechargeInfo getRechargeInfoByRechargeOrderNum(String rechargeOrderNum) {
        Optional<RechargeAcrossPO> optionalRechargeAcrossPO = queryByRechargeOrderNum(rechargeOrderNum);
        log.warn("查询充值成功记录<getRechargeInfoByRechargeOrderNum>，请求参数。rechargeOrderNum: {}, optionalRechargeAcrossPO: {}", rechargeOrderNum, optionalRechargeAcrossPO);
        RechargeInfo rechargeInfo = null;
        if (optionalRechargeAcrossPO.isPresent() && optionalRechargeAcrossPO.get().getStatus() == 1) {
            rechargeInfo = new RechargeInfo();
            RechargeAcrossPO rechargeAcrossPO = optionalRechargeAcrossPO.get();
            rechargeInfo.setRechargeOrderNum(rechargeAcrossPO.getRechargeOrderNum());
            rechargeInfo.setConsumeOrderNum(StringUtils.defaultString(rechargeAcrossPO.getConsumeOrderNum()));
            rechargeInfo.setAddTime(rechargeAcrossPO.getAddTime());
            rechargeInfo.setRechargeTime(rechargeAcrossPO.getRechargeTime());
            rechargeInfo.setKugouId(rechargeAcrossPO.getKugouId());
            rechargeInfo.setFromKugouId(rechargeAcrossPO.getFromKugouId());
            rechargeInfo.setCoin(rechargeAcrossPO.getCoin().stripTrailingZeros().toPlainString());
            rechargeInfo.setAmount(rechargeAcrossPO.getAmount().stripTrailingZeros().toPlainString());
            rechargeInfo.setRealAmount(rechargeAcrossPO.getRealAmount().stripTrailingZeros().toPlainString());
            rechargeInfo.setExtraJsonData(StringUtils.defaultString(mergeExtraJsonData(rechargeAcrossPO)));
            rechargeInfo.setRefer(String.valueOf(rechargeAcrossPO.getRefer()));
            rechargeInfo.setCFrom(rechargeAcrossPO.getCFrom());
            rechargeInfo.setChannelId(rechargeAcrossPO.getChannelId());
            rechargeInfo.setReType(rechargeAcrossPO.getReType());
            rechargeInfo.setPayTypeId(rechargeAcrossPO.getPayTypeId());
            rechargeInfo.setExtend(StringUtils.defaultString(rechargeAcrossPO.getExtend()));
            rechargeInfo.setBusinessId(StringUtils.defaultString(rechargeAcrossPO.getBusinessId()));
        }
        return rechargeInfo;
    }

    /**
     * 特殊充值渠道的第三方订单号以及繁星订单号的对应处理
     *
     * @param orderNum  充值订单号
     * @param payTypeId 充值渠道ID
     * @return 充值订单号
     */
    @Deprecated
    public Optional<String> specialRechargeOrderNumDeal(String orderNum, int payTypeId) {
        //如果是双活架构，则将第三方订单号替换为繁星订单号存储，并建立对应关系
        String rechargeOrderNum = this.makeRechargeOrderNumForAcrossSpecial(orderNum, payTypeId);
        int affected = rechargeAcrossOrderNumDao.insertReturnRechargeOrderNum(rechargeOrderNum, orderNum);
        if (affected < 1) {
            log.warn("绑定第三方交易号与直播充值订单号，数据插入。rechargeOrderNum: {}, orderNum: {}, affected: {}", rechargeOrderNum, orderNum, affected);
        }
        Optional<RechargeAcrossOrdernumPO> optionalRechargeAcrossOrdernumPO = Optional.ofNullable(rechargeAcrossOrderNumDao.getByPrimary(orderNum));
        if (!optionalRechargeAcrossOrdernumPO.isPresent() || StringUtils.isBlank(optionalRechargeAcrossOrdernumPO.get().getRechargeOrderNum())) {
            log.error("绑定第三方交易号与直播充值订单号，绑定失败。rechargeOrderNum: {}, orderNum: {}, payTypeId: {}", rechargeOrderNum, orderNum, payTypeId);
            return Optional.empty();
        }
        rechargeOrderNum = optionalRechargeAcrossOrdernumPO.get().getRechargeOrderNum();
        return this.convertTradeNoToRechargeOrderNum(orderNum, rechargeOrderNum);
    }

    public Optional<String> convertTradeNoToRechargeOrderNum(String tradeNo, String rechargeOrderNum) {
        // 第三方交易号 ==> 直播交易号
        ConvertTradeNoRequest request = new ConvertTradeNoRequest();
        request.setAppId(1000_0002);
        request.setTradeNo(tradeNo);
        request.setRechargeOrderNum(rechargeOrderNum);
        request.setSign(FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXt"));
        try {
            ConvertTradeNoResponse response = platformPayV2Service.convertTradeNoToRechargeOrderNum(request);
            if (Objects.isNull(response) || response.getCode() != 0 || Objects.isNull(response.getData())) {
                log.error("通过主备服务获取第三方交易号映射关系，获取失败。request: {}, response: {}", request, response);
                return Optional.empty();
            }
            TradeNoMappingDTO tradeNoMappingDTO = response.getData();
            log.warn("通过主备服务获取第三方交易号映射关系，获取成功。tradeNoMappingDTO: {}", tradeNoMappingDTO);
            return Optional.of(tradeNoMappingDTO.getRechargeOrderNum());
        } catch (Exception e) {
            log.error("通过主备服务获取第三方交易号映射关系，获取异常。request: {}, message: {}", request, e);
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR, e);
        }
    }

    public Optional<String> generateRechargeOrderNumByTradeNo(PayTypeIdEnum payTypeIdEnum, String tradeNo) {
        if (PayTypeIdEnum.isPddRecharge(payTypeIdEnum.getPayTypeId())) {
            String universalTradeNo = payTypeIdEnum.getPayTypeCode().concat(tradeNo);
            String rechargeOrderNum = this.orderIdService.generateRechargeOrderNumForAcross();
            return this.convertTradeNoToRechargeOrderNum(universalTradeNo, rechargeOrderNum);
        }
        throw new ContextedRuntimeException("不支持的支付渠道")
                .addContextValue("payTypeId", payTypeIdEnum)
                .addContextValue("tradeNo", tradeNo);
    }

    /**
     * 生成跨机房订单号(AppStore、Tmall充值的订单号生成规则)
     *
     * @param orderNum  业务订单
     * @param payTypeId 支付渠道
     * @return
     */
    public String makeRechargeOrderNumForAcrossSpecial(String orderNum, int payTypeId){
        //因为App Store以及天猫充值只有单次回调，返点时无法双活处理，默认机房为0，北京兆维
        int thisServerRoom = 0;
        String payTypeStr = "0";
        switch(payTypeId){
            case 1006: payTypeStr = "1"; break;
            case 1012: payTypeStr = "2"; break;
            default: break;
        }
        String yearMonth = DateHelper.formatYearMonth(new Date());
        String rechargeOrderNum = String.format("R%s9%sS%s%s", thisServerRoom, yearMonth, payTypeStr, orderNum);
        log.warn("苹果充值与天猫充值生成跨机房订单号, orderNum: {}", rechargeOrderNum);
        return rechargeOrderNum;
    }

    public List<RechargeSuccessInfo> getRechargeSuccessList(QueryRechargeSuccessRequest request) {
        return this.getRechargeSuccessList(request.getBeginTime(), request.getEndTime(), request.getLastRechargeId(),
                request.getBatchSize(), request.isIncludeSingCoin());
    }

    public List<RechargeSuccessInfo> getRechargeSuccessList(long beginTime, long endTime, long lastRechargeId,
                                                            int batchSize, Set<Integer> coinTypeSet) {
        if (beginTime < DateHelper.getSecondsByDay("20150101")) {
            log.error("查询充值成功记录，参数非法。beginTime: {}, endTime: {}, lastRechargeId: {}, batchSize: {}",
                    beginTime, endTime, lastRechargeId, batchSize);
            throw new BizException(SysResultCode.RECHARGE_PARAM_ERROR);
        }
        List<RechargeAcrossPO> rechargeAcrossPOList = getSuccessOrderListByCoinType(beginTime, endTime, lastRechargeId, batchSize, coinTypeSet);
        if (CollectionUtils.isEmpty(rechargeAcrossPOList)) {
            return Lists.newArrayList();
        }
        return rechargeAcrossPOList.stream().map(this::buildRechargeSuccessInfo).collect(Collectors.toList());
    }

    private RechargeSuccessInfo buildRechargeSuccessInfo(RechargeAcrossPO rechargeAcrossPO) {
        RechargeSuccessInfo rechargeInfo = new RechargeSuccessInfo();
        rechargeInfo.setRechargeId(rechargeAcrossPO.getRechargeId());
        rechargeInfo.setRechargeOrderNum(rechargeAcrossPO.getRechargeOrderNum());
        rechargeInfo.setConsumeOrderNum(StringUtils.defaultString(rechargeAcrossPO.getConsumeOrderNum()));
        rechargeInfo.setAddTime(rechargeAcrossPO.getAddTime());
        rechargeInfo.setRechargeTime(rechargeAcrossPO.getRechargeTime());
        rechargeInfo.setKugouId(rechargeAcrossPO.getKugouId());
        rechargeInfo.setFromKugouId(rechargeAcrossPO.getFromKugouId());
        rechargeInfo.setCoin(rechargeAcrossPO.getCoin().stripTrailingZeros().toPlainString());
        rechargeInfo.setAmount(rechargeAcrossPO.getAmount().stripTrailingZeros().toPlainString());
        rechargeInfo.setRealAmount(rechargeAcrossPO.getRealAmount().stripTrailingZeros().toPlainString());
        rechargeInfo.setExtraJsonData(StringUtils.defaultString(mergeExtraJsonData(rechargeAcrossPO)));
        rechargeInfo.setRefer(rechargeAcrossPO.getRefer());
        rechargeInfo.setCFrom(rechargeAcrossPO.getCFrom());
        rechargeInfo.setChannelId(rechargeAcrossPO.getChannelId());
        rechargeInfo.setReType(rechargeAcrossPO.getReType());
        rechargeInfo.setPayTypeId(rechargeAcrossPO.getPayTypeId());
        rechargeInfo.setExtend(StringUtils.defaultString(rechargeAcrossPO.getExtend()));
        rechargeInfo.setMoney(convertBigDecimal2Str(rechargeAcrossPO.getMoney()));
        rechargeInfo.setTradeNo(StringUtils.defaultString(rechargeAcrossPO.getTradeNo()));
        rechargeInfo.setTradeTime(rechargeAcrossPO.getTradeTime());
        rechargeInfo.setPartner(StringUtils.defaultString(rechargeAcrossPO.getPartner()));
        rechargeInfo.setCoupon(convertBigDecimal2Str(rechargeAcrossPO.getCoupon()));
        rechargeInfo.setBusinessId(StringUtils.defaultString(rechargeAcrossPO.getBusinessId()));
        rechargeInfo.setServerRoom(rechargeAcrossPO.getServerRoom());
        rechargeInfo.setCouponOrderId(rechargeAcrossPO.getCouponOrderId());
        rechargeInfo.setCouponId(rechargeAcrossPO.getCouponId());
        rechargeInfo.setCouponStatus(rechargeAcrossPO.getCouponStatus());
        rechargeInfo.setIsSandbox(rechargeAcrossPO.getIsSandbox());
        rechargeInfo.setCoinType(rechargeAcrossPO.getCoinType());
        return rechargeInfo;
    }

    public List<RechargeSuccessInfo> getRechargeSuccessList(long beginTime, long endTime, long lastRechargeId, int batchSize, boolean includeSingCoin) {
        if (beginTime < DateHelper.getSecondsByDay("20150101")) {
            log.error("查询充值成功记录，参数非法。beginTime: {}, endTime: {}, lastRechargeId: {}, batchSize: {}", beginTime, endTime, lastRechargeId, batchSize);
            throw new BizException(SysResultCode.RECHARGE_PARAM_ERROR);
        }
        List<RechargeAcrossPO> rechargeAcrossPOList = getSuccessOrderList(beginTime, endTime, lastRechargeId, batchSize, includeSingCoin);
        if (CollectionUtils.isEmpty(rechargeAcrossPOList)) {
            return Lists.newArrayList();
        }
        return rechargeAcrossPOList.stream().map(this::buildRechargeSuccessInfo).collect(Collectors.toList());
    }

    public String convertBigDecimal2Str(BigDecimal value) {
        return Optional.ofNullable(value).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString();
    }

    public List<RechargeInfo> getSuccessOrderListFormat(long beginTime, long endTime, long lastId, int pageSize, boolean includeSingCoin) {
        List<RechargeAcrossPO> list = getSuccessOrderList(beginTime,endTime,lastId,pageSize, includeSingCoin);
        if(CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }
        List<RechargeInfo> result = new ArrayList<>();
        for (RechargeAcrossPO rechargeOrder:list) {
            RechargeInfo rechargeInfo = new RechargeInfo();
            rechargeInfo.setAddTime(rechargeOrder.getAddTime());
            rechargeInfo.setAgentKugouId(rechargeOrder.getAgentKugouId());
            rechargeInfo.setAmount(rechargeOrder.getAmount().toString());
            rechargeInfo.setCFrom(rechargeOrder.getCFrom());
            rechargeInfo.setChannelId(rechargeOrder.getChannelId());
            rechargeInfo.setCoin(rechargeOrder.getCoin().toString());
            rechargeInfo.setCoinAfter(rechargeOrder.getCoinAfter().toString());
            rechargeInfo.setCoinBefore(rechargeOrder.getCoinBefore().toString());
            rechargeInfo.setConsumeOrderNum(rechargeOrder.getConsumeOrderNum());
            rechargeInfo.setExtend(rechargeOrder.getExtend());
            rechargeInfo.setExtraJsonData(mergeExtraJsonData(rechargeOrder));
            rechargeInfo.setFromKugouId(rechargeOrder.getFromKugouId());
            rechargeInfo.setId(rechargeOrder.getRechargeId());
            rechargeInfo.setKugouId(rechargeOrder.getKugouId());
            rechargeInfo.setPayTypeId(rechargeOrder.getPayTypeId());
            rechargeInfo.setRealAmount(rechargeOrder.getRealAmount().toString());
            rechargeInfo.setRechargeOrderNum(rechargeOrder.getRechargeOrderNum());
            rechargeInfo.setRechargeTime(rechargeOrder.getRechargeTime());
            rechargeInfo.setRefer(String.valueOf(rechargeOrder.getRefer()));
            rechargeInfo.setReType(rechargeOrder.getReType());
            rechargeInfo.setBusinessId(rechargeOrder.getBusinessId());
            result.add(rechargeInfo);
        }
        return result;

    }

    private String mergeExtraJsonData(RechargeAcrossPO rechargeAcrossPO) {
        JsonObject jsonObject = null;
        if(rechargeAcrossPO.getExtraJsonData() != null && !rechargeAcrossPO.getExtraJsonData().isEmpty()){
            try {
                jsonObject = new JsonParser().parse(rechargeAcrossPO.getExtraJsonData()).getAsJsonObject();
            }catch (Exception e){
                log.error("extraJsonDataError, order:{},exception:",rechargeAcrossPO,e);
            }
        }
        if(jsonObject == null){
            jsonObject = new JsonObject();
        }
        jsonObject.addProperty("rechargeRebateGray",1);
        return new Gson().toJson(jsonObject);
    }

    private List<RechargeAcrossPO> getSuccessOrderListByCoinType(long beginTime, long endTime, long lastRechargeId,
                                                                 int batchSize, Set<Integer> coinTypeSet) {
        if (endTime < beginTime) {
            log.error("拉取充值成功流水，参数非法。beginTime: {}, endTime: {}, lastId: {}, batchSize: {}, coinTypeSet: {}",
                    beginTime, endTime, lastRechargeId, batchSize, coinTypeSet);
            throw new BizException(SysResultCode.RECHARGE_PARAM_ERROR);
        }
        int pageSize = Math.min(batchSize, 100);
        long lastId = Math.max(lastRechargeId, 0);
        long sTime = Math.max(beginTime, 1);
        long eTime = Math.max(endTime, 1);
        String sMonth = DateFormatUtils.format(sTime * 1000, "yyyyMM");
        String eMonth = DateFormatUtils.format(eTime * 1000, "yyyyMM");
        List<RechargeAcrossPO> sPartList = rechargeAcrossDao.getRechargeSuccessList(sMonth, lastId, sTime, eTime, pageSize, coinTypeSet);
        if (sMonth.equalsIgnoreCase(eMonth)) {
            return sPartList;
        }
        List<RechargeAcrossPO> ePartList = rechargeAcrossDao.getRechargeSuccessList(eMonth, lastId, sTime, eTime, pageSize, coinTypeSet);
        sPartList.addAll(ePartList);
        return sPartList.subList(0, Math.min(pageSize, sPartList.size()));
    }

    public List<RechargeAcrossPO> getSuccessOrderList(long beginTime, long endTime, long lastId, int pageSize, boolean includeSingCoin) {
        if (endTime < beginTime) {
            log.error("拉取充值成功流水，参数非法。beginTime: {}, endTime: {}, lastId: {}, pageSize: {}, includeSingCoin: {}",
                    beginTime, endTime, lastId, pageSize, includeSingCoin);
            throw new BizException(SysResultCode.RECHARGE_PARAM_ERROR);
        }
        pageSize = Math.max(Math.min(pageSize, 100), 10);
        lastId = Math.max(lastId, 0);
        long sTime = Math.max(beginTime, 1);
        long eTime = Math.max(endTime, 1);
        String sMonth = DateFormatUtils.format(sTime * 1000, "yyyyMM");
        String eMonth = DateFormatUtils.format(eTime * 1000, "yyyyMM");
        List<RechargeAcrossPO> sPartList = rechargeAcrossDao.getRechargeAcrossSuccessListByTimeAndId(sMonth, lastId, sTime, eTime, pageSize, includeSingCoin);
        if (sMonth.equalsIgnoreCase(eMonth)) {
            return sPartList;
        }
        List<RechargeAcrossPO> ePartList = rechargeAcrossDao.getRechargeAcrossSuccessListByTimeAndId(eMonth, lastId, sTime, eTime, pageSize, includeSingCoin);
        sPartList.addAll(ePartList);
        return sPartList.subList(0, Math.min(pageSize, sPartList.size()));
    }

    @SneakyThrows
    public String mappingRechargeOrderNum(int bizId, String bizOrderNo) {
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        ConvertBizOrderNoRequest request = new ConvertBizOrderNoRequest();
        request.setBizId(bizId);
        request.setBizOrderNo(bizOrderNo);
        request.setRechargeOrderNum(rechargeOrderNum);
        ConvertBizOrderNoResponse response = this.platformPayV2Service.convertBizOrderNoToRechargeOrderNum(request);
        if (Objects.isNull(response) || response.getCode() != 0 || Objects.isNull(response.getData())) {
            log.error("查询唱唱映射直播交易号失败。request: {}, response: {}", request, response);
            throw new AckException(SysResultCode.FAILURE).addContextValue("bizId", request.getBizId())
                    .addContextValue("bizOrderNo", request.getBizOrderNo());
        }
        return response.getData().getRechargeOrderNum();
    }

    @SneakyThrows
    public Optional<RechargeAcrossPO> queryBizOrder(QueryBizOrderRequest request) {
        String rechargeOrderNum = this.mappingRechargeOrderNum(request.getBusinessId(), request.getOrderNo());
        return this.queryByRechargeOrderNum(rechargeOrderNum);
    }
}
