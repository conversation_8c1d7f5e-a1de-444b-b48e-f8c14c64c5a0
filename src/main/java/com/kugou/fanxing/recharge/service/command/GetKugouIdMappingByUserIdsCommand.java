package com.kugou.fanxing.recharge.service.command;

import com.google.common.collect.Maps;
import com.kugou.fanxing.userbaseinfo.service.UserModuleV2BizService;
import com.kugou.fanxing.userbaseinfo.vo.ResIDMapMsg;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 繁星ID=>酷狗ID
 *
 * <AUTHOR>
 */
@Slf4j
public class GetKugouIdMappingByUserIdsCommand extends HystrixCommand<Map<Long, Long>> {

    private final UserModuleV2BizService.Iface userModuleV2BizService;
    private final List<Long> userIds;

    public GetKugouIdMappingByUserIdsCommand(final UserModuleV2BizService.Iface userModuleV2BizService, final List<Long> userIds) {
        super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("GetKugouIdMappingByUserIdsCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(300)));
        this.userModuleV2BizService = userModuleV2BizService;
        this.userIds = userIds;
    }

    @Override
    protected Map<Long, Long> run() throws Exception {
        ResIDMapMsg resIDMapMsg = this.userModuleV2BizService.getKugouIdMappingByUserIds(userIds);
        if (Objects.isNull(resIDMapMsg) || resIDMapMsg.getRet() != 0 || Objects.isNull(resIDMapMsg.getData())) {
            log.error("批量查询用户酷狗ID信息失败, userIds: {}, resIDMapMsg: {}", userIds, resIDMapMsg);
            return Maps.newHashMap();
        }
        Map<Long, Long> idMapping = resIDMapMsg.getData();
        return MapUtils.isNotEmpty(idMapping) ? idMapping : Maps.newHashMap();
    }

    @Override
    protected Map<Long, Long> getFallback() {
        Map<Long, Long> fallback = Maps.newHashMap();
        log.warn("GetKugouIdMappingByUserIdsCommand服务降级! 通过繁星ID批量查询酷狗ID信息出错, userIds: {}! 降级返回数据: {}, 降级原因: {}",
                userIds, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }
}
