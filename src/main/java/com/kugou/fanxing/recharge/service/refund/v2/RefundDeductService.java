package com.kugou.fanxing.recharge.service.refund.v2;

import com.google.gson.JsonObject;
import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.recharge.constant.AdminAdjustCoinResult;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.DeductCoinSuccessCodeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundDebtV2Dao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundDeductOrderV2Dao;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.bo.AccountChangeTypeBO;
import com.kugou.fanxing.recharge.model.po.RefundDeductOrderV2Po;
import com.kugou.fanxing.recharge.model.vo.UserLogVO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.consume.ConsumeReadService;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.ModelUtils;
import com.kugou.fanxing.thrift.consume.service.ConsumeResp;
import com.kugou.fanxing.thrift.freeze.service.CoinAdjustVO;
import com.kugou.fanxing.thrift.freeze.service.PlatformAddCoinService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Slf4j
@Service
public class RefundDeductService {

    private static final int DEDUCT_COIN_ACCOUNT_CHANGE_TYPE = 980008;

    @Autowired
    private RefundDebtV2Dao refundDebtV2Dao;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private RefundDeductOrderV2Dao refundDeductOrderV2Dao;
    @Autowired
    private ConsumeReadService consumeReadService;
    @Autowired
    private PlatformAddCoinService.Iface platformAddCoinService;

    public AdminAdjustCoinResult deductCoin(RefundDeductOrderV2Po refundDeductOrderV2Po) {
        log.warn("调用管理员星币调整退款扣币，退款扣币开始订单信息。 refundDeductOrderV2Po：{}", refundDeductOrderV2Po);
        CoinAdjustVO coinAdjustVO = new CoinAdjustVO();
        coinAdjustVO.setGlobalId(refundDeductOrderV2Po.getDeductId());
        coinAdjustVO.setTimestamp((int) refundDeductOrderV2Po.getAddTime());
        coinAdjustVO.setAccountChangeType(DEDUCT_COIN_ACCOUNT_CHANGE_TYPE);
        JsonObject ext = new JsonObject();
        ext.addProperty("type", DEDUCT_COIN_ACCOUNT_CHANGE_TYPE);
        coinAdjustVO.setExt(ext.toString());
        coinAdjustVO.setFxcChangeDesc("退款扣币");
        coinAdjustVO.setIp("0.0.0.0");
        coinAdjustVO.setKugouId(refundDeductOrderV2Po.getKugouId());
        coinAdjustVO.setVirtualCoin(String.valueOf(refundDeductOrderV2Po.getDeductCoin().negate()));
        AccountChangeTypeBO accountChangeTypeBO = apolloConfigService.getAccountChangeTypeById(DEDUCT_COIN_ACCOUNT_CHANGE_TYPE);
        if (accountChangeTypeBO == null || accountChangeTypeBO.getConsumeSalt() == null || accountChangeTypeBO.getConsumeSalt().isEmpty()) {
            log.error("调用管理员星币调整退款扣币，accountChangeType配置缺失。refundDeductOrderV2Po：{}", refundDeductOrderV2Po);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        coinAdjustVO.setSign(FinanceSignUtils.makeSign(coinAdjustVO, accountChangeTypeBO.getConsumeSalt()));
        try {
            // 注意⚠️：由于接口存在并发问题，不允许写入后立即执行查询。非成功状态必须全部标记为UNKNOWN(未知状态）延迟查询确认
            ConsumeResp consumeResp = platformAddCoinService.adminAdjustCoin(coinAdjustVO);
            log.warn("调用管理员星币调整退款扣币，请求响应。coinAdjustVO：{}, consumeResp：{}", coinAdjustVO, consumeResp);
            if (DeductCoinSuccessCodeEnum.isSuccess(consumeResp.getRet())) {
                return AdminAdjustCoinResult.SUCCESS;
            }
        } catch (Exception e) {
            log.error("调用管理员星币调整退款扣币，调用异常。refundDeductOrderV2Po：{}", refundDeductOrderV2Po, e);
        }
        return AdminAdjustCoinResult.UNKNOWN;
    }

    public AdminAdjustCoinResult confirmAdminAdjustCoin(RefundDeductOrderV2Po refundDeductOrderV2Po) {
        //回查是否失败
        try {
            String yearMonth = DateHelper.formatYearMonth(refundDeductOrderV2Po.getAddTime() * 1000);
            Optional<UserLogVO> checkRs = consumeReadService.checkUserLog(CoinTypeEnum.STAR_COIN.getCoinType(), yearMonth, refundDeductOrderV2Po.getDeductId(), DEDUCT_COIN_ACCOUNT_CHANGE_TYPE);
            if (checkRs.isPresent() && checkRs.get().getUniqId().startsWith(String.valueOf(refundDeductOrderV2Po.getDeductId()))) {
                return AdminAdjustCoinResult.SUCCESS;
            } else {
                return AdminAdjustCoinResult.FAILURE;
            }
        } catch (Exception e) {
            log.error("调用管理员星币调整退款扣币，确认调用状态异常。：", e);
        }
        return AdminAdjustCoinResult.UNKNOWN;
    }

    @Transactional(transactionManager = "transactionManager_d_fanxing_recharge", propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Optional<RefundDeductOrderV2Po> createDeductOrder(RefundDeductOrderV2Po refundDeductOrderV2Po) {
        int affected = refundDeductOrderV2Dao.insertIgnore(refundDeductOrderV2Po);
        if (affected <= 0) {
            throw new ContextedRuntimeException("保存扣减订单失败").addContextValue("deductId", refundDeductOrderV2Po.getDeductId());
        }
        affected = refundDebtV2Dao.decreaseDebtCoin(refundDeductOrderV2Po.getKugouId(), refundDeductOrderV2Po.getDeductCoin());
        if (affected <= 0) {
            throw new ContextedRuntimeException("更新欠费余额失败").addContextValue("deductId", refundDeductOrderV2Po.getDeductId());
        }
        return ModelUtils.from(refundDeductOrderV2Po, RefundDeductOrderV2Po.class);
    }

    @Transactional(transactionManager = "transactionManager_d_fanxing_recharge", propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean releaseDeductOrder(RefundDeductOrderV2Po refundDeductOrderV2Po) {
        int affected = refundDebtV2Dao.increaseDebtCoin(refundDeductOrderV2Po.getKugouId(), refundDeductOrderV2Po.getDeductCoin());
        if (affected <= 0) {
            throw new ContextedRuntimeException("扣掉已扣款金额失败").addContextValue("deductId", refundDeductOrderV2Po.getDeductId());
        }
        affected = refundDeductOrderV2Dao.updateStatus(refundDeductOrderV2Po.getDeductId(), 2);
        if (affected <= 0) {
            throw new ContextedRuntimeException("更新订单状态为已废弃失败").addContextValue("deductId", refundDeductOrderV2Po.getDeductId());
        }
        return true;
    }

    public boolean successDeductOrder(RefundDeductOrderV2Po refundDeductOrderV2Po) {
        int affected = refundDeductOrderV2Dao.updateStatus(refundDeductOrderV2Po.getDeductId(), 1);
        return affected > 0;
    }

    public boolean unknownDeductOrder(RefundDeductOrderV2Po refundDeductOrderV2Po) {
        int affected = refundDeductOrderV2Dao.updateStatus(refundDeductOrderV2Po.getDeductId(), -1);
        return affected > 0;
    }
}
