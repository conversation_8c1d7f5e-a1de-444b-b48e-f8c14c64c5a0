package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.thrift.plat.user.UserPlatBizService;
import com.kugou.fanxing.thrift.plat.user.vo.ResMsg;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Objects;
import java.util.Optional;

@Slf4j
public class GetUserInfoFromKugouV1Command extends HystrixCommand<Optional<String>> {

    private final UserPlatBizService.Iface userPlatBizService;
    private final long kugouId;

    public GetUserInfoFromKugouV1Command(final UserPlatBizService.Iface userPlatBizService, final long kugouId) {
        super(HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("GetUserInfoFromKugouV1Command"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(500)));
        this.userPlatBizService = userPlatBizService;
        this.kugouId = kugouId;
    }

    @Override
    protected Optional<String> run() throws Exception {
        ResMsg resMsg = userPlatBizService.getUserInfoFromKugouV1(kugouId);
        if (Objects.isNull(resMsg) || StringUtils.isBlank(resMsg.getResponseCode()) || !resMsg.getResponseCode().endsWith("000")) {
            log.warn("调用获取酷狗用户信息接口, 调用失败, kugouId: {}, response: {}", kugouId, resMsg);
            return Optional.empty();
        }
        log.warn("调用获取酷狗用户信息接口, 调用完成, kugouId: {}, response: {}", kugouId, resMsg);
        return Optional.of(resMsg.getData());
    }

    @Override
    protected Optional<String> getFallback() {
        Optional<String> fallback = Optional.empty();
        log.warn("GetUserInfoFromKugouV1Command 服务降级! 调用获取酷狗用户信息接口异常, kugouId: {}! 降级返回数据: {}, 降级原因: {}",
                kugouId, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }
}
