package com.kugou.fanxing.recharge.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.bo.PurchaseProductRequestBo;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.thrift.PurchaseOrder;
import com.kugou.fanxing.recharge.thrift.PurchaseProductRequestV2;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.fanxing.recharge.util.ModelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.ConstraintViolation;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class PurchaseProductService {

    @Autowired
    private PayService payService;
    @Autowired
    private RechargeConfig rechargeConfig;
    @Autowired
    private OrderIdService orderIdService;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private WechatRechargeService wechatRechargeService;
    @Autowired
    private AlipayRechargeService alipayRechargeService;
    @Autowired
    private RechargeCommonService rechargeCommonService;
    @Autowired
    private ValidatingService validatingService;

    /**
     * 支付宝：订单过期时间，格式：yyyyMMddHHmmss, 有效时间低于5分钟，默认重新设置成5分钟
     *   微信：订单过期时间，格式：yyyyMMddHHmmss，20091227091010，注意：过期时间 - now > 5分钟，否则该参数作废
     *
     * @param orderExpireTimeSec 过期时间（秒）
     * @return 支付网关过期时间字符串
     */
    private String formatExpireTime(long orderExpireTimeSec) {
        long orderExpireTimeMills = orderExpireTimeSec > 0 ?
                TimeUnit.SECONDS.toMillis(orderExpireTimeSec) : System.currentTimeMillis();
        return DateFormatUtils.format(orderExpireTimeMills, "yyyyMMddHHmmss");
    }

    public Map<String, Object> callJavaRechargeV2(PurchaseProductRequestV2 request) {
        long businessOrderId = request.getBusinessOrderId() > 0 ? request.getBusinessOrderId() : orderIdService.generateGlobalId();
        Map<String, String> kupaysParam = buildPurchaseProductsKupaysParamByRequest(request);
        Map<String, Object> extendParam = buildPurchaseProductsExtendParamByRequest(businessOrderId, request);
        RechargeAcrossPO rechargeAcrossPO = createPurchaseProductsOrderByRequest(businessOrderId, request, extendParam);
        checkPurchaseProductRequestV2(request, rechargeAcrossPO.getRechargeOrderNum());
        kupaysParam.put("expire_time", formatExpireTime(request.getOrderExpireTime()));
        if (StringUtils.isNotBlank(request.getSyncUrl())) {
            kupaysParam.put("sync_url", StringEscapeUtils.unescapeXml(StringUtils.defaultString(request.getSyncUrl())));
        }
        String extJson = StringUtils.defaultIfBlank(request.getExtJson(), "{}");
        int frontQrCode = JsonUtils.parseJsonPath(extJson, "$.frontQrCode", Integer.class, 0);
        Map<String, Object> payload;
        switch (request.getPayTypeId()) {
            case 3:
                payload = this.alipayRechargeService.purchaseProductsForQr(rechargeAcrossPO, kupaysParam, extendParam);
                break;
            case 39:
                if (frontQrCode < 1) {
                    kupaysParam.put("qr","2");
                    kupaysParam.put("size","5");
                    kupaysParam.put("margin","1");
                }
                payload = this.wechatRechargeService.purchaseProductsForQr(rechargeAcrossPO, kupaysParam, extendParam);
                break;
            case 32:
                kupaysParam.put("wxurl", "1");
                payload = this.wechatRechargeService.purchaseProductsForH5(rechargeAcrossPO, kupaysParam, extendParam);
                break;
            case 33:
                kupaysParam.put("show_url", StringUtils.defaultString(request.getShowUrl(), request.getSyncUrl()));
                payload = this.alipayRechargeService.purchaseProductsForH5(rechargeAcrossPO, kupaysParam, extendParam);
                break;
            default:
                log.warn("充值渠道配置不存在，下单失败。request: {}", request);
                throw new BizException(SysResultCode.E_10000018);
        }
        payload.put("rechargeOrderNum", rechargeAcrossPO.getRechargeOrderNum());
        payload.put("businessId", businessOrderId);
        payload.put("orderId", rechargeAcrossPO.getRechargeOrderNum());
        return payload;
    }

    private void checkPurchaseProductRequestV2(PurchaseProductRequestV2 request, String rechargeOrderNum) {
        // 参数校验
        Optional<PurchaseProductRequestBo> optionalPurchaseProductRequestBo = ModelUtils.from(request, PurchaseProductRequestBo.class);
        if (optionalPurchaseProductRequestBo.isPresent()) {
            PurchaseProductRequestBo purchaseProductRequestBo = optionalPurchaseProductRequestBo.get();
            Optional<ConstraintViolation<PurchaseProductRequestBo>> optionalConstraintViolation = this.validatingService.checkViolation(purchaseProductRequestBo);
            if (optionalConstraintViolation.isPresent()) {
                log.warn("调用PHP充扣下单接口V2，请求参数非法。request: {}, optionalConstraintViolation: {}, ", request, optionalConstraintViolation);
                throw new BizException(SysResultCode.RECHARGE_PARAM_ERROR);
            }
        }
        // 外部校验
        SysResultCode sysResultCode = this.rechargeCommonService.checkThirdPart(request.getPid(), request.getKugouId(),
                rechargeOrderNum, request.getPayTypeId(), new BigDecimal(request.getAmount()));
        if (!sysResultCode.isSuccess()) {
            log.warn("调用PHP充扣下单接口V2，处理外部检查拦截。request: {}, rechargeOrderNum: {}", request, rechargeOrderNum);
            throw new BizException(sysResultCode);
        }
    }

    private RechargeAcrossPO createPurchaseProductsOrderByRequest(long businessOrderId, PurchaseProductRequestV2 request, Map<String, Object> extendParam) {
        PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(request.getPayTypeId());
        int stdPlat = request.getPid();
        long kugouId = request.getKugouId();
        long rechargeKugouId = request.getKugouId();
        BigDecimal amount = new BigDecimal(request.getAmount());
        String clientIp = request.getClientIp();
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        //2024-1-22 从JSON中获取coinType 如果获取不到，则默认是星币的充扣
        CoinTypeEnum coinTypeEnum = CoinTypeEnum.getCoinTypeEnumByJson(request.getExtJson());
        RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(
                stdPlat, kugouId, rechargeKugouId, rechargeOrderNum, payTypeIdEnum, amount, clientIp, coinTypeEnum);
        rechargeAcrossPO.setReType(ReTypeEnum.RETYPE_PURCHASE.getReTypeId());
        rechargeAcrossPO.setBusinessId(StringUtils.defaultString(request.getBusinessId()));
        rechargeAcrossPO.setExtend(this.rechargeCommonService.buildExtendStr(rechargeAcrossPO, extendParam));
        rechargeAcrossPO.setExtraJsonData(JSON.toJSONString(ImmutableMap.of("businessOrderId", businessOrderId)));
        return rechargeAcrossPO;
    }

    private Map<String, String> buildPurchaseProductsKupaysParamByRequest(PurchaseProductRequestV2 request) {
        Map<String, String> kupaysParam = Maps.newHashMap();
        kupaysParam.put("subject", StringUtils.defaultString(request.getSubject()));
        kupaysParam.put("desc", StringUtils.defaultString(request.getSubject()));
        kupaysParam.put("notify_url", rechargeConfig.getNotifyUrl(ReTypeEnum.RETYPE_PURCHASE));
        return kupaysParam;
    }

    /**
     * 根据内网购买商品下单请求构建透传数据
     *
     * @param request 内网购买商品下单请求
     * @return 透传数据
     */
    private Map<String, Object> buildPurchaseProductsExtendParamByRequest(long businessOrderId, PurchaseProductRequestV2 request) {
        PurchaseOrder purchaseOrder = request.getOrderList().stream().findFirst()
                .orElseThrow(() -> new ContextedRuntimeException("充扣内网下单购买商品列表为空")
                        .addContextValue("businessId", request.getBusinessId())
                        .addContextValue("businessOrderId", request.getBusinessOrderId()));
        // 构建userFundPlatParam充扣参数
        Map<String, String> userFundPlatParam = Maps.newHashMap();
        userFundPlatParam.put("senderDepartmentId", "0");
        userFundPlatParam.put("senderProductId", "0");
        userFundPlatParam.put("senderMinorProductId", "0");
        userFundPlatParam.put("senderHardwarePlatform", "0");
        userFundPlatParam.put("senderChannelId", "0");
        userFundPlatParam.put("senderSubChannelId", "0");
        userFundPlatParam.put("receiverDepartmentId", "0");
        userFundPlatParam.put("fromKugouId", String.valueOf(purchaseOrder.getFromKugouId()));
        userFundPlatParam.put("toKugouId", String.valueOf(purchaseOrder.getToKugouId()));
        userFundPlatParam.put("fxcChangeDesc", String.format("用户kugouId:%s，%s", request.getKugouId(), request.getSubject()));
        userFundPlatParam.put("accountChangeType", String.valueOf(purchaseOrder.getAccountChangeType()));
        userFundPlatParam.put("actionId", this.payService.getActionIdByAccountChangeType(purchaseOrder.getAccountChangeType()));
        userFundPlatParam.put("roomId", String.valueOf(purchaseOrder.getRoomId()));
        userFundPlatParam.put("giftId", String.valueOf(purchaseOrder.getGoodsId()));
        userFundPlatParam.put("giftName", this.payService.getGiftName(request.getBusinessId(), purchaseOrder));
        userFundPlatParam.put("giftNum", String.valueOf(purchaseOrder.getGoodsNum()));
        userFundPlatParam.put("coin", new BigDecimal(request.getAmount()).multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString());
        userFundPlatParam.put("ext", purchaseOrder.getExt());
        userFundPlatParam.put("globalId", String.valueOf(businessOrderId));
        userFundPlatParam.put("pid", String.valueOf(request.getPid()));
        userFundPlatParam.put("ip", String.valueOf(request.getClientIp()));

        Map<String, Object> extendParam = Maps.newTreeMap();
        extendParam.put("businessId", businessOrderId);
        extendParam.put("businessType", request.getBusinessId());
        extendParam.put("businessTime", String.valueOf(request.getBusinessTime()));
        extendParam.put("fromKugouId", String.valueOf(request.getKugouId()));
        extendParam.put("toKugouId", String.valueOf(purchaseOrder.getToKugouId()));
        extendParam.put("addTime", String.valueOf(DateHelper.getCurrentSeconds()));
        extendParam.put("subject", StringUtils.defaultString(request.getSubject()));
        extendParam.put("userFundPlatParam", userFundPlatParam);
        extendParam.put("topic", apolloConfigService.businessNotifyTopicOf(request.getBusinessId()));
        extendParam.put("businessExt", this.payService.buildReqExt(request));
        extendParam.put("roomId", String.valueOf(purchaseOrder.getRoomId()));
        extendParam.put("clientIp", String.valueOf(request.getClientIp()));
        extendParam.put("reType", ReTypeEnum.RETYPE_PURCHASE.getReTypeId());
        extendParam.put("coinType", String.valueOf(CoinTypeEnum.getCoinTypeEnumByJson(request.getExtJson()).getCoinType()));
        // 微信公众号支付
        if (StringUtils.isNoneBlank(request.getOpenId())) {
            extendParam.put("openId", request.getOpenId());
        }
        if(request.getOrderExpireTime() > DateHelper.getCurrentSeconds()){
            extendParam.put("orderExpireTime", String.valueOf(request.getOrderExpireTime()));
        }
        return extendParam;
    }
}
