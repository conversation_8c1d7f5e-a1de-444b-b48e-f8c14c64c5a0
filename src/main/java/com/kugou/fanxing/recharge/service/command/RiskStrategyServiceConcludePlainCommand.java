package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.risk.sdk.RiskStrategyService;
import com.kugou.fanxing.risk.sdk.model.RiskResult;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Map;
import java.util.Optional;

@Slf4j
public class RiskStrategyServiceConcludePlainCommand extends HystrixCommand<Optional<RiskResult>> {

    private final RiskStrategyService riskStrategyService;
    private final RiskRequestDto riskRequestDto;

    public RiskStrategyServiceConcludePlainCommand(final RiskStrategyService riskStrategyService, final RiskRequestDto riskRequestDto) {
        super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("RiskStrategyServiceConcludePlainCommand")).
                andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(500)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(30))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(500)));
        this.riskRequestDto = riskRequestDto;
        this.riskStrategyService = riskStrategyService;
    }

    @Override
    protected Optional<RiskResult> run() throws Exception {
        return Optional.ofNullable(riskStrategyService.conclude(riskRequestDto.getAppid(), riskRequestDto.getBiz(),
                riskRequestDto.getEndtype(), riskRequestDto.getSid(), riskRequestDto.getKugouId(), riskRequestDto.getIp(),
                riskRequestDto.getDeviceId(), riskRequestDto.getPlatform(), riskRequestDto.getRoomId(), riskRequestDto.getClientver(),
                riskRequestDto.getClienttype(), riskRequestDto.getTs(), riskRequestDto.getData()));
    }

    @Override
    protected Optional<RiskResult> getFallback() {
        Optional<RiskResult> fallback = Optional.empty();
        log.warn("RiskStrategyServiceConcludePlainCommand服务降级! 风控服务校验异常, riskRequestDto: {}, 降级返回数据: {}, 降级原因: {}",
                riskRequestDto, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskRequestDto {
        private String appid;
        private String biz;
        private String endtype;
        private String sid;
        private long kugouId;
        private String ip;
        private String deviceId;
        private int platform;
        private long roomId;
        private int clientver;
        private int clienttype;
        private long ts;
        private Map<String, Object> data;
    }
}

