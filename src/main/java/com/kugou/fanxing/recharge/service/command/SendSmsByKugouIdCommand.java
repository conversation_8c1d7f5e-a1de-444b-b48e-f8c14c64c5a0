package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.thrift.plat.user.UserPlatBizService;
import com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq;
import com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Objects;

@Slf4j
public class SendSmsByKugouIdCommand extends HystrixCommand<Boolean> {

    private final UserPlatBizService.Iface userPlatBizService;
    private final SendSmsByKugouIdReq request;

    public SendSmsByKugouIdCommand(final UserPlatBizService.Iface userPlatBizService, final SendSmsByKugouIdReq request) {
        super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("SendSmsByKugouIdCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(500)));
        this.userPlatBizService = userPlatBizService;
        this.request = request;
    }

    @Override
    protected Boolean run() throws Exception {
        SendSmsByKugouIdResp response = userPlatBizService.sendSmsByKugouId(request);
        if (Objects.isNull(response) || response.getCode() != 0) {
            log.warn("调用发送短信接口, 调用失败。request: {}, response: {}", request, response);
            return false;
        }
        log.warn("调用发送短信接口, 调用完成。request: {}, response: {}", request, response);
        return true;
    }

    @Override
    protected Boolean getFallback() {
        boolean fallback = false;
        log.warn("SendSmsByKugouIdCommand 服务降级! 调用发送短信接口异常, request: {}! 降级返回数据: {}, 降级原因: {}",
                request, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }
}
