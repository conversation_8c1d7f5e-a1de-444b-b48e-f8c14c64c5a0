package com.kugou.fanxing.recharge.service.command;

import com.kugou.fanxing.thrift.plat.user.UserPlatBizService;
import com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Objects;

/**
 * 检查用户的注销状态
 * true就是注销，false就是正常使用
 *
 * <AUTHOR>
 */
@Slf4j
public class IsUserCancelCommand extends HystrixCommand<Boolean> {

    private final UserPlatBizService.Iface userPlatBizService;
    private final long kugouId;

    public IsUserCancelCommand(final UserPlatBizService.Iface userPlatBizService, final long kugouId) {
        super(HystrixCommand.Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("IsUserCancelCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(200)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(10))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(500)));
        this.userPlatBizService = userPlatBizService;
        this.kugouId = kugouId;
    }

    @Override
    protected Boolean run() throws Exception {
        ResUserCancelStatusResponse resRsp = userPlatBizService.getUserCancelStatus(kugouId);
        if (Objects.nonNull(resRsp) && resRsp.getCode() == 0 && !resRsp.isData()) {
            log.warn("调用用户的注销状态接口, 调用成功且账号是注销状态, kugouId: {}, resRsp: {}", kugouId, resRsp);
            return Boolean.TRUE;
        }
        log.warn("调用用户的注销状态接口, 调用完成, kugouId: {}, resRsp: {}", kugouId, resRsp);
        return Boolean.FALSE;
    }

    @Override
    protected Boolean getFallback() {
        Boolean fallback = Boolean.FALSE;
        log.warn("IsUserCancelCommand 服务降级! 调用用户的注销状态接口异常, kugouId: {}! 降级返回数据: {}, 降级原因: {}",
                kugouId, fallback, ExceptionUtils.getStackTrace(getExecutionException()));
        return fallback;
    }
}
