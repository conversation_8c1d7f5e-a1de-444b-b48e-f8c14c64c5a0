package com.kugou.fanxing.recharge.service.command;

import com.kugou.api.springcloud.GlobalIdServiceThrift.GlobalIdService;
import com.kugou.fanxing.biz.commons.util.SnowFlake;
import com.kugou.fanxing.recharge.util.IpUtils;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;


/**
 * 获取GlobalId
 *
 * <AUTHOR>
 */
@Slf4j
public class GlobalIdServiceCommand extends HystrixCommand<Long> {

    private static final long DATACENTER_BIT = 5;
    private static final long MACHINE_BIT = 5;
    private static final long MAX_DATACENTER_NUM = ~(-1L << DATACENTER_BIT);
    private static final long MAX_MACHINE_NUM = ~(-1L << MACHINE_BIT);

    private final GlobalIdService.Iface globalIdService;
    private final SnowFlake snowFlake;

    public GlobalIdServiceCommand(final GlobalIdService.Iface globalIdService) {
        super(Setter.withGroupKey(HystrixCommandGroupKey.Factory.asKey("GlobalIdServiceCommand"))
                .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                        .withMaxQueueSize(500)
                        .withQueueSizeRejectionThreshold(15)
                        .withCoreSize(30))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                        .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                        .withExecutionTimeoutInMilliseconds(300)));
        this.globalIdService = globalIdService;
        long dataCenterId = Math.max(Math.min(RandomUtils.nextLong(), MAX_DATACENTER_NUM), 0);
        long machineId = Math.max(Math.min(IpUtils.ipToLong(IpUtils.getServerIpAddress()) % 31, MAX_MACHINE_NUM), 0);
        snowFlake = new SnowFlake(dataCenterId, machineId);
    }

    @Override
    protected Long run() throws Exception {
        return globalIdService.get();
    }

    @Override
    protected Long getFallback() {
        long globalId = snowFlake.nextId();
        log.warn("GlobalIdServiceCommand 服务降级! 降级返回数据: {}, 降级原因: {}",
                globalId, ExceptionUtils.getStackTrace(getExecutionException()));
        return globalId;
    }
}

