package com.kugou.fanxing.recharge.service.recharge;

import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.PddOrderCheckResult;
import com.kugou.fanxing.recharge.model.bo.PddProductBo;
import com.kugou.fanxing.recharge.model.dto.CoinCallbackDTO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.PddCreateOrderCheckRequest;
import com.kugou.fanxing.recharge.service.RemoteStrategyService;
import com.kugou.fanxing.recharge.service.callback.AbstractCallbackService;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.fanxing.recharge.util.ModelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.ConstraintViolation;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.UnaryOperator;

@Slf4j
@Service
public class PddRechargeService extends AbstractCallbackService {

    @Autowired
    private RemoteStrategyService remoteStrategyService;

    public JsonResult<PddOrderCheckResult> createOrderCheck(PddCreateOrderCheckRequest request) {
        PddOrderCheckResult pddOrderCheckResult = PddOrderCheckResult.builder().permitRecharge(0).build();
        // 请求参数校验
        Optional<ConstraintViolation<PddCreateOrderCheckRequest>> optionalViolation = validatingService.checkViolation(request);
        if (optionalViolation.isPresent()) {
            log.warn("拼多多充值下单校验，参数非法。request: {}, optionalViolation: {}", request, optionalViolation);
            return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, pddOrderCheckResult);
        }
        // 业务填入为繁星ID
        long fanxingId = request.getUserid();
        Optional<Long> optionalKugouId = this.userFacadeService.getKugouIdByUserId(fanxingId, false);
        if (!optionalKugouId.isPresent() || optionalKugouId.get() < 1L) {
            log.error("拼多多充值下单校验，通过繁星id获取酷狗id失败。request: {}, fanxingId: {} ", request, fanxingId);
            return JsonResult.result(SysResultCode.E_100042, pddOrderCheckResult);
        }
        // 校验是否封禁、注销账户
        long kugouId = optionalKugouId.get();
        if(userFacadeService.isBannedAccount(kugouId) || userFacadeService.isRevokedAccount(kugouId)){
            log.warn("拼多多充值下单校验，封禁注销账号禁止平多多充值。request: {}, kugouId: {}", request, kugouId);
            return JsonResult.result(SysResultCode.RECHARGE_NOT_FANXING, pddOrderCheckResult);
        }
        // 校验是否海外账户
        if (userFacadeService.isOverseasRegisterUser(kugouId)) {
            log.warn("拼多多充值下单校验，海外注册用户禁止平多多充值。request: {}, kugouId: {}", request, kugouId);
            return JsonResult.result(SysResultCode.E_100049, pddOrderCheckResult);
        }
        // 检查用户风险行为
        Map<String, String> params = Maps.newHashMap();
        params.put("action", "createOrderCheck");
        params.put("prodNo", request.getProdNo());
        boolean blockByRisk = remoteStrategyService.strategyVerifyForX(PayTypeIdEnum.PAY_TYPE_PDD, kugouId, params);
        if (blockByRisk) {
            log.warn("拼多多充值下单校验，风控拦截阻断充值。request: {}, kugouId: {}", request, kugouId);
            return JsonResult.result(SysResultCode.RECHARGE_RISK_STRATEGY, pddOrderCheckResult);
        }
        // 检查SKU是否配置
        Optional<PddProductBo> optionalPddProductBo = this.getPddProductByProdNo(request.getProdNo());
        if (!optionalPddProductBo.isPresent()) {
            log.error("拼多多充值下单校验，充值商品价格缺失。request: {}, kugouId: {}", request, kugouId);
            return JsonResult.result(SysResultCode.PRODUCT_PRICE_MISSING, pddOrderCheckResult);
        }
        pddOrderCheckResult.setPermitRecharge(1);
        log.warn("拼多多充值下单校验，充值下单校验通过。request: {}, kugouId: {}, createOrderCheckResult: {}",
                request, kugouId, pddOrderCheckResult);
        return JsonResult.result(SysResultCode.SUCCESS, pddOrderCheckResult);
    }

    public Optional<PddProductBo> getPddProductByProdNo(String prodNo) {
        List<PddProductBo> pddProductBoList = this.apolloConfigService.getPddProductPriceList();
        return pddProductBoList.stream()
                .filter(pddProductBo -> pddProductBo.getProdNo().equals(prodNo))
                .filter(pddProductBo -> Objects.nonNull(pddProductBo.getProdCoin()))
                .filter(pddProductBo -> pddProductBo.getProdCoin().compareTo(BigDecimal.ZERO) > 0)
                .findFirst();
    }

    public JsonResult<Map<String, String>> callbackNotify(CoinCallbackDTO coinCallbackDTO) {
        // 校验参数签名
        boolean isValidSign = checkSignOfPcCallBack(coinCallbackDTO.getAppid(), coinCallbackDTO.getSign());
        if (!isValidSign) {
            log.warn("拼多多充值回调发货，参数签名错误。coinCallbackDTO: {}", coinCallbackDTO);
            return JsonResult.result(SysResultCode.E_30000009, Maps.newHashMap());
        }
        // 校验请求参数
        Optional<ConstraintViolation<CoinCallbackDTO>> optionalViolation = this.validatingService.checkViolation(coinCallbackDTO);
        if (optionalViolation.isPresent()) {
            log.warn("拼多多充值回调发货，请求参数非法。coinCallbackDTO: {}, optionalViolation: {}, ", coinCallbackDTO, optionalViolation);
            throw new BizException(SysResultCode.RECHARGE_PARAM_ERROR);
        }
        // 回查网关校验
        boolean isValidNotify = this.checkCallbackNotify(coinCallbackDTO);
        if (!isValidNotify) {
            log.warn("拼多多充值回调发货，回查网关notifyId。coinCallbackDTO: {}", coinCallbackDTO);
            return JsonResult.result(SysResultCode.E_30000010, Maps.newHashMap());
        }
        // 校验繁星账号
        long fanxingId = coinCallbackDTO.getUserid();
        Optional<Long> optionalKugouId = this.userFacadeService.getKugouIdByUserId(fanxingId, false);
        if (!optionalKugouId.isPresent() || optionalKugouId.get() < 1L) {
            log.error("拼多多充值回调发货，通过繁星id获取酷狗id失败。coinCallbackDTO: {}", coinCallbackDTO);
            return JsonResult.result(SysResultCode.E_100042, Maps.newHashMap());
        }
        long kugouId = optionalKugouId.get();
        // 根据第三方交易号映射唯一直播充值交易号
        Optional<String> optionalRechargeOrderNum = this.rechargeOrderService.generateRechargeOrderNumByTradeNo(PayTypeIdEnum.PAY_TYPE_PDD, coinCallbackDTO.getOrder_no());
        if (!optionalRechargeOrderNum.isPresent()) {
            log.warn("拼多多充值回调发货，直播充值订单号获取失败。coinCallbackDTO: {}", coinCallbackDTO);
            return JsonResult.result(SysResultCode.E_30000012, Maps.newHashMap());
        }
        String rechargeOrderNum = optionalRechargeOrderNum.get();
        // 解析商品信息
        String prodNo = JsonUtils.parseJsonPath(coinCallbackDTO.getExtend(), "$.prodNo", String.class, "");
        Optional<PddProductBo> optionalPddProductBo = this.getPddProductByProdNo(prodNo);
        if (!optionalPddProductBo.isPresent()) {
            log.warn("拼多多充值回调发货，交易SKU定价配置缺失。coinCallbackDTO: {}, prodNo: {}", coinCallbackDTO, prodNo);
            return JsonResult.result(SysResultCode.E_30000008, Maps.newHashMap());
        }
        PddProductBo pddProductBo = optionalPddProductBo.get();
        // 解析购买数量
        Optional<Integer> optionalAmount = JsonUtils.parseJsonPath(coinCallbackDTO.getExtend(), "$.amount", Integer.class);
        if (!optionalAmount.isPresent() || optionalAmount.get() < 1) {
            log.warn("拼多多充值回调发货，交易SKU购买件数缺失。coinCallbackDTO: {}", coinCallbackDTO);
            return JsonResult.result(SysResultCode.E_30000004, Maps.newHashMap());
        }
        // 检查发放星币价值
        BigDecimal totalCent = coinCallbackDTO.getTotal_fee().multiply(BigDecimal.valueOf(100));
        BigDecimal totalCoin = pddProductBo.getProdCoin().multiply(BigDecimal.valueOf(optionalAmount.get()));
        if (totalCent.compareTo(totalCoin) < 0) {
            log.warn("拼多多充值回调校验，发放星币价值大于充值金额。coinCallbackDTO: {}, totalCent: {}, totalCoin: {}", coinCallbackDTO, totalCent, totalCoin);
            return JsonResult.result(SysResultCode.FAILURE, Maps.newHashMap());
        }
        // 读取下单记录
        Optional<RechargeAcrossPO> optionalRechargeAcrossPO = this.rechargeOrderService.queryByRechargeOrderNum(rechargeOrderNum);
        if (!optionalRechargeAcrossPO.isPresent()) {
            log.warn("拼多多充值回调发货，模拟下单。coinCallbackDTO: {}", coinCallbackDTO);
            RechargeAcrossPO rechargeAcrossPO = mockCreateOrder(PayTypeIdEnum.PAY_TYPE_PDD, kugouId, rechargeOrderNum,
                    coinCallbackDTO.getOut_trade_no(), coinCallbackDTO.getTotal_fee());
            rechargeAcrossPO.setCoin(totalCoin);
            int affected = this.rechargeOrderService.addRechargeOrder(rechargeAcrossPO);
            if (affected < 1) {
                log.warn("拼多多充值回调发货，保存下单失败。coinCallbackDTO: {}", coinCallbackDTO);
                return JsonResult.result(SysResultCode.RECHARGE_SYS_ERROR, Maps.newHashMap());
            }
            optionalRechargeAcrossPO = Optional.of(rechargeAcrossPO);
        }
        // 检查交易状态
        RechargeAcrossPO sourceOrder = optionalRechargeAcrossPO.get();
        if (sourceOrder.getStatus() == 1) {
            log.warn("拼多多充值回调发货，订单已经处理过。coinCallbackDTO: {}", coinCallbackDTO);
            return JsonResult.result(SysResultCode.SUCCESS, Maps.newHashMap());
        }
        // 检查充值账户
        if (sourceOrder.getKugouId() != kugouId) {
            log.warn("拼多多充值回调发货，充值下单与回调酷狗ID不一致。coinCallbackDTO: {}", coinCallbackDTO);
            return JsonResult.result(SysResultCode.E_30000006, Maps.newHashMap());
        }
        // 检查用户风险行为
        Map<String, String> params = Maps.newHashMap();
        params.put("action", "callbackNotify");
        params.put("orderNo", rechargeOrderNum);
        params.put("prodNo", prodNo);
        params.put("amount", String.valueOf(optionalAmount.get()));
        boolean blockByRisk = remoteStrategyService.strategyVerifyForX(PayTypeIdEnum.PAY_TYPE_PDD, kugouId, params);
        if (blockByRisk) {
            log.warn("拼多多充值回调校验，风控拦截阻断充值。coinCallbackDTO: {}", coinCallbackDTO);
            return JsonResult.result(SysResultCode.RECHARGE_RISK_STRATEGY, Maps.newHashMap());
        }
        // 设置到账记录
        UnaryOperator<RechargeAcrossPO> map2TargetOrder = source -> {
            // 充值金额，单位：元
            BigDecimal totalFee = coinCallbackDTO.getTotal_fee();
            // 充值金额，单位：分
            BigDecimal realAmount = totalFee.multiply(BigDecimal.valueOf(100));
            // 星币价值：单价*购买件数
            BigDecimal coin = pddProductBo.getProdCoin().multiply(BigDecimal.valueOf(optionalAmount.get()));
            RechargeAcrossPO target = ModelUtils.fromUnchecked(source, RechargeAcrossPO.class);
            target.setCoin(coin);
            target.setRealAmount(realAmount);
            target.setStatus(1);
            target.setRechargeTime(DateHelper.getCurrentSeconds());
            target.setReType(ReTypeEnum.RETYPE_RECHARGE.getReTypeId());
            target.setRechargeId(orderIdService.generateGlobalId());
            target.setTradeNo(StringUtils.defaultString(coinCallbackDTO.getTrade_no()));
            target.setTradeTime(super.parseTradeTime(coinCallbackDTO.getTrade_time()));
            target.setPartner(StringUtils.defaultString(coinCallbackDTO.getPartner()));
            target.setExtend(rechargeCommonService.buildExtendStr(target, Maps.newHashMap()));
            target.setExtraJsonData(StringUtils.defaultString(coinCallbackDTO.getExtend()));
            target.setProductId(StringUtils.defaultString(prodNo));
            return target;
        };
        if (!super.makeDeal(sourceOrder, map2TargetOrder, target -> consumeRpcService.rechargeCoin(target))) {
            log.error("拼多多充值回调发货，充值调用消费加星币失败。payTypeIdEnum: {}, kugouId: {}, coinCallbackDTO: {}", PayTypeIdEnum.PAY_TYPE_PDD, kugouId, coinCallbackDTO);
            return JsonResult.result(SysResultCode.E_30000013, Maps.newHashMap());
        }
        log.warn("拼多多充值回调发货，处理成功。payTypeIdEnum: {}, kugouId: {}, coinCallbackDTO: {}", PayTypeIdEnum.PAY_TYPE_PDD, kugouId, coinCallbackDTO);
        return JsonResult.result(SysResultCode.SUCCESS, Maps.newHashMap());
    }

}
