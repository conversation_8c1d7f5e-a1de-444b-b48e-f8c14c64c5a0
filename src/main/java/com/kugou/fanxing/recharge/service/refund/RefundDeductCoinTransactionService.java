package com.kugou.fanxing.recharge.service.refund;

import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundDeductCoinOrderDao;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundOrderDao;
import com.kugou.fanxing.recharge.model.po.refund.RefundDeductCoinOrderPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class RefundDeductCoinTransactionService {
    @Autowired
    private RefundOrderDao refundOrderDao;

    @Autowired
    private RefundDeductCoinOrderDao refundDeductCoinOrderDao;

    /**
     * 更新订单状态为失败，且回退已经扣币金额
     */
    @Transactional(transactionManager = "transactionManager_d_fanxing_recharge", propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean deductCoinFail(RefundDeductCoinOrderPO po) {
        int rs = refundOrderDao.decrDeductCoinAmount(po.getRechargeOrderNum(), po.getDeductCoin());
        if (rs <= 0) {
            throw new ContextedRuntimeException("扣掉已扣款金额失败,rollback,orderId:" + po.getId());
        }
        int updateRs = refundDeductCoinOrderDao.updateByPrimay(po.getId(), 2);
        if (updateRs <= 0) {
            throw new ContextedRuntimeException("更新订单状态为已废弃失败，rollback，orderId：" + po.getId());
        }
        return true;
    }

    /**
     * 增加已经扣币完成的金额，并且下扣币订单
     */
    @Transactional(transactionManager = "transactionManager_d_fanxing_recharge", propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean createDeductCoinOrder(RefundDeductCoinOrderPO po) {
        int saveRs = refundDeductCoinOrderDao.saveRecord(po);
        if (saveRs <= 0) {
            throw new ContextedRuntimeException("插入订单失败，rollback，orderId：" + po.getId());
        }
        int rs = refundOrderDao.incrDeductCoinAmount(po.getRechargeOrderNum(), po.getDeductCoin());
        if (rs <= 0) {
            throw new ContextedRuntimeException("增加已扣款金额失败,rollback,orderId:" + po.getId());
        }
        return true;
    }


}
