package com.kugou.fanxing.recharge.service.common;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.kugou.fanxing.thrift.service.sensitive.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * 加密解密服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DencryptService {

    private static final String APPLICATION_NAME = "营收充值服务";
    /**
     * 加解密类型（注意：加解密类型必须相同，加解密服务测试环境不会检查，生产环境才会检查）
     * <p>
     * 内容类型; 1：手机号码,2:银行卡号码;3:身份证号码;4:设备号;5:照片、如身份证
     */
    private static final int TYPE = 1;

    @Autowired
    private SensitiveService.Iface sensitiveService;

    private LoadingCache<String, Optional<EncryptResponseDTO>> encryptedDataMap = CacheBuilder.newBuilder()
            .maximumSize(20000)
            .refreshAfterWrite(60, TimeUnit.SECONDS)
            .build(new CacheLoader<String, Optional<EncryptResponseDTO>>() {
                @Override
                public Optional<EncryptResponseDTO> load(String content) throws Exception {
                    log.warn("load from remote, content: {}", content);
                    return encrypt0(content);
                }
            });

    private LoadingCache<String, Optional<String>> decryptedDataMap = CacheBuilder.newBuilder()
            .maximumSize(20000)
            .refreshAfterWrite(60, TimeUnit.SECONDS)
            .build(new CacheLoader<String, Optional<String>>() {
                @Override
                public Optional<String> load(String encrypted) throws Exception {
                    return decrypt0(encrypted);
                }
            });

    public String encrypt(String content) {
        try {
            if (StringUtils.isBlank(content)) {
                return content;
            }
            return encryptedDataMap.get(content)
                    .orElseThrow(() -> new ContextedRuntimeException("数据加密失败").addContextValue("content", content))
                    .getValue();
        } catch (ExecutionException e) {
            throw new ContextedRuntimeException("数据加密失败", e).addContextValue("content", content);
        }
    }

    public String fingerprint(String content) {
        try {
            if (StringUtils.isBlank(content)) {
                return content;
            }
            return encryptedDataMap.get(content)
                    .orElseThrow(() -> new ContextedRuntimeException("数据加密失败").addContextValue("content", content))
                    .getFingerprint();
        } catch (ExecutionException e) {
            throw new ContextedRuntimeException("数据加密失败", e).addContextValue("content", content);
        }
    }

    public String decrypt(String encrypted) {
        if (StringUtils.isBlank(encrypted)) {
            return encrypted;
        }
        try {
            return decryptedDataMap.get(encrypted).orElseThrow(() -> new ContextedRuntimeException("数据解密失败").addContextValue("encrypted", encrypted));
        } catch (ExecutionException e) {
            throw new ContextedRuntimeException("数据解密失败", e).addContextValue("encrypted", encrypted);
        }
    }

    private Optional<EncryptResponseDTO> encrypt0(String content) throws TException {
        SensitiveReqParam request = new SensitiveReqParam()
                .setApplicationName(APPLICATION_NAME)
                .setType(TYPE)
                .setContent(content)
                .setReason("业务需要")
                .setWhat("加密数据")
                .setOperatorKugouId(0L);
        SensitiveEncryptResponse response = sensitiveService.encrypt(request);
        if (Objects.isNull(response) || response.getRet() != 0 || Objects.isNull(response.getData())) {
            log.error("接口sensitiveService.encrypt0调用失败, request: {}, response: {}", request, response);
            throw new ContextedRuntimeException("加密失败")
                    .addContextValue("type", TYPE)
                    .addContextValue("content", content);
        }
        EncryptResponseDTO encryptResponseDTO = response.getData();
        return Optional.of(encryptResponseDTO);
    }

    private Optional<String> decrypt0(String content) throws TException {
        SensitiveReqParam request = new SensitiveReqParam()
                .setApplicationName(APPLICATION_NAME)
                .setType(TYPE)
                .setContent(content)
                .setReason("业务需要")
                .setWhat("解密数据")
                .setOperatorKugouId(0L);
        SensitiveDecryptResponse response = sensitiveService.decrypt(request);
        if (Objects.isNull(response) || response.getRet() != 0 || Objects.isNull(response.getData())) {
            log.error("接口sensitiveService.decrypt0调用失败, request: {}, response: {}", request, response);
            throw new ContextedRuntimeException("解密失败")
                    .addContextValue("type", TYPE)
                    .addContextValue("content", content);
        }
        return Optional.of(response.getData());
    }
}
