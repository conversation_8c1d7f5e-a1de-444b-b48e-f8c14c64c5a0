package com.kugou.fanxing.recharge.exception;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    @Autowired
    private HttpServletRequest request;

    @ExceptionHandler(Exception.class)
    public void errorHandler(Exception e) {
        String requestURI = StringUtils.defaultString(request.getRequestURI()).trim();
        log.error("捕获系统全局异常, requestURI: {}, errorMessage: {}", requestURI, ExceptionUtils.getMessage(e), e);
        Cat.logError(ExceptionUtils.getMessage(e), e);
        // 兼容旧版PHP接口协议（App客户端历史版本）
        if (RechargePlatHelper.isHistoryProtocol(requestURI)) {
            handleExceptionWithOldStruct(e);
            return;
        }
        if (RechargePlatHelper.isKugouOpenProtocol(requestURI)) {
            handleExceptionWithKugouOpenProtocol(e);
            return;
        }
        handleException(e);
    }

    private void handleExceptionWithKugouOpenProtocol(Exception e) {
        if (e instanceof BizException) {
            int code = Math.max(((BizException) e).getCode(), SysResultCode.RECHARGE_SYS_ERROR.getCode());
            ResponseUtils.writeJson(JSON.toJSONString(KugouOpenResult.result(SysResultCode.codeOf(code), Maps.newHashMap())));
            return;
        }
        HttpServletResponse response = SpringContextUtils.getHttpServletResponse();
        response.setStatus(516);
        ResponseUtils.writeJson(JSON.toJSONString(KugouOpenResult.result(SysResultCode.RECHARGE_SYS_ERROR, Maps.newHashMap())));
    }

    /**
     * 旧版PHP数据结构
     */
    private void handleExceptionWithOldStruct(Exception e) {
        if (e instanceof BizException) {
            int code = Math.max(((BizException) e).getCode(), SysResultCode.RECHARGE_SYS_ERROR.getCode());
            ResponseUtils.writeJson(JSON.toJSONString(RechargePlatHelper.buildResp(SysResultCode.codeOf(code))));
            return;
        }
        if (e instanceof AckException) {
            HttpServletResponse response = SpringContextUtils.getHttpServletResponse();
            response.setStatus(516);
            int code = Math.max(((AckException) e).getCode(), SysResultCode.RECHARGE_SYS_ERROR.getCode());
            ResponseUtils.writeJson(JSON.toJSONString(RechargePlatHelper.buildResp(SysResultCode.codeOf(code))));
            return;
        }
        if (e instanceof BindException || e instanceof HttpMessageConversionException) {
            ResponseUtils.writeJson(JSON.toJSONString(RechargePlatHelper.buildResp(SysResultCode.RECHARGE_PARAM_ERROR)));
            return;
        }
        HttpServletResponse response = SpringContextUtils.getHttpServletResponse();
        response.setStatus(516);
        ResponseUtils.writeJson(JSON.toJSONString(RechargePlatHelper.buildResp(SysResultCode.RECHARGE_SYS_ERROR)));
    }

    /**
     * 新版JAVA数据结构
     */
    private void handleException(Exception e) {
        if (e instanceof BizException) {
            int code = Math.max(((BizException) e).getCode(), SysResultCode.RECHARGE_SYS_ERROR.getCode());
            JsonResult<Object> jsonResult = JsonResult.result(SysResultCode.codeOf(code), Maps.newHashMap());
            ResponseUtils.writeJson(JSON.toJSONString(jsonResult));
            return;
        }
        if (e instanceof KupayException) {
            KupayException kupayException = (KupayException) e;
            int code = Math.max(kupayException.getCode(), SysResultCode.RECHARGE_SYS_ERROR.getCode());
            String message = kupayException.getMessage();
            SysResultCode sysResultCode = SysResultCode.codeOf(code);
            JsonResult<Object> jsonResult = JsonResult.result(sysResultCode, message, Maps.newHashMap());
            ResponseUtils.writeJson(JSON.toJSONString(jsonResult));
            return;
        }
        if (e instanceof AckException) {
            int code = Math.max(((AckException) e).getCode(), SysResultCode.RECHARGE_SYS_ERROR.getCode());
            HttpServletResponse response = SpringContextUtils.getHttpServletResponse();
            response.setStatus(516);
            JsonResult<Object> jsonResult = JsonResult.result(SysResultCode.codeOf(code), Maps.newHashMap());
            ResponseUtils.writeJson(JSON.toJSONString(jsonResult));
            return;
        }
        if (e instanceof BindException || e instanceof HttpMessageConversionException || e instanceof HttpRequestMethodNotSupportedException) {
            JsonResult<Object> jsonResult = JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, Maps.newHashMap());
            ResponseUtils.writeJson(JSON.toJSONString(jsonResult));
            return;
        }
        HttpServletResponse response = SpringContextUtils.getHttpServletResponse();
        response.setStatus(516);
        JsonResult<Object> jsonResult = JsonResult.result(SysResultCode.RECHARGE_SYS_ERROR, Maps.newHashMap());
        ResponseUtils.writeJson(JSON.toJSONString(jsonResult));
    }

}
