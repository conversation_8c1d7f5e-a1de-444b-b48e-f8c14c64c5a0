package com.kugou.fanxing.recharge.exception;

import com.alibaba.fastjson.JSON;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.util.JsonResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.web.AbstractErrorController;
import org.springframework.boot.autoconfigure.web.ErrorAttributes;
import org.springframework.boot.autoconfigure.web.ErrorViewResolver;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("${server.error.path:${error.path:/error}}")
public class GlobalErrorController extends AbstractErrorController {

    @Value("${server.error.path:${error.path:/error}}")
    private String errorPath;

    public GlobalErrorController(ErrorAttributes errorAttributes, List<ErrorViewResolver> errorViewResolvers) {
        super(errorAttributes, errorViewResolvers);
    }

    @RequestMapping(produces = "text/html")
    @ResponseBody
    public String errorHtml(HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> body = Collections.unmodifiableMap(getErrorAttributes(request, false));
        HttpStatus status = getStatus(request);
        response.setStatus(status.value());
        return JSON.toJSONString(JsonResult.result(SysResultCode.RECHARGE_SYS_ERROR, status.getReasonPhrase(), body));
    }

    @RequestMapping
    @ResponseBody
    public JsonResult<Map<String, Object>> error(HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> body = Collections.unmodifiableMap(getErrorAttributes(request, false));
        HttpStatus status = getStatus(request);
        response.setStatus(status.value());
        return JsonResult.result(SysResultCode.RECHARGE_SYS_ERROR, status.getReasonPhrase(), body);
    }

    @Override
    public String getErrorPath() {
        return this.errorPath;
    }
}
