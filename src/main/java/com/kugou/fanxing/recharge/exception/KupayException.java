package com.kugou.fanxing.recharge.exception;

import com.kugou.fanxing.recharge.constant.SysResultCode;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;

/**
 * 业务异常
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class KupayException extends ContextedRuntimeException {
    /**
     * 异常代码
     */
    private final int code;

    /**
     * 异常消息
     */
    private final String message;

    public KupayException(SysResultCode sysResultCode) {
        this.code = sysResultCode.getCode();
        this.message = sysResultCode.getMsg();
    }

    public KupayException(SysResultCode sysResultCode, String message) {
        this.code = sysResultCode.getCode();
        this.message = StringUtils.defaultString(message);
    }

    public KupayException(SysResultCode sysResultCode, Throwable cause) {
        super(cause);
        this.code = sysResultCode.getCode();
        this.message = sysResultCode.getMsg();
    }

    public KupayException(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public KupayException(int code, String message, Throwable cause) {
        super(cause);
        this.code = code;
        this.message = message;
    }

}
