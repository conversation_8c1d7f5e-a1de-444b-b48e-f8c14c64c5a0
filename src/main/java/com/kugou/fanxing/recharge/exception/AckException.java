package com.kugou.fanxing.recharge.exception;

import com.kugou.fanxing.recharge.constant.SysResultCode;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.exception.ContextedRuntimeException;

/**
 * 服务异常（返回503 HTTP状态码，利用ACK机制重试）
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class AckException extends ContextedRuntimeException {
    /**
     * 异常代码
     */
    private final int code;

    /**
     * 异常消息
     */
    private final String message;

    public AckException(SysResultCode sysResultCode) {
        this.code = sysResultCode.getCode();
        this.message = sysResultCode.getMsg();
    }

    public AckException(SysResultCode sysResultCode, Throwable cause) {
        super(cause);
        this.code = sysResultCode.getCode();
        this.message = sysResultCode.getMsg();
    }

    public AckException(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public AckException(int code, String message, Throwable cause) {
        super(cause);
        this.code = code;
        this.message = message;
    }
}
