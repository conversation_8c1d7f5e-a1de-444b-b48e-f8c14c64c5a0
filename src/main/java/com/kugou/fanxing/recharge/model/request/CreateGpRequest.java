package com.kugou.fanxing.recharge.model.request;

import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CreateGpRequest extends GetOrderRequest {
    @NotNull(message = "productId不允许为空")
    @Pattern(regexp = ".+", message = "输入的商品不合法")
    private String productId;
    @NotNull(message = "currency不允许为空")
    private String currency;
}