package com.kugou.fanxing.recharge.model.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class GetPayH5Request {
    @NotNull
    @URL
    private String syncUrl;
    @NotBlank
    private String rechargeOrderNum;
    @NotNull
    @Min(value = 1)
    private long ts;
    @NotNull
    @NotBlank
    private String sign;
}
