package com.kugou.fanxing.recharge.model.request;

import lombok.Data;

@Data
public class FapiaoCallbackRequest {
    /**
     * 用户kugouid
     */
    private long userid;
    /**
     * 支付订单网关订单号
     */
    private String out_trade_no;
    /**
     * 支付订单业务订单号
     */
    private String order_no;
    /**
     * 用户是否可以申请开票
     */
    private boolean can_receipt;
    /**
     * 发票处理状态 0:初始化 1:成功 2:处理中 3:失败
     */
    private int receipt_status;
    /**
     * 发票类型 1:普通电子发票 2:普通纸质发票 3:专用纸质发票 4:专用电子发票
     */
    private int receipt_type;
    /**
     * 发票操作类型 1:电子发票正票 2:电子发票冲红 3:普通纸质发票作废(当月) 4:专用纸质发票作废(当月) 5:普通纸质发票红冲(跨月) 6:专用纸质发票红冲(跨月)
     */
    private int operation_type;
    /**
     * 记录添加时间
     */
    private String add_time;
    /**
     * 记录变更时间
     */
    private String update_time;
}
