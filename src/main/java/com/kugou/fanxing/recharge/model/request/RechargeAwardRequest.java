package com.kugou.fanxing.recharge.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RechargeAwardRequest {
    @NotNull
    @Range(min = 0, max = 2)
    private int sourceId;
    @NotNull
    @NotBlank
    private String bizCode;
    @NotNull
    @NotBlank
    @Pattern(regexp = "^R.+")
    private String rechargeOrderNum;
}
