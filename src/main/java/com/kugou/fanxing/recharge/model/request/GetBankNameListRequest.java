package com.kugou.fanxing.recharge.model.request;

import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

@Data
public class GetBankNameListRequest {
    @NotNull
    @NotBlank
    @Length(min = 2, max = 3)
    private String country;
    @NotNull
    @NotBlank
    @Length(min = 2, max = 30)
    private String paymentMethodType;
    @NotNull
    @Range(min = 1, max = 10000)
    private int page = 1;
    @NotNull
    @Range(min = 1, max = 100)
    private int pageSize = 100;
}
