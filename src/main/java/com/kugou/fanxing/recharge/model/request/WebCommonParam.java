package com.kugou.fanxing.recharge.model.request;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

/**
 * WEB通用请求参数
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class WebCommonParam {

    private int appId;
    private long userId;
    private long kugouId;
    private long kuwoId;
    private String token;
    private String uri;
    /**
     * 客户端IP
     */
    private String ip;
    private String host;
    private String referer;
    /**
     * 请求时间戳（毫秒）
     */
    private long timestamp;
    private String userAgent;
    private String httpHost;
    /**
     * 前端传递平台编号
     */
    private int platform;
    /**
     * 终端对应平台编号
     */
    private int pid;
    /**
     * 标准平台号
     */
    private int stdPlat;
    /**
     * APP客户端版本号
     */
    private String version;
    /**
     * 直播通用业务透传参数，json格式
     */
    private String ext;
    /**
     * 酷狗开放平台透传数据，json格式
     */
    private KugouOpenDispatchParam kugouOpenDispatchParam;
    /**
     * 业务线
     */
    private String bizLine;
    /**
     * 业务项
     */
    private String bizType;
    /**
     * 渠道号
     */
    private int channel;
    /**
     * 主播房间ID
     */
    private int stdRid;
    /**
     * 时区
     */
    private String timeZone;
    /**
     * 地区代号
     */
    private String areaCode;
    /**
     * 语言代码
     */
    private String lang;
    /**
     * 设备号
     */
    private String kfd;
    /**
     * 1 豆粉
     */
    private int type;
    /**
     * 1 首次开通
     */
    private int firstType;

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toStringExclude(this, "token");
    }
}
