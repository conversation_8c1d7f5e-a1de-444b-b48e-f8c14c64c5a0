package com.kugou.fanxing.recharge.model.request;

import com.kugou.fanxing.recharge.model.BaseCallbackDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

/**
 * array (
 *   'action' => 'tmallCallBack',
 *   'appid' => '1084',
 *   'areaid' => '01',
 *   'time' => '1609235083',
 *   'notify_id' => '561175b7b01d149ea8235972cb3a52cb',
 *   'order_no' => '1475650910223385583',
 *   'total_fee' => '10.000000',
 *   'royalty_fee' => '0.000000',
 *   'out_trade_no' => '01202012291744360100018812',
 *   'trade_status' => '1',
 *   'sign_type' => 'md5',
 *   'trade_time' => '20201229174436',
 *   'trade_no' => '1475650910223385583',
 *   'partner' => '23728878',
 *   'userid' => '1290249156',
 *   'auserid' => '1290249156',
 *   'extend' => '{"desc":"\\u5929\\u732b\\u5145\\u503c","cardId":"comFanxingPrice10"}',
 *   'sign' => '1a4710afc078cff15fadbec90953075d',
 * )
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class TmallCallbackRequest extends BaseCallbackDTO {
    private String action;
    @NotBlank(message = "对方参数total_fee为空")
    private String total_fee;
    private String royalty_fee;
    @NotBlank(message = "对方参数out_trade_no为空")
    private String out_trade_no;
    @Range(min = 1, max = 1, message = "不成功订单繁星不处理")
    private int trade_status;
    private String sign_type;
    @NotBlank(message = "对方参数userid为空")
    private long auserid;
    private String partner;
    private String trade_no;
    private String trade_time;
}
