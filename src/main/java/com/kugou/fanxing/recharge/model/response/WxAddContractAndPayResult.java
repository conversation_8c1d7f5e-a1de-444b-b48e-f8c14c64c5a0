package com.kugou.fanxing.recharge.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.kugou.fanxing.recharge.common.bind.ParamName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/3/12 16:59
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WxAddContractAndPayResult {
    private String prepay_id;
    private String code_url;
    private String mweb_url;
    private String appid;
    private String noncestr;
    private String partnerid;
    private String contractType;
    private String businessId;
    private String rechargeOrderId;
    private String prepayid;
    @JsonProperty("package")
    @ParamName("package")
    private String packageName;
    private long timestamp;
    private String sign;

}
