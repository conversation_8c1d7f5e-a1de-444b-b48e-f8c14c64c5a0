package com.kugou.fanxing.recharge.model.response;

import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

@Data
public class KupayCommonResponse {
    /**
     * status = 0 失败 error_code  error_msg  错误码及错误原因
     * status = 1 成功 error_coder = 0
     */
    private int status;
    /**
     * 返回的错误码
     */
    private int error_code;
    /**
     * 返回的错误信息
     */
    private String error_msg;
    /**
     *
     */
    private Map<String, Object> data;

    public static KupayCommonResponse result(SysResultCode sysResultCode, Map<String, Object> data) {
        KupayCommonResponse response = new KupayCommonResponse();
        response.setStatus(sysResultCode.isSuccess() ? 1 : 0);
        response.setError_code(sysResultCode.getCode());
        response.setError_msg(sysResultCode.getMsg());
        response.setData(MapUtils.isNotEmpty(data) ? data : Maps.newHashMap());
        return response;
    }
}
