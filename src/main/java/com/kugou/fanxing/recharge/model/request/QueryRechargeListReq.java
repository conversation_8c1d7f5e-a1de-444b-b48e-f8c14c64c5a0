package com.kugou.fanxing.recharge.model.request;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Pattern;

@Data
public class QueryRechargeListReq {
    @Range(min = 0, max = 1, message = "输入的月份信息不合法")
    private int index;
    @Range(min = 1, message = "输入的当前页码不合法")
    private int page;
    @Range(min = 1, message = "输入的分页大小不合法")
    private int pageSize;
    @Pattern(regexp = "^(0|1006)$", message = "输入的充值类型不合法")
    private String payTypeId;
    @Pattern(regexp = "^\\d{0,30}$", message = "输入的密码信息不合法")
    private String pass;
}
