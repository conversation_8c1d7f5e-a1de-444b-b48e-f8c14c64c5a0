package com.kugou.fanxing.recharge.model;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class BaseCallbackDTO {
    private int appid;
    private String areaid;
    private int time;
    private String notify_id;
    @Min(1)
    private long userid;
    @NotBlank(message = "对方参数order_no为空")
    private String order_no;
    private int callback_status;
    @NotNull
    private String extend;
    @NotBlank(message = "对方参数sign为空")
    private String sign;
}
