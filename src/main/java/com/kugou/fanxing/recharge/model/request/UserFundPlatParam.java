/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.recharge.model.request;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2024-03-21")
public class UserFundPlatParam implements org.apache.thrift.TBase<UserFundPlatParam, UserFundPlatParam._Fields>, java.io.Serializable, Cloneable, Comparable<UserFundPlatParam> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("UserFundPlatParam");

  private static final org.apache.thrift.protocol.TField SENDER_DEPARTMENT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("senderDepartmentId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField SENDER_PRODUCT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("senderProductId", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField SENDER_MINOR_PRODUCT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("senderMinorProductId", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField SENDER_HARDWARE_PLATFORM_FIELD_DESC = new org.apache.thrift.protocol.TField("senderHardwarePlatform", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField SENDER_CHANNEL_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("senderChannelId", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField SENDER_SUB_CHANNEL_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("senderSubChannelId", org.apache.thrift.protocol.TType.STRING, (short)6);
  private static final org.apache.thrift.protocol.TField RECEIVER_DEPARTMENT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("receiverDepartmentId", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField ACCOUNT_CHANGE_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("accountChangeType", org.apache.thrift.protocol.TType.STRING, (short)8);
  private static final org.apache.thrift.protocol.TField FXC_CHANGE_DESC_FIELD_DESC = new org.apache.thrift.protocol.TField("fxcChangeDesc", org.apache.thrift.protocol.TType.STRING, (short)9);
  private static final org.apache.thrift.protocol.TField COIN_FIELD_DESC = new org.apache.thrift.protocol.TField("coin", org.apache.thrift.protocol.TType.STRING, (short)10);
  private static final org.apache.thrift.protocol.TField FROM_KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("fromKugouId", org.apache.thrift.protocol.TType.STRING, (short)11);
  private static final org.apache.thrift.protocol.TField TO_KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("toKugouId", org.apache.thrift.protocol.TType.STRING, (short)12);
  private static final org.apache.thrift.protocol.TField ROOM_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("roomId", org.apache.thrift.protocol.TType.STRING, (short)13);
  private static final org.apache.thrift.protocol.TField GIFT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("giftId", org.apache.thrift.protocol.TType.STRING, (short)14);
  private static final org.apache.thrift.protocol.TField GIFT_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("giftName", org.apache.thrift.protocol.TType.STRING, (short)15);
  private static final org.apache.thrift.protocol.TField GIFT_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("giftNum", org.apache.thrift.protocol.TType.STRING, (short)16);
  private static final org.apache.thrift.protocol.TField ACTION_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actionId", org.apache.thrift.protocol.TType.STRING, (short)17);
  private static final org.apache.thrift.protocol.TField EXT_FIELD_DESC = new org.apache.thrift.protocol.TField("ext", org.apache.thrift.protocol.TType.STRING, (short)18);
  private static final org.apache.thrift.protocol.TField PID_FIELD_DESC = new org.apache.thrift.protocol.TField("pid", org.apache.thrift.protocol.TType.STRING, (short)19);
  private static final org.apache.thrift.protocol.TField GLOBAL_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("globalId", org.apache.thrift.protocol.TType.STRING, (short)20);
  private static final org.apache.thrift.protocol.TField IP_FIELD_DESC = new org.apache.thrift.protocol.TField("ip", org.apache.thrift.protocol.TType.STRING, (short)21);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new UserFundPlatParamStandardSchemeFactory());
    schemes.put(TupleScheme.class, new UserFundPlatParamTupleSchemeFactory());
  }

  public String senderDepartmentId; // required
  public String senderProductId; // required
  public String senderMinorProductId; // required
  public String senderHardwarePlatform; // required
  public String senderChannelId; // required
  public String senderSubChannelId; // required
  public String receiverDepartmentId; // required
  public String accountChangeType; // required
  public String fxcChangeDesc; // required
  public String coin; // required
  public String fromKugouId; // required
  public String toKugouId; // required
  public String roomId; // required
  public String giftId; // required
  public String giftName; // required
  public String giftNum; // required
  public String actionId; // required
  public String ext; // required
  public String pid; // required
  public String globalId; // required
  public String ip; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    SENDER_DEPARTMENT_ID((short)1, "senderDepartmentId"),
    SENDER_PRODUCT_ID((short)2, "senderProductId"),
    SENDER_MINOR_PRODUCT_ID((short)3, "senderMinorProductId"),
    SENDER_HARDWARE_PLATFORM((short)4, "senderHardwarePlatform"),
    SENDER_CHANNEL_ID((short)5, "senderChannelId"),
    SENDER_SUB_CHANNEL_ID((short)6, "senderSubChannelId"),
    RECEIVER_DEPARTMENT_ID((short)7, "receiverDepartmentId"),
    ACCOUNT_CHANGE_TYPE((short)8, "accountChangeType"),
    FXC_CHANGE_DESC((short)9, "fxcChangeDesc"),
    COIN((short)10, "coin"),
    FROM_KUGOU_ID((short)11, "fromKugouId"),
    TO_KUGOU_ID((short)12, "toKugouId"),
    ROOM_ID((short)13, "roomId"),
    GIFT_ID((short)14, "giftId"),
    GIFT_NAME((short)15, "giftName"),
    GIFT_NUM((short)16, "giftNum"),
    ACTION_ID((short)17, "actionId"),
    EXT((short)18, "ext"),
    PID((short)19, "pid"),
    GLOBAL_ID((short)20, "globalId"),
    IP((short)21, "ip");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // SENDER_DEPARTMENT_ID
          return SENDER_DEPARTMENT_ID;
        case 2: // SENDER_PRODUCT_ID
          return SENDER_PRODUCT_ID;
        case 3: // SENDER_MINOR_PRODUCT_ID
          return SENDER_MINOR_PRODUCT_ID;
        case 4: // SENDER_HARDWARE_PLATFORM
          return SENDER_HARDWARE_PLATFORM;
        case 5: // SENDER_CHANNEL_ID
          return SENDER_CHANNEL_ID;
        case 6: // SENDER_SUB_CHANNEL_ID
          return SENDER_SUB_CHANNEL_ID;
        case 7: // RECEIVER_DEPARTMENT_ID
          return RECEIVER_DEPARTMENT_ID;
        case 8: // ACCOUNT_CHANGE_TYPE
          return ACCOUNT_CHANGE_TYPE;
        case 9: // FXC_CHANGE_DESC
          return FXC_CHANGE_DESC;
        case 10: // COIN
          return COIN;
        case 11: // FROM_KUGOU_ID
          return FROM_KUGOU_ID;
        case 12: // TO_KUGOU_ID
          return TO_KUGOU_ID;
        case 13: // ROOM_ID
          return ROOM_ID;
        case 14: // GIFT_ID
          return GIFT_ID;
        case 15: // GIFT_NAME
          return GIFT_NAME;
        case 16: // GIFT_NUM
          return GIFT_NUM;
        case 17: // ACTION_ID
          return ACTION_ID;
        case 18: // EXT
          return EXT;
        case 19: // PID
          return PID;
        case 20: // GLOBAL_ID
          return GLOBAL_ID;
        case 21: // IP
          return IP;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.SENDER_DEPARTMENT_ID, new org.apache.thrift.meta_data.FieldMetaData("senderDepartmentId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SENDER_PRODUCT_ID, new org.apache.thrift.meta_data.FieldMetaData("senderProductId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SENDER_MINOR_PRODUCT_ID, new org.apache.thrift.meta_data.FieldMetaData("senderMinorProductId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SENDER_HARDWARE_PLATFORM, new org.apache.thrift.meta_data.FieldMetaData("senderHardwarePlatform", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SENDER_CHANNEL_ID, new org.apache.thrift.meta_data.FieldMetaData("senderChannelId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SENDER_SUB_CHANNEL_ID, new org.apache.thrift.meta_data.FieldMetaData("senderSubChannelId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.RECEIVER_DEPARTMENT_ID, new org.apache.thrift.meta_data.FieldMetaData("receiverDepartmentId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ACCOUNT_CHANGE_TYPE, new org.apache.thrift.meta_data.FieldMetaData("accountChangeType", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.FXC_CHANGE_DESC, new org.apache.thrift.meta_data.FieldMetaData("fxcChangeDesc", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.COIN, new org.apache.thrift.meta_data.FieldMetaData("coin", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.FROM_KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("fromKugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.TO_KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("toKugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ROOM_ID, new org.apache.thrift.meta_data.FieldMetaData("roomId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.GIFT_ID, new org.apache.thrift.meta_data.FieldMetaData("giftId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.GIFT_NAME, new org.apache.thrift.meta_data.FieldMetaData("giftName", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.GIFT_NUM, new org.apache.thrift.meta_data.FieldMetaData("giftNum", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ACTION_ID, new org.apache.thrift.meta_data.FieldMetaData("actionId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT, new org.apache.thrift.meta_data.FieldMetaData("ext", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PID, new org.apache.thrift.meta_data.FieldMetaData("pid", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.GLOBAL_ID, new org.apache.thrift.meta_data.FieldMetaData("globalId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.IP, new org.apache.thrift.meta_data.FieldMetaData("ip", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(UserFundPlatParam.class, metaDataMap);
  }

  public UserFundPlatParam() {
  }

  public UserFundPlatParam(
    String senderDepartmentId,
    String senderProductId,
    String senderMinorProductId,
    String senderHardwarePlatform,
    String senderChannelId,
    String senderSubChannelId,
    String receiverDepartmentId,
    String accountChangeType,
    String fxcChangeDesc,
    String coin,
    String fromKugouId,
    String toKugouId,
    String roomId,
    String giftId,
    String giftName,
    String giftNum,
    String actionId,
    String ext,
    String pid,
    String globalId,
    String ip)
  {
    this();
    this.senderDepartmentId = senderDepartmentId;
    this.senderProductId = senderProductId;
    this.senderMinorProductId = senderMinorProductId;
    this.senderHardwarePlatform = senderHardwarePlatform;
    this.senderChannelId = senderChannelId;
    this.senderSubChannelId = senderSubChannelId;
    this.receiverDepartmentId = receiverDepartmentId;
    this.accountChangeType = accountChangeType;
    this.fxcChangeDesc = fxcChangeDesc;
    this.coin = coin;
    this.fromKugouId = fromKugouId;
    this.toKugouId = toKugouId;
    this.roomId = roomId;
    this.giftId = giftId;
    this.giftName = giftName;
    this.giftNum = giftNum;
    this.actionId = actionId;
    this.ext = ext;
    this.pid = pid;
    this.globalId = globalId;
    this.ip = ip;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public UserFundPlatParam(UserFundPlatParam other) {
    if (other.isSetSenderDepartmentId()) {
      this.senderDepartmentId = other.senderDepartmentId;
    }
    if (other.isSetSenderProductId()) {
      this.senderProductId = other.senderProductId;
    }
    if (other.isSetSenderMinorProductId()) {
      this.senderMinorProductId = other.senderMinorProductId;
    }
    if (other.isSetSenderHardwarePlatform()) {
      this.senderHardwarePlatform = other.senderHardwarePlatform;
    }
    if (other.isSetSenderChannelId()) {
      this.senderChannelId = other.senderChannelId;
    }
    if (other.isSetSenderSubChannelId()) {
      this.senderSubChannelId = other.senderSubChannelId;
    }
    if (other.isSetReceiverDepartmentId()) {
      this.receiverDepartmentId = other.receiverDepartmentId;
    }
    if (other.isSetAccountChangeType()) {
      this.accountChangeType = other.accountChangeType;
    }
    if (other.isSetFxcChangeDesc()) {
      this.fxcChangeDesc = other.fxcChangeDesc;
    }
    if (other.isSetCoin()) {
      this.coin = other.coin;
    }
    if (other.isSetFromKugouId()) {
      this.fromKugouId = other.fromKugouId;
    }
    if (other.isSetToKugouId()) {
      this.toKugouId = other.toKugouId;
    }
    if (other.isSetRoomId()) {
      this.roomId = other.roomId;
    }
    if (other.isSetGiftId()) {
      this.giftId = other.giftId;
    }
    if (other.isSetGiftName()) {
      this.giftName = other.giftName;
    }
    if (other.isSetGiftNum()) {
      this.giftNum = other.giftNum;
    }
    if (other.isSetActionId()) {
      this.actionId = other.actionId;
    }
    if (other.isSetExt()) {
      this.ext = other.ext;
    }
    if (other.isSetPid()) {
      this.pid = other.pid;
    }
    if (other.isSetGlobalId()) {
      this.globalId = other.globalId;
    }
    if (other.isSetIp()) {
      this.ip = other.ip;
    }
  }

  public UserFundPlatParam deepCopy() {
    return new UserFundPlatParam(this);
  }

  @Override
  public void clear() {
    this.senderDepartmentId = null;
    this.senderProductId = null;
    this.senderMinorProductId = null;
    this.senderHardwarePlatform = null;
    this.senderChannelId = null;
    this.senderSubChannelId = null;
    this.receiverDepartmentId = null;
    this.accountChangeType = null;
    this.fxcChangeDesc = null;
    this.coin = null;
    this.fromKugouId = null;
    this.toKugouId = null;
    this.roomId = null;
    this.giftId = null;
    this.giftName = null;
    this.giftNum = null;
    this.actionId = null;
    this.ext = null;
    this.pid = null;
    this.globalId = null;
    this.ip = null;
  }

  public String getSenderDepartmentId() {
    return this.senderDepartmentId;
  }

  public UserFundPlatParam setSenderDepartmentId(String senderDepartmentId) {
    this.senderDepartmentId = senderDepartmentId;
    return this;
  }

  public void unsetSenderDepartmentId() {
    this.senderDepartmentId = null;
  }

  /** Returns true if field senderDepartmentId is set (has been assigned a value) and false otherwise */
  public boolean isSetSenderDepartmentId() {
    return this.senderDepartmentId != null;
  }

  public void setSenderDepartmentIdIsSet(boolean value) {
    if (!value) {
      this.senderDepartmentId = null;
    }
  }

  public String getSenderProductId() {
    return this.senderProductId;
  }

  public UserFundPlatParam setSenderProductId(String senderProductId) {
    this.senderProductId = senderProductId;
    return this;
  }

  public void unsetSenderProductId() {
    this.senderProductId = null;
  }

  /** Returns true if field senderProductId is set (has been assigned a value) and false otherwise */
  public boolean isSetSenderProductId() {
    return this.senderProductId != null;
  }

  public void setSenderProductIdIsSet(boolean value) {
    if (!value) {
      this.senderProductId = null;
    }
  }

  public String getSenderMinorProductId() {
    return this.senderMinorProductId;
  }

  public UserFundPlatParam setSenderMinorProductId(String senderMinorProductId) {
    this.senderMinorProductId = senderMinorProductId;
    return this;
  }

  public void unsetSenderMinorProductId() {
    this.senderMinorProductId = null;
  }

  /** Returns true if field senderMinorProductId is set (has been assigned a value) and false otherwise */
  public boolean isSetSenderMinorProductId() {
    return this.senderMinorProductId != null;
  }

  public void setSenderMinorProductIdIsSet(boolean value) {
    if (!value) {
      this.senderMinorProductId = null;
    }
  }

  public String getSenderHardwarePlatform() {
    return this.senderHardwarePlatform;
  }

  public UserFundPlatParam setSenderHardwarePlatform(String senderHardwarePlatform) {
    this.senderHardwarePlatform = senderHardwarePlatform;
    return this;
  }

  public void unsetSenderHardwarePlatform() {
    this.senderHardwarePlatform = null;
  }

  /** Returns true if field senderHardwarePlatform is set (has been assigned a value) and false otherwise */
  public boolean isSetSenderHardwarePlatform() {
    return this.senderHardwarePlatform != null;
  }

  public void setSenderHardwarePlatformIsSet(boolean value) {
    if (!value) {
      this.senderHardwarePlatform = null;
    }
  }

  public String getSenderChannelId() {
    return this.senderChannelId;
  }

  public UserFundPlatParam setSenderChannelId(String senderChannelId) {
    this.senderChannelId = senderChannelId;
    return this;
  }

  public void unsetSenderChannelId() {
    this.senderChannelId = null;
  }

  /** Returns true if field senderChannelId is set (has been assigned a value) and false otherwise */
  public boolean isSetSenderChannelId() {
    return this.senderChannelId != null;
  }

  public void setSenderChannelIdIsSet(boolean value) {
    if (!value) {
      this.senderChannelId = null;
    }
  }

  public String getSenderSubChannelId() {
    return this.senderSubChannelId;
  }

  public UserFundPlatParam setSenderSubChannelId(String senderSubChannelId) {
    this.senderSubChannelId = senderSubChannelId;
    return this;
  }

  public void unsetSenderSubChannelId() {
    this.senderSubChannelId = null;
  }

  /** Returns true if field senderSubChannelId is set (has been assigned a value) and false otherwise */
  public boolean isSetSenderSubChannelId() {
    return this.senderSubChannelId != null;
  }

  public void setSenderSubChannelIdIsSet(boolean value) {
    if (!value) {
      this.senderSubChannelId = null;
    }
  }

  public String getReceiverDepartmentId() {
    return this.receiverDepartmentId;
  }

  public UserFundPlatParam setReceiverDepartmentId(String receiverDepartmentId) {
    this.receiverDepartmentId = receiverDepartmentId;
    return this;
  }

  public void unsetReceiverDepartmentId() {
    this.receiverDepartmentId = null;
  }

  /** Returns true if field receiverDepartmentId is set (has been assigned a value) and false otherwise */
  public boolean isSetReceiverDepartmentId() {
    return this.receiverDepartmentId != null;
  }

  public void setReceiverDepartmentIdIsSet(boolean value) {
    if (!value) {
      this.receiverDepartmentId = null;
    }
  }

  public String getAccountChangeType() {
    return this.accountChangeType;
  }

  public UserFundPlatParam setAccountChangeType(String accountChangeType) {
    this.accountChangeType = accountChangeType;
    return this;
  }

  public void unsetAccountChangeType() {
    this.accountChangeType = null;
  }

  /** Returns true if field accountChangeType is set (has been assigned a value) and false otherwise */
  public boolean isSetAccountChangeType() {
    return this.accountChangeType != null;
  }

  public void setAccountChangeTypeIsSet(boolean value) {
    if (!value) {
      this.accountChangeType = null;
    }
  }

  public String getFxcChangeDesc() {
    return this.fxcChangeDesc;
  }

  public UserFundPlatParam setFxcChangeDesc(String fxcChangeDesc) {
    this.fxcChangeDesc = fxcChangeDesc;
    return this;
  }

  public void unsetFxcChangeDesc() {
    this.fxcChangeDesc = null;
  }

  /** Returns true if field fxcChangeDesc is set (has been assigned a value) and false otherwise */
  public boolean isSetFxcChangeDesc() {
    return this.fxcChangeDesc != null;
  }

  public void setFxcChangeDescIsSet(boolean value) {
    if (!value) {
      this.fxcChangeDesc = null;
    }
  }

  public String getCoin() {
    return this.coin;
  }

  public UserFundPlatParam setCoin(String coin) {
    this.coin = coin;
    return this;
  }

  public void unsetCoin() {
    this.coin = null;
  }

  /** Returns true if field coin is set (has been assigned a value) and false otherwise */
  public boolean isSetCoin() {
    return this.coin != null;
  }

  public void setCoinIsSet(boolean value) {
    if (!value) {
      this.coin = null;
    }
  }

  public String getFromKugouId() {
    return this.fromKugouId;
  }

  public UserFundPlatParam setFromKugouId(String fromKugouId) {
    this.fromKugouId = fromKugouId;
    return this;
  }

  public void unsetFromKugouId() {
    this.fromKugouId = null;
  }

  /** Returns true if field fromKugouId is set (has been assigned a value) and false otherwise */
  public boolean isSetFromKugouId() {
    return this.fromKugouId != null;
  }

  public void setFromKugouIdIsSet(boolean value) {
    if (!value) {
      this.fromKugouId = null;
    }
  }

  public String getToKugouId() {
    return this.toKugouId;
  }

  public UserFundPlatParam setToKugouId(String toKugouId) {
    this.toKugouId = toKugouId;
    return this;
  }

  public void unsetToKugouId() {
    this.toKugouId = null;
  }

  /** Returns true if field toKugouId is set (has been assigned a value) and false otherwise */
  public boolean isSetToKugouId() {
    return this.toKugouId != null;
  }

  public void setToKugouIdIsSet(boolean value) {
    if (!value) {
      this.toKugouId = null;
    }
  }

  public String getRoomId() {
    return this.roomId;
  }

  public UserFundPlatParam setRoomId(String roomId) {
    this.roomId = roomId;
    return this;
  }

  public void unsetRoomId() {
    this.roomId = null;
  }

  /** Returns true if field roomId is set (has been assigned a value) and false otherwise */
  public boolean isSetRoomId() {
    return this.roomId != null;
  }

  public void setRoomIdIsSet(boolean value) {
    if (!value) {
      this.roomId = null;
    }
  }

  public String getGiftId() {
    return this.giftId;
  }

  public UserFundPlatParam setGiftId(String giftId) {
    this.giftId = giftId;
    return this;
  }

  public void unsetGiftId() {
    this.giftId = null;
  }

  /** Returns true if field giftId is set (has been assigned a value) and false otherwise */
  public boolean isSetGiftId() {
    return this.giftId != null;
  }

  public void setGiftIdIsSet(boolean value) {
    if (!value) {
      this.giftId = null;
    }
  }

  public String getGiftName() {
    return this.giftName;
  }

  public UserFundPlatParam setGiftName(String giftName) {
    this.giftName = giftName;
    return this;
  }

  public void unsetGiftName() {
    this.giftName = null;
  }

  /** Returns true if field giftName is set (has been assigned a value) and false otherwise */
  public boolean isSetGiftName() {
    return this.giftName != null;
  }

  public void setGiftNameIsSet(boolean value) {
    if (!value) {
      this.giftName = null;
    }
  }

  public String getGiftNum() {
    return this.giftNum;
  }

  public UserFundPlatParam setGiftNum(String giftNum) {
    this.giftNum = giftNum;
    return this;
  }

  public void unsetGiftNum() {
    this.giftNum = null;
  }

  /** Returns true if field giftNum is set (has been assigned a value) and false otherwise */
  public boolean isSetGiftNum() {
    return this.giftNum != null;
  }

  public void setGiftNumIsSet(boolean value) {
    if (!value) {
      this.giftNum = null;
    }
  }

  public String getActionId() {
    return this.actionId;
  }

  public UserFundPlatParam setActionId(String actionId) {
    this.actionId = actionId;
    return this;
  }

  public void unsetActionId() {
    this.actionId = null;
  }

  /** Returns true if field actionId is set (has been assigned a value) and false otherwise */
  public boolean isSetActionId() {
    return this.actionId != null;
  }

  public void setActionIdIsSet(boolean value) {
    if (!value) {
      this.actionId = null;
    }
  }

  public String getExt() {
    return this.ext;
  }

  public UserFundPlatParam setExt(String ext) {
    this.ext = ext;
    return this;
  }

  public void unsetExt() {
    this.ext = null;
  }

  /** Returns true if field ext is set (has been assigned a value) and false otherwise */
  public boolean isSetExt() {
    return this.ext != null;
  }

  public void setExtIsSet(boolean value) {
    if (!value) {
      this.ext = null;
    }
  }

  public String getPid() {
    return this.pid;
  }

  public UserFundPlatParam setPid(String pid) {
    this.pid = pid;
    return this;
  }

  public void unsetPid() {
    this.pid = null;
  }

  /** Returns true if field pid is set (has been assigned a value) and false otherwise */
  public boolean isSetPid() {
    return this.pid != null;
  }

  public void setPidIsSet(boolean value) {
    if (!value) {
      this.pid = null;
    }
  }

  public String getGlobalId() {
    return this.globalId;
  }

  public UserFundPlatParam setGlobalId(String globalId) {
    this.globalId = globalId;
    return this;
  }

  public void unsetGlobalId() {
    this.globalId = null;
  }

  /** Returns true if field globalId is set (has been assigned a value) and false otherwise */
  public boolean isSetGlobalId() {
    return this.globalId != null;
  }

  public void setGlobalIdIsSet(boolean value) {
    if (!value) {
      this.globalId = null;
    }
  }

  public String getIp() {
    return this.ip;
  }

  public UserFundPlatParam setIp(String ip) {
    this.ip = ip;
    return this;
  }

  public void unsetIp() {
    this.ip = null;
  }

  /** Returns true if field ip is set (has been assigned a value) and false otherwise */
  public boolean isSetIp() {
    return this.ip != null;
  }

  public void setIpIsSet(boolean value) {
    if (!value) {
      this.ip = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case SENDER_DEPARTMENT_ID:
      if (value == null) {
        unsetSenderDepartmentId();
      } else {
        setSenderDepartmentId((String)value);
      }
      break;

    case SENDER_PRODUCT_ID:
      if (value == null) {
        unsetSenderProductId();
      } else {
        setSenderProductId((String)value);
      }
      break;

    case SENDER_MINOR_PRODUCT_ID:
      if (value == null) {
        unsetSenderMinorProductId();
      } else {
        setSenderMinorProductId((String)value);
      }
      break;

    case SENDER_HARDWARE_PLATFORM:
      if (value == null) {
        unsetSenderHardwarePlatform();
      } else {
        setSenderHardwarePlatform((String)value);
      }
      break;

    case SENDER_CHANNEL_ID:
      if (value == null) {
        unsetSenderChannelId();
      } else {
        setSenderChannelId((String)value);
      }
      break;

    case SENDER_SUB_CHANNEL_ID:
      if (value == null) {
        unsetSenderSubChannelId();
      } else {
        setSenderSubChannelId((String)value);
      }
      break;

    case RECEIVER_DEPARTMENT_ID:
      if (value == null) {
        unsetReceiverDepartmentId();
      } else {
        setReceiverDepartmentId((String)value);
      }
      break;

    case ACCOUNT_CHANGE_TYPE:
      if (value == null) {
        unsetAccountChangeType();
      } else {
        setAccountChangeType((String)value);
      }
      break;

    case FXC_CHANGE_DESC:
      if (value == null) {
        unsetFxcChangeDesc();
      } else {
        setFxcChangeDesc((String)value);
      }
      break;

    case COIN:
      if (value == null) {
        unsetCoin();
      } else {
        setCoin((String)value);
      }
      break;

    case FROM_KUGOU_ID:
      if (value == null) {
        unsetFromKugouId();
      } else {
        setFromKugouId((String)value);
      }
      break;

    case TO_KUGOU_ID:
      if (value == null) {
        unsetToKugouId();
      } else {
        setToKugouId((String)value);
      }
      break;

    case ROOM_ID:
      if (value == null) {
        unsetRoomId();
      } else {
        setRoomId((String)value);
      }
      break;

    case GIFT_ID:
      if (value == null) {
        unsetGiftId();
      } else {
        setGiftId((String)value);
      }
      break;

    case GIFT_NAME:
      if (value == null) {
        unsetGiftName();
      } else {
        setGiftName((String)value);
      }
      break;

    case GIFT_NUM:
      if (value == null) {
        unsetGiftNum();
      } else {
        setGiftNum((String)value);
      }
      break;

    case ACTION_ID:
      if (value == null) {
        unsetActionId();
      } else {
        setActionId((String)value);
      }
      break;

    case EXT:
      if (value == null) {
        unsetExt();
      } else {
        setExt((String)value);
      }
      break;

    case PID:
      if (value == null) {
        unsetPid();
      } else {
        setPid((String)value);
      }
      break;

    case GLOBAL_ID:
      if (value == null) {
        unsetGlobalId();
      } else {
        setGlobalId((String)value);
      }
      break;

    case IP:
      if (value == null) {
        unsetIp();
      } else {
        setIp((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case SENDER_DEPARTMENT_ID:
      return getSenderDepartmentId();

    case SENDER_PRODUCT_ID:
      return getSenderProductId();

    case SENDER_MINOR_PRODUCT_ID:
      return getSenderMinorProductId();

    case SENDER_HARDWARE_PLATFORM:
      return getSenderHardwarePlatform();

    case SENDER_CHANNEL_ID:
      return getSenderChannelId();

    case SENDER_SUB_CHANNEL_ID:
      return getSenderSubChannelId();

    case RECEIVER_DEPARTMENT_ID:
      return getReceiverDepartmentId();

    case ACCOUNT_CHANGE_TYPE:
      return getAccountChangeType();

    case FXC_CHANGE_DESC:
      return getFxcChangeDesc();

    case COIN:
      return getCoin();

    case FROM_KUGOU_ID:
      return getFromKugouId();

    case TO_KUGOU_ID:
      return getToKugouId();

    case ROOM_ID:
      return getRoomId();

    case GIFT_ID:
      return getGiftId();

    case GIFT_NAME:
      return getGiftName();

    case GIFT_NUM:
      return getGiftNum();

    case ACTION_ID:
      return getActionId();

    case EXT:
      return getExt();

    case PID:
      return getPid();

    case GLOBAL_ID:
      return getGlobalId();

    case IP:
      return getIp();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case SENDER_DEPARTMENT_ID:
      return isSetSenderDepartmentId();
    case SENDER_PRODUCT_ID:
      return isSetSenderProductId();
    case SENDER_MINOR_PRODUCT_ID:
      return isSetSenderMinorProductId();
    case SENDER_HARDWARE_PLATFORM:
      return isSetSenderHardwarePlatform();
    case SENDER_CHANNEL_ID:
      return isSetSenderChannelId();
    case SENDER_SUB_CHANNEL_ID:
      return isSetSenderSubChannelId();
    case RECEIVER_DEPARTMENT_ID:
      return isSetReceiverDepartmentId();
    case ACCOUNT_CHANGE_TYPE:
      return isSetAccountChangeType();
    case FXC_CHANGE_DESC:
      return isSetFxcChangeDesc();
    case COIN:
      return isSetCoin();
    case FROM_KUGOU_ID:
      return isSetFromKugouId();
    case TO_KUGOU_ID:
      return isSetToKugouId();
    case ROOM_ID:
      return isSetRoomId();
    case GIFT_ID:
      return isSetGiftId();
    case GIFT_NAME:
      return isSetGiftName();
    case GIFT_NUM:
      return isSetGiftNum();
    case ACTION_ID:
      return isSetActionId();
    case EXT:
      return isSetExt();
    case PID:
      return isSetPid();
    case GLOBAL_ID:
      return isSetGlobalId();
    case IP:
      return isSetIp();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof UserFundPlatParam)
      return this.equals((UserFundPlatParam)that);
    return false;
  }

  public boolean equals(UserFundPlatParam that) {
    if (that == null)
      return false;

    boolean this_present_senderDepartmentId = true && this.isSetSenderDepartmentId();
    boolean that_present_senderDepartmentId = true && that.isSetSenderDepartmentId();
    if (this_present_senderDepartmentId || that_present_senderDepartmentId) {
      if (!(this_present_senderDepartmentId && that_present_senderDepartmentId))
        return false;
      if (!this.senderDepartmentId.equals(that.senderDepartmentId))
        return false;
    }

    boolean this_present_senderProductId = true && this.isSetSenderProductId();
    boolean that_present_senderProductId = true && that.isSetSenderProductId();
    if (this_present_senderProductId || that_present_senderProductId) {
      if (!(this_present_senderProductId && that_present_senderProductId))
        return false;
      if (!this.senderProductId.equals(that.senderProductId))
        return false;
    }

    boolean this_present_senderMinorProductId = true && this.isSetSenderMinorProductId();
    boolean that_present_senderMinorProductId = true && that.isSetSenderMinorProductId();
    if (this_present_senderMinorProductId || that_present_senderMinorProductId) {
      if (!(this_present_senderMinorProductId && that_present_senderMinorProductId))
        return false;
      if (!this.senderMinorProductId.equals(that.senderMinorProductId))
        return false;
    }

    boolean this_present_senderHardwarePlatform = true && this.isSetSenderHardwarePlatform();
    boolean that_present_senderHardwarePlatform = true && that.isSetSenderHardwarePlatform();
    if (this_present_senderHardwarePlatform || that_present_senderHardwarePlatform) {
      if (!(this_present_senderHardwarePlatform && that_present_senderHardwarePlatform))
        return false;
      if (!this.senderHardwarePlatform.equals(that.senderHardwarePlatform))
        return false;
    }

    boolean this_present_senderChannelId = true && this.isSetSenderChannelId();
    boolean that_present_senderChannelId = true && that.isSetSenderChannelId();
    if (this_present_senderChannelId || that_present_senderChannelId) {
      if (!(this_present_senderChannelId && that_present_senderChannelId))
        return false;
      if (!this.senderChannelId.equals(that.senderChannelId))
        return false;
    }

    boolean this_present_senderSubChannelId = true && this.isSetSenderSubChannelId();
    boolean that_present_senderSubChannelId = true && that.isSetSenderSubChannelId();
    if (this_present_senderSubChannelId || that_present_senderSubChannelId) {
      if (!(this_present_senderSubChannelId && that_present_senderSubChannelId))
        return false;
      if (!this.senderSubChannelId.equals(that.senderSubChannelId))
        return false;
    }

    boolean this_present_receiverDepartmentId = true && this.isSetReceiverDepartmentId();
    boolean that_present_receiverDepartmentId = true && that.isSetReceiverDepartmentId();
    if (this_present_receiverDepartmentId || that_present_receiverDepartmentId) {
      if (!(this_present_receiverDepartmentId && that_present_receiverDepartmentId))
        return false;
      if (!this.receiverDepartmentId.equals(that.receiverDepartmentId))
        return false;
    }

    boolean this_present_accountChangeType = true && this.isSetAccountChangeType();
    boolean that_present_accountChangeType = true && that.isSetAccountChangeType();
    if (this_present_accountChangeType || that_present_accountChangeType) {
      if (!(this_present_accountChangeType && that_present_accountChangeType))
        return false;
      if (!this.accountChangeType.equals(that.accountChangeType))
        return false;
    }

    boolean this_present_fxcChangeDesc = true && this.isSetFxcChangeDesc();
    boolean that_present_fxcChangeDesc = true && that.isSetFxcChangeDesc();
    if (this_present_fxcChangeDesc || that_present_fxcChangeDesc) {
      if (!(this_present_fxcChangeDesc && that_present_fxcChangeDesc))
        return false;
      if (!this.fxcChangeDesc.equals(that.fxcChangeDesc))
        return false;
    }

    boolean this_present_coin = true && this.isSetCoin();
    boolean that_present_coin = true && that.isSetCoin();
    if (this_present_coin || that_present_coin) {
      if (!(this_present_coin && that_present_coin))
        return false;
      if (!this.coin.equals(that.coin))
        return false;
    }

    boolean this_present_fromKugouId = true && this.isSetFromKugouId();
    boolean that_present_fromKugouId = true && that.isSetFromKugouId();
    if (this_present_fromKugouId || that_present_fromKugouId) {
      if (!(this_present_fromKugouId && that_present_fromKugouId))
        return false;
      if (!this.fromKugouId.equals(that.fromKugouId))
        return false;
    }

    boolean this_present_toKugouId = true && this.isSetToKugouId();
    boolean that_present_toKugouId = true && that.isSetToKugouId();
    if (this_present_toKugouId || that_present_toKugouId) {
      if (!(this_present_toKugouId && that_present_toKugouId))
        return false;
      if (!this.toKugouId.equals(that.toKugouId))
        return false;
    }

    boolean this_present_roomId = true && this.isSetRoomId();
    boolean that_present_roomId = true && that.isSetRoomId();
    if (this_present_roomId || that_present_roomId) {
      if (!(this_present_roomId && that_present_roomId))
        return false;
      if (!this.roomId.equals(that.roomId))
        return false;
    }

    boolean this_present_giftId = true && this.isSetGiftId();
    boolean that_present_giftId = true && that.isSetGiftId();
    if (this_present_giftId || that_present_giftId) {
      if (!(this_present_giftId && that_present_giftId))
        return false;
      if (!this.giftId.equals(that.giftId))
        return false;
    }

    boolean this_present_giftName = true && this.isSetGiftName();
    boolean that_present_giftName = true && that.isSetGiftName();
    if (this_present_giftName || that_present_giftName) {
      if (!(this_present_giftName && that_present_giftName))
        return false;
      if (!this.giftName.equals(that.giftName))
        return false;
    }

    boolean this_present_giftNum = true && this.isSetGiftNum();
    boolean that_present_giftNum = true && that.isSetGiftNum();
    if (this_present_giftNum || that_present_giftNum) {
      if (!(this_present_giftNum && that_present_giftNum))
        return false;
      if (!this.giftNum.equals(that.giftNum))
        return false;
    }

    boolean this_present_actionId = true && this.isSetActionId();
    boolean that_present_actionId = true && that.isSetActionId();
    if (this_present_actionId || that_present_actionId) {
      if (!(this_present_actionId && that_present_actionId))
        return false;
      if (!this.actionId.equals(that.actionId))
        return false;
    }

    boolean this_present_ext = true && this.isSetExt();
    boolean that_present_ext = true && that.isSetExt();
    if (this_present_ext || that_present_ext) {
      if (!(this_present_ext && that_present_ext))
        return false;
      if (!this.ext.equals(that.ext))
        return false;
    }

    boolean this_present_pid = true && this.isSetPid();
    boolean that_present_pid = true && that.isSetPid();
    if (this_present_pid || that_present_pid) {
      if (!(this_present_pid && that_present_pid))
        return false;
      if (!this.pid.equals(that.pid))
        return false;
    }

    boolean this_present_globalId = true && this.isSetGlobalId();
    boolean that_present_globalId = true && that.isSetGlobalId();
    if (this_present_globalId || that_present_globalId) {
      if (!(this_present_globalId && that_present_globalId))
        return false;
      if (!this.globalId.equals(that.globalId))
        return false;
    }

    boolean this_present_ip = true && this.isSetIp();
    boolean that_present_ip = true && that.isSetIp();
    if (this_present_ip || that_present_ip) {
      if (!(this_present_ip && that_present_ip))
        return false;
      if (!this.ip.equals(that.ip))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_senderDepartmentId = true && (isSetSenderDepartmentId());
    list.add(present_senderDepartmentId);
    if (present_senderDepartmentId)
      list.add(senderDepartmentId);

    boolean present_senderProductId = true && (isSetSenderProductId());
    list.add(present_senderProductId);
    if (present_senderProductId)
      list.add(senderProductId);

    boolean present_senderMinorProductId = true && (isSetSenderMinorProductId());
    list.add(present_senderMinorProductId);
    if (present_senderMinorProductId)
      list.add(senderMinorProductId);

    boolean present_senderHardwarePlatform = true && (isSetSenderHardwarePlatform());
    list.add(present_senderHardwarePlatform);
    if (present_senderHardwarePlatform)
      list.add(senderHardwarePlatform);

    boolean present_senderChannelId = true && (isSetSenderChannelId());
    list.add(present_senderChannelId);
    if (present_senderChannelId)
      list.add(senderChannelId);

    boolean present_senderSubChannelId = true && (isSetSenderSubChannelId());
    list.add(present_senderSubChannelId);
    if (present_senderSubChannelId)
      list.add(senderSubChannelId);

    boolean present_receiverDepartmentId = true && (isSetReceiverDepartmentId());
    list.add(present_receiverDepartmentId);
    if (present_receiverDepartmentId)
      list.add(receiverDepartmentId);

    boolean present_accountChangeType = true && (isSetAccountChangeType());
    list.add(present_accountChangeType);
    if (present_accountChangeType)
      list.add(accountChangeType);

    boolean present_fxcChangeDesc = true && (isSetFxcChangeDesc());
    list.add(present_fxcChangeDesc);
    if (present_fxcChangeDesc)
      list.add(fxcChangeDesc);

    boolean present_coin = true && (isSetCoin());
    list.add(present_coin);
    if (present_coin)
      list.add(coin);

    boolean present_fromKugouId = true && (isSetFromKugouId());
    list.add(present_fromKugouId);
    if (present_fromKugouId)
      list.add(fromKugouId);

    boolean present_toKugouId = true && (isSetToKugouId());
    list.add(present_toKugouId);
    if (present_toKugouId)
      list.add(toKugouId);

    boolean present_roomId = true && (isSetRoomId());
    list.add(present_roomId);
    if (present_roomId)
      list.add(roomId);

    boolean present_giftId = true && (isSetGiftId());
    list.add(present_giftId);
    if (present_giftId)
      list.add(giftId);

    boolean present_giftName = true && (isSetGiftName());
    list.add(present_giftName);
    if (present_giftName)
      list.add(giftName);

    boolean present_giftNum = true && (isSetGiftNum());
    list.add(present_giftNum);
    if (present_giftNum)
      list.add(giftNum);

    boolean present_actionId = true && (isSetActionId());
    list.add(present_actionId);
    if (present_actionId)
      list.add(actionId);

    boolean present_ext = true && (isSetExt());
    list.add(present_ext);
    if (present_ext)
      list.add(ext);

    boolean present_pid = true && (isSetPid());
    list.add(present_pid);
    if (present_pid)
      list.add(pid);

    boolean present_globalId = true && (isSetGlobalId());
    list.add(present_globalId);
    if (present_globalId)
      list.add(globalId);

    boolean present_ip = true && (isSetIp());
    list.add(present_ip);
    if (present_ip)
      list.add(ip);

    return list.hashCode();
  }

  @Override
  public int compareTo(UserFundPlatParam other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetSenderDepartmentId()).compareTo(other.isSetSenderDepartmentId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSenderDepartmentId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.senderDepartmentId, other.senderDepartmentId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSenderProductId()).compareTo(other.isSetSenderProductId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSenderProductId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.senderProductId, other.senderProductId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSenderMinorProductId()).compareTo(other.isSetSenderMinorProductId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSenderMinorProductId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.senderMinorProductId, other.senderMinorProductId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSenderHardwarePlatform()).compareTo(other.isSetSenderHardwarePlatform());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSenderHardwarePlatform()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.senderHardwarePlatform, other.senderHardwarePlatform);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSenderChannelId()).compareTo(other.isSetSenderChannelId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSenderChannelId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.senderChannelId, other.senderChannelId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSenderSubChannelId()).compareTo(other.isSetSenderSubChannelId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSenderSubChannelId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.senderSubChannelId, other.senderSubChannelId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetReceiverDepartmentId()).compareTo(other.isSetReceiverDepartmentId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetReceiverDepartmentId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.receiverDepartmentId, other.receiverDepartmentId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAccountChangeType()).compareTo(other.isSetAccountChangeType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAccountChangeType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.accountChangeType, other.accountChangeType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetFxcChangeDesc()).compareTo(other.isSetFxcChangeDesc());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFxcChangeDesc()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.fxcChangeDesc, other.fxcChangeDesc);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCoin()).compareTo(other.isSetCoin());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCoin()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.coin, other.coin);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetFromKugouId()).compareTo(other.isSetFromKugouId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFromKugouId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.fromKugouId, other.fromKugouId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetToKugouId()).compareTo(other.isSetToKugouId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetToKugouId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.toKugouId, other.toKugouId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoomId()).compareTo(other.isSetRoomId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoomId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roomId, other.roomId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGiftId()).compareTo(other.isSetGiftId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGiftId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.giftId, other.giftId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGiftName()).compareTo(other.isSetGiftName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGiftName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.giftName, other.giftName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGiftNum()).compareTo(other.isSetGiftNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGiftNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.giftNum, other.giftNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetActionId()).compareTo(other.isSetActionId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActionId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actionId, other.actionId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExt()).compareTo(other.isSetExt());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExt()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ext, other.ext);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPid()).compareTo(other.isSetPid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pid, other.pid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGlobalId()).compareTo(other.isSetGlobalId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGlobalId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.globalId, other.globalId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIp()).compareTo(other.isSetIp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ip, other.ip);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("UserFundPlatParam(");
    boolean first = true;

    sb.append("senderDepartmentId:");
    if (this.senderDepartmentId == null) {
      sb.append("null");
    } else {
      sb.append(this.senderDepartmentId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("senderProductId:");
    if (this.senderProductId == null) {
      sb.append("null");
    } else {
      sb.append(this.senderProductId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("senderMinorProductId:");
    if (this.senderMinorProductId == null) {
      sb.append("null");
    } else {
      sb.append(this.senderMinorProductId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("senderHardwarePlatform:");
    if (this.senderHardwarePlatform == null) {
      sb.append("null");
    } else {
      sb.append(this.senderHardwarePlatform);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("senderChannelId:");
    if (this.senderChannelId == null) {
      sb.append("null");
    } else {
      sb.append(this.senderChannelId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("senderSubChannelId:");
    if (this.senderSubChannelId == null) {
      sb.append("null");
    } else {
      sb.append(this.senderSubChannelId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("receiverDepartmentId:");
    if (this.receiverDepartmentId == null) {
      sb.append("null");
    } else {
      sb.append(this.receiverDepartmentId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("accountChangeType:");
    if (this.accountChangeType == null) {
      sb.append("null");
    } else {
      sb.append(this.accountChangeType);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("fxcChangeDesc:");
    if (this.fxcChangeDesc == null) {
      sb.append("null");
    } else {
      sb.append(this.fxcChangeDesc);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("coin:");
    if (this.coin == null) {
      sb.append("null");
    } else {
      sb.append(this.coin);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("fromKugouId:");
    if (this.fromKugouId == null) {
      sb.append("null");
    } else {
      sb.append(this.fromKugouId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("toKugouId:");
    if (this.toKugouId == null) {
      sb.append("null");
    } else {
      sb.append(this.toKugouId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("roomId:");
    if (this.roomId == null) {
      sb.append("null");
    } else {
      sb.append(this.roomId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("giftId:");
    if (this.giftId == null) {
      sb.append("null");
    } else {
      sb.append(this.giftId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("giftName:");
    if (this.giftName == null) {
      sb.append("null");
    } else {
      sb.append(this.giftName);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("giftNum:");
    if (this.giftNum == null) {
      sb.append("null");
    } else {
      sb.append(this.giftNum);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("actionId:");
    if (this.actionId == null) {
      sb.append("null");
    } else {
      sb.append(this.actionId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("ext:");
    if (this.ext == null) {
      sb.append("null");
    } else {
      sb.append(this.ext);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("pid:");
    if (this.pid == null) {
      sb.append("null");
    } else {
      sb.append(this.pid);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("globalId:");
    if (this.globalId == null) {
      sb.append("null");
    } else {
      sb.append(this.globalId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("ip:");
    if (this.ip == null) {
      sb.append("null");
    } else {
      sb.append(this.ip);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws TException {
    // check for required fields
    if (senderDepartmentId == null) {
      throw new TProtocolException("Required field 'senderDepartmentId' was not present! Struct: " + toString());
    }
    if (senderProductId == null) {
      throw new TProtocolException("Required field 'senderProductId' was not present! Struct: " + toString());
    }
    if (senderMinorProductId == null) {
      throw new TProtocolException("Required field 'senderMinorProductId' was not present! Struct: " + toString());
    }
    if (senderHardwarePlatform == null) {
      throw new TProtocolException("Required field 'senderHardwarePlatform' was not present! Struct: " + toString());
    }
    if (senderChannelId == null) {
      throw new TProtocolException("Required field 'senderChannelId' was not present! Struct: " + toString());
    }
    if (senderSubChannelId == null) {
      throw new TProtocolException("Required field 'senderSubChannelId' was not present! Struct: " + toString());
    }
    if (receiverDepartmentId == null) {
      throw new TProtocolException("Required field 'receiverDepartmentId' was not present! Struct: " + toString());
    }
    if (accountChangeType == null) {
      throw new TProtocolException("Required field 'accountChangeType' was not present! Struct: " + toString());
    }
    if (fxcChangeDesc == null) {
      throw new TProtocolException("Required field 'fxcChangeDesc' was not present! Struct: " + toString());
    }
    if (coin == null) {
      throw new TProtocolException("Required field 'coin' was not present! Struct: " + toString());
    }
    if (fromKugouId == null) {
      throw new TProtocolException("Required field 'fromKugouId' was not present! Struct: " + toString());
    }
    if (toKugouId == null) {
      throw new TProtocolException("Required field 'toKugouId' was not present! Struct: " + toString());
    }
    if (roomId == null) {
      throw new TProtocolException("Required field 'roomId' was not present! Struct: " + toString());
    }
    if (giftId == null) {
      throw new TProtocolException("Required field 'giftId' was not present! Struct: " + toString());
    }
    if (giftName == null) {
      throw new TProtocolException("Required field 'giftName' was not present! Struct: " + toString());
    }
    if (giftNum == null) {
      throw new TProtocolException("Required field 'giftNum' was not present! Struct: " + toString());
    }
    if (actionId == null) {
      throw new TProtocolException("Required field 'actionId' was not present! Struct: " + toString());
    }
    if (ext == null) {
      throw new TProtocolException("Required field 'ext' was not present! Struct: " + toString());
    }
    if (pid == null) {
      throw new TProtocolException("Required field 'pid' was not present! Struct: " + toString());
    }
    if (globalId == null) {
      throw new TProtocolException("Required field 'globalId' was not present! Struct: " + toString());
    }
    if (ip == null) {
      throw new TProtocolException("Required field 'ip' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class UserFundPlatParamStandardSchemeFactory implements SchemeFactory {
    public UserFundPlatParamStandardScheme getScheme() {
      return new UserFundPlatParamStandardScheme();
    }
  }

  private static class UserFundPlatParamStandardScheme extends StandardScheme<UserFundPlatParam> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, UserFundPlatParam struct) throws TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // SENDER_DEPARTMENT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.senderDepartmentId = iprot.readString();
              struct.setSenderDepartmentIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // SENDER_PRODUCT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.senderProductId = iprot.readString();
              struct.setSenderProductIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // SENDER_MINOR_PRODUCT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.senderMinorProductId = iprot.readString();
              struct.setSenderMinorProductIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // SENDER_HARDWARE_PLATFORM
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.senderHardwarePlatform = iprot.readString();
              struct.setSenderHardwarePlatformIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // SENDER_CHANNEL_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.senderChannelId = iprot.readString();
              struct.setSenderChannelIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // SENDER_SUB_CHANNEL_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.senderSubChannelId = iprot.readString();
              struct.setSenderSubChannelIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // RECEIVER_DEPARTMENT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.receiverDepartmentId = iprot.readString();
              struct.setReceiverDepartmentIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // ACCOUNT_CHANGE_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.accountChangeType = iprot.readString();
              struct.setAccountChangeTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // FXC_CHANGE_DESC
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.fxcChangeDesc = iprot.readString();
              struct.setFxcChangeDescIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // COIN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.coin = iprot.readString();
              struct.setCoinIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // FROM_KUGOU_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.fromKugouId = iprot.readString();
              struct.setFromKugouIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // TO_KUGOU_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.toKugouId = iprot.readString();
              struct.setToKugouIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // ROOM_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.roomId = iprot.readString();
              struct.setRoomIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 14: // GIFT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.giftId = iprot.readString();
              struct.setGiftIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // GIFT_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.giftName = iprot.readString();
              struct.setGiftNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 16: // GIFT_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.giftNum = iprot.readString();
              struct.setGiftNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 17: // ACTION_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.actionId = iprot.readString();
              struct.setActionIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 18: // EXT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.ext = iprot.readString();
              struct.setExtIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 19: // PID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.pid = iprot.readString();
              struct.setPidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 20: // GLOBAL_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.globalId = iprot.readString();
              struct.setGlobalIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 21: // IP
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.ip = iprot.readString();
              struct.setIpIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, UserFundPlatParam struct) throws TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.senderDepartmentId != null) {
        oprot.writeFieldBegin(SENDER_DEPARTMENT_ID_FIELD_DESC);
        oprot.writeString(struct.senderDepartmentId);
        oprot.writeFieldEnd();
      }
      if (struct.senderProductId != null) {
        oprot.writeFieldBegin(SENDER_PRODUCT_ID_FIELD_DESC);
        oprot.writeString(struct.senderProductId);
        oprot.writeFieldEnd();
      }
      if (struct.senderMinorProductId != null) {
        oprot.writeFieldBegin(SENDER_MINOR_PRODUCT_ID_FIELD_DESC);
        oprot.writeString(struct.senderMinorProductId);
        oprot.writeFieldEnd();
      }
      if (struct.senderHardwarePlatform != null) {
        oprot.writeFieldBegin(SENDER_HARDWARE_PLATFORM_FIELD_DESC);
        oprot.writeString(struct.senderHardwarePlatform);
        oprot.writeFieldEnd();
      }
      if (struct.senderChannelId != null) {
        oprot.writeFieldBegin(SENDER_CHANNEL_ID_FIELD_DESC);
        oprot.writeString(struct.senderChannelId);
        oprot.writeFieldEnd();
      }
      if (struct.senderSubChannelId != null) {
        oprot.writeFieldBegin(SENDER_SUB_CHANNEL_ID_FIELD_DESC);
        oprot.writeString(struct.senderSubChannelId);
        oprot.writeFieldEnd();
      }
      if (struct.receiverDepartmentId != null) {
        oprot.writeFieldBegin(RECEIVER_DEPARTMENT_ID_FIELD_DESC);
        oprot.writeString(struct.receiverDepartmentId);
        oprot.writeFieldEnd();
      }
      if (struct.accountChangeType != null) {
        oprot.writeFieldBegin(ACCOUNT_CHANGE_TYPE_FIELD_DESC);
        oprot.writeString(struct.accountChangeType);
        oprot.writeFieldEnd();
      }
      if (struct.fxcChangeDesc != null) {
        oprot.writeFieldBegin(FXC_CHANGE_DESC_FIELD_DESC);
        oprot.writeString(struct.fxcChangeDesc);
        oprot.writeFieldEnd();
      }
      if (struct.coin != null) {
        oprot.writeFieldBegin(COIN_FIELD_DESC);
        oprot.writeString(struct.coin);
        oprot.writeFieldEnd();
      }
      if (struct.fromKugouId != null) {
        oprot.writeFieldBegin(FROM_KUGOU_ID_FIELD_DESC);
        oprot.writeString(struct.fromKugouId);
        oprot.writeFieldEnd();
      }
      if (struct.toKugouId != null) {
        oprot.writeFieldBegin(TO_KUGOU_ID_FIELD_DESC);
        oprot.writeString(struct.toKugouId);
        oprot.writeFieldEnd();
      }
      if (struct.roomId != null) {
        oprot.writeFieldBegin(ROOM_ID_FIELD_DESC);
        oprot.writeString(struct.roomId);
        oprot.writeFieldEnd();
      }
      if (struct.giftId != null) {
        oprot.writeFieldBegin(GIFT_ID_FIELD_DESC);
        oprot.writeString(struct.giftId);
        oprot.writeFieldEnd();
      }
      if (struct.giftName != null) {
        oprot.writeFieldBegin(GIFT_NAME_FIELD_DESC);
        oprot.writeString(struct.giftName);
        oprot.writeFieldEnd();
      }
      if (struct.giftNum != null) {
        oprot.writeFieldBegin(GIFT_NUM_FIELD_DESC);
        oprot.writeString(struct.giftNum);
        oprot.writeFieldEnd();
      }
      if (struct.actionId != null) {
        oprot.writeFieldBegin(ACTION_ID_FIELD_DESC);
        oprot.writeString(struct.actionId);
        oprot.writeFieldEnd();
      }
      if (struct.ext != null) {
        oprot.writeFieldBegin(EXT_FIELD_DESC);
        oprot.writeString(struct.ext);
        oprot.writeFieldEnd();
      }
      if (struct.pid != null) {
        oprot.writeFieldBegin(PID_FIELD_DESC);
        oprot.writeString(struct.pid);
        oprot.writeFieldEnd();
      }
      if (struct.globalId != null) {
        oprot.writeFieldBegin(GLOBAL_ID_FIELD_DESC);
        oprot.writeString(struct.globalId);
        oprot.writeFieldEnd();
      }
      if (struct.ip != null) {
        oprot.writeFieldBegin(IP_FIELD_DESC);
        oprot.writeString(struct.ip);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class UserFundPlatParamTupleSchemeFactory implements SchemeFactory {
    public UserFundPlatParamTupleScheme getScheme() {
      return new UserFundPlatParamTupleScheme();
    }
  }

  private static class UserFundPlatParamTupleScheme extends TupleScheme<UserFundPlatParam> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, UserFundPlatParam struct) throws TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeString(struct.senderDepartmentId);
      oprot.writeString(struct.senderProductId);
      oprot.writeString(struct.senderMinorProductId);
      oprot.writeString(struct.senderHardwarePlatform);
      oprot.writeString(struct.senderChannelId);
      oprot.writeString(struct.senderSubChannelId);
      oprot.writeString(struct.receiverDepartmentId);
      oprot.writeString(struct.accountChangeType);
      oprot.writeString(struct.fxcChangeDesc);
      oprot.writeString(struct.coin);
      oprot.writeString(struct.fromKugouId);
      oprot.writeString(struct.toKugouId);
      oprot.writeString(struct.roomId);
      oprot.writeString(struct.giftId);
      oprot.writeString(struct.giftName);
      oprot.writeString(struct.giftNum);
      oprot.writeString(struct.actionId);
      oprot.writeString(struct.ext);
      oprot.writeString(struct.pid);
      oprot.writeString(struct.globalId);
      oprot.writeString(struct.ip);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, UserFundPlatParam struct) throws TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.senderDepartmentId = iprot.readString();
      struct.setSenderDepartmentIdIsSet(true);
      struct.senderProductId = iprot.readString();
      struct.setSenderProductIdIsSet(true);
      struct.senderMinorProductId = iprot.readString();
      struct.setSenderMinorProductIdIsSet(true);
      struct.senderHardwarePlatform = iprot.readString();
      struct.setSenderHardwarePlatformIsSet(true);
      struct.senderChannelId = iprot.readString();
      struct.setSenderChannelIdIsSet(true);
      struct.senderSubChannelId = iprot.readString();
      struct.setSenderSubChannelIdIsSet(true);
      struct.receiverDepartmentId = iprot.readString();
      struct.setReceiverDepartmentIdIsSet(true);
      struct.accountChangeType = iprot.readString();
      struct.setAccountChangeTypeIsSet(true);
      struct.fxcChangeDesc = iprot.readString();
      struct.setFxcChangeDescIsSet(true);
      struct.coin = iprot.readString();
      struct.setCoinIsSet(true);
      struct.fromKugouId = iprot.readString();
      struct.setFromKugouIdIsSet(true);
      struct.toKugouId = iprot.readString();
      struct.setToKugouIdIsSet(true);
      struct.roomId = iprot.readString();
      struct.setRoomIdIsSet(true);
      struct.giftId = iprot.readString();
      struct.setGiftIdIsSet(true);
      struct.giftName = iprot.readString();
      struct.setGiftNameIsSet(true);
      struct.giftNum = iprot.readString();
      struct.setGiftNumIsSet(true);
      struct.actionId = iprot.readString();
      struct.setActionIdIsSet(true);
      struct.ext = iprot.readString();
      struct.setExtIsSet(true);
      struct.pid = iprot.readString();
      struct.setPidIsSet(true);
      struct.globalId = iprot.readString();
      struct.setGlobalIdIsSet(true);
      struct.ip = iprot.readString();
      struct.setIpIsSet(true);
    }
  }

}

