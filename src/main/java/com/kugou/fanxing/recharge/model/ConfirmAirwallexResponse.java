package com.kugou.fanxing.recharge.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * https://www.airwallex.com/docs/api#/Payment_Acceptance/Payment_Intents/_api_v1_pa_payment_intents_create/post
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfirmAirwallexResponse {
    private int error_code;
    private String error_msg;
    private int status;
    private ConfirmAirwallexV1 data;
}
