/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.recharge.model.request;

import com.kugou.fanxing.recharge.thrift.UserFundPlatParam;
import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2024-03-21")
public class WxPayAndContractRequest implements org.apache.thrift.TBase<WxPayAndContractRequest, WxPayAndContractRequest._Fields>, java.io.Serializable, Cloneable, Comparable<WxPayAndContractRequest> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("WxPayAndContractRequest");

  private static final org.apache.thrift.protocol.TField ACCESS_TOKEN_FIELD_DESC = new org.apache.thrift.protocol.TField("accessToken", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField BIZ_SIGN_FIELD_DESC = new org.apache.thrift.protocol.TField("bizSign", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField AMOUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("amount", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField FROM_KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("fromKugouId", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField TOKUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("tokugouId", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField BUSINESS_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("businessId", org.apache.thrift.protocol.TType.STRING, (short)6);
  private static final org.apache.thrift.protocol.TField BUSINESS_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("businessTime", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField SUBJECT_FIELD_DESC = new org.apache.thrift.protocol.TField("subject", org.apache.thrift.protocol.TType.STRING, (short)8);
  private static final org.apache.thrift.protocol.TField SYNC_URL_FIELD_DESC = new org.apache.thrift.protocol.TField("syncUrl", org.apache.thrift.protocol.TType.STRING, (short)9);
  private static final org.apache.thrift.protocol.TField CLIENT_IP_FIELD_DESC = new org.apache.thrift.protocol.TField("clientIp", org.apache.thrift.protocol.TType.STRING, (short)10);
  private static final org.apache.thrift.protocol.TField REFER_FIELD_DESC = new org.apache.thrift.protocol.TField("refer", org.apache.thrift.protocol.TType.I32, (short)11);
  private static final org.apache.thrift.protocol.TField C_FROM_FIELD_DESC = new org.apache.thrift.protocol.TField("cFrom", org.apache.thrift.protocol.TType.I32, (short)12);
  private static final org.apache.thrift.protocol.TField PID_FIELD_DESC = new org.apache.thrift.protocol.TField("pid", org.apache.thrift.protocol.TType.I32, (short)13);
  private static final org.apache.thrift.protocol.TField CHANNEL_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("channelId", org.apache.thrift.protocol.TType.I32, (short)14);
  private static final org.apache.thrift.protocol.TField USER_FUND_PLAT_PARAM_FIELD_DESC = new org.apache.thrift.protocol.TField("userFundPlatParam", org.apache.thrift.protocol.TType.STRUCT, (short)15);
  private static final org.apache.thrift.protocol.TField BIZ_APP_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("bizAppId", org.apache.thrift.protocol.TType.I32, (short)16);
  private static final org.apache.thrift.protocol.TField PROTOCOL_FIELD_DESC = new org.apache.thrift.protocol.TField("protocol", org.apache.thrift.protocol.TType.STRING, (short)17);
  private static final org.apache.thrift.protocol.TField EXT_FIELD_DESC = new org.apache.thrift.protocol.TField("ext", org.apache.thrift.protocol.TType.STRING, (short)18);
  private static final org.apache.thrift.protocol.TField BUSINESS_EXT_FIELD_DESC = new org.apache.thrift.protocol.TField("businessExt", org.apache.thrift.protocol.TType.STRING, (short)19);
  private static final org.apache.thrift.protocol.TField BUSINESS_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("businessType", org.apache.thrift.protocol.TType.STRING, (short)20);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new WxPayAndContractRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new WxPayAndContractRequestTupleSchemeFactory());
  }

  public String accessToken; // required
  public String bizSign; // required
  public String amount; // required
  public long fromKugouId; // required
  public long tokugouId; // required
  public String businessId; // required
  public long businessTime; // required
  public String subject; // required
  public String syncUrl; // required
  public String clientIp; // required
  public int refer; // required
  public int cFrom; // required
  public int pid; // required
  public int channelId; // required
  public UserFundPlatParam userFundPlatParam; // required
  public int bizAppId; // required
  public String protocol; // required
  public String ext; // required
  public String businessExt; // required
  public String businessType; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ACCESS_TOKEN((short)1, "accessToken"),
    BIZ_SIGN((short)2, "bizSign"),
    AMOUNT((short)3, "amount"),
    FROM_KUGOU_ID((short)4, "fromKugouId"),
    TOKUGOU_ID((short)5, "tokugouId"),
    BUSINESS_ID((short)6, "businessId"),
    BUSINESS_TIME((short)7, "businessTime"),
    SUBJECT((short)8, "subject"),
    SYNC_URL((short)9, "syncUrl"),
    CLIENT_IP((short)10, "clientIp"),
    REFER((short)11, "refer"),
    C_FROM((short)12, "cFrom"),
    PID((short)13, "pid"),
    CHANNEL_ID((short)14, "channelId"),
    USER_FUND_PLAT_PARAM((short)15, "userFundPlatParam"),
    BIZ_APP_ID((short)16, "bizAppId"),
    PROTOCOL((short)17, "protocol"),
    EXT((short)18, "ext"),
    BUSINESS_EXT((short)19, "businessExt"),
    BUSINESS_TYPE((short)20, "businessType");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACCESS_TOKEN
          return ACCESS_TOKEN;
        case 2: // BIZ_SIGN
          return BIZ_SIGN;
        case 3: // AMOUNT
          return AMOUNT;
        case 4: // FROM_KUGOU_ID
          return FROM_KUGOU_ID;
        case 5: // TOKUGOU_ID
          return TOKUGOU_ID;
        case 6: // BUSINESS_ID
          return BUSINESS_ID;
        case 7: // BUSINESS_TIME
          return BUSINESS_TIME;
        case 8: // SUBJECT
          return SUBJECT;
        case 9: // SYNC_URL
          return SYNC_URL;
        case 10: // CLIENT_IP
          return CLIENT_IP;
        case 11: // REFER
          return REFER;
        case 12: // C_FROM
          return C_FROM;
        case 13: // PID
          return PID;
        case 14: // CHANNEL_ID
          return CHANNEL_ID;
        case 15: // USER_FUND_PLAT_PARAM
          return USER_FUND_PLAT_PARAM;
        case 16: // BIZ_APP_ID
          return BIZ_APP_ID;
        case 17: // PROTOCOL
          return PROTOCOL;
        case 18: // EXT
          return EXT;
        case 19: // BUSINESS_EXT
          return BUSINESS_EXT;
        case 20: // BUSINESS_TYPE
          return BUSINESS_TYPE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __FROMKUGOUID_ISSET_ID = 0;
  private static final int __TOKUGOUID_ISSET_ID = 1;
  private static final int __BUSINESSTIME_ISSET_ID = 2;
  private static final int __REFER_ISSET_ID = 3;
  private static final int __CFROM_ISSET_ID = 4;
  private static final int __PID_ISSET_ID = 5;
  private static final int __CHANNELID_ISSET_ID = 6;
  private static final int __BIZAPPID_ISSET_ID = 7;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACCESS_TOKEN, new org.apache.thrift.meta_data.FieldMetaData("accessToken", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BIZ_SIGN, new org.apache.thrift.meta_data.FieldMetaData("bizSign", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.AMOUNT, new org.apache.thrift.meta_data.FieldMetaData("amount", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.FROM_KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("fromKugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TOKUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("tokugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.BUSINESS_ID, new org.apache.thrift.meta_data.FieldMetaData("businessId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BUSINESS_TIME, new org.apache.thrift.meta_data.FieldMetaData("businessTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SUBJECT, new org.apache.thrift.meta_data.FieldMetaData("subject", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SYNC_URL, new org.apache.thrift.meta_data.FieldMetaData("syncUrl", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CLIENT_IP, new org.apache.thrift.meta_data.FieldMetaData("clientIp", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.REFER, new org.apache.thrift.meta_data.FieldMetaData("refer", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.C_FROM, new org.apache.thrift.meta_data.FieldMetaData("cFrom", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.PID, new org.apache.thrift.meta_data.FieldMetaData("pid", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.CHANNEL_ID, new org.apache.thrift.meta_data.FieldMetaData("channelId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.USER_FUND_PLAT_PARAM, new org.apache.thrift.meta_data.FieldMetaData("userFundPlatParam", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT        , "UserFundPlatParam")));
    tmpMap.put(_Fields.BIZ_APP_ID, new org.apache.thrift.meta_data.FieldMetaData("bizAppId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.PROTOCOL, new org.apache.thrift.meta_data.FieldMetaData("protocol", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT, new org.apache.thrift.meta_data.FieldMetaData("ext", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BUSINESS_EXT, new org.apache.thrift.meta_data.FieldMetaData("businessExt", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BUSINESS_TYPE, new org.apache.thrift.meta_data.FieldMetaData("businessType", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(WxPayAndContractRequest.class, metaDataMap);
  }

  public WxPayAndContractRequest() {
  }

  public WxPayAndContractRequest(
    String accessToken,
    String bizSign,
    String amount,
    long fromKugouId,
    long tokugouId,
    String businessId,
    long businessTime,
    String subject,
    String syncUrl,
    String clientIp,
    int refer,
    int cFrom,
    int pid,
    int channelId,
    UserFundPlatParam userFundPlatParam,
    int bizAppId,
    String protocol,
    String ext,
    String businessExt,
    String businessType)
  {
    this();
    this.accessToken = accessToken;
    this.bizSign = bizSign;
    this.amount = amount;
    this.fromKugouId = fromKugouId;
    setFromKugouIdIsSet(true);
    this.tokugouId = tokugouId;
    setTokugouIdIsSet(true);
    this.businessId = businessId;
    this.businessTime = businessTime;
    setBusinessTimeIsSet(true);
    this.subject = subject;
    this.syncUrl = syncUrl;
    this.clientIp = clientIp;
    this.refer = refer;
    setReferIsSet(true);
    this.cFrom = cFrom;
    setCFromIsSet(true);
    this.pid = pid;
    setPidIsSet(true);
    this.channelId = channelId;
    setChannelIdIsSet(true);
    this.userFundPlatParam = userFundPlatParam;
    this.bizAppId = bizAppId;
    setBizAppIdIsSet(true);
    this.protocol = protocol;
    this.ext = ext;
    this.businessExt = businessExt;
    this.businessType = businessType;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public WxPayAndContractRequest(WxPayAndContractRequest other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetAccessToken()) {
      this.accessToken = other.accessToken;
    }
    if (other.isSetBizSign()) {
      this.bizSign = other.bizSign;
    }
    if (other.isSetAmount()) {
      this.amount = other.amount;
    }
    this.fromKugouId = other.fromKugouId;
    this.tokugouId = other.tokugouId;
    if (other.isSetBusinessId()) {
      this.businessId = other.businessId;
    }
    this.businessTime = other.businessTime;
    if (other.isSetSubject()) {
      this.subject = other.subject;
    }
    if (other.isSetSyncUrl()) {
      this.syncUrl = other.syncUrl;
    }
    if (other.isSetClientIp()) {
      this.clientIp = other.clientIp;
    }
    this.refer = other.refer;
    this.cFrom = other.cFrom;
    this.pid = other.pid;
    this.channelId = other.channelId;
    if (other.isSetUserFundPlatParam()) {
      this.userFundPlatParam = other.userFundPlatParam;
    }
    this.bizAppId = other.bizAppId;
    if (other.isSetProtocol()) {
      this.protocol = other.protocol;
    }
    if (other.isSetExt()) {
      this.ext = other.ext;
    }
    if (other.isSetBusinessExt()) {
      this.businessExt = other.businessExt;
    }
    if (other.isSetBusinessType()) {
      this.businessType = other.businessType;
    }
  }

  public WxPayAndContractRequest deepCopy() {
    return new WxPayAndContractRequest(this);
  }

  @Override
  public void clear() {
    this.accessToken = null;
    this.bizSign = null;
    this.amount = null;
    setFromKugouIdIsSet(false);
    this.fromKugouId = 0;
    setTokugouIdIsSet(false);
    this.tokugouId = 0;
    this.businessId = null;
    setBusinessTimeIsSet(false);
    this.businessTime = 0;
    this.subject = null;
    this.syncUrl = null;
    this.clientIp = null;
    setReferIsSet(false);
    this.refer = 0;
    setCFromIsSet(false);
    this.cFrom = 0;
    setPidIsSet(false);
    this.pid = 0;
    setChannelIdIsSet(false);
    this.channelId = 0;
    this.userFundPlatParam = null;
    setBizAppIdIsSet(false);
    this.bizAppId = 0;
    this.protocol = null;
    this.ext = null;
    this.businessExt = null;
    this.businessType = null;
  }

  public String getAccessToken() {
    return this.accessToken;
  }

  public WxPayAndContractRequest setAccessToken(String accessToken) {
    this.accessToken = accessToken;
    return this;
  }

  public void unsetAccessToken() {
    this.accessToken = null;
  }

  /** Returns true if field accessToken is set (has been assigned a value) and false otherwise */
  public boolean isSetAccessToken() {
    return this.accessToken != null;
  }

  public void setAccessTokenIsSet(boolean value) {
    if (!value) {
      this.accessToken = null;
    }
  }

  public String getBizSign() {
    return this.bizSign;
  }

  public WxPayAndContractRequest setBizSign(String bizSign) {
    this.bizSign = bizSign;
    return this;
  }

  public void unsetBizSign() {
    this.bizSign = null;
  }

  /** Returns true if field bizSign is set (has been assigned a value) and false otherwise */
  public boolean isSetBizSign() {
    return this.bizSign != null;
  }

  public void setBizSignIsSet(boolean value) {
    if (!value) {
      this.bizSign = null;
    }
  }

  public String getAmount() {
    return this.amount;
  }

  public WxPayAndContractRequest setAmount(String amount) {
    this.amount = amount;
    return this;
  }

  public void unsetAmount() {
    this.amount = null;
  }

  /** Returns true if field amount is set (has been assigned a value) and false otherwise */
  public boolean isSetAmount() {
    return this.amount != null;
  }

  public void setAmountIsSet(boolean value) {
    if (!value) {
      this.amount = null;
    }
  }

  public long getFromKugouId() {
    return this.fromKugouId;
  }

  public WxPayAndContractRequest setFromKugouId(long fromKugouId) {
    this.fromKugouId = fromKugouId;
    setFromKugouIdIsSet(true);
    return this;
  }

  public void unsetFromKugouId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __FROMKUGOUID_ISSET_ID);
  }

  /** Returns true if field fromKugouId is set (has been assigned a value) and false otherwise */
  public boolean isSetFromKugouId() {
    return EncodingUtils.testBit(__isset_bitfield, __FROMKUGOUID_ISSET_ID);
  }

  public void setFromKugouIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __FROMKUGOUID_ISSET_ID, value);
  }

  public long getTokugouId() {
    return this.tokugouId;
  }

  public WxPayAndContractRequest setTokugouId(long tokugouId) {
    this.tokugouId = tokugouId;
    setTokugouIdIsSet(true);
    return this;
  }

  public void unsetTokugouId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TOKUGOUID_ISSET_ID);
  }

  /** Returns true if field tokugouId is set (has been assigned a value) and false otherwise */
  public boolean isSetTokugouId() {
    return EncodingUtils.testBit(__isset_bitfield, __TOKUGOUID_ISSET_ID);
  }

  public void setTokugouIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TOKUGOUID_ISSET_ID, value);
  }

  public String getBusinessId() {
    return this.businessId;
  }

  public WxPayAndContractRequest setBusinessId(String businessId) {
    this.businessId = businessId;
    return this;
  }

  public void unsetBusinessId() {
    this.businessId = null;
  }

  /** Returns true if field businessId is set (has been assigned a value) and false otherwise */
  public boolean isSetBusinessId() {
    return this.businessId != null;
  }

  public void setBusinessIdIsSet(boolean value) {
    if (!value) {
      this.businessId = null;
    }
  }

  public long getBusinessTime() {
    return this.businessTime;
  }

  public WxPayAndContractRequest setBusinessTime(long businessTime) {
    this.businessTime = businessTime;
    setBusinessTimeIsSet(true);
    return this;
  }

  public void unsetBusinessTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __BUSINESSTIME_ISSET_ID);
  }

  /** Returns true if field businessTime is set (has been assigned a value) and false otherwise */
  public boolean isSetBusinessTime() {
    return EncodingUtils.testBit(__isset_bitfield, __BUSINESSTIME_ISSET_ID);
  }

  public void setBusinessTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __BUSINESSTIME_ISSET_ID, value);
  }

  public String getSubject() {
    return this.subject;
  }

  public WxPayAndContractRequest setSubject(String subject) {
    this.subject = subject;
    return this;
  }

  public void unsetSubject() {
    this.subject = null;
  }

  /** Returns true if field subject is set (has been assigned a value) and false otherwise */
  public boolean isSetSubject() {
    return this.subject != null;
  }

  public void setSubjectIsSet(boolean value) {
    if (!value) {
      this.subject = null;
    }
  }

  public String getSyncUrl() {
    return this.syncUrl;
  }

  public WxPayAndContractRequest setSyncUrl(String syncUrl) {
    this.syncUrl = syncUrl;
    return this;
  }

  public void unsetSyncUrl() {
    this.syncUrl = null;
  }

  /** Returns true if field syncUrl is set (has been assigned a value) and false otherwise */
  public boolean isSetSyncUrl() {
    return this.syncUrl != null;
  }

  public void setSyncUrlIsSet(boolean value) {
    if (!value) {
      this.syncUrl = null;
    }
  }

  public String getClientIp() {
    return this.clientIp;
  }

  public WxPayAndContractRequest setClientIp(String clientIp) {
    this.clientIp = clientIp;
    return this;
  }

  public void unsetClientIp() {
    this.clientIp = null;
  }

  /** Returns true if field clientIp is set (has been assigned a value) and false otherwise */
  public boolean isSetClientIp() {
    return this.clientIp != null;
  }

  public void setClientIpIsSet(boolean value) {
    if (!value) {
      this.clientIp = null;
    }
  }

  public int getRefer() {
    return this.refer;
  }

  public WxPayAndContractRequest setRefer(int refer) {
    this.refer = refer;
    setReferIsSet(true);
    return this;
  }

  public void unsetRefer() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __REFER_ISSET_ID);
  }

  /** Returns true if field refer is set (has been assigned a value) and false otherwise */
  public boolean isSetRefer() {
    return EncodingUtils.testBit(__isset_bitfield, __REFER_ISSET_ID);
  }

  public void setReferIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __REFER_ISSET_ID, value);
  }

  public int getCFrom() {
    return this.cFrom;
  }

  public WxPayAndContractRequest setCFrom(int cFrom) {
    this.cFrom = cFrom;
    setCFromIsSet(true);
    return this;
  }

  public void unsetCFrom() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CFROM_ISSET_ID);
  }

  /** Returns true if field cFrom is set (has been assigned a value) and false otherwise */
  public boolean isSetCFrom() {
    return EncodingUtils.testBit(__isset_bitfield, __CFROM_ISSET_ID);
  }

  public void setCFromIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CFROM_ISSET_ID, value);
  }

  public int getPid() {
    return this.pid;
  }

  public WxPayAndContractRequest setPid(int pid) {
    this.pid = pid;
    setPidIsSet(true);
    return this;
  }

  public void unsetPid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PID_ISSET_ID);
  }

  /** Returns true if field pid is set (has been assigned a value) and false otherwise */
  public boolean isSetPid() {
    return EncodingUtils.testBit(__isset_bitfield, __PID_ISSET_ID);
  }

  public void setPidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PID_ISSET_ID, value);
  }

  public int getChannelId() {
    return this.channelId;
  }

  public WxPayAndContractRequest setChannelId(int channelId) {
    this.channelId = channelId;
    setChannelIdIsSet(true);
    return this;
  }

  public void unsetChannelId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CHANNELID_ISSET_ID);
  }

  /** Returns true if field channelId is set (has been assigned a value) and false otherwise */
  public boolean isSetChannelId() {
    return EncodingUtils.testBit(__isset_bitfield, __CHANNELID_ISSET_ID);
  }

  public void setChannelIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CHANNELID_ISSET_ID, value);
  }

  public UserFundPlatParam getUserFundPlatParam() {
    return this.userFundPlatParam;
  }

  public WxPayAndContractRequest setUserFundPlatParam(UserFundPlatParam userFundPlatParam) {
    this.userFundPlatParam = userFundPlatParam;
    return this;
  }

  public void unsetUserFundPlatParam() {
    this.userFundPlatParam = null;
  }

  /** Returns true if field userFundPlatParam is set (has been assigned a value) and false otherwise */
  public boolean isSetUserFundPlatParam() {
    return this.userFundPlatParam != null;
  }

  public void setUserFundPlatParamIsSet(boolean value) {
    if (!value) {
      this.userFundPlatParam = null;
    }
  }

  public int getBizAppId() {
    return this.bizAppId;
  }

  public WxPayAndContractRequest setBizAppId(int bizAppId) {
    this.bizAppId = bizAppId;
    setBizAppIdIsSet(true);
    return this;
  }

  public void unsetBizAppId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __BIZAPPID_ISSET_ID);
  }

  /** Returns true if field bizAppId is set (has been assigned a value) and false otherwise */
  public boolean isSetBizAppId() {
    return EncodingUtils.testBit(__isset_bitfield, __BIZAPPID_ISSET_ID);
  }

  public void setBizAppIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __BIZAPPID_ISSET_ID, value);
  }

  public String getProtocol() {
    return this.protocol;
  }

  public WxPayAndContractRequest setProtocol(String protocol) {
    this.protocol = protocol;
    return this;
  }

  public void unsetProtocol() {
    this.protocol = null;
  }

  /** Returns true if field protocol is set (has been assigned a value) and false otherwise */
  public boolean isSetProtocol() {
    return this.protocol != null;
  }

  public void setProtocolIsSet(boolean value) {
    if (!value) {
      this.protocol = null;
    }
  }

  public String getExt() {
    return this.ext;
  }

  public WxPayAndContractRequest setExt(String ext) {
    this.ext = ext;
    return this;
  }

  public void unsetExt() {
    this.ext = null;
  }

  /** Returns true if field ext is set (has been assigned a value) and false otherwise */
  public boolean isSetExt() {
    return this.ext != null;
  }

  public void setExtIsSet(boolean value) {
    if (!value) {
      this.ext = null;
    }
  }

  public String getBusinessExt() {
    return this.businessExt;
  }

  public WxPayAndContractRequest setBusinessExt(String businessExt) {
    this.businessExt = businessExt;
    return this;
  }

  public void unsetBusinessExt() {
    this.businessExt = null;
  }

  /** Returns true if field businessExt is set (has been assigned a value) and false otherwise */
  public boolean isSetBusinessExt() {
    return this.businessExt != null;
  }

  public void setBusinessExtIsSet(boolean value) {
    if (!value) {
      this.businessExt = null;
    }
  }

  public String getBusinessType() {
    return this.businessType;
  }

  public WxPayAndContractRequest setBusinessType(String businessType) {
    this.businessType = businessType;
    return this;
  }

  public void unsetBusinessType() {
    this.businessType = null;
  }

  /** Returns true if field businessType is set (has been assigned a value) and false otherwise */
  public boolean isSetBusinessType() {
    return this.businessType != null;
  }

  public void setBusinessTypeIsSet(boolean value) {
    if (!value) {
      this.businessType = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ACCESS_TOKEN:
      if (value == null) {
        unsetAccessToken();
      } else {
        setAccessToken((String)value);
      }
      break;

    case BIZ_SIGN:
      if (value == null) {
        unsetBizSign();
      } else {
        setBizSign((String)value);
      }
      break;

    case AMOUNT:
      if (value == null) {
        unsetAmount();
      } else {
        setAmount((String)value);
      }
      break;

    case FROM_KUGOU_ID:
      if (value == null) {
        unsetFromKugouId();
      } else {
        setFromKugouId((Long)value);
      }
      break;

    case TOKUGOU_ID:
      if (value == null) {
        unsetTokugouId();
      } else {
        setTokugouId((Long)value);
      }
      break;

    case BUSINESS_ID:
      if (value == null) {
        unsetBusinessId();
      } else {
        setBusinessId((String)value);
      }
      break;

    case BUSINESS_TIME:
      if (value == null) {
        unsetBusinessTime();
      } else {
        setBusinessTime((Long)value);
      }
      break;

    case SUBJECT:
      if (value == null) {
        unsetSubject();
      } else {
        setSubject((String)value);
      }
      break;

    case SYNC_URL:
      if (value == null) {
        unsetSyncUrl();
      } else {
        setSyncUrl((String)value);
      }
      break;

    case CLIENT_IP:
      if (value == null) {
        unsetClientIp();
      } else {
        setClientIp((String)value);
      }
      break;

    case REFER:
      if (value == null) {
        unsetRefer();
      } else {
        setRefer((Integer)value);
      }
      break;

    case C_FROM:
      if (value == null) {
        unsetCFrom();
      } else {
        setCFrom((Integer)value);
      }
      break;

    case PID:
      if (value == null) {
        unsetPid();
      } else {
        setPid((Integer)value);
      }
      break;

    case CHANNEL_ID:
      if (value == null) {
        unsetChannelId();
      } else {
        setChannelId((Integer)value);
      }
      break;

    case USER_FUND_PLAT_PARAM:
      if (value == null) {
        unsetUserFundPlatParam();
      } else {
        setUserFundPlatParam((UserFundPlatParam)value);
      }
      break;

    case BIZ_APP_ID:
      if (value == null) {
        unsetBizAppId();
      } else {
        setBizAppId((Integer)value);
      }
      break;

    case PROTOCOL:
      if (value == null) {
        unsetProtocol();
      } else {
        setProtocol((String)value);
      }
      break;

    case EXT:
      if (value == null) {
        unsetExt();
      } else {
        setExt((String)value);
      }
      break;

    case BUSINESS_EXT:
      if (value == null) {
        unsetBusinessExt();
      } else {
        setBusinessExt((String)value);
      }
      break;

    case BUSINESS_TYPE:
      if (value == null) {
        unsetBusinessType();
      } else {
        setBusinessType((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ACCESS_TOKEN:
      return getAccessToken();

    case BIZ_SIGN:
      return getBizSign();

    case AMOUNT:
      return getAmount();

    case FROM_KUGOU_ID:
      return getFromKugouId();

    case TOKUGOU_ID:
      return getTokugouId();

    case BUSINESS_ID:
      return getBusinessId();

    case BUSINESS_TIME:
      return getBusinessTime();

    case SUBJECT:
      return getSubject();

    case SYNC_URL:
      return getSyncUrl();

    case CLIENT_IP:
      return getClientIp();

    case REFER:
      return getRefer();

    case C_FROM:
      return getCFrom();

    case PID:
      return getPid();

    case CHANNEL_ID:
      return getChannelId();

    case USER_FUND_PLAT_PARAM:
      return getUserFundPlatParam();

    case BIZ_APP_ID:
      return getBizAppId();

    case PROTOCOL:
      return getProtocol();

    case EXT:
      return getExt();

    case BUSINESS_EXT:
      return getBusinessExt();

    case BUSINESS_TYPE:
      return getBusinessType();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ACCESS_TOKEN:
      return isSetAccessToken();
    case BIZ_SIGN:
      return isSetBizSign();
    case AMOUNT:
      return isSetAmount();
    case FROM_KUGOU_ID:
      return isSetFromKugouId();
    case TOKUGOU_ID:
      return isSetTokugouId();
    case BUSINESS_ID:
      return isSetBusinessId();
    case BUSINESS_TIME:
      return isSetBusinessTime();
    case SUBJECT:
      return isSetSubject();
    case SYNC_URL:
      return isSetSyncUrl();
    case CLIENT_IP:
      return isSetClientIp();
    case REFER:
      return isSetRefer();
    case C_FROM:
      return isSetCFrom();
    case PID:
      return isSetPid();
    case CHANNEL_ID:
      return isSetChannelId();
    case USER_FUND_PLAT_PARAM:
      return isSetUserFundPlatParam();
    case BIZ_APP_ID:
      return isSetBizAppId();
    case PROTOCOL:
      return isSetProtocol();
    case EXT:
      return isSetExt();
    case BUSINESS_EXT:
      return isSetBusinessExt();
    case BUSINESS_TYPE:
      return isSetBusinessType();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof WxPayAndContractRequest)
      return this.equals((WxPayAndContractRequest)that);
    return false;
  }

  public boolean equals(WxPayAndContractRequest that) {
    if (that == null)
      return false;

    boolean this_present_accessToken = true && this.isSetAccessToken();
    boolean that_present_accessToken = true && that.isSetAccessToken();
    if (this_present_accessToken || that_present_accessToken) {
      if (!(this_present_accessToken && that_present_accessToken))
        return false;
      if (!this.accessToken.equals(that.accessToken))
        return false;
    }

    boolean this_present_bizSign = true && this.isSetBizSign();
    boolean that_present_bizSign = true && that.isSetBizSign();
    if (this_present_bizSign || that_present_bizSign) {
      if (!(this_present_bizSign && that_present_bizSign))
        return false;
      if (!this.bizSign.equals(that.bizSign))
        return false;
    }

    boolean this_present_amount = true && this.isSetAmount();
    boolean that_present_amount = true && that.isSetAmount();
    if (this_present_amount || that_present_amount) {
      if (!(this_present_amount && that_present_amount))
        return false;
      if (!this.amount.equals(that.amount))
        return false;
    }

    boolean this_present_fromKugouId = true;
    boolean that_present_fromKugouId = true;
    if (this_present_fromKugouId || that_present_fromKugouId) {
      if (!(this_present_fromKugouId && that_present_fromKugouId))
        return false;
      if (this.fromKugouId != that.fromKugouId)
        return false;
    }

    boolean this_present_tokugouId = true;
    boolean that_present_tokugouId = true;
    if (this_present_tokugouId || that_present_tokugouId) {
      if (!(this_present_tokugouId && that_present_tokugouId))
        return false;
      if (this.tokugouId != that.tokugouId)
        return false;
    }

    boolean this_present_businessId = true && this.isSetBusinessId();
    boolean that_present_businessId = true && that.isSetBusinessId();
    if (this_present_businessId || that_present_businessId) {
      if (!(this_present_businessId && that_present_businessId))
        return false;
      if (!this.businessId.equals(that.businessId))
        return false;
    }

    boolean this_present_businessTime = true;
    boolean that_present_businessTime = true;
    if (this_present_businessTime || that_present_businessTime) {
      if (!(this_present_businessTime && that_present_businessTime))
        return false;
      if (this.businessTime != that.businessTime)
        return false;
    }

    boolean this_present_subject = true && this.isSetSubject();
    boolean that_present_subject = true && that.isSetSubject();
    if (this_present_subject || that_present_subject) {
      if (!(this_present_subject && that_present_subject))
        return false;
      if (!this.subject.equals(that.subject))
        return false;
    }

    boolean this_present_syncUrl = true && this.isSetSyncUrl();
    boolean that_present_syncUrl = true && that.isSetSyncUrl();
    if (this_present_syncUrl || that_present_syncUrl) {
      if (!(this_present_syncUrl && that_present_syncUrl))
        return false;
      if (!this.syncUrl.equals(that.syncUrl))
        return false;
    }

    boolean this_present_clientIp = true && this.isSetClientIp();
    boolean that_present_clientIp = true && that.isSetClientIp();
    if (this_present_clientIp || that_present_clientIp) {
      if (!(this_present_clientIp && that_present_clientIp))
        return false;
      if (!this.clientIp.equals(that.clientIp))
        return false;
    }

    boolean this_present_refer = true;
    boolean that_present_refer = true;
    if (this_present_refer || that_present_refer) {
      if (!(this_present_refer && that_present_refer))
        return false;
      if (this.refer != that.refer)
        return false;
    }

    boolean this_present_cFrom = true;
    boolean that_present_cFrom = true;
    if (this_present_cFrom || that_present_cFrom) {
      if (!(this_present_cFrom && that_present_cFrom))
        return false;
      if (this.cFrom != that.cFrom)
        return false;
    }

    boolean this_present_pid = true;
    boolean that_present_pid = true;
    if (this_present_pid || that_present_pid) {
      if (!(this_present_pid && that_present_pid))
        return false;
      if (this.pid != that.pid)
        return false;
    }

    boolean this_present_channelId = true;
    boolean that_present_channelId = true;
    if (this_present_channelId || that_present_channelId) {
      if (!(this_present_channelId && that_present_channelId))
        return false;
      if (this.channelId != that.channelId)
        return false;
    }

    boolean this_present_userFundPlatParam = true && this.isSetUserFundPlatParam();
    boolean that_present_userFundPlatParam = true && that.isSetUserFundPlatParam();
    if (this_present_userFundPlatParam || that_present_userFundPlatParam) {
      if (!(this_present_userFundPlatParam && that_present_userFundPlatParam))
        return false;
      if (!this.userFundPlatParam.equals(that.userFundPlatParam))
        return false;
    }

    boolean this_present_bizAppId = true;
    boolean that_present_bizAppId = true;
    if (this_present_bizAppId || that_present_bizAppId) {
      if (!(this_present_bizAppId && that_present_bizAppId))
        return false;
      if (this.bizAppId != that.bizAppId)
        return false;
    }

    boolean this_present_protocol = true && this.isSetProtocol();
    boolean that_present_protocol = true && that.isSetProtocol();
    if (this_present_protocol || that_present_protocol) {
      if (!(this_present_protocol && that_present_protocol))
        return false;
      if (!this.protocol.equals(that.protocol))
        return false;
    }

    boolean this_present_ext = true && this.isSetExt();
    boolean that_present_ext = true && that.isSetExt();
    if (this_present_ext || that_present_ext) {
      if (!(this_present_ext && that_present_ext))
        return false;
      if (!this.ext.equals(that.ext))
        return false;
    }

    boolean this_present_businessExt = true && this.isSetBusinessExt();
    boolean that_present_businessExt = true && that.isSetBusinessExt();
    if (this_present_businessExt || that_present_businessExt) {
      if (!(this_present_businessExt && that_present_businessExt))
        return false;
      if (!this.businessExt.equals(that.businessExt))
        return false;
    }

    boolean this_present_businessType = true && this.isSetBusinessType();
    boolean that_present_businessType = true && that.isSetBusinessType();
    if (this_present_businessType || that_present_businessType) {
      if (!(this_present_businessType && that_present_businessType))
        return false;
      if (!this.businessType.equals(that.businessType))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_accessToken = true && (isSetAccessToken());
    list.add(present_accessToken);
    if (present_accessToken)
      list.add(accessToken);

    boolean present_bizSign = true && (isSetBizSign());
    list.add(present_bizSign);
    if (present_bizSign)
      list.add(bizSign);

    boolean present_amount = true && (isSetAmount());
    list.add(present_amount);
    if (present_amount)
      list.add(amount);

    boolean present_fromKugouId = true;
    list.add(present_fromKugouId);
    if (present_fromKugouId)
      list.add(fromKugouId);

    boolean present_tokugouId = true;
    list.add(present_tokugouId);
    if (present_tokugouId)
      list.add(tokugouId);

    boolean present_businessId = true && (isSetBusinessId());
    list.add(present_businessId);
    if (present_businessId)
      list.add(businessId);

    boolean present_businessTime = true;
    list.add(present_businessTime);
    if (present_businessTime)
      list.add(businessTime);

    boolean present_subject = true && (isSetSubject());
    list.add(present_subject);
    if (present_subject)
      list.add(subject);

    boolean present_syncUrl = true && (isSetSyncUrl());
    list.add(present_syncUrl);
    if (present_syncUrl)
      list.add(syncUrl);

    boolean present_clientIp = true && (isSetClientIp());
    list.add(present_clientIp);
    if (present_clientIp)
      list.add(clientIp);

    boolean present_refer = true;
    list.add(present_refer);
    if (present_refer)
      list.add(refer);

    boolean present_cFrom = true;
    list.add(present_cFrom);
    if (present_cFrom)
      list.add(cFrom);

    boolean present_pid = true;
    list.add(present_pid);
    if (present_pid)
      list.add(pid);

    boolean present_channelId = true;
    list.add(present_channelId);
    if (present_channelId)
      list.add(channelId);

    boolean present_userFundPlatParam = true && (isSetUserFundPlatParam());
    list.add(present_userFundPlatParam);
    if (present_userFundPlatParam)
      list.add(userFundPlatParam);

    boolean present_bizAppId = true;
    list.add(present_bizAppId);
    if (present_bizAppId)
      list.add(bizAppId);

    boolean present_protocol = true && (isSetProtocol());
    list.add(present_protocol);
    if (present_protocol)
      list.add(protocol);

    boolean present_ext = true && (isSetExt());
    list.add(present_ext);
    if (present_ext)
      list.add(ext);

    boolean present_businessExt = true && (isSetBusinessExt());
    list.add(present_businessExt);
    if (present_businessExt)
      list.add(businessExt);

    boolean present_businessType = true && (isSetBusinessType());
    list.add(present_businessType);
    if (present_businessType)
      list.add(businessType);

    return list.hashCode();
  }

  @Override
  public int compareTo(WxPayAndContractRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetAccessToken()).compareTo(other.isSetAccessToken());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAccessToken()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.accessToken, other.accessToken);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBizSign()).compareTo(other.isSetBizSign());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBizSign()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.bizSign, other.bizSign);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAmount()).compareTo(other.isSetAmount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAmount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.amount, other.amount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetFromKugouId()).compareTo(other.isSetFromKugouId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFromKugouId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.fromKugouId, other.fromKugouId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTokugouId()).compareTo(other.isSetTokugouId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTokugouId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.tokugouId, other.tokugouId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBusinessId()).compareTo(other.isSetBusinessId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBusinessId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.businessId, other.businessId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBusinessTime()).compareTo(other.isSetBusinessTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBusinessTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.businessTime, other.businessTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSubject()).compareTo(other.isSetSubject());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSubject()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.subject, other.subject);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSyncUrl()).compareTo(other.isSetSyncUrl());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSyncUrl()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.syncUrl, other.syncUrl);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetClientIp()).compareTo(other.isSetClientIp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetClientIp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.clientIp, other.clientIp);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRefer()).compareTo(other.isSetRefer());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRefer()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.refer, other.refer);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCFrom()).compareTo(other.isSetCFrom());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCFrom()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.cFrom, other.cFrom);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPid()).compareTo(other.isSetPid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pid, other.pid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetChannelId()).compareTo(other.isSetChannelId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetChannelId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.channelId, other.channelId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUserFundPlatParam()).compareTo(other.isSetUserFundPlatParam());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUserFundPlatParam()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.userFundPlatParam, other.userFundPlatParam);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBizAppId()).compareTo(other.isSetBizAppId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBizAppId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.bizAppId, other.bizAppId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetProtocol()).compareTo(other.isSetProtocol());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetProtocol()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.protocol, other.protocol);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExt()).compareTo(other.isSetExt());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExt()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ext, other.ext);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBusinessExt()).compareTo(other.isSetBusinessExt());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBusinessExt()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.businessExt, other.businessExt);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBusinessType()).compareTo(other.isSetBusinessType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBusinessType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.businessType, other.businessType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("WxPayAndContractRequest(");
    boolean first = true;

    sb.append("accessToken:");
    if (this.accessToken == null) {
      sb.append("null");
    } else {
      sb.append(this.accessToken);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("bizSign:");
    if (this.bizSign == null) {
      sb.append("null");
    } else {
      sb.append(this.bizSign);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("amount:");
    if (this.amount == null) {
      sb.append("null");
    } else {
      sb.append(this.amount);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("fromKugouId:");
    sb.append(this.fromKugouId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("tokugouId:");
    sb.append(this.tokugouId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("businessId:");
    if (this.businessId == null) {
      sb.append("null");
    } else {
      sb.append(this.businessId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("businessTime:");
    sb.append(this.businessTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("subject:");
    if (this.subject == null) {
      sb.append("null");
    } else {
      sb.append(this.subject);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("syncUrl:");
    if (this.syncUrl == null) {
      sb.append("null");
    } else {
      sb.append(this.syncUrl);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("clientIp:");
    if (this.clientIp == null) {
      sb.append("null");
    } else {
      sb.append(this.clientIp);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("refer:");
    sb.append(this.refer);
    first = false;
    if (!first) sb.append(", ");
    sb.append("cFrom:");
    sb.append(this.cFrom);
    first = false;
    if (!first) sb.append(", ");
    sb.append("pid:");
    sb.append(this.pid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("channelId:");
    sb.append(this.channelId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("userFundPlatParam:");
    if (this.userFundPlatParam == null) {
      sb.append("null");
    } else {
      sb.append(this.userFundPlatParam);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("bizAppId:");
    sb.append(this.bizAppId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("protocol:");
    if (this.protocol == null) {
      sb.append("null");
    } else {
      sb.append(this.protocol);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("ext:");
    if (this.ext == null) {
      sb.append("null");
    } else {
      sb.append(this.ext);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("businessExt:");
    if (this.businessExt == null) {
      sb.append("null");
    } else {
      sb.append(this.businessExt);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("businessType:");
    if (this.businessType == null) {
      sb.append("null");
    } else {
      sb.append(this.businessType);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws TException {
    // check for required fields
    if (accessToken == null) {
      throw new TProtocolException("Required field 'accessToken' was not present! Struct: " + toString());
    }
    if (bizSign == null) {
      throw new TProtocolException("Required field 'bizSign' was not present! Struct: " + toString());
    }
    if (amount == null) {
      throw new TProtocolException("Required field 'amount' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'fromKugouId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'tokugouId' because it's a primitive and you chose the non-beans generator.
    if (businessId == null) {
      throw new TProtocolException("Required field 'businessId' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'businessTime' because it's a primitive and you chose the non-beans generator.
    if (subject == null) {
      throw new TProtocolException("Required field 'subject' was not present! Struct: " + toString());
    }
    if (syncUrl == null) {
      throw new TProtocolException("Required field 'syncUrl' was not present! Struct: " + toString());
    }
    if (clientIp == null) {
      throw new TProtocolException("Required field 'clientIp' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'refer' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'cFrom' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'pid' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'channelId' because it's a primitive and you chose the non-beans generator.
    if (userFundPlatParam == null) {
      throw new TProtocolException("Required field 'userFundPlatParam' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'bizAppId' because it's a primitive and you chose the non-beans generator.
    if (protocol == null) {
      throw new TProtocolException("Required field 'protocol' was not present! Struct: " + toString());
    }
    if (ext == null) {
      throw new TProtocolException("Required field 'ext' was not present! Struct: " + toString());
    }
    if (businessExt == null) {
      throw new TProtocolException("Required field 'businessExt' was not present! Struct: " + toString());
    }
    if (businessType == null) {
      throw new TProtocolException("Required field 'businessType' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class WxPayAndContractRequestStandardSchemeFactory implements SchemeFactory {
    public WxPayAndContractRequestStandardScheme getScheme() {
      return new WxPayAndContractRequestStandardScheme();
    }
  }

  private static class WxPayAndContractRequestStandardScheme extends StandardScheme<WxPayAndContractRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, WxPayAndContractRequest struct) throws TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACCESS_TOKEN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.accessToken = iprot.readString();
              struct.setAccessTokenIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // BIZ_SIGN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.bizSign = iprot.readString();
              struct.setBizSignIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // AMOUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.amount = iprot.readString();
              struct.setAmountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // FROM_KUGOU_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.fromKugouId = iprot.readI64();
              struct.setFromKugouIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // TOKUGOU_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.tokugouId = iprot.readI64();
              struct.setTokugouIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // BUSINESS_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.businessId = iprot.readString();
              struct.setBusinessIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // BUSINESS_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.businessTime = iprot.readI64();
              struct.setBusinessTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // SUBJECT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.subject = iprot.readString();
              struct.setSubjectIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // SYNC_URL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.syncUrl = iprot.readString();
              struct.setSyncUrlIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // CLIENT_IP
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.clientIp = iprot.readString();
              struct.setClientIpIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // REFER
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.refer = iprot.readI32();
              struct.setReferIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // C_FROM
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.cFrom = iprot.readI32();
              struct.setCFromIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // PID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.pid = iprot.readI32();
              struct.setPidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 14: // CHANNEL_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.channelId = iprot.readI32();
              struct.setChannelIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // USER_FUND_PLAT_PARAM
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.userFundPlatParam = new UserFundPlatParam();
              struct.userFundPlatParam.read(iprot);
              struct.setUserFundPlatParamIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 16: // BIZ_APP_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.bizAppId = iprot.readI32();
              struct.setBizAppIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 17: // PROTOCOL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.protocol = iprot.readString();
              struct.setProtocolIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 18: // EXT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.ext = iprot.readString();
              struct.setExtIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 19: // BUSINESS_EXT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.businessExt = iprot.readString();
              struct.setBusinessExtIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 20: // BUSINESS_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.businessType = iprot.readString();
              struct.setBusinessTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetFromKugouId()) {
        throw new TProtocolException("Required field 'fromKugouId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetTokugouId()) {
        throw new TProtocolException("Required field 'tokugouId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetBusinessTime()) {
        throw new TProtocolException("Required field 'businessTime' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetRefer()) {
        throw new TProtocolException("Required field 'refer' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetCFrom()) {
        throw new TProtocolException("Required field 'cFrom' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetPid()) {
        throw new TProtocolException("Required field 'pid' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetChannelId()) {
        throw new TProtocolException("Required field 'channelId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetBizAppId()) {
        throw new TProtocolException("Required field 'bizAppId' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, WxPayAndContractRequest struct) throws TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.accessToken != null) {
        oprot.writeFieldBegin(ACCESS_TOKEN_FIELD_DESC);
        oprot.writeString(struct.accessToken);
        oprot.writeFieldEnd();
      }
      if (struct.bizSign != null) {
        oprot.writeFieldBegin(BIZ_SIGN_FIELD_DESC);
        oprot.writeString(struct.bizSign);
        oprot.writeFieldEnd();
      }
      if (struct.amount != null) {
        oprot.writeFieldBegin(AMOUNT_FIELD_DESC);
        oprot.writeString(struct.amount);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(FROM_KUGOU_ID_FIELD_DESC);
      oprot.writeI64(struct.fromKugouId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TOKUGOU_ID_FIELD_DESC);
      oprot.writeI64(struct.tokugouId);
      oprot.writeFieldEnd();
      if (struct.businessId != null) {
        oprot.writeFieldBegin(BUSINESS_ID_FIELD_DESC);
        oprot.writeString(struct.businessId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(BUSINESS_TIME_FIELD_DESC);
      oprot.writeI64(struct.businessTime);
      oprot.writeFieldEnd();
      if (struct.subject != null) {
        oprot.writeFieldBegin(SUBJECT_FIELD_DESC);
        oprot.writeString(struct.subject);
        oprot.writeFieldEnd();
      }
      if (struct.syncUrl != null) {
        oprot.writeFieldBegin(SYNC_URL_FIELD_DESC);
        oprot.writeString(struct.syncUrl);
        oprot.writeFieldEnd();
      }
      if (struct.clientIp != null) {
        oprot.writeFieldBegin(CLIENT_IP_FIELD_DESC);
        oprot.writeString(struct.clientIp);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(REFER_FIELD_DESC);
      oprot.writeI32(struct.refer);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(C_FROM_FIELD_DESC);
      oprot.writeI32(struct.cFrom);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PID_FIELD_DESC);
      oprot.writeI32(struct.pid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(CHANNEL_ID_FIELD_DESC);
      oprot.writeI32(struct.channelId);
      oprot.writeFieldEnd();
      if (struct.userFundPlatParam != null) {
        oprot.writeFieldBegin(USER_FUND_PLAT_PARAM_FIELD_DESC);
        struct.userFundPlatParam.write(oprot);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(BIZ_APP_ID_FIELD_DESC);
      oprot.writeI32(struct.bizAppId);
      oprot.writeFieldEnd();
      if (struct.protocol != null) {
        oprot.writeFieldBegin(PROTOCOL_FIELD_DESC);
        oprot.writeString(struct.protocol);
        oprot.writeFieldEnd();
      }
      if (struct.ext != null) {
        oprot.writeFieldBegin(EXT_FIELD_DESC);
        oprot.writeString(struct.ext);
        oprot.writeFieldEnd();
      }
      if (struct.businessExt != null) {
        oprot.writeFieldBegin(BUSINESS_EXT_FIELD_DESC);
        oprot.writeString(struct.businessExt);
        oprot.writeFieldEnd();
      }
      if (struct.businessType != null) {
        oprot.writeFieldBegin(BUSINESS_TYPE_FIELD_DESC);
        oprot.writeString(struct.businessType);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class WxPayAndContractRequestTupleSchemeFactory implements SchemeFactory {
    public WxPayAndContractRequestTupleScheme getScheme() {
      return new WxPayAndContractRequestTupleScheme();
    }
  }

  private static class WxPayAndContractRequestTupleScheme extends TupleScheme<WxPayAndContractRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, WxPayAndContractRequest struct) throws TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeString(struct.accessToken);
      oprot.writeString(struct.bizSign);
      oprot.writeString(struct.amount);
      oprot.writeI64(struct.fromKugouId);
      oprot.writeI64(struct.tokugouId);
      oprot.writeString(struct.businessId);
      oprot.writeI64(struct.businessTime);
      oprot.writeString(struct.subject);
      oprot.writeString(struct.syncUrl);
      oprot.writeString(struct.clientIp);
      oprot.writeI32(struct.refer);
      oprot.writeI32(struct.cFrom);
      oprot.writeI32(struct.pid);
      oprot.writeI32(struct.channelId);
      struct.userFundPlatParam.write(oprot);
      oprot.writeI32(struct.bizAppId);
      oprot.writeString(struct.protocol);
      oprot.writeString(struct.ext);
      oprot.writeString(struct.businessExt);
      oprot.writeString(struct.businessType);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, WxPayAndContractRequest struct) throws TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.accessToken = iprot.readString();
      struct.setAccessTokenIsSet(true);
      struct.bizSign = iprot.readString();
      struct.setBizSignIsSet(true);
      struct.amount = iprot.readString();
      struct.setAmountIsSet(true);
      struct.fromKugouId = iprot.readI64();
      struct.setFromKugouIdIsSet(true);
      struct.tokugouId = iprot.readI64();
      struct.setTokugouIdIsSet(true);
      struct.businessId = iprot.readString();
      struct.setBusinessIdIsSet(true);
      struct.businessTime = iprot.readI64();
      struct.setBusinessTimeIsSet(true);
      struct.subject = iprot.readString();
      struct.setSubjectIsSet(true);
      struct.syncUrl = iprot.readString();
      struct.setSyncUrlIsSet(true);
      struct.clientIp = iprot.readString();
      struct.setClientIpIsSet(true);
      struct.refer = iprot.readI32();
      struct.setReferIsSet(true);
      struct.cFrom = iprot.readI32();
      struct.setCFromIsSet(true);
      struct.pid = iprot.readI32();
      struct.setPidIsSet(true);
      struct.channelId = iprot.readI32();
      struct.setChannelIdIsSet(true);
      struct.userFundPlatParam = new UserFundPlatParam();
      struct.userFundPlatParam.read(iprot);
      struct.setUserFundPlatParamIsSet(true);
      struct.bizAppId = iprot.readI32();
      struct.setBizAppIdIsSet(true);
      struct.protocol = iprot.readString();
      struct.setProtocolIsSet(true);
      struct.ext = iprot.readString();
      struct.setExtIsSet(true);
      struct.businessExt = iprot.readString();
      struct.setBusinessExtIsSet(true);
      struct.businessType = iprot.readString();
      struct.setBusinessTypeIsSet(true);
    }
  }

}

