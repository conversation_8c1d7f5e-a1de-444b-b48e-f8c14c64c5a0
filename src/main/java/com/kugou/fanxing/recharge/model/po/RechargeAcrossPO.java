package com.kugou.fanxing.recharge.model.po;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 充值流水
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RechargeAcrossPO {
    /**
     * 唯一标识
     */
    protected long rechargeId;
    /**
     * 繁星充值订单号
     */
    protected String rechargeOrderNum;
    /**
     * 网关充值订单号
     */
    protected String consumeOrderNum;
    /**
     * 下单时间
     */
    protected int addTime;
    /**
     * 到帐时间
     */
    protected int rechargeTime;
    /**
     * 酷狗ID
     */
    protected long kugouId;
    /**
     * 代理用户酷狗ID
     */
    protected long agentKugouId;
    /**
     * 代充用户酷狗ID
     */
    protected long fromKugouId;
    /**
     * 充值星币数
     */
    protected BigDecimal coin;
    /**
     * 充值金额(元)
     */
    protected BigDecimal amount;
    /**
     * 充值金额(扣除手续费后,分)
     */
    protected BigDecimal realAmount;
    /**
     * 充值前星币
     */
    protected BigDecimal coinBefore;
    /**
     * 充值后星币
     */
    protected BigDecimal coinAfter;
    /**
     * 充值渠道
     */
    protected int payTypeId;
    /**
     * 充值状态(0:未处理,1:成功,-1:失败)
     */
    protected int status;
    /**
     * 扩展属性字段
     */
    protected String extraJsonData;
    /**
     * 分来源渠道标识(0:官网,1:内嵌页,2:代理,3:销售)
     */
    protected int refer;
    /**
     * 充值来源(0:网页,1:android平台,2:ios平台,3:ipad平台,4:PC繁星,5:酷狗7.0android,6:酷狗7.0ios)
     */
    protected int cFrom;
    /**
     * 手机的分发渠道ID
     */
    protected int channelId;
    /**
     * 用于回调时的订单校验
     */
    protected String extend;
    /**
     * 机房(0:bjzw,1:bjyz,2:gz)
     */
    protected int serverRoom;
    /**
     * 充值类型:0=>默认充值
     */
    protected int reType;
    /**
     * 回调时记录的网关时间
     */
    protected int payTime;
    /**
     * 第三方支付付款时间
     */
    protected int tradeTime;
    /**
     * 交易订单号(第三方交易号）
     */
    protected String tradeNo;
    /**
     * 第三方商户号
     */
    protected String partner;
    /**
     * 业务编号
     */
    protected String businessId;
    /**
     * 业务订单号
     */
    protected String businessOrderNo;
    /**
     * 正式付款金额(元)
     */
    protected BigDecimal money;
    /**
     * 代金券金额(元)
     */
    protected BigDecimal coupon;
    /**
     * 代金券订单ID
     */
    protected long couponOrderId;
    /**
     * 代金券ID
     */
    protected long couponId;
    /**
     * 充值状态 (0:未处理,1:成功)
     */
    protected int couponStatus;
    /**
     * 是否沙盒充值，0否，1是
     */
    protected int isSandbox;
    /**
     * 区域id
     */
    protected int areaId;
    /**
     * 统计扩展字段
     */
    protected String statExt;
    /**
     * 客户端IP
     */
    protected String clientIp;
    /**
     * 商品码
     */
    protected String productId;
    /**
     * 国家代码
     */
    protected String areaCode;
    /**
     * 国家代码--后端返回
     */
    protected String paymentAreaCode;
    /**
     * 市区代号
     */
    protected String timeZone;
    /**
     * 当前币种代号
     */
    protected String currency;
    /**
     * 当前币种金额
     */
    protected BigDecimal currencyAmount;
    /**
     * 美元金额
     */
    protected BigDecimal usdAmount;
    /**
     * 虚拟货币 0/1:星币；2:唱币
     */
    protected int coinType;
    /**
     * 客户端版本号（默认：""）
     */
    protected String appVersion;
}
