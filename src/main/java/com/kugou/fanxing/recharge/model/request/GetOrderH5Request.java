package com.kugou.fanxing.recharge.model.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GetOrderH5Request extends GetOrderRequest {
    /**
     * 重定向的地址
     */
    @URL
    private String redirectUrl;
    /**
     * 支付成功之后的跳转地址
     */
    @URL
    private String syncUrl;
    /**
     * 取消支付之后的跳转地址
     */
    private String showUrl;
    /**
     * 业务编号
     */
    private String businessId;
}
