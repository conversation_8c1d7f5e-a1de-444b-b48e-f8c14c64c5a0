package com.kugou.fanxing.recharge.model.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 */
@Data
public class AirwallexConfirmRequest {
    @NotNull
    @NotBlank
    @Pattern(regexp = "^R.+", message = "输入的rechargeOrderNum不合法")
    private String rechargeOrderNum;
    @NotNull
    @NotBlank
    private String outTradeNo;
    @NotNull
    @NotBlank
    private String paymentType;
    @NotNull
    @NotBlank
    private String paymentMethod;
}
