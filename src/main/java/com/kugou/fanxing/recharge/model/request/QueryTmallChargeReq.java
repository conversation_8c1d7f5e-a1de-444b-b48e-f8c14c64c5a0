package com.kugou.fanxing.recharge.model.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 */
@Data
public class QueryTmallChargeReq {
    @NotNull(message = "customer不允许为空")
    @Pattern(regexp = ".+", message = "输入的被充值账号不合法")
    private String customer;

    @Pattern(regexp = ".+", message = "输入的商品识别码不合法")
    @NotNull(message = "cardId不允许为空")
    private String cardId;
}
