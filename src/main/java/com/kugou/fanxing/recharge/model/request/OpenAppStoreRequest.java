package com.kugou.fanxing.recharge.model.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

/**
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OpenAppStoreRequest extends GetOrderRequest {
    @NotBlank
    private String businessId;

    @NotBlank
    private String productId;
}
