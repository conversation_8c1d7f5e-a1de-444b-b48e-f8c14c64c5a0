package com.kugou.fanxing.recharge.model.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Email;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AirwallexCreateRequest extends GetOrderRequest {
    /**
     * The URL to redirect your customer back to after they authenticate their payment on the PaymentMethod’s app or site.
     * If you’d prefer to redirect to a mobile application, you can alternatively supply an application URI scheme.
     */
    @NotNull
    @NotBlank
    private String returnUrl;
    /**
     * 本地币种
     */
    @NotNull
    @NotBlank
    @Length(min = 2, max=3)
    private String currency;
    /**
     * 本地金额
     */
    @NotNull
    @DecimalMin(value = "0.01")
    private BigDecimal currencyAmount;
    /**
     * 国家代码
     */
    @NotNull
    @NotBlank
    @Length(min = 2, max=3)
    private String country;
    /**
     * 用户姓名
     */
    @Length(min = 1, max=100)
    private String businessName;
    /**
     * 用户邮箱
     */
    @Email
    private String email;
    /**
     * 是否卡支付：1 卡支付
     */
    @Min(0)
    @Max(1)
    private int isCard;
}
