package com.kugou.fanxing.recharge.model.request;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class KugouOpenDispatchParam {
    private String serverid;
    private String servertime;
    private String appid;
    private String signature;
    private String clientver;
    private String dfid;
    private String uuid;
    private String mid;
    private String method;
    private String user_openid;
    private String openappid;
    private String business_data;
    private String access_token;
}
