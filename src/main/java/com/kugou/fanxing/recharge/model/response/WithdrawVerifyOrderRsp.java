package com.kugou.fanxing.recharge.model.response;

import com.google.common.collect.Maps;
import lombok.Data;

import java.util.Map;

@Data
public class WithdrawVerifyOrderRsp {

    private int status;
    private int errcode;
    private String error;
    private Map<String, Object> data;

    public static WithdrawVerifyOrderRsp result(boolean isValidOrder) {
        Map<String, Object> payload = Maps.newHashMap();
        payload.put("exist", isValidOrder ? 1 : 0);
        WithdrawVerifyOrderRsp response = new WithdrawVerifyOrderRsp();
        response.setStatus(0);
        response.setErrcode(0);
        response.setError("");
        response.setData(payload);
        return response;
    }
}
