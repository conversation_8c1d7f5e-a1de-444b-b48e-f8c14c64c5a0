package com.kugou.fanxing.recharge.model;

import com.dianping.cat.Cat;
import com.kugou.fanxing.recharge.constant.KupayErrorCodeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.service.withdraw.AlipayResp;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Objects;

@Data
@Accessors(chain = true)
public class DrawCashUpdater {

    private long orderId;
    private int errorCode;
    private String errorMsg;
    private String errorReason;
    private String outTradeNo;
    private String orderNo;
    private String tradeNo;
    private int transferStatus;
    private Date tradeEndTime;
    private Date updateTime;

    public static DrawCashUpdater build(long orderId, int errorCode, AlipayResp.AlipayResult alipayResult) {
        DrawCashUpdater updater = new DrawCashUpdater();
        updater.setOrderId(orderId);
        updater.setErrorCode(errorCode);
        updater.setErrorMsg(KupayErrorCodeEnum.errorMsgOf(errorCode));
        updater.setErrorReason("");
        updater.setUpdateTime(new Date());
        if (Objects.nonNull(alipayResult)) {
            String tradeNo = StringUtils.defaultIfBlank(alipayResult.getTrade_no(), alipayResult.getPayment_no());
            updater.setOutTradeNo(alipayResult.getOut_trade_no());
            updater.setOrderNo(alipayResult.getOrder_no());
            updater.setTradeNo(tradeNo);
            updater.setTransferStatus(alipayResult.getTransfer_status());
            updater.setTradeEndTime(alipayResult.getTrade_endtime());
            updater.setErrorReason(StringUtils.defaultString(alipayResult.getError_reason()));
            Cat.logEvent("withdraw.errorReason", StringUtils.defaultString(alipayResult.getError_reason()));
        }
        return updater;
    }

    public static DrawCashUpdater build(long orderId, SysResultCode sysResultCode) {
        DrawCashUpdater updater = new DrawCashUpdater();
        updater.setOrderId(orderId);
        updater.setErrorCode(sysResultCode.getCode());
        updater.setErrorMsg(sysResultCode.getMsg());
        updater.setErrorReason("");
        updater.setUpdateTime(new Date());
        return updater;
    }

}
