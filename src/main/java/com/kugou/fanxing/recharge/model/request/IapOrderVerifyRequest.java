package com.kugou.fanxing.recharge.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IapOrderVerifyRequest {
    @NotNull
    @NotBlank
    private String trade_no;
    @NotNull
    @NotBlank
    private String order_no;
    private String sign;
    private String time;
    @Min(1)
    private long userid;
}
