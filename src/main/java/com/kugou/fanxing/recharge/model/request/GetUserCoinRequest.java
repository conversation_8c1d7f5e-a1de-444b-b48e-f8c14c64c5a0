package com.kugou.fanxing.recharge.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetUserCoinRequest {
    /**
     * 虚拟货币类型（1:星币；2:唱币）
     */
    @Range(min = 1, max = 2)
    @Builder.Default
    private int coinType = 1;
}
