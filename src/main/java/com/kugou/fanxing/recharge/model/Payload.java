package com.kugou.fanxing.recharge.model;


import java.util.HashMap;

public class Payload extends HashMap<String, Object> {

    public static class Builder {

        private Builder() {
        }

        public static PayloadWrapper map(String q, Object w) {
            return new PayloadWrapper(q, w);
        }
    }

    public static final class PayloadWrapper {
        private final Payload payload;

        public PayloadWrapper(String q, Object w) {
            payload = new Payload();
            payload.put(q, w);
        }

        public PayloadWrapper map(String q, Object w) {
            payload.put(q, w);
            return this;
        }

        public Payload build() {
            return payload;
        }
    }

}