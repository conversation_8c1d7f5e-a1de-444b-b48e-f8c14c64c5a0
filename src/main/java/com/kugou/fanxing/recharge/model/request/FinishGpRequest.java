package com.kugou.fanxing.recharge.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FinishGpRequest {
    @NotNull(message = "输入的rechargeOrderNum不能为空")
    @Pattern(regexp = "R.+", message = "输入的rechargeOrderNum不合法")
    private String rechargeOrderNum;
    @NotNull(message = "输入的receiptData不能为空")
    @Pattern(regexp = ".+", message = "输入的receiptData不合法")
    private String receiptData;
//    @NotNull(message = "输入的version不能为空")
//    @Pattern(regexp = ".+", message = "输入的version不合法")
    private int isConsume;
}
