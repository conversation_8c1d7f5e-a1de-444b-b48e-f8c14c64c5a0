package com.kugou.fanxing.recharge.model.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;


/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WechatRequest extends GetOrderRequest {
    /**
     * 二维码类型 0：返回短链接(充值地址) 1：自动跳到到二维码图片 2：二维码图片
     */
    @Range(min = 0, max = 2, message = "输入的qr不合法")
    private int qr;
    @Range(min = 0, max = 100, message = "输入的size不合法")
    private int size;
    @Range(min = 0, max = 100, message = "输入的margin不合法")
    private int margin;
    private String Extension1;
    /**
     * 代充被充值用户酷狗ID
     */
    private long rechargeKugouId;
    @Range(min = 0, max = 1, message = "输入的frontQrCode不合法")
    private int frontQrCode = 0;
}
