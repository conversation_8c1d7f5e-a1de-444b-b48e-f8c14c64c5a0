package com.kugou.fanxing.recharge.model.request;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class QueryWithdrawOrderWechatReq {
    @NotNull(message = "openid不允许为空")
    @Pattern(regexp = ".+", message = "输入的openid不合法")
    private String openid;

    @Range(min = 1, message = "输入的biz_appid不合法")
    private int biz_appid;

    @Range(min = 1, message = "order_no")
    private long order_no;

    @NotNull(message = "total_fee")
    private BigDecimal total_fee;
}
