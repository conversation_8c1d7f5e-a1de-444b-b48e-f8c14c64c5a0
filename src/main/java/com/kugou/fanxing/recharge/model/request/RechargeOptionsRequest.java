package com.kugou.fanxing.recharge.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RechargeOptionsRequest {
    /**
     * 业务来源
     */
    private int source;
    /**
     * 支付渠道
     */
    private int payTypeId;
    /**
     * 充值入口来源标识，需要同后端协定
     */
    private String actFlag;
}
