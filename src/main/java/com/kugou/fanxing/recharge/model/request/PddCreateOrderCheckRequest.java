package com.kugou.fanxing.recharge.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PddCreateOrderCheckRequest {
    @NotNull
    @Min(1)
    private long userid;
    @NotNull
    @NotBlank
    private String prodNo;
}
