package com.kugou.fanxing.recharge.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/3/12 10:31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WxRechargeRequest {
    private String accessToken;
    private String bizSign;
    private BigDecimal amount;
    private long fromKugouId;
    private long tokugouId;
    private String businessId;
    private long businessTime;
    private String subject;
    private String syncUrl;
    private String clientIp;
    private int refer;
    private int cFrom;
    private String pid;
    private int channelId;
    private UserFundPlatParam userFundPlatParam;
    private int bizAppId;
    private String protocol;
    private String ext;
    private String businessExt;
}
