package com.kugou.fanxing.recharge.model.request;

import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
public abstract class GetOrderRequest {
    /**
     * 充值方式
     */
    protected PayTypeIdEnum payTypeIdEnum;
    /**
     * 同步地址
     */
    protected String sync_url;
    /**
     * 充值金额（元）
     */
    @NotNull(message = "money不允许为空")
    @DecimalMin(value = "0.01", message = "输入的amount不合法")
    protected BigDecimal amount;
    /**
     * 充值代金券
     */
    @Min(value = 0, message = "输入的couponId不合法")
    protected long couponId;
    /**
     * 充值币种（1：星币；2：唱币）
     */
    protected int coinType = 1;
    /**
     * 初始化请求
     *
     * @param payTypeIdEnum  支付类型
     * @param webCommonParam 通用参数
     */
    public void initRequest(PayTypeIdEnum payTypeIdEnum, WebCommonParam webCommonParam) {
        // 初始化充值类型
        this.payTypeIdEnum = payTypeIdEnum;
        // 设置的回调地址（默认为referer）
        this.setSync_url(StringUtils.defaultIfBlank(this.getSync_url(), webCommonParam.getReferer()));
    }
}
