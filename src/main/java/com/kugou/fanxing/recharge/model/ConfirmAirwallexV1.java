package com.kugou.fanxing.recharge.model;

import com.fasterxml.jackson.annotation.JsonRawValue;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.kugou.fanxing.recharge.util.KeepAsJsonDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * https://www.airwallex.com/docs/api#/Payment_Acceptance/Payment_Intents/_api_v1_pa_payment_intents_create/post
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfirmAirwallexV1 {
    private String content_type;
    @JsonDeserialize(using = KeepAsJsonDeserializer.class)
    private String data;
    private Map<String, String> dcc_data;
    private String method;
    private String qrcode;
    private String qrcode_url;
    private String stage;
    private String type;
    private String url;
}
