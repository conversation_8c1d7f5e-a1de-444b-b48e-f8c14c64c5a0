package com.kugou.fanxing.recharge.model.request;

import lombok.Data;

@Data
public class KupayCommonRequest {
    /**
     * 调用方服务号
     */
    private int serverid;
    /**
     * 调用方时间戳
     */
    private int servertime;
    /**
     * 终端应用编号
     */
    private String appid;
    /**
     * 客户端版本号（取客户端接口传的参数）
     */
    private String clientver;
    /**
     * 机器唯一码 (手机串号 PC内嵌唯一值 web唯一值) （取客户端接口传的参数）
     */
    private String mid;
    /**
     * 客户端设备唯一标识（取客户端接口传的参数）
     */
    private String uuid;
    /**
     * 设备指纹ID, 取客户端接口传的参数
     */
    private String dfid;
    /**
     * 请求签名
     */
    private String signature;
}
