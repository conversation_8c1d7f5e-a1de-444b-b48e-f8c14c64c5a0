package com.kugou.fanxing.recharge.model.request;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class WxMiniProgramRequest {
    @NotNull(message = "openId不允许为空")
    @Pattern(regexp = ".+", message = "输入的openId不合法")
    private String openId;
    @NotNull(message = "money不允许为空")
    @DecimalMin(value = "0.01", message = "输入的money不合法")
    @DecimalMax(value = "5000000", message = "输入的money不合法")
    private BigDecimal money;
}
