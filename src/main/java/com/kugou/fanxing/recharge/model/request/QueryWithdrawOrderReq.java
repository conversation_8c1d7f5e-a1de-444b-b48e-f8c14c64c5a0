package com.kugou.fanxing.recharge.model.request;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2020/3/18 14:11
 * Content:
 * Description:
 * <AUTHOR>
 * userid=*********, biz_appid=3134, order_no=1382698835822825909, user_account=***********, total_fee=2.33
 */
@Data
public class QueryWithdrawOrderReq {
    @Range(min = 1, message = "输入的userid不合法")
    private long userid;

    @Range(min = 1, message = "输入的biz_appid不合法")
    private int biz_appid;

    @Range(min = 1, message = "order_no")
    private long order_no;

    @NotNull(message = "user_account不允许为空")
    @Pattern(regexp = ".+", message = "输入的user_account不合法")
    private String user_account;

    private BigDecimal total_fee;
}
