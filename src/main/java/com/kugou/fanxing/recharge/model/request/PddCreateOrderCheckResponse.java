package com.kugou.fanxing.recharge.model.request;

import com.kugou.fanxing.recharge.model.PddOrderCheckResult;
import com.kugou.fanxing.recharge.util.JsonResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PddCreateOrderCheckResponse {

    private int status;
    private int errcode;
    private String errmsg;
    private PddOrderCheckResult data;

    public static PddCreateOrderCheckResponse from(JsonResult<PddOrderCheckResult> jsonResult) {
        return PddCreateOrderCheckResponse.builder()
                .status(1)
                .errcode(0)
                .errmsg(jsonResult.getMsg())
                .data(jsonResult.getData())
                .build();
    }
}
