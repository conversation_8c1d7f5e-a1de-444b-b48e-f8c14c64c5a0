package com.kugou.fanxing.recharge.common.aop.aspect;

import com.kugou.fanxing.recharge.common.aop.annotation.CdnCache;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

@Slf4j
@Aspect
@Component
@Order(1004)
public class CdnCacheAspect {

    @Pointcut("@annotation(com.kugou.fanxing.recharge.common.aop.annotation.CdnCache)")
    public void pointcut() {
        // DO NOTHING
    }

    @Around("pointcut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        CdnCache cdnCache = signature.getMethod().getAnnotation(CdnCache.class);
        if (cdnCache.cacheStrategy() == CdnCache.CacheStrategy.NO_CACHE) {
            log.warn("统一设置缓存响应头，禁止缓存。method: {}", method.getName());
            HttpServletResponse response=((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
            response.setHeader("Pragma", "No-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setDateHeader("Expires", 0);
        }
        return point.proceed();
    }
}
