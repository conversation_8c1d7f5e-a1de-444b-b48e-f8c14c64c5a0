package com.kugou.fanxing.recharge.common.interceptor.validator;

import com.kugou.fanxing.recharge.constant.ProtocolConstants;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.request.KugouOpenDispatchParam;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.service.KugouOpenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@Component
public class KugouOpenAuthValidator extends AbstractValidator {

    @Autowired
    private KugouOpenService kugouOpenService;

    @Override
    public boolean validate(HttpServletRequest request, HttpServletResponse response) throws Exception {
        if (request.getAttribute(ProtocolConstants.WEB_COMMON_PARAM) instanceof WebCommonParam) {
            WebCommonParam webCommonParam = (WebCommonParam) request.getAttribute(ProtocolConstants.WEB_COMMON_PARAM);
            return checkToken(webCommonParam);
        }
        return false;
    }

    public boolean checkToken(WebCommonParam webCommonParam) {
        // 令牌合法性校验
        KugouOpenDispatchParam kugouOpenDispatchParam = webCommonParam.getKugouOpenDispatchParam();
        long kugouId = this.kugouOpenService.getKugouIdByOpenToken(kugouOpenDispatchParam).orElse(0L);
        if (kugouId < 1) {
            log.warn("酷狗开放平台入口，令牌合法性校验失败。kugouOpenDispatchParam: {}", kugouOpenDispatchParam);
            throw new BizException(SysResultCode.E_50000003);
        }
        webCommonParam.setKugouId(kugouId);
        return true;
    }


}
