package com.kugou.fanxing.recharge.common.filter;

import com.google.common.collect.Lists;
import com.google.gson.JsonParseException;
import com.google.gson.JsonParser;
import com.kugou.fanxing.recharge.constant.PidEnum;
import com.kugou.fanxing.recharge.constant.ProtocolConstants;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class WebParamContextFilter extends AbstractFilter {

    @Override
    public void doHttpFilter(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws IOException, ServletException {
        if (excludePathSet.stream().anyMatch(o -> pathMatcher.match(o, request.getRequestURI()))) {
            chain.doFilter(request, response);
            return;
        }

        WebCommonParam webCommonParam = new WebCommonParam();
        request.setAttribute(ProtocolConstants.WEB_COMMON_PARAM, webCommonParam);
        response.setContentType("text/json; charset=utf-8");
        AccessControlUtils.setCrossOrigin(request, response);

        // 解析Agent
        parseAgent(request, webCommonParam);
        // 解析Pid
        parsePid(request, webCommonParam);
        // 解析Cookie
        parseCookie(request, webCommonParam);
        // 解析Token
        parseToken(request, webCommonParam);

        parseExt(request,webCommonParam);
        chain.doFilter(request, response);
    }

    private void parseAgent(HttpServletRequest request, WebCommonParam webCommonParam) {
        webCommonParam.setTimestamp(NumberUtils.toLong(request.getParameter("_"), new Date().getTime()));
        webCommonParam.setHttpHost(request.getScheme() + "://" + StringUtils.defaultString(request.getHeader("host")) + "/");
        webCommonParam.setHost(StringUtils.defaultString(request.getRemoteHost()));
        webCommonParam.setReferer(StringUtils.defaultString(request.getHeader("referer")));
        webCommonParam.setUri(StringUtils.defaultString(request.getRequestURI()));
        webCommonParam.setIp(IpUtils.getClientIpAddress());
        webCommonParam.setTimeZone(StringUtils.defaultString(request.getParameter("time_zone"),"8"));
        webCommonParam.setLang(StringUtils.defaultString(request.getParameter("lang"),"zh-cn"));
        webCommonParam.setAreaCode(StringUtils.defaultString(request.getParameter("area_code"),"cn"));
        webCommonParam.setKfd(RequestUtils.getPossibleParameter(request, "kfd", "std_dev"));
        webCommonParam.setUserAgent(StringUtils.defaultString(request.getHeader("User-Agent")));
        webCommonParam.setVersion(StringUtils.defaultString(request.getParameter("version")));
    }

    private void parsePid(HttpServletRequest request, WebCommonParam webCommonParam) {
        String pidStr = StringUtils.defaultString(request.getParameter("pid"));
        if (StringUtils.isNumeric(pidStr)) {
            webCommonParam.setPid(ParseUtils.tryParseInt(pidStr, 0));
        }
        String platformIdStr = StringUtils.defaultString(request.getParameter("platform"));
        if (StringUtils.isNumeric(platformIdStr)) {
            int platformId = ParseUtils.tryParseInt(platformIdStr, 0);
            webCommonParam.setPlatform(platformId);
            webCommonParam.setPid(PidEnum.getPidByPlatformId(platformId));
        }
        String stdPlatStr = StringUtils.defaultString(request.getParameter("std_plat"));
        if (StringUtils.isNumeric(stdPlatStr)) {
            webCommonParam.setStdPlat(ParseUtils.tryParseInt(stdPlatStr, 0));
        }
    }

    private void parseCookie(HttpServletRequest request, WebCommonParam webCommonParam) {
        Map<String, Cookie> cookieMap = Arrays.stream(Optional.ofNullable(request.getCookies())
                .orElse(new Cookie[]{}))
                .collect(Collectors.toMap(Cookie::getName, Function.identity(), (oldValue, newValue) -> oldValue));
        WebUserInfoUtils.setUserInfoFromCookie(cookieMap, webCommonParam);
    }

    private void parseToken(HttpServletRequest request, WebCommonParam webCommonParam) {
        List<String> possibleAppIds = Lists.newArrayList(request.getHeader("appid"), request.getParameter("appid"), request.getParameter("appId"));
        List<String> possibleKugouIds = Lists.newArrayList(request.getHeader("kgid"), request.getParameter("kgid"), request.getParameter("kugouId"));
        List<String> possibleTokens = Lists.newArrayList(request.getHeader("token"), request.getParameter("token"), webCommonParam.getToken());
        int appId = NumberUtils.toInt(possibleAppIds.stream().filter(StringUtils::isNotBlank).findFirst().orElse(""));
        long kugouId = NumberUtils.toLong(possibleKugouIds.stream().filter(StringUtils::isNotBlank).findFirst().orElse(""));
        String token = possibleTokens.stream().filter(StringUtils::isNotBlank).findFirst().orElse("");
        webCommonParam.setAppId(appId > 0 ? appId : webCommonParam.getAppId());
        webCommonParam.setKugouId(kugouId > 0 ? kugouId : webCommonParam.getKugouId());
        webCommonParam.setToken(token);
    }

    private void parseExt(HttpServletRequest request,WebCommonParam webCommonParam) {
        String ext = StringUtils.defaultString(request.getParameter("ext"));
        if(!StringUtils.isEmpty(ext)){
            try {
                new JsonParser().parse(ext);
            } catch (JsonParseException e) {
                log.warn("解析请求参数，解析ext异常。request: {}",request);
                throw new BizException(SysResultCode.RECHARGE_PARAM_ERROR);
            }
        }
        webCommonParam.setExt(ext);
    }

}
