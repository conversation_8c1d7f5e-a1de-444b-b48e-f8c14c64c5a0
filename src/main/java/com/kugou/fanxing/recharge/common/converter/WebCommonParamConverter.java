package com.kugou.fanxing.recharge.common.converter;

import com.kugou.fanxing.recharge.constant.ProtocolConstants;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.ModelAndViewContainer;

public class WebCommonParamConverter extends AbstractConverter<WebCommonParam> {

    @Override
    protected Object onResolve(MethodParameter parameter, NativeWebRequest webRequest,
                               ModelAndViewContainer mavContainer, WebDataBinderFactory binderFactory) throws Exception {
        return webRequest.getAttribute(ProtocolConstants.WEB_COMMON_PARAM, 0);
    }
}
