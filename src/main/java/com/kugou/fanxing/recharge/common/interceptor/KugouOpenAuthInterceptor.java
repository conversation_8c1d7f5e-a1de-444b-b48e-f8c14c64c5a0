package com.kugou.fanxing.recharge.common.interceptor;

import com.kugou.fanxing.recharge.common.interceptor.validator.KugouOpenAuthValidator;
import com.kugou.fanxing.recharge.util.AccessControlUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@Component
public class KugouO<PERSON>AuthInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    private KugouOpenAuthValidator validator;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        AccessControlUtils.setCrossOrigin(request, response);
        if (request.getMethod().equals(RequestMethod.OPTIONS.name())) {
            return true;
        }
        return this.validator.validate(request, response);
    }

}
