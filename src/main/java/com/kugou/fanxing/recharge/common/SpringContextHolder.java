package com.kugou.fanxing.recharge.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;


/**
 * Spring Context Holder
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SpringContextHolder implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    public SpringContextHolder() {
    }

    public static ApplicationContext getApplicationContext() {
        if (applicationContext == null) {
            throw new IllegalStateException("ApplicationContext未注入,请在applicationContext.xml中定义SpringContextHolder");
        }
        return applicationContext;
    }

    public void setApplicationContext(ApplicationContext applicationContext) {
        SpringContextHolder.applicationContext = applicationContext;
    }

    public static <T> T getBean(String beanId, Class<T> clazz) {
        return getApplicationContext().getBean(beanId, clazz);
    }

    public static <T> T getBean(Class<T> clazz) {
        return getApplicationContext().getBean(clazz);
    }

    public static void autowireBeanPropertiesByName(Object existingBean) {
        getApplicationContext()
                .getAutowireCapableBeanFactory()
                .autowireBeanProperties(existingBean, AutowireCapableBeanFactory.AUTOWIRE_BY_NAME, false);
    }

    public static void autowireBeanPropertiesByType(Object existingBean) {
        getApplicationContext()
                .getAutowireCapableBeanFactory()
                .autowireBeanProperties(existingBean, AutowireCapableBeanFactory.AUTOWIRE_BY_TYPE, false);
    }

    /**
     * Log Spring Managed Beans
     */
    public static void logSpringManagedBeans() {
        int beanDefinitionCount = getApplicationContext().getBeanDefinitionCount();
        log.info("List Spring Managed {} Beans...", beanDefinitionCount);
        String[] beanDefinitionNames = getApplicationContext().getBeanDefinitionNames();
        for (String beanDefinitionName : beanDefinitionNames) {
            log.info(beanDefinitionName);
        }
    }

}
