package com.kugou.fanxing.recharge.common.converter;

import com.google.common.reflect.TypeToken;
import com.kugou.fanxing.recharge.common.annotation.Nullable;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * 对Controller中的入参进行转换
 * 具体的转换逻辑由子类实现
 * Created by Alex on 2014/4/28
 */
@Slf4j
public abstract class AbstractConverter<T> implements HandlerMethodArgumentResolver {

    final TypeToken<T> type = new TypeToken<T>(getClass()) {
    };

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return type.getRawType().isAssignableFrom(parameter.getParameterType());
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        Object result = onResolve(parameter, webRequest, mavContainer, binderFactory);
        if (result == null && parameter.getMethodAnnotation(Nullable.class) == null) {
            log.warn("解析请求参数失败");
            throw new BizException(SysResultCode.RECHARGE_RESOLVE_ERROR);
        }
        return result;
    }

    protected abstract Object onResolve(MethodParameter parameter, NativeWebRequest webRequest, ModelAndViewContainer mavContainer, WebDataBinderFactory binderFactory) throws Exception;
}
