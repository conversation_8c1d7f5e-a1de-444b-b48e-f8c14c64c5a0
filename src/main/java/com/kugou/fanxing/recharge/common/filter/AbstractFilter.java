package com.kugou.fanxing.recharge.common.filter;

import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

@Slf4j
public abstract class AbstractFilter implements Filter {

    /**
     * 排除的路径
     */
    protected Set<String> excludePathSet = Sets.newConcurrentHashSet();

    /**
     * 路径匹配器
     */
    protected PathMatcher pathMatcher = new AntPathMatcher();

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        String excludePaths = Optional.ofNullable(filterConfig.getInitParameter("excludePaths")).orElse(StringUtils.EMPTY);
        if (StringUtils.isNotBlank(excludePaths)) {
            this.excludePathSet = new HashSet<>(Arrays.asList(excludePaths.split(",")));
        }
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        log.debug("Filter 排除路径, excludePathSet: {}, requestURI: {}", excludePathSet, httpServletRequest.getRequestURI());
        if (excludePathSet.stream().anyMatch(o -> pathMatcher.match(o, httpServletRequest.getRequestURI()))) {
            chain.doFilter(request, response);
            return;
        }
        this.doHttpFilter(httpServletRequest, httpServletResponse, chain);
    }

    public abstract void doHttpFilter(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws IOException, ServletException;

    @Override
    public void destroy() {}
}
