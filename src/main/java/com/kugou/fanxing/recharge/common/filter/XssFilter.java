package com.kugou.fanxing.recharge.common.filter;

import com.kugou.fanxing.recharge.util.JsoupUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
public class XssFilter extends AbstractFilter {

    @Override
    public void doHttpFilter(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws IOException, ServletException {
        XssHttpServletRequestWrapper xssRequest = new XssHttpServletRequestWrapper(request);
        chain.doFilter(xssRequest, response);
    }

    private static class XssHttpServletRequestWrapper extends HttpServletRequestWrapper {

        HttpServletRequest orgRequest = null;

        public XssHttpServletRequestWrapper(HttpServletRequest request) {
            super(request);
            orgRequest = request;
        }

        @Override
        public String getParameter(String name) {
            name = JsoupUtils.clean(name);
            String value = super.getParameter(name);
            if (StringUtils.isNotBlank(value)) {
                value = JsoupUtils.clean(value);
            }
            return value;
        }

        @Override
        public String[] getParameterValues(String name) {
            String[] arr = super.getParameterValues(name);
            if (arr != null) {
                for (int i = 0; i < arr.length; i++) {
                    arr[i] = JsoupUtils.clean(arr[i]);
                }
            }
            return arr;
        }

        @Override
        public String getHeader(String name) {
            name = JsoupUtils.clean(name);
            String value = super.getHeader(name);
            if (StringUtils.isNotBlank(value)) {
                value = JsoupUtils.clean(value);
            }
            return value;
        }

        public HttpServletRequest getOrgRequest() {
            return orgRequest;
        }

        public static HttpServletRequest getOrgRequest(HttpServletRequest req) {
            if (req instanceof XssHttpServletRequestWrapper) {
                return ((XssHttpServletRequestWrapper) req).getOrgRequest();
            }
            return req;
        }
    }

}
