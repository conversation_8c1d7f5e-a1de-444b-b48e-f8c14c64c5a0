package com.kugou.fanxing.recharge.common.aop.aspect;

import com.kugou.config.Env;
import com.kugou.fanxing.recharge.model.request.GetOrderRequest;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;

@Slf4j
@Aspect
@Component
@Order(1004)
public class PoorModeAspect {

    @Autowired
    private Env env;
    @Autowired
    private ApolloConfigService apolloConfigService;

    @Pointcut("@annotation(com.kugou.fanxing.recharge.common.aop.annotation.PoorMode)")
    public void pointcut() {
        // DO NOTHING
    }

    @Around("pointcut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        Arrays.stream(point.getArgs()).filter(GetOrderRequest.class::isInstance).forEach(arg -> {
            GetOrderRequest request = (GetOrderRequest) arg;
            if ((env.isDev() || env.isTest()) && apolloConfigService.getRechargePoorMode()) {
                BigDecimal poorModeAmount = apolloConfigService.getRechargePoorModeAmount();
                log.warn("测试开发环境，下单充值金额重制为{}元", poorModeAmount.stripTrailingZeros().toPlainString());
                request.setAmount(poorModeAmount);
            }
        });
        return point.proceed();
    }
}
