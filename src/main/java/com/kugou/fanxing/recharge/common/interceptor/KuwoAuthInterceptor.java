package com.kugou.fanxing.recharge.common.interceptor;

import com.alibaba.fastjson.JSON;
import com.kugou.fanxing.recharge.constant.ProtocolConstants;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.util.AccessControlUtils;
import com.kugou.fanxing.recharge.util.ThreadNameUtils;
import com.kugou.kw.token.api.CheckKwTokenReq;
import com.kugou.kw.token.api.CheckKwTokenResp;
import com.kugou.kw.token.api.KuwoTokenService;
import com.taobao.api.internal.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static com.kugou.fanxing.recharge.constant.SysResultCode.RECHARGE_TOKEN_INVALID;

@Slf4j
@Component
public class KuwoAuthInterceptor extends HandlerInterceptorAdapter {

    private static final String TOKEN = "token";
    private static final String KUWO_ID = "kuwoId";

    @Autowired
    private KuwoTokenService.Iface kwTokenService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        AccessControlUtils.setCrossOrigin(request, response);
        if (request.getMethod().equals(RequestMethod.OPTIONS.name())) {
            return true;
        }
        ThreadNameUtils.setThreadName();
        String token = request.getHeader(TOKEN);
        String kuwoIdStr = request.getHeader(KUWO_ID);
        if (StringUtils.isEmpty(token) || StringUtils.isEmpty(kuwoIdStr)) {
            log.error("请求参数非法, token:{}, kuwoId:{}", token, kuwoIdStr);
            throw new BizException(RECHARGE_TOKEN_INVALID).addContextValue(TOKEN, token).addContextValue(KUWO_ID, kuwoIdStr);
        }
        if (request.getAttribute(ProtocolConstants.WEB_COMMON_PARAM) instanceof WebCommonParam) {
            WebCommonParam webCommonParam = (WebCommonParam) request.getAttribute(ProtocolConstants.WEB_COMMON_PARAM);
            long kuwoId = Long.parseLong(kuwoIdStr);
            CheckKwTokenReq req = new CheckKwTokenReq();
            req.setToken(token);
            req.setKuwoId(kuwoId);
            log.warn("checkToken req:{}", JSON.toJSONString(req));
            CheckKwTokenResp resp = kwTokenService.checkToken(req);
            log.warn("checkToken resp:{}", JSON.toJSONString(resp));
            if (resp.getCode() != 0 || !resp.isData()) {
                throw new BizException(RECHARGE_TOKEN_INVALID).addContextValue(TOKEN, token).addContextValue(KUWO_ID, kuwoIdStr);
            }
            webCommonParam.setKuwoId(kuwoId);
            return true;
        } else {
            throw new BizException(RECHARGE_TOKEN_INVALID).addContextValue(TOKEN, token).addContextValue(KUWO_ID, kuwoIdStr);
        }
    }
}
