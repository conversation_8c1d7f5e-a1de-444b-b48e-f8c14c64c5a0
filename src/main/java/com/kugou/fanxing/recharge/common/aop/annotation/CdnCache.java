package com.kugou.fanxing.recharge.common.aop.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface CdnCache {
    String name() default "";
    CacheStrategy cacheStrategy() default CacheStrategy.NO_CACHE;
    enum CacheStrategy {
        NO_CACHE,
        CACHE_10M
    }
}
