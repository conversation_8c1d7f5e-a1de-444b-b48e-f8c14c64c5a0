package com.kugou.fanxing.recharge.common.interceptor.validator;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.ProtocolConstants;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.recharge.util.RechargePlatHelper;
import com.kugou.fanxing.recharge.util.ResponseUtils;
import com.kugou.fanxing.thrift.token.dto.CheckPara;
import com.kugou.fanxing.thrift.token.dto.CheckResult;
import com.kugou.fanxing.thrift.token.service.TokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@Component
public class WebRequestValidator extends AbstractValidator {

    @Autowired
    private TokenService.Iface tokenService;
    @Autowired
    private UserFacadeService userFacadeService;
    @Autowired
    private ApolloConfigService apolloConfigService;

    @Override
    public boolean validate(HttpServletRequest request, HttpServletResponse response) throws Exception {
        if (request.getAttribute(ProtocolConstants.WEB_COMMON_PARAM) instanceof WebCommonParam) {
            // 认证校验与封禁校验
            WebCommonParam webCommonParam = (WebCommonParam) request.getAttribute(ProtocolConstants.WEB_COMMON_PARAM);
            return checkAuth(request, response, webCommonParam) && checkBan(request, response, webCommonParam);
        }
        return false;
    }

    /**
     * 检查用户认证状态
     *
     * @param webCommonParam 通用参数
     * @return 是否通过
     */
    private boolean checkAuth(HttpServletRequest request, HttpServletResponse response, WebCommonParam webCommonParam) {
        try {
            if (webCommonParam.getAppId() > 0 && webCommonParam.getKugouId() > 0 && StringUtils.isNotBlank(webCommonParam.getToken())) {
                CheckPara checkPara = new CheckPara()
                        .setAppid(webCommonParam.getAppId())
                        .setKugouid(webCommonParam.getKugouId())
                        .setToken(webCommonParam.getToken())
                        .setIp(webCommonParam.getIp());
                CheckResult checkResult = this.tokenService.checkTokenValue(checkPara);
                if (apolloConfigService.enableDebugMode()) {
                    log.warn("检查用户登陆状态，响应结果。checkPara：{}， checkResult：{}", checkPara, checkResult);
                }
                if (checkResult.getErrorcode() == 0 && checkResult.isResult()) {
                    return true;
                }
            }
            log.error("检查用户登陆状态, 尚未登陆。webCommonParam:{}, requestUri: {}", webCommonParam, request.getRequestURI());
        } catch (Exception e) {
            log.error("检查用户登陆状态, 校验异常。webCommonParam:{}, requestUri: {}", webCommonParam, request.getRequestURI());
            throw new ContextedRuntimeException("检查用户登陆状态, 校验异常。", e)
                    .addContextValue("appId", webCommonParam.getAppId())
                    .addContextValue("kugouId", webCommonParam.getKugouId());
        }
        if (RechargePlatHelper.isHistoryProtocol(request.getRequestURI())) {
            ResponseUtils.writeJson(JSON.toJSONString(RechargePlatHelper.buildResp(SysResultCode.RECHARGE_TOKEN_INVALID)));
        } else {
            ResponseUtils.writeJson(JSON.toJSONString(JsonResult.result(SysResultCode.RECHARGE_TOKEN_INVALID, Maps.newHashMap())));
        }
        return false;
    }

    /**
     * 检查用户封禁状态
     *
     * @param webCommonParam 通用参数
     * @return 是否通过
     */
    private boolean checkBan(HttpServletRequest request, HttpServletResponse response, WebCommonParam webCommonParam) {
        long kugouId = webCommonParam.getKugouId();
        boolean isBanAccount = this.userFacadeService.isBannedAccount(kugouId);
        if (isBanAccount) {
            log.warn("检查用户封禁状态, 已被封禁, webCommonParam: {}", webCommonParam);
            if (RechargePlatHelper.isHistoryProtocol(request.getRequestURI())) {
                ResponseUtils.writeJson(JSON.toJSONString(RechargePlatHelper.buildResp(SysResultCode.RECHARGE_BAN_ACCOUNT)));
            } else {
                ResponseUtils.writeJson(JSON.toJSONString(JsonResult.result(SysResultCode.RECHARGE_BAN_ACCOUNT, Maps.newHashMap())));
            }
            return false;
        }
        return true;
    }
}
