package com.kugou.fanxing.recharge.common.filter;

import com.alibaba.fastjson.JSON;
import com.kugou.fanxing.recharge.constant.ProtocolConstants;
import com.kugou.fanxing.recharge.model.request.KugouOpenDispatchParam;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.util.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
public class KugouOpenParamFilter extends AbstractFilter {

    @Override
    public void doHttpFilter(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws IOException, ServletException {
        KugouOpenDispatchParam kugouOpenDispatchParam = parseKugouOpenRequest(request);
        WebCommonParam webCommonParam = buildWebCommonParam(request, kugouOpenDispatchParam);
        request.setAttribute(ProtocolConstants.WEB_COMMON_PARAM, webCommonParam);
        chain.doFilter(request, response);
    }

    private WebCommonParam buildWebCommonParam(HttpServletRequest request, KugouOpenDispatchParam kugouOpenDispatchParam) {
        WebCommonParam webCommonParam = new WebCommonParam();
        webCommonParam.setTimestamp(System.currentTimeMillis());
        webCommonParam.setHttpHost(request.getScheme() + "://" + StringUtils.defaultString(request.getHeader("host")) + "/");
        webCommonParam.setHost(StringUtils.defaultString(request.getRemoteHost()));
        webCommonParam.setReferer(StringUtils.defaultString(request.getHeader("referer")));
        webCommonParam.setUri(StringUtils.defaultString(request.getRequestURI()));
        webCommonParam.setIp(IpUtils.getClientIpAddress());
        webCommonParam.setUserAgent(StringUtils.defaultString(request.getHeader("User-Agent")));
        webCommonParam.setVersion(StringUtils.defaultString(request.getParameter("version")));
        webCommonParam.setToken(kugouOpenDispatchParam.getAccess_token());
        webCommonParam.setKugouOpenDispatchParam(kugouOpenDispatchParam);
        return webCommonParam;
    }

    /**
     * GET PARAMS
     * serverid=1994
     * servertime=1606715540
     * appid=3244
     * signature=3250d0efd60b93260651a786f85d07d9
     * clientver=1
     * dfid=no-dfid
     * uuid=no-uuid
     * mid=no-mid
     * <p>
     * BODY PARAMS
     * {
     * "method": "game_recharge.getOrderForAlipayM",
     * "user_openid": "f50e6bca4f09cf96adb85c87df0decdf",
     * "openappid": "10063",
     * "business_data": "{\"amount\": 1}",
     * "access_token": "4d5949162bf0a4efb93ce374acbec414ad42b071"
     * }
     *
     * @param request 请求对象
     * @return 酷狗开放平台转发参数
     */
    private KugouOpenDispatchParam parseKugouOpenRequest(HttpServletRequest request) {
        try {
            final String jsonBody = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8);
            KugouOpenDispatchParam kugouOpenDispatchParam = JSON.parseObject(jsonBody, KugouOpenDispatchParam.class);
            kugouOpenDispatchParam.setServerid(StringUtils.defaultString(request.getParameter("serverid")));
            kugouOpenDispatchParam.setServertime(StringUtils.defaultString(request.getParameter("servertime")));
            kugouOpenDispatchParam.setAppid(StringUtils.defaultString(request.getParameter("appid")));
            kugouOpenDispatchParam.setSignature(StringUtils.defaultString(request.getParameter("signature")));
            kugouOpenDispatchParam.setClientver(StringUtils.defaultString(request.getParameter("clientver")));
            kugouOpenDispatchParam.setDfid(StringUtils.defaultString(request.getParameter("dfid")));
            kugouOpenDispatchParam.setUuid(StringUtils.defaultString(request.getParameter("uuid")));
            kugouOpenDispatchParam.setMid(StringUtils.defaultString(request.getParameter("mid")));
            log.warn("酷狗开放平台请求参数解析，解析完毕。kugouOpenDispatchParam: {}, request: {}, bodyJson: {}",
                    kugouOpenDispatchParam, request.getParameterMap(), jsonBody);
            return kugouOpenDispatchParam;
        } catch (Exception e) {
            log.error("酷狗开放平台请求参数解析，解析异常。request: {}", request.getParameterMap(), e);
            throw new ContextedRuntimeException("酷狗开放平台请求参数解析，解析异常。", e);
        }

    }
}
