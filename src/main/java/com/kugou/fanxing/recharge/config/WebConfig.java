package com.kugou.fanxing.recharge.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.kugou.fanxing.recharge.common.bind.ParamNameProcessor;
import com.kugou.fanxing.recharge.common.converter.WebCommonParamConverter;
import com.kugou.fanxing.recharge.common.filter.KugouOpenParamFilter;
import com.kugou.fanxing.recharge.common.filter.WebParamContextFilter;
import com.kugou.fanxing.recharge.common.filter.XssFilter;
import com.kugou.fanxing.recharge.common.interceptor.KugouOpenAuthInterceptor;
import com.kugou.fanxing.recharge.common.interceptor.WebRequestInterceptor;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.DefaultServletHandlerConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * 服务配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableApolloConfig
@EnableTransactionManagement
public class WebConfig extends WebMvcConfigurerAdapter {

    private static final String DEFAULT_PATH = "/**";

    @Autowired
    private WebRequestInterceptor webRequestInterceptor;
    @Autowired
    private KugouOpenAuthInterceptor kugouOpenAuthInterceptor;

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        super.addArgumentResolvers(argumentResolvers);
        argumentResolvers.add(new WebCommonParamConverter());
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        super.addInterceptors(registry);
        // 酷狗直播认证拦截
        registry.addInterceptor(webRequestInterceptor).addPathPatterns(DEFAULT_PATH)
                .excludePathPatterns(
                        UrlConstants.DEFAULT_RECHARGE_OPTIONS,
                        UrlConstants.DEFAULT_RECHARGE_OPTIONS_BY_PID,
                        UrlConstants.RECHARGE_PRESENT_QUERY,
                        UrlConstants.CAN_TMALL_RECHARGE,
                        UrlConstants.TAOBAO_GAME_CHARGE_ZC_VERIFY,
                        UrlConstants.RECHARGE_THRIFT_PATTERNS,
                        UrlConstants.WITH_DRAW_ORDER_VERIFY,
                        UrlConstants.WITH_DRAW_ORDER_VERIFY_WECHAT,
                        UrlConstants.WITH_DRAW_ORDER_VERIFY_SHENTIAN,
                        UrlConstants.OFFLINE_RECHARGE_SWITCH,
                        UrlConstants.KUGOU_OPEN_PATTERNS,
                        UrlConstants.RECHARGE_CALLBACK_COIN,
                        UrlConstants.INTRANET_PATTERNS,
                        UrlConstants.KUGOU_OPEN_PURCHASE_CALLBACK,
                        UrlConstants.GET_RECHARGE_REBATE,
                        UrlConstants.CAN_TMALL_RECHARGE_KW,
                        UrlConstants.TAOBAO_PURCHASE_CALLBACK_KW,
                        UrlConstants.NOVICE_RECHARGE_CONFIG_NEW,
                        UrlConstants.GET_DF_RECHARGE_CONFIG,
                        UrlConstants.RECHARGE_AMOUNT_GEAE,
                        UrlConstants.GET_RECHARGE_AMOUNT_GEAR,
                        UrlConstants.AIRWALLEX_GET_BANK_NAME_LIST,
                        UrlConstants.PDD_PAYMENT_NOTIFY,
                        UrlConstants.PDD_CREATE_ORDER_CHECK
                );
        // 酷狗开放平台认证拦截
        registry.addInterceptor(kugouOpenAuthInterceptor).addPathPatterns(UrlConstants.KUGOU_OPEN_PATTERNS);
    }

    @Override
    public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {
        configurer.enable();
    }

    @Bean
    public FilterRegistrationBean webRequestParamFilter() {
        FilterRegistrationBean registration = new FilterRegistrationBean(new WebParamContextFilter());
        registration.setName("webRequestParamFilter");
        registration.addUrlPatterns(DEFAULT_PATH.replace("**", "*"));
        registration.setOrder(1);
        registration.addInitParameter("excludePaths", excludePaths(
                UrlConstants.RECHARGE_THRIFT_PATTERNS.replace("**", "*"),
                UrlConstants.KUGOU_OPEN_PATTERNS.replace("**", "*")
        ));
        return registration;
    }

    @Bean
    public FilterRegistrationBean kugouOpenParamFilter() {
        FilterRegistrationBean registration = new FilterRegistrationBean(new KugouOpenParamFilter());
        registration.setName("kugouOpenParamFilter");
        registration.addUrlPatterns(UrlConstants.KUGOU_OPEN_PATTERNS.replace("**", "*"));
        registration.setOrder(2);
        return registration;
    }

    @Bean
    public FilterRegistrationBean xssFilter() {
        FilterRegistrationBean registration = new FilterRegistrationBean(new XssFilter());
        registration.setName("xssFilter");
        registration.addUrlPatterns(DEFAULT_PATH.replace("**", "*"));
        registration.setOrder(3);
        registration.addInitParameter("excludePaths", excludePaths(
                UrlConstants.RECHARGE_THRIFT_PATTERNS.replace("**", "*"),
                UrlConstants.AIRWALLEX_APP_CREATE_ORDER.replace("**", "*")
        ));
        return registration;
    }

    private String excludePaths(String... excludePaths) {
        return StringUtils.join(excludePaths, ",");
    }

    @Bean
    protected ParamNameProcessor paramNameProcessor() {
        return new ParamNameProcessor();
    }

    @Bean
    public BeanPostProcessor beanPostProcessor() {
        return new BeanPostProcessor() {
            @Override
            public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
                return bean;
            }
            @Override
            public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
                if (bean instanceof RequestMappingHandlerAdapter) {
                    RequestMappingHandlerAdapter adapter = (RequestMappingHandlerAdapter) bean;
                    List<HandlerMethodArgumentResolver> argumentResolvers = new ArrayList<>(adapter.getArgumentResolvers());
                    argumentResolvers.add(0, paramNameProcessor());
                    adapter.setArgumentResolvers(argumentResolvers);
                }
                return bean;
            }
        };
    }
}
