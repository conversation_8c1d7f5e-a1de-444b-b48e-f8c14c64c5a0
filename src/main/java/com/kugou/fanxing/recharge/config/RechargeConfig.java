package com.kugou.fanxing.recharge.config;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.kugou.config.Env;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.bo.KupayAppTypeInfoBO;
import com.kugou.fanxing.recharge.model.bo.KupayServerInfoBO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.util.SpringContextUtils;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 充值核心配置
 * RechargeConfig.php
 *
 * <AUTHOR>
 */
@Slf4j
@Getter
@ToString
@Configuration
public class RechargeConfig {

    @Autowired
    private Env env;

    /**
     * 机房信息
     */
    @Value("${dataCenter.name:bj-sjq}")
    private String dataCenter;

    /**
     * 支付网关公网地址 pay-service.kugou.com
     * $pc.url
     */
    @Value("${kupay.internet:http://kupay.kugou.com}")
    private String kupayInternet;

    /**
     * 支付网关内网地址 pay-service.kgidc.cn
     * $pc.intranet
     */
    @Value("${kupay.intranet:http://kupay.kugou.com}")
    private String kupayIntranet;

    /**
     * 支付网关内网地址-海外 pay-service.kgidc.cn
     * $pc.intranet
     */
    @Value("${kupay.intranet.out.sea:http://kupay.kugou.com}")
    private String kupayIntranetOutSea;

    /**
     * 酷狗回调校验秘钥
     * $pc.callBackKey
     */
    @Value("${kupay.callbackKey:kugoufanxing2016}")
    private String callBackKey;

    /**
     * 签名类型
     * $pc.sign_type
     */
    @Value("${signType:md5}")
    private String signType;

    /**
     * $pc.webMethod
     */
    @Value("${webMethod:POST}")
    private String webMethod;

    /**
     * 同步回调地址
     * $pc.sync_url
     */
    @Value("${syncUrl:http://fanxing.kugou.com/index.php?action=rechargeFinish}")
    private String syncUrl;

    /**
     * Paypal同步回调地址
     * $pc.sync_url_paypal
     */
    @Value("${syncUrlPaypal:http://fanxing.kugou.com/index.php?action=rechargePaypalFinish}")
    private String syncUrlPaypal;

    /**
     * 支付宝二维码支付, 同步回调地址
     * $pc.sync_url_alipay_qr
     */
    @Value("${syncUrlAlipayQr:http://fanxing.kugou.com/index.php?action=userRechargeList}")
    public String syncUrlAlipayQr;

    @Autowired
    private StrategyConfig strategyConfig;
    @Autowired
    private ApolloConfigService apolloConfigService;

    public static final String O_COIN_BUSINESS_ID = "10000";

    /**
     * 支付宝网银
     */
    public static final Map<Integer, String> alipayBank = ImmutableMap.<Integer, String>builder()
            .put(0, "ICBCB2C")    //中国工商银行
            .put(1, "CMB")        //招商银行
            .put(2, "CCB")        //中国建设银行
            .put(3, "BOCB2C")     //中国银行
            .put(4, "ABC")        //中国农业银行
            .put(5, "COMM")       //交通银行
            .put(6, "SPDB")       //浦发银行
            .put(7, "GDB")        //广东发展银行
            .put(8, "CITIC")      //中信银行
            .put(9, "CEB-DEBIT")  //中国光大银行
            .put(10, "CIB")       //兴业银行
            .put(12, "CMBC")      //中国民生银行
            .put(13, "HZCBB2C")   //杭州银行
            .put(14, "SHBANK")    //上海银行
            .put(15, "NBBANK")    //宁波银行
            .put(16, "SPABANK")   //平安银行
            .put(17, "BJRCB")     //北京农村商业银行
            .put(22, "FDB")       //富滇银行
            .put(23, "BJBANK")    //北京银行
            .put(24, "POSTGC")    //中国邮政储蓄银行
            .put(25, "HXBANK")    //华夏银行
//            .put(26, "ICBCBTB")    //中国工商银行（B2B）
//            .put(27, "ABCBTB")    //中国农业银行（B2B）
//            .put(28, "SPDBB2B")    //上海浦东发展银行（B2B）
//            .put(29, "BOCBTB")    //中国银行（B2B）
//            .put(30, "CMBBTB")    //招商银行（B2B）
//            .put(31, "CCBBTB")    //中国建设银行（B2B）
            .build();

    /**
     * 充值开关名
     */
    public static final Map<Integer, String> serverOptionRechargeList = ImmutableMap.<Integer, String>builder()
            .put(3, "rechargeZhifubao")         //支付宝
            .put(24, "rechargeWangyin")         //网银
            .put(29, "rechargeCaifutong")       //财付通
            .put(39, "rechargeWeixin")          //微信
            .put(25, "rechargePhone")           //手机充值卡
            .put(27, "rechargeMessage")         //手机短信
            .put(17, "rechargeGuhuaV")          //固话V币
            .put(100, "rechargeGameCard")       //游戏卡
            .put(520, "rechargePaypal")         //paypal
            .put(40, "rechargeWeixinMobPlat")   //微信支付(手机平台)
            .put(41, "rechargeWxgzh")           //微信公众号
            .put(30, "rechargeMzhifubao")       //手机支付宝
            .put(28, "rechargePhone")           //手机充值卡(移动端)
            .put(35, "rechargeMyinlian")        //手机银联
            .put(1006, "rechargeAppstore")      //苹果充值
            .put(1012, "rechargeTmall")         //天猫充值
            .build();

    /**
     * 需要在下单的时候,手机端充值
     */
    public static final List<Integer> mobilePayType = Lists.newArrayList(
            40, //微信支付（手机）
            30, //支付宝（手机）
            28, //手机充值卡（手机）
            35, //银联(手机)
            41, //微信公众号
            31, //支付宝wap
            32  //微信wap
    );

    /**
     * 风控策略根据ID换取相应的充值渠道标识
     */
    public static final Map<String, List<Integer>> riskStrategyPayTypeIdRange = ImmutableMap.<String, List<Integer>>builder()
            .put("alipay", Lists.newArrayList(3, 30, 31, 33, 231, 232, 233))
            .put("wechat", Lists.newArrayList(39, 40, 41, 32, 221, 222, 223))
            .put("ios", Lists.newArrayList(1006))
            .put("paypal", Lists.newArrayList(520))
            .put("unipay", Lists.newArrayList(35))
            .put("tmall", Lists.newArrayList(1012))
            .put("bankpay", Lists.newArrayList(24))
            .build();

    /**
     * 订单的过期时间，格式为yyyyMMddHHmmss，**************，注意：过期时间 - now > 5分钟，否则该参数作废
     *
     * @return 订单的过期时间
     */
    public String getExpireTime() {
        Date expireTime = DateUtils.addMinutes(new Date(), 5);
        return DateFormatUtils.format(expireTime, "yyyyMMddHHmmss");
    }

    /**
     * 充值接口地址前缀
     */
    public String getActionUrlPrefix(PayTypeIdEnum payTypeIdEnum) {
        Map<Integer, String> actionUrlPrefix = getActionUrlPrefixMap();
        return Optional.ofNullable(actionUrlPrefix.get(payTypeIdEnum.getPayTypeId()))
                .orElseThrow(() -> new BizException(SysResultCode.E_10000018).addContextValue("payTypeIdEnum", payTypeIdEnum));
    }

    /**
     * 充值接口地址前缀
     */
    public Map<Integer, String> getActionUrlPrefixMap() {
        return ImmutableMap.<Integer, String>builder()
                .put(3, this.kupayInternet + "/v1/alipay?")             //支付宝
                .put(24, this.kupayInternet + "/v1/alipay?")            //银行卡
                .put(29, this.kupayInternet + "/v1/tenpaypay?")         //财付通
                .put(39, this.kupayInternet + "/v1/wxnativepay?")       //微信
                .put(25, this.kupayInternet + "/v1/t91pay?")            //手机充值卡
                .put(27, this.kupayInternet + "/v1/wyingpay?")          //手机短信
                .put(17, this.kupayInternet + "/v1/wyingpay?")          //固话V币
                .put(100, this.kupayInternet + "/v1/merrypay?")         //游戏卡
                .put(20, this.kupayInternet + "/v1/merrypay?")          //游戏卡
                .put(21, this.kupayInternet + "/v1/merrypay?")          //游戏卡
                .put(22, this.kupayInternet + "/v1/merrypay?")          //游戏卡
                .put(23, this.kupayInternet + "/v1/merrypay?")          //游戏卡
                .put(520, this.kupayInternet + "/v1/paypal?")           //paypal
                .put(521, this.kupayInternet + "v1/gp_order?")           //googlePlay
                .put(40, this.kupayIntranet + "/v1/wxapppay?")          //微信支付(手机平台)
                .put(30, this.kupayIntranet + "/v1/mobilealipay?")      //手机支付宝
                .put(28, this.kupayIntranet + "/v1/t19pay?")            //手机充值卡(移动端)
                .put(35, this.kupayIntranet + this.getUnionPayPath())   //手机银联
                .put(41, this.kupayInternet + "/v1/wxgzhpay?")          //微信公众号
                .put(42, this.kupayIntranet + "/v1/wxminiprogram")      //微信小程序
                .put(31, this.kupayInternet + "/v1/wapalipay?")         //支付宝WAP(H5页面)
                .put(32, this.kupayIntranet + "/v1/wxh5pay?")           //微信(H5页面)
                .put(33, this.kupayIntranet + "/v1/alih5pay?")          //支付宝(H5页面)
                .put(1006, this.kupayIntranet + "/v1/iospay?")          //苹果充值
                .build();
    }

    public Optional<String> getActionQrUrlPrefix(PayTypeIdEnum payTypeIdEnum) {
        Map<Integer, String> actionQrUrlPrefix = ImmutableMap.<Integer, String>builder()
                .put(3, this.kupayIntranet + "/v1/alipayqrcode?")       //支付宝
                .put(39, this.kupayIntranet + "/v1/wxnativepay?")       //微信
                .build();
        return Optional.ofNullable(actionQrUrlPrefix.get(payTypeIdEnum.getPayTypeId()));
    }

    public String getUnionPayPath() {
        String unionVersion = SpringContextUtils.getHttpServletRequest().getParameter("union_ver");
        return "2".equals(unionVersion) ? "/v1/unionpay_v2?" : "/v1/unionpay?";
    }

    /**
     * 异步回调接口
     * $pc.notify_url
     */
    public String getNotifyUrl(ReTypeEnum reType) {
        if (ReTypeEnum.isRecharge(reType.getReTypeId())) {
            return this.getRechargeNotifyUrl(reType);
        }
        if (ReTypeEnum.isPurchase(reType.getReTypeId())) {
            return this.getRechargeNotifyUrl(reType);
        }
        log.error("充值配置加载，充值回调配置缺失。reType: {}", reType);
        throw new BizException(SysResultCode.RECHARGE_MISS_NOTIFY_URL).addContextValue("reType", reType);
    }

    public String getRechargeNotifyUrl(ReTypeEnum reType) {
        String intranetHost = getBjZuulAddress();
        if (apolloConfigService.enableRechargeZuulUrlPrefix()) {
            intranetHost = apolloConfigService.getRechargeZuulUrlPrefix();
            log.warn("充值配置获取zuul网关地址，使用[recharge.zuul.url]配置。zuul: {}", intranetHost);
        }
        String notifyUrl = String.format("http://%s%s", intranetHost, reType.getNewNotifyUri());
        log.warn("获取支付网关通知回调地址。reType: {}, notifyUrl: {}", reType, notifyUrl);
        return notifyUrl;
    }

    private String getBjZuulAddress() {
        return apolloConfigService.getBjZuulAddress();
    }

    private String getGzZuulAddress() {
        return apolloConfigService.getGzZuulAddress();
    }

    /**
     * 获取fx_recharge充值服务内网域名
     */
    public String getPhpRechargeIntranet() {
        String intranetHost = getBjZuulAddress();
        // 测试环境走服务发现访问
        if (env.isDev() || env.isTest()) {
            intranetHost = getBjZuulAddress() + "/fx_recharge";
        }
        // 生产环境走代理配置访问
        if (env.isProd()) {
            intranetHost = getBjZuulAddress();
            if (this.getDataCenter().startsWith("bj")) {
                intranetHost = getBjZuulAddress();
            }
            if (this.getDataCenter().startsWith("gz")) {
                intranetHost = getGzZuulAddress();
            }
        }
        log.warn("获取fx_recharge充值服务内网域名，获取成功。intranetHost: {}", intranetHost);
        return intranetHost;
    }

    public int getDataCenterZoneId() {
        if (dataCenter.startsWith("gz")) {
            return 2;
        } else {
            return 0;
        }
    }

    /**
     * 判断是否为正在使用的充值类型
     *
     * @param payTypeId 充值类型
     * @return 是否为正在使用的充值类型
     */
    public boolean isRechargeWithChange(int payTypeId) {
        Optional<PayTypeIdEnum> optionalPayTypeIdEnum = PayTypeIdEnum.of(payTypeId);
        return optionalPayTypeIdEnum.isPresent();
    }

    public String getBankCodeByAlipayBankId(int alipayBankId) {
        return alipayBank.getOrDefault(alipayBankId, "0");
    }

    public boolean existsAlipayBankId(int alipayBankId) {
        return Objects.nonNull(alipayBank.get(alipayBankId));
    }

    /**
     * 获取安卓手机充值渠道列表
     *
     * @param stdPlat 平台版本号
     * @param version APP版本号
     * @return 安卓充值渠道列表
     */
    public List<Integer> getMPayList(int stdPlat, int version) {
        List<Integer> androidPayTypeIdList = apolloConfigService.getPayTypeList();
        if (stdPlat > 0 && version > 0) {
            return androidPayTypeIdList.stream()
                    // 航母版本号低于9207的不显示银联， ********
                    .filter(payTypeId -> !(payTypeId == 35 && stdPlat == 5 && version < 9207))
                    .collect(Collectors.toList());
        }
        return androidPayTypeIdList;
    }

    /**
     * 根据充值方式获取网关配置（默认：1084）
     *
     * @param payTypeIdEnum 充值方式
     * @return 网关配置
     */
    public KupayAppInfoBO getKupayAppIdByPayType(PayTypeIdEnum payTypeIdEnum) {
        Map<Integer, KupayAppInfoBO> kupayAppInfoPOMap = this.apolloConfigService.getKupayAppId();
        Optional<Map.Entry<Integer, KupayAppInfoBO>> optionalEntry = kupayAppInfoPOMap.entrySet().stream().filter(entry -> {
            List<Integer> payTypeIds = entry.getValue().getPayTypeIds();
            return CollectionUtils.isNotEmpty(payTypeIds) && payTypeIds.contains(payTypeIdEnum.getPayTypeId());
        }).findFirst();
        if (optionalEntry.isPresent()) {
            KupayAppInfoBO kupayAppInfoPO = optionalEntry.get().getValue();
            log.warn("根据充值方式获取网关配置，使用特定配置。payTypeIdEnum: {}, KupayAppInfoPO: {}", payTypeIdEnum, kupayAppInfoPO);
            return kupayAppInfoPO;
        }
        KupayAppInfoBO kupayAppInfoPO = kupayAppInfoPOMap.entrySet().stream()
                .filter(entry -> entry.getKey() == 1084)
                .findFirst()
                .orElseThrow(() -> new ContextedRuntimeException("默认酷狗支付网关配置不存在[appId=1084]"))
                .getValue();
        log.warn("根据充值方式获取网关配置，使用默认配置。payTypeIdEnum: {}, KupayAppInfoPO: {}", payTypeIdEnum, kupayAppInfoPO);
        return kupayAppInfoPO;
    }

    /**
     * 根据[kupayAppId]获取网关配置
     *
     * @param kupayAppId 酷狗网关分配业务ID
     * @return 酷狗网关配置信息
     */
    public KupayAppInfoBO getKupayAppIdByAppId(int kupayAppId) {
        Map<Integer, KupayAppInfoBO> kupayAppInfoPOMap = this.apolloConfigService.getKupayAppId();
        if (!kupayAppInfoPOMap.containsKey(kupayAppId) || Objects.isNull(kupayAppInfoPOMap.get(kupayAppId))) {
            throw new ContextedRuntimeException("根据[kupayAppId]获取网关配置，获取配置失败。")
                    .addContextValue("kupayAppId", kupayAppId);
        }
        return kupayAppInfoPOMap.get(kupayAppId);
    }

    /**
     * 根据[kupayAppId]获取网关配置
     *
     * @param kupayServerId 酷狗网关分配业务ID
     * @return 酷狗网关配置信息
     */
    public KupayServerInfoBO getKupayServerInfoByServerId(int kupayServerId) {
        Map<Integer, KupayServerInfoBO> kupayServerInfoPOMap = this.apolloConfigService.getKupayServerId();
        if (!kupayServerInfoPOMap.containsKey(kupayServerId) || Objects.isNull(kupayServerInfoPOMap.get(kupayServerId))) {
            throw new ContextedRuntimeException("根据[kupayAppId]获取网关配置，获取配置失败。")
                    .addContextValue("kupayServerId", kupayServerId);
        }
        return kupayServerInfoPOMap.get(kupayServerId);
    }

    /**
     * 微信支付appId配置，对应传入pid和大酷狗的参数apptype
     * 详细看文档：http://doc.kugou.net/showdoc-master/web/#/8?page_id=1708
     *
     * @param pid 平台代号
     * @return AppType配置
     */
    public String getKupayAppTypeInfoByPid(int pid) {
        Optional<KupayAppTypeInfoBO> optionalKupayAppTypeInfoBO = this.getKupayAppTypeInfoBO(pid);
        if (optionalKupayAppTypeInfoBO.isPresent() && StringUtils.isNotBlank(optionalKupayAppTypeInfoBO.get().getAppType())) {
            return optionalKupayAppTypeInfoBO.get().getAppType();
        }
        return "fanxing";
    }

    public Optional<KupayAppTypeInfoBO> getKupayAppTypeInfoBO(int pid) {
        try {
            List<KupayAppTypeInfoBO> kupayAppTypeInfoBOList = this.apolloConfigService.getKupayAppTypeInfoList();
            Optional<KupayAppTypeInfoBO> optionalKupayAppTypeInfoBO = kupayAppTypeInfoBOList.stream()
                    .filter(kupayAppTypeInfoBO -> pid == kupayAppTypeInfoBO.getPid()).findFirst();
            if (!optionalKupayAppTypeInfoBO.isPresent()) {
                log.warn("读取酷狗支付网关微信AppType配置，对应配置不存在使用默认配置。pid: {}, kupayAppTypeInfoBOList: {}", pid, kupayAppTypeInfoBOList);
                return Optional.empty();
            }
            // 兼容没有配置kupayAppId的情况，防止返回包含kupayAppId=0的配置项
            KupayAppTypeInfoBO kupayAppTypeInfoBO = optionalKupayAppTypeInfoBO.get();
            if (kupayAppTypeInfoBO.getKupayAppId() == 0) {
                kupayAppTypeInfoBO.setKupayAppId(1084);
            }
            return Optional.of(kupayAppTypeInfoBO);
        } catch (Exception e) {
            log.warn("读取酷狗支付网关微信AppType配置，加载异常。pid: {}", pid, e);
        }
        return Optional.empty();
    }

    public KupayAppInfoBO getKupayAppInfo(int stdPlat) {
        Optional<KupayAppTypeInfoBO> optionalKupayAppTypeInfoBO = this.getKupayAppTypeInfoBO(stdPlat);
        return optionalKupayAppTypeInfoBO.map(kupayAppTypeInfoBO -> {
            int kupayAppId = kupayAppTypeInfoBO.getKupayAppId();
            return getKupayAppIdByAppId(kupayAppId);
        }).orElseThrow(() -> new BizException(SysResultCode.UNKNOWN_KUPAY_ID).addContextValue("stdPlat", stdPlat));
    }

    public KupayAppInfoBO getKupayAppInfoWithDefault(int stdPlat,KupayAppInfoBO defaultValue) {
        if(stdPlat < apolloConfigService.getNewMinPid() && !apolloConfigService.allowGetKupayConfigByPid(stdPlat)) {
            return defaultValue;
        }
        Optional<KupayAppTypeInfoBO> optionalKupayAppTypeInfoBO = this.getKupayAppTypeInfoBO(stdPlat);
        return optionalKupayAppTypeInfoBO.map(kupayAppTypeInfoBO -> {
            int kupayAppId = kupayAppTypeInfoBO.getKupayAppId();
            return getKupayAppIdByAppId(kupayAppId);
        }).orElse(defaultValue);
    }

    public String getOpenNotifyUrl() {
        String intranetHost = "zuultest.fxwork.kugou.com/platform_recharge_service";
        if (env.isProd()) {
            intranetHost = getBjZuulAddress() + "/platform_recharge_service";
            if (this.getDataCenter().startsWith("bj")) {
                intranetHost = getBjZuulAddress() + "/platform_recharge_service";
            }
            if (this.getDataCenter().startsWith("gz")) {
                intranetHost = getGzZuulAddress() + "/platform_recharge_service";
            }
        }
        String notifyUrl = String.format("http://%s%s", intranetHost, UrlConstants.KUGOU_OPEN_PURCHASE_CALLBACK);
        log.warn("获取开放支付网关通知回调地址。notifyUrl: {}", notifyUrl);
        return notifyUrl;
    }

    public String getRechargeZuulNotifyUrl(String uri) {
        String intranetHost = "http://" + getBjZuulAddress() + "";
        if (env.isProd()) {
            intranetHost = "http://" + getBjZuulAddress() + "/platform_recharge_service";
            if (this.getDataCenter().startsWith("bj")) {
                intranetHost = "http://" + getBjZuulAddress() + "/platform_recharge_service";
            }
            if (this.getDataCenter().startsWith("gz")) {
                intranetHost = "http://" + getGzZuulAddress() + "/platform_recharge_service";
            }
        }
        String notifyUrl =  StringUtils.join(intranetHost, uri);
        log.warn("获取酷狗支付网关通知回调地址。notifyUrl: {}", notifyUrl);
        return notifyUrl;
    }

    /**
     * 是否直接使用客户端传递的AppType获取酷狗支付网关微信配置
     *
     * @param appType 酷狗支付网关微信配置类型
     * @return 是否直接使用客户端传递的AppType参数
     */
    public boolean useKupayAppTypeDirectly(String appType) {
        if (StringUtils.isBlank(appType)) {
            return false;
        }
        return this.apolloConfigService.useKupayAppTypeDirectly(appType);
    }
}
