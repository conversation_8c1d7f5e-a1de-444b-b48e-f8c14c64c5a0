package com.kugou.fanxing.recharge.config;

import com.google.common.collect.Lists;
import com.kugou.config.Env;
import com.kugou.fanxing.recharge.constant.ServerRoomEnum;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 机房部署配置
 * StrategyConfig.php
 */
@Slf4j
@Getter
@ToString
@Configuration
public class StrategyConfig {

    @Autowired
    private Env env;
    @Autowired
    private ApolloConfigService apolloConfigService;

    @Value("${STRATEGY_CONFIG_NAME:bjzw}")
    private String strategyConfigName;

    // 获取当前使用的策略名，返回值由发版工具修改
    public String getName() {
        if (env.isDev()) {
            return ServerRoomEnum.SERVER_ROOM_BJZW.getName();
        }
        //更改为RMS发版工具替换
        return strategyConfigName;
    }

    /**
     * 获取当前正在使用的机房名称
     */
    public static List<String> getAllowNames() {
        return Lists.newArrayList(ServerRoomEnum.SERVER_ROOM_BJZW.getName(), ServerRoomEnum.SERVER_ROOM_GZ.getName());
    }

    /**
     * 获取当前正在使用的机房编号
     */
    public static List<Integer> getAllowInt() {
        return Lists.newArrayList(ServerRoomEnum.SERVER_ROOM_BJZW.getId(), ServerRoomEnum.SERVER_ROOM_GZ.getId());
    }


    /**
     * 获取当前机房所属的机房编号
     */
    public Optional<Integer> getLocalServerRoom() {
        String serverRoomName = this.getName();
        return getServerRoomByServerRoomName(serverRoomName);
    }


    /**
     * 根据机房名称获取对应的机房编号
     */
    public Optional<Integer> getServerRoomByServerRoomName(String serverRoomName) {
        return Arrays.stream(ServerRoomEnum.values())
                .filter(serverRoomEnum -> serverRoomEnum.getName().equals(serverRoomName))
                .findFirst().map(ServerRoomEnum::getId);
    }

    /**
     * 根据机房编号获取对应的机房名称
     */
    public Optional<String> getServerNameByServerRoom(int serverRoomId) {
        return Arrays.stream(ServerRoomEnum.values())
                .filter(serverRoomEnum -> serverRoomEnum.getId() == serverRoomId)
                .findFirst().map(ServerRoomEnum::getName);
    }

    /**
     * 获取双活开关（0:关闭；1:开启）
     */
    @Deprecated
    public int getAcrossConf() {
        // 双活开关永久开启，避免关闭导致的风险不可控
        return 1;
    }
}
