package com.kugou.fanxing.recharge.config;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class RefundConfig {

    @ApolloConfig("refund.logic")
    private Config apolloConfig;

    public boolean isUserGrayLogicOpen() {
        return apolloConfig.getBooleanProperty("refund.white.user.list.enable",false);
    }

    /**
     * 会被扣币的酷狗id白名单，英文逗号分隔
     *
     * @return 扣币酷狗id白名单
     */
    public List<String> getGrayKugouIdList() {
        return getArrayProperty("refund.white.user.list");

    }

    /**
     * 不需要执行退款扣币的订单id黑名单，在名单内的苹果订单id，不会被扣币，英文逗号分隔
     *
     */
    public List<String> getBlackTransactionId() {
        return getArrayProperty("app.store.refund.black.transaction.id.list");

    }

    private List<String> getArrayProperty(String key)
    {
        String[] resultLsit = new String[]{};
        try {
            resultLsit = apolloConfig.getArrayProperty(key, ",", resultLsit);
        } catch (Exception e) {
            log.error("解析Apollo配置异常, key: {}", key, e);
        }
        return Arrays.asList(resultLsit);
    }
}
