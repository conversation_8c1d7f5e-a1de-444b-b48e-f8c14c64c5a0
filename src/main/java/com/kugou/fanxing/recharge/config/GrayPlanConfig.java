package com.kugou.fanxing.recharge.config;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.kugou.fanxing.commons.util.JsonUtils;
import com.kugou.fanxing.recharge.model.vo.GrayNewPageOption;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/13
 */
@Component
public class GrayPlanConfig {

    @ApolloConfig
    private Config config;

    private static final Logger log = LoggerFactory.getLogger(GrayPlanConfig.class);


    public Set<Integer> noviceRechargeGrayPlatform() {
        try {
            String configValue = config.getProperty("novice.recharge.gray.platform", "97");
            if (StringUtils.isBlank(configValue)) {
                return new HashSet<>(0);
            }
            return Arrays.stream(configValue.split(",")).map(Integer::parseInt).collect(Collectors.toSet());
        } catch (Exception e) {
            log.warn("首充灰度马甲包平台号配置错误 ", e);
            return new HashSet<>(0);
        }
    }

    /**
     * 每个灰度方案进入充值页默认选中的金额
     */
    public Map<Integer, Integer> grayDefaultCoin() {
        String configValue = config.getProperty("novice.recharge.gray.default.coin","{\"1\":100,\"2\":100,\"3\":100,\"4\":100,\"5\":100,\"6\":100}");
        if (StringUtils.isBlank(configValue)) {
            return new HashMap<>(0);
        }
        Map<Integer, Integer> result = JsonUtils.parseMap(configValue, Integer.class, Integer.class);
        return result == null ? new HashMap<>(0) : result;
    }

    /**
     * 每个灰度方案对应的充值页自定义框最少充值金额
     */
    public Map<Integer, Integer> grayMinCoin() {
        String configValue = config.getProperty("novice.recharge.gray.min.coin","{\"1\":1,\"2\":1,\"3\":6,\"4\":6,\"5\":10,\"6\":10}");
        if (StringUtils.isBlank(configValue)) {
            return new HashMap<>(0);
        }
        Map<Integer, Integer> result = JsonUtils.parseMap(configValue, Integer.class, Integer.class);
        return result == null ? new HashMap<>(0) : result;
    }

    /**
     * 每个灰度方案对应的第一档充值金额配置
     */
    public Map<Integer, GrayNewPageOption> grayNewPagePlanOption() {
        String configValue = config.getProperty("novice.recharge.gray.new.page.plan.option", "{\"1\":{\"imageUrl\":\"http://s3.fx.kgimg.com/fxstatic/20220525113517380601.png\",\"money\":0.1},\"2\":{\"imageUrl\":\"http://s3.fx.kgimg.com/fxstatic/20220525113517380601.png\",\"money\":0.1},\"3\":{\"imageUrl\":\"http://s3.fx.kgimg.com/fxstatic/20220524170602279361.png\",\"money\":6},\"4\":{\"imageUrl\":\"http://s3.fx.kgimg.com/fxstatic/20220524170602279361.png\",\"money\":6},\"5\":{\"imageUrl\":\"http://s3.fx.kgimg.com/fxstatic/20220524170643198706.png\",\"money\":10},\"6\":{\"imageUrl\":\"http://s3.fx.kgimg.com/fxstatic/20220524170643198706.png\",\"money\":10}}");
        if (StringUtils.isBlank(configValue)) {
            return new HashMap<>(0);
        }
        Map<Integer, GrayNewPageOption> result = JsonUtils.parseMap(configValue, Integer.class, GrayNewPageOption.class);
        return result == null ? new HashMap<>(0) : result;
    }

    /**
     * 新充值弹框通用充值金额配置（20元、50元、100元、300元）
     */
    public List<GrayNewPageOption> grayNewPageGeneralOptions() {
        String configValue = config.getProperty("novice.recharge.gray.new.page.general.option", "[{\"imageUrl\":\"http://s3.fx.kgimg.com/fxstatic/20220524170723255928.png\",\"money\":20},{\"imageUrl\":\"http://s3.fx.kgimg.com/fxstatic/20220524170759168073.png\",\"money\":50},{\"imageUrl\":\"http://s3.fx.kgimg.com/fxstatic/20220524170830529605.png\",\"money\":100},{\"imageUrl\":\"http://s3.fx.kgimg.com/fxstatic/20220524170904754933.png\",\"money\":300}]");
        if (StringUtils.isBlank(configValue)) {
            return new ArrayList<>(0);
        }
        List<GrayNewPageOption> result = JsonUtils.parseList(configValue, GrayNewPageOption.class);
        return result == null ? new ArrayList<>(0) : result;
    }

    /**
     * 默认的第一档充值金额配置
     */
    public GrayNewPageOption grayNewPageDefaultOption() {
        String configValue = config.getProperty("novice.recharge.gray.new.page.general.option", "{\"imageUrl\":\"http://s3.fx.kgimg.com/fxstatic/20220525113517380601.png\",\"money\":0.1}");
        if (StringUtils.isBlank(configValue)) {
            return new GrayNewPageOption(new BigDecimal("0.1"), "http://s3.fx.kgimg.com/fxstatic/20220525113517380601.png");
        }
        GrayNewPageOption grayNewPageOption = JsonUtils.parseObject(configValue, GrayNewPageOption.class);
        return grayNewPageOption == null ? new GrayNewPageOption(new BigDecimal("0.1"), "http://s3.fx.kgimg.com/fxstatic/20220525113517380601.png") : grayNewPageOption;
    }

    /**
     * 新充值弹框规则文案
     */
    public String grayNewPageRule() {
        return config.getProperty("novice.recharge.gray.new.page.rule", "1、活动时间：即日起，新用户首次充值有机会领取礼包；\n" +
                "2、“首次充值”指独恋注册用户进行第一次星币充值；\n" +
                "3、充值后需在7天内（以付款时间为准）领取，逾期则视为放弃礼包；\n" +
                "4、1元<=首充金额<20元，仅有机会领取3000星币礼包；\n" +
                "20元<=首充金额<50，仅有机会领取8000星币礼包；\n" +
                "50元<=首充金额<100，仅有机会领取15000星币礼包；\n" +
                "100元<=首充金额<300，仅有机会领取30000星币礼包；\n" +
                "300元<=首充金额，仅有机会领取50000星币礼包。\n" +
                "5、点歌券可在点歌广场的点歌界面查看并使用，有效期为7天；\n" +
                "6、领取礼包后，点歌券可在点歌过程中使用，聊天气泡将由系统自动发放；\n" +
                "7、首充礼包仅能在手机客户端和网站其中一个平台领取；\n" +
                "8、独恋保留对本次活动的最终解释权。");
    }

    /**
     * 新充值弹框默认选中的档位
     */
    public Map<Integer, Integer> defaultTabConfig() {
        String configValue = config.getProperty("novice.recharge.gray.default.tab","{\"1\":0,\"2\":0,\"3\":0,\"4\":0,\"5\":0}");
        if (StringUtils.isBlank(configValue)) {
            return new HashMap<>(0);
        }
        Map<Integer, Integer> result = JsonUtils.parseMap(configValue, Integer.class, Integer.class);
        return result == null ? new HashMap<>(0) : result;
    }


    public boolean noviceRechargeGraySwitch() {
        return config.getBooleanProperty("novice.recharge.gray.switch", false);
    }

}
