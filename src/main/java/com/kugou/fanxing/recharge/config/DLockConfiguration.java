package com.kugou.fanxing.recharge.config;

import com.kugou.cache.rediscluster.RedisClusterInterface;
import com.kugou.fanxing.commons.lock.DLockFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DLockConfiguration {
    @Bean
    public DLockFactory dLockFactory(RedisClusterInterface redisClient){
        return new DLockFactory(redisClient);
    }
}
