package com.kugou.fanxing.recharge.factory;

import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.factory.payment.AlipayAppPaymentService;
import com.kugou.fanxing.recharge.factory.payment.AppleAppPaymentService;
import com.kugou.fanxing.recharge.factory.payment.WechatAppPaymentService;
import com.kugou.fanxing.recharge.factory.payment.WechatJsapiPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PaymentServiceFactory {

    @Autowired
    private AlipayAppPaymentService alipayAppPaymentService;
    @Autowired
    private WechatAppPaymentService wechatAppPaymentService;
    @Autowired
    private WechatJsapiPaymentService wechatJsapiPaymentService;
    @Autowired
    private AppleAppPaymentService appleAppPaymentService;

    public PaymentService create(int payTypeId) {
        if (PayTypeIdEnum.isAlipayApp(payTypeId)) {
            return alipayAppPaymentService;
        }
        if (PayTypeIdEnum.isWechatApp(payTypeId)) {
            return wechatAppPaymentService;
        }
        if (PayTypeIdEnum.isWechatJsapi(payTypeId)) {
            return wechatJsapiPaymentService;
        }
        if (PayTypeIdEnum.isAppleApp(payTypeId)) {
            return appleAppPaymentService;
        }
        throw new BizException(SysResultCode.RECHARGE_PAY_TYPE_NOT_SUPPORT)
                .addContextValue("payTypeId", payTypeId);
    }

}
