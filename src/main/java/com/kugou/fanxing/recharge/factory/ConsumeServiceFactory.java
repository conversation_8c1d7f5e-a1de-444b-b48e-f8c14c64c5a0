package com.kugou.fanxing.recharge.factory;

import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.thrift.consume.read.service.PlatformConsumeReadService;
import com.kugou.fanxing.thrift.consume.service.PlatformConsumeService;
import com.kugou.fanxing.thrift.freeze.service.PlatformAddCoinService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023/8/13 15:36
 */
@Component
public class ConsumeServiceFactory {

    @Autowired
    @Qualifier("platformAddCoinService")
    private PlatformAddCoinService.Iface platformAddCoinService;
    @Autowired
    @Qualifier("singAddCoinService")
    private PlatformAddCoinService.Iface singAddCoinService;
    @Autowired
    @Qualifier("platformConsumeReadService")
    private PlatformConsumeReadService.Iface platformConsumeReadService;
    @Autowired
    @Qualifier("singConsumeReadService")
    private PlatformConsumeReadService.Iface singConsumeReadService;
    @Autowired
    @Qualifier("platformConsumeService")
    private PlatformConsumeService.Iface platformConsumeService;
    @Autowired
    @Qualifier("singConsumeService")
    private PlatformConsumeService.Iface singConsumeService;


    public PlatformAddCoinService.Iface getAddCoinService(int coinType) {
        if (CoinTypeEnum.isSingCoinType(coinType)) {
            return singAddCoinService;
        }
        return platformAddCoinService;
    }

    public PlatformConsumeReadService.Iface getConsumeReadService(int coinType) {
        if (CoinTypeEnum.isSingCoinType(coinType)) {
            return singConsumeReadService;
        }
        return platformConsumeReadService;
    }

    public PlatformConsumeService.Iface getConsumeService(int coinType) {
        if (CoinTypeEnum.isSingCoinType(coinType)) {
            return singConsumeService;
        }
        return platformConsumeService;
    }
}
