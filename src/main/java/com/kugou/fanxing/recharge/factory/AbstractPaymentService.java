package com.kugou.fanxing.recharge.factory;

import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.ReTypeEnum;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RechargeAcrossDao;
import com.kugou.fanxing.recharge.model.bo.CouponInfoBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.service.KupayService;
import com.kugou.fanxing.recharge.service.RechargeCommonService;
import com.kugou.fanxing.recharge.service.RechargeCouponService;
import com.kugou.fanxing.recharge.service.RechargeOrderService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.util.DateHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.Optional;

public abstract class AbstractPaymentService implements PaymentService {

    @Autowired
    protected RechargeCommonService rechargeCommonService;
    @Autowired
    protected RechargeCouponService rechargeCouponService;
    @Autowired
    protected RechargeAcrossDao rechargeAcrossDao;
    @Autowired
    protected OrderIdService orderIdService;
    @Autowired
    protected RechargeConfig rechargeConfig;
    @Autowired
    protected KupayService kupayService;
    @Autowired
    protected RechargeOrderService rechargeOrderService;

    protected RechargeAcrossPO createPurchaseOrder(PurchaseRequest purchaseRequest, Map<String, Object> extendParam,
                                                           Optional<CouponInfoBO> optionalCouponInfoBO) {
        PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.payTypeOf(purchaseRequest.getPayTypeId());
        CoinTypeEnum coinTypeEnum = CoinTypeEnum.of(purchaseRequest.getCoinType());
        RechargeAcrossPO rechargeAcrossPO = this.rechargeCommonService.buildRechargeAcross(purchaseRequest.getStdPlat(),
                purchaseRequest.getKugouId(), 0, purchaseRequest.getRechargeOrderNum(), payTypeIdEnum,
                purchaseRequest.getAmount(), purchaseRequest.getClientIp(), coinTypeEnum, optionalCouponInfoBO);
        rechargeAcrossPO.setReType(ReTypeEnum.RETYPE_RECHARGE.getReTypeId());
        rechargeAcrossPO.setBusinessId(String.valueOf(purchaseRequest.getBusinessId()));
        rechargeAcrossPO.setBusinessOrderNo(purchaseRequest.getOrderNo());
        rechargeAcrossPO.setAppVersion(purchaseRequest.getAppVersion());
        rechargeAcrossPO.setExtend(this.rechargeCommonService.buildExtendStr(rechargeAcrossPO, extendParam));
        return rechargeAcrossPO;
    }

    protected Map<String, Object> buildExtendParamByRequest(PurchaseRequest request) {
        Map<String, Object> extendParam = Maps.newTreeMap();
        extendParam.put("businessId", request.getBusinessId());
        extendParam.put("businessType", request.getBusinessId());
        extendParam.put("businessOrderNo", request.getOrderNo());
        extendParam.put("businessTime", String.valueOf(request.getOrderTime()));
        extendParam.put("fromKugouId", String.valueOf(request.getKugouId()));
        extendParam.put("addTime", String.valueOf(DateHelper.getCurrentSeconds()));
        extendParam.put("subject", StringUtils.defaultString(request.getDescription()));
        extendParam.put("roomId", request.getRoomId());
        extendParam.put("clientIp", StringUtils.defaultString(request.getClientIp()));
        extendParam.put("reType", ReTypeEnum.RETYPE_RECHARGE.getReTypeId());
        if (request.getOrderExpireTime() > DateHelper.getCurrentSeconds()) {
            extendParam.put("orderExpireTime", String.valueOf(request.getOrderExpireTime()));
        }
        return extendParam;
    }
}
