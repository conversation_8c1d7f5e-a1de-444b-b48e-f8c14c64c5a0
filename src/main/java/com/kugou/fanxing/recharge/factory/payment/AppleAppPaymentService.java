package com.kugou.fanxing.recharge.factory.payment;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.recharge.alert.AlerterFacade;
import com.kugou.fanxing.recharge.alert.config.AlertConfig;
import com.kugou.fanxing.recharge.constant.*;
import com.kugou.fanxing.recharge.dao.d_fanxing_recharge.RefundOrderDao;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.factory.AbstractPaymentService;
import com.kugou.fanxing.recharge.factory.PaymentInfo;
import com.kugou.fanxing.recharge.factory.PurchaseRequest;
import com.kugou.fanxing.recharge.model.dto.GwiosCheckNewReceiptLocalV1Dto;
import com.kugou.fanxing.recharge.model.dto.GwiosOrderV3Dto;
import com.kugou.fanxing.recharge.model.dto.ReceiptInfoDto;
import com.kugou.fanxing.recharge.model.dto.RefundNotificationDto;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.po.refund.RefundOrderPO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.RechargeSandboxService;
import com.kugou.fanxing.recharge.service.RefundHandlerService;
import com.kugou.fanxing.recharge.service.appstore.AppStoreReceiptService;
import com.kugou.fanxing.recharge.thrift.IapNotificationRequest;
import com.kugou.fanxing.recharge.thrift.ReportIapReceiptDto;
import com.kugou.fanxing.recharge.thrift.ReportIapReceiptRequest;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.fanxing.thrift.pay.v2.PlatformPayV2Service;
import com.kugou.fanxing.thrift.pay.v2.QueryRechargeOrderNumByTradeNoRequest;
import com.kugou.fanxing.thrift.pay.v2.QueryRechargeOrderNumByTradeNoResponse;
import com.kugou.fanxing.thrift.pay.v2.TradeNoMappingDTO;
import com.kugou.fanxing.thrift.recharge.ios.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AppleAppPaymentService extends AbstractPaymentService {

    @Autowired
    private AppStoreRpcService.Iface appStoreRpcService;
    @Autowired
    private AppStoreReceiptService appStoreReceiptService;
    @Autowired
    private RechargeSandboxService rechargeSandboxService;
    @Autowired
    private PlatformPayV2Service.Iface platformPayV2Service;
    @Autowired
    private RefundOrderDao refundOrderDao;
    @Autowired
    private AlerterFacade alerterFacade;
    @Autowired
    private ApolloConfigService apolloConfigService;

    @Autowired
    private RefundHandlerService refundHandlerService;

    @Override
    public PaymentInfo purchase(PurchaseRequest purchaseRequest) {
        if (StringUtils.isBlank(purchaseRequest.getAppVersion())) {
            log.warn("充值购买虚拟货币下单接口，APP支付缺少版本信息。purchaseRequest: {}", purchaseRequest);
            throw new BizException(SysResultCode.RECHARGE_PARAM_ERROR);
        }
        String rechargeOrderNum = purchaseRequest.getRechargeOrderNum();
        int stdPlat = purchaseRequest.getStdPlat();
        String productId = purchaseRequest.getProductId();
        Map<String, Object> extendParam = this.buildExtendParamByRequest(purchaseRequest);
        Optional<AppStoreProductDTO> optionalAppStoreProductDTO = this.getProduct(stdPlat, productId);
        if (!optionalAppStoreProductDTO.isPresent()) {
            log.warn("苹果应用内充值下单接口，货品码参数非法。purchaseRequest: {}, extendParam: {}", purchaseRequest, extendParam);
            throw new BizException(SysResultCode.IAP_PRODUCT_ID_INVALID).addContextValue("productId", productId);
        }
        AppStoreProductDTO appStoreProductDTO = optionalAppStoreProductDTO.get();
        purchaseRequest.setAmount(new BigDecimal(appStoreProductDTO.getMoney()));
        purchaseRequest.setCoinType(CoinTypeEnum.getByProductType(appStoreProductDTO.getProductType()));
        // 创建充值下单记录
        RechargeAcrossPO rechargeAcrossPO = createPurchaseOrder(purchaseRequest, extendParam, Optional.empty());
        rechargeAcrossPO.setProductId(appStoreProductDTO.getProductId());
        rechargeAcrossPO.setCoin(new BigDecimal(appStoreProductDTO.getCoin()));
        rechargeAcrossPO.setCoinType(CoinTypeEnum.getByProductType(appStoreProductDTO.getProductType()));
        GwiosOrderV3Dto gwiosOrderV3Dto = kupayService.gwiosOrderV3(rechargeAcrossPO, extendParam, purchaseRequest)
                .orElseThrow(() -> new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE));
        rechargeAcrossPO.setConsumeOrderNum(gwiosOrderV3Dto.getOut_trade_no());
        String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
        int affected = rechargeAcrossDao.insertIgnore(month, rechargeAcrossPO);
        if (affected < 1) {
            log.warn("苹果应用内充值下单接口，保存订单失败。rechargeAcrossPO: {}", rechargeAcrossPO);
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
        PaymentInfo paymentInfo = PaymentInfo.builder()
                .rechargeOrderNum(rechargeOrderNum)
                .paymentJson(JsonUtils.toJSONString(gwiosOrderV3Dto))
                .build();
        log.warn("苹果应用内充值下单接口，保存订单成功。rechargeAcrossPO: {}", rechargeAcrossPO);
        return paymentInfo;
    }

    @Override
    protected Map<String, Object> buildExtendParamByRequest(PurchaseRequest request) {
        return super.buildExtendParamByRequest(request);
    }

    @SneakyThrows
    private Optional<AppStoreProductDTO> getProduct(int stdPlat, String productId) {
        QueryProductRequest request = new QueryProductRequest();
        request.setCommonParam(new CommonParamsV1().setAppId("10000005")
                .setPid(stdPlat).setTimestamp(System.currentTimeMillis()));
        request.setProductId(productId);
        request.setSign(FinanceSignUtils.makeSign(request, "tgzau2W2KkU2v9QT"));
        QueryProductResponse response = appStoreRpcService.queryProduct(request);
        if (response.getCode() == 0 && Objects.nonNull(response.getData())) {
            return Optional.of(response.getData());
        }
        return Optional.empty();
    }

    public ReportIapReceiptDto reportIapReceipt(ReportIapReceiptRequest request) {
        String rechargeOrderNum = request.getRechargeOrderNum();
        String receiptData = request.getReceiptData();
        Optional<RechargeAcrossPO> optionalRechargeAcrossPO = this.rechargeOrderService.queryByRechargeOrderNum(rechargeOrderNum);
        if (!optionalRechargeAcrossPO.isPresent()) {
            log.warn("苹果应用内充值上报接口，处理外部检查失败。request: {}, request: {}", request, request);
            throw new BizException(SysResultCode.FAILURE);
        }
        RechargeAcrossPO rechargeAcrossPO = optionalRechargeAcrossPO.get();
        int stdPlat = rechargeAcrossPO.getCFrom();
        String productId = rechargeAcrossPO.getProductId();
        Optional<AppStoreProductDTO> optionalAppStoreProductDTO = this.getProduct(stdPlat, productId);
        if (!optionalAppStoreProductDTO.isPresent()) {
            log.warn("苹果应用内充值下单接口，货品码参数非法。request: {}, request: {}", request, request);
            throw new BizException(SysResultCode.IAP_PRODUCT_ID_INVALID).addContextValue("productId", productId);
        }
        AppStoreProductDTO product = optionalAppStoreProductDTO.get();
        String bundleId = product.getBundleId();
        boolean isSandboxReceipt = appStoreReceiptService.isSandboxReceipt(receiptData);
        if (isSandboxReceipt) {
            String appVersion = rechargeAcrossPO.getAppVersion();
            boolean allowAppleSandbox = this.rechargeSandboxService.allowAppleSandbox(stdPlat, appVersion,
                    rechargeOrderNum, rechargeAcrossPO.getAmount());
            if (!allowAppleSandbox) {
                log.warn("苹果应用内充值上报接口，沙盒充值超过使用限额。request: {}", request);
                throw new BizException(SysResultCode.IAP_SANDBOX_AMOUNT_OVERDUE);
            }
        }
        // 上报内购收据
        Map<String, Object> extendParam = Maps.newHashMap();
        extendParam.put("isSandbox", BooleanUtils.toInteger(isSandboxReceipt));
        extendParam.put("businessId", rechargeAcrossPO.getBusinessId());
        extendParam.put("bundleId", bundleId);
        GwiosCheckNewReceiptLocalV1Dto receiptLocalV1Dto = kupayService.gwiosCheckOldReceiptLocalV1(rechargeAcrossPO, extendParam, bundleId, receiptData);
        boolean isValidReceipt = GwiosCheckNewReceiptLocalV1Dto.ReceiptStatusEnum.isValidReceipt(receiptLocalV1Dto.getReceipt_status());
        if (!isValidReceipt) {
            log.warn("苹果应用内充值上报接口，内购收据格式非法。request: {}", request);
            throw new BizException(SysResultCode.IAP_RECEIPT_FORMAT_INVALID);
        }
        log.warn("苹果应用内充值上报接口，保存订单成功。rechargeAcrossPO: {}, receiptLocalV1Dto: {}", rechargeAcrossPO, receiptLocalV1Dto);
        ReportIapReceiptDto reportIapReceiptDto = new ReportIapReceiptDto();
        reportIapReceiptDto.setOutTradeNo(receiptLocalV1Dto.getOut_trade_no());
        reportIapReceiptDto.setRechargeOrderNum(receiptLocalV1Dto.getRechargeOrderNum());
        reportIapReceiptDto.setTransactionId(receiptLocalV1Dto.getTransaction_id());
        // 绑定直播单号
        String tradeNo = StringUtils.prependIfMissing(receiptLocalV1Dto.getTransaction_id(), "TID");
        Optional<String> optionalRechargeOrderNum = this.rechargeOrderService.convertTradeNoToRechargeOrderNum(
                tradeNo, receiptLocalV1Dto.getRechargeOrderNum());
        if (!optionalRechargeOrderNum.isPresent()) {
            log.warn("苹果应用内充值上报接口，内购收据格式非法。request: {}", request);
            throw new BizException(SysResultCode.IAP_RECEIPT_FORMAT_INVALID);
        }
        return reportIapReceiptDto;
    }

    public SysResultCode iapNotification(IapNotificationRequest request) {
        String notification = request.getNotification();
        if (!JsonUtils.isValidJson(notification)) {
            log.warn("苹果内购退款通知接口，通知格式错误。request: {}", request);
            return SysResultCode.RECHARGE_PARAM_ERROR;
        }
        String environment = JsonUtils.parseJsonPathChecked(notification, "$.environment", String.class);
        if (!environment.equalsIgnoreCase("PROD")) {
            log.warn("苹果内购退款通知接口，非生产退款忽略。request: {}", request);
            return SysResultCode.SUCCESS;
        }
        String notificationType = JsonUtils.parseJsonPathChecked(notification, "$.notification_type", String.class);
        if (!AppStoreNotificationTypeEnum.REFUND.getKey().equals(notificationType)) {
            log.warn("苹果内购退款通知接口，非退款通知忽略。request: {}", request);
            return SysResultCode.SUCCESS;
        }
        RefundNotificationDto refundNotificationDto = JSON.parseObject(notification, RefundNotificationDto.class);
        if (Objects.isNull(refundNotificationDto) || Objects.isNull(refundNotificationDto.getUnified_receipt()) ||
                CollectionUtils.isEmpty(refundNotificationDto.getUnified_receipt().getLatest_receipt_info())) {
            log.warn("苹果内购退款通知接口，非退款通知忽略。request: {}", request);
            return SysResultCode.RECHARGE_PARAM_ERROR;
        }
        List<ReceiptInfoDto> receiptInfoDtoList = refundNotificationDto.getUnified_receipt().getLatest_receipt_info();
        List<RefundOrderPO> refundOrderPOList = receiptInfoDtoList.stream().map(this::handleRefundReceipt).collect(Collectors.toList());
        log.warn("处理AppStore服务端退款通知，处理数据条数。refundOrderPOListSize: {}", refundOrderPOList.size());
        return SysResultCode.SUCCESS;
    }

    private RefundOrderPO handleRefundReceipt(ReceiptInfoDto receiptInfoDto) {
        try {
            long cancellationDateMs = receiptInfoDto.getCancellation_date_ms();
            if (cancellationDateMs < DateUtils.addDays(new Date(), -10).getTime()) {
                log.warn("处理AppStore服务端退款通知，历史退款交易跳过。receiptInfoDto: {}", receiptInfoDto);
                return null;
            }
            String transactionId = receiptInfoDto.getTransaction_id();
            Optional<String> optionalRechargeOrderNum = findRechargeOrderNum(transactionId);
            if (!optionalRechargeOrderNum.isPresent()) {
                log.warn("处理AppStore服务端退款通知，退款原单号不存在。receiptInfoDto: {}", receiptInfoDto);
                return null;
            }
            String rechargeOrderNum = optionalRechargeOrderNum.get();
            Optional<RechargeAcrossPO> optionalRechargeAcrossPO = rechargeOrderService
                    .queryByRechargeOrderNum(rechargeOrderNum);
            if (!optionalRechargeAcrossPO.isPresent() || optionalRechargeAcrossPO.get().getStatus() != 1) {
                log.warn("处理AppStore服务端退款通知，退款原订单不存在。receiptInfoDto: {}", receiptInfoDto);
                return null;
            }
            RechargeAcrossPO rechargeAcrossPO = optionalRechargeAcrossPO.get();
            Optional<AppStoreProductDTO> optionalAppStoreProductDTO = getProduct(rechargeAcrossPO.getCFrom(),
                    rechargeAcrossPO.getProductId());
            if (!optionalAppStoreProductDTO.isPresent()) {
                log.warn("处理AppStore服务端退款通知，退款货品码不存在。receiptInfoDto: {}", receiptInfoDto);
                return null;
            }
            RefundOrderPO handledRefundOrder = refundOrderDao.getByRechargeOrderNum(rechargeAcrossPO.getRechargeOrderNum());
            if (Objects.nonNull(handledRefundOrder)) {
                log.warn("处理AppStore服务端退款通知，订单已经处理过。receiptInfoDto: {}", receiptInfoDto);
                return null;
            }
            Date cancellationDate = new Date(receiptInfoDto.getCancellation_date_ms());
            AppStoreProductDTO appStoreProductDTO = optionalAppStoreProductDTO.get();
            RefundOrderPO refundOrderPO = new RefundOrderPO();
            refundOrderPO.setId(orderIdService.generateGlobalId());
            refundOrderPO.setAddTime(DateHelper.getCurrentSeconds());
            refundOrderPO.setCoin(rechargeAcrossPO.getCoin());
            refundOrderPO.setDeductCoin(BigDecimal.ZERO);
            refundOrderPO.setRechargeOrderNum(rechargeAcrossPO.getRechargeOrderNum());
            refundOrderPO.setUpdateTime(DateHelper.getCurrentSeconds());
            refundOrderPO.setKugouId(rechargeAcrossPO.getKugouId());
            refundOrderPO.setCancelTime(cancellationDate.getTime() / 1000);
            refundOrderPO.setPrice(rechargeAcrossPO.getAmount());
            refundOrderPO.setCoinType(CoinTypeEnum.getByProductType(appStoreProductDTO.getProductType()));
            refundOrderPO.setBusinessId(rechargeAcrossPO.getBusinessId());
            refundOrderPO.setHandlerType(refundHandlerService.getHandlerType(rechargeAcrossPO.getBusinessId()));
            refundOrderPO.setNotifyStatus(0);
            int result = refundOrderDao.insert(refundOrderPO);
            log.warn("处理AppStore服务端退款通知，保存退款扣费数据。refundOrderPO: {}, result: {}", refundOrderPO, result);
            String alertContent = String.format("处理iOS退款交易，直播交易单号：%s，苹果交易单号：%s", rechargeOrderNum, transactionId);
            alerterFacade.sendRTX(AlertConfig.RtxConfigEnum.DEFAULT_RTX_CONFIG, alertContent);
            return refundOrderPO;
        } catch (Exception e) {
            log.error("处理AppStore服务端退款通知，构建退款数据异常。receiptInfoDto: {}", receiptInfoDto, e);
            return null;
        }
    }



    @SneakyThrows
    public Optional<String> findRechargeOrderNum(String transactionId) {
        QueryRechargeOrderNumByTradeNoRequest request = new QueryRechargeOrderNumByTradeNoRequest();
        request.setAppId(10000002);
        request.setTradeNo("TID" + transactionId);
        request.setSign(FinanceSignUtils.makeSign(request, "q4Ya9B7MVq5CYBXt"));
        QueryRechargeOrderNumByTradeNoResponse response = this.platformPayV2Service.queryRechargeOrderNumByTradeNo(request);
        if (Objects.nonNull(response) && response.getCode() == 0 && Objects.nonNull(response.getData())) {
            TradeNoMappingDTO tradeNoMappingDTO = response.getData();
            return Optional.ofNullable(tradeNoMappingDTO.getRechargeOrderNum());
        }
        log.error("查询苹果充值成功交易信息，无对应rechargeOrderNum。transactionId: {}", transactionId);
        return Optional.empty();
    }
}
