package com.kugou.fanxing.recharge.factory.payment;

import com.google.common.collect.Maps;
import com.kugou.fanxing.commons.util.JsonUtils;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.factory.AbstractPaymentService;
import com.kugou.fanxing.recharge.factory.PaymentInfo;
import com.kugou.fanxing.recharge.factory.PurchaseRequest;
import com.kugou.fanxing.recharge.model.bo.CouponInfoBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class WechatAppPaymentService extends AbstractPaymentService {

    private Map<String, String> buildKupaysParamByRequest(PurchaseRequest purchaseRequest, RechargeAcrossPO rechargeAcrossPO) {
        Map<String, String> kupaysParam = Maps.newHashMap();
        kupaysParam.put("apptype", "share");
        return kupaysParam;
    }

    @Override
    public PaymentInfo purchase(PurchaseRequest purchaseRequest) {
        // 处理代金券
        String rechargeOrderNum = purchaseRequest.getRechargeOrderNum();
        CoinTypeEnum coinTypeEnum = CoinTypeEnum.of(purchaseRequest.getCoinType());
        Map<String, Object> extendParam = buildExtendParamByRequest(purchaseRequest);
        Optional<CouponInfoBO> optionalCouponInfoBO = Optional.empty();
        try {
            optionalCouponInfoBO = this.rechargeCouponService.handleCouponForOrder(purchaseRequest.getKugouId(),
                    purchaseRequest.getCouponId(), purchaseRequest.getAmount(), coinTypeEnum);
            RechargeAcrossPO rechargeAcrossPO = createPurchaseOrder(purchaseRequest, extendParam, optionalCouponInfoBO);
            String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
            int affected = rechargeAcrossDao.insertIgnore(month, rechargeAcrossPO);
            if (affected < 1) {
                log.warn("微信APP充值下单，保存订单失败。month: {}, rechargeAcrossPO: {}", month, rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            Map<String, String> kupaysParam = buildKupaysParamByRequest(purchaseRequest, rechargeAcrossPO);
            Map<String, Object> dataMap = this.rechargeCommonService.createRechargeResultForMobileByPost(
                    rechargeAcrossPO, kupaysParam, extendParam);
            if (dataMap.isEmpty() || !dataMap.containsKey("actionUrl")) {
                log.warn("微信APP充值下单，生成链接失败。rechargeAcrossPO: {}", rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            String paymentJson = JsonUtils.toJsonString(dataMap.get("actionUrl"));
            return PaymentInfo.builder().rechargeOrderNum(rechargeOrderNum).paymentJson(paymentJson).build();
        } catch (Exception e) {
            log.error("微信APP充值下单，生成链接异常。", e);
            optionalCouponInfoBO.ifPresent(couponInfoBO -> this.rechargeCouponService
                    .unFreezeCouponQuietly(purchaseRequest.getKugouId(), rechargeOrderNum, couponInfoBO));
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }
}
