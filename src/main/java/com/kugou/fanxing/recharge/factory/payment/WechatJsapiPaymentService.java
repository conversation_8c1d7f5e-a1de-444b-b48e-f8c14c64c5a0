package com.kugou.fanxing.recharge.factory.payment;

import com.google.common.collect.Sets;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.factory.AbstractPaymentService;
import com.kugou.fanxing.recharge.factory.PaymentInfo;
import com.kugou.fanxing.recharge.factory.PurchaseRequest;
import com.kugou.fanxing.recharge.model.bo.CouponInfoBO;
import com.kugou.fanxing.recharge.model.dto.WxgzhpayV1Dto;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class WechatJsapiPaymentService extends AbstractPaymentService {
    @Override
    public PaymentInfo purchase(PurchaseRequest purchaseRequest) {
        if (StringUtils.isBlank(purchaseRequest.getOpenid())) {
            log.warn("微信Jsapi充值下单，未提供openid参数。purchaseRequest: {}", purchaseRequest);
            throw new BizException(SysResultCode.RECHARGE_OPENID_NOT_PROVIDED);
        }
        String rechargeOrderNum = purchaseRequest.getRechargeOrderNum();
        CoinTypeEnum coinTypeEnum = CoinTypeEnum.of(purchaseRequest.getCoinType());
        Map<String, Object> extendParam = buildExtendParamByRequest(purchaseRequest);
        Optional<CouponInfoBO> optionalCouponInfoBO = Optional.empty();
        try {
            optionalCouponInfoBO = this.rechargeCouponService.handleCouponForOrder(purchaseRequest.getKugouId(),
                    purchaseRequest.getCouponId(), purchaseRequest.getAmount(), coinTypeEnum);
            RechargeAcrossPO rechargeAcrossPO = createPurchaseOrder(purchaseRequest, extendParam, optionalCouponInfoBO);
            String month = orderIdService.getYearMonthFromRechargeOrderNumSilent(rechargeOrderNum);
            int affected = rechargeAcrossDao.insertIgnore(month, rechargeAcrossPO);
            if (affected < 1) {
                log.warn("微信Jsapi充值下单，保存订单失败。purchaseRequest: {}, rechargeAcrossPO: {}", purchaseRequest, rechargeAcrossPO);
                throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
            }
            Optional<WxgzhpayV1Dto> optionalWxgzhpayV2Dto = this.kupayService.wxgzhpayV1(rechargeAcrossPO, extendParam, purchaseRequest);
            WxgzhpayV1Dto wxgzhpayV1Dto = optionalWxgzhpayV2Dto.orElseThrow(() -> new ContextedRuntimeException("微信Jsapi充值下单，保存订单失败。"));
            if (Sets.newHashSet(30913).contains(wxgzhpayV1Dto.getErrorCode())) {
                log.warn("微信Jsapi充值下单，调用网关失败。purchaseRequest: {}, wxgzhpayV1Dto: {}", purchaseRequest, wxgzhpayV1Dto);
                throw new BizException(wxgzhpayV1Dto.getErrorCode(), wxgzhpayV1Dto.getErrorMsg());
            }
            if (wxgzhpayV1Dto.getErrorCode() != 0) {
                log.warn("微信Jsapi充值下单，调用网关失败。purchaseRequest: {}, wxgzhpayV1Dto: {}", purchaseRequest, wxgzhpayV1Dto);
                throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
            }
            return PaymentInfo.builder().rechargeOrderNum(rechargeOrderNum)
                    .outTradeNo(wxgzhpayV1Dto.getOutTradeNo())
                    .paymentJson(wxgzhpayV1Dto.getData()).build();
        } catch (BizException e) {
            log.error("微信Jsapi充值下单，生成链接异常。purchaseRequest: {}", purchaseRequest, e);
            optionalCouponInfoBO.ifPresent(couponInfoBO -> this.rechargeCouponService
                    .unFreezeCouponQuietly(purchaseRequest.getKugouId(), rechargeOrderNum, couponInfoBO));
            throw e;
        } catch (Exception e) {
            log.error("微信Jsapi充值下单，生成链接异常。purchaseRequest: {}", purchaseRequest, e);
            optionalCouponInfoBO.ifPresent(couponInfoBO -> this.rechargeCouponService
                    .unFreezeCouponQuietly(purchaseRequest.getKugouId(), rechargeOrderNum, couponInfoBO));
            throw new AckException(SysResultCode.RECHARGE_GET_ORDER_FAILURE);
        }
    }
}
