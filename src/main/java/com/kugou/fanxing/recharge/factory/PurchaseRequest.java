package com.kugou.fanxing.recharge.factory;

import com.kugou.fanxing.recharge.thrift.PurchaseCurrencyRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseRequest {
    @NotNull
    @Min(1)
    private long kugouId;
    @NotNull
    @NotBlank
    private String rechargeOrderNum;
    @NotNull
    @NotBlank
    private String description;
    @NotNull
    @Min(1)
    private int coinType;
    @NotNull
    @DecimalMin(value = "0.01", message = "输入的amount不合法")
    private BigDecimal amount;
    private long couponId;
    @NotNull
    @Min(1)
    private int payTypeId;
    @NotNull
    @Min(0)
    private int stdPlat;
    private String clientIp;
    private int businessId;
    private String orderNo;
    @NotNull
    @Min(0)
    private long orderTime;
    private long orderExpireTime;
    private int reType;
    private String openid;
    private String productId;
    private int roomId;
    private String appVersion;

    public static PurchaseRequest from(PurchaseCurrencyRequest purchaseCurrencyRequest, String rechargeOrderNum) {
        PurchaseRequest purchaseRequest = PurchaseRequest.builder()
                .kugouId(purchaseCurrencyRequest.getKugouId())
                .stdPlat(purchaseCurrencyRequest.getStdPlat())
                .rechargeOrderNum(rechargeOrderNum)
                .payTypeId(purchaseCurrencyRequest.getPayTypeId())
                .description(purchaseCurrencyRequest.getDescription())
                .coinType(purchaseCurrencyRequest.getCoinType())
                .amount(new BigDecimal(purchaseCurrencyRequest.getAmount()))
                .couponId(purchaseCurrencyRequest.getCouponId())
                .businessId(purchaseCurrencyRequest.getBusinessId())
                .orderNo(purchaseCurrencyRequest.getOrderNo())
                .orderTime(purchaseCurrencyRequest.getOrderTime())
                .orderExpireTime(purchaseCurrencyRequest.getOrderExpireTime())
                .clientIp(purchaseCurrencyRequest.getClientIp())
                .reType(0)
                .openid(StringUtils.defaultString(purchaseCurrencyRequest.getOpenid()))
                .productId(StringUtils.defaultString(purchaseCurrencyRequest.getProductId()))
                .roomId(purchaseCurrencyRequest.getRoomId())
                .appVersion(StringUtils.trimToEmpty(purchaseCurrencyRequest.getAppVersion()))
                .build();
        log.warn("purchaseCurrencyRequest: {}, purchaseRequest: {}", purchaseCurrencyRequest, purchaseRequest);
        return purchaseRequest;
    }
}
