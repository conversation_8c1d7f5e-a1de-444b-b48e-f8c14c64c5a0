package com.kugou.fanxing.recharge.controller;

import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.model.request.QueryWithdrawOrderReq;
import com.kugou.fanxing.recharge.model.request.QueryWithdrawOrderWechatReq;
import com.kugou.fanxing.recharge.model.response.WithdrawVerifyOrderRsp;
import com.kugou.fanxing.recharge.service.ValidatingService;
import com.kugou.fanxing.recharge.service.withdraw.WithdrawOrderService;
import com.kugou.fanxing.recharge.util.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 提现
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class WithdrawController {

    @Autowired
    private ValidatingService validatingService;
    @Autowired
    private WithdrawOrderService withDrawOrderService;

    /**
     * 支付宝网关回查校验提现订单
     */
    @GetMapping(value = UrlConstants.WITH_DRAW_ORDER_VERIFY)
    public JsonResult<Map<String, Object>> withDrawOrderVerifyAlipay(QueryWithdrawOrderReq param) {
        log.warn("支付宝网关回查校验提现订单，请求参数。param: {}", param);
        String message = validatingService.validateParams(param);
        if (StringUtils.isNotEmpty(message)) {
            log.warn("支付宝网关回查校验提现订单，请求参数不合法。param: {}, message: {}", param, message);
            return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, Maps.newHashMap());
        }
        return withDrawOrderService.withdrawOrderVerify(param);
    }

    /**
     * 微信网关回查校验提现订单
     */
    @GetMapping(value = UrlConstants.WITH_DRAW_ORDER_VERIFY_WECHAT)
    public JsonResult<Map<String, Object>> withDrawOrderVerifyWechat(QueryWithdrawOrderWechatReq param) {
        log.warn("微信网关回查校验提现订单，请求参数。param: {}", param);
        String message = validatingService.validateParams(param);
        if (StringUtils.isNotEmpty(message)) {
            log.warn("微信网关回查校验提现订单，请求参数不合法。param: {}, message: {}", param, message);
            return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, Maps.newHashMap());
        }
        return withDrawOrderService.withdrawOrderVerifyWechat(param);
    }

    /**
     * 深田网关回查校验提现订单
     */
    @GetMapping(value = UrlConstants.WITH_DRAW_ORDER_VERIFY_SHENTIAN)
    public WithdrawVerifyOrderRsp withDrawOrderVerifyStWechat(QueryWithdrawOrderReq param) {
        log.warn("深田网关回查校验提现订单，请求参数。param: {}", param);
        String message = validatingService.validateParams(param);
        if (StringUtils.isNotEmpty(message)) {
            log.warn("深田网关回查校验提现订单，请求参数不合法。param: {}, message: {}", param, message);
            return WithdrawVerifyOrderRsp.result(false);
        }
        boolean isValidOrder = withDrawOrderService.isValidOrder(param);
        return WithdrawVerifyOrderRsp.result(isValidOrder);
    }

}
