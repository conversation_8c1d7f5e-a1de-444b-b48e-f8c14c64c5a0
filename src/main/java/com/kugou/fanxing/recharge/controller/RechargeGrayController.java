package com.kugou.fanxing.recharge.controller;

import com.kugou.fanxing.recharge.common.aop.annotation.CdnCache;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.model.vo.GrayNewPage;
import com.kugou.fanxing.recharge.model.vo.GrayRechargeOption;
import com.kugou.fanxing.recharge.model.vo.GrayRechargePage;
import com.kugou.fanxing.recharge.service.RechargeGrayService;
import com.kugou.fanxing.recharge.util.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/5/13
 */
@RestController
@CrossOrigin
public class RechargeGrayController {

    @Autowired
    private RechargeGrayService rechargeGrayService;


    /**
     * 充值选项列表
     * 增加默认金额及最少充值金额
     */
    @CdnCache(cacheStrategy = CdnCache.CacheStrategy.NO_CACHE)
    @RequestMapping(value = UrlConstants.CUSTOM_RECHARGE_OPTIONS)
    public JsonResult<GrayRechargeOption> queryGrayRechargeOption(WebCommonParam webCommonParam, String ssad) {
        return JsonResult.success(rechargeGrayService.getRechargeOptions(webCommonParam.getKugouId(), ssad, webCommonParam.getPlatform()));
    }

    /**
     * 客户端跳转的充值页类型
     * 0 旧充值页  1 新充值弹框
     */
    @CdnCache(cacheStrategy = CdnCache.CacheStrategy.NO_CACHE)
    @RequestMapping(value = UrlConstants.GRAY_RECHARGE_PAGE_STYLE)
    public JsonResult<GrayRechargePage> grayRechargePageStyle(WebCommonParam webCommonParam, String ssad) {
        return JsonResult.success(rechargeGrayService.grayRechargePage(webCommonParam.getKugouId(), ssad, webCommonParam.getPlatform()));
    }

    /**
     * 新充值弹框配置接口
     */
    @CdnCache(cacheStrategy = CdnCache.CacheStrategy.NO_CACHE)
    @RequestMapping(value = UrlConstants.GRAY_RECHARGE_NEW_PAGE)
    public JsonResult<GrayNewPage> grayRechargeNewPage(WebCommonParam webCommonParam) {
        return JsonResult.success(rechargeGrayService.grayNewPage(webCommonParam.getKugouId(), webCommonParam.getStdPlat()));
    }

}
