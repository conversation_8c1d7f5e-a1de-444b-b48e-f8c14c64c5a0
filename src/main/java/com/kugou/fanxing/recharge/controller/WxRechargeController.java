package com.kugou.fanxing.recharge.controller;

import com.kugou.fanxing.recharge.constant.UrlConstants;

import com.kugou.fanxing.recharge.model.dto.WxSignCallBackDTO;

import com.kugou.fanxing.recharge.service.wxpay.WxRechargeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
public class WxRechargeController {

    @Autowired
    WxRechargeService wxRechargeService;

    @RequestMapping(value = UrlConstants.WX_SIGNUP_CALLBACK)
    public String wxSignCallback(WxSignCallBackDTO wxSignCallBackDTO) {
        return wxRechargeService.wxSignCallback(wxSignCallBackDTO);
    }


}
