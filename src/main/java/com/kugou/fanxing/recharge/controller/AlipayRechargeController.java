package com.kugou.fanxing.recharge.controller;

import com.google.common.collect.Maps;
import com.kugou.config.Env;
import com.kugou.fanxing.recharge.common.aop.annotation.PoorMode;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.model.bo.KugouOpenBusinessBO;
import com.kugou.fanxing.recharge.model.request.*;
import com.kugou.fanxing.recharge.service.AlipayRechargeService;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.fanxing.recharge.util.KugouOpenResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 支付宝
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class AlipayRechargeController {

    @Autowired
    private Env env;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private AlipayRechargeService alipayRechargeService;

    @PoorMode
    @PostMapping(value = UrlConstants.GET_ORDER_FOR_ALIPAY_QR)
    public JsonResult<Map<String, Object>> getOrderForAlipayQr(WebCommonParam webCommonParam, AlipayRequest request) {
        log.warn("网页支付宝充值二维码下单接口, webCommonParam: {}, alipayRequest: {}", webCommonParam, request);
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_3, webCommonParam);
        Map<String, Object> dataMap = this.alipayRechargeService.getOrderForAlipayQr(webCommonParam, request);
        return JsonResult.result(SysResultCode.SUCCESS, dataMap);
    }

    @PoorMode
    @PostMapping(value = UrlConstants.GET_ORDER_FOR_ALIPAY_V2)
    public JsonResult<Map<String, Object>> getOrderForAlipayV2(WebCommonParam webCommonParam, AlipayRequest request) {
        log.warn("网页支付宝充值V2下单接口, webCommonParam: {}, alipayRequest: {}", webCommonParam, request);
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_3, webCommonParam);
        Map<String, Object> dataMap = this.alipayRechargeService.getOrderForAlipay(webCommonParam, request, "v2");
        return JsonResult.result(SysResultCode.SUCCESS, dataMap);
    }

    /**
     * 网页充值下单接口
     */
    @PoorMode
    @PostMapping(value = UrlConstants.GET_ORDER_FOR_ALIPAY)
    public JsonResult<Map<String, Object>> getOrderForAlipayV1(WebCommonParam webCommonParam, AlipayRequest request) {
        log.warn("网页支付宝充值V1下单接口, webCommonParam: {}, alipayRequest: {}", webCommonParam, request);
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_3, webCommonParam);
        Map<String, Object> dataMap = this.alipayRechargeService.getOrderForAlipay(webCommonParam, request, "v1");
        return JsonResult.result(SysResultCode.SUCCESS, dataMap);
    }

    /**
     * 手机充值下单接口
     */
    @RequestMapping(value = UrlConstants.RECHARGE_PLAT_ALIPAY_MOBILE)
    public Map<String, Object> getOrderForAlipayMobile(WebCommonParam webCommonParam, AlipayRequest request) {
        log.warn("旧手机支付宝充值下单接口, webCommonParam: {}, alipayRequest: {}", webCommonParam, request);
        if ((env.isDev() || env.isTest()) && apolloConfigService.getRechargePoorMode()) {
            request.setAmount(new BigDecimal("0.02"));
        }
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_30, webCommonParam);
        return this.alipayRechargeService.getOrderForAlipayMobile(webCommonParam, request,
                CoinTypeEnum.of(request.getCoinType()), false);
    }

    @RequestMapping(value = UrlConstants.GET_ORDER_FOR_ALIPAY_MOBILE)
    public Map<String, Object> alipayM2(WebCommonParam webCommonParam, AlipayRequest request) {
        log.warn("新手机支付宝充值下单V1接口, webCommonParam: {}, alipayRequest: {}", webCommonParam, request);
        if ((env.isDev() || env.isTest()) && apolloConfigService.getRechargePoorMode()) {
            request.setAmount(new BigDecimal("0.02"));
        }
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_30, webCommonParam);
        return this.alipayRechargeService.getOrderForAlipayMobile(webCommonParam, request,
                CoinTypeEnum.of(request.getCoinType()), false);
    }

    @PoorMode
    @RequestMapping(value = UrlConstants.GET_ORDER_FOR_ALIPAY_MOBILE_V2)
    public JsonResult<Map<String, Object>> alipayMV2(WebCommonParam webCommonParam, AlipayRequest request) {
        log.warn("新手机支付宝充值下单V2接口, webCommonParam: {}, alipayRequest: {}", webCommonParam, request);
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_30, webCommonParam);
        Map<String, Object> payload = this.alipayRechargeService.getOrderForAlipayMobile(webCommonParam, request,
                CoinTypeEnum.of(request.getCoinType()), false);
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

    @PoorMode
    @RequestMapping(value = UrlConstants.GET_ORDER_FOR_ALIPAY_APP)
    public JsonResult<Map<String, Object>> alipayApp(WebCommonParam webCommonParam, AlipayRequest request) {
        log.warn("支付宝APP充值下单接口, webCommonParam: {}, alipayRequest: {}", webCommonParam, request);
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_30, webCommonParam);
        Map<String, Object> payload = this.alipayRechargeService.getOrderForAlipayMobile(webCommonParam, request,
                CoinTypeEnum.of(request.getCoinType()), true);
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

    @PoorMode
    @RequestMapping(value = UrlConstants.GET_ORDER_FOR_ALIPAY_APP_SING)
    public JsonResult<Map<String, Object>> alipayAppSing(WebCommonParam webCommonParam, AlipayRequest request) {
        log.warn("支付宝APP唱币充值下单接口, webCommonParam: {}, request: {}", webCommonParam, request);
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_30, webCommonParam);
        request.setCoinType(CoinTypeEnum.SING_COIN.getCoinType());
        Map<String, Object> payload = this.alipayRechargeService.getOrderForAlipayMobile(webCommonParam, request, CoinTypeEnum.SING_COIN, false);
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

    /**
     * H5充值下单接口
     */
    @PoorMode
    @RequestMapping(value = UrlConstants.RECHARGE_PLAT_ALIPAY_H5)
    public JsonResult<Map<String, Object>> liveAlipayH5(WebCommonParam webCommonParam, GetOrderH5Request request) {
        log.warn("支付宝H5充值下单接口，请求参数。webCommonParam: {}, alipayRequest: {}", webCommonParam, request);
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_33, webCommonParam);
        Map<String, Object> payload = this.alipayRechargeService.getOrderForAlipayH5(webCommonParam, request);
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

    /**
     * 手机充值下单接口（酷狗开放平台）
     */
    @RequestMapping(value = UrlConstants.KUGOU_OPEN_ALIPAY_M)
    public KugouOpenResult<Map<String, Object>> openAlipayM(WebCommonParam webCommonParam) {
        log.warn("酷狗开放平台支付宝安卓充值下单接口。webCommonParam: {}", webCommonParam);
        if (!apolloConfigService.allowKugouOpenRecharge()) {
            log.warn("酷狗开放平台支付宝安卓充值下单接口，接口已下架。webCommonParam: {}", webCommonParam);
            return KugouOpenResult.result(SysResultCode.RECHARGE_PAY_TYPE_NOT_SUPPORT, Maps.newHashMap());
        }
        KugouOpenDispatchParam kugouOpenDispatchParam = webCommonParam.getKugouOpenDispatchParam();
        String openAppId = kugouOpenDispatchParam.getOpenappid();
        OpenAlipayMRequest request = new OpenAlipayMRequest();
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_30, webCommonParam);
        request.setBusinessId(openAppId);
        request.setAmount(JsonUtils.parseJsonPath(kugouOpenDispatchParam.getBusiness_data(), "$.amount", BigDecimal.class).orElse(BigDecimal.ZERO));
        if ((env.isDev() || env.isTest()) && apolloConfigService.getRechargePoorMode()) {
            request.setAmount(new BigDecimal("0.01"));
        }
        KugouOpenBusinessBO kugouOpenBusinessBO = this.apolloConfigService.getOpenRechargeBusinessConfig(openAppId);
        webCommonParam.setPid(kugouOpenBusinessBO.getPid());
        Map<String, Object> dataMap = this.alipayRechargeService.getOrderAlipayM(webCommonParam, request, kugouOpenBusinessBO);
        KugouOpenResult<Map<String, Object>> kugouOpenResult = KugouOpenResult.result(SysResultCode.SUCCESS, dataMap);
        log.warn("酷狗开放平台支付宝安卓充值下单接口。kugouOpenResult: {}", kugouOpenResult);
        return kugouOpenResult;
    }

    @PoorMode
    @PostMapping(value = UrlConstants.GAME_ALIPAY_H5)
    public JsonResult<Map<String, Object>> gameAlipayH5(WebCommonParam webCommonParam, GetOrderH5Request request) {
        log.warn("游戏支付宝H5充值下单接口, webCommonParam: {}, alipayRequest: {}", webCommonParam, request);
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_33, webCommonParam);
        Map<String, Object> payload = this.alipayRechargeService.gameAlipayH5(webCommonParam, request);
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }
}
