package com.kugou.fanxing.recharge.controller.fapiao;

import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.common.aop.annotation.CdnCache;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.model.Payload;
import com.kugou.fanxing.recharge.model.request.*;
import com.kugou.fanxing.recharge.model.response.KupayCommonResponse;
import com.kugou.fanxing.recharge.service.FapiaoService;
import com.kugou.fanxing.recharge.service.KupayService;
import com.kugou.fanxing.recharge.service.common.PlatformCertificationServiceImpl;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.recharge.util.Pagination;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Optional;

/**
 * 发票
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class FapiaoController {

    @Autowired
    private FapiaoService fapiaoService;
    @Autowired
    private UserFacadeService userFacadeService;
    @Autowired
    private PlatformCertificationServiceImpl platformCertificationService;

    @CdnCache
    @GetMapping(value = UrlConstants.GET_FAPIAO_RECHARGE_LIST)
    public JsonResult<Map<String, Object>> getFapiaoRechargeList(WebCommonParam webCommonParam, @RequestParam(defaultValue = "-1") long lastRechargeId) {
        log.warn("查询可开发票充值记录，请求参数。webCommonParam: {}, lastRechargeId: {}", webCommonParam, lastRechargeId);
        return fapiaoService.getFapiaoRechargeList(webCommonParam.getPid(), webCommonParam.getKugouId(), lastRechargeId);
    }

    /**
     * 是否允许用户开发票（http://doc.kugou.net/showdoc-master/web/#/8?page_id=10439）
     */
    @PostMapping(value = UrlConstants.CAN_OPEN_FAPIAO)
    public KupayCommonResponse canOpenFapiao(KupayCommonRequest kupayCommonRequest, @RequestBody CanOpenFapiaoRequest request) {
        log.warn("查询用户是否允许，请求参数。kupayCommonRequest: {}, request: {}", kupayCommonRequest, request);
        Map<String, Object> data = Maps.newHashMap();
        boolean hasCertified = this.platformCertificationService.hasCertified(request.getKugouid());
        data.put("can_receipt", hasCertified);
        return KupayCommonResponse.result(SysResultCode.SUCCESS, data);
    }

    /**
     * 开票状态同步回调（http://doc.kugou.net/showdoc-master/web/#/8?page_id=10405）
     */
    @PostMapping(value = UrlConstants.OPEN_FAPIAO_CALLBACK)
    public String openFapiaoCallback(KupayCommonRequest kupayCommonRequest, @RequestBody FapiaoCallbackRequest request) {
        log.warn("开票状态同步回调，请求参数。kupayCommonRequest: {}, request: {}", kupayCommonRequest, request);
        try {
            boolean isSyncSuccess = this.fapiaoService.handleFapiaoCallback(kupayCommonRequest, request);
            if (isSyncSuccess) {
                return "success";
            }
        } catch (Exception e) {
            log.warn("开票状态同步回调，处理异常。kupayCommonRequest: {}, request: {}", kupayCommonRequest, request, e);
        }
        return "failure";
    }

    @PostMapping(value = UrlConstants.GET_FANXING_ID_BY_KUGOU_ID)
    public KupayCommonResponse getFanxingIdByKugouId(KupayCommonRequest kupayCommonRequest, @RequestBody GetFanxingIdByKugouIdRequest request) {
        log.warn("查询直播繁星ID，请求参数。kupayCommonRequest: {}, request: {}", kupayCommonRequest, request);
        Optional<Long> optionalUserId = this.userFacadeService.getUserIdByKugouId(request.getKugouid(), false);
        Map<String, Object> data = Maps.newHashMap();
        data.put("fxid", optionalUserId.orElse(-1L));
        return KupayCommonResponse.result(SysResultCode.SUCCESS, data);
    }

    @CdnCache
    @GetMapping(value = UrlConstants.SHOW_HISTORY_FAPIAO)
    public JsonResult<Payload> showHistoryFapiao(WebCommonParam webCommonParam) {
        boolean isShow = this.fapiaoService.showHistoryFapiao(webCommonParam.getKugouId());
        Payload payload = Payload.Builder.map("isShow", isShow).build();
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

    @CdnCache
    @GetMapping(value = UrlConstants.HISTORY_FAPIAO_LIST)
    public JsonResult<KupayService.RcptOrderWrapper> historyFapiaoList(WebCommonParam webCommonParam, int page, int pageSize) {
        Pagination pagination = new Pagination.Builder(page, pageSize).build();
        KupayService.RcptOrderWrapper rcptOrderWrapper = this.fapiaoService.historyFapiaoList(webCommonParam.getKugouId(), pagination);
        return JsonResult.result(SysResultCode.SUCCESS, rcptOrderWrapper);
    }

}
