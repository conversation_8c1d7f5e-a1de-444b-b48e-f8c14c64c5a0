package com.kugou.fanxing.recharge.controller;

import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.model.PddOrderCheckResult;
import com.kugou.fanxing.recharge.model.dto.CoinCallbackDTO;
import com.kugou.fanxing.recharge.model.request.PddCreateOrderCheckRequest;
import com.kugou.fanxing.recharge.model.request.PddCreateOrderCheckResponse;
import com.kugou.fanxing.recharge.service.recharge.PddRechargeService;
import com.kugou.fanxing.recharge.util.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 拼多多支付渠道
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class PddRechargeController {

    @Autowired
    private PddRechargeService pddRechargeService;

    @RequestMapping(value = UrlConstants.PDD_CREATE_ORDER_CHECK)
    public PddCreateOrderCheckResponse createOrderCheck(PddCreateOrderCheckRequest request) {
        log.warn("拼多多充值下单校验，请求参数。request: {}", request);
        JsonResult<PddOrderCheckResult> jsonResult = this.pddRechargeService.createOrderCheck(request);
        return PddCreateOrderCheckResponse.from(jsonResult);
    }

    @RequestMapping(value = UrlConstants.PDD_PAYMENT_NOTIFY)
    public String callbackNotify(CoinCallbackDTO coinCallbackDTO) {
        log.warn("拼多多充值回调发货，请求参数。coinCallbackDTO: {}", coinCallbackDTO);
        JsonResult<Map<String, String>> jsonResult = this.pddRechargeService.callbackNotify(coinCallbackDTO);
        SysResultCode sysResultCode = SysResultCode.codeOf(jsonResult.getCode());
        return sysResultCode.isSuccess() ? "success" : "failure";
    }
}
