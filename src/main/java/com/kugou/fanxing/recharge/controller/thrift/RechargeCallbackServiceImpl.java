package com.kugou.fanxing.recharge.controller.thrift;

import com.kugou.fanxing.commons.lock.DLock;
import com.kugou.fanxing.commons.lock.DLockFactory;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.service.callback.AppleCallbackService;
import com.kugou.fanxing.recharge.thrift.callback.*;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.rpc.exception.ServerException;
import com.kugou.rpc.server.thrift.annotation.ThriftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

@Slf4j
@ThriftService("/platform_recharge_service/thrift/rechargeCallbackService")
public class RechargeCallbackServiceImpl implements RechargeCallbackService.Iface {

    @Autowired
    private AppleCallbackService appleCallbackService;
    @Autowired
    private DLockFactory dLockFactory;

    /**
     * 苹果充值购买货币回调
     *
     * @param request 请求参数
     * @return 处理结果
     */
    @Override
    public CallbackResponse purchaseCoinForIos(PurchaseCoinForIosRequest request) {
        log.warn("苹果充值购买星币回调, 开始处理请求。request: {}", request);
        try (DLock lock = dLockFactory.getLock(request.getOrderNum())) {
            if (!lock.acquire(1)) {
                log.warn("苹果充值购买星币回调, 请求太快。orderNo: {}", request.getOrderNum());
                return RpcResultHelper.buildCallbackResponse(SysResultCode.DORETRY);
            }
            JsonResult<Map<String, String>> jsonResult = this.appleCallbackService.purchaseCoin(request);
            return RpcResultHelper.buildCallbackResponse(SysResultCode.codeOf(jsonResult.getCode()), jsonResult.getData());
        } catch (BizException e) {
            log.error("苹果充值购买星币回调，处理异常。request: {}", request, e);
            return RpcResultHelper.buildCallbackResponse(SysResultCode.RECHARGE_SYS_ERROR);
        } catch (Exception e) {
            log.error("苹果充值购买星币回调，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    /**
     * 苹果充值购买商品回调
     *
     * @param request 请求参数
     * @return 处理结果
     */
    @Override
    public CallbackResponse purchaseForIos(PurchaseForIosRequest request) {
        log.warn("苹果充值购买物品回调, 开始处理请求。request: {}", request);
        try (DLock lock = dLockFactory.getLock(request.getOrderNo())) {
            if (!lock.acquire(1)) {
                log.warn("苹果充值购买物品回调, 请求太快。orderNo: {}", request.getOrderNo());
                return RpcResultHelper.buildCallbackResponse(SysResultCode.DORETRY);
            }
            JsonResult<Map<String, String>> jsonResult = this.appleCallbackService.purchaseProducts(request);
            return RpcResultHelper.buildCallbackResponse(SysResultCode.codeOf(jsonResult.getCode()), jsonResult.getData());
        } catch (BizException e) {
            log.error("苹果充值购买物品回调，处理异常。request: {}", request, e);
            return RpcResultHelper.buildCallbackResponse(SysResultCode.RECHARGE_SYS_ERROR);
        } catch (Exception e) {
            log.error("苹果充值购买物品回调，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public CallbackResponse renewalsForIos(RenewalsForIosRequest request) {
        log.warn("苹果充值续费物品回调, 开始处理请求。request: {}", request);
        try (DLock lock = dLockFactory.getLock(request.getOrderNo())) {
            if (!lock.acquire(1)) {
                log.warn("苹果充值续费物品回调, 请求太快。orderNo: {}", request.getOrderNo());
                return RpcResultHelper.buildCallbackResponse(SysResultCode.DORETRY);
            }
            JsonResult<Map<String, String>> jsonResult = this.appleCallbackService.renewalsProducts(request);
            return RpcResultHelper.buildCallbackResponse(SysResultCode.codeOf(jsonResult.getCode()), jsonResult.getData());
        } catch (BizException e) {
            log.error("苹果充值续费物品回调，处理异常。request: {}", request, e);
            return RpcResultHelper.buildCallbackResponse(SysResultCode.RECHARGE_SYS_ERROR);
        } catch (Exception e) {
            log.error("苹果充值续费物品回调，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

}
