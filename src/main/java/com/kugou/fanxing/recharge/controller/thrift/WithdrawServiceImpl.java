package com.kugou.fanxing.recharge.controller.thrift;

import com.google.common.collect.Lists;
import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.po.WithdrawAccountPO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.common.DencryptService;
import com.kugou.fanxing.recharge.service.withdraw.DrawCashService;
import com.kugou.fanxing.recharge.service.withdraw.WithdrawAccountService;
import com.kugou.fanxing.recharge.withdraw.thrift.*;
import com.kugou.rpc.exception.ServerException;
import com.kugou.rpc.server.thrift.annotation.ThriftService;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.helper.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;

/**
 * 提现服务
 *
 * <AUTHOR>
 */
@Slf4j
@ThriftService("/platform_recharge_service/thrift/withdrawService")
public class WithdrawServiceImpl implements WithdrawService.Iface {

    @Autowired
    private WithdrawAccountService withdrawAccountService;
    @Autowired
    private DrawCashService drawCashService;
    @Autowired
    private DencryptService dencryptService;

    @Autowired
    private ApolloConfigService apolloConfigService;

    @Override
    public AccountQueryResult queryAccountInfoByKugouId(WithdrawAccountQueryRequest request) {
        try {
            log.warn("查询支付宝提现账号信息, request: {}", request);
            long kugouId = request.getKugouId();
            Optional<WithdrawAccountDTO> optionalWithdrawAccountDTO = withdrawAccountService.queryAccountInfoByKugouId(kugouId);
            return optionalWithdrawAccountDTO.map(withdrawAccountDTO -> RpcResultHelper.buildAccountQueryResult(SysResultCode.SUCCESS, withdrawAccountDTO))
                    .orElseGet(() -> RpcResultHelper.buildAccountQueryResult(SysResultCode.SUCCESS, null));
        } catch (BizException e) {
            log.error("查询支付宝提现账号信息，操作异常, request: {}", request, e);
            return RpcResultHelper.buildAccountQueryResult(SysResultCode.WITHDRAW_ACCOUNT_EXCEPTION, null);
        } catch (Exception e) {
            log.error("查询支付宝提现账号信息，故障转移, request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public WithdrawResult bindAccountInfo(AccountChangeRequest request) {
        try {
            log.warn("绑定提现账号信息, request: {}", request);
            if (!FinanceSignUtils.checkSign(request, apolloConfigService.appKeyOf(request.getAppId()))) {
                log.warn("绑定提现账号信息, 签名验证失败, request: {}", request);
                return RpcResultHelper.buildWithdrawResult(SysResultCode.RECHARGE_SIGN_INVALID);
            }
            SysResultCode sysResultCode = this.withdrawAccountService.saveAccountInfo(request);
            return RpcResultHelper.buildWithdrawResult(sysResultCode);
        } catch (BizException e) {
            log.error("绑定提现账号信息，操作异常, request: {}", request, e);
            return RpcResultHelper.buildWithdrawResult(SysResultCode.WITHDRAW_ACCOUNT_EXCEPTION);
        } catch (Exception e) {
            log.error("绑定提现账号信息，故障转移, request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public WithdrawResult createWithdrawOrder(CreateWithdrawOrderRequest request) {
        try {
            log.warn("创建提现订单, request: {}", request);
            if (!FinanceSignUtils.checkSign(request, apolloConfigService.appKeyOf(request.getAppId()))) {
                log.warn("绑定提现账号信息, 签名验证失败, request: {}", request);
                return RpcResultHelper.buildWithdrawResult(SysResultCode.RECHARGE_SIGN_INVALID);
            }
            SysResultCode sysResultCode = this.drawCashService.drawCash(request);
            return RpcResultHelper.buildWithdrawResult(sysResultCode);
        } catch (BizException e) {
            log.error("创建提现订单，操作异常, request: {}", request, e);
            return RpcResultHelper.buildWithdrawResult(SysResultCode.WITHDRAW_ORDER_EXCEPTION);
        } catch (Exception e) {
            log.error("创建提现订单，故障转移, request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public WithdrawOrderResult queryWithdrawOrder(QueryWithdrawOrderRequest request) {
        try {
            log.warn("查询提现订单, request: {}", request);
            if (!FinanceSignUtils.checkSign(request, apolloConfigService.appKeyOf(request.getAppId()))) {
                log.warn("查询提现订单, 签名验证失败, request: {}", request);
                return RpcResultHelper.buildWithdrawOrderResult(SysResultCode.RECHARGE_SIGN_INVALID, null);
            }
            Optional<WithdrawOrderDTO> optionalWithdrawOrderDTO = drawCashService.queryWithdrawOrderByOrderId(request.getOrderId());
            if (optionalWithdrawOrderDTO.isPresent()) {
                WithdrawOrderDTO withdrawOrderDTO = optionalWithdrawOrderDTO.get();
                return RpcResultHelper.buildWithdrawOrderResult(SysResultCode.SUCCESS, withdrawOrderDTO);
            }
            return new WithdrawOrderResult().setCode(0);
        } catch (BizException e) {
            log.error("查询提现订单，操作异常, request: {}", request, e);
            return RpcResultHelper.buildWithdrawOrderResult(SysResultCode.WITHDRAW_ORDER_EXCEPTION, null);
        } catch (Exception e) {
            log.error("查询提现订单，故障转移, request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public WithdrawResult cancelWithdrawOrder(CancelWithdrawOrderRequest request) {
        try {
            log.warn("取消提现订单, request: {}", request);
            if (!FinanceSignUtils.checkSign(request, apolloConfigService.appKeyOf(request.getAppId()))) {
                log.warn("取消提现订单, 签名验证失败, request: {}", request);
                return RpcResultHelper.buildWithdrawResult(SysResultCode.RECHARGE_SIGN_INVALID);
            }
            SysResultCode sysResultCode = this.drawCashService.cancelDrawCash(request.getOrderId(), request.getReason());
            return RpcResultHelper.buildWithdrawResult(sysResultCode);
        } catch (BizException e) {
            log.error("取消提现订单，操作异常, request: {}", request, e);
            return RpcResultHelper.buildWithdrawResult(SysResultCode.WITHDRAW_CANCEL_EXCEPTION);
        } catch (Exception e) {
            log.error("取消提现订单，故障转移, request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public QueryWithdrawKugouIdResult getKugouIdByAliPayAccount(QueryWithdrawKugouIdRequest request) {
        try {
            log.warn("根据支付宝账号反查酷狗ID, request: {}", request);
            if (!FinanceSignUtils.checkSign(request, apolloConfigService.appKeyOf(request.getAppId()))) {
                log.warn("根据支付宝账号反查酷狗ID, 签名验证失败, request: {}", request);
                return RpcResultHelper.buildQueryKugouIdResult(SysResultCode.RECHARGE_SIGN_INVALID, Lists.newArrayList());
            }
            if (StringUtil.isBlank(request.getAccount())) {
                log.warn("根据支付宝账号反查酷狗ID, 请求参数不合法, account: {}", request.getAccount());
                return RpcResultHelper.buildQueryKugouIdResult(SysResultCode.RECHARGE_RESOLVE_ERROR, Lists.newArrayList());
            }
            String accountFingerprint = dencryptService.fingerprint(request.getAccount());
            Optional<List<WithdrawAccountPO>> queryKugouIdAccount = this.drawCashService.queryKugouIdAccount(accountFingerprint);
            if (!queryKugouIdAccount.isPresent()) {
                log.warn("根据支付宝账号反查酷狗ID, 操作成功，查询kuGouId不存在, request: {}", request);
                return RpcResultHelper.buildQueryKugouIdResult(SysResultCode.SUCCESS, Lists.newArrayList());
            }
            List<Long> kugouIdList = Lists.newArrayList();
            List<WithdrawAccountPO> withdrawAccountPOList = queryKugouIdAccount.get();
            for (WithdrawAccountPO withdrawAccountPO : withdrawAccountPOList) {
                kugouIdList.add(withdrawAccountPO.getKugouId());
            }
            return RpcResultHelper.buildQueryKugouIdResult(SysResultCode.SUCCESS, kugouIdList);
        } catch (BizException e) {
            log.error("根据支付宝账号反查酷狗ID，操作异常, request: {}", request, e);
            return RpcResultHelper.buildQueryKugouIdResult(SysResultCode.RECHARGE_SYS_ERROR, Lists.newArrayList());
        } catch (Exception e) {
            log.error("根据支付宝账号反查酷狗ID，故障转移, request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public WithdrawResult bindAccountInfoV2(AccountChangeV2Request request) {
        try {
            log.warn("绑定提现账号信息, request: {}", request);
            if (!FinanceSignUtils.checkSign(request, apolloConfigService.appKeyOf(request.getAppId()))) {
                log.warn("绑定提现账号信息, 签名验证失败, request: {}", request);
                return RpcResultHelper.buildWithdrawResult(SysResultCode.RECHARGE_SIGN_INVALID);
            }
            SysResultCode sysResultCode = this.withdrawAccountService.saveAccountInfoV2(request);
            return RpcResultHelper.buildWithdrawResult(sysResultCode);
        } catch (BizException e) {
            log.error("绑定提现账号信息，操作异常, request: {}", request, e);
            return RpcResultHelper.buildWithdrawResult(SysResultCode.WITHDRAW_ACCOUNT_EXCEPTION);
        } catch (Exception e) {
            log.error("绑定提现账号信息，故障转移, request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public WithdrawResult createWithdrawOrderV2(CreateWithdrawOrderV2Request request) {
        try {
            log.warn("创建提现订单, request: {}", request);
            if (!FinanceSignUtils.checkSign(request, apolloConfigService.appKeyOf(request.getAppId()))) {
                log.warn("创建提现订单, 签名验证失败, request: {}", request);
                return RpcResultHelper.buildWithdrawResult(SysResultCode.RECHARGE_SIGN_INVALID);
            }
            SysResultCode sysResultCode = this.drawCashService.drawCashV2(request);
            return RpcResultHelper.buildWithdrawResult(sysResultCode);
        } catch (BizException e) {
            log.error("创建提现订单，操作异常, request: {}", request, e);
            return RpcResultHelper.buildWithdrawResult(SysResultCode.WITHDRAW_ORDER_EXCEPTION);
        } catch (Exception e) {
            log.error("创建提现订单，故障转移, request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public WechatAccountQueryResult queryWechatAccountInfoByKugouId(WechatAccountQueryRequest request) {
        try {
            log.warn("查询微信提现账号信息, request: {}", request);

            Optional<WithdrawAccountWechatDTO> optionalWithdrawAccountDTO = this.withdrawAccountService.queryWechatAccountInfoByKugouId(request.getKugouId(), request.getBizAppId());
            return optionalWithdrawAccountDTO.map(withdrawAccountDTO -> RpcResultHelper.buildWecahtAccountQueryResult(SysResultCode.SUCCESS, withdrawAccountDTO))
                    .orElseGet(() -> RpcResultHelper.buildWecahtAccountQueryResult(SysResultCode.SUCCESS, null));
        } catch (BizException e) {
            log.error("查询微信提现账号信息，操作异常, request: {}", request, e);
            return RpcResultHelper.buildWecahtAccountQueryResult(SysResultCode.WITHDRAW_ACCOUNT_EXCEPTION, null);
        } catch (Exception e) {
            log.error("查询微信提现账号信息，故障转移, request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }
}

