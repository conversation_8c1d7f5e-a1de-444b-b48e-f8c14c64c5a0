package com.kugou.fanxing.recharge.controller.thrift;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.ApproveDetailStatusEnum;
import com.kugou.fanxing.recharge.constant.ApproveStatusEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.bo.OfflinePayDataBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.po.RechargeRenewalPO;
import com.kugou.fanxing.recharge.model.vo.offline.ApplyInfoDetailVO;
import com.kugou.fanxing.recharge.thrift.*;
import com.kugou.fanxing.recharge.thrift.callback.CallbackResponse;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.recharge.withdraw.thrift.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class RpcResultHelper {

    private RpcResultHelper() {
    }

    public static CallbackResponse buildCallbackResponse(JsonResult.ResultCode resultCode) {
        return buildCallbackResponse(resultCode, Maps.newHashMap());
    }

    public static CallbackResponse buildCallbackResponse(JsonResult.ResultCode resultCode, Map<String, String> dataMap) {
        return new CallbackResponse().setCode(resultCode.getCode()).setMsg(resultCode.getMsg()).setData(JSON.toJSONString(dataMap));
    }

    public static PurchaseProductResponse buildPurchaseProductResponse(int code, String msg, Map<String, Object> dataMap) {
        return new PurchaseProductResponse().setCode(code).setMsg(msg)
                .setData(JSON.toJSONString(dataMap));
    }

    public static PurchaseProductResponse buildPurchaseProductResponse(JsonResult.ResultCode resultCode, Map<String, Object> dataMap) {
        return buildPurchaseProductResponse(resultCode.getCode(), resultCode.getMsg(), dataMap);
    }

    public static QueryApproveListResponse buildQueryApproveListResponse(JsonResult.ResultCode resultCode, int totalCount, List<ApplyInfoDetailVO> applyDetailList) {
        List<ApplyInfoDetailDTO> applyInfoDetailDTOList = applyDetailList.stream().map(vo -> new ApplyInfoDetailDTO()
                .setApplyType(vo.getApplyType())
                .setBatchNo(vo.getBatchNo())
                .setOrderNums(vo.getOrderNums())
                .setToKugouId(vo.getToKugouId())
                .setToUserId(vo.getToUserId())
                .setToUserName(vo.getToUserName())
                .setNewRechargeOrderNum(vo.getNewRechargeOrderNum())
                .setToUserAmount(vo.getToUserAmount().toPlainString())
                .setStatus(vo.getStatus())
                .setStatusLabel(ApproveDetailStatusEnum.labelOf(vo.getStatus()))
                .setCreateTime(DateHelper.format(vo.getCreateTime()))
                .setApproveStatus(vo.getApproveStatus())
                .setApproveStatusLabel(ApproveStatusEnum.labelOf(vo.getApproveStatus()))
                .setApproveRemark(vo.getApproveRemark())
                .setImagePath(vo.getImagePath())
                .setToUserCoin(vo.getToUserCoin().toPlainString())
                .setAlipayAccount(vo.getAlipayAccount())
                .setPhone(vo.getPhone())
                .setJsonExtend(vo.getJsonExtend()))
                .collect(Collectors.toList());
        return new QueryApproveListResponse()
                .setCode(resultCode.getCode())
                .setMsg(resultCode.getMsg())
                .setCount(totalCount)
                .setData(applyInfoDetailDTOList);
    }

    public static QueryUserRechargeListResponse buildQueryUserRechargeListResponse(JsonResult.ResultCode resultCode, int totalCount, List<RechargeAcrossDTO> rechargeAcrossDTOList) {
        return new QueryUserRechargeListResponse()
                .setCode(resultCode.getCode())
                .setMsg(resultCode.getMsg())
                .setCount(totalCount)
                .setData(rechargeAcrossDTOList);
    }

    public static RechargeApproveResponse buildRechargeApproveResponse(JsonResult.ResultCode resultCode) {
        return new RechargeApproveResponse()
                .setCode(resultCode.getCode())
                .setMsg(resultCode.getMsg());
    }

    public static QueryUserRenewalResponse buildQueryUserRenewalResponse(SysResultCode resultCode) {
        return new QueryUserRenewalResponse()
                .setCode(resultCode.getCode())
                .setMsg(resultCode.getMsg());
    }

    public static QueryUserRenewalResponse buildQueryUserRenewalResponse(SysResultCode resultCode, RechargeRenewalPO rechargeRenewalPO, QueryUserRenewalRequest request) {
        Date lastRenewalTime = rechargeRenewalPO.getLastRenewalTime();
        Date nextRenewalTime = rechargeRenewalPO.getNextRenewalTime();
        UserRenewalInfoDTO userRenewalInfoDTO = new UserRenewalInfoDTO();
        userRenewalInfoDTO.setPid(rechargeRenewalPO.getPid());
        userRenewalInfoDTO.setKugouId(rechargeRenewalPO.getKugouId());
        userRenewalInfoDTO.setRenewalType(3);
        userRenewalInfoDTO.setBusinessId(request.getBusinessId());
        userRenewalInfoDTO.setSignStatus(rechargeRenewalPO.getSignStatus());
        userRenewalInfoDTO.setLastRenewalTime(Objects.nonNull(lastRenewalTime) ? lastRenewalTime.getTime() : 0);
        userRenewalInfoDTO.setNextRenewalTime(Objects.nonNull(nextRenewalTime) ? nextRenewalTime.getTime() : 0);
        return new QueryUserRenewalResponse()
                .setCode(resultCode.getCode())
                .setMsg(resultCode.getMsg())
                .setData(userRenewalInfoDTO);
    }

    public static ResultList buildResultList(int ret, String msg, List<RechargeInfo> list,long lastId) {
        ResultList resultList = new ResultList();
        resultList.setRet(ret);
        resultList.setMsg(msg).setLastId(lastId);
        if(list != null){
            resultList.setData(list);
        }
        return resultList;
    }


    public static ResultList buildResultList(int ret, String msg) {
        ResultList resultList = new ResultList();
        resultList.setRet(ret);
        resultList.setMsg(msg);
        return resultList;
    }

    public static ResultInfo buildResultInfo(int ret,String msg,RechargeInfo data) {
        ResultInfo resultInfo = new ResultInfo();
        resultInfo.setRet(ret).setMsg(msg);
        if(data != null){
            resultInfo.setData(data);
        }
        return resultInfo;
    }

    public static WithdrawResult buildWithdrawResult(SysResultCode sysResultCode) {
        return new WithdrawResult()
                .setCode(sysResultCode.getCode())
                .setMsg(sysResultCode.getMsg());
    }

    public static WithdrawOrderResult buildWithdrawOrderResult(JsonResult.ResultCode resultCode, WithdrawOrderDTO withdrawOrderDTO) {
        WithdrawOrderResult withdrawOrderResult = new WithdrawOrderResult();
        withdrawOrderResult.setCode(resultCode.getCode());
        withdrawOrderResult.setMsg(resultCode.getMsg());
        withdrawOrderResult.setData(withdrawOrderDTO);
        return withdrawOrderResult;
    }

    public static AccountQueryResult buildAccountQueryResult(JsonResult.ResultCode resultCode, WithdrawAccountDTO withdrawAccountDTO) {
        AccountQueryResult result = new AccountQueryResult();
        result.setCode(resultCode.getCode());
        result.setMsg(resultCode.getMsg());
        result.setData(withdrawAccountDTO);
        return result;
    }

    public static WechatAccountQueryResult buildWecahtAccountQueryResult(JsonResult.ResultCode resultCode, WithdrawAccountWechatDTO withdrawAccountDTO) {
        WechatAccountQueryResult result = new WechatAccountQueryResult();
        result.setCode(resultCode.getCode());
        result.setMsg(resultCode.getMsg());
        result.setData(withdrawAccountDTO);
        return result;
    }

    public static QueryWithdrawKugouIdResult buildQueryKugouIdResult(JsonResult.ResultCode resultCode, List<Long>kugouIdList) {
        return new QueryWithdrawKugouIdResult()
                .setCode(resultCode.getCode())
                .setMsg(resultCode.getMsg())
                .setData(kugouIdList);
    }

    public static UserYearRechargeStatResponse buildUserYearRechargeStatResponse(JsonResult.ResultCode resultCode, UserYearRechargeStatData userYearRechargeStatData) {
        UserYearRechargeStatResponse userYearRechargeStatResponse = new UserYearRechargeStatResponse();
        userYearRechargeStatResponse.setCode(resultCode.getCode()).setMsg(resultCode.getMsg());
        if (userYearRechargeStatData != null) {
            userYearRechargeStatResponse.setData(userYearRechargeStatData);
        }
        return userYearRechargeStatResponse;
    }


    public static UserEverRechargeResponse buildUserEverRechargeResponse(JsonResult.ResultCode resultCode, UserEverRechargeDTO userEverRechargeDTO) {
        UserEverRechargeResponse userEverRechargeResponse = new UserEverRechargeResponse();
        userEverRechargeResponse.setCode(resultCode.getCode()).setMsg(resultCode.getMsg());
        if (userEverRechargeDTO != null) {
            userEverRechargeResponse.setData(userEverRechargeDTO);
        }
        return userEverRechargeResponse;
    }

    public static ResultSuccessList buildResultSuccessList(int ret, String msg, List<RechargeSuccessInfo> rechargeSuccessInfoList, long lastRechargeId) {
        ResultSuccessList resultList = new ResultSuccessList();
        resultList.setRet(ret);
        resultList.setMsg(msg).setLastRechargeId(lastRechargeId);
        if (CollectionUtils.isNotEmpty(rechargeSuccessInfoList)) {
            resultList.setData(rechargeSuccessInfoList);
        }
        return resultList;
    }

    public static ResultSuccessList buildResultSuccessList(int ret, String msg) {
        ResultSuccessList resultList = new ResultSuccessList();
        resultList.setRet(ret);
        resultList.setMsg(msg);
        return resultList;
    }

    public static SyncKupayOfflineRechargeOrderResponse buildSyncKupayOfflineRechargeOrderResponse(JsonResult.ResultCode resultCode, OfflinePayDataBO offlinePayDataBO) {
        SyncKupayOfflineRechargeOrderResponse response = new SyncKupayOfflineRechargeOrderResponse();
        response.setCode(resultCode.getCode()).setMsg(resultCode.getMsg());
        if (offlinePayDataBO != null) {
            SyncKupayOfflineRechargeOrderDTO orderDTO = new SyncKupayOfflineRechargeOrderDTO();
            orderDTO.setOutTradeNo(offlinePayDataBO.getOut_trade_no());
            orderDTO.setOrderNo(offlinePayDataBO.getOrder_no());
            orderDTO.setTradeNo(offlinePayDataBO.getTrade_no());
            orderDTO.setTotalFee(offlinePayDataBO.getTotal_fee());
            orderDTO.setSubject(offlinePayDataBO.getSubject());
            orderDTO.setDesc(offlinePayDataBO.getDesc());
            response.setData(orderDTO);
        }
        return response;
    }

    public static QueryRechargeOrderResponse buildQueryRechargeOrderResponse(SysResultCode sysResultCode, Optional<RechargeAcrossPO> optionalRechargeAcrossPO) {
        return buildQueryRechargeOrderResponse(sysResultCode, optionalRechargeAcrossPO, "");
    }

    public static QueryRechargeOrderResponse buildQueryRechargeOrderResponse(SysResultCode sysResultCode, Optional<RechargeAcrossPO> optionalRechargeAcrossPO, String orderNum) {
        QueryRechargeOrderResponse response = new QueryRechargeOrderResponse();
        response.setCode(sysResultCode.getCode());
        response.setMsg(sysResultCode.getMsg());
        optionalRechargeAcrossPO.ifPresent(rechargeAcrossPO -> {
            BigDecimal money = Objects.nonNull(rechargeAcrossPO.getMoney()) ? rechargeAcrossPO.getMoney() : BigDecimal.ZERO;
            BigDecimal coupon = Objects.nonNull(rechargeAcrossPO.getCoupon()) ? rechargeAcrossPO.getCoupon() : BigDecimal.ZERO;
            RechargeOrderInfoDto rechargeOrderInfoDto = new RechargeOrderInfoDto();
            rechargeOrderInfoDto.setRechargeOrderNum(rechargeAcrossPO.getRechargeOrderNum());
            rechargeOrderInfoDto.setOutTradeNo(StringUtils.defaultString(rechargeAcrossPO.getConsumeOrderNum()));
            rechargeOrderInfoDto.setAddTime(rechargeAcrossPO.getAddTime());
            rechargeOrderInfoDto.setRechargeTime(rechargeAcrossPO.getRechargeTime());
            rechargeOrderInfoDto.setKugouId(rechargeAcrossPO.getKugouId());
            rechargeOrderInfoDto.setAgentKugouId(rechargeAcrossPO.getAgentKugouId());
            rechargeOrderInfoDto.setCoin(rechargeAcrossPO.getCoin().stripTrailingZeros().toPlainString());
            rechargeOrderInfoDto.setAmount(rechargeAcrossPO.getAmount().stripTrailingZeros().toPlainString());
            rechargeOrderInfoDto.setMoney(money.stripTrailingZeros().toPlainString());
            rechargeOrderInfoDto.setCoupon(coupon.stripTrailingZeros().toPlainString());
            rechargeOrderInfoDto.setRealAmount(rechargeAcrossPO.getRealAmount().stripTrailingZeros().toPlainString());
            rechargeOrderInfoDto.setPayTypeId(rechargeAcrossPO.getPayTypeId());
            rechargeOrderInfoDto.setExtraJsonData(StringUtils.defaultString(rechargeAcrossPO.getExtraJsonData()));
            rechargeOrderInfoDto.setRefer(rechargeAcrossPO.getRefer());
            rechargeOrderInfoDto.setCFrom(rechargeAcrossPO.getCFrom());
            rechargeOrderInfoDto.setChannelId(rechargeAcrossPO.getChannelId());
            rechargeOrderInfoDto.setReType(rechargeAcrossPO.getReType());
            rechargeOrderInfoDto.setExtend(StringUtils.defaultString(rechargeAcrossPO.getExtend()));
            rechargeOrderInfoDto.setBusinessId(StringUtils.defaultString(rechargeAcrossPO.getBusinessId()));
            rechargeOrderInfoDto.setTradeTime(rechargeAcrossPO.getTradeTime());
            rechargeOrderInfoDto.setTradeNo(StringUtils.defaultString(rechargeAcrossPO.getTradeNo()));
            rechargeOrderInfoDto.setPartner(StringUtils.defaultString(rechargeAcrossPO.getPartner()));
            rechargeOrderInfoDto.setCouponOrderId(rechargeAcrossPO.getCouponOrderId());
            rechargeOrderInfoDto.setCouponStatus(rechargeAcrossPO.getCouponStatus());
            rechargeOrderInfoDto.setCouponId(rechargeAcrossPO.getCouponId());
            rechargeOrderInfoDto.setStatus(rechargeAcrossPO.getStatus());
            if (StringUtils.isNotBlank(orderNum)) {
                rechargeOrderInfoDto.setOrderNum(orderNum);
            }
            response.setData(rechargeOrderInfoDto);
        });
        return response;
    }

    public static PurchaseCurrencyResponse buildPurchaseCurrencyResponse(BizException e) {
        PurchaseCurrencyResponse response = new PurchaseCurrencyResponse();
        response.setCode(e.getCode());
        response.setMsg(e.getMessage());
        return response;
    }

    public static PurchaseCurrencyResponse buildPurchaseCurrencyResponse(SysResultCode sysResultCode, PurchaseCurrencyDto purchaseCurrencyDto) {
        PurchaseCurrencyResponse response = new PurchaseCurrencyResponse();
        response.setCode(sysResultCode.getCode());
        response.setMsg(sysResultCode.getMsg());
        response.setData(purchaseCurrencyDto);
        return response;
    }

    public static ReportIapReceiptResponse buildReportIapReceiptResponse(SysResultCode sysResultCode, ReportIapReceiptDto reportIapReceiptDto) {
        ReportIapReceiptResponse response = new ReportIapReceiptResponse();
        response.setCode(sysResultCode.getCode());
        response.setMsg(sysResultCode.getMsg());
        response.setData(reportIapReceiptDto);
        return response;
    }

    public static QueryBizOrderResponse buildQueryBizOrderResponse(SysResultCode sysResultCode, Optional<RechargeAcrossPO> optionalRechargeAcrossPO) {
        QueryBizOrderResponse response = new QueryBizOrderResponse();
        response.setCode(sysResultCode.getCode());
        response.setMsg(sysResultCode.getMsg());
        optionalRechargeAcrossPO.ifPresent(rechargeAcrossPO -> {
            BigDecimal money = Objects.nonNull(rechargeAcrossPO.getMoney()) ? rechargeAcrossPO.getMoney() : BigDecimal.ZERO;
            BigDecimal coupon = Objects.nonNull(rechargeAcrossPO.getCoupon()) ? rechargeAcrossPO.getCoupon() : BigDecimal.ZERO;
            RechargeOrderInfoDto rechargeOrderInfoDto = new RechargeOrderInfoDto();
            rechargeOrderInfoDto.setRechargeOrderNum(rechargeAcrossPO.getRechargeOrderNum());
            rechargeOrderInfoDto.setOutTradeNo(StringUtils.defaultString(rechargeAcrossPO.getConsumeOrderNum()));
            rechargeOrderInfoDto.setAddTime(rechargeAcrossPO.getAddTime());
            rechargeOrderInfoDto.setRechargeTime(rechargeAcrossPO.getRechargeTime());
            rechargeOrderInfoDto.setKugouId(rechargeAcrossPO.getKugouId());
            rechargeOrderInfoDto.setAgentKugouId(rechargeAcrossPO.getAgentKugouId());
            rechargeOrderInfoDto.setCoin(rechargeAcrossPO.getCoin().stripTrailingZeros().toPlainString());
            rechargeOrderInfoDto.setAmount(rechargeAcrossPO.getAmount().stripTrailingZeros().toPlainString());
            rechargeOrderInfoDto.setMoney(money.stripTrailingZeros().toPlainString());
            rechargeOrderInfoDto.setCoupon(coupon.stripTrailingZeros().toPlainString());
            rechargeOrderInfoDto.setRealAmount(rechargeAcrossPO.getRealAmount().stripTrailingZeros().toPlainString());
            rechargeOrderInfoDto.setPayTypeId(rechargeAcrossPO.getPayTypeId());
            rechargeOrderInfoDto.setExtraJsonData(StringUtils.defaultString(rechargeAcrossPO.getExtraJsonData()));
            rechargeOrderInfoDto.setRefer(rechargeAcrossPO.getRefer());
            rechargeOrderInfoDto.setCFrom(rechargeAcrossPO.getCFrom());
            rechargeOrderInfoDto.setChannelId(rechargeAcrossPO.getChannelId());
            rechargeOrderInfoDto.setReType(rechargeAcrossPO.getReType());
            rechargeOrderInfoDto.setExtend(StringUtils.defaultString(rechargeAcrossPO.getExtend()));
            rechargeOrderInfoDto.setBusinessId(StringUtils.defaultString(rechargeAcrossPO.getBusinessId()));
            rechargeOrderInfoDto.setTradeTime(rechargeAcrossPO.getTradeTime());
            rechargeOrderInfoDto.setTradeNo(StringUtils.defaultString(rechargeAcrossPO.getTradeNo()));
            rechargeOrderInfoDto.setPartner(StringUtils.defaultString(rechargeAcrossPO.getPartner()));
            rechargeOrderInfoDto.setCouponOrderId(rechargeAcrossPO.getCouponOrderId());
            rechargeOrderInfoDto.setCouponStatus(rechargeAcrossPO.getCouponStatus());
            rechargeOrderInfoDto.setCouponId(rechargeAcrossPO.getCouponId());
            rechargeOrderInfoDto.setStatus(rechargeAcrossPO.getStatus());
            response.setData(rechargeOrderInfoDto);
        });
        return response;
    }

    public static QueryRechargeListResponse buildQueryRechargeListResponse(SysResultCode sysResultCode,
                                                                                 List<RechargeSuccessInfo> rechargeSuccessInfoList) {
        QueryRechargeListResponse response = new QueryRechargeListResponse();
        response.setCode(sysResultCode.getCode());
        response.setMsg(sysResultCode.getMsg());
        if (sysResultCode.isSuccess()) {
            List<RechargeSuccessInfo> data = CollectionUtils.isNotEmpty(rechargeSuccessInfoList)
                    ? rechargeSuccessInfoList : Lists.newArrayList();
            long lastRechargeId = rechargeSuccessInfoList.stream().map(RechargeSuccessInfo::getRechargeId)
                    .reduce((first, second) -> second).orElse(0L);
            response.setLastRechargeId(lastRechargeId);
            response.setData(data);
        }
        return response;
    }

    public static IapNotificationResponse buildIapNotificationResponse(SysResultCode sysResultCode) {
        IapNotificationResponse response = new IapNotificationResponse();
        response.setCode(sysResultCode.getCode());
        response.setMsg(sysResultCode.getMsg());
        return response;
    }

    public static WxPayAndContractResponse buildWxPayAndContractResponse(JsonResult.ResultCode resultCode,String data) {
        return new WxPayAndContractResponse()
                .setCode(resultCode.getCode())
                .setMsg(resultCode.getMsg())
                .setData(data);
    }
}
