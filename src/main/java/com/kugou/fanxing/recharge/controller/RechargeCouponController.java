package com.kugou.fanxing.recharge.controller;

import com.kugou.fanxing.recharge.common.aop.annotation.CdnCache;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.model.vo.RechargeCouponInfoVO;
import com.kugou.fanxing.recharge.service.RechargeCouponService;
import com.kugou.fanxing.recharge.util.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 充值代金券
 */
@Slf4j
@RestController
public class RechargeCouponController {

    @Autowired
    private RechargeCouponService rechargeCouponService;

    /**
     * 网站充值代金券列表
     */
    @CdnCache(cacheStrategy = CdnCache.CacheStrategy.NO_CACHE)
    @RequestMapping(value = UrlConstants.GET_COUPON_LIST_PC)
    public JsonResult<List<RechargeCouponInfoVO>> getCouponList(WebCommonParam webCommonParam) {
        return rechargeCouponService.getCouponListResult(webCommonParam.getKugouId());
    }

    /**
     * 安卓充值代金券列表
     */
    @CdnCache(cacheStrategy = CdnCache.CacheStrategy.NO_CACHE)
    @RequestMapping(value = UrlConstants.GET_COUPON_LIST_MOBILE)
    public JsonResult<List<RechargeCouponInfoVO>> getCouponListM(WebCommonParam webCommonParam) {
        return rechargeCouponService.getCouponListResult(webCommonParam.getKugouId());
    }

}
