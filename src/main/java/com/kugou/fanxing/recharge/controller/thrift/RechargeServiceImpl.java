package com.kugou.fanxing.recharge.controller.thrift;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.kugou.config.Env;
import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.recharge.alert.AlerterFacade;
import com.kugou.fanxing.recharge.constant.ApplyTypeEnum;
import com.kugou.fanxing.recharge.constant.ApproveStatusEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.factory.payment.AppleAppPaymentService;
import com.kugou.fanxing.recharge.model.bo.OfflinePayDataBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.po.RechargeRenewalPO;
import com.kugou.fanxing.recharge.model.po.offline.BigRechargeApply;
import com.kugou.fanxing.recharge.model.vo.offline.ApplyInfoDetailVO;
import com.kugou.fanxing.recharge.service.*;
import com.kugou.fanxing.recharge.service.appstore.RechargeAutoRenewService;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.offline.BigRechargeService;
import com.kugou.fanxing.recharge.thrift.RechargeService;
import com.kugou.fanxing.recharge.thrift.*;
import com.kugou.fanxing.recharge.util.DateHelper;
import com.kugou.fanxing.recharge.util.FinanceSignEnhancedUtils;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.recharge.util.Pagination;
import com.kugou.fanxing.thrift.pay.v2.PlatformPayV2Service;
import com.kugou.fanxing.thrift.pay.v2.QueryRechargeOrderNumByTradeNoRequest;
import com.kugou.fanxing.thrift.pay.v2.QueryRechargeOrderNumByTradeNoResponse;
import com.kugou.fanxing.thrift.recharge.ios.*;
import com.kugou.rpc.exception.ServerException;
import com.kugou.rpc.server.thrift.annotation.ThriftService;
import com.kugou.rpc.server.web.RpcContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@ThriftService("/platform_recharge_service/thrift/rechargeService")
public class RechargeServiceImpl implements RechargeService.Iface {

    @Autowired
    private AlerterFacade alerterFacade;
    @Autowired
    private PayService payService;
    @Autowired
    private PurchaseProductService purchaseProductService;
    @Autowired
    private PurchaseCurrencyService purchaseCurrencyService;
    @Autowired
    private BigRechargeService bigRechargeService;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private RechargeAutoRenewService rechargeAutoRenewService;
    @Autowired
    private Env env;
    @Autowired
    private KupayService kupayService;
    @Autowired
    private RechargeOrderService rechargeOrderService;
    @Autowired
    private AppStoreRpcService.Iface appStoreRpcService;
    @Autowired
    private PlatformPayV2Service.Iface platformPayV2Service;
    @Autowired
    private OrderIdService orderIdService;
    @Autowired
    private AppleAppPaymentService appleAppPaymentService;

    /**
     * 查询用户充值记录
     */
    @Override
    public QueryUserRechargeListResponse queryUserRechargeList(QueryUserRechargeListRequest request) {
        log.warn("查询用户充值记录, 开始处理请求, request: {}", request);
        try {
            String month = request.getMonth();
            long kugouId = request.getKugouId();
            if (kugouId < 1 || !DateHelper.isValidYearMonth(month)) {
                log.warn("查询用户充值记录, 参数验证失败, request: {}", request);
                return RpcResultHelper.buildQueryUserRechargeListResponse(SysResultCode.RECHARGE_PARAM_ERROR, 0, Lists.newArrayList());
            }
            if (!FinanceSignUtils.checkSign(request, apolloConfigService.appKeyOf(request.getAppId()))) {
                log.warn("查询用户充值记录, 签名验证失败, request: {}", request);
                return RpcResultHelper.buildQueryUserRechargeListResponse(SysResultCode.RECHARGE_SIGN_INVALID, 0, Lists.newArrayList());
            }
            Pagination pagination = new Pagination.Builder(request.getPage(), request.getPageSize()).build();
            List<RechargeAcrossDTO> rechargeAcrossDTOList = payService.getRechargeListForPC(month, kugouId, pagination);
            return RpcResultHelper.buildQueryUserRechargeListResponse(JsonResult.DefaultResultCodeEnum.SUCCESS, pagination.getTotalCount(), rechargeAcrossDTOList);
        } catch (BizException e) {
            log.error("查询用户充值记录，查询异常。request: {}", request, e);
            return RpcResultHelper.buildQueryUserRechargeListResponse(JsonResult.DefaultResultCodeEnum.FAILURE, 0, Lists.newArrayList());
        } catch (Exception e) {
            log.error("查询用户充值记录，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    /**
     * 线下充值审核列表
     */
    @Override
    public QueryApproveListResponse queryOfflineRechargeApproveList(QueryApproveListRequest request) {
        try {
            ApplyTypeEnum applyType = ApplyTypeEnum.statusOf(request.getApplyType())
                    .orElseThrow(() -> new ContextedRuntimeException("申请类型不存在").addContextValue("applyType", request.getApplyType()));
            long batchNo = request.getBatchNo();
            String rechargeOrderNum = request.getRechargeOrderNum();
            int page = request.getPage();
            int pageSize = request.getPageSize();
            log.warn("线下充值审核列表, batchNo: {}, rechargeOrderNum: {}, page: {}, pageSize: {}", batchNo, rechargeOrderNum, page, pageSize);
            Pagination pagination = new Pagination.Builder(page, pageSize).build();
            List<ApplyInfoDetailVO> applyDetailListSorted = this.bigRechargeService.getApplyDetailListSortedByPage(applyType, batchNo, rechargeOrderNum, pagination);
            int count = this.bigRechargeService.getApplyDetailListCount(applyType, batchNo, rechargeOrderNum);
            pagination.setTotalCount(count);
            return RpcResultHelper.buildQueryApproveListResponse(JsonResult.DefaultResultCodeEnum.SUCCESS, pagination.getTotalCount(), applyDetailListSorted);
        } catch (BizException e) {
            log.error("线下充值审核列表，操作异常。request: {}", request, e);
            return RpcResultHelper.buildQueryApproveListResponse(JsonResult.DefaultResultCodeEnum.FAILURE, 0, Lists.newArrayList());
        } catch (Exception e) {
            log.error("线下充值审核列表，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    /**
     * 线下充值申请审核
     */
    @Override
    public RechargeApproveResponse approveOfflineRechargeApply(RechargeApproveRequest request) {
        try {
            long batchNo = request.getBatchNo();
            int status = request.getStatus();
            String remark = request.getRemark();
            String bankTransactionId = request.getBankTransactionId();
            log.warn("线下充值申请审核，请求参数。request: {}", request);
            // 校验操作类型
            Optional<ApproveStatusEnum> approveStatusEnumOptional = ApproveStatusEnum.statusOf(status);
            if (!approveStatusEnumOptional.isPresent()) {
                return RpcResultHelper.buildRechargeApproveResponse(SysResultCode.RECHARGE_PARAM_ERROR);
            }
            ApproveStatusEnum approveStatusEnum = approveStatusEnumOptional.get();
            // 校验申请编号
            Optional<BigRechargeApply> bigRechargeApplyOptional = this.bigRechargeService.getRechargeApplyByBatchNo(batchNo);
            if (!bigRechargeApplyOptional.isPresent()) {
                log.warn("线下充值申请审核，申请编号不存在。batchNo: {}", batchNo);
                return RpcResultHelper.buildRechargeApproveResponse(SysResultCode.E_40000223);
            }
            BigRechargeApply bigRechargeApply = bigRechargeApplyOptional.get();
            if (bigRechargeApply.getApproveStatus() != ApproveStatusEnum.APPROVE_STATUS_0.getStatus()) {
                log.warn("线下充值申请审核，申请编号已审核。batchNo: {}", batchNo);
                return RpcResultHelper.buildRechargeApproveResponse(SysResultCode.E_40000224);
            }
            // 风控服务校验
            boolean verifyResult = this.bigRechargeService.strategyVerify(bigRechargeApply, approveStatusEnum, remark);
            if (verifyResult) {
                log.error("线下充值申请审核，风控服务阻断。request: {}", request);
                return RpcResultHelper.buildRechargeApproveResponse(SysResultCode.E_40000218);
            }
            // 更新审核状态
            SysResultCode resultCode = this.bigRechargeService.approveApply(bigRechargeApply, approveStatusEnum, remark, bankTransactionId);
            if (SysResultCode.SUCCESS.equals(resultCode)) {
                log.error("线下充值申请审核，发送短信与站内信通知。request: {}", request);
                if (ApproveStatusEnum.APPROVE_STATUS_1.equals(approveStatusEnum)) {
                    String content = "尊敬的用户：您的支付宝转账充值申请已完成，请您注意查收，如有问题请前往客服中心联系客服。";
                    alerterFacade.sendMSG("支付宝转账充值结果提醒！ ", content, bigRechargeApply.getApplyKugouId());
                    alerterFacade.sendSMS("【酷狗直播】", content, bigRechargeApply.getApplyKugouId());
                } else if (ApproveStatusEnum.APPROVE_STATUS_2.equals(approveStatusEnum)) {
                    String content = "尊敬的用户：您的支付宝转账充值申请出现了问题，请重新上传信息。如有问题请前往客服中心联系客服。";
                    alerterFacade.sendMSG("支付宝转账充值结果提醒！ ", content, bigRechargeApply.getApplyKugouId());
                    alerterFacade.sendSMS("【酷狗直播】", content, bigRechargeApply.getApplyKugouId());
                } else {
                    log.error("无法处理的审核类型: {}", resultCode);
                }
            }
            return RpcResultHelper.buildRechargeApproveResponse(resultCode);
        } catch (BizException e) {
            log.error("线下充值申请审核，操作异常。request: {}", request, e);
            return RpcResultHelper.buildRechargeApproveResponse(SysResultCode.RECHARGE_SYS_ERROR);
        } catch (Exception e) {
            log.error("线下充值申请审核，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    /**
     * 充扣下单接口V1
     */
    @Override
    @Deprecated
    public PurchaseProductResponse createOrderForPurchaseProduct(PurchaseProductRequest request) {
        try {
            // 处理测试环境
            if ((env.isDev() || env.isTest()) && apolloConfigService.getRechargePoorMode()) {
                request.setAmount(new BigDecimal("0.02").stripTrailingZeros().toPlainString());
            }
            // 校验签名参数
            String sign = FinanceSignUtils.makeSign(request, apolloConfigService.businessKeyOf(request.getBusinessId()));
            log.warn("client sign: {}, server sign: {}", request.getSign(), sign);
            // 转调PHP充值
            Map<String, Object> dataMap = this.payService.callPhpRecharge(request);
            return RpcResultHelper.buildPurchaseProductResponse(SysResultCode.SUCCESS, dataMap);
        } catch (BizException e) {
            log.error("调用PHP充扣下单接口，操作异常。request: {}", request, e);
            return RpcResultHelper.buildPurchaseProductResponse(SysResultCode.RECHARGE_SYS_ERROR, Maps.newHashMap());
        } catch (Exception e) {
            log.error("调用PHP充扣下单接口，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    /**
     * 充扣下单接口V2
     */
    @Override
    public PurchaseProductResponse createOrderForPurchaseProductV2(PurchaseProductRequestV2 request) {
        try {
            log.error("调用充扣V2下单接口，请求参数。request: {}", request);
            // 处理测试环境
            if ((env.isDev() || env.isTest()) && apolloConfigService.getRechargePoorMode()) {
                BigDecimal poorModeAmount = apolloConfigService.getRechargePoorModeAmount();
                request.setAmount(poorModeAmount.stripTrailingZeros().toPlainString());
            }
            // 校验签名参数
            String sign = FinanceSignEnhancedUtils.makeSign(request, apolloConfigService.businessKeyOf(request.getBusinessId()));
            String sign2 = FinanceSignEnhancedUtils.makeSign(request, apolloConfigService.businessKeyOf(request.getBusinessId()),
                    Sets.newHashSet("orderExpireTime", "showUrl"));
            if(!sign.equals(request.getSign()) && !sign2.equals(request.getSign())){
                Cat.logEvent("signError", request.getBusinessId());
                //默认关闭,测试环境打开
                if(apolloConfigService.isValidateSignWithPurchaseProductV2()){
                    throw new BizException(SysResultCode.E_30000009);
                }
            }
            log.warn("调用充扣V2下单接口，签名信息。request: {}, client sign: {}, server sign: {}, {}", request, request.getSign(), sign, sign2);
            payService.purchaseOrderListCheck(request);
            String name = StringUtils.joinWith("-", request.getBusinessId(), request.getPayTypeId());
            Cat.logEvent("callJavaRechargeV2", name);
            Map<String, Object>  payload = this.purchaseProductService.callJavaRechargeV2(request);
            log.error("调用充扣V2下单接口，成功响应。request: {}, payload: {}", request, payload);
            return RpcResultHelper.buildPurchaseProductResponse(SysResultCode.SUCCESS, payload);
        } catch (BizException e) {
            log.error("调用充扣V2下单接口，操作异常。request: {}", request, e);
            return RpcResultHelper.buildPurchaseProductResponse(SysResultCode.codeOf(e.getCode()), Maps.newHashMap());
        } catch (Exception e) {
            log.error("调用充扣V2下单接口，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public PurchaseCurrencyResponse createOrderForPurchaseCurrency(PurchaseCurrencyRequest request) {
        try {
            log.warn("充值购买虚拟货币下单接口，请求参数。serverName: {}, clientIp: {}, request: {}",
                    RpcContextHolder.getClientName(), RpcContextHolder.getClientIp(), request);
            // 校验签名参数
            String clientSign = StringUtils.defaultString(request.getSign());
            String serverSign = FinanceSignUtils.makeSign(request, apolloConfigService.businessKeyOf(request.getBusinessId()));
            if (!StringUtils.equals(clientSign, serverSign)) {
                log.warn("充值购买虚拟货币下单接口，非法签名。request: {}, clientSign: {}, serverSign: {}", request, clientSign, serverSign);
                return RpcResultHelper.buildPurchaseCurrencyResponse(SysResultCode.RECHARGE_SIGN_INVALID, null);
            }
            // 处理充值金额
            if ((env.isDev() || env.isTest()) && apolloConfigService.getRechargePoorMode()) {
                BigDecimal poorModeAmount = apolloConfigService.getRechargePoorModeAmount();
                request.setAmount(poorModeAmount.stripTrailingZeros().toPlainString());
            }
            PurchaseCurrencyDto purchaseCurrencyDto = this.purchaseCurrencyService.purchase(request);
            return RpcResultHelper.buildPurchaseCurrencyResponse(SysResultCode.SUCCESS, purchaseCurrencyDto);
        } catch (BizException e) {
            log.error("充值购买虚拟货币下单接口，操作异常。request: {}", request, e);
            return RpcResultHelper.buildPurchaseCurrencyResponse(e);
        } catch (Exception e) {
            log.error("充值购买虚拟货币下单接口，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public QueryUserRenewalResponse queryUserRenewalInfo(QueryUserRenewalRequest request) {
        try {
            boolean isValidSign = FinanceSignUtils.checkSign(request, apolloConfigService.appKeyOf(request.getAppId()));
            if (!isValidSign) {
                log.warn("查询用户签约信息，签名错误。appId: {}, appKey: {}, request: {}", request.getAppId(), apolloConfigService.appKeyOf(request.getAppId()), request);
                return RpcResultHelper.buildQueryUserRenewalResponse(SysResultCode.RECHARGE_SIGN_INVALID);
            }
            Optional<RechargeRenewalPO> optionalRechargeRenewalPO = this.rechargeAutoRenewService.getLatestUserRenewal(request.getKugouId(), request.getRenewalType(), Long.parseLong(request.getBusinessId()));
            if (!optionalRechargeRenewalPO.isPresent()) {
                log.warn("查询用户签约信息，尚未签约。request: {}", request);
                return RpcResultHelper.buildQueryUserRenewalResponse(SysResultCode.SUCCESS);
            }
            return RpcResultHelper.buildQueryUserRenewalResponse(SysResultCode.SUCCESS, optionalRechargeRenewalPO.get(), request);
        } catch (BizException e) {
            log.error("查询用户签约信息，查询异常。request: {}", request, e);
            return RpcResultHelper.buildQueryUserRenewalResponse(SysResultCode.RECHARGE_SYS_ERROR);
        } catch (Exception e) {
            log.error("查询用户签约信息，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public QueryUserRechargeListResponse queryUserRechargeListWithTime(QueryUserRechargeListRequestWithTime request) {
        log.warn("查询用户充值记录, 开始处理请求, request: {}", request);
        try {
            String month = request.getMonth();
            long kugouId = request.getKugouId();
            if (kugouId < 1 || request.getStartTime() > request.getEndTime() || !DateHelper.isValidYearMonth(month)) {
                log.warn("查询用户充值记录, 参数验证失败, request: {}", request);
                return RpcResultHelper.buildQueryUserRechargeListResponse(SysResultCode.RECHARGE_PARAM_ERROR, 0, Lists.newArrayList());
            }
            if (!FinanceSignUtils.checkSign(request, apolloConfigService.appKeyOf(request.getAppId()))) {
                log.warn("查询用户充值记录, 签名验证失败, request: {}", request);
                return RpcResultHelper.buildQueryUserRechargeListResponse(SysResultCode.RECHARGE_SIGN_INVALID, 0, Lists.newArrayList());
            }
            Pagination pagination = new Pagination.Builder(request.getPage(), request.getPageSize()).build();
            List<RechargeAcrossDTO> rechargeAcrossDTOList = payService.getRechargeListByUserWithTime(month,kugouId, request.getStartTime(),request.getEndTime(),pagination);
            return RpcResultHelper.buildQueryUserRechargeListResponse(JsonResult.DefaultResultCodeEnum.SUCCESS, pagination.getTotalCount(), rechargeAcrossDTOList);
        } catch (BizException e) {
            log.error("查询用户充值记录，查询异常。request: {}", request, e);
            return RpcResultHelper.buildQueryUserRechargeListResponse(JsonResult.DefaultResultCodeEnum.FAILURE, 0, Lists.newArrayList());
        } catch (Exception e) {
            log.error("查询用户充值记录，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public SyncKupayOfflineRechargeOrderResponse syncKupayOfflineRechargeOrder(SyncKupayOfflineRechargeOrderRequest request) {
        try {
            log.warn("同步线下充值订单号至酷狗支付网关, 开始处理请求, request: {}", request);
            BigDecimal money = new BigDecimal(request.getMoney());
            OfflinePayDataBO offlinePayDataBO = this.kupayService.orderOfflinepayV1(request.getKugouId(), request.getRechargeOrderNum(), request.getTradeNo(), request.getRechargeTime(), money);
            return RpcResultHelper.buildSyncKupayOfflineRechargeOrderResponse(JsonResult.DefaultResultCodeEnum.SUCCESS, offlinePayDataBO);
        } catch (BizException e) {
            log.error("同步线下充值订单号至酷狗支付网关，查询异常。request: {}", request, e);
            return RpcResultHelper.buildSyncKupayOfflineRechargeOrderResponse(JsonResult.DefaultResultCodeEnum.FAILURE, null);
        } catch (Exception e) {
            log.error("同步线下充值订单号至酷狗支付网关，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public QueryRechargeOrderResponse queryRechargeOrder(QueryRechargeOrderRequest request) {
        String rechargeOrderNum = request.getRechargeOrderNum();
        log.warn("查询充值成功交易信息，请求参数。request: {}", request);
        try {
            Optional<RechargeAcrossPO> optionalRechargeAcrossPO = this.rechargeOrderService.queryByRechargeOrderNum(rechargeOrderNum);
            if (optionalRechargeAcrossPO.isPresent() && optionalRechargeAcrossPO.get().getStatus() == 1 && optionalRechargeAcrossPO.get().getPayTypeId() == 1006) {
                String transactionId = orderIdService.extractTransactionId(optionalRechargeAcrossPO.get().getRechargeOrderNum())
                        .orElse(null);
                if(transactionId != null) {
                    QueryOrderNumByTransactionIdRequest request1 = new QueryOrderNumByTransactionIdRequest().setTransactionId(transactionId);
                    QueryOrderNumByTransactionIdResponse response = this.appStoreRpcService.queryOrderNumByTransactionId(request1);
                    if (Objects.nonNull(response) && response.getCode() == 0 && StringUtils.isNotBlank(response.getOrderNum())) {
                        String orderNum = response.getOrderNum();
                        return RpcResultHelper.buildQueryRechargeOrderResponse(SysResultCode.SUCCESS, optionalRechargeAcrossPO, orderNum);
                    }
                }
            }
            return RpcResultHelper.buildQueryRechargeOrderResponse(SysResultCode.SUCCESS, optionalRechargeAcrossPO, "");
        } catch (BizException e) {
            log.error("查询充值成功交易信息，查询异常。request: {}", request, e);
            return RpcResultHelper.buildQueryRechargeOrderResponse(SysResultCode.FAILURE, Optional.empty());
        } catch (Exception e) {
            log.error("查询充值成功交易信息，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public QueryRechargeOrderResponse queryRechargeOrderByOrderNum(QueryRechargeOrderByOrderNumRequest request) {
        try {
            log.warn("查询苹果充值成功交易信息，请求参数。request: {}", request);
            QueryTransactionIdByOrderNumRequest request1 = new QueryTransactionIdByOrderNumRequest().setOrderNum(request.getOrderNum());
            QueryTransactionIdByOrderNumResponse response1 = this.appStoreRpcService.queryTransactionIdByOrderNum(request1);
            if (Objects.isNull(response1) || response1.getCode() != 0 || StringUtils.isBlank(response1.getTransactionId())) {
                log.warn("查询苹果充值成功交易信息，无对应transactionId。request: {}, request1: {}, response1: {}", request, request1, response1);
                return RpcResultHelper.buildQueryRechargeOrderResponse(SysResultCode.SUCCESS, Optional.empty());
            }
            QueryRechargeOrderNumByTradeNoRequest request2 = new QueryRechargeOrderNumByTradeNoRequest();
            request2.setAppId(1000_0002);
            request2.setTradeNo("TID".concat(response1.getTransactionId()));
            request2.setSign(FinanceSignUtils.makeSign(request2, "q4Ya9B7MVq5CYBXt"));
            QueryRechargeOrderNumByTradeNoResponse response2 = this.platformPayV2Service.queryRechargeOrderNumByTradeNo(request2);
            if (Objects.isNull(response2) || response2.getCode() != 0 || Objects.isNull(response2.getData())) {
                log.error("查询苹果充值成功交易信息，无对应rechargeOrderNum。request: {}, request2: {}, response2: {}", request, request2, response2);
                return RpcResultHelper.buildQueryRechargeOrderResponse(SysResultCode.SUCCESS, Optional.empty());
            }
            String rechargeOrderNum = response2.getData().getRechargeOrderNum();
            Optional<RechargeAcrossPO> optionalRechargeAcrossPO = this.rechargeOrderService.queryByRechargeOrderNum(rechargeOrderNum);
            return RpcResultHelper.buildQueryRechargeOrderResponse(SysResultCode.SUCCESS, optionalRechargeAcrossPO);
        } catch (BizException e) {
            log.error("查询苹果充值成功交易信息，查询异常。request: {}", request, e);
            return RpcResultHelper.buildQueryRechargeOrderResponse(SysResultCode.FAILURE, Optional.empty());
        } catch (Exception e) {
            log.error("查询苹果充值成功交易信息，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public ReportIapReceiptResponse reportIapReceipt(ReportIapReceiptRequest request) {
        try {
            log.warn("苹果内购上报付费收据接口，请求参数。request: {}", request);
            // 校验签名参数
            String clientSign = StringUtils.defaultString(request.getSign());
            String serverSign = FinanceSignUtils.makeSign(request, apolloConfigService.businessKeyOf(request.getBusinessId()));
            if (!StringUtils.equals(clientSign, serverSign)) {
                log.warn("苹果内购上报付费收据接口，非法签名。request: {}, clientSign: {}, serverSign: {}", request, clientSign, serverSign);
                return RpcResultHelper.buildReportIapReceiptResponse(SysResultCode.RECHARGE_SIGN_INVALID, null);
            }
            ReportIapReceiptDto reportIapReceiptDto = this.appleAppPaymentService.reportIapReceipt(request);
            return RpcResultHelper.buildReportIapReceiptResponse(SysResultCode.SUCCESS, reportIapReceiptDto);
        } catch (BizException e) {
            log.error("苹果内购上报付费收据接口，操作异常。request: {}", request, e);
            return RpcResultHelper.buildReportIapReceiptResponse(SysResultCode.codeOf(e.getCode()), null);
        } catch (Exception e) {
            log.error("苹果内购上报付费收据接口，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public IapNotificationResponse iapNotification(IapNotificationRequest request) {
        try {
            log.warn("苹果内购退款通知接口，请求参数。request: {}", request);
            // 校验签名参数
            String clientSign = StringUtils.defaultString(request.getSign());
            String serverSign = FinanceSignUtils.makeSign(request, apolloConfigService.businessKeyOf(request.getBusinessId()));
            if (!StringUtils.equals(clientSign, serverSign)) {
                log.warn("苹果内购退款通知接口，非法签名。request: {}, clientSign: {}, serverSign: {}", request, clientSign, serverSign);
                return RpcResultHelper.buildIapNotificationResponse(SysResultCode.RECHARGE_SIGN_INVALID);
            }
            SysResultCode sysResultCode = this.appleAppPaymentService.iapNotification(request);
            return RpcResultHelper.buildIapNotificationResponse(sysResultCode);
        } catch (BizException e) {
            log.error("苹果内购退款通知接口，操作异常。request: {}", request, e);
            return RpcResultHelper.buildIapNotificationResponse(SysResultCode.codeOf(e.getCode()));
        } catch (Exception e) {
            log.error("苹果内购退款通知接口，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public QueryBizOrderResponse queryBizOrder(QueryBizOrderRequest request) {
        try {
            // 校验签名参数
            String clientSign = StringUtils.defaultString(request.getSign());
            String serverSign = FinanceSignUtils.makeSign(request, apolloConfigService.businessKeyOf(request.getBusinessId()));
            if (!StringUtils.equals(clientSign, serverSign)) {
                log.warn("查询业务充值订单到账接口，非法签名。request: {}, clientSign: {}, serverSign: {}", request, clientSign, serverSign);
                return RpcResultHelper.buildQueryBizOrderResponse(SysResultCode.RECHARGE_SIGN_INVALID, Optional.empty());
            }
            Optional<RechargeAcrossPO> optionalRechargeAcrossPO = this.rechargeOrderService.queryBizOrder(request);
            return RpcResultHelper.buildQueryBizOrderResponse(SysResultCode.SUCCESS, optionalRechargeAcrossPO);
        } catch (BizException e) {
            log.error("查询业务充值订单到账接口，操作异常。request: {}", request, e);
            return RpcResultHelper.buildQueryBizOrderResponse(SysResultCode.codeOf(e.getCode()), Optional.empty());
        } catch (Exception e) {
            log.error("查询业务充值订单到账接口，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public QueryRechargeListResponse queryRechargeList(QueryRechargeListRequest request) {
        try {
            // 校验签名参数
            String clientSign = StringUtils.defaultString(request.getSign());
            String serverSign = FinanceSignUtils.makeSign(request, apolloConfigService.businessKeyOf(request.getBusinessId()));
            if (!StringUtils.equals(clientSign, serverSign)) {
                log.warn("查询充值成功记录，非法签名。request: {}, clientSign: {}, serverSign: {}", request, clientSign, serverSign);
                return RpcResultHelper.buildQueryRechargeListResponse(SysResultCode.RECHARGE_SIGN_INVALID, Lists.newArrayList());
            }
            long beginTime = request.getBeginTime();
            long endTime = request.getEndTime();
            long lastRechargeId = request.getLastRechargeId();
            int batchSize = request.getBatchSize();
            List<RechargeSuccessInfo> rechargeSuccessInfoList = rechargeOrderService.getRechargeSuccessList(
                    beginTime, endTime, lastRechargeId, batchSize, request.getCoinTypes());
            return RpcResultHelper.buildQueryRechargeListResponse(SysResultCode.SUCCESS, rechargeSuccessInfoList);
        } catch (BizException e) {
            log.error("查询充值成功记录，操作异常。request: {}", request, e);
            return RpcResultHelper.buildQueryRechargeListResponse(SysResultCode.codeOf(e.getCode()), Lists.newArrayList());
        } catch (Exception e) {
            log.error("查询充值成功记录，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

}
