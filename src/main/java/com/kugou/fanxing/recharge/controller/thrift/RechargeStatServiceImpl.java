package com.kugou.fanxing.recharge.controller.thrift;

import com.kugou.fanxing.biz.commons.util.FinanceSignUtils;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.stat.UserEverRechargeStatService;
import com.kugou.fanxing.recharge.thrift.*;
import com.kugou.rpc.exception.ServerException;
import com.kugou.rpc.server.thrift.annotation.ThriftService;
import com.kugou.rpc.server.web.RpcContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@ThriftService("/platform_recharge_service/thrift/rechargeStatService")
public class RechargeStatServiceImpl implements RechargeStatService.Iface {

    @Autowired
    private UserEverRechargeStatService userEverRechargeStatService;
    @Autowired
    private ApolloConfigService apolloConfigService;

    @Override
    @Deprecated
    public UserYearRechargeStatResponse userYearRechargeStat(UserYearRechargeStatRequest request) {
        log.warn("查询用户年度充值金额，请求参数。request: {}, serverName: {}, clientIp: {}",
                request, RpcContextHolder.getClientName(), RpcContextHolder.getClientIp());
        return RpcResultHelper.buildUserYearRechargeStatResponse(SysResultCode.E_10000025, null);
    }

    @Override
    public UserEverRechargeResponse userEverRecharge(UserEverRechargeRequest request) {
        try {
            String clientIp = RpcContextHolder.getClientIp();
            String serverName = RpcContextHolder.getClientName();
            if (apolloConfigService.enableDebugMode()) {
                log.warn("查询用户历史充值总额，请求参数。request: {}, clientIp: {}, serverName: {}", request, clientIp, serverName);
            }
            if (!FinanceSignUtils.checkSign(request, apolloConfigService.appKeyOf(request.getAppId()))) {
                log.warn("查询用户历史充值总额, 签名验证失败, request: {}, clientIp: {}, serverName: {}", request, clientIp, serverName);
                return RpcResultHelper.buildUserEverRechargeResponse(SysResultCode.RECHARGE_SIGN_INVALID, null);
            }
            UserEverRechargeDTO userEverRechargeDTO = userEverRechargeStatService.getUserEverRecharge(request.getKugouId());
            return RpcResultHelper.buildUserEverRechargeResponse(SysResultCode.SUCCESS, userEverRechargeDTO);
        } catch (BizException e) {
            log.error("查询用户历史充值总额，操作异常, request: {}", request, e);
            return RpcResultHelper.buildUserEverRechargeResponse(SysResultCode.RECHARGE_SYS_ERROR, null);
        } catch (Exception e) {
            log.error("查询用户历史充值总额，故障转移, request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }
}
