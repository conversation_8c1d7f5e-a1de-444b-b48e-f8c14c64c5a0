package com.kugou.fanxing.recharge.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.kugou.fanxing.biz.commons.util.GrayTools;
import com.kugou.fanxing.recharge.common.aop.annotation.CdnCache;
import com.kugou.fanxing.recharge.constant.*;
import com.kugou.fanxing.recharge.exception.AckException;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.Payload;
import com.kugou.fanxing.recharge.model.bo.RechargeAwardConfig;
import com.kugou.fanxing.recharge.model.bo.RechargeAwardConfigVo;
import com.kugou.fanxing.recharge.model.bo.RechargeGear;
import com.kugou.fanxing.recharge.model.dto.NewUserCoinDto;
import com.kugou.fanxing.recharge.model.dto.UserCoinDTO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.*;
import com.kugou.fanxing.recharge.model.vo.*;
import com.kugou.fanxing.recharge.pay.thrift.RechargeBusinessOrderDTO;
import com.kugou.fanxing.recharge.service.*;
import com.kugou.fanxing.recharge.service.stat.UserEverRechargeStatService;
import com.kugou.fanxing.recharge.util.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.ConstraintViolation;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
public class RechargeController {

    @Autowired
    private PayService payService;
    @Autowired
    private ValidatingService validatingService;
    @Autowired
    private RechargeOptionService rechargeOptionService;
    @Autowired
    private UserEverRechargeStatService userEverRechargeStatService;
    @Autowired
    private RechargeRebateService rechargeRebateService;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private AgentRechargeService agentRechargeService;
    @Autowired
    private RechargeAwardService rechargeAwardService;

    @ApolloConfig
    private Config config;

    private static final ExecutorService executorService = ThreadUtils.newThreadPool("GetUserCoinV2");

    private static final GrayTools fapiaoGrayTools = GrayTools.module("permitFapiao");
    private static final GrayTools fapiaoH5GrayTools = GrayTools.module("permitFapiaoH5");
    private static final GrayTools thirdGrayTools = GrayTools.module("permitThird");
    /**
     * 唱唱充值客户端切换新接口灰度开关
     */
    private static final GrayTools singMergeGray = GrayTools.module("singMerge");

    @CdnCache(cacheStrategy = CdnCache.CacheStrategy.NO_CACHE)
    @RequestMapping(value = UrlConstants.GET_DF_RECHARGE_CONFIG)
    public JsonResult<JSONArray> getDfPaymentConfig(WebCommonParam webCommonParam) {
        log.warn("RechargeController.getDfPaymentConfig, webCommonParam: {}", webCommonParam);
        boolean hasDisableFansWxRenewal = this.apolloConfigService.hasDisableFansWxRenewal(webCommonParam.getStdPlat());
        JSONObject alipayPay = new JSONObject();
        alipayPay.put("type", 1);
        alipayPay.put("name", "支付宝");
        alipayPay.put("h5PaymentId", "H5AliPay");
        alipayPay.put("pcPaymentId", "PCAliPay");
        alipayPay.put("appPaymentId", "AppAliPay");
        JSONObject wechatPay = new JSONObject();
        wechatPay.put("type", 2);
        wechatPay.put("name", "微信");
        wechatPay.put("h5PaymentId", "H5WxPay");
        wechatPay.put("pcPaymentId", "PCWxPay");
        if (!hasDisableFansWxRenewal) {
            wechatPay.put("appPaymentId", "AppWxPay");
        }
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(alipayPay);
        jsonArray.add(wechatPay);
        return JsonResult.success(jsonArray);
    }

    /**
     * 查询用户充值记录
     *
     * @param webCommonParam 通用参数
     * @param param          业务参数
     */
    @CdnCache(cacheStrategy = CdnCache.CacheStrategy.NO_CACHE)
    @RequestMapping(value = UrlConstants.GET_RECHARGE_LIST)
    public JsonResult<RechargeAcrossListVO> getRechargeList(WebCommonParam webCommonParam, QueryRechargeListReq param) {
        log.warn("RechargeController.getRechargeList, common params: {}, business params: {}", webCommonParam, param);
        try {
            // 请求参数校验
            String message = validatingService.validateParams(param);
            if (StringUtils.isNotEmpty(message)) {
                return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, new RechargeAcrossListVO());
            }
            // 单页至多展示100条数据
            long kugouId = webCommonParam.getKugouId();
            int payTypeId = Integer.parseInt(param.getPayTypeId());
            Pagination pagination = new Pagination.Builder(param.getPage(), Math.min(param.getPageSize(), 100)).build();
            RechargeAcrossListVO rechargeAcrossListVO = payService.getRechargeList(kugouId, param.getIndex(), payTypeId, pagination);
            return JsonResult.success(rechargeAcrossListVO);
        } catch (Exception e) {
            Cat.logError("RechargeController.getRechargeList exception", e);
            log.error("RechargeController.getRechargeList exception, common params: {}, business params: {}", webCommonParam, param, e);
            return JsonResult.success(new RechargeAcrossListVO());
        }
    }

    /**
     * 为他人充值记录
     */
    @CdnCache
    @RequestMapping(value = UrlConstants.GET_RECHARGE_FOR_OTHER_LIST)
    public JsonResult<AgentRechargeAcrossListVO> getRechargeForOtherList(WebCommonParam webCommonParam, QueryRechargeListReq param) {
        log.warn("为他人充值记录，请求参数。webCommonParam: {}, param: {}", webCommonParam, param);
        try {
            String message = validatingService.validateParams(param);
            if (StringUtils.isNotEmpty(message)) {
                log.warn("为他人充值记录，参数错误。webCommonParam: {}, param: {}", webCommonParam, param);
                return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, new AgentRechargeAcrossListVO());
            }
            long kugouId = webCommonParam.getKugouId();
            Pagination pagination = new Pagination.Builder(param.getPage(), Math.min(param.getPageSize(), 100)).build();
            AgentRechargeAcrossListVO agentRechargeAcrossListVO = payService.getRechargeForOtherList(kugouId, param.getIndex(), pagination);
            return JsonResult.result(SysResultCode.SUCCESS, agentRechargeAcrossListVO);
        } catch (Exception e) {
            log.warn("为他人充值记录，查询异常。webCommonParam: {}, param: {}", webCommonParam, param, e);
            return JsonResult.result(SysResultCode.SUCCESS, new AgentRechargeAcrossListVO());
        }
    }

    /**
     * 他人为我充值记录
     */
    @CdnCache
    @RequestMapping(value = UrlConstants.GET_RECHARGE_OTHER_FOR_ME_LIST)
    public JsonResult<AgentRechargeAcrossListVO> getRechargeOtherForMeList(WebCommonParam webCommonParam, QueryRechargeListReq param) {
        log.warn("他人为我充值记录，请求参数。webCommonParam: {}, param: {}", webCommonParam, param);
        try {
            String message = validatingService.validateParams(param);
            if (StringUtils.isNotEmpty(message)) {
                log.warn("他人为我充值记录，参数错误。webCommonParam: {}, param: {}", webCommonParam, param);
                return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, new AgentRechargeAcrossListVO());
            }
            long kugouId = webCommonParam.getKugouId();
            Pagination pagination = new Pagination.Builder(param.getPage(), Math.min(param.getPageSize(), 100)).build();
            AgentRechargeAcrossListVO agentRechargeAcrossListVO = payService.getRechargeOtherForMeList(kugouId, param.getIndex(), pagination);
            return JsonResult.result(SysResultCode.SUCCESS, agentRechargeAcrossListVO);
        } catch (Exception e) {
            log.warn("他人为我充值记录，查询异常。webCommonParam: {}, param: {}", webCommonParam, param, e);
            return JsonResult.result(SysResultCode.SUCCESS, new AgentRechargeAcrossListVO());
        }
    }
    
    @RequestMapping(value = UrlConstants.GET_GAME_RECHARGE_LIST)
    public JsonResult<RechargeAcrossListVO> getGameRechargeList(WebCommonParam webCommonParam, QueryRechargeListReq param) {
        log.warn("获取游戏充值列表信息，请求参数。common params: {}, business params: {}", webCommonParam, param);
        try {
            // 请求参数校验
            String message = validatingService.validateParams(param);
            if (StringUtils.isNotEmpty(message)) {
                return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, new RechargeAcrossListVO());
            }
            // 单页至多展示100条数据
            long kugouId = webCommonParam.getKugouId();
            int payTypeId = Integer.parseInt(param.getPayTypeId());
            Pagination pagination = new Pagination.Builder(param.getPage(), Math.min(param.getPageSize(), 100)).build();
            RechargeAcrossListVO rechargeAcrossListVO = payService.getGameRechargeList(kugouId, param.getIndex(), payTypeId, pagination);
            return JsonResult.success(rechargeAcrossListVO);
        } catch (Exception e) {
            Cat.logError("获取游戏充值列表信息 exception", e);
            log.error("获取游戏充值列表信息, common params: {}, business params: {}", webCommonParam, param, e);
            return JsonResult.success(new RechargeAcrossListVO());
        }
    }

    /**
     * 获取默认充值配置列表
     * @deprecated Use the UrlConstants.DEFAULT_RECHARGE_OPTIONS_BY_PID instead.
     */
    @Deprecated
    @GetMapping(value = UrlConstants.DEFAULT_RECHARGE_OPTIONS)
    public JsonResult<List<RechargeOptionItem>> defaultRechargeOptions(@RequestParam(value = "kugouId",required = false) Long kugouId,
                                                                       @RequestParam(value = "source",required = false, defaultValue = "0") int source) {
        try {
            return JsonResult.success(rechargeOptionService.getDefaultListByPid(null, null, kugouId, source, Optional.empty()));
        } catch (Exception e) {
            Cat.logError("RechargeController.defaultRechargeOptions exception", e);
            log.error("RechargeController.defaultRechargeOptions exception", e);
            return JsonResult.success(null);
        }
    }

    /**
     * 获取默认充值配置列表--支持根据pid用不同的配置
     */
    @GetMapping(value = UrlConstants.DEFAULT_RECHARGE_OPTIONS_BY_PID)
    public JsonResult<List<RechargeOptionItem>> defaultRechargeOptionsByPid(WebCommonParam webCommonParam, RechargeOptionsRequest request) {
        try {
            PayTypeIdEnum payTypeIdEnum = PayTypeIdEnum.of(request.getPayTypeId()).orElse(null);
            Optional<String> optionalActFlag = Optional.ofNullable(request.getActFlag());
            List<RechargeOptionItem> rechargeOptionItemList = rechargeOptionService.getDefaultListByPid(payTypeIdEnum,
                    webCommonParam.getPid(), webCommonParam.getKugouId(), request.getSource(), optionalActFlag);
            return JsonResult.success(rechargeOptionItemList);
        } catch (Exception e) {
            Cat.logError("RechargeController.defaultRechargeOptionsByPid exception", e);
            log.error("RechargeController.defaultRechargeOptionsByPid exception", e);
            return JsonResult.success(null);
        }
    }

    /**
     * 获取充值金额档位
     */
    @GetMapping(value = UrlConstants.GET_RECHARGE_AMOUNT_GEAR)
    public JsonResult<List<RechargeAmountGear>> getRechargeAmountGear(WebCommonParam webCommonParam) {
        List<RechargeAmountGear> gearList = rechargeOptionService.getRechargeAmountGear(webCommonParam.getStdPlat());
        return JsonResult.success(gearList);
    }

    /**
     * 获取充值金额赠品
     */
    @CdnCache(cacheStrategy = CdnCache.CacheStrategy.NO_CACHE)
    @GetMapping(value = UrlConstants.GET_RECHARGE_PRESENT_LIST)
    public JsonResult<RechargeAmountPresent> getRechargePresentList(WebCommonParam webCommonParam) {
        RechargeAmountPresent rechargeAmountPresent = rechargeOptionService.getRechargePresentList(webCommonParam.getStdPlat());
        return JsonResult.success(rechargeAmountPresent);
    }

    /**
     * 查询自定义充值金额的赠品信息
     */
    @CdnCache(cacheStrategy = CdnCache.CacheStrategy.NO_CACHE)
    @GetMapping(value = UrlConstants.RECHARGE_PRESENT_QUERY)
    public JsonResult<RechargeOptionItem> rechargePresentQuery(WebCommonParam webCommonParam, RechargePresentQueryReq param) {
        log.warn("RechargeController.rechargePresentQuery, common params: {}, business params: {}", webCommonParam, param);
        try {
            // 请求参数校验
            String message = validatingService.validateParams(param);
            if (StringUtils.isNotEmpty(message)) {
                return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, new RechargeOptionItem());
            }
            return JsonResult.success(rechargeOptionService.getBySpecialNum(param.getMoney(), webCommonParam.getKugouId(), param.getCoinType()));
        } catch (Exception e) {
            Cat.logError("RechargeController.rechargePresentQuery exception", e);
            log.error("RechargeController.rechargePresentQuery exception, common params: {}, business params: {}", webCommonParam, param, e);
            return JsonResult.success(new RechargeOptionItem());
        }
    }

    /**
     * <pre>
     * 查询指定订单的完成状态
     * status!=3：上报E201000002
     * </pre>
     *
     * @param webCommonParam 通用参数
     * @param param          业务参数
     */
    @PostMapping(value = UrlConstants.RECHARGE_ORDER_STATUS)
    public JsonResult<RechargeOrderStatusItem> getOrderStatus(WebCommonParam webCommonParam, OrderStatusRequest param) {
        log.warn("查询指定订单的完成状态。common params: {}, business params: {}", webCommonParam, param);
        RechargeOrderStatusItem rechargeOrderStatusItem = new RechargeOrderStatusItem();
        rechargeOrderStatusItem.setCoin(0).setRechargeOrderNum(param.getRechargeOrderNum()).setStatus(1);
        try {
            // 请求参数校验
            String message = validatingService.validateParams(param);
            if (StringUtils.isNotEmpty(message)) {
                return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, rechargeOrderStatusItem);
            }
            // 获取当前业务线
            BizLineEnum bizLineEnum = BizLineEnum.of(param.getBizLine());
            RechargeAcrossPO rechargeAcrossPO = payService.getSuccessRechargeOrder(bizLineEnum, param.getRechargeOrderNum());
            if (rechargeAcrossPO == null) {
                return JsonResult.success(rechargeOrderStatusItem);
            }
            rechargeOrderStatusItem.setCoin(rechargeAcrossPO.getCoin().intValue());
            rechargeOrderStatusItem.setStatus(2);
            if (!ReTypeEnum.isPurchase(rechargeAcrossPO.getReType())) {
                rechargeOrderStatusItem.setStatus(3);
                return JsonResult.success(rechargeOrderStatusItem);
            }
            RechargeBusinessOrderDTO rechargeBusinessOrderDTO = payService.getRechargePayOrder(rechargeAcrossPO);
            if (rechargeBusinessOrderDTO != null) {
                rechargeOrderStatusItem.setStatus(3);
            }
            return JsonResult.success(rechargeOrderStatusItem);
        } catch (Exception e) {
            Cat.logError("查询指定订单的完成状态。exception", e);
            log.error("查询指定订单的完成状态。exception, common params: {}, business params: {}", webCommonParam, param, e);
            return JsonResult.success(rechargeOrderStatusItem);
        }
    }

    @PostMapping(value = UrlConstants.RECHARGE_ORDER_STATUS_V2)
    public JsonResult<RechargeOrderInfoVO> getOrderStatusV2(WebCommonParam webCommonParam, OrderStatusRequest param) {
        log.warn("查询充值订单完成状态V2。webCommonParam: {}, param: {}", webCommonParam, param);
        // 构建响应结构
        RechargeOrderInfoVO rechargeOrderInfoVO = new RechargeOrderInfoVO()
                .setRechargeOrderNum(param.getRechargeOrderNum())
                .setStatus(RechargeOrderStatusEnum.NOT_PAY.getStatus())
                .setCoin(BigDecimal.ZERO.stripTrailingZeros().toPlainString());
        // 请求参数校验
        String message = validatingService.validateParams(param);
        if (StringUtils.isNotEmpty(message)) {
            log.error("查询充值订单完成状态V2，参数校验失败。webCommonParam: {}, param: {}", webCommonParam, param);
            return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, rechargeOrderInfoVO);
        }
        // 获取当前业务线
        RechargeAcrossPO rechargeAcrossPO = payService.getSuccessRechargeOrder(BizLineEnum.of(param.getBizLine()), param.getRechargeOrderNum());
        if (rechargeAcrossPO == null) {
            log.warn("查询充值订单完成状态V2，订单尚未支付。webCommonParam: {}, param: {}", webCommonParam, param);
            return JsonResult.result(SysResultCode.SUCCESS, rechargeOrderInfoVO);
        }
        rechargeOrderInfoVO.setCoin(rechargeAcrossPO.getCoin().stripTrailingZeros().toPlainString());
        rechargeOrderInfoVO.setStatus(RechargeOrderStatusEnum.HAS_PAY.getStatus());
        // 货币类默认发货
        if (ReTypeEnum.isRecharge(rechargeAcrossPO.getReType())) {
            rechargeOrderInfoVO.setStatus(RechargeOrderStatusEnum.SERVED.getStatus());
            log.warn("查询充值订单完成状态V2，货币类已发货。webCommonParam: {}, param: {}", webCommonParam, param);
            return JsonResult.result(SysResultCode.SUCCESS, rechargeOrderInfoVO);
        }
        // 商品类查询发货
        RechargeBusinessOrderDTO rechargeBusinessOrderDTO = payService.getRechargePayOrder(rechargeAcrossPO);
        if (rechargeBusinessOrderDTO != null) {
            log.warn("查询充值订单完成状态V2，商品类已发货。webCommonParam: {}, param: {}", webCommonParam, param);
            rechargeOrderInfoVO.setStatus(RechargeOrderStatusEnum.SERVED.getStatus());
        }
        log.warn("查询充值订单完成状态V2，商品类未发货。webCommonParam: {}, param: {}", webCommonParam, param);
        return JsonResult.result(SysResultCode.SUCCESS, rechargeOrderInfoVO);
    }

    /**
     * 获取安卓手机端充值渠道列表
     *
     * @param webCommonParam     通用参数
     * @param androidCommonParam 参数
     */
    @RequestMapping(value = UrlConstants.RECHARGE_GET_MPAY_LIST)
    public JsonResult<Map<String, Object>> getMPayList(WebCommonParam webCommonParam, AndroidCommonParam androidCommonParam) {
        long kugouId = webCommonParam.getKugouId();
        log.warn("获取手机端充值渠道列表, kugouId: {}, webCommonParam: {}, androidCommonParam: {}",
                kugouId, webCommonParam, androidCommonParam);
        List<PayTypeItem> payTypeList = this.payService.getMPayList(kugouId, androidCommonParam);
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("payTypeList", payTypeList);
        dataMap.put("serverTime", DateHelper.getCurrentSeconds());
        return JsonResult.success(dataMap);
    }

    @RequestMapping(value = UrlConstants.GET_USER_COIN_FROM_MASTER)
    public JsonResult<UserCoinDTO> getUserCoin(WebCommonParam commonParam, GetUserCoinRequest request) {
        log.warn("获取用户星币余额, commonParam: {}, request: {}", commonParam, request);
        Optional<UserEntity> optionalUserEntity = userEverRechargeStatService.getUserEntity(
                commonParam.getKugouId(), 10020, request.getCoinType());
        if (!optionalUserEntity.isPresent()) {
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        UserCoinDTO userCoinDTO = new UserCoinDTO();
        userCoinDTO.setCoin((int) optionalUserEntity.get().getCoin());
        return JsonResult.success(userCoinDTO);
    }

    @SneakyThrows
    @RequestMapping(value = UrlConstants.GET_USER_COIN_FROM_V2)
    public JsonResult<NewUserCoinDto> getUserCoinV2(WebCommonParam commonParam) {
        CompletableFuture<Optional<UserEntity>> starCoinFuture = CompletableFuture.supplyAsync(() ->
                userEverRechargeStatService.getUserEntity(commonParam.getKugouId(), 10020, CoinTypeEnum.STAR_COIN.getCoinType()), executorService);
        CompletableFuture<Optional<UserEntity>> singCoinFuture = CompletableFuture.supplyAsync(() ->
                userEverRechargeStatService.getUserEntity(commonParam.getKugouId(), 10020, CoinTypeEnum.SING_COIN.getCoinType()), executorService);
        CompletableFuture<Void> allOf = CompletableFuture.allOf(starCoinFuture, singCoinFuture);
        allOf.join();
        Optional<UserEntity> optionalStarUser = starCoinFuture.get();
        Optional<UserEntity> optionalSingUser = singCoinFuture.get();
        if (!optionalStarUser.isPresent() || !optionalSingUser.isPresent()) {
            throw new AckException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        String starCoin = BigDecimal.valueOf(optionalStarUser.get().getCoin()).stripTrailingZeros().toPlainString();
        NewUserCoinDto userCoinDto = NewUserCoinDto.builder()
                .coin(starCoin)
                .starCoin(starCoin)
                .singCoin(BigDecimal.valueOf(optionalSingUser.get().getCoin()).stripTrailingZeros().toPlainString())
                .build();
        return JsonResult.success(userCoinDto);
    }

    /**
     * 查询充值返点信息
     *
     * @param webCommonParam 通用参数
     */
    @RequestMapping(value = UrlConstants.GET_RECHARGE_REBATE)
    public JsonResult<List<RechargeRebateVO>> getRechargeRebate(WebCommonParam webCommonParam) {
        return JsonResult.result(SysResultCode.SUCCESS, this.rechargeRebateService.getRechargeRebateInfo());
    }

    /**
     * 充值相关配置
     *
     * @param webCommonParam 通用参数
     * @return 响应JSON对象
     */
    @CdnCache(cacheStrategy = CdnCache.CacheStrategy.NO_CACHE)
    @RequestMapping(value = {UrlConstants.GET_RECHARGE_SWITCH_LIST, UrlConstants.GET_RECHARGE_CONFIG_LIST, UrlConstants.PRE_ORDERS_LIST})
    public JsonResult<Map<String, Object>> getRechargeSwitch(WebCommonParam webCommonParam, @RequestParam(required = false, defaultValue = "false") Boolean isH5) {
        Map<String, Object> payload = Maps.newHashMap();
        payload.put("permitFapiao", isH5 ? fapiaoH5GrayTools.isGray(webCommonParam.getKugouId()) : fapiaoGrayTools.isGray(webCommonParam.getKugouId()));
        payload.put("fapiaoSwitch", apolloConfigService.getFapiaoMaintenanceSwitch());
        payload.put("fapiaoToasts", apolloConfigService.getFapiaoMaintenanceToasts());
        payload.put("enabledPoor", thirdGrayTools.isGray(webCommonParam.getKugouId()));
        payload.put("poorPageUrl", apolloConfigService.getThirdPartyPaymentUrl());
        payload.put("thirdFullPageUrl", apolloConfigService.getThirdPartyFullPaymentUrl());
        payload.put("thirdHalfPageUrl", apolloConfigService.getThirdPartyHalfPaymentUrl());
        payload.put("enableAgent", agentRechargeService.enableAgentRecharge(webCommonParam.getKugouId()));
        payload.put("serverTimes", System.currentTimeMillis());
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

    @PostMapping(value = UrlConstants.USER_RECHARGE_STATE)
    public JsonResult<Map<String, Object>> userRechargeState(WebCommonParam webCommonParam) {
        Map<String, Object> payload = Maps.newHashMap();
        payload.put("activityUrl", this.apolloConfigService.getFirstRechargeJump());
        payload.put("hasGiftBag", 0);
        payload.put("hasRecharge", this.userEverRechargeStatService.hasUserEverRecharge(webCommonParam.getKugouId()) ? 1 : 0);
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

    /**
     * 获取被充值用户信息
     */
    @CdnCache(cacheStrategy = CdnCache.CacheStrategy.NO_CACHE)
    @GetMapping(value = UrlConstants.GET_RECHARGED_USER_INFO)
    public JsonResult<AgentRechargeUserInfoVO> getRechargeUserInfo(WebCommonParam webCommonParam, GetRechargeUserInfoRequest request) {
        AgentRechargeUserInfoVO agentRechargeUserInfoVO = agentRechargeService.getUserInfo(webCommonParam, request);
        return JsonResult.result(SysResultCode.SUCCESS, agentRechargeUserInfoVO);
    }

    @CdnCache(cacheStrategy = CdnCache.CacheStrategy.NO_CACHE)
    @GetMapping(value = UrlConstants.RECHARGE_AWARD_CONFIG)
    public JsonResult<Payload> rechargeAwardConfig(WebCommonParam commonParam, RechargeAwardConfigRequest request) {
        Payload payload = Payload.Builder.map("canParticipate", false).map("rechargeBagConfig", null).build();
        Optional<ConstraintViolation<RechargeAwardConfigRequest>> optionalViolation = this.validatingService.checkViolation(request);
        if (optionalViolation.isPresent() || commonParam.getStdPlat() < 1) {
            log.warn("查询充值活动参与资格，请求参数非法。commonParam: {}, request: {}, optionalViolation: {}, ", commonParam, request, optionalViolation);
            throw new BizException(SysResultCode.RECHARGE_PARAM_ERROR);
        }
        Optional<RechargeAwardConfig> optionalRechargeBagConfig = this.apolloConfigService.getRechargeAwardConfig();
        if (!optionalRechargeBagConfig.isPresent()) {
            return JsonResult.result(SysResultCode.SUCCESS, payload);
        }
        RechargeAwardConfig rechargeAwardConfig = optionalRechargeBagConfig.get();
        RechargeAwardConfigVo rechargeAwardConfigVo = ModelUtils.fromUnchecked(rechargeAwardConfig, RechargeAwardConfigVo.class);
        boolean canParticipate = this.rechargeAwardService.canParticipate(commonParam, request, rechargeAwardConfig);
        payload.put("canParticipate", canParticipate);
        payload.put("rechargeBagConfig", rechargeAwardConfigVo);
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

    @CdnCache(cacheStrategy = CdnCache.CacheStrategy.NO_CACHE)
    @GetMapping(value = UrlConstants.QUERY_RECHARGE_AWARDS)
    public JsonResult<QueryRechargeAwardsVo> queryRechargeAwards(WebCommonParam commonParam, RechargeAwardRequest request) {
        QueryRechargeAwardsVo queryRechargeAwardsVo = QueryRechargeAwardsVo.builder().rechargeOrderNum(request.getRechargeOrderNum())
                .hasRechargeAward(false).rechargeBagConfig(null).prizePageUrl("").build();
        Optional<ConstraintViolation<RechargeAwardRequest>> optionalViolation = this.validatingService.checkViolation(request);
        if (optionalViolation.isPresent() || commonParam.getStdPlat() < 1) {
            log.warn("查询充值活动奖励信息，请求参数非法。commonParam: {}, request: {}, optionalViolation: {}, ", commonParam, request, optionalViolation);
            throw new BizException(SysResultCode.RECHARGE_PARAM_ERROR);
        }
        Optional<RechargeAwardConfig> optionalRechargeBagConfig = this.apolloConfigService.getRechargeAwardConfig();
        if (!optionalRechargeBagConfig.isPresent()) {
            return JsonResult.result(SysResultCode.SUCCESS, queryRechargeAwardsVo);
        }
        RechargeAwardConfig rechargeAwardConfig = optionalRechargeBagConfig.get();
        Optional<RechargeGear> optionalRechargeGear = this.rechargeAwardService.queryRechargeAward(commonParam, request, rechargeAwardConfig);
        RechargeGear rechargeGear = optionalRechargeGear.orElse(null);
        queryRechargeAwardsVo.setHasRechargeAward(optionalRechargeGear.isPresent());
        queryRechargeAwardsVo.setRechargeBagConfig(rechargeGear);
        queryRechargeAwardsVo.setPrizePageUrl(rechargeAwardConfig.getPopupConfig().getPrizePageUrl());
        return JsonResult.result(SysResultCode.SUCCESS, queryRechargeAwardsVo);
    }

    @CdnCache(cacheStrategy = CdnCache.CacheStrategy.NO_CACHE)
    @GetMapping(value = UrlConstants.RECHARGE_SING_MERGE)
    public JsonResult<SingMergeVo> singMerge(WebCommonParam commonParam) {
        boolean useNew = singMergeGray.isGray(commonParam.getKugouId());
        SingMergeVo singMergeVo = SingMergeVo.builder().useNew(useNew).build();
        return JsonResult.result(SysResultCode.SUCCESS, singMergeVo);
    }
}
