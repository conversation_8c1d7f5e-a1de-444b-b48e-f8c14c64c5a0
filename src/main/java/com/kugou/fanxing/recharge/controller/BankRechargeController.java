package com.kugou.fanxing.recharge.controller;

import com.google.common.collect.Maps;
import com.kugou.config.Env;
import com.kugou.fanxing.recharge.common.aop.annotation.PoorMode;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.model.request.BankMobileRequest;
import com.kugou.fanxing.recharge.model.request.BankRequest;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.BankRechargeService;
import com.kugou.fanxing.recharge.service.RechargeCommonService;
import com.kugou.fanxing.recharge.service.ValidatingService;
import com.kugou.fanxing.recharge.util.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 银行卡
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class BankRechargeController {

    @Autowired
    private Env env;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private RechargeConfig rechargeConfig;
    @Autowired
    private RechargeCommonService rechargeCommonService;
    @Autowired
    private ValidatingService validatingService;
    @Autowired
    private BankRechargeService bankRechargeService;

    /**
     * 网页银行卡充值下单接口
     */
    @PoorMode
    @PostMapping(value = UrlConstants.GET_ORDER_FOR_BANK_V2)
    public JsonResult<Map<String, Object>> getOrderForBankV2(WebCommonParam webCommonParam, BankRequest request) {
        log.warn("网页银行卡充值下单接口V2, webCommonParam: {}, bankRequest: {}", webCommonParam, request);
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_24, webCommonParam);
        Map<String, Object> dataMap = this.bankRechargeService.getOrderForBankV2(webCommonParam, request);
        return JsonResult.result(SysResultCode.SUCCESS, dataMap);
    }

    /**
     * 网页银行卡充值下单接口
     */
    @PostMapping(value = UrlConstants.GET_ORDER_FOR_BANK)
    public JsonResult<Map<String, Object>> getOrderForBank(WebCommonParam webCommonParam, BankRequest request) {
        log.warn("网页银行卡充值下单接口, webCommonParam: {}, bankRequest: {}", webCommonParam, request);
        if ((env.isDev() || env.isTest()) && apolloConfigService.getRechargePoorMode()) {
            request.setAmount(new BigDecimal("0.02"));
        }
        // 初始化请求体
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_24, webCommonParam);
        // 请求参数校验
        String message = validatingService.validateParams(request);
        if (StringUtils.isNotEmpty(message)) {
            log.warn("网页银行卡充值下单接口，请求参数不合法。webCommonParam: {}, bankRequest: {}", webCommonParam, request);
            return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, Maps.newHashMap());
        }
        // 检查充值银行
        if (!rechargeConfig.existsAlipayBankId(request.getAlipayBankId())) {
            log.warn("网页银行卡充值下单接口，银行ID暂不支持。webCommonParam: {}, bankRequest: {}", webCommonParam, request);
            return JsonResult.failure(SysResultCode.RECHARGE_CHOOSE_OTHER_BANK, JsonResult.ResultCode::getMsg, Maps.newHashMap());
        }
        // 校验充值渠道
        if (!this.rechargeConfig.isRechargeWithChange(request.getPayTypeIdEnum().getPayTypeId())) {
            log.warn("网页银行卡充值下单接口，充值渠道尚未配置。webCommonParam: {}, bankRequest: {}", webCommonParam, request);
            return JsonResult.failure(SysResultCode.RECHARGE_PAY_TYPE_NOT_SUPPORT, JsonResult.ResultCode::getMsg, Maps.newHashMap());
        }
        if (this.rechargeCommonService.isForbiddenPayType(request.getPayTypeIdEnum())) {
            log.warn("网页银行卡充值下单接口，充值渠道暂停服务。webCommonParam: {}, bankRequest: {}", webCommonParam, request);
            return JsonResult.failure(SysResultCode.RECHARGE_PAUSE_SERVICE, JsonResult.ResultCode::getMsg, Maps.newHashMap());
        }
        Map<String, Object> dataMap = this.bankRechargeService.getOrderForBank(webCommonParam, request);
        return JsonResult.success(SysResultCode.SUCCESS, "", dataMap);
    }

    @RequestMapping(value = UrlConstants.RECHARGE_PLAT_BANKPAY_MOBILE)
    public Map<String, Object> bankPayM(WebCommonParam webCommonParam, BankMobileRequest request) {
        log.warn("旧手机银行卡充值下单接口, webCommonParam: {}, alipayRequest: {}", webCommonParam, request);
        if ((env.isDev() || env.isTest()) && apolloConfigService.getRechargePoorMode()) {
            request.setAmount(new BigDecimal("0.02"));
        }
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_35, webCommonParam);
        return this.bankRechargeService.getBankMobile(webCommonParam, request);
    }

    @RequestMapping(value = UrlConstants.GET_ORDER_FOR_BANKPAY_MOBILE)
    public Map<String, Object> bankPayM2(WebCommonParam webCommonParam, BankMobileRequest request) {
        log.warn("新手机银行卡充值下单V1接口, webCommonParam: {}, alipayRequest: {}", webCommonParam, request);
        if ((env.isDev() || env.isTest()) && apolloConfigService.getRechargePoorMode()) {
            request.setAmount(new BigDecimal("0.02"));
        }
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_35, webCommonParam);
        return this.bankRechargeService.getBankMobile(webCommonParam, request);
    }

    @RequestMapping(value = UrlConstants.GET_ORDER_FOR_BANKPAY_MOBILE_V2)
    public JsonResult<Map<String, Object>> bankPayMV2(WebCommonParam webCommonParam, BankMobileRequest request) {
        log.warn("新手机银行卡充值下单V2接口, webCommonParam: {}, alipayRequest: {}", webCommonParam, request);
        if ((env.isDev() || env.isTest()) && apolloConfigService.getRechargePoorMode()) {
            request.setAmount(new BigDecimal("0.02"));
        }
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_35, webCommonParam);
        return JsonResult.result(SysResultCode.SUCCESS, this.bankRechargeService.getBankMobile(webCommonParam, request));
    }
}
