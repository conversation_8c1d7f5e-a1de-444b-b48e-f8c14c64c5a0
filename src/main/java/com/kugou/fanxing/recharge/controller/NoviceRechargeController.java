package com.kugou.fanxing.recharge.controller;

import com.dianping.cat.Cat;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.model.vo.NoviceRechargeBaseVO;
import com.kugou.fanxing.recharge.model.vo.NoviceRechargeVO;
import com.kugou.fanxing.recharge.service.NoviceRechargeBusService;
import com.kugou.fanxing.recharge.util.PhpResult;
import com.kugou.fanxing.recharge.util.PhpResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@CrossOrigin
public class NoviceRechargeController {
    @Autowired
    private NoviceRechargeBusService noviceRechargeBusService;

    @RequestMapping(value = UrlConstants.NOVICE_RECHARGE_CONFIG_NEW)
    public PhpResult<NoviceRechargeVO> getNoviceRechargeConfigNew(WebCommonParam webCommonParam) {
        log.warn("NoviceRechargeController.getRechargeList, common params: {}", webCommonParam);
        try {
            return PhpResultUtils.createSucess(noviceRechargeBusService.getNoviceRechargeConfigNew(webCommonParam.getKugouId()));
        } catch (Exception e) {
            Cat.logError("NoviceRechargeController.getNoviceRechargeConfigNew exception", e);
            log.error("NoviceRechargeController.getNoviceRechargeConfigNew exception, common params: {}", webCommonParam, e);
            return PhpResultUtils.createFail();
        }
    }

}
