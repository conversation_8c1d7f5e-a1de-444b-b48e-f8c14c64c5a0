package com.kugou.fanxing.recharge.controller.callback;

import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.dto.CoinCallbackDTO;
import com.kugou.fanxing.recharge.service.callback.DefaultCallbackService;
import com.kugou.fanxing.recharge.util.CallbackRespHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 直播充值回调
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class LiveRechargeCallbackController {

    @Autowired
    private DefaultCallbackService defaultCallbackService;

    /**
     * 充值购买星币回调（注意⚠️：重构PHP旧有接口）
     */
    @RequestMapping(UrlConstants.RECHARGE_CALLBACK_COIN)
    public String callBackAcross(CoinCallbackDTO coinCallbackDTO) {
        log.warn("充值购买货币回调，请求参数。coinCallbackDTO: {}", coinCallbackDTO);
        SysResultCode sysResultCode = this.defaultCallbackService.purchaseCurrencyCallback(coinCallbackDTO);
        return CallbackRespHelper.buildResp(sysResultCode);
    }

    /**
     * 充值购买商品回调（注意⚠️：重构PHP旧有接口
     */
    @RequestMapping(UrlConstants.RECHARGE_CALLBACK_GOODS)
    public String callBackGoods(CoinCallbackDTO coinCallbackDTO) {
        log.warn("充值购买商品回调，请求参数。coinCallbackDTO: {}", coinCallbackDTO);
        SysResultCode sysResultCode = this.defaultCallbackService.callBackGoods(coinCallbackDTO);
        if (!sysResultCode.isSuccess()) {
            log.warn("充值购买商品回调，处理失败。coinCallbackDTO: {}", coinCallbackDTO);
            throw new BizException(SysResultCode.RECHARGE_SYS_ERROR);
        }
        return "success";
    }

}
