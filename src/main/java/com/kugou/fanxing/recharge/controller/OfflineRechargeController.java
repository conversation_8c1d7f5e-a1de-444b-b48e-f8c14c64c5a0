package com.kugou.fanxing.recharge.controller;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.ApplyTypeEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.request.OfflineRecheckRequest;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.model.response.KupayCommonResponse;
import com.kugou.fanxing.recharge.model.vo.offline.ApplyAlipayVO;
import com.kugou.fanxing.recharge.model.vo.offline.ApplyBankVO;
import com.kugou.fanxing.recharge.model.vo.offline.ApplyInfoDetailVO;
import com.kugou.fanxing.recharge.model.vo.offline.ApplyRebateVO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.ValidatingService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.service.offline.BigRebateService;
import com.kugou.fanxing.recharge.service.offline.BigRechargeService;
import com.kugou.fanxing.recharge.util.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 线下充值
 * <p>
 * 1.支付宝对公转账
 * 2.银行卡对公转账
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class OfflineRechargeController {

    /**
     * 大额充值返点展示开关：true/false
     */
    public static final String BIG_RECHARGE_REBATE_FRONT_SWITCH = "platform.pay.bigRecharge.rebate.front.switch";

    /**
     * 大额充值加币展示开关：true/false
     */
    public static final String BIG_RECHARGE_RECHARGE_FRONT_SWITCH = "platform.pay.bigRecharge.recharge.front.switch";

    @Autowired
    private ValidatingService validatingService;
    @Autowired
    private BigRechargeService bigRechargeService;
    @Autowired
    private BigRebateService bigRebateService;
    @Autowired
    private UserFacadeService userFacadeService;
    @Autowired
    private ApolloConfigService apolloConfigService;

    /**
     * 线下充值 - 银行卡对公转账
     */
    @PostMapping(value = UrlConstants.OFFLINE_RECHARGE_APPLY_BANK)
    public JsonResult<Map<String, Object>> applyBank(WebCommonParam webCommonParam, ApplyBankVO applyBankVO) {
        log.warn("银行卡对公转账，请求参数。webCommonParam: {}, applyBankVO: {}", webCommonParam, applyBankVO);
        try {
            // 开关控制
            boolean rechargeSwitch = ConfigService.getAppConfig().getBooleanProperty("platform.pay.bigRecharge.recharge.switch", true);
            if (!rechargeSwitch) {
                return JsonResult.result(SysResultCode.E_40000020, JsonResult.ResultCode::getMsg, Maps.newHashMap());
            }
            // 参数校验
            String message = validatingService.validateParams(applyBankVO);
            if (StringUtils.isNoneBlank(message)) {
                return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, Maps.newHashMap());
            }
            // 兼容酷我
            List<Long> toKuwoIdList = applyBankVO.getToKuwoIdList();
            if (CollectionUtils.isNotEmpty(toKuwoIdList) && this.apolloConfigService.isKuwoEnv()) {
                List<Long> toKugouIdList = this.userFacadeService.getKugouIdsByKuwoIds(toKuwoIdList);
                String toKugouIds = toKugouIdList.stream().map(String::valueOf).reduce((s1, s2) -> StringUtils.join(s1, ",", s2))
                        .orElseThrow(() -> new BizException(SysResultCode.RECHARGE_ERROR_KUWO_ID));
                log.warn("银行卡对公转账，兼容酷我。toKuwoIdList: {}, toKugouIdList: {}", toKuwoIdList, toKugouIdList);
                applyBankVO.setToKugouIds(toKugouIds);
            }
            SysResultCode resultCode = this.bigRechargeService.applyBank(webCommonParam, applyBankVO);
            return JsonResult.result(resultCode, Maps.newHashMap());
        } catch (Exception e) {
            log.error("银行卡对公转账，申请异常。webCommonParam: {}, applyBankVO: {}", webCommonParam, applyBankVO, e);
            return JsonResult.result(SysResultCode.E_40000210, Maps.newHashMap());
        }
    }

    /**
     * 线下充值 - 支付宝对公转账
     */
    @PostMapping(value = UrlConstants.OFFLINE_RECHARGE_APPLY_ALIPAY)
    public JsonResult<Map<String, Object>> applyAlipay(WebCommonParam webCommonParam, ApplyAlipayVO rechargeApplyVO) {
        log.warn("支付宝对公转账，请求参数。webCommonParam: {}, rechargeApplyVO: {}", webCommonParam, rechargeApplyVO);
        try {
            // 开关控制
            boolean rechargeSwitch = ConfigService.getAppConfig().getBooleanProperty("platform.pay.bigRecharge.recharge.switch", true);
            if (!rechargeSwitch) {
                return JsonResult.result(SysResultCode.E_40000020, JsonResult.ResultCode::getMsg, Maps.newHashMap());
            }
            // 参数校验
            String message = validatingService.validateParams(rechargeApplyVO);
            if (StringUtils.isNotEmpty(message)) {
                return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, Maps.newHashMap());
            }
            // 兼容酷我
            List<Long> toKuwoIdList = rechargeApplyVO.getToKuwoIdList();
            if (CollectionUtils.isNotEmpty(toKuwoIdList) && this.apolloConfigService.isKuwoEnv()) {
                List<Long> toKugouIdList = this.userFacadeService.getKugouIdsByKuwoIds(toKuwoIdList);
                String toKugouIds = toKugouIdList.stream().map(String::valueOf).reduce((s1, s2) -> StringUtils.join(s1, ",", s2))
                        .orElseThrow(() -> new BizException(SysResultCode.RECHARGE_ERROR_KUWO_ID));
                log.warn("支付宝对公转账，兼容酷我。toKuwoIdList: {}, toKugouIdList: {}", toKuwoIdList, toKugouIdList);
                rechargeApplyVO.setToKugouIds(toKugouIds);
            }
            SysResultCode resultCode = this.bigRechargeService.handleBigRecharge(webCommonParam, rechargeApplyVO);
            return JsonResult.result(resultCode, JsonResult.ResultCode::getMsg, Maps.newHashMap());
        } catch (Exception e) {
            log.error("支付宝对公转账，申请异常。webCommonParam: {}, rechargeApplyVO: {}", webCommonParam, rechargeApplyVO, e);
            return JsonResult.result(SysResultCode.E_40000210, Maps.newHashMap());
        }
    }

    /**
     * 线下充值 - 前端展示控制开关
     */
    @GetMapping(value = UrlConstants.OFFLINE_RECHARGE_SWITCH)
    public JsonResult<Map<String, String>> bigRechargeSwitch() {
        Config config = ConfigService.getAppConfig();
        boolean rechargeSwitch = config.getBooleanProperty(BIG_RECHARGE_RECHARGE_FRONT_SWITCH, true);
        boolean rebateSwitch = config.getBooleanProperty(BIG_RECHARGE_REBATE_FRONT_SWITCH, true);
        Map<String, String> dataMap = ImmutableMap.of(
                "rechargeShow", rechargeSwitch ? "1" : "0",
                "rebateShow", rebateSwitch ? "1" : "0"
        );
        return JsonResult.result(SysResultCode.SUCCESS, dataMap);
    }

    /**
     * 线下充值 - 大额充值合并返点
     */
    @PostMapping(value = UrlConstants.BIG_RECHARGE_REBATE_APPLY)
    public JsonResult<Map<String, Object>> applyRebate(WebCommonParam webCommonParam, ApplyRebateVO rebateApplyVO) {
        log.warn("RechargeController.applyRebate, common params: {}, business params: {}", webCommonParam, rebateApplyVO);
        try {
            // 开关控制
            boolean rebateSwitch = ConfigService.getAppConfig().getBooleanProperty("platform.pay.bigRecharge.rebate.switch", true);
            if (!rebateSwitch) {
                return JsonResult.result(SysResultCode.E_40000020, JsonResult.ResultCode::getMsg, Maps.newHashMap());
            }
            // 兼容酷我
            if (Objects.nonNull(rebateApplyVO.getKuwoId()) && this.apolloConfigService.isKuwoEnv()) {
                Optional<Long> optionalKugouId = this.userFacadeService.getKugouIdByKuwoId(rebateApplyVO.getKuwoId());
                if (!optionalKugouId.isPresent()) {
                    log.warn("大额充值合并返点，酷我映射酷狗ID失败。kuwoId: {}, kugouId: {}", rebateApplyVO.getKuwoId(), optionalKugouId);
                    return JsonResult.result(SysResultCode.RECHARGE_ERROR_KUWO_ID, Maps.newHashMap());
                }
                rebateApplyVO.setKugouId(optionalKugouId.get());
                log.warn("大额充值合并返点，兼容酷我。webCommonParam: {}, rebateApplyVO: {}", webCommonParam, rebateApplyVO);
            }
            // 参数校验
            String message = validatingService.validateParams(rebateApplyVO);
            if (StringUtils.isNotEmpty(message)) {
                log.warn("大额充值合并返点，参数错误。webCommonParam: {}, rebateApplyVO: {}, message: {}", webCommonParam, rebateApplyVO, message);
                return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, Maps.newHashMap());
            }
            // 合并返点
            SysResultCode resultCode = this.bigRebateService.handleBigCombineRebate(webCommonParam, rebateApplyVO);
            return JsonResult.result(resultCode, JsonResult.ResultCode::getMsg, Maps.newHashMap());
        } catch (Exception e) {
            log.error("applyRebate failure", e);
            return JsonResult.result(SysResultCode.E_40000018, JsonResult.ResultCode::getMsg, Maps.newHashMap());
        }
    }


    /**
     * 线下充值 - 用户申请列表
     * 1、大额合并返点
     * 2、支付宝对公转账
     * 3、银行卡对公转账
     */
    @GetMapping(value = UrlConstants.BIG_RECHARGE_APPLY_LIST)
    public JsonResult<List<ApplyInfoDetailVO>> rechargeApplyList(WebCommonParam webCommonParam, int applyType) {
        log.warn("RechargeController.applyList, common params: {}", webCommonParam);
        try {
            if (ApplyTypeEnum.APPLY_TYPE_1.getStatus() == applyType) {
                List<ApplyInfoDetailVO> applyListSorted = this.bigRechargeService.getApplyListSorted(webCommonParam.getKugouId());
                return JsonResult.result(SysResultCode.SUCCESS, applyListSorted);
            }
            List<ApplyInfoDetailVO> applyDetailListSorted = this.bigRechargeService.getApplyDetailListSorted(webCommonParam.getKugouId(), applyType);
            return JsonResult.result(SysResultCode.SUCCESS, applyDetailListSorted);
        } catch (Exception e) {
            log.error("rechargeApplyList exception", e);
            return JsonResult.result(SysResultCode.RECHARGE_SYS_ERROR, JsonResult.ResultCode::getMsg, Lists.newArrayList());
        }
    }

    /**
     * 线下下单回查校验（http://doc.kugou.net/showdoc-master/web/#/8?page_id=10756）
     */
    @GetMapping(value = UrlConstants.OFFLINE_RECHARGE_KUPAY_RECHECK)
    public KupayCommonResponse offlineRecheck(OfflineRecheckRequest request) {
        log.warn("线下下单回查校验，请求参数。request: {}", request);
        Map<String, Object> data = Maps.newHashMap();
        data.put("exist", 1);
        return KupayCommonResponse.result(SysResultCode.SUCCESS, data);
    }

}
