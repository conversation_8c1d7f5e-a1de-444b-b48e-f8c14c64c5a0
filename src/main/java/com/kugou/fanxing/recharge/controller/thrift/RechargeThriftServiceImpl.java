package com.kugou.fanxing.recharge.controller.thrift;

import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.service.RechargeOrderService;
import com.kugou.fanxing.recharge.thrift.*;
import com.kugou.rpc.exception.ServerException;
import com.kugou.rpc.server.thrift.annotation.ThriftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Slf4j
@ThriftService("/platform_recharge_service/thrift/rechargeThriftService")
public class RechargeThriftServiceImpl implements RechargeThriftService.Iface {

    private static final String SUCCESS = "success";

    @Autowired
    private RechargeOrderService rechargeOrderService;

    @Override
    public ResultList getRechargeList(long beginTime,long endTime,long lastId,int pageSize) {
        try {
            List<RechargeInfo> list = rechargeOrderService.getSuccessOrderListFormat(beginTime,endTime,lastId,pageSize, false);
            long rsLastId = 0;
            if(!CollectionUtils.isEmpty(list)){
                rsLastId = list.get(list.size() - 1).getId();
            }
            return RpcResultHelper.buildResultList(SysResultCode.SUCCESS.getCode(),SUCCESS,list,rsLastId);
        }catch (BizException e){
            log.warn("获取成功充值记录异常。beginTime: {}, endTime: {}, lastId: {}, pageSize: {}", beginTime, endTime, lastId, pageSize, e);
            return RpcResultHelper.buildResultList(e.getCode(),e.getMessage());
        }catch (Exception e) {
            log.warn("获取成功充值记录异常，故障转移。beginTime: {}, endTime: {}, lastId: {}, pageSize: {}", beginTime, endTime, lastId, pageSize, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public ResultSuccessList getRechargeSuccessList(long beginTime, long endTime, long lastRechargeId, int batchSize) {
        try {
            List<RechargeSuccessInfo> rechargeSuccessInfoList = rechargeOrderService.getRechargeSuccessList(beginTime, endTime, lastRechargeId, batchSize, false);
            long lastId = rechargeSuccessInfoList.stream().map(RechargeSuccessInfo::getRechargeId).reduce((first, second) -> second).orElse(0L);
            return RpcResultHelper.buildResultSuccessList(SysResultCode.SUCCESS.getCode(), SUCCESS, rechargeSuccessInfoList, lastId);
        } catch (BizException e) {
            log.warn("查询充值成功记录，业务异常。beginTime: {}, endTime: {}, lastRechargeId: {}, batchSize: {}", beginTime, endTime, lastRechargeId, batchSize, e);
            return RpcResultHelper.buildResultSuccessList(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.warn("查询充值成功记录，故障转移。beginTime: {}, endTime: {}, lastRechargeId: {}, batchSize: {}", beginTime, endTime, lastRechargeId, batchSize, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public ResultSuccessList getRechargeSuccessListV2(QueryRechargeSuccessRequest request) {
        try {
            List<RechargeSuccessInfo> rechargeSuccessInfoList = rechargeOrderService.getRechargeSuccessList(request);
            long lastId = rechargeSuccessInfoList.stream().map(RechargeSuccessInfo::getRechargeId).reduce((first, second) -> second).orElse(0L);
            return RpcResultHelper.buildResultSuccessList(SysResultCode.SUCCESS.getCode(), SUCCESS, rechargeSuccessInfoList, lastId);
        } catch (BizException e) {
            log.warn("查询充值成功记录V2，业务异常。request: {}", request, e);
            return RpcResultHelper.buildResultSuccessList(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.warn("查询充值成功记录V2，故障转移。request: {}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

    @Override
    public ResultList getFirstRechargeList(long beginTime,long endTime,long lastId,int pageSize){
        return RpcResultHelper.buildResultList(0,SUCCESS,null,0);
    }

    @Override
    public ResultInfo getRechargeInfo(String rechargeOrderNum) {
        try {
            return RpcResultHelper.buildResultInfo(0,SUCCESS,rechargeOrderService.getRechargeInfoByRechargeOrderNum(rechargeOrderNum));
        }catch (BizException e) {
            return RpcResultHelper.buildResultInfo(e.getCode(), e.getMessage(),null);
        }catch (Exception e){
            log.warn("查询充值成功记录，业务异常,rechargeOrderNum:{},exception:",rechargeOrderNum);
            throw new ServerException(516, e.getMessage(), e);
        }

    }

    @Override
    public AgentInfo isAgent(long kugouId) {
        // 旧版代充业务已经下线
        return new AgentInfo().setMsg(SUCCESS).setRet(0).setIsAgent(0);
    }

    @Override
    public TmallFirstRechargeInfo isTmallFirstRecharge(String rechargeOrderNum) {
        return new TmallFirstRechargeInfo().setRet(0).setMsg(SUCCESS);
    }

    @Override
    public ResultInfo getRechargeInfoFromMaster(String rechargeOrderNum) {
        try {
            RechargeInfo rechargeInfo = rechargeOrderService.getRechargeInfoByRechargeOrderNum(rechargeOrderNum);
            if (Objects.isNull(rechargeInfo)) {
                return RpcResultHelper.buildResultInfo(1, "订单不存在", null);
            }
            return RpcResultHelper.buildResultInfo(0, "ok", rechargeInfo);
        } catch (BizException e) {
            return RpcResultHelper.buildResultInfo(e.getCode(), e.getMessage(), null);
        } catch (Exception e) {
            log.warn("查询充值成功记录<getRechargeInfoFromMaster>，处理异常。rechargeOrderNum:{}", rechargeOrderNum, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }

}
