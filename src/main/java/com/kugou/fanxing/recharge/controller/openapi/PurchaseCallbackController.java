package com.kugou.fanxing.recharge.controller.openapi;

import com.google.common.collect.Maps;
import com.kugou.config.Env;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.model.bo.KugouOpenBusinessBO;
import com.kugou.fanxing.recharge.model.dto.CoinCallbackDTO;
import com.kugou.fanxing.recharge.model.dto.RechargeExtendDTO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.callback.OpenRechargeCallbackService;
import com.kugou.fanxing.recharge.util.HttpClientUtils;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.fanxing.recharge.util.SignUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Map;
import java.util.Optional;


/**
 * 酷狗开放平台充值购买回调
 *
 * <AUTHOR>
 */
@Slf4j
@Controller
public class PurchaseCallbackController {

    private static final String SUCCESS = "success";
    private static final String FAILURE = "failure";

    @Autowired
    private Env env;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private OpenRechargeCallbackService openRechargeCallbackService;

    /**
     * 支付网关异步回调通知接口
     * http://doc.kugou.net/showdoc-master/web/#/8?page_id=2036
     *
     * @param request         HTTP请求
     * @param coinCallbackDTO 业务数据
     * @return 成功时返回success，失败时返回failure，注意小写
     */
    @ResponseBody
    @RequestMapping(value = UrlConstants.KUGOU_OPEN_PURCHASE_CALLBACK)
    public String callback(CoinCallbackDTO coinCallbackDTO) {
        try {
            log.warn("酷狗开放平台充值回调，请求参数。coinCallbackDTO: {}", coinCallbackDTO);
            SysResultCode sysResultCode = openRechargeCallbackService.checkPrerequisites(coinCallbackDTO);
            if (env.isProd() && !sysResultCode.isSuccess()) {
                log.warn("酷狗开放平台充值回调，前置条件检查不通过。coinCallbackDTO: {}", coinCallbackDTO);
                return FAILURE;
            }
            sysResultCode = openRechargeCallbackService.handleOpenCallbackOrder(coinCallbackDTO, callbackOrder -> {
                log.warn("酷狗开放平台充值回调，开始调用业务方发货。callbackOrder: {}", callbackOrder);
                String openAppId = callbackOrder.getBusinessId();
                KugouOpenBusinessBO kugouOpenBusinessBO = this.apolloConfigService.getOpenRechargeBusinessConfig(openAppId);
                String extend = new String(Base64.decodeBase64(StringUtils.defaultString(callbackOrder.getExtend())));
                RechargeExtendDTO rechargeExtendDTO = this.openRechargeCallbackService.parseRechargeExtendDTO(extend);
                String businessNotifyUrl = this.apolloConfigService.getOpenRechargeBusinessConfig(rechargeExtendDTO.getBusinessId()).getBusinessNotifyUrl();
                Map<String, String> bodyParams = Maps.newHashMap();
                bodyParams.put("openAppId", openAppId);
                bodyParams.put("businessId", rechargeExtendDTO.getBusinessId());
                bodyParams.put("rechargeOrderNum", callbackOrder.getRechargeOrderNum());
                bodyParams.put("kugouId", String.valueOf(callbackOrder.getKugouId()));
                bodyParams.put("tradeNo", callbackOrder.getTradeNo());
                bodyParams.put("tradeTime", String.valueOf(callbackOrder.getTradeTime()));
                bodyParams.put("partner", callbackOrder.getPartner());
                bodyParams.put("money", callbackOrder.getMoney().stripTrailingZeros().toPlainString());
                bodyParams.put("addTime", String.valueOf(callbackOrder.getAddTime()));
                bodyParams.put("userOpenid", rechargeExtendDTO.getUserOpenid());
                bodyParams.put("signature", SignUtils.buildSignByKugouOpen(bodyParams, kugouOpenBusinessBO.getSecretKey()));
                Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(env.isProd() ? "hfp.fanxing.kgidc.cn" : "forward.proxy.kgidc.cn", 3128));
                Optional<String> optionalJson = HttpClientUtils.doPostJSONProxy(proxy, businessNotifyUrl, Maps.newHashMap(), bodyParams);
                if (!optionalJson.isPresent()) {
                    log.warn("酷狗开放平台充值业务回调失败。optionalJson: {}, callbackOrder: {}", optionalJson, callbackOrder);
                    return SysResultCode.E_50000004;
                }
                int code = JsonUtils.parseJsonPathChecked(optionalJson.get(), "$.code", Integer.class);
                if (code != 0) {
                    log.warn("酷狗开放平台充值业务回调失败。optionalJson: {}, callbackOrder: {}", optionalJson, callbackOrder);
                    return SysResultCode.E_50000004;
                }
                log.warn("酷狗支付网关充值回调，通知业务发货成功。optionalJson: {}, callbackOrder: {}", optionalJson, callbackOrder);
                return SysResultCode.SUCCESS;
            });
            if (!sysResultCode.isSuccess()) {
                log.warn("酷狗开放平台充值回调，处理回调订单失败。coinCallbackDTO: {}", coinCallbackDTO);
                return FAILURE;
            }
            return SUCCESS;
        } catch (Exception e) {
            // 注意⚠️：捕获异常网关收到FAILURE会重试
            log.error("酷狗开放平台充值回调，处理回调订单异常。coinCallbackDTO: {}", coinCallbackDTO, e);
            return FAILURE;
        }
    }


}
