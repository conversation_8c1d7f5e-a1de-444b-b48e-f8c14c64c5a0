package com.kugou.fanxing.recharge.controller;

import com.google.common.collect.Maps;
import com.kugou.config.Env;
import com.kugou.fanxing.recharge.common.aop.annotation.PoorMode;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.CoinTypeEnum;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.model.bo.KugouOpenBusinessBO;
import com.kugou.fanxing.recharge.model.dto.WxMiniProgramDTO;
import com.kugou.fanxing.recharge.model.request.*;
import com.kugou.fanxing.recharge.service.*;
import com.kugou.fanxing.recharge.service.common.OrderIdService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.fanxing.recharge.util.KugouOpenResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 微信支付
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class WechatRechargeController {

    @Autowired
    private Env env;
    @Autowired
    private ValidatingService validatingService;
    @Autowired
    private OrderIdService orderIdService;
    @Autowired
    private WechatRechargeService wechatRechargeService;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private RechargeConfig rechargeConfig;
    @Autowired
    private RemoteStrategyService remoteStrategyService;
    @Autowired
    private RechargeCommonService rechargeCommonService;
    @Autowired
    private UserFacadeService userFacadeService;

    @GetMapping(value = UrlConstants.REFRESH_WECHAT_QRCODE)
    public JsonResult<Map<String, Object>> refreshWechatQrCode(WebCommonParam webCommonParam, String rechargeOrderNum) {
        log.warn("网页微信刷新二维码信息。webCommonParam: {}, rechargeOrderNum: {}", webCommonParam, rechargeOrderNum);
        return wechatRechargeService.refreshWechatQrCode(webCommonParam, rechargeOrderNum);
    }

    /**
     * 网页微信充值下单接口
     */
    @PoorMode
    @PostMapping(value = UrlConstants.GET_ORDER_FOR_WECHAT)
    public JsonResult<Map<String, Object>> getOrderForWechat(WebCommonParam webCommonParam, WechatRequest wechatRequest) {
        log.warn("微信网页充值下单接口，请求参数。webCommonParam: {}, wechatRequest: {}", webCommonParam, wechatRequest);
        wechatRequest.initRequest(PayTypeIdEnum.PAY_TYPE_ID_39, webCommonParam);
        // 请求参数校验
        String message = validatingService.validateParams(wechatRequest);
        if (StringUtils.isNotEmpty(message)) {
            log.warn("微信网页充值下单接口，请求参数不合法。webCommonParam: {}, wechatRequest: {}", webCommonParam, wechatRequest);
            return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, Maps.newHashMap());
        }
        // 检查充值渠道配置
        if (!this.rechargeConfig.isRechargeWithChange(wechatRequest.getPayTypeIdEnum().getPayTypeId())) {
            log.warn("微信网页充值下单接口，充值渠道尚未配置。webCommonParam: {}, wechatRequest: {}", webCommonParam, wechatRequest);
            return JsonResult.result(SysResultCode.RECHARGE_PAY_TYPE_NOT_SUPPORT, Maps.newHashMap());
        }
        // 检查充值渠道开关
        if (this.rechargeCommonService.isForbiddenPayType(wechatRequest.getPayTypeIdEnum())) {
            log.warn("微信网页充值下单接口，充值渠道暂停服务。webCommonParam: {}, wechatRequest: {}", webCommonParam, wechatRequest);
            return JsonResult.result(SysResultCode.RECHARGE_PAUSE_SERVICE, Maps.newHashMap());
        }
        Map<String, Object> dataMap = this.wechatRechargeService.getOrderForWechat(webCommonParam, wechatRequest);
        return JsonResult.result(SysResultCode.SUCCESS, dataMap);
    }

    /**
     * 微信小程序下单接口
     * http://doc.kugou.net/showdoc-master/web/#/8?page_id=1594
     */
    @PostMapping(value = UrlConstants.GET_ORDER_FOR_WX_XCX)
    public JsonResult<Map<String, Object>> getOrderForWxMiniProgram(WebCommonParam webCommonParam, WxMiniProgramRequest request) {
        if ((env.isDev() || env.isTest()) && apolloConfigService.getRechargePoorMode()) {
            request.setMoney(new BigDecimal("0.02"));
        }
        // 响应有效载荷
        Map<String, Object> payload = Maps.newHashMap();

        // 请求参数校验
        log.warn("微信小程序下单接口, webCommonParam: {}, wxMiniProgramRequest: {}", webCommonParam, request);
        String message = validatingService.validateParams(request);
        if (StringUtils.isNotEmpty(message)) {
            return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, payload);
        }

        // 检查平台编号
        if (webCommonParam.getPid() != 39 && webCommonParam.getPid() != 40) {
            log.warn("微信小程序下单接口, 检查pid参数不合法, webCommonParam: {}, wxMiniProgramRequest: {}", webCommonParam, request);
            return JsonResult.failure(SysResultCode.RECHARGE_PARAM_ERROR, JsonResult.ResultCode::getMsg, payload);
        }

        // 检查充值金额
        List<BigDecimal> permitRechargeMoneyList = apolloConfigService.getWxMiniProgramPermitRechargeMoneyList();
        boolean permission = permitRechargeMoneyList.stream().anyMatch(permitMoney -> permitMoney.equals(request.getMoney()));
        if (!permission) {
            log.warn("微信小程序下单接口, 检查充值金额非在售产品, money: {}, permitRechargeMoneyList: {}", request.getMoney(), permitRechargeMoneyList);
            return JsonResult.failure(SysResultCode.RECHARGE_MONEY_INVALID, resultCode -> String.format(resultCode.getMsg(), message), payload);
        }

        // 检查直播账号
        long kugouId = webCommonParam.getKugouId();
        Optional<Long> optionalUserId = this.userFacadeService.getUserIdByKugouId(kugouId, false);
        if (!optionalUserId.isPresent() || optionalUserId.get() < 1) {
            log.warn("微信小程序下单接口, 检查酷狗账号对应繁星账号不存在, kugouId: {}", kugouId);
            return JsonResult.failure(SysResultCode.RECHARGE_NOT_FANXING, resultCode -> String.format(resultCode.getMsg(), message), payload);
        }

        // 风控参数校验
        String rechargeOrderNum = orderIdService.generateRechargeOrderNumForAcross();
        if (remoteStrategyService.strategyVerifyForWxMiniProgram(PayTypeIdEnum.PAY_TYPE_ID_42, rechargeOrderNum, kugouId, "", request)) {
            log.warn("微信小程序下单接口, 风控校验不通过, kugouId: {}, wxMiniProgramRequest: {}", kugouId, request);
            return JsonResult.failure(SysResultCode.RECHARGE_RISK_STRATEGY, resultCode -> String.format(resultCode.getMsg(), message), payload);
        }

        // 创建充值订单
        Optional<WxMiniProgramDTO> optionalRechargeOrderNum = this.wechatRechargeService.getOrderForWxxcx(rechargeOrderNum, webCommonParam, request);
        if (!optionalRechargeOrderNum.isPresent()) {
            log.error("微信小程序下单接口, 调用微信小程序下单接口失败");
            return JsonResult.failure(JsonResult.DefaultResultCodeEnum.FAILURE, JsonResult.ResultCode::getMsg, payload);
        }
        payload.put("rechargeOrderNum", rechargeOrderNum);
        payload.put("paymentParams", optionalRechargeOrderNum.get());
        return JsonResult.success(payload);
    }

    /**
     * 微信公众号下单接口
     */
    @PoorMode
    @PostMapping(value = UrlConstants.GET_ORDER_FOR_WX_GZH)
    public JsonResult<Map<String, Object>> getOrderForWxgzh(WebCommonParam webCommonParam, WxgzhRequest request) {
        // 微信公众号不支持充值代金券
        if (request.getCouponId() > 0) {
            log.warn("微信公众号下单接口，不支持使用代金券。webCommonParam: {}, wxgzhRequest: {}", webCommonParam, request);
            request.setCouponId(0);
        }
        log.warn("微信公众号下单接口，请求参数。webCommonParam: {}, wxgzhRequest: {}", webCommonParam, request);
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_41, webCommonParam);
        Map<String, Object> dataMap = this.wechatRechargeService.getOrderForWxgzh(webCommonParam, request);
        return JsonResult.result(SysResultCode.SUCCESS, dataMap);
    }

    @RequestMapping(value = UrlConstants.RECHARGE_PLAT_WECHAT_MOBILE)
    public Map<String, Object> weixinPayM(WebCommonParam webCommonParam, WechatMobileRequest request) {
        log.warn("旧手机微信充值下单接口, webCommonParam: {}, request: {}", webCommonParam, request);
        if ((env.isDev() || env.isTest()) && apolloConfigService.getRechargePoorMode()) {
            request.setAmount(new BigDecimal("0.02"));
        }
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_40, webCommonParam);
        return this.wechatRechargeService.getOrderForWechatMobile(webCommonParam, request,
                CoinTypeEnum.of(request.getCoinType()), false);
    }

    @RequestMapping(value = UrlConstants.GET_ORDER_FOR_WECHAT_MOBILE)
    public Map<String, Object> weixinPayM2(WebCommonParam webCommonParam, WechatMobileRequest request) {
        log.warn("新手机微信充值下单V1接口, webCommonParam: {}, request: {}", webCommonParam, request);
        if ((env.isDev() || env.isTest()) && apolloConfigService.getRechargePoorMode()) {
            request.setAmount(new BigDecimal("0.02"));
        }
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_40, webCommonParam);
        return this.wechatRechargeService.getOrderForWechatMobile(webCommonParam, request,
                CoinTypeEnum.of(request.getCoinType()), false);
    }

    @PoorMode
    @RequestMapping(value = UrlConstants.GET_ORDER_FOR_WECHAT_MOBILE_V2)
    public JsonResult<Map<String, Object>> weixinPayMV2(WebCommonParam webCommonParam, WechatMobileRequest request) {
        log.warn("新手机微信充值下单V2接口, webCommonParam: {}, request: {}", webCommonParam, request);
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_40, webCommonParam);
        Map<String, Object> payload = this.wechatRechargeService.getOrderForWechatMobile(webCommonParam,
                request, CoinTypeEnum.of(request.getCoinType()), false);
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

    @PoorMode
    @RequestMapping(value = UrlConstants.GET_ORDER_FOR_WECHAT_APP)
    public JsonResult<Map<String, Object>> wechatApp(WebCommonParam webCommonParam, WechatMobileRequest request) {
        log.warn("微信APP充值下单接口, webCommonParam: {}, request: {}", webCommonParam, request);
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_40, webCommonParam);
        Map<String, Object> payload = this.wechatRechargeService.getOrderForWechatMobile(webCommonParam,
                request, CoinTypeEnum.of(request.getCoinType()), true);
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

    @PoorMode
    @RequestMapping(value = UrlConstants.GET_ORDER_FOR_WECHAT_APP_SING)
    public JsonResult<Map<String, Object>> wechatAppSing(WebCommonParam webCommonParam, WechatMobileRequest request) {
        log.warn("微信APP唱币充值下单接口, webCommonParam: {}, request: {}", webCommonParam, request);
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_40, webCommonParam);
        request.setCoinType(CoinTypeEnum.SING_COIN.getCoinType());
        Map<String, Object> payload = this.wechatRechargeService.getOrderForWechatMobile(webCommonParam, request, CoinTypeEnum.SING_COIN, false);
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

    @PoorMode
    @RequestMapping(value = UrlConstants.RECHARGE_PLAT_WECHAT_H5)
    public JsonResult<Map<String, Object>> wechatPayH5(WebCommonParam webCommonParam, GetOrderH5Request request) {
        log.warn("手机微信充值下单接口, webCommonParam: {}, request: {}", webCommonParam, request);
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_32, webCommonParam);
        Map<String, Object> payload = this.wechatRechargeService.getOrderForWechatH5(webCommonParam, request);
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

    /**
     * 酷狗开放平台-手机微信充值下单接口
     */
    @RequestMapping(value = UrlConstants.KUGOU_OPEN_WECHAT_M)
    public KugouOpenResult<Map<String, Object>> openWechatM(WebCommonParam webCommonParam) {
        log.warn("酷狗开放平台微信安卓充值下单接口。webCommonParam: {}", webCommonParam);
        if (!apolloConfigService.allowKugouOpenRecharge()) {
            log.warn("酷狗开放平台微信安卓充值下单接口，接口已下架。webCommonParam: {}", webCommonParam);
            return KugouOpenResult.result(SysResultCode.RECHARGE_PAY_TYPE_NOT_SUPPORT, Maps.newHashMap());
        }
        KugouOpenDispatchParam kugouOpenDispatchParam = webCommonParam.getKugouOpenDispatchParam();
        String openAppId = kugouOpenDispatchParam.getOpenappid();
        OpenWechatMRequest request = new OpenWechatMRequest();
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_40, webCommonParam);
        request.setBusinessId(openAppId);
        request.setAmount(JsonUtils.parseJsonPath(kugouOpenDispatchParam.getBusiness_data(), "$.amount", BigDecimal.class).orElse(BigDecimal.ZERO));
        if ((env.isDev() || env.isTest()) && apolloConfigService.getRechargePoorMode()) {
            request.setAmount(new BigDecimal("0.01"));
        }
        KugouOpenBusinessBO kugouOpenBusinessBO = this.apolloConfigService.getOpenRechargeBusinessConfig(openAppId);
        webCommonParam.setPid(kugouOpenBusinessBO.getPid());
        Map<String, Object> dataMap = this.wechatRechargeService.getOrderWechatM(webCommonParam, request, kugouOpenBusinessBO);
        KugouOpenResult<Map<String, Object>> kugouOpenResult = KugouOpenResult.result(SysResultCode.SUCCESS, dataMap);
        log.warn("酷狗开放平台微信安卓充值下单接口。kugouOpenResult: {}", kugouOpenResult);
        return kugouOpenResult;
    }

    @PoorMode
    @PostMapping(value = UrlConstants.GAME_WECHAT_H5)
    public JsonResult<Map<String, Object>> gameWechatH5(WebCommonParam webCommonParam, GetOrderH5Request request) {
        log.warn("游戏微信H5充值下单接口, webCommonParam: {}, request: {}", webCommonParam, request);
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_32, webCommonParam);
        Map<String, Object> payload = this.wechatRechargeService.gameWechatH5(webCommonParam, request);
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

}
