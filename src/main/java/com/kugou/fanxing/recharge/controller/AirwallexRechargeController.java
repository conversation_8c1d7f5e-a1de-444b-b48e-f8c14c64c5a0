package com.kugou.fanxing.recharge.controller;

import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.common.aop.annotation.CdnCache;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.model.dto.AirwallexRefundNotifyDto;
import com.kugou.fanxing.recharge.model.request.*;
import com.kugou.fanxing.recharge.model.vo.CountryInfoVo;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.RechargeCommonService;
import com.kugou.fanxing.recharge.service.bi.DatabusService;
import com.kugou.fanxing.recharge.service.recharge.AirwallexRechargeService;
import com.kugou.fanxing.recharge.service.refund.v2.RefundServiceFactory;
import com.kugou.fanxing.recharge.util.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
public class AirwallexRechargeController {

    @Autowired
    private DatabusService databusService;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private RechargeCommonService rechargeCommonService;
    @Autowired
    private AirwallexRechargeService airwallexRechargeService;

    @CdnCache(cacheStrategy = CdnCache.CacheStrategy.NO_CACHE)
    @GetMapping(value = UrlConstants.AIRWALLEX_COUNTRY_LIST)
    public JsonResult<Map<String, Object>> overseasCountryList(WebCommonParam commonParam) {
        log.warn("Airwallex获取国家代码列表，请求参数。commonParam: {}", commonParam);
        SysResultCode sysResultCode = this.rechargeCommonService.checkParameter(commonParam, new Object());
        if (!sysResultCode.isSuccess()) {
            return JsonResult.result(sysResultCode, Maps.newHashMap());
        }
        // 国家列表针对禁止访问账号不可见
        if (this.apolloConfigService.isAirwallexBlockAccount(commonParam.getKugouId())) {
            log.warn("Airwallex获取国家代码列表，禁止访问账号不展示。commonParam: {}", commonParam);
            return JsonResult.result(SysResultCode.SUCCESS, Maps.newHashMap());
        }
        // 国家列表针对灰度账号和BI导入用户可见
        if (this.apolloConfigService.isAirwallexAllowAccount(commonParam.getKugouId()) || databusService.isAirwallexAllowKugouId(commonParam.getKugouId())) {
            List<CountryInfoVo> countryInfoVoList = this.apolloConfigService.getOverseasCountryList();
            Map<String, Object> payload = Maps.newHashMap();
            payload.put("countryList", countryInfoVoList);
            return JsonResult.result(SysResultCode.SUCCESS, payload);
        }
        log.warn("Airwallex获取国家代码列表，非允许访问账号不展示。commonParam: {}", commonParam);
        return JsonResult.result(SysResultCode.SUCCESS, Maps.newHashMap());
    }

    @PostMapping(value = UrlConstants.AIRWALLEX_APP_CREATE_ORDER)
    public JsonResult<Map<String, Object>> appCreateOrder(WebCommonParam webCommonParam, AirwallexCreateRequest request) {
        log.warn("Airwallex手机创建订单接口，请求参数。webCommonParam: {}, request: {}", webCommonParam, request);
        request.initRequest(PayTypeIdEnum.PAY_TYPE_ID_522, webCommonParam);
        return this.airwallexRechargeService.appCreateOrder(webCommonParam, request);
    }

    @PostMapping(value = UrlConstants.AIRWALLEX_APP_CONFIRM_ORDER)
    public JsonResult<Map<String, Object>> appConfirmOrder(WebCommonParam webCommonParam, AirwallexConfirmRequest request) {
        log.warn("Airwallex手机下单确认接口，请求参数。webCommonParam: {}, request: {}", webCommonParam, request);
        return this.airwallexRechargeService.appConfirmOrder(webCommonParam, request);
    }

    @CdnCache(cacheStrategy = CdnCache.CacheStrategy.NO_CACHE)
    @GetMapping(value = UrlConstants.AIRWALLEX_GET_AMOUNT_METHOD_LIST)
    public JsonResult<Map<String, Object>> getPaymentMethodTypes(WebCommonParam webCommonParam, GetPaymentMethodTypesRequest request) {
        log.warn("Airwallex获取支付金额与方式列表，请求参数。webCommonParam: {}, request: {}", webCommonParam, request);
        return this.airwallexRechargeService.getPaymentMethodTypes(webCommonParam, request);
    }

    @CdnCache(cacheStrategy = CdnCache.CacheStrategy.NO_CACHE)
    @GetMapping(value = UrlConstants.AIRWALLEX_GET_BANK_NAME_LIST)
    public JsonResult<Map<String, Object>> getBankNameList(WebCommonParam webCommonParam, GetBankNameListRequest request) {
        log.warn("Airwallex获取银行名称列表，请求参数。webCommonParam: {}, request: {}", webCommonParam, request);
        return this.airwallexRechargeService.getBankNameList(webCommonParam, request);
    }

    @RequestMapping(UrlConstants.AIRWALLEX_REFUND_NOTIFY)
    public String refundNotify(AirwallexRefundNotifyDto airwallexRefundNotifyDto) {
        log.warn("Airwallex获取用户退款通知，请求参数。airwallexRefundNotifyDto: {}", airwallexRefundNotifyDto);
        boolean isSuccess = RefundServiceFactory.createRefundService(PayTypeIdEnum.PAY_TYPE_ID_522.getPayTypeId())
                .receiveNotify(airwallexRefundNotifyDto);
        return isSuccess ? "success" : "failure";
    }

}
