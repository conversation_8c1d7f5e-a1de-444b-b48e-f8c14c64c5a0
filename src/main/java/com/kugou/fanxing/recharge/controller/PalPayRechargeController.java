package com.kugou.fanxing.recharge.controller;

import com.google.common.collect.Maps;
import com.kugou.config.Env;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.model.request.PayPalRequest;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.PayPalRechargeService;
import com.kugou.fanxing.recharge.service.ValidatingService;
import com.kugou.fanxing.recharge.util.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Map;

/**
 * PayPal
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class PalPayRechargeController {

    @Autowired
    private PayPalRechargeService payPalRechargeService;
    @Autowired
    private ValidatingService validatingService;
    @Autowired
    private RechargeConfig rechargeConfig;
    @Autowired
    private Env env;
    @Autowired
    private ApolloConfigService apolloConfigService;

    /**
     * PayPal充值下单接口
     */
    @PostMapping(value = UrlConstants.GET_ORDER_FOR_PAY_PAL)
    public JsonResult<Map<String, Object>> getOrderForPayPal(WebCommonParam webCommonParam, @RequestBody PayPalRequest payPalRequest) {
        // 请求参数校验
        log.warn("PayPal充值下单接口, webCommonParam: {}, payPalRequest: {}", webCommonParam, payPalRequest);
        String message = validatingService.validateParams(payPalRequest);
        if (StringUtils.isNotEmpty(message)) {
            log.warn("PayPal充值下单接口，请求参数不合法。webCommonParam: {}, payPalRequest: {}", webCommonParam, payPalRequest);
            return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, Maps.newHashMap());
        }
        if ((env.isDev() || env.isTest()) && apolloConfigService.getRechargePoorMode()) {
            payPalRequest.setAmount(new BigDecimal("0.02"));
        }
        // 校验充值渠道
        int payTypeId = PayTypeIdEnum.PAY_TYPE_ID_520.getPayTypeId();
        if (!this.rechargeConfig.isRechargeWithChange(payTypeId)) {
            log.warn("PayPal充值下单接口，充值渠道尚未配置。webCommonParam: {}, payPalRequest: {}", webCommonParam, payPalRequest);
            return JsonResult.failure(SysResultCode.RECHARGE_PAY_TYPE_NOT_SUPPORT, JsonResult.ResultCode::getMsg, Maps.newHashMap());
        }
        // 充值成功的回调地址（默认为referer）
        payPalRequest.setSync_url(StringUtils.defaultIfBlank(payPalRequest.getSync_url(), webCommonParam.getReferer()));
        Map<String, Object> dataMap = payPalRechargeService.getPayPalUrl(webCommonParam, payPalRequest);
        return JsonResult.success(SysResultCode.SUCCESS, "", dataMap);
    }


}
