package com.kugou.fanxing.recharge.controller;

import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.model.bo.KugouOpenBusinessBO;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.IapOrderVerifyRequest;
import com.kugou.fanxing.recharge.model.request.KugouOpenDispatchParam;
import com.kugou.fanxing.recharge.model.request.OpenAppStoreRequest;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.model.vo.AppStoreProductVO;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.AppStoreRechargeService;
import com.kugou.fanxing.recharge.service.RechargeOrderService;
import com.kugou.fanxing.recharge.service.ValidatingService;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.fanxing.recharge.util.KugouOpenResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

@Slf4j
@RestController
public class AppStoreRechargeController {

    public static final String SUCCESS = "success";
    public static final String FAILURE = "failure";
    @Autowired
    private AppStoreRechargeService appStoreRechargeService;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private ValidatingService validatingService;
    @Autowired
    private RechargeOrderService rechargeOrderService;

    /**
     * 网关回查
     */
    @RequestMapping(value = UrlConstants.KUPAY_APPSTORE_ORDER_CALLBACK)
    public String orderCallback(HttpServletRequest request) {
        log.warn("苹果内购酷狗支付网关回调，请求参数。requestMethod: {}, parameterMap: {}",
                request.getMethod(), request.getParameterMap());
        return SUCCESS;
    }

    /**
     * 网关校验
     */
    @RequestMapping(value = UrlConstants.KUPAY_APPSTORE_ORDER_VERIFY)
    public String orderVerify(HttpServletRequest request, IapOrderVerifyRequest iapOrderVerifyRequest) {
        log.warn("苹果内购酷狗支付网关回查，请求参数。requestMethod: {}, parameterMap: {}, iapOrderVerifyRequest: {}",
                request.getMethod(), request.getParameterMap(), iapOrderVerifyRequest);
        Optional<ConstraintViolation<IapOrderVerifyRequest>> optionalConstraintViolation = this.validatingService.checkViolation(iapOrderVerifyRequest);
        if (optionalConstraintViolation.isPresent()) {
            log.warn("苹果内购酷狗支付网关回查，请求参数缺失。requestMethod: {}, parameterMap: {}, iapOrderVerifyRequest: {}, optionalConstraintViolation: {}",
                    request.getMethod(), request.getParameterMap(), iapOrderVerifyRequest, optionalConstraintViolation);
            Cat.logEvent("Kupay", "appstoreOrderVerify", "1", "");
            return FAILURE;
        }
        // 获取充值单号
        String tradeNo = iapOrderVerifyRequest.getTrade_no();
        Optional<String> optionalRechargeOrderNum = this.rechargeOrderService.specialRechargeOrderNumDeal("TID" + tradeNo, PayTypeIdEnum.PAY_TYPE_ID_1006.getPayTypeId());
        if (!optionalRechargeOrderNum.isPresent()) {
            log.warn("苹果内购酷狗支付网关回查，充值订单号获取失败。requestMethod: {}, parameterMap: {}, iapOrderVerifyRequest: {}, optionalRechargeOrderNum: {}",
                    request.getMethod(), request.getParameterMap(), iapOrderVerifyRequest, optionalRechargeOrderNum);
            Cat.logEvent("Kupay", "appstoreOrderVerify", "1", "");
            return FAILURE;
        }
        String rechargeOrderNum = optionalRechargeOrderNum.get();
        Optional<RechargeAcrossPO> optionalRechargeAcrossPO = this.rechargeOrderService.queryByRechargeOrderNum(rechargeOrderNum);
        if (!optionalRechargeAcrossPO.isPresent() || optionalRechargeAcrossPO.get().getStatus() != 1) {
            log.warn("苹果内购酷狗支付网关回查，充值订单号暂未支付。requestMethod: {}, parameterMap: {}, iapOrderVerifyRequest: {}, optionalRechargeAcrossPO: {}",
                    request.getMethod(), request.getParameterMap(), iapOrderVerifyRequest, optionalRechargeAcrossPO);
            Cat.logEvent("Kupay", "appstoreOrderVerify", "1", "");
            return FAILURE;
        }
        log.warn("苹果内购酷狗支付网关回查，充值订单号支付成功。requestMethod: {}, parameterMap: {}, iapOrderVerifyRequest: {}, optionalRechargeAcrossPO: {}",
                request.getMethod(), request.getParameterMap(), iapOrderVerifyRequest, optionalRechargeAcrossPO);
        Cat.logEvent("Kupay", "appstoreOrderVerify", "0", "");
        return SUCCESS;
    }

    /**
     * 创建订单
     */
    @PostMapping(value = UrlConstants.KUGOU_OPEN_IAP_CREATE_ORDER)
    public KugouOpenResult<Map<String, Object>> createOrder(WebCommonParam webCommonParam) {
        log.warn("游戏苹果内购充值下单，请求参数。webCommonParam: {}", webCommonParam);
        KugouOpenDispatchParam kugouOpenDispatchParam = webCommonParam.getKugouOpenDispatchParam();
        String openAppId = kugouOpenDispatchParam.getOpenappid();
        int pid = JsonUtils.parseJsonPath(kugouOpenDispatchParam.getBusiness_data(), "$.std_plat", Integer.class, 307);
        String productId = JsonUtils.parseJsonPathChecked(kugouOpenDispatchParam.getBusiness_data(), "$.productId", String.class);
        Optional<AppStoreProductVO> optionalAppStoreProductVO = appStoreRechargeService.getProduct(openAppId, pid, productId);
        if (!optionalAppStoreProductVO.isPresent()) {
            log.warn("游戏苹果内购充值下单，请求参数。webCommonParam: {}", webCommonParam);
            throw new BizException(SysResultCode.E_60000000).addContextValue("openappid", openAppId)
                    .addContextValue("pid", pid).addContextValue("productId", productId);
        }
        AppStoreProductVO appStoreProductVO = optionalAppStoreProductVO.get();
        OpenAppStoreRequest openAppStoreRequest = new OpenAppStoreRequest();
        openAppStoreRequest.initRequest(PayTypeIdEnum.PAY_TYPE_ID_1006, webCommonParam);
        openAppStoreRequest.setBusinessId(openAppId);
        openAppStoreRequest.setProductId(productId);
        openAppStoreRequest.setAmount(new BigDecimal(appStoreProductVO.getPrice()));
        KugouOpenBusinessBO kugouOpenBusinessBO = this.apolloConfigService.getOpenRechargeBusinessConfig(openAppId);
        webCommonParam.setPid(pid);
        Map<String, Object> dataMap = appStoreRechargeService.getOrder(webCommonParam, openAppStoreRequest, kugouOpenBusinessBO);
        KugouOpenResult<Map<String, Object>> kugouOpenResult = KugouOpenResult.result(SysResultCode.SUCCESS, dataMap);
        log.warn("游戏苹果内购充值下单，下单成功。webCommonParam: {}, dataMap: {}", webCommonParam, dataMap);
        return kugouOpenResult;
    }

    /**
     * 收据上报
     */
    @PostMapping(value = UrlConstants.KUGOU_OPEN_IAP_FINISH_ORDER)
    public KugouOpenResult<Map<String, Object>> finishOrder(WebCommonParam webCommonParam) {
        log.warn("游戏苹果内购收据上报，请求参数。webCommonParam: {}", webCommonParam);
        SysResultCode sysResultCode = this.appStoreRechargeService.finishOrder(webCommonParam);
        Map<String, Object> payload = Maps.newHashMap();
        KugouOpenResult<Map<String, Object>> kugouOpenResult = KugouOpenResult.result(sysResultCode, payload);
        log.warn("游戏苹果内购收据上报，响应结果。kugouOpenResult: {}", kugouOpenResult);
        payload.put("code", sysResultCode.getCode());
        payload.put("msg", sysResultCode.getMsg());
        return kugouOpenResult;
    }

}
