package com.kugou.fanxing.recharge.controller.openapi;

import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.util.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Slf4j
@Controller
public class MockBusinessController {

    @ResponseBody
    @RequestMapping(value = "/intranet/api/v1/notifyBusiness")
    public JsonResult<Map<String, Object>> notifyBusiness(HttpServletRequest request) {
        log.warn("酷狗开放平台充值业务回调。request: {}", request.getParameterMap());
        return JsonResult.result(SysResultCode.SUCCESS, Maps.newHashMap());
    }
}
