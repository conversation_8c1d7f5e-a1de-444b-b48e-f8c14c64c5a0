package com.kugou.fanxing.recharge.controller.thrift;

import com.kugou.fanxing.recharge.exception.BizException;
import com.kugou.fanxing.recharge.service.wxpay.WxRechargeService;
import com.kugou.fanxing.recharge.thrift.WxPayAndContractRequest;
import com.kugou.fanxing.recharge.thrift.WxPayAndContractResponse;
import com.kugou.fanxing.recharge.thrift.WxRechargeThriftService;
import com.kugou.fanxing.recharge.util.JsonResult;
import com.kugou.rpc.exception.ServerException;
import com.kugou.rpc.server.thrift.annotation.ThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR>
 * @Date 2024/3/21 12:24
 */
@Slf4j
@ThriftService("/platform_recharge_service/thrift/wxRechargeThriftService")
public class WxRechargeThriftServiceImpl implements WxRechargeThriftService.Iface{
    @Autowired
    WxRechargeService wxRechargeService;

    @Override
    public WxPayAndContractResponse payAndContract(WxPayAndContractRequest request) throws TException {
        log.warn("支付并签约请求开始,参数:{}", request);
        try {
            return wxRechargeService.payAndContract(request);
        } catch (BizException e) {
            log.error("支付并签约请求失败,参数：{}", request, e);
            return RpcResultHelper.buildWxPayAndContractResponse(JsonResult.DefaultResultCodeEnum.FAILURE, "");
        } catch (Exception e) {
            log.error("支付并签约请求异常,参数：{}", request, e);
            throw new ServerException(516, e.getMessage(), e);
        }
    }
}
