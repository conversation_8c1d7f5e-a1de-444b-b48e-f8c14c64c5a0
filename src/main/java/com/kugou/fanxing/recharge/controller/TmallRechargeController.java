package com.kugou.fanxing.recharge.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.model.dto.BaseGametopDTO;
import com.kugou.fanxing.recharge.model.dto.TmallRefundNotifyDto;
import com.kugou.fanxing.recharge.model.request.QueryTmallChargeReq;
import com.kugou.fanxing.recharge.model.request.TaobaoCommonRequest;
import com.kugou.fanxing.recharge.model.request.TmallCallbackRequest;
import com.kugou.fanxing.recharge.service.TmallRechargeService;
import com.kugou.fanxing.recharge.service.ValidatingService;
import com.kugou.fanxing.recharge.service.callback.TmallCallbackService;
import com.kugou.fanxing.recharge.service.refund.v2.RefundServiceFactory;
import com.kugou.fanxing.recharge.util.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 天猫充值
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class TmallRechargeController {

    @Autowired
    private ValidatingService validatingService;
    @Autowired
    private TmallRechargeService tmallRechargeService;
    @Autowired
    private TmallCallbackService tmallCallbackService;

    /**
     * 判断天猫商城是否可以使用
     */
    @GetMapping(value = UrlConstants.CAN_TMALL_RECHARGE)
    public JsonResult<Map<String, Object>> canTmallRecharge(QueryTmallChargeReq param) {
        log.warn("酷狗直播天猫判断账号是否可用, param: {}", param);
        String message = validatingService.validateParams(param);
        if (StringUtils.isNotEmpty(message)) {
            return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, Maps.newHashMap());
        }
        return tmallRechargeService.checkTmallCanRecharge(param);
    }

    /**
     * 淘宝游戏充值账号验证接口
     */
    @PostMapping(value = UrlConstants.TAOBAO_GAME_CHARGE_ZC_VERIFY)
    public Map<String, Object> taobaoGameChargeZcVerify(TaobaoCommonRequest commonRequest, HttpServletRequest request) {
        log.warn("淘宝游戏充值账号验证, commonRequest: {}", commonRequest);
        Map<String, Object> dataMap = Maps.newHashMap();
        BaseGametopDTO gametopVerifyDTO = tmallRechargeService.queryRechargeAccountNickname(request);
        dataMap.put("gametopVerify", gametopVerifyDTO);
        return dataMap;
    }

    /**
     * 天猫充值回调接口
     */
    @RequestMapping(value = UrlConstants.TAOBAO_PURCHASE_CALLBACK)
    public String callbackTmall(TmallCallbackRequest tmallCallbackRequest) {
        log.warn("酷狗直播天猫星币充值回调接口，请求参数。tmallCallbackRequest: {}", tmallCallbackRequest);
        JsonResult<Map<String, String>> jsonResult = this.tmallCallbackService.purchaseCoin(tmallCallbackRequest);
        if (!SysResultCode.codeOf(jsonResult.getCode()).isSuccess()) {
            return JSON.toJSONString(jsonResult);
        }
        return "success";
    }

    @GetMapping(value = UrlConstants.CAN_TMALL_RECHARGE_KW)
    public JsonResult<Map<String, Object>> canTmallRechargeKw(QueryTmallChargeReq param) {
        log.warn("酷我直播天猫判断账号是否可用, param: {}", param);
        String message = validatingService.validateParams(param);
        if (StringUtils.isNotEmpty(message)) {
            return JsonResult.result(SysResultCode.RECHARGE_PARAM_ERROR, Maps.newHashMap());
        }
        return tmallRechargeService.checkTmallCanRechargeKw(param);
    }

    @RequestMapping(value = UrlConstants.TAOBAO_PURCHASE_CALLBACK_KW)
    public String callbackTmallKw(TmallCallbackRequest tmallCallbackRequest) {
        log.warn("酷我直播天猫星币充值回调接口，请求参数。tmallCallbackRequest: {}", tmallCallbackRequest);
        JsonResult<Map<String, String>> jsonResult = this.tmallCallbackService.purchaseCoinKw(tmallCallbackRequest);
        if (!SysResultCode.codeOf(jsonResult.getCode()).isSuccess()) {
            return JSON.toJSONString(jsonResult);
        }
        return "success";
    }

    /**
     * 天猫退款回调文档：http://doc.kugou.net/showdoc-master/web/#/8?page_id=1516
     */
    @RequestMapping(value = UrlConstants.TMALL_REFUND_NOTIFY)
    public String tmallRefund(TmallRefundNotifyDto tmallRefundNotifyDto) {
        log.warn("天猫退款，请求参数。tmallRefundNotifyDto: {}", tmallRefundNotifyDto);
        boolean isSuccess = RefundServiceFactory.createRefundService(PayTypeIdEnum.PAY_TYPE_ID_1012.getPayTypeId())
                .receiveNotify(tmallRefundNotifyDto);
        return isSuccess ? "success" : "failure";
    }

}
