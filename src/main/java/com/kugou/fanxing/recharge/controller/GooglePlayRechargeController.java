package com.kugou.fanxing.recharge.controller;

import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.model.dto.GooglePayRefundNotifyDto;
import com.kugou.fanxing.recharge.model.request.CreateGpRequest;
import com.kugou.fanxing.recharge.model.request.FinishGpRequest;
import com.kugou.fanxing.recharge.model.request.RechargeAmountGearRequest;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.model.vo.SeaOutProductVo;
import com.kugou.fanxing.recharge.service.recharge.GooglePlayRechargeService;
import com.kugou.fanxing.recharge.service.refund.v2.RefundServiceFactory;
import com.kugou.fanxing.recharge.util.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
public class GooglePlayRechargeController {

    @Autowired
    private GooglePlayRechargeService googlePlayRechargeService;


    @GetMapping(value = UrlConstants.RECHARGE_AMOUNT_GEAE)
    public JsonResult<List<SeaOutProductVo>> rechargeAmountGear(WebCommonParam commonParam, RechargeAmountGearRequest rechargeAmountGearRequest) {
        log.warn("海外获取支付列表接口, webCommonParam: {}, RechargeAmountGearRequest: {}", commonParam, rechargeAmountGearRequest);
        return JsonResult.result(SysResultCode.SUCCESS, googlePlayRechargeService.getProductList(commonParam, rechargeAmountGearRequest));
    }

    @PostMapping(value = UrlConstants.CREATE_ORDER_FOR_GP)
    public JsonResult<Map<String, Object>> createOrderForIap(WebCommonParam commonParam, CreateGpRequest createGpRequest) {
        log.warn("GOOGLE应用内充值下单接口, webCommonParam: {}, createIapRequest: {}", commonParam, createGpRequest);
        createGpRequest.initRequest(PayTypeIdEnum.PAY_TYPE_ID_521, commonParam);
        Map<String, Object> payload = googlePlayRechargeService.createOrderForGp(commonParam, createGpRequest);
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

    @PostMapping(value = UrlConstants.FINISH_ORDER_FOR_GP)
    public JsonResult<Map<String, Object>> finishOrderForIap(WebCommonParam commonParam, FinishGpRequest finishIapRequest) {
        log.warn("GOOGLE应用内充值上报接口, webCommonParam: {}, finishIapRequest: {}", commonParam, finishIapRequest);
        Map<String, Object> payload = googlePlayRechargeService.finishOrderForGp(commonParam, finishIapRequest);
        return JsonResult.result(SysResultCode.SUCCESS, payload);
    }

    /**
     * <a href="http://kapi.kugou.net/project/343/interface/api/100976">google play退款通知</a>
     */
    @RequestMapping(UrlConstants.GOOGLE_PAY_REFUND_NOTIFY)
    public String googlePayRefundNotify(GooglePayRefundNotifyDto googlePayRefundNotifyDto) {
        boolean isSuccess = RefundServiceFactory.createRefundService(PayTypeIdEnum.PAY_TYPE_ID_521.getPayTypeId())
                .receiveNotify(googlePayRefundNotifyDto);
        return isSuccess ? "success" : "failure";
    }
}
