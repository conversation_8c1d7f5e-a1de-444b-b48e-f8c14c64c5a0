package com.kugou.fanxing.recharge.controller.callback;

import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.model.dto.CoinCallbackDTO;
import com.kugou.fanxing.recharge.service.callback.GameCallbackService;
import com.kugou.fanxing.recharge.util.CallbackRespHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 游戏充值回调
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class GameRechargeCallbackController {

    @Autowired
    private GameCallbackService gameCallbackService;

    @RequestMapping(UrlConstants.GAME_PURCHASE_CURRENCY_CALLBACK)
    public String purchaseCurrencyCallback(CoinCallbackDTO coinCallbackDTO) {
        log.warn("游戏充值购买货币回调，请求参数。coinCallbackDTO: {}", coinCallbackDTO);
        SysResultCode sysResultCode = this.gameCallbackService.purchaseCurrencyCallback(coinCallbackDTO);
        return CallbackRespHelper.buildResp(sysResultCode);
    }
}
