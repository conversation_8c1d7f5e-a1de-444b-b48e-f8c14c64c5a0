package com.kugou.fanxing.recharge.constant;

import lombok.Getter;
import lombok.ToString;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@ToString
public enum BizTypeEnum {

    COIN("coin", "星币"),
    ;

    private final String bizType;
    private final String desc;

    BizTypeEnum(String bizType, String desc) {
        this.bizType = bizType;
        this.desc = desc;
    }

    public static Optional<BizLineEnum> of(String bizLine) {
        return Stream.of(BizLineEnum.values())
                .filter(bizLineEnum -> bizLineEnum.getCode().equals(bizLine))
                .findFirst();
    }
}
