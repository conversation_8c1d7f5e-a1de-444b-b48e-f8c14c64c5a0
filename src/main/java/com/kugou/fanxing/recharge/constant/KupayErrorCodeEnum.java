package com.kugou.fanxing.recharge.constant;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 酷狗支付网关错误码
 *
 * <AUTHOR>
 */
@Getter
public enum KupayErrorCodeEnum {
    ERROR_CODE_20001(20001, "系统错误"),
    ERROR_CODE_20006(20006, "非法签名"),
    ERROR_CODE_20010(20010, "参数错误"),
    ERROR_CODE_20017(20017, "token错误"),
    ERROR_CODE_30901(30901, "参数值不在允许范围内"),
    ERROR_CODE_30902(30902, "与第三方签名不一致"),
    ERROR_CODE_30903(30903, "添加订单记录失败"),
    ERROR_CODE_30904(30904, "查询订单记录失败"),
    ERROR_CODE_30905(30905, "第三方回调网关时更新订单失败"),
    ERROR_CODE_30906(30906, "网关回调业务层失败"),
    ERROR_CODE_30907(30907, "发送请求超时或失败"),
    ERROR_CODE_30908(30908, "第三方验证支付信息错误"),
    ERROR_CODE_30909(30909, "非法消息通知"),
    ERROR_CODE_30910(30910, "appid请求量超出阀值"),
    ERROR_CODE_30911(30911, "相同appid并发请求被禁止"),
    ERROR_CODE_30912(30912, "签约号错误"),
    ERROR_CODE_30913(30913, "获取预支付订单号失败"),
    ERROR_CODE_30914(30914, "更新签约号状态失败"),
    ERROR_CODE_30915(30915, "签约号不存在"),
    ERROR_CODE_30916(30916, "des解密失败"),
    ERROR_CODE_30917(30917, "密文传输过程中已被修改"),
    ERROR_CODE_30918(30918, "业务方回调测试错误"),
    ERROR_CODE_30919(30919, "业务方回调测试成功"),
    ERROR_CODE_30920(30920, "业务层与网关兑换比例后费用为0"),
    ERROR_CODE_30921(30921, "报警信息,业务出现小概率事件,可能业务不正常"),
    ERROR_CODE_30922(30922, "同步支付失败,常用于卡密支付,如天下通"),
    ERROR_CODE_30923(30923, "签约号与用户id不匹配"),
    ERROR_CODE_30924(30924, "发送请求返回业务错误信息"),
    ERROR_CODE_30925(30925, "订单已经支付成功,不可以再重复支付"),
    ERROR_CODE_30926(30926, "查询记录不存在或查询失败(不一定是订单记录)"),
    ERROR_CODE_30927(30927, "流水日志,主要用于记录各执行步骤耗时"),
    ERROR_CODE_30928(30928, "同步支付失败"),
    ERROR_CODE_30929(30929, "请求已失效"),
    ERROR_CODE_30930(30930, "同步支付失败"),
    ERROR_CODE_30931(30931, "回调IP不在白名单中 或 没有访问权限"),
    ERROR_CODE_30932(30932, "非成功特殊回调状态(常用于paypal)"),
    ERROR_CODE_30933(30933, "调用接口生成二维码失败"),
    ERROR_CODE_30934(30934, "调用第三方接口,发送支付短信验证码失败"),
    ERROR_CODE_30935(30935, "传入金额与卡密(产品代码)金额不一致(比如联通包月接口)"),
    ERROR_CODE_30936(30936, "传入参数支持,但参数值不在范围内"),
    ERROR_CODE_30937(30937, "订单号为空"),
    ERROR_CODE_30938(30938, "订单信息已改变(同一订单号带不同参数访问)"),
    ERROR_CODE_30939(30939, "确认订购信息失败(电信爱游戏接口)"),
    ERROR_CODE_30940(30940, "添加包月记录失败"),
    ERROR_CODE_30941(30941, "业务层没有检查notify_id 或 notify_id非法."),
    ERROR_CODE_30942(30942, "更新回调业务层状态失败."),
    ERROR_CODE_30943(30943, "状态不可到达.(出现这种情况应该是if-else太多，判断状态遗漏 或者第三方回调状态有改变)"),
    ERROR_CODE_30944(30944, "gateway 更新订单状态失败"),
    ERROR_CODE_30945(30945, "获取默认配置失败(出现这个错误有可能是配置文件配置有错)"),
    ERROR_CODE_30946(30946, "签名方式非预期(出现这个错误有可能是支付宝不再用md5签名)"),
    ERROR_CODE_30947(30947, "检查第三方notify_id失败(一般支付宝才有)"),
    ERROR_CODE_30948(30948, "解约失败"),
    ERROR_CODE_30949(30949, "代扣款失败(支付宝)"),
    ERROR_CODE_30950(30950, "查询第三方得到的签约状态与数据库记录的状态不一致"),
    ERROR_CODE_30951(30951, "添加队列消息失败(联通包月)"),
    ERROR_CODE_30952(30952, "后台队列任务进程异常(可能连不上redis或进程不启动或进程数过多)"),
    ERROR_CODE_30953(30953, "聚财通扫码支付返回的微信支付链接为空"),
    ERROR_CODE_30954(30954, "第三方接口返回非预期值"),
    ERROR_CODE_30955(30955, "mod,ct,ac缺少必要的参数"),
    ERROR_CODE_30956(30956, "类加载失败"),
    ERROR_CODE_30957(30957, "(联通)业务向网关同步订单失败"),
    ERROR_CODE_30958(30958, "(联通)业务向网关同步订单失败 订单号重复"),
    ERROR_CODE_30959(30959, "(联通)业务向网关同步订单业务状态不对"),
    ERROR_CODE_30960(30960, "(电信)产品重复订购"),
    ERROR_CODE_30961(30961, "(电信)验证码错误"),
    ERROR_CODE_30962(30962, "PHP错误日志"),
    ERROR_CODE_30963(30963, "通用参数错误"),
    ERROR_CODE_30964(30964, "内部网络繁忙"),
    ERROR_CODE_30965(30965, "业务操作失败，如支付宝提现失败，一般调对应第三方返回失败"),
    ERROR_CODE_30966(30966, "权限不足，一般是biz_appid未接入"),
    ERROR_CODE_30967(30967, "订单号重复"),
    ERROR_CODE_30968(30968, "超过提现次数"),
    ERROR_CODE_30969(30969, ""),
    ERROR_CODE_30970(30970, ""),
    ERROR_CODE_30971(30971, ""),
    ERROR_CODE_30972(30972, "业务订单回查校验不存在"),
    ERROR_CODE_30973(30973, "并发锁计数器出现负数，用于记录切换 redis3.0 集群时的日志"),
    ERROR_CODE_30974(30974, "符合服务降级配置，请求被拒绝"),
    ERROR_CODE_30975(30975, "回调业务次数大于1"),
    ERROR_CODE_30976(30976, "业务回调地址访问出错，业务不可用"),
    ERROR_CODE_30977(30977, "回调业务失败（主动回调接口的定时任务 callback_business_queue.php）"),
    ERROR_CODE_30978(30978, "主动请求回调接口队列过长，进队失败"),
    ERROR_CODE_30979(30979, "主动请求回调接口失败"),
    ERROR_CODE_30980(30980, "超时记录"),
    ERROR_CODE_30981(30981, "回调金额与订单不一致!!!"),
    ERROR_CODE_30982(30982, "支付宝网页支付，http_referer头为空，金额大于1000禁止下单"),
    ERROR_CODE_30983(30983, "支付宝网页支付，http_referer头不为空，不是来自酷狗的域名"),
    ERROR_CODE_30984(30984, "第三方代付下单请求失败(深田)"),
    ERROR_CODE_30985(30985, "命中风控拦截策略")
    ;
    private final int errorCode;
    private final String errorMsg;

    KupayErrorCodeEnum(int errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public static String errorMsgOf(int errorCode) {
        Optional<KupayErrorCodeEnum> optionalKupayErrorCodeEnum = Arrays.stream(KupayErrorCodeEnum.values())
                .filter(kupayErrorCodeEnum -> kupayErrorCodeEnum.getErrorCode() == errorCode)
                .findAny();
        return optionalKupayErrorCodeEnum.isPresent() ? optionalKupayErrorCodeEnum.get().getErrorMsg() : "";
    }
}
