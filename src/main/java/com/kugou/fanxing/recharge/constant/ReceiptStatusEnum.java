package com.kugou.fanxing.recharge.constant;

import lombok.Getter;
import lombok.ToString;

import java.util.Optional;
import java.util.stream.Stream;

/**
 * https://developer.apple.com/documentation/appstorereceipts/status
 */
@Getter
@ToString
public enum ReceiptStatusEnum {

    STATUS_0(0, "This receipt is valid"),

    STATUS_21000(21000, "The request to the App Store was not made using the HTTP POST request method."),
    STATUS_21001(21001, "This status code is no longer sent by the App Store."),
    STATUS_21002(21002, "The data in the receipt-data property was malformed or the service experienced a temporary issue. Try again."),
    STATUS_21003(21003, "The receipt could not be authenticated."),
    STATUS_21004(21004, "The shared secret you provided does not match the shared secret on file for your account."),
    STATUS_21005(21005, "The receipt server was temporarily unable to provide the receipt. Try again."),
    STATUS_21006(21006, "This receipt is valid but the subscription has expired. When this status code is returned to your server, the receipt data is also decoded and returned as part of the response. Only returned for iOS 6-style transaction receipts for auto-renewable subscriptions."),
    STATUS_21007(21007, "This receipt is from the test environment, but it was sent to the production environment for verification."),
    STATUS_21008(21008, "This receipt is from the production environment, but it was sent to the test environment for verification."),
    STATUS_21009(21009, "Internal data access error. Try again later."),
    STATUS_21010(21010, "The user account cannot be found or has been deleted."),

    // Status codes 21100-21199 are various internal data access errors.
    STATUS_21100(21100, "Internal data access error. Try again later."),
    STATUS_21101(21101, "Internal data access error. Try again later."),
    STATUS_21102(21102, "Internal data access error. Try again later."),
    STATUS_21103(21103, "Internal data access error. Try again later."),
    STATUS_21104(21104, "Internal data access error. Try again later."),
    STATUS_21105(21105, "Internal data access error. Try again later."),
    STATUS_21106(21106, "Internal data access error. Try again later."),
    STATUS_21107(21107, "Internal data access error. Try again later."),
    STATUS_21108(21108, "Internal data access error. Try again later."),
    STATUS_21109(21109, "Internal data access error. Try again later."),
    STATUS_21110(21110, "Internal data access error. Try again later."),
    STATUS_21111(21111, "Internal data access error. Try again later."),
    STATUS_21112(21112, "Internal data access error. Try again later."),
    STATUS_21113(21113, "Internal data access error. Try again later."),
    STATUS_21114(21114, "Internal data access error. Try again later."),
    STATUS_21115(21115, "Internal data access error. Try again later."),
    STATUS_21116(21116, "Internal data access error. Try again later."),
    STATUS_21117(21117, "Internal data access error. Try again later."),
    STATUS_21118(21118, "Internal data access error. Try again later."),
    STATUS_21119(21119, "Internal data access error. Try again later."),
    STATUS_21120(21120, "Internal data access error. Try again later."),
    STATUS_21121(21121, "Internal data access error. Try again later."),
    STATUS_21122(21122, "Internal data access error. Try again later."),
    STATUS_21123(21123, "Internal data access error. Try again later."),
    STATUS_21124(21124, "Internal data access error. Try again later."),
    STATUS_21125(21125, "Internal data access error. Try again later."),
    STATUS_21126(21126, "Internal data access error. Try again later."),
    STATUS_21127(21127, "Internal data access error. Try again later."),
    STATUS_21128(21128, "Internal data access error. Try again later."),
    STATUS_21129(21129, "Internal data access error. Try again later."),
    STATUS_21130(21130, "Internal data access error. Try again later."),
    STATUS_21131(21131, "Internal data access error. Try again later."),
    STATUS_21132(21132, "Internal data access error. Try again later."),
    STATUS_21133(21133, "Internal data access error. Try again later."),
    STATUS_21134(21134, "Internal data access error. Try again later."),
    STATUS_21135(21135, "Internal data access error. Try again later."),
    STATUS_21136(21136, "Internal data access error. Try again later."),
    STATUS_21137(21137, "Internal data access error. Try again later."),
    STATUS_21138(21138, "Internal data access error. Try again later."),
    STATUS_21139(21139, "Internal data access error. Try again later."),
    STATUS_21140(21140, "Internal data access error. Try again later."),
    STATUS_21141(21141, "Internal data access error. Try again later."),
    STATUS_21142(21142, "Internal data access error. Try again later."),
    STATUS_21143(21143, "Internal data access error. Try again later."),
    STATUS_21144(21144, "Internal data access error. Try again later."),
    STATUS_21145(21145, "Internal data access error. Try again later."),
    STATUS_21146(21146, "Internal data access error. Try again later."),
    STATUS_21147(21147, "Internal data access error. Try again later."),
    STATUS_21148(21148, "Internal data access error. Try again later."),
    STATUS_21149(21149, "Internal data access error. Try again later."),
    STATUS_21150(21150, "Internal data access error. Try again later."),
    STATUS_21151(21151, "Internal data access error. Try again later."),
    STATUS_21152(21152, "Internal data access error. Try again later."),
    STATUS_21153(21153, "Internal data access error. Try again later."),
    STATUS_21154(21154, "Internal data access error. Try again later."),
    STATUS_21155(21155, "Internal data access error. Try again later."),
    STATUS_21156(21156, "Internal data access error. Try again later."),
    STATUS_21157(21157, "Internal data access error. Try again later."),
    STATUS_21158(21158, "Internal data access error. Try again later."),
    STATUS_21159(21159, "Internal data access error. Try again later."),
    STATUS_21160(21160, "Internal data access error. Try again later."),
    STATUS_21161(21161, "Internal data access error. Try again later."),
    STATUS_21162(21162, "Internal data access error. Try again later."),
    STATUS_21163(21163, "Internal data access error. Try again later."),
    STATUS_21164(21164, "Internal data access error. Try again later."),
    STATUS_21165(21165, "Internal data access error. Try again later."),
    STATUS_21166(21166, "Internal data access error. Try again later."),
    STATUS_21167(21167, "Internal data access error. Try again later."),
    STATUS_21168(21168, "Internal data access error. Try again later."),
    STATUS_21169(21169, "Internal data access error. Try again later."),
    STATUS_21170(21170, "Internal data access error. Try again later."),
    STATUS_21171(21171, "Internal data access error. Try again later."),
    STATUS_21172(21172, "Internal data access error. Try again later."),
    STATUS_21173(21173, "Internal data access error. Try again later."),
    STATUS_21174(21174, "Internal data access error. Try again later."),
    STATUS_21175(21175, "Internal data access error. Try again later."),
    STATUS_21176(21176, "Internal data access error. Try again later."),
    STATUS_21177(21177, "Internal data access error. Try again later."),
    STATUS_21178(21178, "Internal data access error. Try again later."),
    STATUS_21179(21179, "Internal data access error. Try again later."),
    STATUS_21180(21180, "Internal data access error. Try again later."),
    STATUS_21181(21181, "Internal data access error. Try again later."),
    STATUS_21182(21182, "Internal data access error. Try again later."),
    STATUS_21183(21183, "Internal data access error. Try again later."),
    STATUS_21184(21184, "Internal data access error. Try again later."),
    STATUS_21185(21185, "Internal data access error. Try again later."),
    STATUS_21186(21186, "Internal data access error. Try again later."),
    STATUS_21187(21187, "Internal data access error. Try again later."),
    STATUS_21188(21188, "Internal data access error. Try again later."),
    STATUS_21189(21189, "Internal data access error. Try again later."),
    STATUS_21190(21190, "Internal data access error. Try again later."),
    STATUS_21191(21191, "Internal data access error. Try again later."),
    STATUS_21192(21192, "Internal data access error. Try again later."),
    STATUS_21193(21193, "Internal data access error. Try again later."),
    STATUS_21194(21194, "Internal data access error. Try again later."),
    STATUS_21195(21195, "Internal data access error. Try again later."),
    STATUS_21196(21196, "Internal data access error. Try again later."),
    STATUS_21197(21197, "Internal data access error. Try again later."),
    STATUS_21198(21198, "Internal data access error. Try again later."),
    STATUS_21199(21199, "Internal data access error. Try again later.");

    private final int code;
    private final String desc;

    ReceiptStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Optional<ReceiptStatusEnum> of(int code) {
        return Stream.of(ReceiptStatusEnum.values())
                .filter(receiptStatusEnum -> receiptStatusEnum.getCode() == code)
                .findFirst();
    }

    public static boolean isValidReceipt(int code) {
        return STATUS_0.getCode() == code;
    }

}
