package com.kugou.fanxing.recharge.constant;

import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

/**
 * 大额充值申请类型
 * <p>
 * 1 - 官网合并返点
 * 2 - 线下充值流程
 *
 * <AUTHOR>
 */
@Getter
public enum ApplyTypeEnum {

    /**
     * 大额充值-官网合并返点
     */
    APPLY_TYPE_1(1, "大额充值-官网合并返点"),
    /**
     * 线下充值-支付宝对公转账
     */
    APPLY_TYPE_2(2, "线下充值-支付宝对公转账"),
    /**
     * 线下充值-银行卡对公转账
     */
    APPLY_TYPE_3(3, "线下充值-银行卡对公转账")
    ;

    private final int status;
    private final String label;

    ApplyTypeEnum(int status, String label) {
        this.status = status;
        this.label = label;
    }

    public static Optional<ApplyTypeEnum> statusOf(int status) {
        return Stream.of(ApplyTypeEnum.values())
                .filter(approveStatusEnum -> approveStatusEnum.status == status)
                .findFirst();
    }
}
