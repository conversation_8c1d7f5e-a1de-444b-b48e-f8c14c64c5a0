package com.kugou.fanxing.recharge.constant;

import lombok.Getter;
import org.apache.commons.lang3.ArrayUtils;

import java.util.Arrays;
import java.util.Optional;

@Getter
public enum PidEnum {

    PID_001(1, new int[]{5}, "独立繁星android平台"),
    PID_002(2, new int[]{6}, "独立繁星ios平台"),
    PID_003(3, new int[]{3}, "kugou ipad 平台"),
    PID_005(5, new int[]{0, 1}, "kugou安卓平台"),
    PID_006(6, new int[]{2}, "kugou ios 平台"),

    PID_010(10, new int[]{10}, "酷狗 ipad"),
    PID_011(11, new int[]{4}, "kugou android pad平台"),
    PID_012(12, new int[]{8}, "独立繁星android pad平台"),
    PID_015(15, new int[]{15}, "独立繁星ios平台备用1"),
    PID_016(16, new int[]{16}, "独立繁星ios平台备用2"),
    PID_018(18, new int[]{18, 207}, "手机繁星H5页"),

    PID_035(35, new int[]{35}, "独立繁星安卓极速版"),
    PID_036(36, new int[]{36}, "独立繁星IOS极速版"),

    PID_085(85, new int[]{85}, "移动直播PC化，PCWEB"),
    PID_086(86, new int[]{86}, "移动直播PC化，PC内嵌页"),
    PID_087(87, new int[]{87}, "移动直播PC化，PC客户端"),

    PID_111(111, new int[]{111}, "安卓繁星备用包（繁星直播1)"),
    PID_112(112, new int[]{112}, "安卓繁星备用包（繁星直播2)"),
    PID_113(113, new int[]{113}, "安卓繁星备用包（繁星直播3)"),
    PID_114(114, new int[]{114}, "安卓繁星备用包（繁星直播4)"),

    PID_121(121, new int[]{121}, "iOS繁星备用包（Pro）"),
    PID_122(122, new int[]{122}, "iOS繁星备用包(星秀场)"),
    PID_123(123, new int[]{123}, "iOS繁星备用包(星live1)"),
    PID_124(124, new int[]{124}, "iOS繁星备用包(星live2)"),
    ;

    /**
     * 后端对应平台编号
     */
    private final int pid;
    /**
     * 前端传递平台编号
     */
    private final int[] platformId;
    /**
     * 平台编号描述信息
     */
    private final String desc;

    PidEnum(int pid, int[] platformId, String desc) {
        this.pid = pid;
        this.platformId = platformId;
        this.desc = desc;
    }

    public static int getPidByPlatformId(int platformId) {
        Optional<PidEnum> optionalFxPidEnum = Arrays.stream(PidEnum.values())
                .filter(fxPidEnum -> ArrayUtils.contains(fxPidEnum.getPlatformId(), platformId))
                .findAny();
        return optionalFxPidEnum.map(PidEnum::getPid).orElseGet(PID_005::getPid);
    }
}
