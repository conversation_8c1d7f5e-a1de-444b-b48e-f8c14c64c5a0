package com.kugou.fanxing.recharge.constant;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum DeductCoinSuccessCodeEnum {
    SUCCESS_0(0L, "成功"),
    SUCCESS_110000002(110000002L, "已经结算"),
    SUCCESS_110104002(110104002L, "已经结算,重复结算，参数不同错误"),
    SUCCESS_1009909(110000002L, "第二次请求幂等"),
    ;
    private final long ret;
    private final String msg;

    DeductCoinSuccessCodeEnum(long errorCode, String errorMsg) {
        this.ret = errorCode;
        this.msg = errorMsg;
    }

    public static DeductCoinSuccessCodeEnum getByRet(long ret) {
        return Arrays.stream(DeductCoinSuccessCodeEnum.values())
                .filter(kupayErrorCodeEnum -> kupayErrorCodeEnum.getRet() == ret)
                .findAny().orElse(null);
    }

    public static boolean isSuccess(long ret) {
        return Arrays.stream(DeductCoinSuccessCodeEnum.values())
                .anyMatch(kupayErrorCodeEnum -> kupayErrorCodeEnum.getRet() == ret);
    }
}
