package com.kugou.fanxing.recharge.constant;

import lombok.Getter;

/**
 * 提现订单状态
 *
 * <AUTHOR>
 **/
@Getter
public enum UserYearStatJobRunStatus {
    /**
     * 初始状态
     */
    PROCESS(0, "初始状态"),

    DOWNLOADING(1, "下载中"),

    DOWNLOAD_SUCCESS(2, "下载成功"),
    WRITING(3, "写入db中"),
    SUCCESS(4, "写入db成功")
    ;

    private final int value;
    private final String label;

    UserYearStatJobRunStatus(int value, String label) {
        this.value = value;
        this.label = label;
    }
}
