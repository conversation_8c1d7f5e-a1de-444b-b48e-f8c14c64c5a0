package com.kugou.fanxing.recharge.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@AllArgsConstructor
public enum SourceIdEnum {

    FX_TYPE(0, "秀场"),
    YS_TYPE(1, "鱼声"),
    CC_TYPE(2, "唱唱");

    private final int code;
    private final String desc;

    public static boolean isFxType(int sourceId) {
        return sourceId == FX_TYPE.getCode();
    }

    public static boolean isYsType(int sourceId) {
        return sourceId == YS_TYPE.getCode();
    }

    public static boolean isCcType(int sourceId) {
        return sourceId == CC_TYPE.getCode();
    }
}
