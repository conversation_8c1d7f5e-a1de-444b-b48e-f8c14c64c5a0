package com.kugou.fanxing.recharge.constant;

import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

/**
 * 返点状态
 * 0 - 待审核
 * 1 - 通过
 * 2 - 未通过
 * 3 - 已返点
 * 4 - 合并返点
 *
 * <AUTHOR>
 */
@Getter
public enum RebateStatusEnum {
    /**
     * 待审核
     */
    REBATE_STATUS_0((short) 0, "待审核"),
    /**
     * 通过
     */
    REBATE_STATUS_1((short) 1, "通过"),
    /**
     * 未通过
     */
    REBATE_STATUS_2((short) 2, "未通过"),
    /**
     * 已返点
     */
    REBATE_STATUS_3((short) 3, "已返点"),
    /**
     * 合并返点
     */
    REBATE_STATUS_4((short) 4, "合并返点");

    private final short status;
    private final String label;

    RebateStatusEnum(short status, String label) {
        this.status = status;
        this.label = label;
    }

    public static Optional<RebateStatusEnum> statusOf(short status) {
        return Stream.of(RebateStatusEnum.values())
                .filter(item -> item.status == status)
                .findFirst();
    }

}
