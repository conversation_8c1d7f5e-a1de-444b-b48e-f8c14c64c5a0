package com.kugou.fanxing.recharge.constant;

import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;

@Getter
@ToString
public enum DrawTypeEnum {

    DRAW_TYPE_ALIPAY(1, "支付宝提现"),
    DRAW_TYPE_WECHAT(2, "微信提现"),
    DRAW_TYPE_ST_WECHAT(32, "微信提现(第三方深田代付)");

    private final int code;
    private final String desc;

    DrawTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 是否支持的提现类型
     *
     * @param code 提现类型代码
     * @return 是否支持的提现类型
     */
    public static boolean isSupportDrawCash(int code) {
        return Arrays.stream(DrawTypeEnum.values()).anyMatch(drawTypeEnum -> drawTypeEnum.getCode() == code);
    }

    public static boolean isAlipayDrawCash(int code) {
        return DRAW_TYPE_ALIPAY.getCode() == code;
    }

    public static boolean isWechatDrawCash(int code) {
        return DRAW_TYPE_WECHAT.getCode() == code;
    }

    public static boolean isStWechatDrawCash(int code) {
        return DRAW_TYPE_ST_WECHAT.getCode() == code;
    }

    public static int[] getWechatDrawTypeCode() {
        return new int[] { DRAW_TYPE_WECHAT.getCode(), DRAW_TYPE_ST_WECHAT.getCode() };
    }
}
