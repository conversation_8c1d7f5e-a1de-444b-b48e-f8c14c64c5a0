package com.kugou.fanxing.recharge.constant;

import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public enum AdminAdjustCoinResult {

    INITIAL(0, "initial"),
    SUCCESS(1,"success"),
    UNKNOWN(-1, "unknown"),
    FAILURE(2, "failure");

    private final int code;
    private final String desc;

    AdminAdjustCoinResult(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean isSuccess(int code) {
        return SUCCESS.getCode() == code;
    }

    public static boolean isFailure(int code) {
        return FAILURE.getCode() == code;
    }

    public static boolean isUnknown(int code) {
        return UNKNOWN.getCode() == code;
    }

    public static boolean isInitial(int code) {
        return INITIAL.getCode() == code;
    }
}
