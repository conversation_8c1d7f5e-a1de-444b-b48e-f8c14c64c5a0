package com.kugou.fanxing.recharge.constant;

import lombok.Getter;

/**
 * 提现订单状态
 *
 * <AUTHOR>
 **/
@Getter
public enum WithdrawOrderStatusEnum {

    INITIAL(-1, "提现已受理"),
    PROCESS(0, "网关处理中"),
    SUCCESS(1, "提现成功"),
    FAILURE(2, "提现失败"),
    CANCEL(3, "提现取消")
    ;

    private final int value;
    private final String label;

    WithdrawOrderStatusEnum(int value, String label) {
        this.value = value;
        this.label = label;
    }
}
