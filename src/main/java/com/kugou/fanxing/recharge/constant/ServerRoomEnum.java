package com.kugou.fanxing.recharge.constant;

import lombok.Getter;

/**
 * 机房信息
 *
 * <AUTHOR>
 */
@Getter
public enum ServerRoomEnum {

    /**
     * 北京兆维
     */
    SERVER_ROOM_BJZW(0, "bjzw"),
    /**
     * 北京亦庄
     */
    SERVER_ROOM_BJYZ(1, "bjyz"),
    /**
     * 广州四季青
     */
    SERVER_ROOM_GZ(2, "gz");

    /**
     * 机房编号
     */
    private final int id;

    /**
     * 机房名称
     */
    private final String name;

    ServerRoomEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }


}
