package com.kugou.fanxing.recharge.constant;

/**
 * 充值服务路径
 *
 * <AUTHOR>
 */
public class UrlConstants {

    private UrlConstants() {}

    // 公网接口（http）
    public static final String GET_RECHARGE_LIST = "/recharge/api/v1/getRechargeList";
    public static final String GET_RECHARGE_FOR_OTHER_LIST = "/recharge/api/v1/getRechargeForOtherList";
    public static final String GET_RECHARGE_OTHER_FOR_ME_LIST = "/recharge/api/v1/getRechargeOtherForMeList";
    public static final String GET_GAME_RECHARGE_LIST = "/recharge/api/v1/getGameRechargeList";
    public static final String DEFAULT_RECHARGE_OPTIONS = "/recharge/api/v1/defaultRechargeOptions";
    public static final String RECHARGE_PRESENT_QUERY = "/recharge/api/v1/rechargePresentQuery";
    public static final String RECHARGE_ORDER_STATUS = "/recharge/api/v1/getOrderStatus";
    public static final String RECHARGE_ORDER_STATUS_V2 = "/recharge/api/v2/getOrderStatus";
    public static final String TAOBAO_GAME_CHARGE_ZC_VERIFY = "/recharge/api/v1/taobaoGameChargeZcVerify";
    public static final String DEFAULT_RECHARGE_OPTIONS_BY_PID = "/recharge/api/v1/defaultRechargeOptionsByPid";
    public static final String GET_RECHARGE_AMOUNT_GEAR = "/recharge/api/v1/getRechargeAmountGear";
    public static final String GET_RECHARGE_PRESENT_LIST = "/recharge/api/v1/getRechargePresentList";
    public static final String GET_ORDER_FOR_WX_XCX = "/recharge/api/v1/getOrderForWxMiniProgram";
    public static final String GET_ORDER_FOR_WX_GZH = "/recharge/api/v1/getOrderForWxgzh";
    public static final String GET_ORDER_FOR_PAY_PAL = "/recharge/api/v1/getOrderForPayPal";
    public static final String GET_ORDER_FOR_BANK = "/recharge/api/v1/getOrderForBank";
    public static final String GET_ORDER_FOR_BANK_V2 = "/recharge/api/v2/getOrderForBank";
    public static final String GET_ORDER_FOR_ALIPAY = "/recharge/api/v1/getOrderForAlipay";
    public static final String GET_ORDER_FOR_ALIPAY_V2 = "/recharge/api/v2/getOrderForAlipay";
    public static final String GET_ORDER_FOR_ALIPAY_QR = "/recharge/api/v1/getOrderForAlipayQR";
    public static final String GET_ORDER_FOR_WECHAT = "/recharge/api/v1/getOrderForWechat";
    public static final String REFRESH_WECHAT_QRCODE = "/recharge/api/v1/refreshWechatQrCode";
    public static final String OFFLINE_RECHARGE_SWITCH = "/recharge/api/v1/offline/switch";
    public static final String OFFLINE_RECHARGE_APPLY_BANK = "/recharge/api/v1/offline/applyBank";
    public static final String OFFLINE_RECHARGE_APPLY_ALIPAY = "/recharge/api/v1/offline/applyAlipay";
    public static final String BIG_RECHARGE_APPLY_LIST = "/recharge/api/v1/offline/applyList";
    public static final String BIG_RECHARGE_REBATE_APPLY = "/recharge/api/v1/offline/rebate/apply";
    public static final String OFFLINE_RECHARGE_KUPAY_RECHECK = "/intranet/api/v1/offline/kupayRecheck";
    public static final String RECHARGE_GET_MPAY_LIST = "/recharge/api/v1/getMPayList";
    public static final String RECHARGE_PLAT_ALIPAY_MOBILE = "/RechargePlat/RechargeService/RechargePayService/aliPayM";
    public static final String RECHARGE_PLAT_BANKPAY_MOBILE = "/RechargePlat/RechargeService/RechargePayService/bankPayM";
    public static final String RECHARGE_PLAT_WECHAT_MOBILE = "/RechargePlat/RechargeService/RechargePayService/weixinPayM";
    public static final String RECHARGE_PLAT_WECHAT_H5 = "/recharge/api/v1/getOrderForWechatH";
    public static final String RECHARGE_PLAT_ALIPAY_H5 = "/recharge/api/v1/getOrderForAlipayH";
    public static final String GET_USER_COIN_FROM_MASTER = "/recharge/api/v1/getUserCoinM";
    public static final String GET_USER_COIN_FROM_V2 = "/recharge/api/v2/getUserCoin";
    public static final String GET_COUPON_LIST_PC = "/recharge/api/v1/getCouponList";
    public static final String GET_COUPON_LIST_MOBILE = "/recharge/api/v1/getCouponListM";
    public static final String GET_RECHARGE_REBATE = "/recharge/api/v1/getRechargeRebate";
    public static final String GET_ORDER_FOR_WECHAT_MOBILE = "/recharge/api/v1/weixinPayM";
    public static final String GET_ORDER_FOR_ALIPAY_MOBILE = "/recharge/api/v1/aliPayM";
    public static final String GET_ORDER_FOR_BANKPAY_MOBILE = "/recharge/api/v1/bankPayM";
    public static final String GET_ORDER_FOR_WECHAT_MOBILE_V2 = "/recharge/api/v2/weixinPayM";
    public static final String GET_ORDER_FOR_WECHAT_APP = "/recharge/api/v1/wechatApp";
    public static final String GET_ORDER_FOR_ALIPAY_MOBILE_V2 = "/recharge/api/v2/aliPayM";
    public static final String GET_ORDER_FOR_ALIPAY_APP = "/recharge/api/v1/alipayApp";
    public static final String GET_ORDER_FOR_ALIPAY_APP_SING = "/recharge/api/v2/sing/alipayApp";
    public static final String GET_ORDER_FOR_WECHAT_APP_SING = "/recharge/api/v2/sing/wechatApp";
    public static final String GET_ORDER_FOR_BANKPAY_MOBILE_V2 = "/recharge/api/v2/bankPayM";
    public static final String GET_FAPIAO_RECHARGE_LIST = "/recharge/api/v1/fapiao/rechargeList";
    public static final String GET_RECHARGE_SWITCH_LIST = "/recharge/api/v1/switchList";
    public static final String GET_RECHARGE_CONFIG_LIST = "/recharge/api/v1/configList";
    public static final String PRE_ORDERS_LIST = "/recharge/api/v1/preordersList";
    public static final String GAME_WECHAT_H5 = "/recharge/api/v1/game/wechatH5";
    public static final String GAME_ALIPAY_H5 = "/recharge/api/v1/game/alipayH5";
    public static final String USER_RECHARGE_STATE = "/recharge/api/v1/userRechargeState";
    public static final String NOVICE_RECHARGE_CONFIG_NEW = "/recharge/api/v1/noviceRechargeConfigNew";
    public static final String GET_RECHARGED_USER_INFO = "/recharge/api/v1/agent/recharge/getUserInfo";
    public static final String GET_DF_RECHARGE_CONFIG = "/recharge/api/v1/getFansPayConfig";
    public static final String CUSTOM_RECHARGE_OPTIONS = "/recharge/api/v1/customRechargeOptions";
    public static final String GRAY_RECHARGE_PAGE_STYLE = "/recharge/api/v1/rechargePageStyle";
    public static final String GRAY_RECHARGE_NEW_PAGE = "/recharge/api/v1/newRechargePage";
    public static final String SHOW_HISTORY_FAPIAO = "/recharge/api/v1/fapiao/showHistoryFapiao";
    public static final String HISTORY_FAPIAO_LIST = "/recharge/api/v1/fapiao/historyFapiaoList";
    public static final String RECHARGE_AWARD_CONFIG = "/recharge/api/v1/award/awardConfig";
    public static final String QUERY_RECHARGE_AWARDS = "/recharge/api/v1/award/queryRechargeAwards";
    public static final String RECHARGE_AMOUNT_GEAE = "/recharge/api/v1/rechargeAmountGear";
    public static final String CREATE_ORDER_FOR_GP = "/recharge/api/v1/createOrderForGp";
    public static final String FINISH_ORDER_FOR_GP = "/recharge/api/v1/finishOrderForGp";
    public static final String AIRWALLEX_APP_CREATE_ORDER = "/recharge/api/v1/overseas/aw/appCreateOrder";
    public static final String AIRWALLEX_APP_CONFIRM_ORDER = "/recharge/api/v1/overseas/aw/appConfirmOrder";
    public static final String AIRWALLEX_GET_AMOUNT_METHOD_LIST = "/recharge/api/v1/overseas/aw/getAmountMethodList";
    public static final String AIRWALLEX_GET_BANK_NAME_LIST = "/recharge/api/v1/overseas/aw/getBankNameList";
    public static final String AIRWALLEX_COUNTRY_LIST = "/recharge/api/v1/overseas/getCountryList";
    public static final String RECHARGE_SING_MERGE = "/recharge/api/v1/singMerge";

    // 酷狗开放平台
    public static final String KUGOU_OPEN_PATTERNS = "/recharge/api/v1/openapi/**";
    public static final String KUGOU_OPEN_ALIPAY_M = "/recharge/api/v1/openapi/getOrderForAlipayM";
    public static final String KUGOU_OPEN_WECHAT_M = "/recharge/api/v1/openapi/getOrderForWechatM";
    public static final String KUGOU_OPEN_IAP_CREATE_ORDER = "/recharge/api/v1/openapi/iap/createOrder";
    public static final String KUGOU_OPEN_IAP_FINISH_ORDER = "/recharge/api/v1/openapi/iap/finishOrder";

    // 内网接口（http）
    public static final String INTRANET_PATTERNS = "/intranet/api/v1/**";
    public static final String CAN_TMALL_RECHARGE = "/intranet/api/v1/canTmallRecharge";
    public static final String TAOBAO_PURCHASE_CALLBACK = "/intranet/api/v1/purchase/tmallCallBack";
    public static final String CAN_TMALL_RECHARGE_KW = "/intranet/api/v1/kw/canTmallRecharge";
    public static final String TAOBAO_PURCHASE_CALLBACK_KW = "/intranet/api/v1/kw/purchase/tmallCallBack";
    public static final String WITH_DRAW_ORDER_VERIFY = "/intranet/api/v1/withdrawOrderVerify";
    public static final String WITH_DRAW_ORDER_VERIFY_WECHAT = "/intranet/api/v1/withdraw/wechat/orderVerify";
    public static final String WITH_DRAW_ORDER_VERIFY_SHENTIAN = "/intranet/api/v1/withdraw/shentian/orderVerify";
    public static final String KUGOU_OPEN_PURCHASE_CALLBACK = "/intranet/api/v1/purchase/callback";
    public static final String RECHARGE_CALLBACK_COIN = "/intranet/api/v1/purchase/callBackCoin";
    public static final String RECHARGE_CALLBACK_GOODS = "/intranet/api/v1/purchase/callbackGoods";
    public static final String CAN_OPEN_FAPIAO = "/intranet/api/v1/canOpenFapiao";
    public static final String OPEN_FAPIAO_CALLBACK = "/intranet/api/v1/fapiao/callback";
    public static final String GET_FANXING_ID_BY_KUGOU_ID = "/intranet/api/v1/getFanxingIdByKugouId";
    public static final String GAME_PURCHASE_CURRENCY_CALLBACK = "/intranet/api/v1/game/purchaseCurrencyCallBack";
    public static final String KUPAY_APPSTORE_ORDER_CALLBACK = "/intranet/api/v1/appstore/orderCallback";
    public static final String KUPAY_APPSTORE_ORDER_VERIFY = "/intranet/api/v1/appstore/orderVerify";
    public static final String TMALL_REFUND_NOTIFY = "/intranet/api/v1/tmallRefund";
    public static final String GOOGLE_PAY_REFUND_NOTIFY = "/intranet/api/v1/googlePayRefund";
    public static final String AIRWALLEX_REFUND_NOTIFY = "/intranet/api/v1/airwallex/refund";
    public static final String PDD_CREATE_ORDER_CHECK = "/intranet/api/v1/pdd/orderCheck";
    public static final String PDD_PAYMENT_NOTIFY = "/intranet/api/v1/pdd/orderNotify";


    public static final String WX_SIGNUP_CALLBACK = "/intranet/api/v1/wxSignCallback";


    // 内网接口（thrift）
    public static final String RECHARGE_THRIFT_PATTERNS = "/platform_recharge_service/thrift/**";

}
