package com.kugou.fanxing.recharge.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;
import java.util.stream.Stream;

/**
 * 汇总审核状态（0：待审核；1：通过；2：不通过）
 *
 * <AUTHOR>
 */
@Getter
public enum ApproveStatusEnum {
    /**
     * 待审核
     */
    APPROVE_STATUS_0(0, "待审核"),
    /**
     * 通过
     */
    APPROVE_STATUS_1(1, "通过"),
    /**
     * 不通过
     */
    APPROVE_STATUS_2(2, "不通过");

    private final int status;
    private final String label;

    ApproveStatusEnum(int status, String label) {
        this.status = status;
        this.label = label;
    }

    public static Optional<ApproveStatusEnum> statusOf(int status) {
        return Stream.of(ApproveStatusEnum.values())
                .filter(approveStatusEnum -> approveStatusEnum.status == status)
                .findFirst();
    }

    public static String labelOf(int status) {
        String approveStatusLabel = StringUtils.EMPTY;
        Optional<ApproveStatusEnum> approveStatusEnumOptional = ApproveStatusEnum.statusOf(status);
        if (approveStatusEnumOptional.isPresent()) {
            approveStatusLabel = approveStatusEnumOptional.get().getLabel();
        }
        return approveStatusLabel;
    }
}
