package com.kugou.fanxing.recharge.constant;

import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.exception.ContextedRuntimeException;

import java.util.Arrays;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * 充值分类
 *
 * <AUTHOR>
 */
@Getter
@ToString
public enum ReTypeEnum {

    RETYPE_RECHARGE(0, "充值货币", "RechargePlat/RechargeService/RechargeService/callBackAcross?action=callBackAcross", UrlConstants.RECHARGE_CALLBACK_COIN),
    RETYPE_PURCHASE(1, "购买商品", "RechargePlat/RechargeService/PaymentService/callback", UrlConstants.RECHARGE_CALLBACK_GOODS),
    RETYPE_RENEWALS(2, "自动续费", "", "");

    private final int reTypeId;
    private final String reTypeLabel;
    private final String notifyUri;
    private final String newNotifyUri;

    ReTypeEnum(int reTypeId, String reTypeLabel, String notifyUri, String newNotifyUri) {
        this.reTypeId = reTypeId;
        this.reTypeLabel = reTypeLabel;
        this.notifyUri = notifyUri;
        this.newNotifyUri = newNotifyUri;
    }

    public static String labelOf(int reTypeId) {
        Optional<ReTypeEnum> optionalReTypeEnum = Arrays.stream(ReTypeEnum.values())
                .filter(reTypeEnum -> reTypeEnum.reTypeId == reTypeId)
                .findAny();
        return optionalReTypeEnum.isPresent() ? optionalReTypeEnum.get().getReTypeLabel() : "其他充值";
    }

    /**
     * 是否充值星币
     *
     * @param reTypeId 类型ID
     * @return 是否充值星币
     */
    public static boolean isRecharge(int reTypeId) {
        return RETYPE_RECHARGE.getReTypeId() == reTypeId;
    }

    /**
     * 是否充值购买
     *
     * @param reTypeId 类型ID
     * @return 是否充值购买
     */
    public static boolean isPurchase(int reTypeId) {
        return RETYPE_PURCHASE.getReTypeId() == reTypeId;
    }

    /**
     * 是否自动续费
     *
     * @param reTypeId 类型ID
     * @return 是否自动续费
     */
    public static boolean isRenewals(int reTypeId) {
        return RETYPE_RENEWALS.getReTypeId() == reTypeId;
    }

    public static ReTypeEnum of(int reTypeId) {
        return Stream.of(ReTypeEnum.values())
                .filter(reTypeEnum -> reTypeEnum.getReTypeId() == reTypeId)
                .findFirst()
                .orElseThrow(() -> new ContextedRuntimeException("获取购买续费类型失败")
                        .addContextValue("reTypeId", reTypeId));
    }
}
