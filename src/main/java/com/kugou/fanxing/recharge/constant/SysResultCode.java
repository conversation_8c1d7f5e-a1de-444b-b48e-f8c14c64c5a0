package com.kugou.fanxing.recharge.constant;

import com.kugou.fanxing.recharge.util.JsonResult;
import lombok.Getter;

import java.util.function.Consumer;
import java.util.stream.Stream;

@Getter
public enum SysResultCode implements JsonResult.ResultCode {
    DORETRY(-1, "操作太快，稍后重试"),
    SUCCESS(0, "操作成功"),
    FAILURE(1, "系统繁忙，请稍后重试"),
    RECHARGE_SYS_ERROR(1, "系统繁忙，请稍后重试"),
    RECHARGE_PARAM_ERROR(100001, "请求参数不合法"),
    RECHARGE_TOKEN_INVALID(100002, "请重新登录"),
    RECHARGE_BAN_ACCOUNT(100003, "您的帐号因违规被封号，如有疑问请联系客服"),
    RECHARGE_NOT_FANXING(100005, "您的账号存在异常，如有疑问请联系客服"),
    RECHARGE_MONEY_INVALID(100006, "您的充值金额不合法，如有疑问请联系客服"),
    RECHARGE_RISK_STRATEGY(100007, "充值行为存在风险，如有疑问请联系客服"),
    RECHARGE_RESOLVE_ERROR(100008, "解析请求参数失败"),
    RECHARGE_SIGN_INVALID(100009, "请求签名不合法"),
    RECHARGE_GET_ORDER_FAILURE(100010, "生成订单出错"),
    RECHARGE_PAY_TYPE_NOT_SUPPORT(100011, "充值渠道尚未开通"),
    RECHARGE_BY_OTHER_TYPE(100012, "请选择其他充值方式进行充值！"),
    RECHARGE_PAYPAL_MIN_AMOUNT(100013, "单笔充值不能少于10美元"),
    RECHARGE_PAYPAL_MAX_AMOUNT(100014, "单笔充值不能大于100美元"),
    RECHARGE_PAYPAL_OVERSEAS(100015, "充值区域必须为海外"),
    RECHARGE_PAUSE_SERVICE(100016, "因服务升级, 该充值渠道暂停服务"),
    RECHARGE_CHOOSE_OTHER_BANK(100017, "请选择其他银行进行充值!"),
    E_10000018(100018, "充值渠道配置不存在"),
    E_10000019(100019, "充扣V2接口暂不支持小程序购买"),
    E_10000020(100020, "购买的物品列表参数不能为空"),
    E_10000021(100021, "购买的物品列表有误"),
    E_10000022(100022, "购买的物品列表总价值大于付款金额"),
    E_10000023(100023, "暂不支持购买本类型的物品"),
    E_10000024(100024, "代金券业务关闭"),
    E_10000025(100025, "服务功能已下线"),
    RECHARGE_FAMILY_CONTROL(100026, "青少年模式开启中，不能充值和消费"),
    RECHARGE_COUPON_OFFLINE(100027, "代金券服务关闭，请联系客服"),
    RECHARGE_COUPON_USED(100028, "代金券已使用或正在使用中，请刷新页面重新查看"),
    RECHARGE_COUPON_ILLEGAL(100029, "代金券不可用"),
    RECHARGE_COUPON_CONDITION(100030, "充值金额未达券额要求，无法使用"),
    RECHARGE_COUPON_FREEZE(100031, "使用代金券失败，请重试"),
    E_10000032(100032, "充值调用消费加星币异常"),
    RECHARGE_ORDER_NOT_EXISTS(100033, "充值订单不存在，请刷新页面后重新扫码"),
    RECHARGE_ORDER_TIMEOUT(100034, "充值订单超时，请刷新二维码"),
    RECHARGE_ORDER_PAYED(100035, "充值订单已支付"),
    E_10000036(100036, "充值调用虚拟资产加虚拟货币异常"),
    RECHARGE_MISS_NOTIFY_URL(100037, "充值回调配置缺失"),
    RECHARGE_ERROR_KUWO_ID(100038, "酷我ID填写错误"),
    E_10000039(100039, "促销优惠活动尚未开启"),
    E_10000040(100040, "暂不符合活动购买条件"),
    E_100041(100041, "代充不可使用代金券"),
    E_100042(100042, "请输入正确的酷我/繁星ID"),
    E_100043(100043, "不可以给自己代充哦"),
    E_100044(100044, "功能暂时关闭，如有疑问请联系客服"),
    E_100045(100045, "代充账号已封禁，暂不支持充值"),
    E_100046(100046, "代充账号已注销，暂不支持充值"),
    E_100047(100047, "订单不存在"),
    E_100048(100048, "下单信息不一致"),
    E_100049(100049, "海外账户暂不支持当前支付渠道，如有疑问请联系客服"),
    GP_PRODUCT_ID_INVALID(100050,"货品码有误或者不存在"),
    GP_RECEIPT_FORMAT_INVALID(100051, "收据格式非法"),
    GP_BLACK_CURRENCY(100052, "你所在的地区暂不支持该渠道充值"),
    KUPAY_CHANNEL_PROBLEM(100053, "第三方支付渠道问题"),
    PRODUCT_PRICE_MISSING(100054, "商品价格配置缺失"),
    FORBIDDEN_RECHARGE_PLAT(100055, "功能即将下线，暂不能充值"),
    RECHARGE_NOT_KUWO_ID(100056, "您的账号存在异常，如有疑问请联系客服"),
    RECHARGE_COUPON_NOT_ALLOW(100057, "当前币种不支持使用代金券"),
    IAP_SANDBOX_VERSION_BLOCKED(100058, "此版本禁止沙盒充值"),
    IAP_SANDBOX_AMOUNT_OVERDUE(100059, "此版本沙盒充值超额"),
    IAP_RECEIPT_FORMAT_INVALID(100060, "收据格式非法"),
    IAP_PRODUCT_ID_INVALID(100061, "货品码参数非法"),
    UNKNOWN_KUPAY_ID(100062, "支付网关配置参数缺失"),
    RECHARGE_NOT_REGISTER(100063, "账号未注册为繁星用户"),
    RECHARGE_OPENID_NOT_PROVIDED(100064, "请提供微信openid参数"),
    RECHARGE_INVALID_COIN_TYPE(100065, "当前接口只允许充值唱币"),
    RECHARGE_INVALID_DESCRIPTION(100066, "参数description不允许包含空格"),
    RECHARGE_FORBIDDEN_SING_COIN(100067, "服务维护中，暂时无法充值唱币"),
    WITHDRAW_ACCOUNT_ALREADY_BIND(********, "酷狗ID重复绑定提现账号"),
    WITHDRAW_ACCOUNT_LIMITED_BIND(********, "账号至多允许绑定3个酷狗ID"),
    WITHDRAW_ACCOUNT_NOT_MATCH(********, "提现实名信息与实名认证不符"),
    WITHDRAW_ACCOUNT_BIND_FAIL(********, "保存绑定提现账号失败"),
    WITHDRAW_ACCOUNT_NOT_EXISTS(********, "绑定提现账号失败"),
    WITHDRAW_ACCOUNT_NOT_BIND(********, "酷狗ID尚未绑定提现账号"),
    WITHDRAW_ACCOUNT_EXCEPTION(********, "提现账号处理异常"),
    WITHDRAW_ORDER_NOT_REGISTER(********, "提现订单业务未注册"),
    WITHDRAW_ORDER_SAVE_FAIL(********, "保存用户提现订单失败"),
    WITHDRAW_ORDER_EXCEPTION(********, "提现订单处理异常"),
    WITHDRAW_ORDER_NOT_EXISTS(********, "提现订单不存在"),
    WITHDRAW_ORDER_ILLEGAL_AMOUNT(********, "提现金额不合法"),
    WITHDRAW_CANCEL_FORBIDDEN(********, "提现订单禁止取消"),
    WITHDRAW_CANCEL_FAILURE(********, "提现订单取消失败"),
    WITHDRAW_CANCEL_EXCEPTION(********, "提现订单取消异常"),
    WITHDRAW_ORDER_NO_BUDGET(********, "提现预算不足"),
    WITHDRAW_ORDER_ILLEGAL_TYPE(********, "提现类型不支持"),
    WITHDRAW_ORDER_RISK_LIMIT(********, "提现订单风控拦截"),

    // 充值回调
    E_********(********, "未知错误"),
    E_********(********, "我方内部错误，需要重发"),
    E_********(********, "对方参数错误"),
    E_30000003(30000003, "IP未授权"),
    E_30000004(30000004, "交易SKU购买件数缺失"),
    E_30000005(30000005, "订单已经充值过"),
    E_30000006(30000006, "要充值的酷狗id不符"),
    E_30000007(30000007, "订单不存在"),
    E_30000008(30000008, "交易SKU定价配置缺失"),
    E_30000009(30000009, "参数签名错误"),
    E_30000010(30000010, "回查notify_id不存在"),
    E_30000011(30000011, "根据店铺品牌信息获取酷狗ID失败"),
    E_30000012(30000012, "无法绑定直播订单号"),
    E_30000013(30000013, "充值星币发放失败"),
    E_30000014(30000014, "不满足沙盒充值条件"),

    // 线下充值
    E_40000015(40000015, "申请返点交易号不存在"),
    E_40000016(40000016, "申请返点交易号已返点"),
    E_40000017(40000017, "申请返点交易号金额少于10万"),
    E_40000018(40000018, "返点申请提交失败，请稍后重试"),
    E_40000019(40000019, "申请返点交易号时间间隔超过24h, 不允许合并返点"),
    E_40000020(40000020, "系统正在维护中，暂时无法使用哦~"),
    E_40000021(40000021, "疑似风险请求，为保障您的账户安全，本次返点申请提交失败"),
    E_40000022(40000022, "申请合并返点需提交两笔及以上交易号"),
    E_40000023(40000023, "申请无效，合并返点的总金额未达到更高返点比例要求"),
    E_40000210(40000210, "充值申请提交失败，请稍后重试"),
    E_40000211(40000211, "非法请求，充值账号与金额不一致"),
    E_40000212(40000212, "用户繁星ID不存在"),
    E_40000213(40000213, "支付宝订单号信息填写错误"),
    E_40000214(40000214, "支付宝订单号信息重复提交"),
    E_40000215(40000215, "填写的酷狗账号重复，请核对后再提交"),
    E_40000216(40000216, "填写的支付宝单号重复，请核对后再提交"),
    E_40000217(40000217, "系统正在维护中，暂时无法使用哦~"),
    E_40000218(40000218, "疑似风险请求，为保障您的账户安全，本次充值申请提交失败"),
    E_40000219(40000219, "填写的酷狗账号重复，请核对后再提交"),
    E_40000220(40000220, "银行卡转账交易号重复提交"),
    E_40000221(40000221, "需要提供银行卡转账交易号"),
    E_40000222(40000222, "填写的酷狗账号不存在，请核对后再提交"),
    E_40000223(40000223, "线下充值申请审核，申请编号不存在"),
    E_40000224(40000224, "线下充值申请审核，申请编号已审核"),
    E_40000225(40000225, "用户酷狗ID不存在"),

    // 酷狗开放平台
    E_50000000(50000000, "酷狗开放平台请求网络异常"),
    E_50000001(50000001, "酷狗开放平台业务配置缺失"),
    E_50000002(50000002, "酷狗开放平台业务签名错误"),
    E_50000003(50000003, "酷狗开放平台业务令牌失效"),
    E_50000004(50000004, "酷狗开放平台业务回调失败"),
    E_50000005(50000005, "酷狗开放平台服务端配置缺失"),
    E_50000006(50000006, "当前版本禁止使用沙盒充值"),

    // iOS充值
    E_60000000(60000000, "货品码不存在"),

    //微信支付
    WX_RECHARGE_GET_ORDER_FAILED(70000001, "获取订单号失败"),
    WX_RECHARGE_FAMILY_CONTROL_FAILED(70000002, "当前用户处于家长模式"),
    WX_RECHARGE_ORDER_INSERT_FAILED(70000003, "订单号重复,请勿重复操作"),
    WX_RECHARGE_REQUEST_PAY_FAILED(70000004, "请求支付接口失败"),



    ;
    private final int code;
    private final String msg;

    SysResultCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public boolean isSuccess() {
        return SUCCESS.equals(this);
    }

    public static SysResultCode codeOf(int code) {
        return Stream.of(SysResultCode.values())
                .filter(sysResultCode -> sysResultCode.getCode() == code)
                .findFirst()
                .orElse(SysResultCode.RECHARGE_SYS_ERROR);
    }

    public void postHandler(Consumer<SysResultCode> onSuccess, Consumer<SysResultCode> onFailure) {
        if (isSuccess()) {
            onSuccess.accept(this);
        } else {
            onFailure.accept(this);
        }
    }
}
