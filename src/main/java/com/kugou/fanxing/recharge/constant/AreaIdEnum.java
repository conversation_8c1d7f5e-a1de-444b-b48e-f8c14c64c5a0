package com.kugou.fanxing.recharge.constant;

import lombok.Getter;
import lombok.ToString;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@ToString
public enum AreaIdEnum {

    MAIN_LAND(0, "大陆"),
    OUT_SEA(1, "海外"),
    ;

    private final int areaId;
    private final String desc;

    AreaIdEnum(int areaId, String desc) {
        this.areaId = areaId;
        this.desc = desc;
    }

    public static Optional<AreaIdEnum> of(int areaId) {
        return Stream.of(AreaIdEnum.values())
                .filter(bizLineEnum -> bizLineEnum.getAreaId() == areaId)
                .findFirst();
    }

    public static boolean isOverseas(int areaId) {
        return AreaIdEnum.OUT_SEA.getAreaId() == areaId;
    }
}
