package com.kugou.fanxing.recharge.constant;

import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.exception.ContextedRuntimeException;

import java.util.Optional;
import java.util.stream.Stream;

/**
 * 充值支付类型
 *
 * <AUTHOR>
 */
@Getter
@ToString
public enum PayTypeIdEnum {
    PAY_TYPE_ID_3(3, "支付宝", ""),
    PAY_TYPE_ID_24(24, "银行卡", ""),
    PAY_TYPE_ID_25(25, "手机充值卡", ""),
    PAY_TYPE_ID_26(26, "天下通", ""),
    PAY_TYPE_ID_27(27, "手机短信", ""),
    PAY_TYPE_ID_28(28, "手机充值卡(手机平台)", ""),
    PAY_TYPE_ID_29(29, "财付通", ""),
    PAY_TYPE_ID_30(30, "android支付宝", ""),
    PAY_TYPE_ID_31(31, "支付宝Wap", ""),
    PAY_TYPE_ID_32(32, "微信Wap支付", ""),
    PAY_TYPE_ID_33(33, "支付宝H5", ""),
    PAY_TYPE_ID_35(35, "银联", ""),
    PAY_TYPE_ID_39(39, "微信支付", ""),
    PAY_TYPE_ID_40(40, "微信支付(手机平台)", ""),
    PAY_TYPE_ID_41(41, "微信公众号支付", ""),
    PAY_TYPE_ID_42(42, "微信小程序支付", ""),
    PAY_TYPE_ID_50(50, "小米支付", ""),
    PAY_TYPE_ID_221(221, "微信代扣-自动续费", ""),
    PAY_TYPE_ID_222(222, "微信代扣-PC", ""),
    PAY_TYPE_ID_223(223, "微信代扣-APP", ""),
    PAY_TYPE_ID_231(231, "支付宝代扣-自动续费", ""),
    PAY_TYPE_ID_232(232, "支付宝代扣-PC", ""),
    PAY_TYPE_ID_233(233, "支付宝代扣-APP", ""),
    PAY_TYPE_ID_100(100, "游戏卡", ""),
    PAY_TYPE_ID_520(520, "PayPal", ""),
    PAY_TYPE_ID_521(521, "GooglePlay", ""),
    PAY_TYPE_ID_522(522, "Airwallex", ""),
    PAY_TYPE_ID_1001(1001, "迅雷", ""),
    PAY_TYPE_ID_1002(1002, "热度", ""),
    PAY_TYPE_ID_1003(1003, "电信15包月", ""),
    PAY_TYPE_ID_1004(1004, "快播", ""),
    PAY_TYPE_ID_1005(1005, "3G", ""),
    PAY_TYPE_ID_1006(1006, "AppStore", "appstore"),
    PAY_TYPE_ID_1007(1007, "水葫芦", ""),
    PAY_TYPE_ID_1008(1008, "电信星币对换", ""),
    PAY_TYPE_ID_1009(1009, "凤凰", ""),
    PAY_TYPE_ID_1010(1010, "妖妖", ""),
    PAY_TYPE_ID_1011(1011, "对公转账", ""),
    PAY_TYPE_ID_1012(1012, "天猫售卖", ""),
    PAY_TYPE_PDD(1013, "拼多多售卖", "PDD"),
    ;

    private final int payTypeId;
    private final String payTypeLabel;

    private final String payTypeCode;

    PayTypeIdEnum(int payTypeId, String payTypeLabel, String payTypeCode) {
        this.payTypeId = payTypeId;
        this.payTypeLabel = payTypeLabel;
        this.payTypeCode = payTypeCode;
    }

    public static String payTypeLabelOf(int payTypeId) {
        Optional<PayTypeIdEnum> optionalPayTypeIdEnum = of(payTypeId);
        return optionalPayTypeIdEnum.isPresent() ? optionalPayTypeIdEnum.get().getPayTypeLabel() : "其他渠道";
    }

    public static PayTypeIdEnum payTypeOf(int payTypeId) {
        return of(payTypeId).orElseThrow(() -> new ContextedRuntimeException("充值方式配置不存在")
                .addContextValue("payTypeId", payTypeId));
    }

    public static Optional<PayTypeIdEnum> of(int payTypeId) {
        return Stream.of(PayTypeIdEnum.values())
                .filter(payTypeIdEnum -> payTypeIdEnum.payTypeId == payTypeId)
                .findFirst();
    }

    public static boolean isPayPalRecharge(int payTypeId) {
        return PayTypeIdEnum.PAY_TYPE_ID_520.getPayTypeId() == payTypeId;
    }

    public static boolean isGooglePlayRecharge(int payTypeId) {
        return PayTypeIdEnum.PAY_TYPE_ID_521.getPayTypeId() == payTypeId;
    }

    public static boolean isTmallRecharge(int payTypeId) {
        return PayTypeIdEnum.PAY_TYPE_ID_1012.getPayTypeId() == payTypeId;
    }

    public static boolean isOfflineRecharge(int payTypeId) {
        return PayTypeIdEnum.PAY_TYPE_ID_1011.getPayTypeId() == payTypeId;
    }

    public static boolean isAirwallexRecharge(int payTypeId) {
        return PayTypeIdEnum.PAY_TYPE_ID_522.getPayTypeId() == payTypeId;
    }

    public static boolean isPddRecharge(int payTypeId) {
        return PayTypeIdEnum.PAY_TYPE_PDD.getPayTypeId() == payTypeId;
    }

    public static boolean isAlipayApp(int payTypeId) {
        return PayTypeIdEnum.PAY_TYPE_ID_30.getPayTypeId() == payTypeId;
    }

    public static boolean isWechatApp(int payTypeId) {
        return PayTypeIdEnum.PAY_TYPE_ID_40.getPayTypeId() == payTypeId;
    }

    public static boolean isWechatJsapi(int payTypeId) {
        return PayTypeIdEnum.PAY_TYPE_ID_41.getPayTypeId() == payTypeId;
    }

    public static boolean isAppleApp(int payTypeId) {
        return PayTypeIdEnum.PAY_TYPE_ID_1006.getPayTypeId() == payTypeId;
    }

    public static boolean isAppPayType(int payTypeId) {
        return isAppleApp(payTypeId) || isAlipayApp(payTypeId) || isWechatApp(payTypeId);
    }
}
