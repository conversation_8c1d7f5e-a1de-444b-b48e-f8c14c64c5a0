package com.kugou.fanxing.recharge.constant;

import com.google.common.collect.Sets;
import lombok.Getter;

import java.util.Set;

/**
 * 消费服务错误码
 *
 * <AUTHOR>
 */
@Getter
public enum ConsumeResponseEnum {

    RES_SUCCESS_CODE(0, "成功"),
    SECOND_REQUEST(********, "globalId重复"),
    RES_FAIL_CODE(1, "失败"),
    TOKEN_ERROR(*********, "token验证失败"),
    ARGUMENT_INVALID(*********, "参数非法"),
    USER_IP_HAS_BEEN_BAN(*********, "IP封禁"),
    USER_ACCOUNT_HAS_BEEN_BAN(*********, "账户被封"),
    USER_NOT_EXISTS(*********, "用户不存在"),
    SIGN_ERROR(*********, "签名错误"),
    HAS_BALANCE_ERROR(*********, "重复结算"),
    HAS_BALANCE_STATUS_ERROR(*********, "冻结订单状态异常"),
    RETURN_COIN_ERROR(*********, "退币失败"),
    TIME_OUT(********, "请求超时"),
    NOT_ENOUGH_MONEY(*********, "余额不足"),
    CONCURRENT_OPERATION(*********, "你的操作太快了"),
    UNCAUGHT_EXCEPTION(*********, "服务器繁忙，请稍候重试"),
    RATE_LIMIT(*********, "服务过载，请稍后再试"),
    REJECT_SERVE(*********, "业务流量异常，拒绝服务"),
    RISK_MANAGEMENT_KUGOU_ID(*********2, "某个酷狗用户调用平台频率过高"),
    UN_SAFE_USER(*********, "高危用户异常"),
    READONLY_SERVE(*********, "只读服务"),
    RECHARGE_REPEAT(*********, "rechargeId重复"),
    ADD_BEAN_REPEAT(*********, "加星豆订单单重复"),
    SQL_EXCEPTION(********, "数据库操作异常"),
    DB_OPRATION_FAILED(********, "数据库操作失败"),
    BEAN_EXCHANGE_FREEZE(*********, "星豆兑换被冻结"),
    ACCOUNT_MONEY_UNUSUAL(*********, "账户金钱异常"),
    ADD_BEAN_ERROR_KUGOUID(*********, "调用加星豆接口，toKugouId错误"),
    BLOCK_DECREASE_COINS(*********, "阻断扣减星币"),
    BLOCK_DECREASE_BEANS(*********, "阻断扣减星豆"),
    BEAN_EXCHAGE_ALARM_DISABLE(*********, "星豆兑换告警"),
    FORBID_CONSUME(*********, "禁止消费"),
    USER_ACCOUNT_BAN(********, "账户被冻结"),
    STAR_ACCOUNT_HAS_BEEN_BAN(********, "主播账户被冻结"),
    ROOM_HAS_BEEN_BAN(********, "房间已被封禁"),
    SERVER_SELF_KILL(********, "服务自杀"),
    ;

    private final long value;
    private final String label;

    ConsumeResponseEnum(long value, String label) {
        this.value = value;
        this.label = label;
    }

    /**
     * 是否调用消费加星币成功
     *
     * @param ret 消费响应码
     * @return 是否调用消费成功
     */
    public static boolean isSuccessRetCode(long ret) {
        Set<Long> successRetSet = Sets.newHashSet(RES_SUCCESS_CODE.getValue(), SECOND_REQUEST.getValue());
        return successRetSet.contains(ret);
    }

    /**
     * 是否调用消费充扣成功
     *
     * @param ret 消费响应码
     * @return 是否调用消费成功
     */
    public static boolean isSuccessPayment(long ret) {
        Set<Long> successRetSet = Sets.newHashSet(RES_SUCCESS_CODE.getValue(), SECOND_REQUEST.getValue(), RECHARGE_REPEAT.getValue());
        return successRetSet.contains(ret);
    }

}
