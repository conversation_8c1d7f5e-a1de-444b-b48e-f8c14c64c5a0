package com.kugou.fanxing.recharge.constant;

import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

/**
 * 财务变更类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum AccountChangeTypeEnum {
    ACCOUNT_CHANGE_TYPE_100001("100001", "星币"),
    ACCOUNT_CHANGE_TYPE_110078("110078", "豆粉"),
    ACCOUNT_CHANGE_TYPE_110218("110218", "豆粉首月特惠"),
    ACCOUNT_CHANGE_TYPE_110031("110031", "VIP"),
    ACCOUNT_CHANGE_TYPE_110032("110032", "VIP"),
    ACCOUNT_CHANGE_TYPE_110014("110014", "守护"),
    ACCOUNT_CHANGE_TYPE_110209("110209", "演唱会门票"),
    ACCOUNT_CHANGE_TYPE_110223("110223", "为你心动"),
    ACCOUNT_CHANGE_TYPE_110224("110224", "爱的泡泡"),
    ACCOUNT_CHANGE_TYPE_110403("110403", "续充礼包"),
    ACCOUNT_CHANGE_TYPE_110583("110583", "心愿单"),
    ;

    private final String value;
    private final String label;

    AccountChangeTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static String bizTypeLabelOf(String accountChangeType) {
        Optional<AccountChangeTypeEnum> optional = Stream.of(AccountChangeTypeEnum.values())
                .filter(accountChangeTypeEnum -> accountChangeTypeEnum.value.equals(accountChangeType))
                .findFirst();
        return optional.isPresent() ? optional.get().getLabel() : "业务";
    }
}
