package com.kugou.fanxing.recharge.constant;

import lombok.Data;

/**
 * @Author: yuzhaopeng
 * @Description:
 * @Date: 2024/2/26 10:37
 */
public enum HandlerTypeEnum {

    DEFAULT(0, "0:默认处理方式(扣减星币/唱币)"),
    NOTIFY(1, "通知回调")
    ;
    private final int code;
    private final String desc;

    HandlerTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
