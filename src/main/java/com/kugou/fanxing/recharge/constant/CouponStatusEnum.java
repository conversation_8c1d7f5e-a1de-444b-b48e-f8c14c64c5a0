package com.kugou.fanxing.recharge.constant;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 代金券使用错误码
 * t_recharge_coupon.status
 *
 * <AUTHOR>
 */
@Getter
public enum CouponStatusEnum {

    STATUS_PADDING(0, "尚未支付"),
    STATUS_SUCCESS(1, "使用成功"),
    STATUS_CANCEL(2, "超时支付"),
    STATUS_FAILED(-1, "使用失败");

    private final int status;
    private final String label;

    CouponStatusEnum(int status, String label) {
        this.status = status;
        this.label = label;
    }

    public static String labelOf(int status) {
        Optional<CouponStatusEnum> optionalCouponStatusEnum = Arrays.stream(CouponStatusEnum.values())
                .filter(couponStatusEnum -> couponStatusEnum.getStatus() == status)
                .findAny();
        return optionalCouponStatusEnum.isPresent() ? optionalCouponStatusEnum.get().getLabel() : "未知状态";
    }
}
