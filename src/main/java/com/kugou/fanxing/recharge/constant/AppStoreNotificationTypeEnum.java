package com.kugou.fanxing.recharge.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * https://developer.apple.com/documentation/appstoreservernotifications/notification_type
 *
 * <AUTHOR>
 */
@Getter
@ToString
@AllArgsConstructor
public enum AppStoreNotificationTypeEnum {
    CANCEL("CANCEL", ""),
    DID_CHANGE_RENEWAL_PREF("DID_CHANGE_RENEWAL_PREF", ""),
    DID_CHANGE_RENEWAL_STATUS("DID_CHANGE_RENEWAL_STATUS", ""),
    DID_FAIL_TO_RENEW("DID_FAIL_TO_RENEW", ""),
    DID_RECOVER("DID_RECOVER", ""),
    INITIAL_BUY("INITIAL_BUY", ""),
    INTERACTIVE_RENEWAL("INTERACTIVE_RENEWAL", ""),
    RENEWAL("R<PERSON><PERSON><PERSON><PERSON>", ""),
    REFUND("REFUND", "")
    ;
    private final String key;
    private final String des;
}
