package com.kugou.fanxing.recharge.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;


@Getter
@ToString
@AllArgsConstructor
public enum ProductTypeEnum {
    SING_COIN(120, "唱币"),
    ;

    private final int productType;
    private final String desc;

    public static boolean isSingProductType(int productType){
        return SING_COIN.getProductType() == productType;
    }
}