package com.kugou.fanxing.recharge.constant;

import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public enum BizLineEnum {

    LIVE("LIVE", "直播", "星币"),
    GAME("GAME", "游戏", "欧币");

    private final String code;
    private final String desc;
    private final String coinDesc;

    BizLineEnum(String code, String desc, String coinDesc) {
        this.code = code;
        this.desc = desc;
        this.coinDesc = coinDesc;
    }

    /**
     * 是否游戏业务线
     */
    public static boolean isGameLine(String code) {
        return GAME.getCode().equalsIgnoreCase(code);
    }

    /**
     * 是否直播业务线
     */
    public static boolean isLiveLine(String code) {
        return LIVE.getCode().equalsIgnoreCase(code);
    }

    public static BizLineEnum of(String code) {
        if (isGameLine(code)) {
            return BizLineEnum.GAME;
        }
        return BizLineEnum.LIVE;
    }

}
