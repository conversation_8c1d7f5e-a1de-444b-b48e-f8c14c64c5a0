package com.kugou.fanxing.recharge.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;
import java.util.stream.Stream;

/**
 * 明细审核状态（0：待审核；1：通过；2：不通过; 3:已加星币）
 *
 * <AUTHOR>
 */
@Getter
public enum ApproveDetailStatusEnum {
    /**
     * 待审核
     */
    APPROVE_STATUS_0(0, "待审核"),
    /**
     * 通过
     */
    APPROVE_STATUS_1(1, "通过"),
    /**
     * 不通过
     */
    APPROVE_STATUS_2(2, "不通过"),
    /**
     * 已加星币
     */
    APPROVE_STATUS_3(3, "已加星币");

    private final int status;
    private final String label;

    ApproveDetailStatusEnum(int status, String label) {
        this.status = status;
        this.label = label;
    }

    public static Optional<ApproveDetailStatusEnum> statusOf(int status) {
        return Stream.of(ApproveDetailStatusEnum.values())
                .filter(approveStatusEnum -> approveStatusEnum.status == status)
                .findFirst();
    }

    public static String labelOf(int status) {
        String label = StringUtils.EMPTY;
        Optional<ApproveDetailStatusEnum> approveDetailStatusEnumOptional = ApproveDetailStatusEnum.statusOf(status);
        if (approveDetailStatusEnumOptional.isPresent()) {
            label = approveDetailStatusEnumOptional.get().getLabel();
        }
        return label;
    }
}
