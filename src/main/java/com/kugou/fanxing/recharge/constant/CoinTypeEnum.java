package com.kugou.fanxing.recharge.constant;

import com.kugou.fanxing.recharge.util.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.stream.Stream;

@Getter
@ToString
@AllArgsConstructor
public enum CoinTypeEnum {

    STAR_COIN(1, "星币"),
    SING_COIN(2, "唱币");

    private final int coinType;
    private final String desc;

    public static boolean allowCoupon(CoinTypeEnum  coinTypeEnum) {
        return coinTypeEnum.getCoinType() == STAR_COIN.getCoinType();
    }

    public static boolean isSingCoinType(int coinType){
        return SING_COIN.getCoinType() == coinType;
    }

    public static int getByProductType(int productType) {
        CoinTypeEnum coinTypeEnum = ProductTypeEnum.isSingProductType(productType) ? SING_COIN : STAR_COIN;
        return coinTypeEnum.getCoinType();
    }

    public static CoinTypeEnum of(int coinType) {
        return Stream.of(CoinTypeEnum.values())
                .filter(coinTypeEnum -> coinTypeEnum.getCoinType() == coinType)
                .findFirst()
                .orElse(CoinTypeEnum.STAR_COIN);
    }

    public static boolean isValidCoinType(int coinType) {
        return ArrayUtils.contains(new int[]{0, STAR_COIN.getCoinType(), SING_COIN.getCoinType()}, coinType);
    }

    /**
     * 从JSON中获取coinType 如果不存在 默认就是星币
     * @param json
     * @return
     */
    public static CoinTypeEnum getCoinTypeEnumByJson(String json){
        String extJson = StringUtils.defaultIfBlank(json, "{}");
        int coinType = JsonUtils.parseJsonPath(extJson, "$.coinType", Integer.class, 1);
        return CoinTypeEnum.of(coinType);
    }
}
