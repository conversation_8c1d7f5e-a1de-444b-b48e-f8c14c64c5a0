package com.kugou.fanxing.recharge.constant;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 充值订单的代金券状态描述
 * t_recharge_across_${month}.couponStatus
 *
 * <AUTHOR>
 */
@Getter
public enum RechargeCouponStatusEnum {

    NOT_USE(0, "没有使用代金券"),
    SUCCESS(1, "代金券使用成功"),
    FAILURE(-1, "代金券使用失败"),
    ;

    private final int status;
    private final String label;

    RechargeCouponStatusEnum(int status, String label) {
        this.status = status;
        this.label = label;
    }

    public static String labelOf(int status) {
        Optional<RechargeCouponStatusEnum> optionalRechargeCouponStatusEnum = Arrays.stream(RechargeCouponStatusEnum.values())
                .filter(couponStatusEnum -> couponStatusEnum.getStatus() == status)
                .findAny();
        return optionalRechargeCouponStatusEnum.isPresent() ? optionalRechargeCouponStatusEnum.get().getLabel() : "未知状态";
    }
}

