globalIdService.applicationName=platform_globalid_v2
globalIdService.protocol=thrift
globalIdService.className=com.kugou.api.springcloud.GlobalIdServiceThrift.GlobalIdService
globalIdService.url=/service
globalIdService.ConnectTimeout=500
globalIdService.ReadTimeout=3000
globalIdService.enableRetry=true

tokenService.applicationName=fxsoa_checktoken
tokenService.protocol=thrift
tokenService.className=com.kugou.fanxing.thrift.token.service.TokenService
tokenService.url=/soa/token/thrift/tokenservice
tokenService.ConnectTimeout=500
tokenService.ReadTimeout=3000
tokenService.enableRetry=true

userIdMappingService.applicationName=fx_idservice
userIdMappingService.protocol=thrift
userIdMappingService.className=com.kugou.fanxing.thrift.idmapping.user.UserIdMappingService
userIdMappingService.url=/soa/idservice/thrift/idmapping
userIdMappingService.ConnectTimeout=500
userIdMappingService.ReadTimeout=3000
userIdMappingService.enableRetry=true

userModuleV2BizService.applicationName=fxsoa_user_baseinfo
userModuleV2BizService.protocol=thrift
userModuleV2BizService.className=com.kugou.fanxing.userbaseinfo.service.UserModuleV2BizService
userModuleV2BizService.url=/soa/usermodule/userv2service.thrift
userModuleV2BizService.ConnectTimeout=500
userModuleV2BizService.ReadTimeout=3000
userModuleV2BizService.enableRetry=true

platformOperationService.applicationName=platform_operation_service
platformOperationService.protocol=thrift
platformOperationService.className=com.kugou.fanxing.thrift.operation.FamilyControllService
platformOperationService.url=/platform/platform-operation-service/familyControllService
platformOperationService.ConnectTimeout=300
platformOperationService.ReadTimeout=3000
platformOperationService.enableRetry=true

strategyService.applicationName=platform_strategy_service
strategyService.protocol=thrift
strategyService.className=com.kugou.fanxing.risk.sdk.thrift.strategy.service.PlatformStrategyService
strategyService.url=/platform/strategy
strategyService.ConnectTimeout=500
strategyService.ReadTimeout=3000
strategyService.enableRetry=false

rechargeInfoQueryThriftService.applicationName=platform_recharge_asset_allocate
rechargeInfoQueryThriftService.protocol=thrift
rechargeInfoQueryThriftService.className=com.kugou.platform.recharge.queryservice.thrift.RechargeInfoQueryThriftService
rechargeInfoQueryThriftService.url=/platform/recharge/queryservice
rechargeInfoQueryThriftService.ConnectTimeout=500
rechargeInfoQueryThriftService.ReadTimeout=3000
rechargeInfoQueryThriftService.enableRetry=true

AfterRechargeAssetAllocateReadService.applicationName=platform_recharge_asset_allocate
AfterRechargeAssetAllocateReadService.protocol=thrift
AfterRechargeAssetAllocateReadService.className=com.kugou.platform.after.recharge.asset.allocate.thrift.AfterRechargeAssetAllocateReadService
AfterRechargeAssetAllocateReadService.url=/platform/afterrecharge/read_service
AfterRechargeAssetAllocateReadService.ConnectTimeout=500
AfterRechargeAssetAllocateReadService.ReadTimeout=3000
AfterRechargeAssetAllocateReadService.enableRetry=true

fXIpService.applicationName=fx_ip_service
fXIpService.protocol=thrift
fXIpService.className=com.kugou.fanxing.ip.api.FXIpService
fXIpService.url=/fx-ip-service/api/common/ipInfo
fXIpService.ConnectTimeout=500
fXIpService.ReadTimeout=3000
fXIpService.enableRetry=true

sensitiveService.applicationName=fx_sensitive_service
sensitiveService.protocol=thrift
sensitiveService.className=com.kugou.fanxing.thrift.service.sensitive.SensitiveService
sensitiveService.url=/soa/user/thrift/sensitive
sensitiveService.ConnectTimeout=500
sensitiveService.ReadTimeout=3000
sensitiveService.enableRetry=false
sensitiveService.enableGateWay=true

platformCertificationService.applicationName=platform_certification_service
platformCertificationService.protocol=thrift
platformCertificationService.className=com.kugou.fanxing.thrift.certification.PlatformCertificationService
platformCertificationService.url=/platform/thrift/certificationService
platformCertificationService.ConnectTimeout=500
platformCertificationService.ReadTimeout=3000

dataAlterationAuditService.applicationName=fx_data_alteration_audit_service
dataAlterationAuditService.protocol=thrift
dataAlterationAuditService.className=com.kugou.fanxing.thrift.service.data.alteration.DataAlterationAuditService
dataAlterationAuditService.url=/soa/alteration/thrift/log
dataAlterationAuditService.ConnectTimeout=500
dataAlterationAuditService.ReadTimeout=3000
dataAlterationAuditService.enableRetry=true

couponListService.applicationName=mobile_mfx_activity_infiltrate
couponListService.protocol=thrift
couponListService.className=com.kugou.mfx.activity.infiltrate.thrift.service.CouponListService
couponListService.url=/mfx-activity-infiltrate/thrift/coupon/couponListService.thrift
couponListService.ConnectTimeout=500
couponListService.ReadTimeout=3000
couponListService.enableRetry=true

fxSms.applicationName=fx_sms
fxSms.protocol=thrift
fxSms.className=com.kugou.fanxing.thrift.sms.SmsSendService
fxSms.url=/soa/sms/thrift/sendsms
fxSms.readTimeout=3000

userPlatBizService.applicationName=fxsoa_soa_user
userPlatBizService.protocol=thrift
userPlatBizService.className=com.kugou.fanxing.thrift.plat.user.UserPlatBizService
userPlatBizService.url=/plat/user/thrift/userplatbizservice
userPlatBizService.ConnectTimeout=3000
userPlatBizService.ReadTimeout=10000
userPlatBizService.enableRetry=true
userPlatBizService.enableGateWay=true

rechargeAllocateQueryService.applicationName=platform_pay_service
rechargeAllocateQueryService.protocol=thrift
rechargeAllocateQueryService.className=com.kugou.fanxing.recharge.pay.thrift.RechargeAllocateQueryService
rechargeAllocateQueryService.url=/platform_pay_service/thrift/rechargeAllocateQueryService
rechargeAllocateQueryService.ConnectTimeout=300
rechargeAllocateQueryService.ReadTimeout=3000
rechargeAllocateQueryService.enableRetry=true

platformPayV2Service.applicationName=platform_pay_service
platformPayV2Service.protocol=thrift
platformPayV2Service.className=com.kugou.fanxing.thrift.pay.v2.PlatformPayV2Service
platformPayV2Service.url=/platform_pay_service/thrift/platformPayV2Service
platformPayV2Service.ConnectTimeout=500
platformPayV2Service.ReadTimeout=3000
platformPayV2Service.enableRetry=true

noviceRechargeService.applicationName=platform_pay_service
noviceRechargeService.protocol=thrift
noviceRechargeService.className=com.kugou.fanxing.thrift.pay.novicerecharge.NoviceRechargeService
noviceRechargeService.url=/platform_pay_service/thrift/noviceRechargeService
noviceRechargeService.ConnectTimeout=500
noviceRechargeService.ReadTimeout=3000
noviceRechargeService.enableRetry=true

goodsGiftService.applicationName=fanxing_finance_goods
goodsGiftService.protocol=thrift
goodsGiftService.className=com.kugou.fanxing.finance.goods.gift.GoodsGiftService
goodsGiftService.url=/fanxing_finance_goods/gift_service
goodsGiftService.ConnectTimeout=500
goodsGiftService.ReadTimeout=3000
goodsGiftService.enableRetry=true

platformConsumeReadService.applicationName=platform_consume_service
platformConsumeReadService.protocol=thrift
platformConsumeReadService.className=com.kugou.fanxing.thrift.consume.read.service.PlatformConsumeReadService
platformConsumeReadService.url=/platform_consume_read_service/platform_consume_service
platformConsumeReadService.ConnectTimeout=500
platformConsumeReadService.ReadTimeout=3000
platformConsumeReadService.enableRetry=true

platformAddCoinService.applicationName=platform_consume_service
platformAddCoinService.protocol=thrift
platformAddCoinService.className=com.kugou.fanxing.thrift.freeze.service.PlatformAddCoinService
platformAddCoinService.url=/platform_consume_service/add_coin
platformAddCoinService.connectTimeout=1000
platformAddCoinService.readTimeout=3000
platformAddCoinService.enableRetry=true

platformConsumeService.applicationName=platform_consume_service
platformConsumeService.protocol=thrift
platformConsumeService.className=com.kugou.fanxing.thrift.consume.service.PlatformConsumeService
platformConsumeService.url=/platform/platform_consume_service
platformConsumeService.ConnectTimeout=1000
platformConsumeService.ReadTimeout=3000
platformConsumeService.enableRetry=true

kgrpcProxy.applicationName=kgrpc_proxy
kgrpcProxy.protocol=http
kgrpcProxy.enableRetry=true
kgrpcProxy.enableGateWay=true
kgrpcProxy.ConnectTimeout=3000
kgrpcProxy.ReadTimeout=3000

banAccountService.applicationName=fxsoa_banaccount
banAccountService.protocol=thrift
banAccountService.className=com.kugou.fanxing.thrift.banaccount.service.BanAccountService
banAccountService.url=/soa/banaccount/thrift/service
banAccountService.enableRetry=true
banAccountService.enableGateWay=true
banAccountService.ConnectTimeout=3000
banAccountService.ReadTimeout=3000

couponService.applicationName=platform_coupon_service
couponService.protocol=thrift
couponService.className=com.kugou.fanxing.coupon.thrift.CouponService
couponService.url=/platform_coupon_service/couponService
couponService.enableRetry=true
couponService.enableGateWay=true
couponService.ConnectTimeout=3000
couponService.ReadTimeout=3000

couponReadService.applicationName=platform_coupon_service
couponReadService.protocol=thrift
couponReadService.className=com.kugou.fanxing.coupon.thrift.read.CouponReadService
couponReadService.url=/platform_coupon_service/couponReadService
couponReadService.enableRetry=true
couponReadService.enableGateWay=true
couponReadService.ConnectTimeout=3000
couponReadService.ReadTimeout=3000

assetService.applicationName=platform_asset_service
assetService.protocol=thrift
assetService.className=com.kugou.fanxing.platform.asset.service.thrift.AssetService
assetService.url=/platform_asset_service/assetService
assetService.connectTimeout=1000
assetService.readTimeout=5000
assetService.enableRetry=false

assetReadService.applicationName=platform_asset_service
assetReadService.protocol=thrift
assetReadService.className=com.kugou.fanxing.platform.asset.readservice.thrift.AssetReadService
assetReadService.url=/platform_asset_service/assetReadService
assetReadService.connectTimeout=1000
assetReadService.readTimeout=3000
assetReadService.enableRetry=true

KuwoIdMappingService.applicationName=kw_idservice
KuwoIdMappingService.protocol=thrift
KuwoIdMappingService.className=com.kugou.kw.idservice.api.struct.KuwoIdMappingService
KuwoIdMappingService.url=/soa/kw/idservice/thrift/idmapping
KuwoIdMappingService.ConnectTimeout=1000
KuwoIdMappingService.ReadTimeout=2000

budgetResourceService.applicationName=platform_budget_service
budgetResourceService.protocol=thrift
budgetResourceService.className=com.kugou.fanxing.thrift.budget.resource.BudgetResourceService
budgetResourceService.url=/platform/platform_budget_service/budget_resource
budgetResourceService.connectTimeout=1000
budgetResourceService.readTimeout=3000

KuwoTokenService.applicationName=kw_checktoken
KuwoTokenService.protocol=thrift
KuwoTokenService.className=com.kugou.kw.token.api.KuwoTokenService
KuwoTokenService.url=/kw/token/thrift/tokenservice
KuwoTokenService.ConnectTimeout=1000
KuwoTokenService.ReadTimeout=2000

starQueryService.applicationName=fx_star_service
starQueryService.protocol=thrift
starQueryService.className=com.kugou.fanxing.star.api.StarQueryService
starQueryService.url=/api/star/query
starQueryService.ConnectTimeout=1000
starQueryService.ReadTimeout=2000
starQueryService.enableRetry=true

fanGroupService.applicationName=platform_guard_service
fanGroupService.protocol=thrift
fanGroupService.className=com.kugou.fanxing.thrift.fangroup.service.FanGroupService
fanGroupService.url=/platform_fangroup_service/fanGroup
fanGroupService.connectTimeout=1000
fanGroupService.readTimeout=2000
fanGroupService.enableRetry=true

appStoreService.applicationName=mobile_show_pay
appStoreService.protocol=thrift
appStoreService.className=com.kugou.fanxing.thrift.recharge.ios.AppStoreRpcService
appStoreService.url=/platform/pay/thrift/appStoreRpcService
appStoreService.ConnectTimeout=500
appStoreService.ReadTimeout=500
appStoreService.enableRetry=true

rechargeGrayPlanService.applicationName=fx_activity_register
rechargeGrayPlanService.protocol=thrift
rechargeGrayPlanService.className=com.kugou.fanxing.activity.register.recharge.RechargeGrayPlanService
rechargeGrayPlanService.url=/fx/activity/register/recharge/grayPlan.thrift
rechargeGrayPlanService.ConnectTimeout=500
rechargeGrayPlanService.ReadTimeout=3000
rechargeGrayPlanService.enableRetry=true

singConsumeReadService.applicationName=sing_consume_service
singConsumeReadService.protocol=thrift
singConsumeReadService.className=com.kugou.fanxing.thrift.consume.read.service.PlatformConsumeReadService
singConsumeReadService.url=/platform_consume_read_service/platform_consume_service
singConsumeReadService.ConnectTimeout=500
singConsumeReadService.ReadTimeout=3000
singConsumeReadService.enableRetry=true

singAddCoinService.applicationName=sing_consume_service
singAddCoinService.protocol=thrift
singAddCoinService.className=com.kugou.fanxing.thrift.freeze.service.PlatformAddCoinService
singAddCoinService.url=/platform_consume_service/add_coin
singAddCoinService.connectTimeout=1000
singAddCoinService.readTimeout=3000
singAddCoinService.enableRetry=true

singConsumeService.applicationName=sing_consume_service
singConsumeService.protocol=thrift
singConsumeService.className=com.kugou.fanxing.thrift.consume.service.PlatformConsumeService
singConsumeService.url=/platform/platform_consume_service
singConsumeService.ConnectTimeout=1000
singConsumeService.ReadTimeout=3000
singConsumeService.enableRetry=true

appDispatchServiceV2.applicationName = platform_socket_message
appDispatchServiceV2.protocol = thrift
appDispatchServiceV2.className = com.kugou.fanxing.thrift.acksocket.gather.service.AppDispatchServiceV2
appDispatchServiceV2.url = /socket/v2/message.thrift
appDispatchServiceV2.ConnectTimeout=300
appDispatchServiceV2.ReadTimeout=600
appDispatchServiceV2.enableRetry=true