[redis]
redisCluster.cluster.servers = @redisCluster.cluster.servers@
redisCluster.cluster.password = @redisCluster.cluster.password@

[mysql]
d_fanxing_recharge.druid.master.url=@d_fanxing_recharge.druid.master.url@
d_fanxing_recharge.druid.master.username=@d_fanxing_recharge.druid.master.username@
d_fanxing_recharge.druid.master.password=@d_fanxing_recharge.druid.master.password@
d_fanxing_recharge.consul.master.dbServiceName=@d_fanxing_recharge.consul.master.dbServiceName@
d_fanxing_recharge.consul.master.dbServiceTags=master
d_fanxing_recharge.druid.slave.urliplist=@d_fanxing_recharge.druid.slave.urliplist@
d_fanxing_recharge.druid.slave.urlparam=@d_fanxing_recharge.druid.slave.urlparam@
d_fanxing_recharge.druid.slave.database=@d_fanxing_recharge.druid.slave.database@
d_fanxing_recharge.druid.slave.username=@d_fanxing_recharge.druid.slave.username@
d_fanxing_recharge.druid.slave.password=@d_fanxing_recharge.druid.slave.password@
d_fanxing_recharge.consul.slave.dbServiceName=@d_fanxing_recharge.consul.slave.dbServiceName@
d_fanxing_recharge.consul.slave.dbServiceTags=@d_fanxing_recharge.consul.slave.dbServiceTags@
d_fanxing_recharge.basePackage=com.kugou.fanxing.recharge.dao.d_fanxing_recharge,com.kugou.mq.pulsar.transaction.dao
d_fanxing_recharge.transactionManager.name=transactionManager_d_fanxing_recharge
d_fanxing_recharge.maxWait=3000
d_fanxing_recharge.maxActive=64
