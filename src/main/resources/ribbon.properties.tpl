globalIdService.applicationName=platform_globalid_v2
globalIdService.protocol=thrift
globalIdService.className=com.kugou.api.springcloud.GlobalIdServiceThrift.GlobalIdService
globalIdService.url=/service
globalIdService.ConnectTimeout=300
globalIdService.ReadTimeout=300
globalIdService.enableRetry=true

tokenService.applicationName=fxsoa_checktoken
tokenService.protocol=thrift
tokenService.className=com.kugou.fanxing.thrift.token.service.TokenService
tokenService.url=/soa/token/thrift/tokenservice
tokenService.ConnectTimeout=300
tokenService.ReadTimeout=300
tokenService.enableRetry=true

userIdMappingService.applicationName=fx_idservice
userIdMappingService.protocol=thrift
userIdMappingService.className=com.kugou.fanxing.thrift.idmapping.user.UserIdMappingService
userIdMappingService.url=/soa/idservice/thrift/idmapping
userIdMappingService.ConnectTimeout=300
userIdMappingService.ReadTimeout=300
userIdMappingService.enableRetry=true

userModuleV2BizService.applicationName=fxsoa_user_baseinfo
userModuleV2BizService.protocol=thrift
userModuleV2BizService.className=com.kugou.fanxing.userbaseinfo.service.UserModuleV2BizService
userModuleV2BizService.url=/soa/usermodule/userv2service.thrift
userModuleV2BizService.ConnectTimeout=300
userModuleV2BizService.ReadTimeout=300
userModuleV2BizService.enableRetry=true

platformOperationService.applicationName=platform_operation_service
platformOperationService.protocol=thrift
platformOperationService.className=com.kugou.fanxing.thrift.operation.FamilyControllService
platformOperationService.url=/platform/platform-operation-service/familyControllService
platformOperationService.ConnectTimeout=300
platformOperationService.ReadTimeout=300
platformOperationService.enableRetry=true

strategyService.applicationName=platform_strategy_service
strategyService.protocol=thrift
strategyService.className=com.kugou.fanxing.risk.sdk.thrift.strategy.service.PlatformStrategyService
strategyService.url=/platform/strategy
strategyService.ConnectTimeout=300
strategyService.ReadTimeout=350
strategyService.enableRetry=false

rechargeInfoQueryThriftService.applicationName=platform_recharge_asset_allocate
rechargeInfoQueryThriftService.protocol=thrift
rechargeInfoQueryThriftService.className=com.kugou.platform.recharge.queryservice.thrift.RechargeInfoQueryThriftService
rechargeInfoQueryThriftService.url=/platform/recharge/queryservice
rechargeInfoQueryThriftService.ConnectTimeout=300
rechargeInfoQueryThriftService.ReadTimeout=300
rechargeInfoQueryThriftService.enableRetry=true

AfterRechargeAssetAllocateReadService.applicationName=platform_recharge_asset_allocate
AfterRechargeAssetAllocateReadService.protocol=thrift
AfterRechargeAssetAllocateReadService.className=com.kugou.platform.after.recharge.asset.allocate.thrift.AfterRechargeAssetAllocateReadService
AfterRechargeAssetAllocateReadService.url=/platform/afterrecharge/read_service
AfterRechargeAssetAllocateReadService.ConnectTimeout=300
AfterRechargeAssetAllocateReadService.ReadTimeout=300
AfterRechargeAssetAllocateReadService.enableRetry=true

fXIpService.applicationName=fx_ip_service
fXIpService.protocol=thrift
fXIpService.className=com.kugou.fanxing.ip.api.FXIpService
fXIpService.url=/fx-ip-service/api/common/ipInfo
fXIpService.ConnectTimeout=300
fXIpService.ReadTimeout=300
fXIpService.enableRetry=true

sensitiveService.applicationName=fx_sensitive_service
sensitiveService.protocol=thrift
sensitiveService.className=com.kugou.fanxing.thrift.service.sensitive.SensitiveService
sensitiveService.url=/soa/user/thrift/sensitive
sensitiveService.ConnectTimeout=300
sensitiveService.ReadTimeout=300
sensitiveService.enableRetry=false
sensitiveService.enableGateWay=true

platformCertificationService.applicationName=platform_certification_service
platformCertificationService.protocol=thrift
platformCertificationService.className=com.kugou.fanxing.thrift.certification.PlatformCertificationService
platformCertificationService.url=/platform/thrift/certificationService
platformCertificationService.ConnectTimeout=300
platformCertificationService.ReadTimeout=300

dataAlterationAuditService.applicationName=fx_data_alteration_audit_service
dataAlterationAuditService.protocol=thrift
dataAlterationAuditService.className=com.kugou.fanxing.thrift.service.data.alteration.DataAlterationAuditService
dataAlterationAuditService.url=/soa/alteration/thrift/log
dataAlterationAuditService.ConnectTimeout=300
dataAlterationAuditService.ReadTimeout=300
dataAlterationAuditService.enableRetry=true

couponListService.applicationName=mobile_mfx_activity_infiltrate
couponListService.protocol=thrift
couponListService.className=com.kugou.mfx.activity.infiltrate.thrift.service.CouponListService
couponListService.url=/mfx-activity-infiltrate/thrift/coupon/couponListService.thrift
couponListService.ConnectTimeout=300
couponListService.ReadTimeout=300
couponListService.enableRetry=true

fxSms.applicationName=fx_sms
fxSms.protocol=thrift
fxSms.className=com.kugou.fanxing.thrift.sms.SmsSendService
fxSms.url=/soa/sms/thrift/sendsms
fxSms.readTimeout=300

userPlatBizService.applicationName=fxsoa_soa_user
userPlatBizService.protocol=thrift
userPlatBizService.className=com.kugou.fanxing.thrift.plat.user.UserPlatBizService
userPlatBizService.url=/plat/user/thrift/userplatbizservice
userPlatBizService.ConnectTimeout=300
userPlatBizService.ReadTimeout=300
userPlatBizService.enableRetry=true
userPlatBizService.enableGateWay=true

rechargeAllocateQueryService.applicationName=platform_pay_service
rechargeAllocateQueryService.protocol=thrift
rechargeAllocateQueryService.className=com.kugou.fanxing.recharge.pay.thrift.RechargeAllocateQueryService
rechargeAllocateQueryService.url=/platform_pay_service/thrift/rechargeAllocateQueryService
rechargeAllocateQueryService.ConnectTimeout=300
rechargeAllocateQueryService.ReadTimeout=300
rechargeAllocateQueryService.enableRetry=true

platformPayV2Service.applicationName=platform_pay_service
platformPayV2Service.protocol=thrift
platformPayV2Service.className=com.kugou.fanxing.thrift.pay.v2.PlatformPayV2Service
platformPayV2Service.url=/platform_pay_service/thrift/platformPayV2Service
platformPayV2Service.ConnectTimeout=@platformPayV2Service.ConnectTimeout@
platformPayV2Service.ReadTimeout=@platformPayV2Service.ReadTimeout@
platformPayV2Service.enableRetry=true

noviceRechargeService.applicationName=platform_pay_service
noviceRechargeService.protocol=thrift
noviceRechargeService.className=com.kugou.fanxing.thrift.pay.novicerecharge.NoviceRechargeService
noviceRechargeService.url=/platform_pay_service/thrift/noviceRechargeService
noviceRechargeService.ConnectTimeout=@noviceRechargeService.ConnectTimeout@
noviceRechargeService.ReadTimeout=@noviceRechargeService.ReadTimeout@
noviceRechargeService.enableRetry=true

goodsGiftService.applicationName=fanxing_finance_goods
goodsGiftService.protocol=thrift
goodsGiftService.className=com.kugou.fanxing.finance.goods.gift.GoodsGiftService
goodsGiftService.url=/fanxing_finance_goods/gift_service
goodsGiftService.ConnectTimeout=300
goodsGiftService.ReadTimeout=300
goodsGiftService.enableRetry=true

platformConsumeReadService.applicationName=platform_consume_service
platformConsumeReadService.protocol=thrift
platformConsumeReadService.className=com.kugou.fanxing.thrift.consume.read.service.PlatformConsumeReadService
platformConsumeReadService.url=/platform_consume_read_service/platform_consume_service
platformConsumeReadService.ConnectTimeout=@platformConsumeReadService.ConnectTimeout@
platformConsumeReadService.ReadTimeout=@platformConsumeReadService.ReadTimeout@
platformConsumeReadService.enableRetry=true

platformAddCoinService.applicationName=platform_consume_service
platformAddCoinService.protocol=thrift
platformAddCoinService.className=com.kugou.fanxing.thrift.freeze.service.PlatformAddCoinService
platformAddCoinService.url=/platform_consume_service/add_coin
platformAddCoinService.connectTimeout=@platformAddCoinService.connectTimeout@
platformAddCoinService.readTimeout=@platformAddCoinService.readTimeout@
platformAddCoinService.enableRetry=true

platformConsumeService.applicationName=platform_consume_service
platformConsumeService.protocol=thrift
platformConsumeService.className=com.kugou.fanxing.thrift.consume.service.PlatformConsumeService
platformConsumeService.url=/platform/platform_consume_service
platformConsumeService.ConnectTimeout=@platformConsumeService.ConnectTimeout@
platformConsumeService.ReadTimeout=@platformConsumeService.ReadTimeout@
platformConsumeService.enableRetry=true

kgrpcProxy.applicationName=kgrpc_proxy
kgrpcProxy.protocol=http
kgrpcProxy.enableRetry=true
kgrpcProxy.enableGateWay=true
kgrpcProxy.ConnectTimeout=@kgrpcProxy.ConnectTimeout@
kgrpcProxy.ReadTimeout=@kgrpcProxy.ReadTimeout@

banAccountService.applicationName=fxsoa_banaccount
banAccountService.protocol=thrift
banAccountService.className=com.kugou.fanxing.thrift.banaccount.service.BanAccountService
banAccountService.url=/soa/banaccount/thrift/service
banAccountService.ConnectTimeout=300
banAccountService.ReadTimeout=300
banAccountService.enableRetry=true
banAccountService.enableGateWay=true

couponService.applicationName=platform_coupon_service
couponService.protocol=thrift
couponService.className=com.kugou.fanxing.coupon.thrift.CouponService
couponService.url=/platform_coupon_service/couponService
couponService.enableRetry=true
couponService.enableGateWay=true
couponService.ConnectTimeout=@couponService.ConnectTimeout@
couponService.ReadTimeout=@couponService.ReadTimeout@

couponReadService.applicationName=platform_coupon_service
couponReadService.protocol=thrift
couponReadService.className=com.kugou.fanxing.coupon.thrift.read.CouponReadService
couponReadService.url=/platform_coupon_service/couponReadService
couponReadService.enableRetry=true
couponReadService.enableGateWay=true
couponReadService.ConnectTimeout=@couponReadService.ConnectTimeout@
couponReadService.ReadTimeout=@couponReadService.ReadTimeout@

assetService.applicationName=platform_asset_service
assetService.protocol=thrift
assetService.className=com.kugou.fanxing.platform.asset.service.thrift.AssetService
assetService.url=/platform_asset_service/assetService
assetService.connectTimeout=@assetService.connectTimeout@
assetService.readTimeout=@assetService.readTimeout@
assetService.enableRetry=false

assetReadService.applicationName=platform_asset_service
assetReadService.protocol=thrift
assetReadService.className=com.kugou.fanxing.platform.asset.readservice.thrift.AssetReadService
assetReadService.url=/platform_asset_service/assetReadService
assetReadService.connectTimeout=@assetReadService.connectTimeout@
assetReadService.readTimeout=@assetReadService.readTimeout@
assetReadService.enableRetry=true

KuwoIdMappingService.applicationName=kw_idservice
KuwoIdMappingService.protocol=thrift
KuwoIdMappingService.className=com.kugou.kw.idservice.api.struct.KuwoIdMappingService
KuwoIdMappingService.url=/soa/kw/idservice/thrift/idmapping
KuwoIdMappingService.ConnectTimeout=300
KuwoIdMappingService.ReadTimeout=600

budgetResourceService.applicationName=platform_budget_service
budgetResourceService.protocol=thrift
budgetResourceService.className=com.kugou.fanxing.thrift.budget.resource.BudgetResourceService
budgetResourceService.url=/platform/platform_budget_service/budget_resource
budgetResourceService.connectTimeout=300
budgetResourceService.readTimeout=600

KuwoTokenService.applicationName=kw_checktoken
KuwoTokenService.protocol=thrift
KuwoTokenService.className=com.kugou.kw.token.api.KuwoTokenService
KuwoTokenService.url=/kw/token/thrift/tokenservice
KuwoTokenService.ConnectTimeout=300
KuwoTokenService.ReadTimeout=600

starQueryService.applicationName=fx_star_service
starQueryService.protocol=thrift
starQueryService.className=com.kugou.fanxing.star.api.StarQueryService
starQueryService.url=/api/star/query
starQueryService.ConnectTimeout=@starQueryService.ConnectTimeout@
starQueryService.ReadTimeout=@starQueryService.ReadTimeout@
starQueryService.enableRetry=true

fanGroupService.applicationName=platform_guard_service
fanGroupService.protocol=thrift
fanGroupService.className=com.kugou.fanxing.thrift.fangroup.service.FanGroupService
fanGroupService.url=/platform_fangroup_service/fanGroup
fanGroupService.connectTimeout=@fanGroupService.connectTimeout@
fanGroupService.readTimeout=@fanGroupService.readTimeout@
fanGroupService.enableRetry=true

appStoreService.applicationName=mobile_show_pay
appStoreService.protocol=thrift
appStoreService.className=com.kugou.fanxing.thrift.recharge.ios.AppStoreRpcService
appStoreService.url=/platform/pay/thrift/appStoreRpcService
appStoreService.ConnectTimeout=500
appStoreService.ReadTimeout=500
appStoreService.enableRetry=true

rechargeGrayPlanService.applicationName=fx_activity_register
rechargeGrayPlanService.protocol=thrift
rechargeGrayPlanService.className=com.kugou.fanxing.activity.register.recharge.RechargeGrayPlanService
rechargeGrayPlanService.url=/fx/activity/register/recharge/grayPlan.thrift
rechargeGrayPlanService.ConnectTimeout=300
rechargeGrayPlanService.ReadTimeout=300

singConsumeReadService.applicationName=sing_consume_service
singConsumeReadService.protocol=thrift
singConsumeReadService.className=com.kugou.fanxing.thrift.consume.read.service.PlatformConsumeReadService
singConsumeReadService.url=/platform_consume_read_service/platform_consume_service
singConsumeReadService.ConnectTimeout=@singConsumeReadService.ConnectTimeout@
singConsumeReadService.ReadTimeout=@singConsumeReadService.ReadTimeout@
singConsumeReadService.enableRetry=true

singAddCoinService.applicationName=sing_consume_service
singAddCoinService.protocol=thrift
singAddCoinService.className=com.kugou.fanxing.thrift.freeze.service.PlatformAddCoinService
singAddCoinService.url=/platform_consume_service/add_coin
singAddCoinService.connectTimeout=@singAddCoinService.connectTimeout@
singAddCoinService.readTimeout=@singAddCoinService.readTimeout@
singAddCoinService.enableRetry=true

singConsumeService.applicationName=sing_consume_service
singConsumeService.protocol=thrift
singConsumeService.className=com.kugou.fanxing.thrift.consume.service.PlatformConsumeService
singConsumeService.url=/platform/platform_consume_service
singConsumeService.ConnectTimeout=@singConsumeService.ConnectTimeout@
singConsumeService.ReadTimeout=@singConsumeService.ReadTimeout@
singConsumeService.enableRetry=true

appDispatchServiceV2.applicationName = platform_socket_message
appDispatchServiceV2.protocol = thrift
appDispatchServiceV2.className = com.kugou.fanxing.thrift.acksocket.gather.service.AppDispatchServiceV2
appDispatchServiceV2.url = /socket/v2/message.thrift
appDispatchServiceV2.ConnectTimeout=300
appDispatchServiceV2.ReadTimeout=600
appDispatchServiceV2.enableRetry=true