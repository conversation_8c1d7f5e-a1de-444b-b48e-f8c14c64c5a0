# dc
dataCenter.name=bj-sjq
STRATEGY_CONFIG_NAME=bjzw

# server
spring.application.name=platform_recharge_service
spring.profiles.active=dev
server.port=18888

# apollo
apollo.server=http://***********:8080
apollo.cluster=default
eureka.client.region=FXBJ

# cat
cat.server.ip=**********
cat.client.id=platform_recharge_service

# log
log.level=warn

# kupay
kupay.internet=http://kupay.kugou.com
kupay.intranet=http://kupay.kugou.com
kupay.intranet.out.sea=http://istar-pay.kgidc.cn
kupay.callbackKey=kugoufanxing2016
kupay.success.orders.url = http://kupay.kugou.com/v1/orderstatus/getbypaytime
kupay.orderstatus.getbyorderno.url=http://kupay.kugou.com/v1/orderstatus/getbyorderno
kupay.orderstatus.getbyorderno.token=ODZiNDIzN
kupay.withdraw.serverId=1870
kupay.withdraw.serverKey=rYyNI4G7BdzzCYfXww3eHaW1U0haNcqE

syncUrl=http://fanxing.kugou.com/index.php?action=rechargeFinish
syncUrlPaypal=http://fanxing.kugou.com/index.php?action=rechargePaypalFinish
syncUrlAlipayQr=http://fanxing.kugou.com/index.php?action=userRechargeList

# ç©åæå¡
finance.goods.appKey=3i80mz16rj

# é¢ç®
platform.budget.service.appId=8
platform.budget.service.appKey=vi3lidtoyg

# nsq
mq.nsq.lookup = FXBJ,FXGZ
mq.nsq.nsqd = **********
risk.sdk.filter.uris=/recharge/api/v1/getOrderForPayPal,/recharge/api/v1/getOrderForWxgzh,/recharge/api/v1/getRechargeList,/RechargePlat/RechargeService/RechargePayService/aliPayM,/RechargePlat/RechargeService/RechargePayService/weixinPayM,/RechargePlat/RechargeService/RechargePayService/bankPayM,/recharge/api/v1/createOrderForGp

#pulsar
# token
kugou.pulsar.token=******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kugou.pulsar.region=SJQ

kugou.pulsar.tx.shardingStrategy=month

#pulsarå¯ç¨å¯é æ¶æ¯
kugou.pulsar.tx.enable=true
#producer
kugou.pulsar.producer.topics=recharge

#topic
kugou.pulsar.producer.recharge.topic-name=persistent://revenue_group/platform_recharge_service/rechargeSuccess
kugou.pulsar.producer.singRecharge.topic-name=persistent://revenue_group/platform_recharge_service/singRechargeSuccess
kugou.pulsar.producer.recharge.refund.topic-name=persistent://revenue_group/platform_recharge_service/refundSuccess






