<configuration>
	<springProperty scope="context" name="level" source="log.level"/> 

	<property name="log.dir" value="work/log/${POD_NAME:-/}"/>

	<logger name="com.kugou.mq.pulsar.core.haclient" level="error"/>
    
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${log.dir}/app.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${log.dir}/app.%d{yyyy-MM-dd}.log.gz</FileNamePattern>
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{HH:mm:ss} %-5level [%thread]%logger{16} - %msg%L%n</pattern>
        </encoder>
    </appender>
	<appender name="FILE_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
		<discardingThreshold>0</discardingThreshold>
		<queueSize>512</queueSize>
		<appender-ref ref="FILE" />
	</appender>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<!-- encoder 默认配置为PatternLayoutEncoder -->
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss} [%-5level] %C  %L %msg%n</pattern>
		</encoder>
	</appender>
    <appender name="STDOUT_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <appender-ref ref="STDOUT" />
    </appender>
    
    <appender name="CAT" class="com.dianping.cat.log.logback.LogAppender"></appender>
    <appender name="CAT_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <appender-ref ref="CAT" />
    </appender>

    <logger name="com.trendrr.nsq.Connection" level="WARN" additivity="false">
        <appender-ref ref="FILE_ASYNC" />
    </logger>

    <springProfile name="dev">
	    <root level="${level}">
	    	<appender-ref ref="FILE_ASYNC" />
	        <appender-ref ref="STDOUT" />
	        <appender-ref ref="CAT" />
	    </root>
	</springProfile>

	<springProfile name="test">
        <root level="${level}">
            <appender-ref ref="FILE_ASYNC" />
            <appender-ref ref="STDOUT_ASYNC" />
            <appender-ref ref="CAT_ASYNC" />
        </root>
    </springProfile>
	
	<springProfile name="prod">
	    <root level="${level}">
	    	<appender-ref ref="FILE_ASYNC" />
        	<appender-ref ref="CAT_ASYNC" />
	    </root>
	</springProfile>
     
</configuration>