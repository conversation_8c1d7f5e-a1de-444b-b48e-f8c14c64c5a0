<configuration>
	<springProperty scope="context" name="level" source="log.level"/> 

	<property name="log.dir" value="work/log"/>

	<logger name="com.kugou.mq.pulsar.core.haclient" level="error"/>
	<logger name="com.kugou.fanxing.recharge.dao" level="warn"/>

	<appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${log.dir}/app.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${log.dir}/app.%d{yyyy-MM-dd}.log.gz</FileNamePattern>
            <MaxHistory>1</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{HH:mm:ss} %-5level [%thread]%logger{16} - %msg%L%n
            </pattern>
        </encoder>
    </appender>

    <!-- 异步输出 -->
	<appender name="FILE_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>256</queueSize>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="FILE" />
	</appender>

    <!-- 输出到控制台 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<!-- encoder 默认配置为PatternLayoutEncoder -->
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss} [%-4level] [%thread]%logger{16} %C  %L %msg%n</pattern>
		</encoder>
	</appender>
    
     <!-- 输出到CAT -->
    <appender name="CAT" class="com.dianping.cat.log.logback.LogAppender"></appender>

    <springProfile name="dev">

	    <logger name="com.trendrr.nsq.Connection" level="WARN" additivity="false">
	        <appender-ref ref="FILE_ASYNC" />
	    </logger>
    	
	    <root level="${level}"> 
	    	<appender-ref ref="FILE_ASYNC" />
			<appender-ref ref="CAT" />
			<appender-ref ref="STDOUT" />
	    </root>
	</springProfile>

	<springProfile name="test">

		<logger name="com.trendrr.nsq.Connection" level="WARN" additivity="false">
			<appender-ref ref="FILE_ASYNC" />
		</logger>

		<root level="${level}">
			<appender-ref ref="FILE_ASYNC" />
			<appender-ref ref="CAT" />
			<appender-ref ref="STDOUT" />
		</root>
	</springProfile>
	
	<springProfile name="prod">

	    <logger name="com.trendrr.nsq.Connection" level="WARN" additivity="false">
	        <appender-ref ref="FILE_ASYNC" />
	    </logger>
	
	    <root level="${level}"> 
	    	<appender-ref ref="FILE_ASYNC" />
        	<appender-ref ref="CAT" />
	    </root>
	</springProfile>
     
</configuration>