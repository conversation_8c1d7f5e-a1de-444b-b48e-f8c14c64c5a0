# dc
dataCenter.name=@dataCenter.name@
STRATEGY_CONFIG_NAME=@STRATEGY_CONFIG_NAME@

# server
spring.application.name=@spring.application.name@
spring.profiles.active=@spring.profiles.active@
server.port=@server.port@

# apollo
apollo.server=@apollo.server@
apollo.cluster=@apollo.cluster@
eureka.client.region=@eureka.client.region@

# cat
cat.server.ip=@cat.server.ip@
cat.client.id=platform_recharge_service

# log
log.level=@log.level@

# kupay
kupay.internet=@kupay.internet@
kupay.intranet=@kupay.intranet@
kupay.intranet.out.sea=@kupay.intranet.out.sea@
kupay.callbackKey=@kupay.callbackKey@
kupay.success.orders.url=@kupay.success.orders.url@
kupay.orderstatus.getbyorderno.url=@kupay.orderstatus.getbyorderno.url@
kupay.orderstatus.getbyorderno.token=@kupay.orderstatus.getbyorderno.token@
kupay.withdraw.serverId=@kupay.withdraw.serverId@
kupay.withdraw.serverKey=@kupay.withdraw.serverKey@

syncUrl=@syncUrl@
syncUrlPaypal=@syncUrlPaypal@
syncUrlAlipayQr=@syncUrlAlipayQr@

#物品服务
finance.goods.appKey=@finance.goods.appKey@

# 预算
platform.budget.service.appId=@platform.budget.service.appId@
platform.budget.service.appKey=@platform.budget.service.appKey@

# nsq
mq.nsq.lookup = FXBJ,FXGZ
mq.nsq.nsqd = @mq.nsq.nsqd@
risk.sdk.filter.uris=@risk.sdk.filter.uris@

#pulsar
#token
kugou.pulsar.token=@kugou.pulsar.token@
kugou.pulsar.region=@kugou.pulsar.region@

kugou.pulsar.tx.shardingStrategy=month

#pulsar启用可靠消息
kugou.pulsar.tx.enable=true


#producer
kugou.pulsar.producer.topics=recharge

#topic
kugou.pulsar.producer.recharge.topic-name=persistent://revenue_group/platform_recharge_service/rechargeSuccess
kugou.pulsar.producer.singRecharge.topic-name=persistent://revenue_group/platform_recharge_service/singRechargeSuccess
kugou.pulsar.producer.recharge.refund.topic-name=persistent://revenue_group/platform_recharge_service/refundSuccess

