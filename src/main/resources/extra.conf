[redis]
redisCluster.cluster.servers = ***********:7000,***********:7001,***********:7002
redisCluster.cluster.password = mT17YNGycD

[mysql]
d_fanxing_recharge.druid.master.url=**************************************************************************************************************************************
d_fanxing_recharge.druid.master.username=fanxing
d_fanxing_recharge.druid.master.password=kugou2014
d_fanxing_recharge.consul.master.dbServiceName=
d_fanxing_recharge.consul.master.dbServiceTags=master
d_fanxing_recharge.druid.slave.urliplist=***********:3306
d_fanxing_recharge.druid.slave.urlparam=autoReconnect=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false
d_fanxing_recharge.druid.slave.database=d_fanxing_recharge
d_fanxing_recharge.druid.slave.username=fanxing
d_fanxing_recharge.druid.slave.password=kugou2014
d_fanxing_recharge.consul.slave.dbServiceName=
d_fanxing_recharge.consul.slave.dbServiceTags=slave
d_fanxing_recharge.consul.consulServers=
d_fanxing_recharge.basePackage=com.kugou.fanxing.recharge.dao.d_fanxing_recharge,com.kugou.mq.pulsar.transaction.dao
d_fanxing_recharge.transactionManager.name=transactionManager_d_fanxing_recharge
d_fanxing_recharge.maxWait=3000

