/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.platform.after.recharge.asset.allocate.thrift;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-07-19")
public class GetRechargePresentRequest implements org.apache.thrift.TBase<GetRechargePresentRequest, GetRechargePresentRequest._Fields>, java.io.Serializable, Cloneable, Comparable<GetRechargePresentRequest> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GetRechargePresentRequest");

  private static final org.apache.thrift.protocol.TField MONEYS_FIELD_DESC = new org.apache.thrift.protocol.TField("moneys", org.apache.thrift.protocol.TType.LIST, (short)1);
  private static final org.apache.thrift.protocol.TField KG_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kgId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField ACT_FLAG_FIELD_DESC = new org.apache.thrift.protocol.TField("actFlag", org.apache.thrift.protocol.TType.STRING, (short)3);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new GetRechargePresentRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new GetRechargePresentRequestTupleSchemeFactory());
  }

  /**
   * 请求的金额列表
   * 
   */
  public List<Long> moneys; // required
  /**
   * 酷狗id
   * 
   */
  public long kgId; // optional
  /**
   * 业务标识id
   * 
   */
  public String actFlag; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 请求的金额列表
     * 
     */
    MONEYS((short)1, "moneys"),
    /**
     * 酷狗id
     * 
     */
    KG_ID((short)2, "kgId"),
    /**
     * 业务标识id
     * 
     */
    ACT_FLAG((short)3, "actFlag");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // MONEYS
          return MONEYS;
        case 2: // KG_ID
          return KG_ID;
        case 3: // ACT_FLAG
          return ACT_FLAG;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __KGID_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.KG_ID,_Fields.ACT_FLAG};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.MONEYS, new org.apache.thrift.meta_data.FieldMetaData("moneys", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
    tmpMap.put(_Fields.KG_ID, new org.apache.thrift.meta_data.FieldMetaData("kgId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ACT_FLAG, new org.apache.thrift.meta_data.FieldMetaData("actFlag", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetRechargePresentRequest.class, metaDataMap);
  }

  public GetRechargePresentRequest() {
  }

  public GetRechargePresentRequest(
    List<Long> moneys)
  {
    this();
    this.moneys = moneys;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GetRechargePresentRequest(GetRechargePresentRequest other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetMoneys()) {
      List<Long> __this__moneys = new ArrayList<Long>(other.moneys);
      this.moneys = __this__moneys;
    }
    this.kgId = other.kgId;
    if (other.isSetActFlag()) {
      this.actFlag = other.actFlag;
    }
  }

  public GetRechargePresentRequest deepCopy() {
    return new GetRechargePresentRequest(this);
  }

  @Override
  public void clear() {
    this.moneys = null;
    setKgIdIsSet(false);
    this.kgId = 0;
    this.actFlag = null;
  }

  public int getMoneysSize() {
    return (this.moneys == null) ? 0 : this.moneys.size();
  }

  public java.util.Iterator<Long> getMoneysIterator() {
    return (this.moneys == null) ? null : this.moneys.iterator();
  }

  public void addToMoneys(long elem) {
    if (this.moneys == null) {
      this.moneys = new ArrayList<Long>();
    }
    this.moneys.add(elem);
  }

  /**
   * 请求的金额列表
   * 
   */
  public List<Long> getMoneys() {
    return this.moneys;
  }

  /**
   * 请求的金额列表
   * 
   */
  public GetRechargePresentRequest setMoneys(List<Long> moneys) {
    this.moneys = moneys;
    return this;
  }

  public void unsetMoneys() {
    this.moneys = null;
  }

  /** Returns true if field moneys is set (has been assigned a value) and false otherwise */
  public boolean isSetMoneys() {
    return this.moneys != null;
  }

  public void setMoneysIsSet(boolean value) {
    if (!value) {
      this.moneys = null;
    }
  }

  /**
   * 酷狗id
   * 
   */
  public long getKgId() {
    return this.kgId;
  }

  /**
   * 酷狗id
   * 
   */
  public GetRechargePresentRequest setKgId(long kgId) {
    this.kgId = kgId;
    setKgIdIsSet(true);
    return this;
  }

  public void unsetKgId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KGID_ISSET_ID);
  }

  /** Returns true if field kgId is set (has been assigned a value) and false otherwise */
  public boolean isSetKgId() {
    return EncodingUtils.testBit(__isset_bitfield, __KGID_ISSET_ID);
  }

  public void setKgIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KGID_ISSET_ID, value);
  }

  /**
   * 业务标识id
   * 
   */
  public String getActFlag() {
    return this.actFlag;
  }

  /**
   * 业务标识id
   * 
   */
  public GetRechargePresentRequest setActFlag(String actFlag) {
    this.actFlag = actFlag;
    return this;
  }

  public void unsetActFlag() {
    this.actFlag = null;
  }

  /** Returns true if field actFlag is set (has been assigned a value) and false otherwise */
  public boolean isSetActFlag() {
    return this.actFlag != null;
  }

  public void setActFlagIsSet(boolean value) {
    if (!value) {
      this.actFlag = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case MONEYS:
      if (value == null) {
        unsetMoneys();
      } else {
        setMoneys((List<Long>)value);
      }
      break;

    case KG_ID:
      if (value == null) {
        unsetKgId();
      } else {
        setKgId((Long)value);
      }
      break;

    case ACT_FLAG:
      if (value == null) {
        unsetActFlag();
      } else {
        setActFlag((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case MONEYS:
      return getMoneys();

    case KG_ID:
      return getKgId();

    case ACT_FLAG:
      return getActFlag();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case MONEYS:
      return isSetMoneys();
    case KG_ID:
      return isSetKgId();
    case ACT_FLAG:
      return isSetActFlag();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof GetRechargePresentRequest)
      return this.equals((GetRechargePresentRequest)that);
    return false;
  }

  public boolean equals(GetRechargePresentRequest that) {
    if (that == null)
      return false;

    boolean this_present_moneys = true && this.isSetMoneys();
    boolean that_present_moneys = true && that.isSetMoneys();
    if (this_present_moneys || that_present_moneys) {
      if (!(this_present_moneys && that_present_moneys))
        return false;
      if (!this.moneys.equals(that.moneys))
        return false;
    }

    boolean this_present_kgId = true && this.isSetKgId();
    boolean that_present_kgId = true && that.isSetKgId();
    if (this_present_kgId || that_present_kgId) {
      if (!(this_present_kgId && that_present_kgId))
        return false;
      if (this.kgId != that.kgId)
        return false;
    }

    boolean this_present_actFlag = true && this.isSetActFlag();
    boolean that_present_actFlag = true && that.isSetActFlag();
    if (this_present_actFlag || that_present_actFlag) {
      if (!(this_present_actFlag && that_present_actFlag))
        return false;
      if (!this.actFlag.equals(that.actFlag))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_moneys = true && (isSetMoneys());
    list.add(present_moneys);
    if (present_moneys)
      list.add(moneys);

    boolean present_kgId = true && (isSetKgId());
    list.add(present_kgId);
    if (present_kgId)
      list.add(kgId);

    boolean present_actFlag = true && (isSetActFlag());
    list.add(present_actFlag);
    if (present_actFlag)
      list.add(actFlag);

    return list.hashCode();
  }

  @Override
  public int compareTo(GetRechargePresentRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetMoneys()).compareTo(other.isSetMoneys());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMoneys()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.moneys, other.moneys);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetKgId()).compareTo(other.isSetKgId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKgId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kgId, other.kgId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetActFlag()).compareTo(other.isSetActFlag());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActFlag()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actFlag, other.actFlag);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("GetRechargePresentRequest(");
    boolean first = true;

    sb.append("moneys:");
    if (this.moneys == null) {
      sb.append("null");
    } else {
      sb.append(this.moneys);
    }
    first = false;
    if (isSetKgId()) {
      if (!first) sb.append(", ");
      sb.append("kgId:");
      sb.append(this.kgId);
      first = false;
    }
    if (isSetActFlag()) {
      if (!first) sb.append(", ");
      sb.append("actFlag:");
      if (this.actFlag == null) {
        sb.append("null");
      } else {
        sb.append(this.actFlag);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (moneys == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'moneys' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GetRechargePresentRequestStandardSchemeFactory implements SchemeFactory {
    public GetRechargePresentRequestStandardScheme getScheme() {
      return new GetRechargePresentRequestStandardScheme();
    }
  }

  private static class GetRechargePresentRequestStandardScheme extends StandardScheme<GetRechargePresentRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GetRechargePresentRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // MONEYS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.moneys = new ArrayList<Long>(_list0.size);
                long _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = iprot.readI64();
                  struct.moneys.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setMoneysIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // KG_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.kgId = iprot.readI64();
              struct.setKgIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ACT_FLAG
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.actFlag = iprot.readString();
              struct.setActFlagIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GetRechargePresentRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.moneys != null) {
        oprot.writeFieldBegin(MONEYS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.moneys.size()));
          for (long _iter3 : struct.moneys)
          {
            oprot.writeI64(_iter3);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.isSetKgId()) {
        oprot.writeFieldBegin(KG_ID_FIELD_DESC);
        oprot.writeI64(struct.kgId);
        oprot.writeFieldEnd();
      }
      if (struct.actFlag != null) {
        if (struct.isSetActFlag()) {
          oprot.writeFieldBegin(ACT_FLAG_FIELD_DESC);
          oprot.writeString(struct.actFlag);
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GetRechargePresentRequestTupleSchemeFactory implements SchemeFactory {
    public GetRechargePresentRequestTupleScheme getScheme() {
      return new GetRechargePresentRequestTupleScheme();
    }
  }

  private static class GetRechargePresentRequestTupleScheme extends TupleScheme<GetRechargePresentRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GetRechargePresentRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      {
        oprot.writeI32(struct.moneys.size());
        for (long _iter4 : struct.moneys)
        {
          oprot.writeI64(_iter4);
        }
      }
      BitSet optionals = new BitSet();
      if (struct.isSetKgId()) {
        optionals.set(0);
      }
      if (struct.isSetActFlag()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetKgId()) {
        oprot.writeI64(struct.kgId);
      }
      if (struct.isSetActFlag()) {
        oprot.writeString(struct.actFlag);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GetRechargePresentRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      {
        org.apache.thrift.protocol.TList _list5 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
        struct.moneys = new ArrayList<Long>(_list5.size);
        long _elem6;
        for (int _i7 = 0; _i7 < _list5.size; ++_i7)
        {
          _elem6 = iprot.readI64();
          struct.moneys.add(_elem6);
        }
      }
      struct.setMoneysIsSet(true);
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.kgId = iprot.readI64();
        struct.setKgIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.actFlag = iprot.readString();
        struct.setActFlagIsSet(true);
      }
    }
  }

}

