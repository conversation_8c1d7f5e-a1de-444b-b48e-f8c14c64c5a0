/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.platform.order.v2.thrift;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2020-05-28")
public class SyncOrderService {

  /**
   * 同步订单模型
   * 
   */
  public interface Iface {

    /**
     * 订单根据规则扣费
     * 
     * 
     * @param orderRequest
     * @param consumeRequests
     */
    public OrderResponse createConsumeOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests) throws org.apache.thrift.TException;

    /**
     * 订单根据规则扣费发货
     * 
     * 
     * @param orderRequest
     * @param consumeRequests
     * @param ruleInfo
     */
    public OrderResponse createConsumeDeliverOrderByRule(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, RuleInfo ruleInfo) throws org.apache.thrift.TException;

    /**
     * 订单扣费发货
     * 
     * 
     * @param orderRequest
     * @param consumeRequests
     * @param goodsList
     */
    public OrderResponse createConsumeDeliverOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, GoodsList goodsList) throws org.apache.thrift.TException;

    /**
     * 订单根据规则发货
     * 
     * 
     * @param orderRequest
     * @param ruleInfo
     */
    public OrderResponse createDeliverOrderByRule(OrderRequest orderRequest, RuleInfo ruleInfo) throws org.apache.thrift.TException;

    /**
     * 订单直接发货
     * 
     * 
     * @param orderRequest
     * @param goodsList
     */
    public OrderResponse createDeliverOrder(OrderRequest orderRequest, GoodsList goodsList) throws org.apache.thrift.TException;

  }

  public interface AsyncIface {

    public void createConsumeOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void createConsumeDeliverOrderByRule(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, RuleInfo ruleInfo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void createConsumeDeliverOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, GoodsList goodsList, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void createDeliverOrderByRule(OrderRequest orderRequest, RuleInfo ruleInfo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void createDeliverOrder(OrderRequest orderRequest, GoodsList goodsList, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

  }

  public static class Client extends org.apache.thrift.TServiceClient implements Iface {
    public static class Factory implements org.apache.thrift.TServiceClientFactory<Client> {
      public Factory() {}
      public Client getClient(org.apache.thrift.protocol.TProtocol prot) {
        return new Client(prot);
      }
      public Client getClient(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
        return new Client(iprot, oprot);
      }
    }

    public Client(org.apache.thrift.protocol.TProtocol prot)
    {
      super(prot, prot);
    }

    public Client(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
      super(iprot, oprot);
    }

    public OrderResponse createConsumeOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests) throws org.apache.thrift.TException
    {
      send_createConsumeOrder(orderRequest, consumeRequests);
      return recv_createConsumeOrder();
    }

    public void send_createConsumeOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests) throws org.apache.thrift.TException
    {
      createConsumeOrder_args args = new createConsumeOrder_args();
      args.setOrderRequest(orderRequest);
      args.setConsumeRequests(consumeRequests);
      sendBase("createConsumeOrder", args);
    }

    public OrderResponse recv_createConsumeOrder() throws org.apache.thrift.TException
    {
      createConsumeOrder_result result = new createConsumeOrder_result();
      receiveBase(result, "createConsumeOrder");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "createConsumeOrder failed: unknown result");
    }

    public OrderResponse createConsumeDeliverOrderByRule(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, RuleInfo ruleInfo) throws org.apache.thrift.TException
    {
      send_createConsumeDeliverOrderByRule(orderRequest, consumeRequests, ruleInfo);
      return recv_createConsumeDeliverOrderByRule();
    }

    public void send_createConsumeDeliverOrderByRule(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, RuleInfo ruleInfo) throws org.apache.thrift.TException
    {
      createConsumeDeliverOrderByRule_args args = new createConsumeDeliverOrderByRule_args();
      args.setOrderRequest(orderRequest);
      args.setConsumeRequests(consumeRequests);
      args.setRuleInfo(ruleInfo);
      sendBase("createConsumeDeliverOrderByRule", args);
    }

    public OrderResponse recv_createConsumeDeliverOrderByRule() throws org.apache.thrift.TException
    {
      createConsumeDeliverOrderByRule_result result = new createConsumeDeliverOrderByRule_result();
      receiveBase(result, "createConsumeDeliverOrderByRule");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "createConsumeDeliverOrderByRule failed: unknown result");
    }

    public OrderResponse createConsumeDeliverOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, GoodsList goodsList) throws org.apache.thrift.TException
    {
      send_createConsumeDeliverOrder(orderRequest, consumeRequests, goodsList);
      return recv_createConsumeDeliverOrder();
    }

    public void send_createConsumeDeliverOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, GoodsList goodsList) throws org.apache.thrift.TException
    {
      createConsumeDeliverOrder_args args = new createConsumeDeliverOrder_args();
      args.setOrderRequest(orderRequest);
      args.setConsumeRequests(consumeRequests);
      args.setGoodsList(goodsList);
      sendBase("createConsumeDeliverOrder", args);
    }

    public OrderResponse recv_createConsumeDeliverOrder() throws org.apache.thrift.TException
    {
      createConsumeDeliverOrder_result result = new createConsumeDeliverOrder_result();
      receiveBase(result, "createConsumeDeliverOrder");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "createConsumeDeliverOrder failed: unknown result");
    }

    public OrderResponse createDeliverOrderByRule(OrderRequest orderRequest, RuleInfo ruleInfo) throws org.apache.thrift.TException
    {
      send_createDeliverOrderByRule(orderRequest, ruleInfo);
      return recv_createDeliverOrderByRule();
    }

    public void send_createDeliverOrderByRule(OrderRequest orderRequest, RuleInfo ruleInfo) throws org.apache.thrift.TException
    {
      createDeliverOrderByRule_args args = new createDeliverOrderByRule_args();
      args.setOrderRequest(orderRequest);
      args.setRuleInfo(ruleInfo);
      sendBase("createDeliverOrderByRule", args);
    }

    public OrderResponse recv_createDeliverOrderByRule() throws org.apache.thrift.TException
    {
      createDeliverOrderByRule_result result = new createDeliverOrderByRule_result();
      receiveBase(result, "createDeliverOrderByRule");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "createDeliverOrderByRule failed: unknown result");
    }

    public OrderResponse createDeliverOrder(OrderRequest orderRequest, GoodsList goodsList) throws org.apache.thrift.TException
    {
      send_createDeliverOrder(orderRequest, goodsList);
      return recv_createDeliverOrder();
    }

    public void send_createDeliverOrder(OrderRequest orderRequest, GoodsList goodsList) throws org.apache.thrift.TException
    {
      createDeliverOrder_args args = new createDeliverOrder_args();
      args.setOrderRequest(orderRequest);
      args.setGoodsList(goodsList);
      sendBase("createDeliverOrder", args);
    }

    public OrderResponse recv_createDeliverOrder() throws org.apache.thrift.TException
    {
      createDeliverOrder_result result = new createDeliverOrder_result();
      receiveBase(result, "createDeliverOrder");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "createDeliverOrder failed: unknown result");
    }

  }
  public static class AsyncClient extends org.apache.thrift.async.TAsyncClient implements AsyncIface {
    public static class Factory implements org.apache.thrift.async.TAsyncClientFactory<AsyncClient> {
      private org.apache.thrift.async.TAsyncClientManager clientManager;
      private org.apache.thrift.protocol.TProtocolFactory protocolFactory;
      public Factory(org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.protocol.TProtocolFactory protocolFactory) {
        this.clientManager = clientManager;
        this.protocolFactory = protocolFactory;
      }
      public AsyncClient getAsyncClient(org.apache.thrift.transport.TNonblockingTransport transport) {
        return new AsyncClient(protocolFactory, clientManager, transport);
      }
    }

    public AsyncClient(org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.transport.TNonblockingTransport transport) {
      super(protocolFactory, clientManager, transport);
    }

    public void createConsumeOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      createConsumeOrder_call method_call = new createConsumeOrder_call(orderRequest, consumeRequests, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class createConsumeOrder_call extends org.apache.thrift.async.TAsyncMethodCall {
      private OrderRequest orderRequest;
      private List<ConsumeRequest> consumeRequests;
      public createConsumeOrder_call(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.orderRequest = orderRequest;
        this.consumeRequests = consumeRequests;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("createConsumeOrder", org.apache.thrift.protocol.TMessageType.CALL, 0));
        createConsumeOrder_args args = new createConsumeOrder_args();
        args.setOrderRequest(orderRequest);
        args.setConsumeRequests(consumeRequests);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public OrderResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_createConsumeOrder();
      }
    }

    public void createConsumeDeliverOrderByRule(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, RuleInfo ruleInfo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      createConsumeDeliverOrderByRule_call method_call = new createConsumeDeliverOrderByRule_call(orderRequest, consumeRequests, ruleInfo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class createConsumeDeliverOrderByRule_call extends org.apache.thrift.async.TAsyncMethodCall {
      private OrderRequest orderRequest;
      private List<ConsumeRequest> consumeRequests;
      private RuleInfo ruleInfo;
      public createConsumeDeliverOrderByRule_call(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, RuleInfo ruleInfo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.orderRequest = orderRequest;
        this.consumeRequests = consumeRequests;
        this.ruleInfo = ruleInfo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("createConsumeDeliverOrderByRule", org.apache.thrift.protocol.TMessageType.CALL, 0));
        createConsumeDeliverOrderByRule_args args = new createConsumeDeliverOrderByRule_args();
        args.setOrderRequest(orderRequest);
        args.setConsumeRequests(consumeRequests);
        args.setRuleInfo(ruleInfo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public OrderResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_createConsumeDeliverOrderByRule();
      }
    }

    public void createConsumeDeliverOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, GoodsList goodsList, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      createConsumeDeliverOrder_call method_call = new createConsumeDeliverOrder_call(orderRequest, consumeRequests, goodsList, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class createConsumeDeliverOrder_call extends org.apache.thrift.async.TAsyncMethodCall {
      private OrderRequest orderRequest;
      private List<ConsumeRequest> consumeRequests;
      private GoodsList goodsList;
      public createConsumeDeliverOrder_call(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, GoodsList goodsList, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.orderRequest = orderRequest;
        this.consumeRequests = consumeRequests;
        this.goodsList = goodsList;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("createConsumeDeliverOrder", org.apache.thrift.protocol.TMessageType.CALL, 0));
        createConsumeDeliverOrder_args args = new createConsumeDeliverOrder_args();
        args.setOrderRequest(orderRequest);
        args.setConsumeRequests(consumeRequests);
        args.setGoodsList(goodsList);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public OrderResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_createConsumeDeliverOrder();
      }
    }

    public void createDeliverOrderByRule(OrderRequest orderRequest, RuleInfo ruleInfo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      createDeliverOrderByRule_call method_call = new createDeliverOrderByRule_call(orderRequest, ruleInfo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class createDeliverOrderByRule_call extends org.apache.thrift.async.TAsyncMethodCall {
      private OrderRequest orderRequest;
      private RuleInfo ruleInfo;
      public createDeliverOrderByRule_call(OrderRequest orderRequest, RuleInfo ruleInfo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.orderRequest = orderRequest;
        this.ruleInfo = ruleInfo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("createDeliverOrderByRule", org.apache.thrift.protocol.TMessageType.CALL, 0));
        createDeliverOrderByRule_args args = new createDeliverOrderByRule_args();
        args.setOrderRequest(orderRequest);
        args.setRuleInfo(ruleInfo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public OrderResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_createDeliverOrderByRule();
      }
    }

    public void createDeliverOrder(OrderRequest orderRequest, GoodsList goodsList, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      createDeliverOrder_call method_call = new createDeliverOrder_call(orderRequest, goodsList, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class createDeliverOrder_call extends org.apache.thrift.async.TAsyncMethodCall {
      private OrderRequest orderRequest;
      private GoodsList goodsList;
      public createDeliverOrder_call(OrderRequest orderRequest, GoodsList goodsList, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.orderRequest = orderRequest;
        this.goodsList = goodsList;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("createDeliverOrder", org.apache.thrift.protocol.TMessageType.CALL, 0));
        createDeliverOrder_args args = new createDeliverOrder_args();
        args.setOrderRequest(orderRequest);
        args.setGoodsList(goodsList);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public OrderResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_createDeliverOrder();
      }
    }

  }

  public static class Processor<I extends Iface> extends org.apache.thrift.TBaseProcessor<I> implements org.apache.thrift.TProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());
    public Processor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>>()));
    }

    protected Processor(I iface, Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends Iface> Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> getProcessMap(Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      processMap.put("createConsumeOrder", new createConsumeOrder());
      processMap.put("createConsumeDeliverOrderByRule", new createConsumeDeliverOrderByRule());
      processMap.put("createConsumeDeliverOrder", new createConsumeDeliverOrder());
      processMap.put("createDeliverOrderByRule", new createDeliverOrderByRule());
      processMap.put("createDeliverOrder", new createDeliverOrder());
      return processMap;
    }

    public static class createConsumeOrder<I extends Iface> extends org.apache.thrift.ProcessFunction<I, createConsumeOrder_args> {
      public createConsumeOrder() {
        super("createConsumeOrder");
      }

      public createConsumeOrder_args getEmptyArgsInstance() {
        return new createConsumeOrder_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public createConsumeOrder_result getResult(I iface, createConsumeOrder_args args) throws org.apache.thrift.TException {
        createConsumeOrder_result result = new createConsumeOrder_result();
        result.success = iface.createConsumeOrder(args.orderRequest, args.consumeRequests);
        return result;
      }
    }

    public static class createConsumeDeliverOrderByRule<I extends Iface> extends org.apache.thrift.ProcessFunction<I, createConsumeDeliverOrderByRule_args> {
      public createConsumeDeliverOrderByRule() {
        super("createConsumeDeliverOrderByRule");
      }

      public createConsumeDeliverOrderByRule_args getEmptyArgsInstance() {
        return new createConsumeDeliverOrderByRule_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public createConsumeDeliverOrderByRule_result getResult(I iface, createConsumeDeliverOrderByRule_args args) throws org.apache.thrift.TException {
        createConsumeDeliverOrderByRule_result result = new createConsumeDeliverOrderByRule_result();
        result.success = iface.createConsumeDeliverOrderByRule(args.orderRequest, args.consumeRequests, args.ruleInfo);
        return result;
      }
    }

    public static class createConsumeDeliverOrder<I extends Iface> extends org.apache.thrift.ProcessFunction<I, createConsumeDeliverOrder_args> {
      public createConsumeDeliverOrder() {
        super("createConsumeDeliverOrder");
      }

      public createConsumeDeliverOrder_args getEmptyArgsInstance() {
        return new createConsumeDeliverOrder_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public createConsumeDeliverOrder_result getResult(I iface, createConsumeDeliverOrder_args args) throws org.apache.thrift.TException {
        createConsumeDeliverOrder_result result = new createConsumeDeliverOrder_result();
        result.success = iface.createConsumeDeliverOrder(args.orderRequest, args.consumeRequests, args.goodsList);
        return result;
      }
    }

    public static class createDeliverOrderByRule<I extends Iface> extends org.apache.thrift.ProcessFunction<I, createDeliverOrderByRule_args> {
      public createDeliverOrderByRule() {
        super("createDeliverOrderByRule");
      }

      public createDeliverOrderByRule_args getEmptyArgsInstance() {
        return new createDeliverOrderByRule_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public createDeliverOrderByRule_result getResult(I iface, createDeliverOrderByRule_args args) throws org.apache.thrift.TException {
        createDeliverOrderByRule_result result = new createDeliverOrderByRule_result();
        result.success = iface.createDeliverOrderByRule(args.orderRequest, args.ruleInfo);
        return result;
      }
    }

    public static class createDeliverOrder<I extends Iface> extends org.apache.thrift.ProcessFunction<I, createDeliverOrder_args> {
      public createDeliverOrder() {
        super("createDeliverOrder");
      }

      public createDeliverOrder_args getEmptyArgsInstance() {
        return new createDeliverOrder_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public createDeliverOrder_result getResult(I iface, createDeliverOrder_args args) throws org.apache.thrift.TException {
        createDeliverOrder_result result = new createDeliverOrder_result();
        result.success = iface.createDeliverOrder(args.orderRequest, args.goodsList);
        return result;
      }
    }

  }

  public static class AsyncProcessor<I extends AsyncIface> extends org.apache.thrift.TBaseAsyncProcessor<I> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());
    public AsyncProcessor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.AsyncProcessFunction<I, ? extends org.apache.thrift.TBase, ?>>()));
    }

    protected AsyncProcessor(I iface, Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends AsyncIface> Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase,?>> getProcessMap(Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      processMap.put("createConsumeOrder", new createConsumeOrder());
      processMap.put("createConsumeDeliverOrderByRule", new createConsumeDeliverOrderByRule());
      processMap.put("createConsumeDeliverOrder", new createConsumeDeliverOrder());
      processMap.put("createDeliverOrderByRule", new createDeliverOrderByRule());
      processMap.put("createDeliverOrder", new createDeliverOrder());
      return processMap;
    }

    public static class createConsumeOrder<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, createConsumeOrder_args, OrderResponse> {
      public createConsumeOrder() {
        super("createConsumeOrder");
      }

      public createConsumeOrder_args getEmptyArgsInstance() {
        return new createConsumeOrder_args();
      }

      public AsyncMethodCallback<OrderResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<OrderResponse>() { 
          public void onComplete(OrderResponse o) {
            createConsumeOrder_result result = new createConsumeOrder_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            createConsumeOrder_result result = new createConsumeOrder_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, createConsumeOrder_args args, org.apache.thrift.async.AsyncMethodCallback<OrderResponse> resultHandler) throws TException {
        iface.createConsumeOrder(args.orderRequest, args.consumeRequests,resultHandler);
      }
    }

    public static class createConsumeDeliverOrderByRule<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, createConsumeDeliverOrderByRule_args, OrderResponse> {
      public createConsumeDeliverOrderByRule() {
        super("createConsumeDeliverOrderByRule");
      }

      public createConsumeDeliverOrderByRule_args getEmptyArgsInstance() {
        return new createConsumeDeliverOrderByRule_args();
      }

      public AsyncMethodCallback<OrderResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<OrderResponse>() { 
          public void onComplete(OrderResponse o) {
            createConsumeDeliverOrderByRule_result result = new createConsumeDeliverOrderByRule_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            createConsumeDeliverOrderByRule_result result = new createConsumeDeliverOrderByRule_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, createConsumeDeliverOrderByRule_args args, org.apache.thrift.async.AsyncMethodCallback<OrderResponse> resultHandler) throws TException {
        iface.createConsumeDeliverOrderByRule(args.orderRequest, args.consumeRequests, args.ruleInfo,resultHandler);
      }
    }

    public static class createConsumeDeliverOrder<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, createConsumeDeliverOrder_args, OrderResponse> {
      public createConsumeDeliverOrder() {
        super("createConsumeDeliverOrder");
      }

      public createConsumeDeliverOrder_args getEmptyArgsInstance() {
        return new createConsumeDeliverOrder_args();
      }

      public AsyncMethodCallback<OrderResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<OrderResponse>() { 
          public void onComplete(OrderResponse o) {
            createConsumeDeliverOrder_result result = new createConsumeDeliverOrder_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            createConsumeDeliverOrder_result result = new createConsumeDeliverOrder_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, createConsumeDeliverOrder_args args, org.apache.thrift.async.AsyncMethodCallback<OrderResponse> resultHandler) throws TException {
        iface.createConsumeDeliverOrder(args.orderRequest, args.consumeRequests, args.goodsList,resultHandler);
      }
    }

    public static class createDeliverOrderByRule<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, createDeliverOrderByRule_args, OrderResponse> {
      public createDeliverOrderByRule() {
        super("createDeliverOrderByRule");
      }

      public createDeliverOrderByRule_args getEmptyArgsInstance() {
        return new createDeliverOrderByRule_args();
      }

      public AsyncMethodCallback<OrderResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<OrderResponse>() { 
          public void onComplete(OrderResponse o) {
            createDeliverOrderByRule_result result = new createDeliverOrderByRule_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            createDeliverOrderByRule_result result = new createDeliverOrderByRule_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, createDeliverOrderByRule_args args, org.apache.thrift.async.AsyncMethodCallback<OrderResponse> resultHandler) throws TException {
        iface.createDeliverOrderByRule(args.orderRequest, args.ruleInfo,resultHandler);
      }
    }

    public static class createDeliverOrder<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, createDeliverOrder_args, OrderResponse> {
      public createDeliverOrder() {
        super("createDeliverOrder");
      }

      public createDeliverOrder_args getEmptyArgsInstance() {
        return new createDeliverOrder_args();
      }

      public AsyncMethodCallback<OrderResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<OrderResponse>() { 
          public void onComplete(OrderResponse o) {
            createDeliverOrder_result result = new createDeliverOrder_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            createDeliverOrder_result result = new createDeliverOrder_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, createDeliverOrder_args args, org.apache.thrift.async.AsyncMethodCallback<OrderResponse> resultHandler) throws TException {
        iface.createDeliverOrder(args.orderRequest, args.goodsList,resultHandler);
      }
    }

  }

  public static class createConsumeOrder_args implements org.apache.thrift.TBase<createConsumeOrder_args, createConsumeOrder_args._Fields>, java.io.Serializable, Cloneable, Comparable<createConsumeOrder_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("createConsumeOrder_args");

    private static final org.apache.thrift.protocol.TField ORDER_REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("orderRequest", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField CONSUME_REQUESTS_FIELD_DESC = new org.apache.thrift.protocol.TField("consumeRequests", org.apache.thrift.protocol.TType.LIST, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new createConsumeOrder_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new createConsumeOrder_argsTupleSchemeFactory());
    }

    public OrderRequest orderRequest; // required
    public List<ConsumeRequest> consumeRequests; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      ORDER_REQUEST((short)1, "orderRequest"),
      CONSUME_REQUESTS((short)2, "consumeRequests");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // ORDER_REQUEST
            return ORDER_REQUEST;
          case 2: // CONSUME_REQUESTS
            return CONSUME_REQUESTS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.ORDER_REQUEST, new org.apache.thrift.meta_data.FieldMetaData("orderRequest", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderRequest.class)));
      tmpMap.put(_Fields.CONSUME_REQUESTS, new org.apache.thrift.meta_data.FieldMetaData("consumeRequests", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
              new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeRequest.class))));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(createConsumeOrder_args.class, metaDataMap);
    }

    public createConsumeOrder_args() {
    }

    public createConsumeOrder_args(
      OrderRequest orderRequest,
      List<ConsumeRequest> consumeRequests)
    {
      this();
      this.orderRequest = orderRequest;
      this.consumeRequests = consumeRequests;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public createConsumeOrder_args(createConsumeOrder_args other) {
      if (other.isSetOrderRequest()) {
        this.orderRequest = new OrderRequest(other.orderRequest);
      }
      if (other.isSetConsumeRequests()) {
        List<ConsumeRequest> __this__consumeRequests = new ArrayList<ConsumeRequest>(other.consumeRequests.size());
        for (ConsumeRequest other_element : other.consumeRequests) {
          __this__consumeRequests.add(new ConsumeRequest(other_element));
        }
        this.consumeRequests = __this__consumeRequests;
      }
    }

    public createConsumeOrder_args deepCopy() {
      return new createConsumeOrder_args(this);
    }

    @Override
    public void clear() {
      this.orderRequest = null;
      this.consumeRequests = null;
    }

    public OrderRequest getOrderRequest() {
      return this.orderRequest;
    }

    public createConsumeOrder_args setOrderRequest(OrderRequest orderRequest) {
      this.orderRequest = orderRequest;
      return this;
    }

    public void unsetOrderRequest() {
      this.orderRequest = null;
    }

    /** Returns true if field orderRequest is set (has been assigned a value) and false otherwise */
    public boolean isSetOrderRequest() {
      return this.orderRequest != null;
    }

    public void setOrderRequestIsSet(boolean value) {
      if (!value) {
        this.orderRequest = null;
      }
    }

    public int getConsumeRequestsSize() {
      return (this.consumeRequests == null) ? 0 : this.consumeRequests.size();
    }

    public java.util.Iterator<ConsumeRequest> getConsumeRequestsIterator() {
      return (this.consumeRequests == null) ? null : this.consumeRequests.iterator();
    }

    public void addToConsumeRequests(ConsumeRequest elem) {
      if (this.consumeRequests == null) {
        this.consumeRequests = new ArrayList<ConsumeRequest>();
      }
      this.consumeRequests.add(elem);
    }

    public List<ConsumeRequest> getConsumeRequests() {
      return this.consumeRequests;
    }

    public createConsumeOrder_args setConsumeRequests(List<ConsumeRequest> consumeRequests) {
      this.consumeRequests = consumeRequests;
      return this;
    }

    public void unsetConsumeRequests() {
      this.consumeRequests = null;
    }

    /** Returns true if field consumeRequests is set (has been assigned a value) and false otherwise */
    public boolean isSetConsumeRequests() {
      return this.consumeRequests != null;
    }

    public void setConsumeRequestsIsSet(boolean value) {
      if (!value) {
        this.consumeRequests = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case ORDER_REQUEST:
        if (value == null) {
          unsetOrderRequest();
        } else {
          setOrderRequest((OrderRequest)value);
        }
        break;

      case CONSUME_REQUESTS:
        if (value == null) {
          unsetConsumeRequests();
        } else {
          setConsumeRequests((List<ConsumeRequest>)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case ORDER_REQUEST:
        return getOrderRequest();

      case CONSUME_REQUESTS:
        return getConsumeRequests();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case ORDER_REQUEST:
        return isSetOrderRequest();
      case CONSUME_REQUESTS:
        return isSetConsumeRequests();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof createConsumeOrder_args)
        return this.equals((createConsumeOrder_args)that);
      return false;
    }

    public boolean equals(createConsumeOrder_args that) {
      if (that == null)
        return false;

      boolean this_present_orderRequest = true && this.isSetOrderRequest();
      boolean that_present_orderRequest = true && that.isSetOrderRequest();
      if (this_present_orderRequest || that_present_orderRequest) {
        if (!(this_present_orderRequest && that_present_orderRequest))
          return false;
        if (!this.orderRequest.equals(that.orderRequest))
          return false;
      }

      boolean this_present_consumeRequests = true && this.isSetConsumeRequests();
      boolean that_present_consumeRequests = true && that.isSetConsumeRequests();
      if (this_present_consumeRequests || that_present_consumeRequests) {
        if (!(this_present_consumeRequests && that_present_consumeRequests))
          return false;
        if (!this.consumeRequests.equals(that.consumeRequests))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_orderRequest = true && (isSetOrderRequest());
      list.add(present_orderRequest);
      if (present_orderRequest)
        list.add(orderRequest);

      boolean present_consumeRequests = true && (isSetConsumeRequests());
      list.add(present_consumeRequests);
      if (present_consumeRequests)
        list.add(consumeRequests);

      return list.hashCode();
    }

    @Override
    public int compareTo(createConsumeOrder_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetOrderRequest()).compareTo(other.isSetOrderRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetOrderRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderRequest, other.orderRequest);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetConsumeRequests()).compareTo(other.isSetConsumeRequests());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetConsumeRequests()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.consumeRequests, other.consumeRequests);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("createConsumeOrder_args(");
      boolean first = true;

      sb.append("orderRequest:");
      if (this.orderRequest == null) {
        sb.append("null");
      } else {
        sb.append(this.orderRequest);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("consumeRequests:");
      if (this.consumeRequests == null) {
        sb.append("null");
      } else {
        sb.append(this.consumeRequests);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (orderRequest == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderRequest' was not present! Struct: " + toString());
      }
      if (consumeRequests == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'consumeRequests' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (orderRequest != null) {
        orderRequest.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class createConsumeOrder_argsStandardSchemeFactory implements SchemeFactory {
      public createConsumeOrder_argsStandardScheme getScheme() {
        return new createConsumeOrder_argsStandardScheme();
      }
    }

    private static class createConsumeOrder_argsStandardScheme extends StandardScheme<createConsumeOrder_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, createConsumeOrder_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // ORDER_REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.orderRequest = new OrderRequest();
                struct.orderRequest.read(iprot);
                struct.setOrderRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // CONSUME_REQUESTS
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list40 = iprot.readListBegin();
                  struct.consumeRequests = new ArrayList<ConsumeRequest>(_list40.size);
                  ConsumeRequest _elem41;
                  for (int _i42 = 0; _i42 < _list40.size; ++_i42)
                  {
                    _elem41 = new ConsumeRequest();
                    _elem41.read(iprot);
                    struct.consumeRequests.add(_elem41);
                  }
                  iprot.readListEnd();
                }
                struct.setConsumeRequestsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, createConsumeOrder_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.orderRequest != null) {
          oprot.writeFieldBegin(ORDER_REQUEST_FIELD_DESC);
          struct.orderRequest.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.consumeRequests != null) {
          oprot.writeFieldBegin(CONSUME_REQUESTS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.consumeRequests.size()));
            for (ConsumeRequest _iter43 : struct.consumeRequests)
            {
              _iter43.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class createConsumeOrder_argsTupleSchemeFactory implements SchemeFactory {
      public createConsumeOrder_argsTupleScheme getScheme() {
        return new createConsumeOrder_argsTupleScheme();
      }
    }

    private static class createConsumeOrder_argsTupleScheme extends TupleScheme<createConsumeOrder_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, createConsumeOrder_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.orderRequest.write(oprot);
        {
          oprot.writeI32(struct.consumeRequests.size());
          for (ConsumeRequest _iter44 : struct.consumeRequests)
          {
            _iter44.write(oprot);
          }
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, createConsumeOrder_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.orderRequest = new OrderRequest();
        struct.orderRequest.read(iprot);
        struct.setOrderRequestIsSet(true);
        {
          org.apache.thrift.protocol.TList _list45 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.consumeRequests = new ArrayList<ConsumeRequest>(_list45.size);
          ConsumeRequest _elem46;
          for (int _i47 = 0; _i47 < _list45.size; ++_i47)
          {
            _elem46 = new ConsumeRequest();
            _elem46.read(iprot);
            struct.consumeRequests.add(_elem46);
          }
        }
        struct.setConsumeRequestsIsSet(true);
      }
    }

  }

  public static class createConsumeOrder_result implements org.apache.thrift.TBase<createConsumeOrder_result, createConsumeOrder_result._Fields>, java.io.Serializable, Cloneable, Comparable<createConsumeOrder_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("createConsumeOrder_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new createConsumeOrder_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new createConsumeOrder_resultTupleSchemeFactory());
    }

    public OrderResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(createConsumeOrder_result.class, metaDataMap);
    }

    public createConsumeOrder_result() {
    }

    public createConsumeOrder_result(
      OrderResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public createConsumeOrder_result(createConsumeOrder_result other) {
      if (other.isSetSuccess()) {
        this.success = new OrderResponse(other.success);
      }
    }

    public createConsumeOrder_result deepCopy() {
      return new createConsumeOrder_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public OrderResponse getSuccess() {
      return this.success;
    }

    public createConsumeOrder_result setSuccess(OrderResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((OrderResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof createConsumeOrder_result)
        return this.equals((createConsumeOrder_result)that);
      return false;
    }

    public boolean equals(createConsumeOrder_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(createConsumeOrder_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("createConsumeOrder_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class createConsumeOrder_resultStandardSchemeFactory implements SchemeFactory {
      public createConsumeOrder_resultStandardScheme getScheme() {
        return new createConsumeOrder_resultStandardScheme();
      }
    }

    private static class createConsumeOrder_resultStandardScheme extends StandardScheme<createConsumeOrder_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, createConsumeOrder_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new OrderResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, createConsumeOrder_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class createConsumeOrder_resultTupleSchemeFactory implements SchemeFactory {
      public createConsumeOrder_resultTupleScheme getScheme() {
        return new createConsumeOrder_resultTupleScheme();
      }
    }

    private static class createConsumeOrder_resultTupleScheme extends TupleScheme<createConsumeOrder_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, createConsumeOrder_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, createConsumeOrder_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new OrderResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class createConsumeDeliverOrderByRule_args implements org.apache.thrift.TBase<createConsumeDeliverOrderByRule_args, createConsumeDeliverOrderByRule_args._Fields>, java.io.Serializable, Cloneable, Comparable<createConsumeDeliverOrderByRule_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("createConsumeDeliverOrderByRule_args");

    private static final org.apache.thrift.protocol.TField ORDER_REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("orderRequest", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField CONSUME_REQUESTS_FIELD_DESC = new org.apache.thrift.protocol.TField("consumeRequests", org.apache.thrift.protocol.TType.LIST, (short)2);
    private static final org.apache.thrift.protocol.TField RULE_INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("ruleInfo", org.apache.thrift.protocol.TType.STRUCT, (short)3);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new createConsumeDeliverOrderByRule_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new createConsumeDeliverOrderByRule_argsTupleSchemeFactory());
    }

    public OrderRequest orderRequest; // required
    public List<ConsumeRequest> consumeRequests; // required
    public RuleInfo ruleInfo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      ORDER_REQUEST((short)1, "orderRequest"),
      CONSUME_REQUESTS((short)2, "consumeRequests"),
      RULE_INFO((short)3, "ruleInfo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // ORDER_REQUEST
            return ORDER_REQUEST;
          case 2: // CONSUME_REQUESTS
            return CONSUME_REQUESTS;
          case 3: // RULE_INFO
            return RULE_INFO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.ORDER_REQUEST, new org.apache.thrift.meta_data.FieldMetaData("orderRequest", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderRequest.class)));
      tmpMap.put(_Fields.CONSUME_REQUESTS, new org.apache.thrift.meta_data.FieldMetaData("consumeRequests", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
              new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeRequest.class))));
      tmpMap.put(_Fields.RULE_INFO, new org.apache.thrift.meta_data.FieldMetaData("ruleInfo", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, RuleInfo.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(createConsumeDeliverOrderByRule_args.class, metaDataMap);
    }

    public createConsumeDeliverOrderByRule_args() {
    }

    public createConsumeDeliverOrderByRule_args(
      OrderRequest orderRequest,
      List<ConsumeRequest> consumeRequests,
      RuleInfo ruleInfo)
    {
      this();
      this.orderRequest = orderRequest;
      this.consumeRequests = consumeRequests;
      this.ruleInfo = ruleInfo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public createConsumeDeliverOrderByRule_args(createConsumeDeliverOrderByRule_args other) {
      if (other.isSetOrderRequest()) {
        this.orderRequest = new OrderRequest(other.orderRequest);
      }
      if (other.isSetConsumeRequests()) {
        List<ConsumeRequest> __this__consumeRequests = new ArrayList<ConsumeRequest>(other.consumeRequests.size());
        for (ConsumeRequest other_element : other.consumeRequests) {
          __this__consumeRequests.add(new ConsumeRequest(other_element));
        }
        this.consumeRequests = __this__consumeRequests;
      }
      if (other.isSetRuleInfo()) {
        this.ruleInfo = new RuleInfo(other.ruleInfo);
      }
    }

    public createConsumeDeliverOrderByRule_args deepCopy() {
      return new createConsumeDeliverOrderByRule_args(this);
    }

    @Override
    public void clear() {
      this.orderRequest = null;
      this.consumeRequests = null;
      this.ruleInfo = null;
    }

    public OrderRequest getOrderRequest() {
      return this.orderRequest;
    }

    public createConsumeDeliverOrderByRule_args setOrderRequest(OrderRequest orderRequest) {
      this.orderRequest = orderRequest;
      return this;
    }

    public void unsetOrderRequest() {
      this.orderRequest = null;
    }

    /** Returns true if field orderRequest is set (has been assigned a value) and false otherwise */
    public boolean isSetOrderRequest() {
      return this.orderRequest != null;
    }

    public void setOrderRequestIsSet(boolean value) {
      if (!value) {
        this.orderRequest = null;
      }
    }

    public int getConsumeRequestsSize() {
      return (this.consumeRequests == null) ? 0 : this.consumeRequests.size();
    }

    public java.util.Iterator<ConsumeRequest> getConsumeRequestsIterator() {
      return (this.consumeRequests == null) ? null : this.consumeRequests.iterator();
    }

    public void addToConsumeRequests(ConsumeRequest elem) {
      if (this.consumeRequests == null) {
        this.consumeRequests = new ArrayList<ConsumeRequest>();
      }
      this.consumeRequests.add(elem);
    }

    public List<ConsumeRequest> getConsumeRequests() {
      return this.consumeRequests;
    }

    public createConsumeDeliverOrderByRule_args setConsumeRequests(List<ConsumeRequest> consumeRequests) {
      this.consumeRequests = consumeRequests;
      return this;
    }

    public void unsetConsumeRequests() {
      this.consumeRequests = null;
    }

    /** Returns true if field consumeRequests is set (has been assigned a value) and false otherwise */
    public boolean isSetConsumeRequests() {
      return this.consumeRequests != null;
    }

    public void setConsumeRequestsIsSet(boolean value) {
      if (!value) {
        this.consumeRequests = null;
      }
    }

    public RuleInfo getRuleInfo() {
      return this.ruleInfo;
    }

    public createConsumeDeliverOrderByRule_args setRuleInfo(RuleInfo ruleInfo) {
      this.ruleInfo = ruleInfo;
      return this;
    }

    public void unsetRuleInfo() {
      this.ruleInfo = null;
    }

    /** Returns true if field ruleInfo is set (has been assigned a value) and false otherwise */
    public boolean isSetRuleInfo() {
      return this.ruleInfo != null;
    }

    public void setRuleInfoIsSet(boolean value) {
      if (!value) {
        this.ruleInfo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case ORDER_REQUEST:
        if (value == null) {
          unsetOrderRequest();
        } else {
          setOrderRequest((OrderRequest)value);
        }
        break;

      case CONSUME_REQUESTS:
        if (value == null) {
          unsetConsumeRequests();
        } else {
          setConsumeRequests((List<ConsumeRequest>)value);
        }
        break;

      case RULE_INFO:
        if (value == null) {
          unsetRuleInfo();
        } else {
          setRuleInfo((RuleInfo)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case ORDER_REQUEST:
        return getOrderRequest();

      case CONSUME_REQUESTS:
        return getConsumeRequests();

      case RULE_INFO:
        return getRuleInfo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case ORDER_REQUEST:
        return isSetOrderRequest();
      case CONSUME_REQUESTS:
        return isSetConsumeRequests();
      case RULE_INFO:
        return isSetRuleInfo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof createConsumeDeliverOrderByRule_args)
        return this.equals((createConsumeDeliverOrderByRule_args)that);
      return false;
    }

    public boolean equals(createConsumeDeliverOrderByRule_args that) {
      if (that == null)
        return false;

      boolean this_present_orderRequest = true && this.isSetOrderRequest();
      boolean that_present_orderRequest = true && that.isSetOrderRequest();
      if (this_present_orderRequest || that_present_orderRequest) {
        if (!(this_present_orderRequest && that_present_orderRequest))
          return false;
        if (!this.orderRequest.equals(that.orderRequest))
          return false;
      }

      boolean this_present_consumeRequests = true && this.isSetConsumeRequests();
      boolean that_present_consumeRequests = true && that.isSetConsumeRequests();
      if (this_present_consumeRequests || that_present_consumeRequests) {
        if (!(this_present_consumeRequests && that_present_consumeRequests))
          return false;
        if (!this.consumeRequests.equals(that.consumeRequests))
          return false;
      }

      boolean this_present_ruleInfo = true && this.isSetRuleInfo();
      boolean that_present_ruleInfo = true && that.isSetRuleInfo();
      if (this_present_ruleInfo || that_present_ruleInfo) {
        if (!(this_present_ruleInfo && that_present_ruleInfo))
          return false;
        if (!this.ruleInfo.equals(that.ruleInfo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_orderRequest = true && (isSetOrderRequest());
      list.add(present_orderRequest);
      if (present_orderRequest)
        list.add(orderRequest);

      boolean present_consumeRequests = true && (isSetConsumeRequests());
      list.add(present_consumeRequests);
      if (present_consumeRequests)
        list.add(consumeRequests);

      boolean present_ruleInfo = true && (isSetRuleInfo());
      list.add(present_ruleInfo);
      if (present_ruleInfo)
        list.add(ruleInfo);

      return list.hashCode();
    }

    @Override
    public int compareTo(createConsumeDeliverOrderByRule_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetOrderRequest()).compareTo(other.isSetOrderRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetOrderRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderRequest, other.orderRequest);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetConsumeRequests()).compareTo(other.isSetConsumeRequests());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetConsumeRequests()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.consumeRequests, other.consumeRequests);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetRuleInfo()).compareTo(other.isSetRuleInfo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRuleInfo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ruleInfo, other.ruleInfo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("createConsumeDeliverOrderByRule_args(");
      boolean first = true;

      sb.append("orderRequest:");
      if (this.orderRequest == null) {
        sb.append("null");
      } else {
        sb.append(this.orderRequest);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("consumeRequests:");
      if (this.consumeRequests == null) {
        sb.append("null");
      } else {
        sb.append(this.consumeRequests);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("ruleInfo:");
      if (this.ruleInfo == null) {
        sb.append("null");
      } else {
        sb.append(this.ruleInfo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (orderRequest == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderRequest' was not present! Struct: " + toString());
      }
      if (consumeRequests == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'consumeRequests' was not present! Struct: " + toString());
      }
      if (ruleInfo == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'ruleInfo' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (orderRequest != null) {
        orderRequest.validate();
      }
      if (ruleInfo != null) {
        ruleInfo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class createConsumeDeliverOrderByRule_argsStandardSchemeFactory implements SchemeFactory {
      public createConsumeDeliverOrderByRule_argsStandardScheme getScheme() {
        return new createConsumeDeliverOrderByRule_argsStandardScheme();
      }
    }

    private static class createConsumeDeliverOrderByRule_argsStandardScheme extends StandardScheme<createConsumeDeliverOrderByRule_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, createConsumeDeliverOrderByRule_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // ORDER_REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.orderRequest = new OrderRequest();
                struct.orderRequest.read(iprot);
                struct.setOrderRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // CONSUME_REQUESTS
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list48 = iprot.readListBegin();
                  struct.consumeRequests = new ArrayList<ConsumeRequest>(_list48.size);
                  ConsumeRequest _elem49;
                  for (int _i50 = 0; _i50 < _list48.size; ++_i50)
                  {
                    _elem49 = new ConsumeRequest();
                    _elem49.read(iprot);
                    struct.consumeRequests.add(_elem49);
                  }
                  iprot.readListEnd();
                }
                struct.setConsumeRequestsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // RULE_INFO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.ruleInfo = new RuleInfo();
                struct.ruleInfo.read(iprot);
                struct.setRuleInfoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, createConsumeDeliverOrderByRule_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.orderRequest != null) {
          oprot.writeFieldBegin(ORDER_REQUEST_FIELD_DESC);
          struct.orderRequest.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.consumeRequests != null) {
          oprot.writeFieldBegin(CONSUME_REQUESTS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.consumeRequests.size()));
            for (ConsumeRequest _iter51 : struct.consumeRequests)
            {
              _iter51.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        if (struct.ruleInfo != null) {
          oprot.writeFieldBegin(RULE_INFO_FIELD_DESC);
          struct.ruleInfo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class createConsumeDeliverOrderByRule_argsTupleSchemeFactory implements SchemeFactory {
      public createConsumeDeliverOrderByRule_argsTupleScheme getScheme() {
        return new createConsumeDeliverOrderByRule_argsTupleScheme();
      }
    }

    private static class createConsumeDeliverOrderByRule_argsTupleScheme extends TupleScheme<createConsumeDeliverOrderByRule_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, createConsumeDeliverOrderByRule_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.orderRequest.write(oprot);
        {
          oprot.writeI32(struct.consumeRequests.size());
          for (ConsumeRequest _iter52 : struct.consumeRequests)
          {
            _iter52.write(oprot);
          }
        }
        struct.ruleInfo.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, createConsumeDeliverOrderByRule_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.orderRequest = new OrderRequest();
        struct.orderRequest.read(iprot);
        struct.setOrderRequestIsSet(true);
        {
          org.apache.thrift.protocol.TList _list53 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.consumeRequests = new ArrayList<ConsumeRequest>(_list53.size);
          ConsumeRequest _elem54;
          for (int _i55 = 0; _i55 < _list53.size; ++_i55)
          {
            _elem54 = new ConsumeRequest();
            _elem54.read(iprot);
            struct.consumeRequests.add(_elem54);
          }
        }
        struct.setConsumeRequestsIsSet(true);
        struct.ruleInfo = new RuleInfo();
        struct.ruleInfo.read(iprot);
        struct.setRuleInfoIsSet(true);
      }
    }

  }

  public static class createConsumeDeliverOrderByRule_result implements org.apache.thrift.TBase<createConsumeDeliverOrderByRule_result, createConsumeDeliverOrderByRule_result._Fields>, java.io.Serializable, Cloneable, Comparable<createConsumeDeliverOrderByRule_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("createConsumeDeliverOrderByRule_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new createConsumeDeliverOrderByRule_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new createConsumeDeliverOrderByRule_resultTupleSchemeFactory());
    }

    public OrderResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(createConsumeDeliverOrderByRule_result.class, metaDataMap);
    }

    public createConsumeDeliverOrderByRule_result() {
    }

    public createConsumeDeliverOrderByRule_result(
      OrderResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public createConsumeDeliverOrderByRule_result(createConsumeDeliverOrderByRule_result other) {
      if (other.isSetSuccess()) {
        this.success = new OrderResponse(other.success);
      }
    }

    public createConsumeDeliverOrderByRule_result deepCopy() {
      return new createConsumeDeliverOrderByRule_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public OrderResponse getSuccess() {
      return this.success;
    }

    public createConsumeDeliverOrderByRule_result setSuccess(OrderResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((OrderResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof createConsumeDeliverOrderByRule_result)
        return this.equals((createConsumeDeliverOrderByRule_result)that);
      return false;
    }

    public boolean equals(createConsumeDeliverOrderByRule_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(createConsumeDeliverOrderByRule_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("createConsumeDeliverOrderByRule_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class createConsumeDeliverOrderByRule_resultStandardSchemeFactory implements SchemeFactory {
      public createConsumeDeliverOrderByRule_resultStandardScheme getScheme() {
        return new createConsumeDeliverOrderByRule_resultStandardScheme();
      }
    }

    private static class createConsumeDeliverOrderByRule_resultStandardScheme extends StandardScheme<createConsumeDeliverOrderByRule_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, createConsumeDeliverOrderByRule_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new OrderResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, createConsumeDeliverOrderByRule_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class createConsumeDeliverOrderByRule_resultTupleSchemeFactory implements SchemeFactory {
      public createConsumeDeliverOrderByRule_resultTupleScheme getScheme() {
        return new createConsumeDeliverOrderByRule_resultTupleScheme();
      }
    }

    private static class createConsumeDeliverOrderByRule_resultTupleScheme extends TupleScheme<createConsumeDeliverOrderByRule_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, createConsumeDeliverOrderByRule_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, createConsumeDeliverOrderByRule_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new OrderResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class createConsumeDeliverOrder_args implements org.apache.thrift.TBase<createConsumeDeliverOrder_args, createConsumeDeliverOrder_args._Fields>, java.io.Serializable, Cloneable, Comparable<createConsumeDeliverOrder_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("createConsumeDeliverOrder_args");

    private static final org.apache.thrift.protocol.TField ORDER_REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("orderRequest", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField CONSUME_REQUESTS_FIELD_DESC = new org.apache.thrift.protocol.TField("consumeRequests", org.apache.thrift.protocol.TType.LIST, (short)2);
    private static final org.apache.thrift.protocol.TField GOODS_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("goodsList", org.apache.thrift.protocol.TType.STRUCT, (short)3);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new createConsumeDeliverOrder_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new createConsumeDeliverOrder_argsTupleSchemeFactory());
    }

    public OrderRequest orderRequest; // required
    public List<ConsumeRequest> consumeRequests; // required
    public GoodsList goodsList; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      ORDER_REQUEST((short)1, "orderRequest"),
      CONSUME_REQUESTS((short)2, "consumeRequests"),
      GOODS_LIST((short)3, "goodsList");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // ORDER_REQUEST
            return ORDER_REQUEST;
          case 2: // CONSUME_REQUESTS
            return CONSUME_REQUESTS;
          case 3: // GOODS_LIST
            return GOODS_LIST;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.ORDER_REQUEST, new org.apache.thrift.meta_data.FieldMetaData("orderRequest", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderRequest.class)));
      tmpMap.put(_Fields.CONSUME_REQUESTS, new org.apache.thrift.meta_data.FieldMetaData("consumeRequests", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
              new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeRequest.class))));
      tmpMap.put(_Fields.GOODS_LIST, new org.apache.thrift.meta_data.FieldMetaData("goodsList", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, GoodsList.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(createConsumeDeliverOrder_args.class, metaDataMap);
    }

    public createConsumeDeliverOrder_args() {
    }

    public createConsumeDeliverOrder_args(
      OrderRequest orderRequest,
      List<ConsumeRequest> consumeRequests,
      GoodsList goodsList)
    {
      this();
      this.orderRequest = orderRequest;
      this.consumeRequests = consumeRequests;
      this.goodsList = goodsList;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public createConsumeDeliverOrder_args(createConsumeDeliverOrder_args other) {
      if (other.isSetOrderRequest()) {
        this.orderRequest = new OrderRequest(other.orderRequest);
      }
      if (other.isSetConsumeRequests()) {
        List<ConsumeRequest> __this__consumeRequests = new ArrayList<ConsumeRequest>(other.consumeRequests.size());
        for (ConsumeRequest other_element : other.consumeRequests) {
          __this__consumeRequests.add(new ConsumeRequest(other_element));
        }
        this.consumeRequests = __this__consumeRequests;
      }
      if (other.isSetGoodsList()) {
        this.goodsList = new GoodsList(other.goodsList);
      }
    }

    public createConsumeDeliverOrder_args deepCopy() {
      return new createConsumeDeliverOrder_args(this);
    }

    @Override
    public void clear() {
      this.orderRequest = null;
      this.consumeRequests = null;
      this.goodsList = null;
    }

    public OrderRequest getOrderRequest() {
      return this.orderRequest;
    }

    public createConsumeDeliverOrder_args setOrderRequest(OrderRequest orderRequest) {
      this.orderRequest = orderRequest;
      return this;
    }

    public void unsetOrderRequest() {
      this.orderRequest = null;
    }

    /** Returns true if field orderRequest is set (has been assigned a value) and false otherwise */
    public boolean isSetOrderRequest() {
      return this.orderRequest != null;
    }

    public void setOrderRequestIsSet(boolean value) {
      if (!value) {
        this.orderRequest = null;
      }
    }

    public int getConsumeRequestsSize() {
      return (this.consumeRequests == null) ? 0 : this.consumeRequests.size();
    }

    public java.util.Iterator<ConsumeRequest> getConsumeRequestsIterator() {
      return (this.consumeRequests == null) ? null : this.consumeRequests.iterator();
    }

    public void addToConsumeRequests(ConsumeRequest elem) {
      if (this.consumeRequests == null) {
        this.consumeRequests = new ArrayList<ConsumeRequest>();
      }
      this.consumeRequests.add(elem);
    }

    public List<ConsumeRequest> getConsumeRequests() {
      return this.consumeRequests;
    }

    public createConsumeDeliverOrder_args setConsumeRequests(List<ConsumeRequest> consumeRequests) {
      this.consumeRequests = consumeRequests;
      return this;
    }

    public void unsetConsumeRequests() {
      this.consumeRequests = null;
    }

    /** Returns true if field consumeRequests is set (has been assigned a value) and false otherwise */
    public boolean isSetConsumeRequests() {
      return this.consumeRequests != null;
    }

    public void setConsumeRequestsIsSet(boolean value) {
      if (!value) {
        this.consumeRequests = null;
      }
    }

    public GoodsList getGoodsList() {
      return this.goodsList;
    }

    public createConsumeDeliverOrder_args setGoodsList(GoodsList goodsList) {
      this.goodsList = goodsList;
      return this;
    }

    public void unsetGoodsList() {
      this.goodsList = null;
    }

    /** Returns true if field goodsList is set (has been assigned a value) and false otherwise */
    public boolean isSetGoodsList() {
      return this.goodsList != null;
    }

    public void setGoodsListIsSet(boolean value) {
      if (!value) {
        this.goodsList = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case ORDER_REQUEST:
        if (value == null) {
          unsetOrderRequest();
        } else {
          setOrderRequest((OrderRequest)value);
        }
        break;

      case CONSUME_REQUESTS:
        if (value == null) {
          unsetConsumeRequests();
        } else {
          setConsumeRequests((List<ConsumeRequest>)value);
        }
        break;

      case GOODS_LIST:
        if (value == null) {
          unsetGoodsList();
        } else {
          setGoodsList((GoodsList)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case ORDER_REQUEST:
        return getOrderRequest();

      case CONSUME_REQUESTS:
        return getConsumeRequests();

      case GOODS_LIST:
        return getGoodsList();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case ORDER_REQUEST:
        return isSetOrderRequest();
      case CONSUME_REQUESTS:
        return isSetConsumeRequests();
      case GOODS_LIST:
        return isSetGoodsList();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof createConsumeDeliverOrder_args)
        return this.equals((createConsumeDeliverOrder_args)that);
      return false;
    }

    public boolean equals(createConsumeDeliverOrder_args that) {
      if (that == null)
        return false;

      boolean this_present_orderRequest = true && this.isSetOrderRequest();
      boolean that_present_orderRequest = true && that.isSetOrderRequest();
      if (this_present_orderRequest || that_present_orderRequest) {
        if (!(this_present_orderRequest && that_present_orderRequest))
          return false;
        if (!this.orderRequest.equals(that.orderRequest))
          return false;
      }

      boolean this_present_consumeRequests = true && this.isSetConsumeRequests();
      boolean that_present_consumeRequests = true && that.isSetConsumeRequests();
      if (this_present_consumeRequests || that_present_consumeRequests) {
        if (!(this_present_consumeRequests && that_present_consumeRequests))
          return false;
        if (!this.consumeRequests.equals(that.consumeRequests))
          return false;
      }

      boolean this_present_goodsList = true && this.isSetGoodsList();
      boolean that_present_goodsList = true && that.isSetGoodsList();
      if (this_present_goodsList || that_present_goodsList) {
        if (!(this_present_goodsList && that_present_goodsList))
          return false;
        if (!this.goodsList.equals(that.goodsList))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_orderRequest = true && (isSetOrderRequest());
      list.add(present_orderRequest);
      if (present_orderRequest)
        list.add(orderRequest);

      boolean present_consumeRequests = true && (isSetConsumeRequests());
      list.add(present_consumeRequests);
      if (present_consumeRequests)
        list.add(consumeRequests);

      boolean present_goodsList = true && (isSetGoodsList());
      list.add(present_goodsList);
      if (present_goodsList)
        list.add(goodsList);

      return list.hashCode();
    }

    @Override
    public int compareTo(createConsumeDeliverOrder_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetOrderRequest()).compareTo(other.isSetOrderRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetOrderRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderRequest, other.orderRequest);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetConsumeRequests()).compareTo(other.isSetConsumeRequests());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetConsumeRequests()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.consumeRequests, other.consumeRequests);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetGoodsList()).compareTo(other.isSetGoodsList());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetGoodsList()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.goodsList, other.goodsList);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("createConsumeDeliverOrder_args(");
      boolean first = true;

      sb.append("orderRequest:");
      if (this.orderRequest == null) {
        sb.append("null");
      } else {
        sb.append(this.orderRequest);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("consumeRequests:");
      if (this.consumeRequests == null) {
        sb.append("null");
      } else {
        sb.append(this.consumeRequests);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("goodsList:");
      if (this.goodsList == null) {
        sb.append("null");
      } else {
        sb.append(this.goodsList);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (orderRequest == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderRequest' was not present! Struct: " + toString());
      }
      if (consumeRequests == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'consumeRequests' was not present! Struct: " + toString());
      }
      if (goodsList == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'goodsList' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (orderRequest != null) {
        orderRequest.validate();
      }
      if (goodsList != null) {
        goodsList.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class createConsumeDeliverOrder_argsStandardSchemeFactory implements SchemeFactory {
      public createConsumeDeliverOrder_argsStandardScheme getScheme() {
        return new createConsumeDeliverOrder_argsStandardScheme();
      }
    }

    private static class createConsumeDeliverOrder_argsStandardScheme extends StandardScheme<createConsumeDeliverOrder_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, createConsumeDeliverOrder_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // ORDER_REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.orderRequest = new OrderRequest();
                struct.orderRequest.read(iprot);
                struct.setOrderRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // CONSUME_REQUESTS
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list56 = iprot.readListBegin();
                  struct.consumeRequests = new ArrayList<ConsumeRequest>(_list56.size);
                  ConsumeRequest _elem57;
                  for (int _i58 = 0; _i58 < _list56.size; ++_i58)
                  {
                    _elem57 = new ConsumeRequest();
                    _elem57.read(iprot);
                    struct.consumeRequests.add(_elem57);
                  }
                  iprot.readListEnd();
                }
                struct.setConsumeRequestsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // GOODS_LIST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.goodsList = new GoodsList();
                struct.goodsList.read(iprot);
                struct.setGoodsListIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, createConsumeDeliverOrder_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.orderRequest != null) {
          oprot.writeFieldBegin(ORDER_REQUEST_FIELD_DESC);
          struct.orderRequest.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.consumeRequests != null) {
          oprot.writeFieldBegin(CONSUME_REQUESTS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.consumeRequests.size()));
            for (ConsumeRequest _iter59 : struct.consumeRequests)
            {
              _iter59.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        if (struct.goodsList != null) {
          oprot.writeFieldBegin(GOODS_LIST_FIELD_DESC);
          struct.goodsList.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class createConsumeDeliverOrder_argsTupleSchemeFactory implements SchemeFactory {
      public createConsumeDeliverOrder_argsTupleScheme getScheme() {
        return new createConsumeDeliverOrder_argsTupleScheme();
      }
    }

    private static class createConsumeDeliverOrder_argsTupleScheme extends TupleScheme<createConsumeDeliverOrder_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, createConsumeDeliverOrder_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.orderRequest.write(oprot);
        {
          oprot.writeI32(struct.consumeRequests.size());
          for (ConsumeRequest _iter60 : struct.consumeRequests)
          {
            _iter60.write(oprot);
          }
        }
        struct.goodsList.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, createConsumeDeliverOrder_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.orderRequest = new OrderRequest();
        struct.orderRequest.read(iprot);
        struct.setOrderRequestIsSet(true);
        {
          org.apache.thrift.protocol.TList _list61 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.consumeRequests = new ArrayList<ConsumeRequest>(_list61.size);
          ConsumeRequest _elem62;
          for (int _i63 = 0; _i63 < _list61.size; ++_i63)
          {
            _elem62 = new ConsumeRequest();
            _elem62.read(iprot);
            struct.consumeRequests.add(_elem62);
          }
        }
        struct.setConsumeRequestsIsSet(true);
        struct.goodsList = new GoodsList();
        struct.goodsList.read(iprot);
        struct.setGoodsListIsSet(true);
      }
    }

  }

  public static class createConsumeDeliverOrder_result implements org.apache.thrift.TBase<createConsumeDeliverOrder_result, createConsumeDeliverOrder_result._Fields>, java.io.Serializable, Cloneable, Comparable<createConsumeDeliverOrder_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("createConsumeDeliverOrder_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new createConsumeDeliverOrder_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new createConsumeDeliverOrder_resultTupleSchemeFactory());
    }

    public OrderResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(createConsumeDeliverOrder_result.class, metaDataMap);
    }

    public createConsumeDeliverOrder_result() {
    }

    public createConsumeDeliverOrder_result(
      OrderResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public createConsumeDeliverOrder_result(createConsumeDeliverOrder_result other) {
      if (other.isSetSuccess()) {
        this.success = new OrderResponse(other.success);
      }
    }

    public createConsumeDeliverOrder_result deepCopy() {
      return new createConsumeDeliverOrder_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public OrderResponse getSuccess() {
      return this.success;
    }

    public createConsumeDeliverOrder_result setSuccess(OrderResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((OrderResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof createConsumeDeliverOrder_result)
        return this.equals((createConsumeDeliverOrder_result)that);
      return false;
    }

    public boolean equals(createConsumeDeliverOrder_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(createConsumeDeliverOrder_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("createConsumeDeliverOrder_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class createConsumeDeliverOrder_resultStandardSchemeFactory implements SchemeFactory {
      public createConsumeDeliverOrder_resultStandardScheme getScheme() {
        return new createConsumeDeliverOrder_resultStandardScheme();
      }
    }

    private static class createConsumeDeliverOrder_resultStandardScheme extends StandardScheme<createConsumeDeliverOrder_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, createConsumeDeliverOrder_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new OrderResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, createConsumeDeliverOrder_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class createConsumeDeliverOrder_resultTupleSchemeFactory implements SchemeFactory {
      public createConsumeDeliverOrder_resultTupleScheme getScheme() {
        return new createConsumeDeliverOrder_resultTupleScheme();
      }
    }

    private static class createConsumeDeliverOrder_resultTupleScheme extends TupleScheme<createConsumeDeliverOrder_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, createConsumeDeliverOrder_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, createConsumeDeliverOrder_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new OrderResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class createDeliverOrderByRule_args implements org.apache.thrift.TBase<createDeliverOrderByRule_args, createDeliverOrderByRule_args._Fields>, java.io.Serializable, Cloneable, Comparable<createDeliverOrderByRule_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("createDeliverOrderByRule_args");

    private static final org.apache.thrift.protocol.TField ORDER_REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("orderRequest", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField RULE_INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("ruleInfo", org.apache.thrift.protocol.TType.STRUCT, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new createDeliverOrderByRule_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new createDeliverOrderByRule_argsTupleSchemeFactory());
    }

    public OrderRequest orderRequest; // required
    public RuleInfo ruleInfo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      ORDER_REQUEST((short)1, "orderRequest"),
      RULE_INFO((short)2, "ruleInfo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // ORDER_REQUEST
            return ORDER_REQUEST;
          case 2: // RULE_INFO
            return RULE_INFO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.ORDER_REQUEST, new org.apache.thrift.meta_data.FieldMetaData("orderRequest", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderRequest.class)));
      tmpMap.put(_Fields.RULE_INFO, new org.apache.thrift.meta_data.FieldMetaData("ruleInfo", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, RuleInfo.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(createDeliverOrderByRule_args.class, metaDataMap);
    }

    public createDeliverOrderByRule_args() {
    }

    public createDeliverOrderByRule_args(
      OrderRequest orderRequest,
      RuleInfo ruleInfo)
    {
      this();
      this.orderRequest = orderRequest;
      this.ruleInfo = ruleInfo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public createDeliverOrderByRule_args(createDeliverOrderByRule_args other) {
      if (other.isSetOrderRequest()) {
        this.orderRequest = new OrderRequest(other.orderRequest);
      }
      if (other.isSetRuleInfo()) {
        this.ruleInfo = new RuleInfo(other.ruleInfo);
      }
    }

    public createDeliverOrderByRule_args deepCopy() {
      return new createDeliverOrderByRule_args(this);
    }

    @Override
    public void clear() {
      this.orderRequest = null;
      this.ruleInfo = null;
    }

    public OrderRequest getOrderRequest() {
      return this.orderRequest;
    }

    public createDeliverOrderByRule_args setOrderRequest(OrderRequest orderRequest) {
      this.orderRequest = orderRequest;
      return this;
    }

    public void unsetOrderRequest() {
      this.orderRequest = null;
    }

    /** Returns true if field orderRequest is set (has been assigned a value) and false otherwise */
    public boolean isSetOrderRequest() {
      return this.orderRequest != null;
    }

    public void setOrderRequestIsSet(boolean value) {
      if (!value) {
        this.orderRequest = null;
      }
    }

    public RuleInfo getRuleInfo() {
      return this.ruleInfo;
    }

    public createDeliverOrderByRule_args setRuleInfo(RuleInfo ruleInfo) {
      this.ruleInfo = ruleInfo;
      return this;
    }

    public void unsetRuleInfo() {
      this.ruleInfo = null;
    }

    /** Returns true if field ruleInfo is set (has been assigned a value) and false otherwise */
    public boolean isSetRuleInfo() {
      return this.ruleInfo != null;
    }

    public void setRuleInfoIsSet(boolean value) {
      if (!value) {
        this.ruleInfo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case ORDER_REQUEST:
        if (value == null) {
          unsetOrderRequest();
        } else {
          setOrderRequest((OrderRequest)value);
        }
        break;

      case RULE_INFO:
        if (value == null) {
          unsetRuleInfo();
        } else {
          setRuleInfo((RuleInfo)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case ORDER_REQUEST:
        return getOrderRequest();

      case RULE_INFO:
        return getRuleInfo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case ORDER_REQUEST:
        return isSetOrderRequest();
      case RULE_INFO:
        return isSetRuleInfo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof createDeliverOrderByRule_args)
        return this.equals((createDeliverOrderByRule_args)that);
      return false;
    }

    public boolean equals(createDeliverOrderByRule_args that) {
      if (that == null)
        return false;

      boolean this_present_orderRequest = true && this.isSetOrderRequest();
      boolean that_present_orderRequest = true && that.isSetOrderRequest();
      if (this_present_orderRequest || that_present_orderRequest) {
        if (!(this_present_orderRequest && that_present_orderRequest))
          return false;
        if (!this.orderRequest.equals(that.orderRequest))
          return false;
      }

      boolean this_present_ruleInfo = true && this.isSetRuleInfo();
      boolean that_present_ruleInfo = true && that.isSetRuleInfo();
      if (this_present_ruleInfo || that_present_ruleInfo) {
        if (!(this_present_ruleInfo && that_present_ruleInfo))
          return false;
        if (!this.ruleInfo.equals(that.ruleInfo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_orderRequest = true && (isSetOrderRequest());
      list.add(present_orderRequest);
      if (present_orderRequest)
        list.add(orderRequest);

      boolean present_ruleInfo = true && (isSetRuleInfo());
      list.add(present_ruleInfo);
      if (present_ruleInfo)
        list.add(ruleInfo);

      return list.hashCode();
    }

    @Override
    public int compareTo(createDeliverOrderByRule_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetOrderRequest()).compareTo(other.isSetOrderRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetOrderRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderRequest, other.orderRequest);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetRuleInfo()).compareTo(other.isSetRuleInfo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRuleInfo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ruleInfo, other.ruleInfo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("createDeliverOrderByRule_args(");
      boolean first = true;

      sb.append("orderRequest:");
      if (this.orderRequest == null) {
        sb.append("null");
      } else {
        sb.append(this.orderRequest);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("ruleInfo:");
      if (this.ruleInfo == null) {
        sb.append("null");
      } else {
        sb.append(this.ruleInfo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (orderRequest == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderRequest' was not present! Struct: " + toString());
      }
      if (ruleInfo == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'ruleInfo' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (orderRequest != null) {
        orderRequest.validate();
      }
      if (ruleInfo != null) {
        ruleInfo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class createDeliverOrderByRule_argsStandardSchemeFactory implements SchemeFactory {
      public createDeliverOrderByRule_argsStandardScheme getScheme() {
        return new createDeliverOrderByRule_argsStandardScheme();
      }
    }

    private static class createDeliverOrderByRule_argsStandardScheme extends StandardScheme<createDeliverOrderByRule_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, createDeliverOrderByRule_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // ORDER_REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.orderRequest = new OrderRequest();
                struct.orderRequest.read(iprot);
                struct.setOrderRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // RULE_INFO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.ruleInfo = new RuleInfo();
                struct.ruleInfo.read(iprot);
                struct.setRuleInfoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, createDeliverOrderByRule_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.orderRequest != null) {
          oprot.writeFieldBegin(ORDER_REQUEST_FIELD_DESC);
          struct.orderRequest.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.ruleInfo != null) {
          oprot.writeFieldBegin(RULE_INFO_FIELD_DESC);
          struct.ruleInfo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class createDeliverOrderByRule_argsTupleSchemeFactory implements SchemeFactory {
      public createDeliverOrderByRule_argsTupleScheme getScheme() {
        return new createDeliverOrderByRule_argsTupleScheme();
      }
    }

    private static class createDeliverOrderByRule_argsTupleScheme extends TupleScheme<createDeliverOrderByRule_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, createDeliverOrderByRule_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.orderRequest.write(oprot);
        struct.ruleInfo.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, createDeliverOrderByRule_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.orderRequest = new OrderRequest();
        struct.orderRequest.read(iprot);
        struct.setOrderRequestIsSet(true);
        struct.ruleInfo = new RuleInfo();
        struct.ruleInfo.read(iprot);
        struct.setRuleInfoIsSet(true);
      }
    }

  }

  public static class createDeliverOrderByRule_result implements org.apache.thrift.TBase<createDeliverOrderByRule_result, createDeliverOrderByRule_result._Fields>, java.io.Serializable, Cloneable, Comparable<createDeliverOrderByRule_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("createDeliverOrderByRule_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new createDeliverOrderByRule_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new createDeliverOrderByRule_resultTupleSchemeFactory());
    }

    public OrderResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(createDeliverOrderByRule_result.class, metaDataMap);
    }

    public createDeliverOrderByRule_result() {
    }

    public createDeliverOrderByRule_result(
      OrderResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public createDeliverOrderByRule_result(createDeliverOrderByRule_result other) {
      if (other.isSetSuccess()) {
        this.success = new OrderResponse(other.success);
      }
    }

    public createDeliverOrderByRule_result deepCopy() {
      return new createDeliverOrderByRule_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public OrderResponse getSuccess() {
      return this.success;
    }

    public createDeliverOrderByRule_result setSuccess(OrderResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((OrderResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof createDeliverOrderByRule_result)
        return this.equals((createDeliverOrderByRule_result)that);
      return false;
    }

    public boolean equals(createDeliverOrderByRule_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(createDeliverOrderByRule_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("createDeliverOrderByRule_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class createDeliverOrderByRule_resultStandardSchemeFactory implements SchemeFactory {
      public createDeliverOrderByRule_resultStandardScheme getScheme() {
        return new createDeliverOrderByRule_resultStandardScheme();
      }
    }

    private static class createDeliverOrderByRule_resultStandardScheme extends StandardScheme<createDeliverOrderByRule_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, createDeliverOrderByRule_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new OrderResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, createDeliverOrderByRule_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class createDeliverOrderByRule_resultTupleSchemeFactory implements SchemeFactory {
      public createDeliverOrderByRule_resultTupleScheme getScheme() {
        return new createDeliverOrderByRule_resultTupleScheme();
      }
    }

    private static class createDeliverOrderByRule_resultTupleScheme extends TupleScheme<createDeliverOrderByRule_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, createDeliverOrderByRule_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, createDeliverOrderByRule_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new OrderResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class createDeliverOrder_args implements org.apache.thrift.TBase<createDeliverOrder_args, createDeliverOrder_args._Fields>, java.io.Serializable, Cloneable, Comparable<createDeliverOrder_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("createDeliverOrder_args");

    private static final org.apache.thrift.protocol.TField ORDER_REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("orderRequest", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField GOODS_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("goodsList", org.apache.thrift.protocol.TType.STRUCT, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new createDeliverOrder_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new createDeliverOrder_argsTupleSchemeFactory());
    }

    public OrderRequest orderRequest; // required
    public GoodsList goodsList; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      ORDER_REQUEST((short)1, "orderRequest"),
      GOODS_LIST((short)2, "goodsList");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // ORDER_REQUEST
            return ORDER_REQUEST;
          case 2: // GOODS_LIST
            return GOODS_LIST;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.ORDER_REQUEST, new org.apache.thrift.meta_data.FieldMetaData("orderRequest", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderRequest.class)));
      tmpMap.put(_Fields.GOODS_LIST, new org.apache.thrift.meta_data.FieldMetaData("goodsList", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, GoodsList.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(createDeliverOrder_args.class, metaDataMap);
    }

    public createDeliverOrder_args() {
    }

    public createDeliverOrder_args(
      OrderRequest orderRequest,
      GoodsList goodsList)
    {
      this();
      this.orderRequest = orderRequest;
      this.goodsList = goodsList;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public createDeliverOrder_args(createDeliverOrder_args other) {
      if (other.isSetOrderRequest()) {
        this.orderRequest = new OrderRequest(other.orderRequest);
      }
      if (other.isSetGoodsList()) {
        this.goodsList = new GoodsList(other.goodsList);
      }
    }

    public createDeliverOrder_args deepCopy() {
      return new createDeliverOrder_args(this);
    }

    @Override
    public void clear() {
      this.orderRequest = null;
      this.goodsList = null;
    }

    public OrderRequest getOrderRequest() {
      return this.orderRequest;
    }

    public createDeliverOrder_args setOrderRequest(OrderRequest orderRequest) {
      this.orderRequest = orderRequest;
      return this;
    }

    public void unsetOrderRequest() {
      this.orderRequest = null;
    }

    /** Returns true if field orderRequest is set (has been assigned a value) and false otherwise */
    public boolean isSetOrderRequest() {
      return this.orderRequest != null;
    }

    public void setOrderRequestIsSet(boolean value) {
      if (!value) {
        this.orderRequest = null;
      }
    }

    public GoodsList getGoodsList() {
      return this.goodsList;
    }

    public createDeliverOrder_args setGoodsList(GoodsList goodsList) {
      this.goodsList = goodsList;
      return this;
    }

    public void unsetGoodsList() {
      this.goodsList = null;
    }

    /** Returns true if field goodsList is set (has been assigned a value) and false otherwise */
    public boolean isSetGoodsList() {
      return this.goodsList != null;
    }

    public void setGoodsListIsSet(boolean value) {
      if (!value) {
        this.goodsList = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case ORDER_REQUEST:
        if (value == null) {
          unsetOrderRequest();
        } else {
          setOrderRequest((OrderRequest)value);
        }
        break;

      case GOODS_LIST:
        if (value == null) {
          unsetGoodsList();
        } else {
          setGoodsList((GoodsList)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case ORDER_REQUEST:
        return getOrderRequest();

      case GOODS_LIST:
        return getGoodsList();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case ORDER_REQUEST:
        return isSetOrderRequest();
      case GOODS_LIST:
        return isSetGoodsList();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof createDeliverOrder_args)
        return this.equals((createDeliverOrder_args)that);
      return false;
    }

    public boolean equals(createDeliverOrder_args that) {
      if (that == null)
        return false;

      boolean this_present_orderRequest = true && this.isSetOrderRequest();
      boolean that_present_orderRequest = true && that.isSetOrderRequest();
      if (this_present_orderRequest || that_present_orderRequest) {
        if (!(this_present_orderRequest && that_present_orderRequest))
          return false;
        if (!this.orderRequest.equals(that.orderRequest))
          return false;
      }

      boolean this_present_goodsList = true && this.isSetGoodsList();
      boolean that_present_goodsList = true && that.isSetGoodsList();
      if (this_present_goodsList || that_present_goodsList) {
        if (!(this_present_goodsList && that_present_goodsList))
          return false;
        if (!this.goodsList.equals(that.goodsList))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_orderRequest = true && (isSetOrderRequest());
      list.add(present_orderRequest);
      if (present_orderRequest)
        list.add(orderRequest);

      boolean present_goodsList = true && (isSetGoodsList());
      list.add(present_goodsList);
      if (present_goodsList)
        list.add(goodsList);

      return list.hashCode();
    }

    @Override
    public int compareTo(createDeliverOrder_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetOrderRequest()).compareTo(other.isSetOrderRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetOrderRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderRequest, other.orderRequest);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetGoodsList()).compareTo(other.isSetGoodsList());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetGoodsList()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.goodsList, other.goodsList);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("createDeliverOrder_args(");
      boolean first = true;

      sb.append("orderRequest:");
      if (this.orderRequest == null) {
        sb.append("null");
      } else {
        sb.append(this.orderRequest);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("goodsList:");
      if (this.goodsList == null) {
        sb.append("null");
      } else {
        sb.append(this.goodsList);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (orderRequest == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderRequest' was not present! Struct: " + toString());
      }
      if (goodsList == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'goodsList' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (orderRequest != null) {
        orderRequest.validate();
      }
      if (goodsList != null) {
        goodsList.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class createDeliverOrder_argsStandardSchemeFactory implements SchemeFactory {
      public createDeliverOrder_argsStandardScheme getScheme() {
        return new createDeliverOrder_argsStandardScheme();
      }
    }

    private static class createDeliverOrder_argsStandardScheme extends StandardScheme<createDeliverOrder_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, createDeliverOrder_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // ORDER_REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.orderRequest = new OrderRequest();
                struct.orderRequest.read(iprot);
                struct.setOrderRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // GOODS_LIST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.goodsList = new GoodsList();
                struct.goodsList.read(iprot);
                struct.setGoodsListIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, createDeliverOrder_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.orderRequest != null) {
          oprot.writeFieldBegin(ORDER_REQUEST_FIELD_DESC);
          struct.orderRequest.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.goodsList != null) {
          oprot.writeFieldBegin(GOODS_LIST_FIELD_DESC);
          struct.goodsList.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class createDeliverOrder_argsTupleSchemeFactory implements SchemeFactory {
      public createDeliverOrder_argsTupleScheme getScheme() {
        return new createDeliverOrder_argsTupleScheme();
      }
    }

    private static class createDeliverOrder_argsTupleScheme extends TupleScheme<createDeliverOrder_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, createDeliverOrder_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.orderRequest.write(oprot);
        struct.goodsList.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, createDeliverOrder_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.orderRequest = new OrderRequest();
        struct.orderRequest.read(iprot);
        struct.setOrderRequestIsSet(true);
        struct.goodsList = new GoodsList();
        struct.goodsList.read(iprot);
        struct.setGoodsListIsSet(true);
      }
    }

  }

  public static class createDeliverOrder_result implements org.apache.thrift.TBase<createDeliverOrder_result, createDeliverOrder_result._Fields>, java.io.Serializable, Cloneable, Comparable<createDeliverOrder_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("createDeliverOrder_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new createDeliverOrder_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new createDeliverOrder_resultTupleSchemeFactory());
    }

    public OrderResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(createDeliverOrder_result.class, metaDataMap);
    }

    public createDeliverOrder_result() {
    }

    public createDeliverOrder_result(
      OrderResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public createDeliverOrder_result(createDeliverOrder_result other) {
      if (other.isSetSuccess()) {
        this.success = new OrderResponse(other.success);
      }
    }

    public createDeliverOrder_result deepCopy() {
      return new createDeliverOrder_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public OrderResponse getSuccess() {
      return this.success;
    }

    public createDeliverOrder_result setSuccess(OrderResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((OrderResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof createDeliverOrder_result)
        return this.equals((createDeliverOrder_result)that);
      return false;
    }

    public boolean equals(createDeliverOrder_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(createDeliverOrder_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("createDeliverOrder_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class createDeliverOrder_resultStandardSchemeFactory implements SchemeFactory {
      public createDeliverOrder_resultStandardScheme getScheme() {
        return new createDeliverOrder_resultStandardScheme();
      }
    }

    private static class createDeliverOrder_resultStandardScheme extends StandardScheme<createDeliverOrder_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, createDeliverOrder_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new OrderResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, createDeliverOrder_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class createDeliverOrder_resultTupleSchemeFactory implements SchemeFactory {
      public createDeliverOrder_resultTupleScheme getScheme() {
        return new createDeliverOrder_resultTupleScheme();
      }
    }

    private static class createDeliverOrder_resultTupleScheme extends TupleScheme<createDeliverOrder_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, createDeliverOrder_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, createDeliverOrder_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new OrderResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

}
