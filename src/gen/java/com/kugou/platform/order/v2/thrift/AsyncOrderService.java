/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.platform.order.v2.thrift;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2020-05-28")
public class AsyncOrderService {

  /**
   * 异步订单模型
   * 
   */
  public interface Iface {

    /**
     * 订单扣费(下单)
     * 
     * 
     * @param orderRequest
     * @param consumeRequests
     */
    public OrderResponse createAsyncConsumeOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests) throws org.apache.thrift.TException;

    /**
     * 订单(下单)
     * 
     * 
     * @param orderRequest
     */
    public OrderResponse createAsyncOrder(OrderRequest orderRequest) throws org.apache.thrift.TException;

    /**
     * 订单根据规则发货
     * 
     * 
     * @param orderRequest
     * @param ruleInfo
     */
    public OrderResponse asyncDeliverOrderByRule(OrderRequest orderRequest, RuleInfo ruleInfo) throws org.apache.thrift.TException;

    /**
     * 订单根据规则扣费发货
     * 
     * 
     * @param orderRequest
     * @param consumeRequests
     * @param ruleInfo
     */
    public OrderResponse asyncConsumeDeliverOrderByRule(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, RuleInfo ruleInfo) throws org.apache.thrift.TException;

    /**
     * 订单扣费发货
     * 
     * 
     * @param orderRequest
     * @param consumeRequests
     * @param goodsList
     */
    public OrderResponse asyncConsumeDeliverOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, GoodsList goodsList) throws org.apache.thrift.TException;

    /**
     * 订单发货
     * 
     * 
     * @param orderRequest
     * @param goodsList
     */
    public OrderResponse asyncDeliverOrder(OrderRequest orderRequest, GoodsList goodsList) throws org.apache.thrift.TException;

    /**
     * finsh订单
     * 
     * 
     * @param orderRequest
     */
    public OrderResponse finishOrder(OrderRequest orderRequest) throws org.apache.thrift.TException;

    /**
     * finsh-订单finish并根据规则发货
     * 
     * 
     * @param orderRequest
     * @param ruleInfo
     */
    public OrderResponse finishOrderByRule(OrderRequest orderRequest, RuleInfo ruleInfo) throws org.apache.thrift.TException;

  }

  public interface AsyncIface {

    public void createAsyncConsumeOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void createAsyncOrder(OrderRequest orderRequest, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void asyncDeliverOrderByRule(OrderRequest orderRequest, RuleInfo ruleInfo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void asyncConsumeDeliverOrderByRule(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, RuleInfo ruleInfo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void asyncConsumeDeliverOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, GoodsList goodsList, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void asyncDeliverOrder(OrderRequest orderRequest, GoodsList goodsList, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void finishOrder(OrderRequest orderRequest, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void finishOrderByRule(OrderRequest orderRequest, RuleInfo ruleInfo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

  }

  public static class Client extends org.apache.thrift.TServiceClient implements Iface {
    public static class Factory implements org.apache.thrift.TServiceClientFactory<Client> {
      public Factory() {}
      public Client getClient(org.apache.thrift.protocol.TProtocol prot) {
        return new Client(prot);
      }
      public Client getClient(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
        return new Client(iprot, oprot);
      }
    }

    public Client(org.apache.thrift.protocol.TProtocol prot)
    {
      super(prot, prot);
    }

    public Client(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
      super(iprot, oprot);
    }

    public OrderResponse createAsyncConsumeOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests) throws org.apache.thrift.TException
    {
      send_createAsyncConsumeOrder(orderRequest, consumeRequests);
      return recv_createAsyncConsumeOrder();
    }

    public void send_createAsyncConsumeOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests) throws org.apache.thrift.TException
    {
      createAsyncConsumeOrder_args args = new createAsyncConsumeOrder_args();
      args.setOrderRequest(orderRequest);
      args.setConsumeRequests(consumeRequests);
      sendBase("createAsyncConsumeOrder", args);
    }

    public OrderResponse recv_createAsyncConsumeOrder() throws org.apache.thrift.TException
    {
      createAsyncConsumeOrder_result result = new createAsyncConsumeOrder_result();
      receiveBase(result, "createAsyncConsumeOrder");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "createAsyncConsumeOrder failed: unknown result");
    }

    public OrderResponse createAsyncOrder(OrderRequest orderRequest) throws org.apache.thrift.TException
    {
      send_createAsyncOrder(orderRequest);
      return recv_createAsyncOrder();
    }

    public void send_createAsyncOrder(OrderRequest orderRequest) throws org.apache.thrift.TException
    {
      createAsyncOrder_args args = new createAsyncOrder_args();
      args.setOrderRequest(orderRequest);
      sendBase("createAsyncOrder", args);
    }

    public OrderResponse recv_createAsyncOrder() throws org.apache.thrift.TException
    {
      createAsyncOrder_result result = new createAsyncOrder_result();
      receiveBase(result, "createAsyncOrder");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "createAsyncOrder failed: unknown result");
    }

    public OrderResponse asyncDeliverOrderByRule(OrderRequest orderRequest, RuleInfo ruleInfo) throws org.apache.thrift.TException
    {
      send_asyncDeliverOrderByRule(orderRequest, ruleInfo);
      return recv_asyncDeliverOrderByRule();
    }

    public void send_asyncDeliverOrderByRule(OrderRequest orderRequest, RuleInfo ruleInfo) throws org.apache.thrift.TException
    {
      asyncDeliverOrderByRule_args args = new asyncDeliverOrderByRule_args();
      args.setOrderRequest(orderRequest);
      args.setRuleInfo(ruleInfo);
      sendBase("asyncDeliverOrderByRule", args);
    }

    public OrderResponse recv_asyncDeliverOrderByRule() throws org.apache.thrift.TException
    {
      asyncDeliverOrderByRule_result result = new asyncDeliverOrderByRule_result();
      receiveBase(result, "asyncDeliverOrderByRule");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "asyncDeliverOrderByRule failed: unknown result");
    }

    public OrderResponse asyncConsumeDeliverOrderByRule(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, RuleInfo ruleInfo) throws org.apache.thrift.TException
    {
      send_asyncConsumeDeliverOrderByRule(orderRequest, consumeRequests, ruleInfo);
      return recv_asyncConsumeDeliverOrderByRule();
    }

    public void send_asyncConsumeDeliverOrderByRule(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, RuleInfo ruleInfo) throws org.apache.thrift.TException
    {
      asyncConsumeDeliverOrderByRule_args args = new asyncConsumeDeliverOrderByRule_args();
      args.setOrderRequest(orderRequest);
      args.setConsumeRequests(consumeRequests);
      args.setRuleInfo(ruleInfo);
      sendBase("asyncConsumeDeliverOrderByRule", args);
    }

    public OrderResponse recv_asyncConsumeDeliverOrderByRule() throws org.apache.thrift.TException
    {
      asyncConsumeDeliverOrderByRule_result result = new asyncConsumeDeliverOrderByRule_result();
      receiveBase(result, "asyncConsumeDeliverOrderByRule");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "asyncConsumeDeliverOrderByRule failed: unknown result");
    }

    public OrderResponse asyncConsumeDeliverOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, GoodsList goodsList) throws org.apache.thrift.TException
    {
      send_asyncConsumeDeliverOrder(orderRequest, consumeRequests, goodsList);
      return recv_asyncConsumeDeliverOrder();
    }

    public void send_asyncConsumeDeliverOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, GoodsList goodsList) throws org.apache.thrift.TException
    {
      asyncConsumeDeliverOrder_args args = new asyncConsumeDeliverOrder_args();
      args.setOrderRequest(orderRequest);
      args.setConsumeRequests(consumeRequests);
      args.setGoodsList(goodsList);
      sendBase("asyncConsumeDeliverOrder", args);
    }

    public OrderResponse recv_asyncConsumeDeliverOrder() throws org.apache.thrift.TException
    {
      asyncConsumeDeliverOrder_result result = new asyncConsumeDeliverOrder_result();
      receiveBase(result, "asyncConsumeDeliverOrder");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "asyncConsumeDeliverOrder failed: unknown result");
    }

    public OrderResponse asyncDeliverOrder(OrderRequest orderRequest, GoodsList goodsList) throws org.apache.thrift.TException
    {
      send_asyncDeliverOrder(orderRequest, goodsList);
      return recv_asyncDeliverOrder();
    }

    public void send_asyncDeliverOrder(OrderRequest orderRequest, GoodsList goodsList) throws org.apache.thrift.TException
    {
      asyncDeliverOrder_args args = new asyncDeliverOrder_args();
      args.setOrderRequest(orderRequest);
      args.setGoodsList(goodsList);
      sendBase("asyncDeliverOrder", args);
    }

    public OrderResponse recv_asyncDeliverOrder() throws org.apache.thrift.TException
    {
      asyncDeliverOrder_result result = new asyncDeliverOrder_result();
      receiveBase(result, "asyncDeliverOrder");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "asyncDeliverOrder failed: unknown result");
    }

    public OrderResponse finishOrder(OrderRequest orderRequest) throws org.apache.thrift.TException
    {
      send_finishOrder(orderRequest);
      return recv_finishOrder();
    }

    public void send_finishOrder(OrderRequest orderRequest) throws org.apache.thrift.TException
    {
      finishOrder_args args = new finishOrder_args();
      args.setOrderRequest(orderRequest);
      sendBase("finishOrder", args);
    }

    public OrderResponse recv_finishOrder() throws org.apache.thrift.TException
    {
      finishOrder_result result = new finishOrder_result();
      receiveBase(result, "finishOrder");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "finishOrder failed: unknown result");
    }

    public OrderResponse finishOrderByRule(OrderRequest orderRequest, RuleInfo ruleInfo) throws org.apache.thrift.TException
    {
      send_finishOrderByRule(orderRequest, ruleInfo);
      return recv_finishOrderByRule();
    }

    public void send_finishOrderByRule(OrderRequest orderRequest, RuleInfo ruleInfo) throws org.apache.thrift.TException
    {
      finishOrderByRule_args args = new finishOrderByRule_args();
      args.setOrderRequest(orderRequest);
      args.setRuleInfo(ruleInfo);
      sendBase("finishOrderByRule", args);
    }

    public OrderResponse recv_finishOrderByRule() throws org.apache.thrift.TException
    {
      finishOrderByRule_result result = new finishOrderByRule_result();
      receiveBase(result, "finishOrderByRule");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "finishOrderByRule failed: unknown result");
    }

  }
  public static class AsyncClient extends org.apache.thrift.async.TAsyncClient implements AsyncIface {
    public static class Factory implements org.apache.thrift.async.TAsyncClientFactory<AsyncClient> {
      private org.apache.thrift.async.TAsyncClientManager clientManager;
      private org.apache.thrift.protocol.TProtocolFactory protocolFactory;
      public Factory(org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.protocol.TProtocolFactory protocolFactory) {
        this.clientManager = clientManager;
        this.protocolFactory = protocolFactory;
      }
      public AsyncClient getAsyncClient(org.apache.thrift.transport.TNonblockingTransport transport) {
        return new AsyncClient(protocolFactory, clientManager, transport);
      }
    }

    public AsyncClient(org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.transport.TNonblockingTransport transport) {
      super(protocolFactory, clientManager, transport);
    }

    public void createAsyncConsumeOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      createAsyncConsumeOrder_call method_call = new createAsyncConsumeOrder_call(orderRequest, consumeRequests, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class createAsyncConsumeOrder_call extends org.apache.thrift.async.TAsyncMethodCall {
      private OrderRequest orderRequest;
      private List<ConsumeRequest> consumeRequests;
      public createAsyncConsumeOrder_call(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.orderRequest = orderRequest;
        this.consumeRequests = consumeRequests;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("createAsyncConsumeOrder", org.apache.thrift.protocol.TMessageType.CALL, 0));
        createAsyncConsumeOrder_args args = new createAsyncConsumeOrder_args();
        args.setOrderRequest(orderRequest);
        args.setConsumeRequests(consumeRequests);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public OrderResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_createAsyncConsumeOrder();
      }
    }

    public void createAsyncOrder(OrderRequest orderRequest, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      createAsyncOrder_call method_call = new createAsyncOrder_call(orderRequest, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class createAsyncOrder_call extends org.apache.thrift.async.TAsyncMethodCall {
      private OrderRequest orderRequest;
      public createAsyncOrder_call(OrderRequest orderRequest, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.orderRequest = orderRequest;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("createAsyncOrder", org.apache.thrift.protocol.TMessageType.CALL, 0));
        createAsyncOrder_args args = new createAsyncOrder_args();
        args.setOrderRequest(orderRequest);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public OrderResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_createAsyncOrder();
      }
    }

    public void asyncDeliverOrderByRule(OrderRequest orderRequest, RuleInfo ruleInfo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      asyncDeliverOrderByRule_call method_call = new asyncDeliverOrderByRule_call(orderRequest, ruleInfo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class asyncDeliverOrderByRule_call extends org.apache.thrift.async.TAsyncMethodCall {
      private OrderRequest orderRequest;
      private RuleInfo ruleInfo;
      public asyncDeliverOrderByRule_call(OrderRequest orderRequest, RuleInfo ruleInfo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.orderRequest = orderRequest;
        this.ruleInfo = ruleInfo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("asyncDeliverOrderByRule", org.apache.thrift.protocol.TMessageType.CALL, 0));
        asyncDeliverOrderByRule_args args = new asyncDeliverOrderByRule_args();
        args.setOrderRequest(orderRequest);
        args.setRuleInfo(ruleInfo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public OrderResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_asyncDeliverOrderByRule();
      }
    }

    public void asyncConsumeDeliverOrderByRule(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, RuleInfo ruleInfo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      asyncConsumeDeliverOrderByRule_call method_call = new asyncConsumeDeliverOrderByRule_call(orderRequest, consumeRequests, ruleInfo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class asyncConsumeDeliverOrderByRule_call extends org.apache.thrift.async.TAsyncMethodCall {
      private OrderRequest orderRequest;
      private List<ConsumeRequest> consumeRequests;
      private RuleInfo ruleInfo;
      public asyncConsumeDeliverOrderByRule_call(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, RuleInfo ruleInfo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.orderRequest = orderRequest;
        this.consumeRequests = consumeRequests;
        this.ruleInfo = ruleInfo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("asyncConsumeDeliverOrderByRule", org.apache.thrift.protocol.TMessageType.CALL, 0));
        asyncConsumeDeliverOrderByRule_args args = new asyncConsumeDeliverOrderByRule_args();
        args.setOrderRequest(orderRequest);
        args.setConsumeRequests(consumeRequests);
        args.setRuleInfo(ruleInfo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public OrderResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_asyncConsumeDeliverOrderByRule();
      }
    }

    public void asyncConsumeDeliverOrder(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, GoodsList goodsList, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      asyncConsumeDeliverOrder_call method_call = new asyncConsumeDeliverOrder_call(orderRequest, consumeRequests, goodsList, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class asyncConsumeDeliverOrder_call extends org.apache.thrift.async.TAsyncMethodCall {
      private OrderRequest orderRequest;
      private List<ConsumeRequest> consumeRequests;
      private GoodsList goodsList;
      public asyncConsumeDeliverOrder_call(OrderRequest orderRequest, List<ConsumeRequest> consumeRequests, GoodsList goodsList, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.orderRequest = orderRequest;
        this.consumeRequests = consumeRequests;
        this.goodsList = goodsList;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("asyncConsumeDeliverOrder", org.apache.thrift.protocol.TMessageType.CALL, 0));
        asyncConsumeDeliverOrder_args args = new asyncConsumeDeliverOrder_args();
        args.setOrderRequest(orderRequest);
        args.setConsumeRequests(consumeRequests);
        args.setGoodsList(goodsList);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public OrderResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_asyncConsumeDeliverOrder();
      }
    }

    public void asyncDeliverOrder(OrderRequest orderRequest, GoodsList goodsList, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      asyncDeliverOrder_call method_call = new asyncDeliverOrder_call(orderRequest, goodsList, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class asyncDeliverOrder_call extends org.apache.thrift.async.TAsyncMethodCall {
      private OrderRequest orderRequest;
      private GoodsList goodsList;
      public asyncDeliverOrder_call(OrderRequest orderRequest, GoodsList goodsList, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.orderRequest = orderRequest;
        this.goodsList = goodsList;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("asyncDeliverOrder", org.apache.thrift.protocol.TMessageType.CALL, 0));
        asyncDeliverOrder_args args = new asyncDeliverOrder_args();
        args.setOrderRequest(orderRequest);
        args.setGoodsList(goodsList);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public OrderResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_asyncDeliverOrder();
      }
    }

    public void finishOrder(OrderRequest orderRequest, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      finishOrder_call method_call = new finishOrder_call(orderRequest, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class finishOrder_call extends org.apache.thrift.async.TAsyncMethodCall {
      private OrderRequest orderRequest;
      public finishOrder_call(OrderRequest orderRequest, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.orderRequest = orderRequest;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("finishOrder", org.apache.thrift.protocol.TMessageType.CALL, 0));
        finishOrder_args args = new finishOrder_args();
        args.setOrderRequest(orderRequest);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public OrderResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_finishOrder();
      }
    }

    public void finishOrderByRule(OrderRequest orderRequest, RuleInfo ruleInfo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      finishOrderByRule_call method_call = new finishOrderByRule_call(orderRequest, ruleInfo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class finishOrderByRule_call extends org.apache.thrift.async.TAsyncMethodCall {
      private OrderRequest orderRequest;
      private RuleInfo ruleInfo;
      public finishOrderByRule_call(OrderRequest orderRequest, RuleInfo ruleInfo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.orderRequest = orderRequest;
        this.ruleInfo = ruleInfo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("finishOrderByRule", org.apache.thrift.protocol.TMessageType.CALL, 0));
        finishOrderByRule_args args = new finishOrderByRule_args();
        args.setOrderRequest(orderRequest);
        args.setRuleInfo(ruleInfo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public OrderResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_finishOrderByRule();
      }
    }

  }

  public static class Processor<I extends Iface> extends org.apache.thrift.TBaseProcessor<I> implements org.apache.thrift.TProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());
    public Processor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>>()));
    }

    protected Processor(I iface, Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends Iface> Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> getProcessMap(Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      processMap.put("createAsyncConsumeOrder", new createAsyncConsumeOrder());
      processMap.put("createAsyncOrder", new createAsyncOrder());
      processMap.put("asyncDeliverOrderByRule", new asyncDeliverOrderByRule());
      processMap.put("asyncConsumeDeliverOrderByRule", new asyncConsumeDeliverOrderByRule());
      processMap.put("asyncConsumeDeliverOrder", new asyncConsumeDeliverOrder());
      processMap.put("asyncDeliverOrder", new asyncDeliverOrder());
      processMap.put("finishOrder", new finishOrder());
      processMap.put("finishOrderByRule", new finishOrderByRule());
      return processMap;
    }

    public static class createAsyncConsumeOrder<I extends Iface> extends org.apache.thrift.ProcessFunction<I, createAsyncConsumeOrder_args> {
      public createAsyncConsumeOrder() {
        super("createAsyncConsumeOrder");
      }

      public createAsyncConsumeOrder_args getEmptyArgsInstance() {
        return new createAsyncConsumeOrder_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public createAsyncConsumeOrder_result getResult(I iface, createAsyncConsumeOrder_args args) throws org.apache.thrift.TException {
        createAsyncConsumeOrder_result result = new createAsyncConsumeOrder_result();
        result.success = iface.createAsyncConsumeOrder(args.orderRequest, args.consumeRequests);
        return result;
      }
    }

    public static class createAsyncOrder<I extends Iface> extends org.apache.thrift.ProcessFunction<I, createAsyncOrder_args> {
      public createAsyncOrder() {
        super("createAsyncOrder");
      }

      public createAsyncOrder_args getEmptyArgsInstance() {
        return new createAsyncOrder_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public createAsyncOrder_result getResult(I iface, createAsyncOrder_args args) throws org.apache.thrift.TException {
        createAsyncOrder_result result = new createAsyncOrder_result();
        result.success = iface.createAsyncOrder(args.orderRequest);
        return result;
      }
    }

    public static class asyncDeliverOrderByRule<I extends Iface> extends org.apache.thrift.ProcessFunction<I, asyncDeliverOrderByRule_args> {
      public asyncDeliverOrderByRule() {
        super("asyncDeliverOrderByRule");
      }

      public asyncDeliverOrderByRule_args getEmptyArgsInstance() {
        return new asyncDeliverOrderByRule_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public asyncDeliverOrderByRule_result getResult(I iface, asyncDeliverOrderByRule_args args) throws org.apache.thrift.TException {
        asyncDeliverOrderByRule_result result = new asyncDeliverOrderByRule_result();
        result.success = iface.asyncDeliverOrderByRule(args.orderRequest, args.ruleInfo);
        return result;
      }
    }

    public static class asyncConsumeDeliverOrderByRule<I extends Iface> extends org.apache.thrift.ProcessFunction<I, asyncConsumeDeliverOrderByRule_args> {
      public asyncConsumeDeliverOrderByRule() {
        super("asyncConsumeDeliverOrderByRule");
      }

      public asyncConsumeDeliverOrderByRule_args getEmptyArgsInstance() {
        return new asyncConsumeDeliverOrderByRule_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public asyncConsumeDeliverOrderByRule_result getResult(I iface, asyncConsumeDeliverOrderByRule_args args) throws org.apache.thrift.TException {
        asyncConsumeDeliverOrderByRule_result result = new asyncConsumeDeliverOrderByRule_result();
        result.success = iface.asyncConsumeDeliverOrderByRule(args.orderRequest, args.consumeRequests, args.ruleInfo);
        return result;
      }
    }

    public static class asyncConsumeDeliverOrder<I extends Iface> extends org.apache.thrift.ProcessFunction<I, asyncConsumeDeliverOrder_args> {
      public asyncConsumeDeliverOrder() {
        super("asyncConsumeDeliverOrder");
      }

      public asyncConsumeDeliverOrder_args getEmptyArgsInstance() {
        return new asyncConsumeDeliverOrder_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public asyncConsumeDeliverOrder_result getResult(I iface, asyncConsumeDeliverOrder_args args) throws org.apache.thrift.TException {
        asyncConsumeDeliverOrder_result result = new asyncConsumeDeliverOrder_result();
        result.success = iface.asyncConsumeDeliverOrder(args.orderRequest, args.consumeRequests, args.goodsList);
        return result;
      }
    }

    public static class asyncDeliverOrder<I extends Iface> extends org.apache.thrift.ProcessFunction<I, asyncDeliverOrder_args> {
      public asyncDeliverOrder() {
        super("asyncDeliverOrder");
      }

      public asyncDeliverOrder_args getEmptyArgsInstance() {
        return new asyncDeliverOrder_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public asyncDeliverOrder_result getResult(I iface, asyncDeliverOrder_args args) throws org.apache.thrift.TException {
        asyncDeliverOrder_result result = new asyncDeliverOrder_result();
        result.success = iface.asyncDeliverOrder(args.orderRequest, args.goodsList);
        return result;
      }
    }

    public static class finishOrder<I extends Iface> extends org.apache.thrift.ProcessFunction<I, finishOrder_args> {
      public finishOrder() {
        super("finishOrder");
      }

      public finishOrder_args getEmptyArgsInstance() {
        return new finishOrder_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public finishOrder_result getResult(I iface, finishOrder_args args) throws org.apache.thrift.TException {
        finishOrder_result result = new finishOrder_result();
        result.success = iface.finishOrder(args.orderRequest);
        return result;
      }
    }

    public static class finishOrderByRule<I extends Iface> extends org.apache.thrift.ProcessFunction<I, finishOrderByRule_args> {
      public finishOrderByRule() {
        super("finishOrderByRule");
      }

      public finishOrderByRule_args getEmptyArgsInstance() {
        return new finishOrderByRule_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public finishOrderByRule_result getResult(I iface, finishOrderByRule_args args) throws org.apache.thrift.TException {
        finishOrderByRule_result result = new finishOrderByRule_result();
        result.success = iface.finishOrderByRule(args.orderRequest, args.ruleInfo);
        return result;
      }
    }

  }

  public static class AsyncProcessor<I extends AsyncIface> extends org.apache.thrift.TBaseAsyncProcessor<I> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());
    public AsyncProcessor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.AsyncProcessFunction<I, ? extends org.apache.thrift.TBase, ?>>()));
    }

    protected AsyncProcessor(I iface, Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends AsyncIface> Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase,?>> getProcessMap(Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      processMap.put("createAsyncConsumeOrder", new createAsyncConsumeOrder());
      processMap.put("createAsyncOrder", new createAsyncOrder());
      processMap.put("asyncDeliverOrderByRule", new asyncDeliverOrderByRule());
      processMap.put("asyncConsumeDeliverOrderByRule", new asyncConsumeDeliverOrderByRule());
      processMap.put("asyncConsumeDeliverOrder", new asyncConsumeDeliverOrder());
      processMap.put("asyncDeliverOrder", new asyncDeliverOrder());
      processMap.put("finishOrder", new finishOrder());
      processMap.put("finishOrderByRule", new finishOrderByRule());
      return processMap;
    }

    public static class createAsyncConsumeOrder<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, createAsyncConsumeOrder_args, OrderResponse> {
      public createAsyncConsumeOrder() {
        super("createAsyncConsumeOrder");
      }

      public createAsyncConsumeOrder_args getEmptyArgsInstance() {
        return new createAsyncConsumeOrder_args();
      }

      public AsyncMethodCallback<OrderResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<OrderResponse>() { 
          public void onComplete(OrderResponse o) {
            createAsyncConsumeOrder_result result = new createAsyncConsumeOrder_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            createAsyncConsumeOrder_result result = new createAsyncConsumeOrder_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, createAsyncConsumeOrder_args args, org.apache.thrift.async.AsyncMethodCallback<OrderResponse> resultHandler) throws TException {
        iface.createAsyncConsumeOrder(args.orderRequest, args.consumeRequests,resultHandler);
      }
    }

    public static class createAsyncOrder<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, createAsyncOrder_args, OrderResponse> {
      public createAsyncOrder() {
        super("createAsyncOrder");
      }

      public createAsyncOrder_args getEmptyArgsInstance() {
        return new createAsyncOrder_args();
      }

      public AsyncMethodCallback<OrderResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<OrderResponse>() { 
          public void onComplete(OrderResponse o) {
            createAsyncOrder_result result = new createAsyncOrder_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            createAsyncOrder_result result = new createAsyncOrder_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, createAsyncOrder_args args, org.apache.thrift.async.AsyncMethodCallback<OrderResponse> resultHandler) throws TException {
        iface.createAsyncOrder(args.orderRequest,resultHandler);
      }
    }

    public static class asyncDeliverOrderByRule<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, asyncDeliverOrderByRule_args, OrderResponse> {
      public asyncDeliverOrderByRule() {
        super("asyncDeliverOrderByRule");
      }

      public asyncDeliverOrderByRule_args getEmptyArgsInstance() {
        return new asyncDeliverOrderByRule_args();
      }

      public AsyncMethodCallback<OrderResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<OrderResponse>() { 
          public void onComplete(OrderResponse o) {
            asyncDeliverOrderByRule_result result = new asyncDeliverOrderByRule_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            asyncDeliverOrderByRule_result result = new asyncDeliverOrderByRule_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, asyncDeliverOrderByRule_args args, org.apache.thrift.async.AsyncMethodCallback<OrderResponse> resultHandler) throws TException {
        iface.asyncDeliverOrderByRule(args.orderRequest, args.ruleInfo,resultHandler);
      }
    }

    public static class asyncConsumeDeliverOrderByRule<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, asyncConsumeDeliverOrderByRule_args, OrderResponse> {
      public asyncConsumeDeliverOrderByRule() {
        super("asyncConsumeDeliverOrderByRule");
      }

      public asyncConsumeDeliverOrderByRule_args getEmptyArgsInstance() {
        return new asyncConsumeDeliverOrderByRule_args();
      }

      public AsyncMethodCallback<OrderResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<OrderResponse>() { 
          public void onComplete(OrderResponse o) {
            asyncConsumeDeliverOrderByRule_result result = new asyncConsumeDeliverOrderByRule_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            asyncConsumeDeliverOrderByRule_result result = new asyncConsumeDeliverOrderByRule_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, asyncConsumeDeliverOrderByRule_args args, org.apache.thrift.async.AsyncMethodCallback<OrderResponse> resultHandler) throws TException {
        iface.asyncConsumeDeliverOrderByRule(args.orderRequest, args.consumeRequests, args.ruleInfo,resultHandler);
      }
    }

    public static class asyncConsumeDeliverOrder<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, asyncConsumeDeliverOrder_args, OrderResponse> {
      public asyncConsumeDeliverOrder() {
        super("asyncConsumeDeliverOrder");
      }

      public asyncConsumeDeliverOrder_args getEmptyArgsInstance() {
        return new asyncConsumeDeliverOrder_args();
      }

      public AsyncMethodCallback<OrderResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<OrderResponse>() { 
          public void onComplete(OrderResponse o) {
            asyncConsumeDeliverOrder_result result = new asyncConsumeDeliverOrder_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            asyncConsumeDeliverOrder_result result = new asyncConsumeDeliverOrder_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, asyncConsumeDeliverOrder_args args, org.apache.thrift.async.AsyncMethodCallback<OrderResponse> resultHandler) throws TException {
        iface.asyncConsumeDeliverOrder(args.orderRequest, args.consumeRequests, args.goodsList,resultHandler);
      }
    }

    public static class asyncDeliverOrder<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, asyncDeliverOrder_args, OrderResponse> {
      public asyncDeliverOrder() {
        super("asyncDeliverOrder");
      }

      public asyncDeliverOrder_args getEmptyArgsInstance() {
        return new asyncDeliverOrder_args();
      }

      public AsyncMethodCallback<OrderResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<OrderResponse>() { 
          public void onComplete(OrderResponse o) {
            asyncDeliverOrder_result result = new asyncDeliverOrder_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            asyncDeliverOrder_result result = new asyncDeliverOrder_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, asyncDeliverOrder_args args, org.apache.thrift.async.AsyncMethodCallback<OrderResponse> resultHandler) throws TException {
        iface.asyncDeliverOrder(args.orderRequest, args.goodsList,resultHandler);
      }
    }

    public static class finishOrder<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, finishOrder_args, OrderResponse> {
      public finishOrder() {
        super("finishOrder");
      }

      public finishOrder_args getEmptyArgsInstance() {
        return new finishOrder_args();
      }

      public AsyncMethodCallback<OrderResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<OrderResponse>() { 
          public void onComplete(OrderResponse o) {
            finishOrder_result result = new finishOrder_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            finishOrder_result result = new finishOrder_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, finishOrder_args args, org.apache.thrift.async.AsyncMethodCallback<OrderResponse> resultHandler) throws TException {
        iface.finishOrder(args.orderRequest,resultHandler);
      }
    }

    public static class finishOrderByRule<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, finishOrderByRule_args, OrderResponse> {
      public finishOrderByRule() {
        super("finishOrderByRule");
      }

      public finishOrderByRule_args getEmptyArgsInstance() {
        return new finishOrderByRule_args();
      }

      public AsyncMethodCallback<OrderResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<OrderResponse>() { 
          public void onComplete(OrderResponse o) {
            finishOrderByRule_result result = new finishOrderByRule_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            finishOrderByRule_result result = new finishOrderByRule_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, finishOrderByRule_args args, org.apache.thrift.async.AsyncMethodCallback<OrderResponse> resultHandler) throws TException {
        iface.finishOrderByRule(args.orderRequest, args.ruleInfo,resultHandler);
      }
    }

  }

  public static class createAsyncConsumeOrder_args implements org.apache.thrift.TBase<createAsyncConsumeOrder_args, createAsyncConsumeOrder_args._Fields>, java.io.Serializable, Cloneable, Comparable<createAsyncConsumeOrder_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("createAsyncConsumeOrder_args");

    private static final org.apache.thrift.protocol.TField ORDER_REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("orderRequest", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField CONSUME_REQUESTS_FIELD_DESC = new org.apache.thrift.protocol.TField("consumeRequests", org.apache.thrift.protocol.TType.LIST, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new createAsyncConsumeOrder_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new createAsyncConsumeOrder_argsTupleSchemeFactory());
    }

    public OrderRequest orderRequest; // required
    public List<ConsumeRequest> consumeRequests; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      ORDER_REQUEST((short)1, "orderRequest"),
      CONSUME_REQUESTS((short)2, "consumeRequests");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // ORDER_REQUEST
            return ORDER_REQUEST;
          case 2: // CONSUME_REQUESTS
            return CONSUME_REQUESTS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.ORDER_REQUEST, new org.apache.thrift.meta_data.FieldMetaData("orderRequest", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderRequest.class)));
      tmpMap.put(_Fields.CONSUME_REQUESTS, new org.apache.thrift.meta_data.FieldMetaData("consumeRequests", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
              new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeRequest.class))));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(createAsyncConsumeOrder_args.class, metaDataMap);
    }

    public createAsyncConsumeOrder_args() {
    }

    public createAsyncConsumeOrder_args(
      OrderRequest orderRequest,
      List<ConsumeRequest> consumeRequests)
    {
      this();
      this.orderRequest = orderRequest;
      this.consumeRequests = consumeRequests;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public createAsyncConsumeOrder_args(createAsyncConsumeOrder_args other) {
      if (other.isSetOrderRequest()) {
        this.orderRequest = new OrderRequest(other.orderRequest);
      }
      if (other.isSetConsumeRequests()) {
        List<ConsumeRequest> __this__consumeRequests = new ArrayList<ConsumeRequest>(other.consumeRequests.size());
        for (ConsumeRequest other_element : other.consumeRequests) {
          __this__consumeRequests.add(new ConsumeRequest(other_element));
        }
        this.consumeRequests = __this__consumeRequests;
      }
    }

    public createAsyncConsumeOrder_args deepCopy() {
      return new createAsyncConsumeOrder_args(this);
    }

    @Override
    public void clear() {
      this.orderRequest = null;
      this.consumeRequests = null;
    }

    public OrderRequest getOrderRequest() {
      return this.orderRequest;
    }

    public createAsyncConsumeOrder_args setOrderRequest(OrderRequest orderRequest) {
      this.orderRequest = orderRequest;
      return this;
    }

    public void unsetOrderRequest() {
      this.orderRequest = null;
    }

    /** Returns true if field orderRequest is set (has been assigned a value) and false otherwise */
    public boolean isSetOrderRequest() {
      return this.orderRequest != null;
    }

    public void setOrderRequestIsSet(boolean value) {
      if (!value) {
        this.orderRequest = null;
      }
    }

    public int getConsumeRequestsSize() {
      return (this.consumeRequests == null) ? 0 : this.consumeRequests.size();
    }

    public java.util.Iterator<ConsumeRequest> getConsumeRequestsIterator() {
      return (this.consumeRequests == null) ? null : this.consumeRequests.iterator();
    }

    public void addToConsumeRequests(ConsumeRequest elem) {
      if (this.consumeRequests == null) {
        this.consumeRequests = new ArrayList<ConsumeRequest>();
      }
      this.consumeRequests.add(elem);
    }

    public List<ConsumeRequest> getConsumeRequests() {
      return this.consumeRequests;
    }

    public createAsyncConsumeOrder_args setConsumeRequests(List<ConsumeRequest> consumeRequests) {
      this.consumeRequests = consumeRequests;
      return this;
    }

    public void unsetConsumeRequests() {
      this.consumeRequests = null;
    }

    /** Returns true if field consumeRequests is set (has been assigned a value) and false otherwise */
    public boolean isSetConsumeRequests() {
      return this.consumeRequests != null;
    }

    public void setConsumeRequestsIsSet(boolean value) {
      if (!value) {
        this.consumeRequests = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case ORDER_REQUEST:
        if (value == null) {
          unsetOrderRequest();
        } else {
          setOrderRequest((OrderRequest)value);
        }
        break;

      case CONSUME_REQUESTS:
        if (value == null) {
          unsetConsumeRequests();
        } else {
          setConsumeRequests((List<ConsumeRequest>)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case ORDER_REQUEST:
        return getOrderRequest();

      case CONSUME_REQUESTS:
        return getConsumeRequests();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case ORDER_REQUEST:
        return isSetOrderRequest();
      case CONSUME_REQUESTS:
        return isSetConsumeRequests();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof createAsyncConsumeOrder_args)
        return this.equals((createAsyncConsumeOrder_args)that);
      return false;
    }

    public boolean equals(createAsyncConsumeOrder_args that) {
      if (that == null)
        return false;

      boolean this_present_orderRequest = true && this.isSetOrderRequest();
      boolean that_present_orderRequest = true && that.isSetOrderRequest();
      if (this_present_orderRequest || that_present_orderRequest) {
        if (!(this_present_orderRequest && that_present_orderRequest))
          return false;
        if (!this.orderRequest.equals(that.orderRequest))
          return false;
      }

      boolean this_present_consumeRequests = true && this.isSetConsumeRequests();
      boolean that_present_consumeRequests = true && that.isSetConsumeRequests();
      if (this_present_consumeRequests || that_present_consumeRequests) {
        if (!(this_present_consumeRequests && that_present_consumeRequests))
          return false;
        if (!this.consumeRequests.equals(that.consumeRequests))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_orderRequest = true && (isSetOrderRequest());
      list.add(present_orderRequest);
      if (present_orderRequest)
        list.add(orderRequest);

      boolean present_consumeRequests = true && (isSetConsumeRequests());
      list.add(present_consumeRequests);
      if (present_consumeRequests)
        list.add(consumeRequests);

      return list.hashCode();
    }

    @Override
    public int compareTo(createAsyncConsumeOrder_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetOrderRequest()).compareTo(other.isSetOrderRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetOrderRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderRequest, other.orderRequest);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetConsumeRequests()).compareTo(other.isSetConsumeRequests());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetConsumeRequests()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.consumeRequests, other.consumeRequests);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("createAsyncConsumeOrder_args(");
      boolean first = true;

      sb.append("orderRequest:");
      if (this.orderRequest == null) {
        sb.append("null");
      } else {
        sb.append(this.orderRequest);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("consumeRequests:");
      if (this.consumeRequests == null) {
        sb.append("null");
      } else {
        sb.append(this.consumeRequests);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (orderRequest == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderRequest' was not present! Struct: " + toString());
      }
      if (consumeRequests == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'consumeRequests' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (orderRequest != null) {
        orderRequest.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class createAsyncConsumeOrder_argsStandardSchemeFactory implements SchemeFactory {
      public createAsyncConsumeOrder_argsStandardScheme getScheme() {
        return new createAsyncConsumeOrder_argsStandardScheme();
      }
    }

    private static class createAsyncConsumeOrder_argsStandardScheme extends StandardScheme<createAsyncConsumeOrder_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, createAsyncConsumeOrder_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // ORDER_REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.orderRequest = new OrderRequest();
                struct.orderRequest.read(iprot);
                struct.setOrderRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // CONSUME_REQUESTS
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list64 = iprot.readListBegin();
                  struct.consumeRequests = new ArrayList<ConsumeRequest>(_list64.size);
                  ConsumeRequest _elem65;
                  for (int _i66 = 0; _i66 < _list64.size; ++_i66)
                  {
                    _elem65 = new ConsumeRequest();
                    _elem65.read(iprot);
                    struct.consumeRequests.add(_elem65);
                  }
                  iprot.readListEnd();
                }
                struct.setConsumeRequestsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, createAsyncConsumeOrder_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.orderRequest != null) {
          oprot.writeFieldBegin(ORDER_REQUEST_FIELD_DESC);
          struct.orderRequest.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.consumeRequests != null) {
          oprot.writeFieldBegin(CONSUME_REQUESTS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.consumeRequests.size()));
            for (ConsumeRequest _iter67 : struct.consumeRequests)
            {
              _iter67.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class createAsyncConsumeOrder_argsTupleSchemeFactory implements SchemeFactory {
      public createAsyncConsumeOrder_argsTupleScheme getScheme() {
        return new createAsyncConsumeOrder_argsTupleScheme();
      }
    }

    private static class createAsyncConsumeOrder_argsTupleScheme extends TupleScheme<createAsyncConsumeOrder_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, createAsyncConsumeOrder_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.orderRequest.write(oprot);
        {
          oprot.writeI32(struct.consumeRequests.size());
          for (ConsumeRequest _iter68 : struct.consumeRequests)
          {
            _iter68.write(oprot);
          }
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, createAsyncConsumeOrder_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.orderRequest = new OrderRequest();
        struct.orderRequest.read(iprot);
        struct.setOrderRequestIsSet(true);
        {
          org.apache.thrift.protocol.TList _list69 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.consumeRequests = new ArrayList<ConsumeRequest>(_list69.size);
          ConsumeRequest _elem70;
          for (int _i71 = 0; _i71 < _list69.size; ++_i71)
          {
            _elem70 = new ConsumeRequest();
            _elem70.read(iprot);
            struct.consumeRequests.add(_elem70);
          }
        }
        struct.setConsumeRequestsIsSet(true);
      }
    }

  }

  public static class createAsyncConsumeOrder_result implements org.apache.thrift.TBase<createAsyncConsumeOrder_result, createAsyncConsumeOrder_result._Fields>, java.io.Serializable, Cloneable, Comparable<createAsyncConsumeOrder_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("createAsyncConsumeOrder_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new createAsyncConsumeOrder_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new createAsyncConsumeOrder_resultTupleSchemeFactory());
    }

    public OrderResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(createAsyncConsumeOrder_result.class, metaDataMap);
    }

    public createAsyncConsumeOrder_result() {
    }

    public createAsyncConsumeOrder_result(
      OrderResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public createAsyncConsumeOrder_result(createAsyncConsumeOrder_result other) {
      if (other.isSetSuccess()) {
        this.success = new OrderResponse(other.success);
      }
    }

    public createAsyncConsumeOrder_result deepCopy() {
      return new createAsyncConsumeOrder_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public OrderResponse getSuccess() {
      return this.success;
    }

    public createAsyncConsumeOrder_result setSuccess(OrderResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((OrderResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof createAsyncConsumeOrder_result)
        return this.equals((createAsyncConsumeOrder_result)that);
      return false;
    }

    public boolean equals(createAsyncConsumeOrder_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(createAsyncConsumeOrder_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("createAsyncConsumeOrder_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class createAsyncConsumeOrder_resultStandardSchemeFactory implements SchemeFactory {
      public createAsyncConsumeOrder_resultStandardScheme getScheme() {
        return new createAsyncConsumeOrder_resultStandardScheme();
      }
    }

    private static class createAsyncConsumeOrder_resultStandardScheme extends StandardScheme<createAsyncConsumeOrder_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, createAsyncConsumeOrder_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new OrderResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, createAsyncConsumeOrder_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class createAsyncConsumeOrder_resultTupleSchemeFactory implements SchemeFactory {
      public createAsyncConsumeOrder_resultTupleScheme getScheme() {
        return new createAsyncConsumeOrder_resultTupleScheme();
      }
    }

    private static class createAsyncConsumeOrder_resultTupleScheme extends TupleScheme<createAsyncConsumeOrder_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, createAsyncConsumeOrder_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, createAsyncConsumeOrder_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new OrderResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class createAsyncOrder_args implements org.apache.thrift.TBase<createAsyncOrder_args, createAsyncOrder_args._Fields>, java.io.Serializable, Cloneable, Comparable<createAsyncOrder_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("createAsyncOrder_args");

    private static final org.apache.thrift.protocol.TField ORDER_REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("orderRequest", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new createAsyncOrder_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new createAsyncOrder_argsTupleSchemeFactory());
    }

    public OrderRequest orderRequest; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      ORDER_REQUEST((short)1, "orderRequest");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // ORDER_REQUEST
            return ORDER_REQUEST;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.ORDER_REQUEST, new org.apache.thrift.meta_data.FieldMetaData("orderRequest", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderRequest.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(createAsyncOrder_args.class, metaDataMap);
    }

    public createAsyncOrder_args() {
    }

    public createAsyncOrder_args(
      OrderRequest orderRequest)
    {
      this();
      this.orderRequest = orderRequest;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public createAsyncOrder_args(createAsyncOrder_args other) {
      if (other.isSetOrderRequest()) {
        this.orderRequest = new OrderRequest(other.orderRequest);
      }
    }

    public createAsyncOrder_args deepCopy() {
      return new createAsyncOrder_args(this);
    }

    @Override
    public void clear() {
      this.orderRequest = null;
    }

    public OrderRequest getOrderRequest() {
      return this.orderRequest;
    }

    public createAsyncOrder_args setOrderRequest(OrderRequest orderRequest) {
      this.orderRequest = orderRequest;
      return this;
    }

    public void unsetOrderRequest() {
      this.orderRequest = null;
    }

    /** Returns true if field orderRequest is set (has been assigned a value) and false otherwise */
    public boolean isSetOrderRequest() {
      return this.orderRequest != null;
    }

    public void setOrderRequestIsSet(boolean value) {
      if (!value) {
        this.orderRequest = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case ORDER_REQUEST:
        if (value == null) {
          unsetOrderRequest();
        } else {
          setOrderRequest((OrderRequest)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case ORDER_REQUEST:
        return getOrderRequest();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case ORDER_REQUEST:
        return isSetOrderRequest();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof createAsyncOrder_args)
        return this.equals((createAsyncOrder_args)that);
      return false;
    }

    public boolean equals(createAsyncOrder_args that) {
      if (that == null)
        return false;

      boolean this_present_orderRequest = true && this.isSetOrderRequest();
      boolean that_present_orderRequest = true && that.isSetOrderRequest();
      if (this_present_orderRequest || that_present_orderRequest) {
        if (!(this_present_orderRequest && that_present_orderRequest))
          return false;
        if (!this.orderRequest.equals(that.orderRequest))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_orderRequest = true && (isSetOrderRequest());
      list.add(present_orderRequest);
      if (present_orderRequest)
        list.add(orderRequest);

      return list.hashCode();
    }

    @Override
    public int compareTo(createAsyncOrder_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetOrderRequest()).compareTo(other.isSetOrderRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetOrderRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderRequest, other.orderRequest);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("createAsyncOrder_args(");
      boolean first = true;

      sb.append("orderRequest:");
      if (this.orderRequest == null) {
        sb.append("null");
      } else {
        sb.append(this.orderRequest);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (orderRequest == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderRequest' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (orderRequest != null) {
        orderRequest.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class createAsyncOrder_argsStandardSchemeFactory implements SchemeFactory {
      public createAsyncOrder_argsStandardScheme getScheme() {
        return new createAsyncOrder_argsStandardScheme();
      }
    }

    private static class createAsyncOrder_argsStandardScheme extends StandardScheme<createAsyncOrder_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, createAsyncOrder_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // ORDER_REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.orderRequest = new OrderRequest();
                struct.orderRequest.read(iprot);
                struct.setOrderRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, createAsyncOrder_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.orderRequest != null) {
          oprot.writeFieldBegin(ORDER_REQUEST_FIELD_DESC);
          struct.orderRequest.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class createAsyncOrder_argsTupleSchemeFactory implements SchemeFactory {
      public createAsyncOrder_argsTupleScheme getScheme() {
        return new createAsyncOrder_argsTupleScheme();
      }
    }

    private static class createAsyncOrder_argsTupleScheme extends TupleScheme<createAsyncOrder_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, createAsyncOrder_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.orderRequest.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, createAsyncOrder_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.orderRequest = new OrderRequest();
        struct.orderRequest.read(iprot);
        struct.setOrderRequestIsSet(true);
      }
    }

  }

  public static class createAsyncOrder_result implements org.apache.thrift.TBase<createAsyncOrder_result, createAsyncOrder_result._Fields>, java.io.Serializable, Cloneable, Comparable<createAsyncOrder_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("createAsyncOrder_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new createAsyncOrder_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new createAsyncOrder_resultTupleSchemeFactory());
    }

    public OrderResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(createAsyncOrder_result.class, metaDataMap);
    }

    public createAsyncOrder_result() {
    }

    public createAsyncOrder_result(
      OrderResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public createAsyncOrder_result(createAsyncOrder_result other) {
      if (other.isSetSuccess()) {
        this.success = new OrderResponse(other.success);
      }
    }

    public createAsyncOrder_result deepCopy() {
      return new createAsyncOrder_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public OrderResponse getSuccess() {
      return this.success;
    }

    public createAsyncOrder_result setSuccess(OrderResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((OrderResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof createAsyncOrder_result)
        return this.equals((createAsyncOrder_result)that);
      return false;
    }

    public boolean equals(createAsyncOrder_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(createAsyncOrder_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("createAsyncOrder_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class createAsyncOrder_resultStandardSchemeFactory implements SchemeFactory {
      public createAsyncOrder_resultStandardScheme getScheme() {
        return new createAsyncOrder_resultStandardScheme();
      }
    }

    private static class createAsyncOrder_resultStandardScheme extends StandardScheme<createAsyncOrder_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, createAsyncOrder_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new OrderResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, createAsyncOrder_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class createAsyncOrder_resultTupleSchemeFactory implements SchemeFactory {
      public createAsyncOrder_resultTupleScheme getScheme() {
        return new createAsyncOrder_resultTupleScheme();
      }
    }

    private static class createAsyncOrder_resultTupleScheme extends TupleScheme<createAsyncOrder_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, createAsyncOrder_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, createAsyncOrder_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new OrderResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class asyncDeliverOrderByRule_args implements org.apache.thrift.TBase<asyncDeliverOrderByRule_args, asyncDeliverOrderByRule_args._Fields>, java.io.Serializable, Cloneable, Comparable<asyncDeliverOrderByRule_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("asyncDeliverOrderByRule_args");

    private static final org.apache.thrift.protocol.TField ORDER_REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("orderRequest", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField RULE_INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("ruleInfo", org.apache.thrift.protocol.TType.STRUCT, (short)3);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new asyncDeliverOrderByRule_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new asyncDeliverOrderByRule_argsTupleSchemeFactory());
    }

    public OrderRequest orderRequest; // required
    public RuleInfo ruleInfo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      ORDER_REQUEST((short)1, "orderRequest"),
      RULE_INFO((short)3, "ruleInfo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // ORDER_REQUEST
            return ORDER_REQUEST;
          case 3: // RULE_INFO
            return RULE_INFO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.ORDER_REQUEST, new org.apache.thrift.meta_data.FieldMetaData("orderRequest", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderRequest.class)));
      tmpMap.put(_Fields.RULE_INFO, new org.apache.thrift.meta_data.FieldMetaData("ruleInfo", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, RuleInfo.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(asyncDeliverOrderByRule_args.class, metaDataMap);
    }

    public asyncDeliverOrderByRule_args() {
    }

    public asyncDeliverOrderByRule_args(
      OrderRequest orderRequest,
      RuleInfo ruleInfo)
    {
      this();
      this.orderRequest = orderRequest;
      this.ruleInfo = ruleInfo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public asyncDeliverOrderByRule_args(asyncDeliverOrderByRule_args other) {
      if (other.isSetOrderRequest()) {
        this.orderRequest = new OrderRequest(other.orderRequest);
      }
      if (other.isSetRuleInfo()) {
        this.ruleInfo = new RuleInfo(other.ruleInfo);
      }
    }

    public asyncDeliverOrderByRule_args deepCopy() {
      return new asyncDeliverOrderByRule_args(this);
    }

    @Override
    public void clear() {
      this.orderRequest = null;
      this.ruleInfo = null;
    }

    public OrderRequest getOrderRequest() {
      return this.orderRequest;
    }

    public asyncDeliverOrderByRule_args setOrderRequest(OrderRequest orderRequest) {
      this.orderRequest = orderRequest;
      return this;
    }

    public void unsetOrderRequest() {
      this.orderRequest = null;
    }

    /** Returns true if field orderRequest is set (has been assigned a value) and false otherwise */
    public boolean isSetOrderRequest() {
      return this.orderRequest != null;
    }

    public void setOrderRequestIsSet(boolean value) {
      if (!value) {
        this.orderRequest = null;
      }
    }

    public RuleInfo getRuleInfo() {
      return this.ruleInfo;
    }

    public asyncDeliverOrderByRule_args setRuleInfo(RuleInfo ruleInfo) {
      this.ruleInfo = ruleInfo;
      return this;
    }

    public void unsetRuleInfo() {
      this.ruleInfo = null;
    }

    /** Returns true if field ruleInfo is set (has been assigned a value) and false otherwise */
    public boolean isSetRuleInfo() {
      return this.ruleInfo != null;
    }

    public void setRuleInfoIsSet(boolean value) {
      if (!value) {
        this.ruleInfo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case ORDER_REQUEST:
        if (value == null) {
          unsetOrderRequest();
        } else {
          setOrderRequest((OrderRequest)value);
        }
        break;

      case RULE_INFO:
        if (value == null) {
          unsetRuleInfo();
        } else {
          setRuleInfo((RuleInfo)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case ORDER_REQUEST:
        return getOrderRequest();

      case RULE_INFO:
        return getRuleInfo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case ORDER_REQUEST:
        return isSetOrderRequest();
      case RULE_INFO:
        return isSetRuleInfo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof asyncDeliverOrderByRule_args)
        return this.equals((asyncDeliverOrderByRule_args)that);
      return false;
    }

    public boolean equals(asyncDeliverOrderByRule_args that) {
      if (that == null)
        return false;

      boolean this_present_orderRequest = true && this.isSetOrderRequest();
      boolean that_present_orderRequest = true && that.isSetOrderRequest();
      if (this_present_orderRequest || that_present_orderRequest) {
        if (!(this_present_orderRequest && that_present_orderRequest))
          return false;
        if (!this.orderRequest.equals(that.orderRequest))
          return false;
      }

      boolean this_present_ruleInfo = true && this.isSetRuleInfo();
      boolean that_present_ruleInfo = true && that.isSetRuleInfo();
      if (this_present_ruleInfo || that_present_ruleInfo) {
        if (!(this_present_ruleInfo && that_present_ruleInfo))
          return false;
        if (!this.ruleInfo.equals(that.ruleInfo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_orderRequest = true && (isSetOrderRequest());
      list.add(present_orderRequest);
      if (present_orderRequest)
        list.add(orderRequest);

      boolean present_ruleInfo = true && (isSetRuleInfo());
      list.add(present_ruleInfo);
      if (present_ruleInfo)
        list.add(ruleInfo);

      return list.hashCode();
    }

    @Override
    public int compareTo(asyncDeliverOrderByRule_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetOrderRequest()).compareTo(other.isSetOrderRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetOrderRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderRequest, other.orderRequest);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetRuleInfo()).compareTo(other.isSetRuleInfo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRuleInfo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ruleInfo, other.ruleInfo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("asyncDeliverOrderByRule_args(");
      boolean first = true;

      sb.append("orderRequest:");
      if (this.orderRequest == null) {
        sb.append("null");
      } else {
        sb.append(this.orderRequest);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("ruleInfo:");
      if (this.ruleInfo == null) {
        sb.append("null");
      } else {
        sb.append(this.ruleInfo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (orderRequest == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderRequest' was not present! Struct: " + toString());
      }
      if (ruleInfo == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'ruleInfo' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (orderRequest != null) {
        orderRequest.validate();
      }
      if (ruleInfo != null) {
        ruleInfo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class asyncDeliverOrderByRule_argsStandardSchemeFactory implements SchemeFactory {
      public asyncDeliverOrderByRule_argsStandardScheme getScheme() {
        return new asyncDeliverOrderByRule_argsStandardScheme();
      }
    }

    private static class asyncDeliverOrderByRule_argsStandardScheme extends StandardScheme<asyncDeliverOrderByRule_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, asyncDeliverOrderByRule_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // ORDER_REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.orderRequest = new OrderRequest();
                struct.orderRequest.read(iprot);
                struct.setOrderRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // RULE_INFO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.ruleInfo = new RuleInfo();
                struct.ruleInfo.read(iprot);
                struct.setRuleInfoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, asyncDeliverOrderByRule_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.orderRequest != null) {
          oprot.writeFieldBegin(ORDER_REQUEST_FIELD_DESC);
          struct.orderRequest.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.ruleInfo != null) {
          oprot.writeFieldBegin(RULE_INFO_FIELD_DESC);
          struct.ruleInfo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class asyncDeliverOrderByRule_argsTupleSchemeFactory implements SchemeFactory {
      public asyncDeliverOrderByRule_argsTupleScheme getScheme() {
        return new asyncDeliverOrderByRule_argsTupleScheme();
      }
    }

    private static class asyncDeliverOrderByRule_argsTupleScheme extends TupleScheme<asyncDeliverOrderByRule_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, asyncDeliverOrderByRule_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.orderRequest.write(oprot);
        struct.ruleInfo.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, asyncDeliverOrderByRule_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.orderRequest = new OrderRequest();
        struct.orderRequest.read(iprot);
        struct.setOrderRequestIsSet(true);
        struct.ruleInfo = new RuleInfo();
        struct.ruleInfo.read(iprot);
        struct.setRuleInfoIsSet(true);
      }
    }

  }

  public static class asyncDeliverOrderByRule_result implements org.apache.thrift.TBase<asyncDeliverOrderByRule_result, asyncDeliverOrderByRule_result._Fields>, java.io.Serializable, Cloneable, Comparable<asyncDeliverOrderByRule_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("asyncDeliverOrderByRule_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new asyncDeliverOrderByRule_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new asyncDeliverOrderByRule_resultTupleSchemeFactory());
    }

    public OrderResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(asyncDeliverOrderByRule_result.class, metaDataMap);
    }

    public asyncDeliverOrderByRule_result() {
    }

    public asyncDeliverOrderByRule_result(
      OrderResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public asyncDeliverOrderByRule_result(asyncDeliverOrderByRule_result other) {
      if (other.isSetSuccess()) {
        this.success = new OrderResponse(other.success);
      }
    }

    public asyncDeliverOrderByRule_result deepCopy() {
      return new asyncDeliverOrderByRule_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public OrderResponse getSuccess() {
      return this.success;
    }

    public asyncDeliverOrderByRule_result setSuccess(OrderResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((OrderResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof asyncDeliverOrderByRule_result)
        return this.equals((asyncDeliverOrderByRule_result)that);
      return false;
    }

    public boolean equals(asyncDeliverOrderByRule_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(asyncDeliverOrderByRule_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("asyncDeliverOrderByRule_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class asyncDeliverOrderByRule_resultStandardSchemeFactory implements SchemeFactory {
      public asyncDeliverOrderByRule_resultStandardScheme getScheme() {
        return new asyncDeliverOrderByRule_resultStandardScheme();
      }
    }

    private static class asyncDeliverOrderByRule_resultStandardScheme extends StandardScheme<asyncDeliverOrderByRule_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, asyncDeliverOrderByRule_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new OrderResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, asyncDeliverOrderByRule_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class asyncDeliverOrderByRule_resultTupleSchemeFactory implements SchemeFactory {
      public asyncDeliverOrderByRule_resultTupleScheme getScheme() {
        return new asyncDeliverOrderByRule_resultTupleScheme();
      }
    }

    private static class asyncDeliverOrderByRule_resultTupleScheme extends TupleScheme<asyncDeliverOrderByRule_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, asyncDeliverOrderByRule_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, asyncDeliverOrderByRule_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new OrderResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class asyncConsumeDeliverOrderByRule_args implements org.apache.thrift.TBase<asyncConsumeDeliverOrderByRule_args, asyncConsumeDeliverOrderByRule_args._Fields>, java.io.Serializable, Cloneable, Comparable<asyncConsumeDeliverOrderByRule_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("asyncConsumeDeliverOrderByRule_args");

    private static final org.apache.thrift.protocol.TField ORDER_REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("orderRequest", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField CONSUME_REQUESTS_FIELD_DESC = new org.apache.thrift.protocol.TField("consumeRequests", org.apache.thrift.protocol.TType.LIST, (short)2);
    private static final org.apache.thrift.protocol.TField RULE_INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("ruleInfo", org.apache.thrift.protocol.TType.STRUCT, (short)3);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new asyncConsumeDeliverOrderByRule_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new asyncConsumeDeliverOrderByRule_argsTupleSchemeFactory());
    }

    public OrderRequest orderRequest; // required
    public List<ConsumeRequest> consumeRequests; // required
    public RuleInfo ruleInfo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      ORDER_REQUEST((short)1, "orderRequest"),
      CONSUME_REQUESTS((short)2, "consumeRequests"),
      RULE_INFO((short)3, "ruleInfo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // ORDER_REQUEST
            return ORDER_REQUEST;
          case 2: // CONSUME_REQUESTS
            return CONSUME_REQUESTS;
          case 3: // RULE_INFO
            return RULE_INFO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.ORDER_REQUEST, new org.apache.thrift.meta_data.FieldMetaData("orderRequest", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderRequest.class)));
      tmpMap.put(_Fields.CONSUME_REQUESTS, new org.apache.thrift.meta_data.FieldMetaData("consumeRequests", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
              new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeRequest.class))));
      tmpMap.put(_Fields.RULE_INFO, new org.apache.thrift.meta_data.FieldMetaData("ruleInfo", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, RuleInfo.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(asyncConsumeDeliverOrderByRule_args.class, metaDataMap);
    }

    public asyncConsumeDeliverOrderByRule_args() {
    }

    public asyncConsumeDeliverOrderByRule_args(
      OrderRequest orderRequest,
      List<ConsumeRequest> consumeRequests,
      RuleInfo ruleInfo)
    {
      this();
      this.orderRequest = orderRequest;
      this.consumeRequests = consumeRequests;
      this.ruleInfo = ruleInfo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public asyncConsumeDeliverOrderByRule_args(asyncConsumeDeliverOrderByRule_args other) {
      if (other.isSetOrderRequest()) {
        this.orderRequest = new OrderRequest(other.orderRequest);
      }
      if (other.isSetConsumeRequests()) {
        List<ConsumeRequest> __this__consumeRequests = new ArrayList<ConsumeRequest>(other.consumeRequests.size());
        for (ConsumeRequest other_element : other.consumeRequests) {
          __this__consumeRequests.add(new ConsumeRequest(other_element));
        }
        this.consumeRequests = __this__consumeRequests;
      }
      if (other.isSetRuleInfo()) {
        this.ruleInfo = new RuleInfo(other.ruleInfo);
      }
    }

    public asyncConsumeDeliverOrderByRule_args deepCopy() {
      return new asyncConsumeDeliverOrderByRule_args(this);
    }

    @Override
    public void clear() {
      this.orderRequest = null;
      this.consumeRequests = null;
      this.ruleInfo = null;
    }

    public OrderRequest getOrderRequest() {
      return this.orderRequest;
    }

    public asyncConsumeDeliverOrderByRule_args setOrderRequest(OrderRequest orderRequest) {
      this.orderRequest = orderRequest;
      return this;
    }

    public void unsetOrderRequest() {
      this.orderRequest = null;
    }

    /** Returns true if field orderRequest is set (has been assigned a value) and false otherwise */
    public boolean isSetOrderRequest() {
      return this.orderRequest != null;
    }

    public void setOrderRequestIsSet(boolean value) {
      if (!value) {
        this.orderRequest = null;
      }
    }

    public int getConsumeRequestsSize() {
      return (this.consumeRequests == null) ? 0 : this.consumeRequests.size();
    }

    public java.util.Iterator<ConsumeRequest> getConsumeRequestsIterator() {
      return (this.consumeRequests == null) ? null : this.consumeRequests.iterator();
    }

    public void addToConsumeRequests(ConsumeRequest elem) {
      if (this.consumeRequests == null) {
        this.consumeRequests = new ArrayList<ConsumeRequest>();
      }
      this.consumeRequests.add(elem);
    }

    public List<ConsumeRequest> getConsumeRequests() {
      return this.consumeRequests;
    }

    public asyncConsumeDeliverOrderByRule_args setConsumeRequests(List<ConsumeRequest> consumeRequests) {
      this.consumeRequests = consumeRequests;
      return this;
    }

    public void unsetConsumeRequests() {
      this.consumeRequests = null;
    }

    /** Returns true if field consumeRequests is set (has been assigned a value) and false otherwise */
    public boolean isSetConsumeRequests() {
      return this.consumeRequests != null;
    }

    public void setConsumeRequestsIsSet(boolean value) {
      if (!value) {
        this.consumeRequests = null;
      }
    }

    public RuleInfo getRuleInfo() {
      return this.ruleInfo;
    }

    public asyncConsumeDeliverOrderByRule_args setRuleInfo(RuleInfo ruleInfo) {
      this.ruleInfo = ruleInfo;
      return this;
    }

    public void unsetRuleInfo() {
      this.ruleInfo = null;
    }

    /** Returns true if field ruleInfo is set (has been assigned a value) and false otherwise */
    public boolean isSetRuleInfo() {
      return this.ruleInfo != null;
    }

    public void setRuleInfoIsSet(boolean value) {
      if (!value) {
        this.ruleInfo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case ORDER_REQUEST:
        if (value == null) {
          unsetOrderRequest();
        } else {
          setOrderRequest((OrderRequest)value);
        }
        break;

      case CONSUME_REQUESTS:
        if (value == null) {
          unsetConsumeRequests();
        } else {
          setConsumeRequests((List<ConsumeRequest>)value);
        }
        break;

      case RULE_INFO:
        if (value == null) {
          unsetRuleInfo();
        } else {
          setRuleInfo((RuleInfo)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case ORDER_REQUEST:
        return getOrderRequest();

      case CONSUME_REQUESTS:
        return getConsumeRequests();

      case RULE_INFO:
        return getRuleInfo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case ORDER_REQUEST:
        return isSetOrderRequest();
      case CONSUME_REQUESTS:
        return isSetConsumeRequests();
      case RULE_INFO:
        return isSetRuleInfo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof asyncConsumeDeliverOrderByRule_args)
        return this.equals((asyncConsumeDeliverOrderByRule_args)that);
      return false;
    }

    public boolean equals(asyncConsumeDeliverOrderByRule_args that) {
      if (that == null)
        return false;

      boolean this_present_orderRequest = true && this.isSetOrderRequest();
      boolean that_present_orderRequest = true && that.isSetOrderRequest();
      if (this_present_orderRequest || that_present_orderRequest) {
        if (!(this_present_orderRequest && that_present_orderRequest))
          return false;
        if (!this.orderRequest.equals(that.orderRequest))
          return false;
      }

      boolean this_present_consumeRequests = true && this.isSetConsumeRequests();
      boolean that_present_consumeRequests = true && that.isSetConsumeRequests();
      if (this_present_consumeRequests || that_present_consumeRequests) {
        if (!(this_present_consumeRequests && that_present_consumeRequests))
          return false;
        if (!this.consumeRequests.equals(that.consumeRequests))
          return false;
      }

      boolean this_present_ruleInfo = true && this.isSetRuleInfo();
      boolean that_present_ruleInfo = true && that.isSetRuleInfo();
      if (this_present_ruleInfo || that_present_ruleInfo) {
        if (!(this_present_ruleInfo && that_present_ruleInfo))
          return false;
        if (!this.ruleInfo.equals(that.ruleInfo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_orderRequest = true && (isSetOrderRequest());
      list.add(present_orderRequest);
      if (present_orderRequest)
        list.add(orderRequest);

      boolean present_consumeRequests = true && (isSetConsumeRequests());
      list.add(present_consumeRequests);
      if (present_consumeRequests)
        list.add(consumeRequests);

      boolean present_ruleInfo = true && (isSetRuleInfo());
      list.add(present_ruleInfo);
      if (present_ruleInfo)
        list.add(ruleInfo);

      return list.hashCode();
    }

    @Override
    public int compareTo(asyncConsumeDeliverOrderByRule_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetOrderRequest()).compareTo(other.isSetOrderRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetOrderRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderRequest, other.orderRequest);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetConsumeRequests()).compareTo(other.isSetConsumeRequests());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetConsumeRequests()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.consumeRequests, other.consumeRequests);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetRuleInfo()).compareTo(other.isSetRuleInfo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRuleInfo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ruleInfo, other.ruleInfo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("asyncConsumeDeliverOrderByRule_args(");
      boolean first = true;

      sb.append("orderRequest:");
      if (this.orderRequest == null) {
        sb.append("null");
      } else {
        sb.append(this.orderRequest);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("consumeRequests:");
      if (this.consumeRequests == null) {
        sb.append("null");
      } else {
        sb.append(this.consumeRequests);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("ruleInfo:");
      if (this.ruleInfo == null) {
        sb.append("null");
      } else {
        sb.append(this.ruleInfo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (orderRequest == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderRequest' was not present! Struct: " + toString());
      }
      if (consumeRequests == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'consumeRequests' was not present! Struct: " + toString());
      }
      if (ruleInfo == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'ruleInfo' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (orderRequest != null) {
        orderRequest.validate();
      }
      if (ruleInfo != null) {
        ruleInfo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class asyncConsumeDeliverOrderByRule_argsStandardSchemeFactory implements SchemeFactory {
      public asyncConsumeDeliverOrderByRule_argsStandardScheme getScheme() {
        return new asyncConsumeDeliverOrderByRule_argsStandardScheme();
      }
    }

    private static class asyncConsumeDeliverOrderByRule_argsStandardScheme extends StandardScheme<asyncConsumeDeliverOrderByRule_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, asyncConsumeDeliverOrderByRule_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // ORDER_REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.orderRequest = new OrderRequest();
                struct.orderRequest.read(iprot);
                struct.setOrderRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // CONSUME_REQUESTS
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list72 = iprot.readListBegin();
                  struct.consumeRequests = new ArrayList<ConsumeRequest>(_list72.size);
                  ConsumeRequest _elem73;
                  for (int _i74 = 0; _i74 < _list72.size; ++_i74)
                  {
                    _elem73 = new ConsumeRequest();
                    _elem73.read(iprot);
                    struct.consumeRequests.add(_elem73);
                  }
                  iprot.readListEnd();
                }
                struct.setConsumeRequestsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // RULE_INFO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.ruleInfo = new RuleInfo();
                struct.ruleInfo.read(iprot);
                struct.setRuleInfoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, asyncConsumeDeliverOrderByRule_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.orderRequest != null) {
          oprot.writeFieldBegin(ORDER_REQUEST_FIELD_DESC);
          struct.orderRequest.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.consumeRequests != null) {
          oprot.writeFieldBegin(CONSUME_REQUESTS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.consumeRequests.size()));
            for (ConsumeRequest _iter75 : struct.consumeRequests)
            {
              _iter75.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        if (struct.ruleInfo != null) {
          oprot.writeFieldBegin(RULE_INFO_FIELD_DESC);
          struct.ruleInfo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class asyncConsumeDeliverOrderByRule_argsTupleSchemeFactory implements SchemeFactory {
      public asyncConsumeDeliverOrderByRule_argsTupleScheme getScheme() {
        return new asyncConsumeDeliverOrderByRule_argsTupleScheme();
      }
    }

    private static class asyncConsumeDeliverOrderByRule_argsTupleScheme extends TupleScheme<asyncConsumeDeliverOrderByRule_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, asyncConsumeDeliverOrderByRule_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.orderRequest.write(oprot);
        {
          oprot.writeI32(struct.consumeRequests.size());
          for (ConsumeRequest _iter76 : struct.consumeRequests)
          {
            _iter76.write(oprot);
          }
        }
        struct.ruleInfo.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, asyncConsumeDeliverOrderByRule_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.orderRequest = new OrderRequest();
        struct.orderRequest.read(iprot);
        struct.setOrderRequestIsSet(true);
        {
          org.apache.thrift.protocol.TList _list77 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.consumeRequests = new ArrayList<ConsumeRequest>(_list77.size);
          ConsumeRequest _elem78;
          for (int _i79 = 0; _i79 < _list77.size; ++_i79)
          {
            _elem78 = new ConsumeRequest();
            _elem78.read(iprot);
            struct.consumeRequests.add(_elem78);
          }
        }
        struct.setConsumeRequestsIsSet(true);
        struct.ruleInfo = new RuleInfo();
        struct.ruleInfo.read(iprot);
        struct.setRuleInfoIsSet(true);
      }
    }

  }

  public static class asyncConsumeDeliverOrderByRule_result implements org.apache.thrift.TBase<asyncConsumeDeliverOrderByRule_result, asyncConsumeDeliverOrderByRule_result._Fields>, java.io.Serializable, Cloneable, Comparable<asyncConsumeDeliverOrderByRule_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("asyncConsumeDeliverOrderByRule_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new asyncConsumeDeliverOrderByRule_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new asyncConsumeDeliverOrderByRule_resultTupleSchemeFactory());
    }

    public OrderResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(asyncConsumeDeliverOrderByRule_result.class, metaDataMap);
    }

    public asyncConsumeDeliverOrderByRule_result() {
    }

    public asyncConsumeDeliverOrderByRule_result(
      OrderResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public asyncConsumeDeliverOrderByRule_result(asyncConsumeDeliverOrderByRule_result other) {
      if (other.isSetSuccess()) {
        this.success = new OrderResponse(other.success);
      }
    }

    public asyncConsumeDeliverOrderByRule_result deepCopy() {
      return new asyncConsumeDeliverOrderByRule_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public OrderResponse getSuccess() {
      return this.success;
    }

    public asyncConsumeDeliverOrderByRule_result setSuccess(OrderResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((OrderResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof asyncConsumeDeliverOrderByRule_result)
        return this.equals((asyncConsumeDeliverOrderByRule_result)that);
      return false;
    }

    public boolean equals(asyncConsumeDeliverOrderByRule_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(asyncConsumeDeliverOrderByRule_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("asyncConsumeDeliverOrderByRule_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class asyncConsumeDeliverOrderByRule_resultStandardSchemeFactory implements SchemeFactory {
      public asyncConsumeDeliverOrderByRule_resultStandardScheme getScheme() {
        return new asyncConsumeDeliverOrderByRule_resultStandardScheme();
      }
    }

    private static class asyncConsumeDeliverOrderByRule_resultStandardScheme extends StandardScheme<asyncConsumeDeliverOrderByRule_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, asyncConsumeDeliverOrderByRule_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new OrderResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, asyncConsumeDeliverOrderByRule_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class asyncConsumeDeliverOrderByRule_resultTupleSchemeFactory implements SchemeFactory {
      public asyncConsumeDeliverOrderByRule_resultTupleScheme getScheme() {
        return new asyncConsumeDeliverOrderByRule_resultTupleScheme();
      }
    }

    private static class asyncConsumeDeliverOrderByRule_resultTupleScheme extends TupleScheme<asyncConsumeDeliverOrderByRule_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, asyncConsumeDeliverOrderByRule_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, asyncConsumeDeliverOrderByRule_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new OrderResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class asyncConsumeDeliverOrder_args implements org.apache.thrift.TBase<asyncConsumeDeliverOrder_args, asyncConsumeDeliverOrder_args._Fields>, java.io.Serializable, Cloneable, Comparable<asyncConsumeDeliverOrder_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("asyncConsumeDeliverOrder_args");

    private static final org.apache.thrift.protocol.TField ORDER_REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("orderRequest", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField CONSUME_REQUESTS_FIELD_DESC = new org.apache.thrift.protocol.TField("consumeRequests", org.apache.thrift.protocol.TType.LIST, (short)2);
    private static final org.apache.thrift.protocol.TField GOODS_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("goodsList", org.apache.thrift.protocol.TType.STRUCT, (short)3);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new asyncConsumeDeliverOrder_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new asyncConsumeDeliverOrder_argsTupleSchemeFactory());
    }

    public OrderRequest orderRequest; // required
    public List<ConsumeRequest> consumeRequests; // required
    public GoodsList goodsList; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      ORDER_REQUEST((short)1, "orderRequest"),
      CONSUME_REQUESTS((short)2, "consumeRequests"),
      GOODS_LIST((short)3, "goodsList");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // ORDER_REQUEST
            return ORDER_REQUEST;
          case 2: // CONSUME_REQUESTS
            return CONSUME_REQUESTS;
          case 3: // GOODS_LIST
            return GOODS_LIST;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.ORDER_REQUEST, new org.apache.thrift.meta_data.FieldMetaData("orderRequest", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderRequest.class)));
      tmpMap.put(_Fields.CONSUME_REQUESTS, new org.apache.thrift.meta_data.FieldMetaData("consumeRequests", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
              new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeRequest.class))));
      tmpMap.put(_Fields.GOODS_LIST, new org.apache.thrift.meta_data.FieldMetaData("goodsList", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, GoodsList.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(asyncConsumeDeliverOrder_args.class, metaDataMap);
    }

    public asyncConsumeDeliverOrder_args() {
    }

    public asyncConsumeDeliverOrder_args(
      OrderRequest orderRequest,
      List<ConsumeRequest> consumeRequests,
      GoodsList goodsList)
    {
      this();
      this.orderRequest = orderRequest;
      this.consumeRequests = consumeRequests;
      this.goodsList = goodsList;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public asyncConsumeDeliverOrder_args(asyncConsumeDeliverOrder_args other) {
      if (other.isSetOrderRequest()) {
        this.orderRequest = new OrderRequest(other.orderRequest);
      }
      if (other.isSetConsumeRequests()) {
        List<ConsumeRequest> __this__consumeRequests = new ArrayList<ConsumeRequest>(other.consumeRequests.size());
        for (ConsumeRequest other_element : other.consumeRequests) {
          __this__consumeRequests.add(new ConsumeRequest(other_element));
        }
        this.consumeRequests = __this__consumeRequests;
      }
      if (other.isSetGoodsList()) {
        this.goodsList = new GoodsList(other.goodsList);
      }
    }

    public asyncConsumeDeliverOrder_args deepCopy() {
      return new asyncConsumeDeliverOrder_args(this);
    }

    @Override
    public void clear() {
      this.orderRequest = null;
      this.consumeRequests = null;
      this.goodsList = null;
    }

    public OrderRequest getOrderRequest() {
      return this.orderRequest;
    }

    public asyncConsumeDeliverOrder_args setOrderRequest(OrderRequest orderRequest) {
      this.orderRequest = orderRequest;
      return this;
    }

    public void unsetOrderRequest() {
      this.orderRequest = null;
    }

    /** Returns true if field orderRequest is set (has been assigned a value) and false otherwise */
    public boolean isSetOrderRequest() {
      return this.orderRequest != null;
    }

    public void setOrderRequestIsSet(boolean value) {
      if (!value) {
        this.orderRequest = null;
      }
    }

    public int getConsumeRequestsSize() {
      return (this.consumeRequests == null) ? 0 : this.consumeRequests.size();
    }

    public java.util.Iterator<ConsumeRequest> getConsumeRequestsIterator() {
      return (this.consumeRequests == null) ? null : this.consumeRequests.iterator();
    }

    public void addToConsumeRequests(ConsumeRequest elem) {
      if (this.consumeRequests == null) {
        this.consumeRequests = new ArrayList<ConsumeRequest>();
      }
      this.consumeRequests.add(elem);
    }

    public List<ConsumeRequest> getConsumeRequests() {
      return this.consumeRequests;
    }

    public asyncConsumeDeliverOrder_args setConsumeRequests(List<ConsumeRequest> consumeRequests) {
      this.consumeRequests = consumeRequests;
      return this;
    }

    public void unsetConsumeRequests() {
      this.consumeRequests = null;
    }

    /** Returns true if field consumeRequests is set (has been assigned a value) and false otherwise */
    public boolean isSetConsumeRequests() {
      return this.consumeRequests != null;
    }

    public void setConsumeRequestsIsSet(boolean value) {
      if (!value) {
        this.consumeRequests = null;
      }
    }

    public GoodsList getGoodsList() {
      return this.goodsList;
    }

    public asyncConsumeDeliverOrder_args setGoodsList(GoodsList goodsList) {
      this.goodsList = goodsList;
      return this;
    }

    public void unsetGoodsList() {
      this.goodsList = null;
    }

    /** Returns true if field goodsList is set (has been assigned a value) and false otherwise */
    public boolean isSetGoodsList() {
      return this.goodsList != null;
    }

    public void setGoodsListIsSet(boolean value) {
      if (!value) {
        this.goodsList = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case ORDER_REQUEST:
        if (value == null) {
          unsetOrderRequest();
        } else {
          setOrderRequest((OrderRequest)value);
        }
        break;

      case CONSUME_REQUESTS:
        if (value == null) {
          unsetConsumeRequests();
        } else {
          setConsumeRequests((List<ConsumeRequest>)value);
        }
        break;

      case GOODS_LIST:
        if (value == null) {
          unsetGoodsList();
        } else {
          setGoodsList((GoodsList)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case ORDER_REQUEST:
        return getOrderRequest();

      case CONSUME_REQUESTS:
        return getConsumeRequests();

      case GOODS_LIST:
        return getGoodsList();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case ORDER_REQUEST:
        return isSetOrderRequest();
      case CONSUME_REQUESTS:
        return isSetConsumeRequests();
      case GOODS_LIST:
        return isSetGoodsList();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof asyncConsumeDeliverOrder_args)
        return this.equals((asyncConsumeDeliverOrder_args)that);
      return false;
    }

    public boolean equals(asyncConsumeDeliverOrder_args that) {
      if (that == null)
        return false;

      boolean this_present_orderRequest = true && this.isSetOrderRequest();
      boolean that_present_orderRequest = true && that.isSetOrderRequest();
      if (this_present_orderRequest || that_present_orderRequest) {
        if (!(this_present_orderRequest && that_present_orderRequest))
          return false;
        if (!this.orderRequest.equals(that.orderRequest))
          return false;
      }

      boolean this_present_consumeRequests = true && this.isSetConsumeRequests();
      boolean that_present_consumeRequests = true && that.isSetConsumeRequests();
      if (this_present_consumeRequests || that_present_consumeRequests) {
        if (!(this_present_consumeRequests && that_present_consumeRequests))
          return false;
        if (!this.consumeRequests.equals(that.consumeRequests))
          return false;
      }

      boolean this_present_goodsList = true && this.isSetGoodsList();
      boolean that_present_goodsList = true && that.isSetGoodsList();
      if (this_present_goodsList || that_present_goodsList) {
        if (!(this_present_goodsList && that_present_goodsList))
          return false;
        if (!this.goodsList.equals(that.goodsList))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_orderRequest = true && (isSetOrderRequest());
      list.add(present_orderRequest);
      if (present_orderRequest)
        list.add(orderRequest);

      boolean present_consumeRequests = true && (isSetConsumeRequests());
      list.add(present_consumeRequests);
      if (present_consumeRequests)
        list.add(consumeRequests);

      boolean present_goodsList = true && (isSetGoodsList());
      list.add(present_goodsList);
      if (present_goodsList)
        list.add(goodsList);

      return list.hashCode();
    }

    @Override
    public int compareTo(asyncConsumeDeliverOrder_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetOrderRequest()).compareTo(other.isSetOrderRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetOrderRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderRequest, other.orderRequest);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetConsumeRequests()).compareTo(other.isSetConsumeRequests());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetConsumeRequests()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.consumeRequests, other.consumeRequests);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetGoodsList()).compareTo(other.isSetGoodsList());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetGoodsList()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.goodsList, other.goodsList);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("asyncConsumeDeliverOrder_args(");
      boolean first = true;

      sb.append("orderRequest:");
      if (this.orderRequest == null) {
        sb.append("null");
      } else {
        sb.append(this.orderRequest);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("consumeRequests:");
      if (this.consumeRequests == null) {
        sb.append("null");
      } else {
        sb.append(this.consumeRequests);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("goodsList:");
      if (this.goodsList == null) {
        sb.append("null");
      } else {
        sb.append(this.goodsList);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (orderRequest == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderRequest' was not present! Struct: " + toString());
      }
      if (consumeRequests == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'consumeRequests' was not present! Struct: " + toString());
      }
      if (goodsList == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'goodsList' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (orderRequest != null) {
        orderRequest.validate();
      }
      if (goodsList != null) {
        goodsList.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class asyncConsumeDeliverOrder_argsStandardSchemeFactory implements SchemeFactory {
      public asyncConsumeDeliverOrder_argsStandardScheme getScheme() {
        return new asyncConsumeDeliverOrder_argsStandardScheme();
      }
    }

    private static class asyncConsumeDeliverOrder_argsStandardScheme extends StandardScheme<asyncConsumeDeliverOrder_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, asyncConsumeDeliverOrder_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // ORDER_REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.orderRequest = new OrderRequest();
                struct.orderRequest.read(iprot);
                struct.setOrderRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // CONSUME_REQUESTS
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list80 = iprot.readListBegin();
                  struct.consumeRequests = new ArrayList<ConsumeRequest>(_list80.size);
                  ConsumeRequest _elem81;
                  for (int _i82 = 0; _i82 < _list80.size; ++_i82)
                  {
                    _elem81 = new ConsumeRequest();
                    _elem81.read(iprot);
                    struct.consumeRequests.add(_elem81);
                  }
                  iprot.readListEnd();
                }
                struct.setConsumeRequestsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // GOODS_LIST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.goodsList = new GoodsList();
                struct.goodsList.read(iprot);
                struct.setGoodsListIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, asyncConsumeDeliverOrder_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.orderRequest != null) {
          oprot.writeFieldBegin(ORDER_REQUEST_FIELD_DESC);
          struct.orderRequest.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.consumeRequests != null) {
          oprot.writeFieldBegin(CONSUME_REQUESTS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.consumeRequests.size()));
            for (ConsumeRequest _iter83 : struct.consumeRequests)
            {
              _iter83.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        if (struct.goodsList != null) {
          oprot.writeFieldBegin(GOODS_LIST_FIELD_DESC);
          struct.goodsList.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class asyncConsumeDeliverOrder_argsTupleSchemeFactory implements SchemeFactory {
      public asyncConsumeDeliverOrder_argsTupleScheme getScheme() {
        return new asyncConsumeDeliverOrder_argsTupleScheme();
      }
    }

    private static class asyncConsumeDeliverOrder_argsTupleScheme extends TupleScheme<asyncConsumeDeliverOrder_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, asyncConsumeDeliverOrder_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.orderRequest.write(oprot);
        {
          oprot.writeI32(struct.consumeRequests.size());
          for (ConsumeRequest _iter84 : struct.consumeRequests)
          {
            _iter84.write(oprot);
          }
        }
        struct.goodsList.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, asyncConsumeDeliverOrder_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.orderRequest = new OrderRequest();
        struct.orderRequest.read(iprot);
        struct.setOrderRequestIsSet(true);
        {
          org.apache.thrift.protocol.TList _list85 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.consumeRequests = new ArrayList<ConsumeRequest>(_list85.size);
          ConsumeRequest _elem86;
          for (int _i87 = 0; _i87 < _list85.size; ++_i87)
          {
            _elem86 = new ConsumeRequest();
            _elem86.read(iprot);
            struct.consumeRequests.add(_elem86);
          }
        }
        struct.setConsumeRequestsIsSet(true);
        struct.goodsList = new GoodsList();
        struct.goodsList.read(iprot);
        struct.setGoodsListIsSet(true);
      }
    }

  }

  public static class asyncConsumeDeliverOrder_result implements org.apache.thrift.TBase<asyncConsumeDeliverOrder_result, asyncConsumeDeliverOrder_result._Fields>, java.io.Serializable, Cloneable, Comparable<asyncConsumeDeliverOrder_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("asyncConsumeDeliverOrder_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new asyncConsumeDeliverOrder_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new asyncConsumeDeliverOrder_resultTupleSchemeFactory());
    }

    public OrderResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(asyncConsumeDeliverOrder_result.class, metaDataMap);
    }

    public asyncConsumeDeliverOrder_result() {
    }

    public asyncConsumeDeliverOrder_result(
      OrderResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public asyncConsumeDeliverOrder_result(asyncConsumeDeliverOrder_result other) {
      if (other.isSetSuccess()) {
        this.success = new OrderResponse(other.success);
      }
    }

    public asyncConsumeDeliverOrder_result deepCopy() {
      return new asyncConsumeDeliverOrder_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public OrderResponse getSuccess() {
      return this.success;
    }

    public asyncConsumeDeliverOrder_result setSuccess(OrderResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((OrderResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof asyncConsumeDeliverOrder_result)
        return this.equals((asyncConsumeDeliverOrder_result)that);
      return false;
    }

    public boolean equals(asyncConsumeDeliverOrder_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(asyncConsumeDeliverOrder_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("asyncConsumeDeliverOrder_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class asyncConsumeDeliverOrder_resultStandardSchemeFactory implements SchemeFactory {
      public asyncConsumeDeliverOrder_resultStandardScheme getScheme() {
        return new asyncConsumeDeliverOrder_resultStandardScheme();
      }
    }

    private static class asyncConsumeDeliverOrder_resultStandardScheme extends StandardScheme<asyncConsumeDeliverOrder_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, asyncConsumeDeliverOrder_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new OrderResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, asyncConsumeDeliverOrder_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class asyncConsumeDeliverOrder_resultTupleSchemeFactory implements SchemeFactory {
      public asyncConsumeDeliverOrder_resultTupleScheme getScheme() {
        return new asyncConsumeDeliverOrder_resultTupleScheme();
      }
    }

    private static class asyncConsumeDeliverOrder_resultTupleScheme extends TupleScheme<asyncConsumeDeliverOrder_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, asyncConsumeDeliverOrder_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, asyncConsumeDeliverOrder_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new OrderResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class asyncDeliverOrder_args implements org.apache.thrift.TBase<asyncDeliverOrder_args, asyncDeliverOrder_args._Fields>, java.io.Serializable, Cloneable, Comparable<asyncDeliverOrder_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("asyncDeliverOrder_args");

    private static final org.apache.thrift.protocol.TField ORDER_REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("orderRequest", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField GOODS_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("goodsList", org.apache.thrift.protocol.TType.STRUCT, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new asyncDeliverOrder_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new asyncDeliverOrder_argsTupleSchemeFactory());
    }

    public OrderRequest orderRequest; // required
    public GoodsList goodsList; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      ORDER_REQUEST((short)1, "orderRequest"),
      GOODS_LIST((short)2, "goodsList");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // ORDER_REQUEST
            return ORDER_REQUEST;
          case 2: // GOODS_LIST
            return GOODS_LIST;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.ORDER_REQUEST, new org.apache.thrift.meta_data.FieldMetaData("orderRequest", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderRequest.class)));
      tmpMap.put(_Fields.GOODS_LIST, new org.apache.thrift.meta_data.FieldMetaData("goodsList", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, GoodsList.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(asyncDeliverOrder_args.class, metaDataMap);
    }

    public asyncDeliverOrder_args() {
    }

    public asyncDeliverOrder_args(
      OrderRequest orderRequest,
      GoodsList goodsList)
    {
      this();
      this.orderRequest = orderRequest;
      this.goodsList = goodsList;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public asyncDeliverOrder_args(asyncDeliverOrder_args other) {
      if (other.isSetOrderRequest()) {
        this.orderRequest = new OrderRequest(other.orderRequest);
      }
      if (other.isSetGoodsList()) {
        this.goodsList = new GoodsList(other.goodsList);
      }
    }

    public asyncDeliverOrder_args deepCopy() {
      return new asyncDeliverOrder_args(this);
    }

    @Override
    public void clear() {
      this.orderRequest = null;
      this.goodsList = null;
    }

    public OrderRequest getOrderRequest() {
      return this.orderRequest;
    }

    public asyncDeliverOrder_args setOrderRequest(OrderRequest orderRequest) {
      this.orderRequest = orderRequest;
      return this;
    }

    public void unsetOrderRequest() {
      this.orderRequest = null;
    }

    /** Returns true if field orderRequest is set (has been assigned a value) and false otherwise */
    public boolean isSetOrderRequest() {
      return this.orderRequest != null;
    }

    public void setOrderRequestIsSet(boolean value) {
      if (!value) {
        this.orderRequest = null;
      }
    }

    public GoodsList getGoodsList() {
      return this.goodsList;
    }

    public asyncDeliverOrder_args setGoodsList(GoodsList goodsList) {
      this.goodsList = goodsList;
      return this;
    }

    public void unsetGoodsList() {
      this.goodsList = null;
    }

    /** Returns true if field goodsList is set (has been assigned a value) and false otherwise */
    public boolean isSetGoodsList() {
      return this.goodsList != null;
    }

    public void setGoodsListIsSet(boolean value) {
      if (!value) {
        this.goodsList = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case ORDER_REQUEST:
        if (value == null) {
          unsetOrderRequest();
        } else {
          setOrderRequest((OrderRequest)value);
        }
        break;

      case GOODS_LIST:
        if (value == null) {
          unsetGoodsList();
        } else {
          setGoodsList((GoodsList)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case ORDER_REQUEST:
        return getOrderRequest();

      case GOODS_LIST:
        return getGoodsList();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case ORDER_REQUEST:
        return isSetOrderRequest();
      case GOODS_LIST:
        return isSetGoodsList();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof asyncDeliverOrder_args)
        return this.equals((asyncDeliverOrder_args)that);
      return false;
    }

    public boolean equals(asyncDeliverOrder_args that) {
      if (that == null)
        return false;

      boolean this_present_orderRequest = true && this.isSetOrderRequest();
      boolean that_present_orderRequest = true && that.isSetOrderRequest();
      if (this_present_orderRequest || that_present_orderRequest) {
        if (!(this_present_orderRequest && that_present_orderRequest))
          return false;
        if (!this.orderRequest.equals(that.orderRequest))
          return false;
      }

      boolean this_present_goodsList = true && this.isSetGoodsList();
      boolean that_present_goodsList = true && that.isSetGoodsList();
      if (this_present_goodsList || that_present_goodsList) {
        if (!(this_present_goodsList && that_present_goodsList))
          return false;
        if (!this.goodsList.equals(that.goodsList))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_orderRequest = true && (isSetOrderRequest());
      list.add(present_orderRequest);
      if (present_orderRequest)
        list.add(orderRequest);

      boolean present_goodsList = true && (isSetGoodsList());
      list.add(present_goodsList);
      if (present_goodsList)
        list.add(goodsList);

      return list.hashCode();
    }

    @Override
    public int compareTo(asyncDeliverOrder_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetOrderRequest()).compareTo(other.isSetOrderRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetOrderRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderRequest, other.orderRequest);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetGoodsList()).compareTo(other.isSetGoodsList());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetGoodsList()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.goodsList, other.goodsList);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("asyncDeliverOrder_args(");
      boolean first = true;

      sb.append("orderRequest:");
      if (this.orderRequest == null) {
        sb.append("null");
      } else {
        sb.append(this.orderRequest);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("goodsList:");
      if (this.goodsList == null) {
        sb.append("null");
      } else {
        sb.append(this.goodsList);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (orderRequest == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderRequest' was not present! Struct: " + toString());
      }
      if (goodsList == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'goodsList' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (orderRequest != null) {
        orderRequest.validate();
      }
      if (goodsList != null) {
        goodsList.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class asyncDeliverOrder_argsStandardSchemeFactory implements SchemeFactory {
      public asyncDeliverOrder_argsStandardScheme getScheme() {
        return new asyncDeliverOrder_argsStandardScheme();
      }
    }

    private static class asyncDeliverOrder_argsStandardScheme extends StandardScheme<asyncDeliverOrder_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, asyncDeliverOrder_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // ORDER_REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.orderRequest = new OrderRequest();
                struct.orderRequest.read(iprot);
                struct.setOrderRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // GOODS_LIST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.goodsList = new GoodsList();
                struct.goodsList.read(iprot);
                struct.setGoodsListIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, asyncDeliverOrder_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.orderRequest != null) {
          oprot.writeFieldBegin(ORDER_REQUEST_FIELD_DESC);
          struct.orderRequest.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.goodsList != null) {
          oprot.writeFieldBegin(GOODS_LIST_FIELD_DESC);
          struct.goodsList.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class asyncDeliverOrder_argsTupleSchemeFactory implements SchemeFactory {
      public asyncDeliverOrder_argsTupleScheme getScheme() {
        return new asyncDeliverOrder_argsTupleScheme();
      }
    }

    private static class asyncDeliverOrder_argsTupleScheme extends TupleScheme<asyncDeliverOrder_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, asyncDeliverOrder_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.orderRequest.write(oprot);
        struct.goodsList.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, asyncDeliverOrder_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.orderRequest = new OrderRequest();
        struct.orderRequest.read(iprot);
        struct.setOrderRequestIsSet(true);
        struct.goodsList = new GoodsList();
        struct.goodsList.read(iprot);
        struct.setGoodsListIsSet(true);
      }
    }

  }

  public static class asyncDeliverOrder_result implements org.apache.thrift.TBase<asyncDeliverOrder_result, asyncDeliverOrder_result._Fields>, java.io.Serializable, Cloneable, Comparable<asyncDeliverOrder_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("asyncDeliverOrder_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new asyncDeliverOrder_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new asyncDeliverOrder_resultTupleSchemeFactory());
    }

    public OrderResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(asyncDeliverOrder_result.class, metaDataMap);
    }

    public asyncDeliverOrder_result() {
    }

    public asyncDeliverOrder_result(
      OrderResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public asyncDeliverOrder_result(asyncDeliverOrder_result other) {
      if (other.isSetSuccess()) {
        this.success = new OrderResponse(other.success);
      }
    }

    public asyncDeliverOrder_result deepCopy() {
      return new asyncDeliverOrder_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public OrderResponse getSuccess() {
      return this.success;
    }

    public asyncDeliverOrder_result setSuccess(OrderResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((OrderResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof asyncDeliverOrder_result)
        return this.equals((asyncDeliverOrder_result)that);
      return false;
    }

    public boolean equals(asyncDeliverOrder_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(asyncDeliverOrder_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("asyncDeliverOrder_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class asyncDeliverOrder_resultStandardSchemeFactory implements SchemeFactory {
      public asyncDeliverOrder_resultStandardScheme getScheme() {
        return new asyncDeliverOrder_resultStandardScheme();
      }
    }

    private static class asyncDeliverOrder_resultStandardScheme extends StandardScheme<asyncDeliverOrder_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, asyncDeliverOrder_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new OrderResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, asyncDeliverOrder_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class asyncDeliverOrder_resultTupleSchemeFactory implements SchemeFactory {
      public asyncDeliverOrder_resultTupleScheme getScheme() {
        return new asyncDeliverOrder_resultTupleScheme();
      }
    }

    private static class asyncDeliverOrder_resultTupleScheme extends TupleScheme<asyncDeliverOrder_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, asyncDeliverOrder_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, asyncDeliverOrder_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new OrderResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class finishOrder_args implements org.apache.thrift.TBase<finishOrder_args, finishOrder_args._Fields>, java.io.Serializable, Cloneable, Comparable<finishOrder_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("finishOrder_args");

    private static final org.apache.thrift.protocol.TField ORDER_REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("orderRequest", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new finishOrder_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new finishOrder_argsTupleSchemeFactory());
    }

    public OrderRequest orderRequest; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      ORDER_REQUEST((short)1, "orderRequest");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // ORDER_REQUEST
            return ORDER_REQUEST;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.ORDER_REQUEST, new org.apache.thrift.meta_data.FieldMetaData("orderRequest", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderRequest.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(finishOrder_args.class, metaDataMap);
    }

    public finishOrder_args() {
    }

    public finishOrder_args(
      OrderRequest orderRequest)
    {
      this();
      this.orderRequest = orderRequest;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public finishOrder_args(finishOrder_args other) {
      if (other.isSetOrderRequest()) {
        this.orderRequest = new OrderRequest(other.orderRequest);
      }
    }

    public finishOrder_args deepCopy() {
      return new finishOrder_args(this);
    }

    @Override
    public void clear() {
      this.orderRequest = null;
    }

    public OrderRequest getOrderRequest() {
      return this.orderRequest;
    }

    public finishOrder_args setOrderRequest(OrderRequest orderRequest) {
      this.orderRequest = orderRequest;
      return this;
    }

    public void unsetOrderRequest() {
      this.orderRequest = null;
    }

    /** Returns true if field orderRequest is set (has been assigned a value) and false otherwise */
    public boolean isSetOrderRequest() {
      return this.orderRequest != null;
    }

    public void setOrderRequestIsSet(boolean value) {
      if (!value) {
        this.orderRequest = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case ORDER_REQUEST:
        if (value == null) {
          unsetOrderRequest();
        } else {
          setOrderRequest((OrderRequest)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case ORDER_REQUEST:
        return getOrderRequest();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case ORDER_REQUEST:
        return isSetOrderRequest();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof finishOrder_args)
        return this.equals((finishOrder_args)that);
      return false;
    }

    public boolean equals(finishOrder_args that) {
      if (that == null)
        return false;

      boolean this_present_orderRequest = true && this.isSetOrderRequest();
      boolean that_present_orderRequest = true && that.isSetOrderRequest();
      if (this_present_orderRequest || that_present_orderRequest) {
        if (!(this_present_orderRequest && that_present_orderRequest))
          return false;
        if (!this.orderRequest.equals(that.orderRequest))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_orderRequest = true && (isSetOrderRequest());
      list.add(present_orderRequest);
      if (present_orderRequest)
        list.add(orderRequest);

      return list.hashCode();
    }

    @Override
    public int compareTo(finishOrder_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetOrderRequest()).compareTo(other.isSetOrderRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetOrderRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderRequest, other.orderRequest);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("finishOrder_args(");
      boolean first = true;

      sb.append("orderRequest:");
      if (this.orderRequest == null) {
        sb.append("null");
      } else {
        sb.append(this.orderRequest);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (orderRequest == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderRequest' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (orderRequest != null) {
        orderRequest.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class finishOrder_argsStandardSchemeFactory implements SchemeFactory {
      public finishOrder_argsStandardScheme getScheme() {
        return new finishOrder_argsStandardScheme();
      }
    }

    private static class finishOrder_argsStandardScheme extends StandardScheme<finishOrder_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, finishOrder_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // ORDER_REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.orderRequest = new OrderRequest();
                struct.orderRequest.read(iprot);
                struct.setOrderRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, finishOrder_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.orderRequest != null) {
          oprot.writeFieldBegin(ORDER_REQUEST_FIELD_DESC);
          struct.orderRequest.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class finishOrder_argsTupleSchemeFactory implements SchemeFactory {
      public finishOrder_argsTupleScheme getScheme() {
        return new finishOrder_argsTupleScheme();
      }
    }

    private static class finishOrder_argsTupleScheme extends TupleScheme<finishOrder_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, finishOrder_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.orderRequest.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, finishOrder_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.orderRequest = new OrderRequest();
        struct.orderRequest.read(iprot);
        struct.setOrderRequestIsSet(true);
      }
    }

  }

  public static class finishOrder_result implements org.apache.thrift.TBase<finishOrder_result, finishOrder_result._Fields>, java.io.Serializable, Cloneable, Comparable<finishOrder_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("finishOrder_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new finishOrder_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new finishOrder_resultTupleSchemeFactory());
    }

    public OrderResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(finishOrder_result.class, metaDataMap);
    }

    public finishOrder_result() {
    }

    public finishOrder_result(
      OrderResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public finishOrder_result(finishOrder_result other) {
      if (other.isSetSuccess()) {
        this.success = new OrderResponse(other.success);
      }
    }

    public finishOrder_result deepCopy() {
      return new finishOrder_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public OrderResponse getSuccess() {
      return this.success;
    }

    public finishOrder_result setSuccess(OrderResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((OrderResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof finishOrder_result)
        return this.equals((finishOrder_result)that);
      return false;
    }

    public boolean equals(finishOrder_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(finishOrder_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("finishOrder_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class finishOrder_resultStandardSchemeFactory implements SchemeFactory {
      public finishOrder_resultStandardScheme getScheme() {
        return new finishOrder_resultStandardScheme();
      }
    }

    private static class finishOrder_resultStandardScheme extends StandardScheme<finishOrder_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, finishOrder_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new OrderResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, finishOrder_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class finishOrder_resultTupleSchemeFactory implements SchemeFactory {
      public finishOrder_resultTupleScheme getScheme() {
        return new finishOrder_resultTupleScheme();
      }
    }

    private static class finishOrder_resultTupleScheme extends TupleScheme<finishOrder_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, finishOrder_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, finishOrder_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new OrderResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class finishOrderByRule_args implements org.apache.thrift.TBase<finishOrderByRule_args, finishOrderByRule_args._Fields>, java.io.Serializable, Cloneable, Comparable<finishOrderByRule_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("finishOrderByRule_args");

    private static final org.apache.thrift.protocol.TField ORDER_REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("orderRequest", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField RULE_INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("ruleInfo", org.apache.thrift.protocol.TType.STRUCT, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new finishOrderByRule_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new finishOrderByRule_argsTupleSchemeFactory());
    }

    public OrderRequest orderRequest; // required
    public RuleInfo ruleInfo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      ORDER_REQUEST((short)1, "orderRequest"),
      RULE_INFO((short)2, "ruleInfo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // ORDER_REQUEST
            return ORDER_REQUEST;
          case 2: // RULE_INFO
            return RULE_INFO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.ORDER_REQUEST, new org.apache.thrift.meta_data.FieldMetaData("orderRequest", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderRequest.class)));
      tmpMap.put(_Fields.RULE_INFO, new org.apache.thrift.meta_data.FieldMetaData("ruleInfo", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, RuleInfo.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(finishOrderByRule_args.class, metaDataMap);
    }

    public finishOrderByRule_args() {
    }

    public finishOrderByRule_args(
      OrderRequest orderRequest,
      RuleInfo ruleInfo)
    {
      this();
      this.orderRequest = orderRequest;
      this.ruleInfo = ruleInfo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public finishOrderByRule_args(finishOrderByRule_args other) {
      if (other.isSetOrderRequest()) {
        this.orderRequest = new OrderRequest(other.orderRequest);
      }
      if (other.isSetRuleInfo()) {
        this.ruleInfo = new RuleInfo(other.ruleInfo);
      }
    }

    public finishOrderByRule_args deepCopy() {
      return new finishOrderByRule_args(this);
    }

    @Override
    public void clear() {
      this.orderRequest = null;
      this.ruleInfo = null;
    }

    public OrderRequest getOrderRequest() {
      return this.orderRequest;
    }

    public finishOrderByRule_args setOrderRequest(OrderRequest orderRequest) {
      this.orderRequest = orderRequest;
      return this;
    }

    public void unsetOrderRequest() {
      this.orderRequest = null;
    }

    /** Returns true if field orderRequest is set (has been assigned a value) and false otherwise */
    public boolean isSetOrderRequest() {
      return this.orderRequest != null;
    }

    public void setOrderRequestIsSet(boolean value) {
      if (!value) {
        this.orderRequest = null;
      }
    }

    public RuleInfo getRuleInfo() {
      return this.ruleInfo;
    }

    public finishOrderByRule_args setRuleInfo(RuleInfo ruleInfo) {
      this.ruleInfo = ruleInfo;
      return this;
    }

    public void unsetRuleInfo() {
      this.ruleInfo = null;
    }

    /** Returns true if field ruleInfo is set (has been assigned a value) and false otherwise */
    public boolean isSetRuleInfo() {
      return this.ruleInfo != null;
    }

    public void setRuleInfoIsSet(boolean value) {
      if (!value) {
        this.ruleInfo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case ORDER_REQUEST:
        if (value == null) {
          unsetOrderRequest();
        } else {
          setOrderRequest((OrderRequest)value);
        }
        break;

      case RULE_INFO:
        if (value == null) {
          unsetRuleInfo();
        } else {
          setRuleInfo((RuleInfo)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case ORDER_REQUEST:
        return getOrderRequest();

      case RULE_INFO:
        return getRuleInfo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case ORDER_REQUEST:
        return isSetOrderRequest();
      case RULE_INFO:
        return isSetRuleInfo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof finishOrderByRule_args)
        return this.equals((finishOrderByRule_args)that);
      return false;
    }

    public boolean equals(finishOrderByRule_args that) {
      if (that == null)
        return false;

      boolean this_present_orderRequest = true && this.isSetOrderRequest();
      boolean that_present_orderRequest = true && that.isSetOrderRequest();
      if (this_present_orderRequest || that_present_orderRequest) {
        if (!(this_present_orderRequest && that_present_orderRequest))
          return false;
        if (!this.orderRequest.equals(that.orderRequest))
          return false;
      }

      boolean this_present_ruleInfo = true && this.isSetRuleInfo();
      boolean that_present_ruleInfo = true && that.isSetRuleInfo();
      if (this_present_ruleInfo || that_present_ruleInfo) {
        if (!(this_present_ruleInfo && that_present_ruleInfo))
          return false;
        if (!this.ruleInfo.equals(that.ruleInfo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_orderRequest = true && (isSetOrderRequest());
      list.add(present_orderRequest);
      if (present_orderRequest)
        list.add(orderRequest);

      boolean present_ruleInfo = true && (isSetRuleInfo());
      list.add(present_ruleInfo);
      if (present_ruleInfo)
        list.add(ruleInfo);

      return list.hashCode();
    }

    @Override
    public int compareTo(finishOrderByRule_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetOrderRequest()).compareTo(other.isSetOrderRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetOrderRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderRequest, other.orderRequest);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetRuleInfo()).compareTo(other.isSetRuleInfo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRuleInfo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ruleInfo, other.ruleInfo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("finishOrderByRule_args(");
      boolean first = true;

      sb.append("orderRequest:");
      if (this.orderRequest == null) {
        sb.append("null");
      } else {
        sb.append(this.orderRequest);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("ruleInfo:");
      if (this.ruleInfo == null) {
        sb.append("null");
      } else {
        sb.append(this.ruleInfo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (orderRequest == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderRequest' was not present! Struct: " + toString());
      }
      if (ruleInfo == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'ruleInfo' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (orderRequest != null) {
        orderRequest.validate();
      }
      if (ruleInfo != null) {
        ruleInfo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class finishOrderByRule_argsStandardSchemeFactory implements SchemeFactory {
      public finishOrderByRule_argsStandardScheme getScheme() {
        return new finishOrderByRule_argsStandardScheme();
      }
    }

    private static class finishOrderByRule_argsStandardScheme extends StandardScheme<finishOrderByRule_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, finishOrderByRule_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // ORDER_REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.orderRequest = new OrderRequest();
                struct.orderRequest.read(iprot);
                struct.setOrderRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // RULE_INFO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.ruleInfo = new RuleInfo();
                struct.ruleInfo.read(iprot);
                struct.setRuleInfoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, finishOrderByRule_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.orderRequest != null) {
          oprot.writeFieldBegin(ORDER_REQUEST_FIELD_DESC);
          struct.orderRequest.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.ruleInfo != null) {
          oprot.writeFieldBegin(RULE_INFO_FIELD_DESC);
          struct.ruleInfo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class finishOrderByRule_argsTupleSchemeFactory implements SchemeFactory {
      public finishOrderByRule_argsTupleScheme getScheme() {
        return new finishOrderByRule_argsTupleScheme();
      }
    }

    private static class finishOrderByRule_argsTupleScheme extends TupleScheme<finishOrderByRule_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, finishOrderByRule_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.orderRequest.write(oprot);
        struct.ruleInfo.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, finishOrderByRule_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.orderRequest = new OrderRequest();
        struct.orderRequest.read(iprot);
        struct.setOrderRequestIsSet(true);
        struct.ruleInfo = new RuleInfo();
        struct.ruleInfo.read(iprot);
        struct.setRuleInfoIsSet(true);
      }
    }

  }

  public static class finishOrderByRule_result implements org.apache.thrift.TBase<finishOrderByRule_result, finishOrderByRule_result._Fields>, java.io.Serializable, Cloneable, Comparable<finishOrderByRule_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("finishOrderByRule_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new finishOrderByRule_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new finishOrderByRule_resultTupleSchemeFactory());
    }

    public OrderResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OrderResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(finishOrderByRule_result.class, metaDataMap);
    }

    public finishOrderByRule_result() {
    }

    public finishOrderByRule_result(
      OrderResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public finishOrderByRule_result(finishOrderByRule_result other) {
      if (other.isSetSuccess()) {
        this.success = new OrderResponse(other.success);
      }
    }

    public finishOrderByRule_result deepCopy() {
      return new finishOrderByRule_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public OrderResponse getSuccess() {
      return this.success;
    }

    public finishOrderByRule_result setSuccess(OrderResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((OrderResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof finishOrderByRule_result)
        return this.equals((finishOrderByRule_result)that);
      return false;
    }

    public boolean equals(finishOrderByRule_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(finishOrderByRule_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("finishOrderByRule_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class finishOrderByRule_resultStandardSchemeFactory implements SchemeFactory {
      public finishOrderByRule_resultStandardScheme getScheme() {
        return new finishOrderByRule_resultStandardScheme();
      }
    }

    private static class finishOrderByRule_resultStandardScheme extends StandardScheme<finishOrderByRule_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, finishOrderByRule_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new OrderResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, finishOrderByRule_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class finishOrderByRule_resultTupleSchemeFactory implements SchemeFactory {
      public finishOrderByRule_resultTupleScheme getScheme() {
        return new finishOrderByRule_resultTupleScheme();
      }
    }

    private static class finishOrderByRule_resultTupleScheme extends TupleScheme<finishOrderByRule_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, finishOrderByRule_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, finishOrderByRule_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new OrderResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

}
