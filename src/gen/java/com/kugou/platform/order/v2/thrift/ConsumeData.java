/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.platform.order.v2.thrift;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2020-05-28")
public class ConsumeData implements org.apache.thrift.TBase<ConsumeData, ConsumeData._Fields>, java.io.Serializable, Cloneable, Comparable<ConsumeData> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ConsumeData");

  private static final org.apache.thrift.protocol.TField CONSUME_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("consumeId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField ORDER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("orderId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField CONSUME_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("consumeType", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField CONSUME_ITEM_FIELD_DESC = new org.apache.thrift.protocol.TField("consumeItem", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField CONSUME_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("consumeNum", org.apache.thrift.protocol.TType.STRING, (short)6);
  private static final org.apache.thrift.protocol.TField REAL_CONSUME_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("realConsumeNum", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("status", org.apache.thrift.protocol.TType.I32, (short)8);
  private static final org.apache.thrift.protocol.TField ADD_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("addTime", org.apache.thrift.protocol.TType.I64, (short)9);
  private static final org.apache.thrift.protocol.TField UPDATE_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("updateTime", org.apache.thrift.protocol.TType.I64, (short)10);
  private static final org.apache.thrift.protocol.TField EXT_FIELD_DESC = new org.apache.thrift.protocol.TField("ext", org.apache.thrift.protocol.TType.STRING, (short)11);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new ConsumeDataStandardSchemeFactory());
    schemes.put(TupleScheme.class, new ConsumeDataTupleSchemeFactory());
  }

  /**
   * 支付id
   * 
   */
  public long consumeId; // required
  /**
   * 父订单id
   * 
   */
  public long orderId; // required
  public long kugouId; // required
  /**
   * 消费类型(1:星币支付，2:仓库出仓，3：虑拟仓库出仓，9:不支付)
   * 
   */
  public int consumeType; // required
  public long consumeItem; // optional
  /**
   * 支付数量
   * 
   */
  public String consumeNum; // optional
  /**
   * 真实支付数量(解冻情况会跟consumeNum不一致)
   * 
   */
  public String realConsumeNum; // optional
  /**
   * 支付状态，0：未完成，1：成功，2：失败，3：异常
   * 
   */
  public int status; // required
  /**
   * 创建时间
   * 
   */
  public long addTime; // required
  /**
   * 更新时间
   * 
   */
  public long updateTime; // required
  /**
   * 扩展信息
   * 
   */
  public String ext; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 支付id
     * 
     */
    CONSUME_ID((short)1, "consumeId"),
    /**
     * 父订单id
     * 
     */
    ORDER_ID((short)2, "orderId"),
    KUGOU_ID((short)3, "kugouId"),
    /**
     * 消费类型(1:星币支付，2:仓库出仓，3：虑拟仓库出仓，9:不支付)
     * 
     */
    CONSUME_TYPE((short)4, "consumeType"),
    CONSUME_ITEM((short)5, "consumeItem"),
    /**
     * 支付数量
     * 
     */
    CONSUME_NUM((short)6, "consumeNum"),
    /**
     * 真实支付数量(解冻情况会跟consumeNum不一致)
     * 
     */
    REAL_CONSUME_NUM((short)7, "realConsumeNum"),
    /**
     * 支付状态，0：未完成，1：成功，2：失败，3：异常
     * 
     */
    STATUS((short)8, "status"),
    /**
     * 创建时间
     * 
     */
    ADD_TIME((short)9, "addTime"),
    /**
     * 更新时间
     * 
     */
    UPDATE_TIME((short)10, "updateTime"),
    /**
     * 扩展信息
     * 
     */
    EXT((short)11, "ext");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // CONSUME_ID
          return CONSUME_ID;
        case 2: // ORDER_ID
          return ORDER_ID;
        case 3: // KUGOU_ID
          return KUGOU_ID;
        case 4: // CONSUME_TYPE
          return CONSUME_TYPE;
        case 5: // CONSUME_ITEM
          return CONSUME_ITEM;
        case 6: // CONSUME_NUM
          return CONSUME_NUM;
        case 7: // REAL_CONSUME_NUM
          return REAL_CONSUME_NUM;
        case 8: // STATUS
          return STATUS;
        case 9: // ADD_TIME
          return ADD_TIME;
        case 10: // UPDATE_TIME
          return UPDATE_TIME;
        case 11: // EXT
          return EXT;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __CONSUMEID_ISSET_ID = 0;
  private static final int __ORDERID_ISSET_ID = 1;
  private static final int __KUGOUID_ISSET_ID = 2;
  private static final int __CONSUMETYPE_ISSET_ID = 3;
  private static final int __CONSUMEITEM_ISSET_ID = 4;
  private static final int __STATUS_ISSET_ID = 5;
  private static final int __ADDTIME_ISSET_ID = 6;
  private static final int __UPDATETIME_ISSET_ID = 7;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.CONSUME_ITEM,_Fields.CONSUME_NUM,_Fields.REAL_CONSUME_NUM,_Fields.EXT};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.CONSUME_ID, new org.apache.thrift.meta_data.FieldMetaData("consumeId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ORDER_ID, new org.apache.thrift.meta_data.FieldMetaData("orderId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CONSUME_TYPE, new org.apache.thrift.meta_data.FieldMetaData("consumeType", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.CONSUME_ITEM, new org.apache.thrift.meta_data.FieldMetaData("consumeItem", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CONSUME_NUM, new org.apache.thrift.meta_data.FieldMetaData("consumeNum", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.REAL_CONSUME_NUM, new org.apache.thrift.meta_data.FieldMetaData("realConsumeNum", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.STATUS, new org.apache.thrift.meta_data.FieldMetaData("status", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ADD_TIME, new org.apache.thrift.meta_data.FieldMetaData("addTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.UPDATE_TIME, new org.apache.thrift.meta_data.FieldMetaData("updateTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXT, new org.apache.thrift.meta_data.FieldMetaData("ext", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ConsumeData.class, metaDataMap);
  }

  public ConsumeData() {
  }

  public ConsumeData(
    long consumeId,
    long orderId,
    long kugouId,
    int consumeType,
    int status,
    long addTime,
    long updateTime)
  {
    this();
    this.consumeId = consumeId;
    setConsumeIdIsSet(true);
    this.orderId = orderId;
    setOrderIdIsSet(true);
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    this.consumeType = consumeType;
    setConsumeTypeIsSet(true);
    this.status = status;
    setStatusIsSet(true);
    this.addTime = addTime;
    setAddTimeIsSet(true);
    this.updateTime = updateTime;
    setUpdateTimeIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public ConsumeData(ConsumeData other) {
    __isset_bitfield = other.__isset_bitfield;
    this.consumeId = other.consumeId;
    this.orderId = other.orderId;
    this.kugouId = other.kugouId;
    this.consumeType = other.consumeType;
    this.consumeItem = other.consumeItem;
    if (other.isSetConsumeNum()) {
      this.consumeNum = other.consumeNum;
    }
    if (other.isSetRealConsumeNum()) {
      this.realConsumeNum = other.realConsumeNum;
    }
    this.status = other.status;
    this.addTime = other.addTime;
    this.updateTime = other.updateTime;
    if (other.isSetExt()) {
      this.ext = other.ext;
    }
  }

  public ConsumeData deepCopy() {
    return new ConsumeData(this);
  }

  @Override
  public void clear() {
    setConsumeIdIsSet(false);
    this.consumeId = 0;
    setOrderIdIsSet(false);
    this.orderId = 0;
    setKugouIdIsSet(false);
    this.kugouId = 0;
    setConsumeTypeIsSet(false);
    this.consumeType = 0;
    setConsumeItemIsSet(false);
    this.consumeItem = 0;
    this.consumeNum = null;
    this.realConsumeNum = null;
    setStatusIsSet(false);
    this.status = 0;
    setAddTimeIsSet(false);
    this.addTime = 0;
    setUpdateTimeIsSet(false);
    this.updateTime = 0;
    this.ext = null;
  }

  /**
   * 支付id
   * 
   */
  public long getConsumeId() {
    return this.consumeId;
  }

  /**
   * 支付id
   * 
   */
  public ConsumeData setConsumeId(long consumeId) {
    this.consumeId = consumeId;
    setConsumeIdIsSet(true);
    return this;
  }

  public void unsetConsumeId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CONSUMEID_ISSET_ID);
  }

  /** Returns true if field consumeId is set (has been assigned a value) and false otherwise */
  public boolean isSetConsumeId() {
    return EncodingUtils.testBit(__isset_bitfield, __CONSUMEID_ISSET_ID);
  }

  public void setConsumeIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CONSUMEID_ISSET_ID, value);
  }

  /**
   * 父订单id
   * 
   */
  public long getOrderId() {
    return this.orderId;
  }

  /**
   * 父订单id
   * 
   */
  public ConsumeData setOrderId(long orderId) {
    this.orderId = orderId;
    setOrderIdIsSet(true);
    return this;
  }

  public void unsetOrderId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ORDERID_ISSET_ID);
  }

  /** Returns true if field orderId is set (has been assigned a value) and false otherwise */
  public boolean isSetOrderId() {
    return EncodingUtils.testBit(__isset_bitfield, __ORDERID_ISSET_ID);
  }

  public void setOrderIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ORDERID_ISSET_ID, value);
  }

  public long getKugouId() {
    return this.kugouId;
  }

  public ConsumeData setKugouId(long kugouId) {
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    return this;
  }

  public void unsetKugouId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
  public boolean isSetKugouId() {
    return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  public void setKugouIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
  }

  /**
   * 消费类型(1:星币支付，2:仓库出仓，3：虑拟仓库出仓，9:不支付)
   * 
   */
  public int getConsumeType() {
    return this.consumeType;
  }

  /**
   * 消费类型(1:星币支付，2:仓库出仓，3：虑拟仓库出仓，9:不支付)
   * 
   */
  public ConsumeData setConsumeType(int consumeType) {
    this.consumeType = consumeType;
    setConsumeTypeIsSet(true);
    return this;
  }

  public void unsetConsumeType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CONSUMETYPE_ISSET_ID);
  }

  /** Returns true if field consumeType is set (has been assigned a value) and false otherwise */
  public boolean isSetConsumeType() {
    return EncodingUtils.testBit(__isset_bitfield, __CONSUMETYPE_ISSET_ID);
  }

  public void setConsumeTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CONSUMETYPE_ISSET_ID, value);
  }

  public long getConsumeItem() {
    return this.consumeItem;
  }

  public ConsumeData setConsumeItem(long consumeItem) {
    this.consumeItem = consumeItem;
    setConsumeItemIsSet(true);
    return this;
  }

  public void unsetConsumeItem() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CONSUMEITEM_ISSET_ID);
  }

  /** Returns true if field consumeItem is set (has been assigned a value) and false otherwise */
  public boolean isSetConsumeItem() {
    return EncodingUtils.testBit(__isset_bitfield, __CONSUMEITEM_ISSET_ID);
  }

  public void setConsumeItemIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CONSUMEITEM_ISSET_ID, value);
  }

  /**
   * 支付数量
   * 
   */
  public String getConsumeNum() {
    return this.consumeNum;
  }

  /**
   * 支付数量
   * 
   */
  public ConsumeData setConsumeNum(String consumeNum) {
    this.consumeNum = consumeNum;
    return this;
  }

  public void unsetConsumeNum() {
    this.consumeNum = null;
  }

  /** Returns true if field consumeNum is set (has been assigned a value) and false otherwise */
  public boolean isSetConsumeNum() {
    return this.consumeNum != null;
  }

  public void setConsumeNumIsSet(boolean value) {
    if (!value) {
      this.consumeNum = null;
    }
  }

  /**
   * 真实支付数量(解冻情况会跟consumeNum不一致)
   * 
   */
  public String getRealConsumeNum() {
    return this.realConsumeNum;
  }

  /**
   * 真实支付数量(解冻情况会跟consumeNum不一致)
   * 
   */
  public ConsumeData setRealConsumeNum(String realConsumeNum) {
    this.realConsumeNum = realConsumeNum;
    return this;
  }

  public void unsetRealConsumeNum() {
    this.realConsumeNum = null;
  }

  /** Returns true if field realConsumeNum is set (has been assigned a value) and false otherwise */
  public boolean isSetRealConsumeNum() {
    return this.realConsumeNum != null;
  }

  public void setRealConsumeNumIsSet(boolean value) {
    if (!value) {
      this.realConsumeNum = null;
    }
  }

  /**
   * 支付状态，0：未完成，1：成功，2：失败，3：异常
   * 
   */
  public int getStatus() {
    return this.status;
  }

  /**
   * 支付状态，0：未完成，1：成功，2：失败，3：异常
   * 
   */
  public ConsumeData setStatus(int status) {
    this.status = status;
    setStatusIsSet(true);
    return this;
  }

  public void unsetStatus() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  /** Returns true if field status is set (has been assigned a value) and false otherwise */
  public boolean isSetStatus() {
    return EncodingUtils.testBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  public void setStatusIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STATUS_ISSET_ID, value);
  }

  /**
   * 创建时间
   * 
   */
  public long getAddTime() {
    return this.addTime;
  }

  /**
   * 创建时间
   * 
   */
  public ConsumeData setAddTime(long addTime) {
    this.addTime = addTime;
    setAddTimeIsSet(true);
    return this;
  }

  public void unsetAddTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ADDTIME_ISSET_ID);
  }

  /** Returns true if field addTime is set (has been assigned a value) and false otherwise */
  public boolean isSetAddTime() {
    return EncodingUtils.testBit(__isset_bitfield, __ADDTIME_ISSET_ID);
  }

  public void setAddTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ADDTIME_ISSET_ID, value);
  }

  /**
   * 更新时间
   * 
   */
  public long getUpdateTime() {
    return this.updateTime;
  }

  /**
   * 更新时间
   * 
   */
  public ConsumeData setUpdateTime(long updateTime) {
    this.updateTime = updateTime;
    setUpdateTimeIsSet(true);
    return this;
  }

  public void unsetUpdateTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __UPDATETIME_ISSET_ID);
  }

  /** Returns true if field updateTime is set (has been assigned a value) and false otherwise */
  public boolean isSetUpdateTime() {
    return EncodingUtils.testBit(__isset_bitfield, __UPDATETIME_ISSET_ID);
  }

  public void setUpdateTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __UPDATETIME_ISSET_ID, value);
  }

  /**
   * 扩展信息
   * 
   */
  public String getExt() {
    return this.ext;
  }

  /**
   * 扩展信息
   * 
   */
  public ConsumeData setExt(String ext) {
    this.ext = ext;
    return this;
  }

  public void unsetExt() {
    this.ext = null;
  }

  /** Returns true if field ext is set (has been assigned a value) and false otherwise */
  public boolean isSetExt() {
    return this.ext != null;
  }

  public void setExtIsSet(boolean value) {
    if (!value) {
      this.ext = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case CONSUME_ID:
      if (value == null) {
        unsetConsumeId();
      } else {
        setConsumeId((Long)value);
      }
      break;

    case ORDER_ID:
      if (value == null) {
        unsetOrderId();
      } else {
        setOrderId((Long)value);
      }
      break;

    case KUGOU_ID:
      if (value == null) {
        unsetKugouId();
      } else {
        setKugouId((Long)value);
      }
      break;

    case CONSUME_TYPE:
      if (value == null) {
        unsetConsumeType();
      } else {
        setConsumeType((Integer)value);
      }
      break;

    case CONSUME_ITEM:
      if (value == null) {
        unsetConsumeItem();
      } else {
        setConsumeItem((Long)value);
      }
      break;

    case CONSUME_NUM:
      if (value == null) {
        unsetConsumeNum();
      } else {
        setConsumeNum((String)value);
      }
      break;

    case REAL_CONSUME_NUM:
      if (value == null) {
        unsetRealConsumeNum();
      } else {
        setRealConsumeNum((String)value);
      }
      break;

    case STATUS:
      if (value == null) {
        unsetStatus();
      } else {
        setStatus((Integer)value);
      }
      break;

    case ADD_TIME:
      if (value == null) {
        unsetAddTime();
      } else {
        setAddTime((Long)value);
      }
      break;

    case UPDATE_TIME:
      if (value == null) {
        unsetUpdateTime();
      } else {
        setUpdateTime((Long)value);
      }
      break;

    case EXT:
      if (value == null) {
        unsetExt();
      } else {
        setExt((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case CONSUME_ID:
      return getConsumeId();

    case ORDER_ID:
      return getOrderId();

    case KUGOU_ID:
      return getKugouId();

    case CONSUME_TYPE:
      return getConsumeType();

    case CONSUME_ITEM:
      return getConsumeItem();

    case CONSUME_NUM:
      return getConsumeNum();

    case REAL_CONSUME_NUM:
      return getRealConsumeNum();

    case STATUS:
      return getStatus();

    case ADD_TIME:
      return getAddTime();

    case UPDATE_TIME:
      return getUpdateTime();

    case EXT:
      return getExt();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case CONSUME_ID:
      return isSetConsumeId();
    case ORDER_ID:
      return isSetOrderId();
    case KUGOU_ID:
      return isSetKugouId();
    case CONSUME_TYPE:
      return isSetConsumeType();
    case CONSUME_ITEM:
      return isSetConsumeItem();
    case CONSUME_NUM:
      return isSetConsumeNum();
    case REAL_CONSUME_NUM:
      return isSetRealConsumeNum();
    case STATUS:
      return isSetStatus();
    case ADD_TIME:
      return isSetAddTime();
    case UPDATE_TIME:
      return isSetUpdateTime();
    case EXT:
      return isSetExt();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof ConsumeData)
      return this.equals((ConsumeData)that);
    return false;
  }

  public boolean equals(ConsumeData that) {
    if (that == null)
      return false;

    boolean this_present_consumeId = true;
    boolean that_present_consumeId = true;
    if (this_present_consumeId || that_present_consumeId) {
      if (!(this_present_consumeId && that_present_consumeId))
        return false;
      if (this.consumeId != that.consumeId)
        return false;
    }

    boolean this_present_orderId = true;
    boolean that_present_orderId = true;
    if (this_present_orderId || that_present_orderId) {
      if (!(this_present_orderId && that_present_orderId))
        return false;
      if (this.orderId != that.orderId)
        return false;
    }

    boolean this_present_kugouId = true;
    boolean that_present_kugouId = true;
    if (this_present_kugouId || that_present_kugouId) {
      if (!(this_present_kugouId && that_present_kugouId))
        return false;
      if (this.kugouId != that.kugouId)
        return false;
    }

    boolean this_present_consumeType = true;
    boolean that_present_consumeType = true;
    if (this_present_consumeType || that_present_consumeType) {
      if (!(this_present_consumeType && that_present_consumeType))
        return false;
      if (this.consumeType != that.consumeType)
        return false;
    }

    boolean this_present_consumeItem = true && this.isSetConsumeItem();
    boolean that_present_consumeItem = true && that.isSetConsumeItem();
    if (this_present_consumeItem || that_present_consumeItem) {
      if (!(this_present_consumeItem && that_present_consumeItem))
        return false;
      if (this.consumeItem != that.consumeItem)
        return false;
    }

    boolean this_present_consumeNum = true && this.isSetConsumeNum();
    boolean that_present_consumeNum = true && that.isSetConsumeNum();
    if (this_present_consumeNum || that_present_consumeNum) {
      if (!(this_present_consumeNum && that_present_consumeNum))
        return false;
      if (!this.consumeNum.equals(that.consumeNum))
        return false;
    }

    boolean this_present_realConsumeNum = true && this.isSetRealConsumeNum();
    boolean that_present_realConsumeNum = true && that.isSetRealConsumeNum();
    if (this_present_realConsumeNum || that_present_realConsumeNum) {
      if (!(this_present_realConsumeNum && that_present_realConsumeNum))
        return false;
      if (!this.realConsumeNum.equals(that.realConsumeNum))
        return false;
    }

    boolean this_present_status = true;
    boolean that_present_status = true;
    if (this_present_status || that_present_status) {
      if (!(this_present_status && that_present_status))
        return false;
      if (this.status != that.status)
        return false;
    }

    boolean this_present_addTime = true;
    boolean that_present_addTime = true;
    if (this_present_addTime || that_present_addTime) {
      if (!(this_present_addTime && that_present_addTime))
        return false;
      if (this.addTime != that.addTime)
        return false;
    }

    boolean this_present_updateTime = true;
    boolean that_present_updateTime = true;
    if (this_present_updateTime || that_present_updateTime) {
      if (!(this_present_updateTime && that_present_updateTime))
        return false;
      if (this.updateTime != that.updateTime)
        return false;
    }

    boolean this_present_ext = true && this.isSetExt();
    boolean that_present_ext = true && that.isSetExt();
    if (this_present_ext || that_present_ext) {
      if (!(this_present_ext && that_present_ext))
        return false;
      if (!this.ext.equals(that.ext))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_consumeId = true;
    list.add(present_consumeId);
    if (present_consumeId)
      list.add(consumeId);

    boolean present_orderId = true;
    list.add(present_orderId);
    if (present_orderId)
      list.add(orderId);

    boolean present_kugouId = true;
    list.add(present_kugouId);
    if (present_kugouId)
      list.add(kugouId);

    boolean present_consumeType = true;
    list.add(present_consumeType);
    if (present_consumeType)
      list.add(consumeType);

    boolean present_consumeItem = true && (isSetConsumeItem());
    list.add(present_consumeItem);
    if (present_consumeItem)
      list.add(consumeItem);

    boolean present_consumeNum = true && (isSetConsumeNum());
    list.add(present_consumeNum);
    if (present_consumeNum)
      list.add(consumeNum);

    boolean present_realConsumeNum = true && (isSetRealConsumeNum());
    list.add(present_realConsumeNum);
    if (present_realConsumeNum)
      list.add(realConsumeNum);

    boolean present_status = true;
    list.add(present_status);
    if (present_status)
      list.add(status);

    boolean present_addTime = true;
    list.add(present_addTime);
    if (present_addTime)
      list.add(addTime);

    boolean present_updateTime = true;
    list.add(present_updateTime);
    if (present_updateTime)
      list.add(updateTime);

    boolean present_ext = true && (isSetExt());
    list.add(present_ext);
    if (present_ext)
      list.add(ext);

    return list.hashCode();
  }

  @Override
  public int compareTo(ConsumeData other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetConsumeId()).compareTo(other.isSetConsumeId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetConsumeId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.consumeId, other.consumeId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetOrderId()).compareTo(other.isSetOrderId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOrderId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderId, other.orderId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKugouId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetConsumeType()).compareTo(other.isSetConsumeType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetConsumeType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.consumeType, other.consumeType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetConsumeItem()).compareTo(other.isSetConsumeItem());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetConsumeItem()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.consumeItem, other.consumeItem);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetConsumeNum()).compareTo(other.isSetConsumeNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetConsumeNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.consumeNum, other.consumeNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRealConsumeNum()).compareTo(other.isSetRealConsumeNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRealConsumeNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.realConsumeNum, other.realConsumeNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetStatus()).compareTo(other.isSetStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.status, other.status);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAddTime()).compareTo(other.isSetAddTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAddTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.addTime, other.addTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUpdateTime()).compareTo(other.isSetUpdateTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUpdateTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.updateTime, other.updateTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExt()).compareTo(other.isSetExt());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExt()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ext, other.ext);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("ConsumeData(");
    boolean first = true;

    sb.append("consumeId:");
    sb.append(this.consumeId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("orderId:");
    sb.append(this.orderId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("kugouId:");
    sb.append(this.kugouId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("consumeType:");
    sb.append(this.consumeType);
    first = false;
    if (isSetConsumeItem()) {
      if (!first) sb.append(", ");
      sb.append("consumeItem:");
      sb.append(this.consumeItem);
      first = false;
    }
    if (isSetConsumeNum()) {
      if (!first) sb.append(", ");
      sb.append("consumeNum:");
      if (this.consumeNum == null) {
        sb.append("null");
      } else {
        sb.append(this.consumeNum);
      }
      first = false;
    }
    if (isSetRealConsumeNum()) {
      if (!first) sb.append(", ");
      sb.append("realConsumeNum:");
      if (this.realConsumeNum == null) {
        sb.append("null");
      } else {
        sb.append(this.realConsumeNum);
      }
      first = false;
    }
    if (!first) sb.append(", ");
    sb.append("status:");
    sb.append(this.status);
    first = false;
    if (!first) sb.append(", ");
    sb.append("addTime:");
    sb.append(this.addTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("updateTime:");
    sb.append(this.updateTime);
    first = false;
    if (isSetExt()) {
      if (!first) sb.append(", ");
      sb.append("ext:");
      if (this.ext == null) {
        sb.append("null");
      } else {
        sb.append(this.ext);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'consumeId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'orderId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'consumeType' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'status' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'addTime' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'updateTime' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class ConsumeDataStandardSchemeFactory implements SchemeFactory {
    public ConsumeDataStandardScheme getScheme() {
      return new ConsumeDataStandardScheme();
    }
  }

  private static class ConsumeDataStandardScheme extends StandardScheme<ConsumeData> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, ConsumeData struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // CONSUME_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.consumeId = iprot.readI64();
              struct.setConsumeIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ORDER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.orderId = iprot.readI64();
              struct.setOrderIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // KUGOU_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.kugouId = iprot.readI64();
              struct.setKugouIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // CONSUME_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.consumeType = iprot.readI32();
              struct.setConsumeTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // CONSUME_ITEM
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.consumeItem = iprot.readI64();
              struct.setConsumeItemIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // CONSUME_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.consumeNum = iprot.readString();
              struct.setConsumeNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // REAL_CONSUME_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.realConsumeNum = iprot.readString();
              struct.setRealConsumeNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.status = iprot.readI32();
              struct.setStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // ADD_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.addTime = iprot.readI64();
              struct.setAddTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // UPDATE_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.updateTime = iprot.readI64();
              struct.setUpdateTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // EXT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.ext = iprot.readString();
              struct.setExtIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetConsumeId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'consumeId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetOrderId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetKugouId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetConsumeType()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'consumeType' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetStatus()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'status' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetAddTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'addTime' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetUpdateTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'updateTime' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, ConsumeData struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(CONSUME_ID_FIELD_DESC);
      oprot.writeI64(struct.consumeId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ORDER_ID_FIELD_DESC);
      oprot.writeI64(struct.orderId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
      oprot.writeI64(struct.kugouId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(CONSUME_TYPE_FIELD_DESC);
      oprot.writeI32(struct.consumeType);
      oprot.writeFieldEnd();
      if (struct.isSetConsumeItem()) {
        oprot.writeFieldBegin(CONSUME_ITEM_FIELD_DESC);
        oprot.writeI64(struct.consumeItem);
        oprot.writeFieldEnd();
      }
      if (struct.consumeNum != null) {
        if (struct.isSetConsumeNum()) {
          oprot.writeFieldBegin(CONSUME_NUM_FIELD_DESC);
          oprot.writeString(struct.consumeNum);
          oprot.writeFieldEnd();
        }
      }
      if (struct.realConsumeNum != null) {
        if (struct.isSetRealConsumeNum()) {
          oprot.writeFieldBegin(REAL_CONSUME_NUM_FIELD_DESC);
          oprot.writeString(struct.realConsumeNum);
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldBegin(STATUS_FIELD_DESC);
      oprot.writeI32(struct.status);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ADD_TIME_FIELD_DESC);
      oprot.writeI64(struct.addTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(UPDATE_TIME_FIELD_DESC);
      oprot.writeI64(struct.updateTime);
      oprot.writeFieldEnd();
      if (struct.ext != null) {
        if (struct.isSetExt()) {
          oprot.writeFieldBegin(EXT_FIELD_DESC);
          oprot.writeString(struct.ext);
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class ConsumeDataTupleSchemeFactory implements SchemeFactory {
    public ConsumeDataTupleScheme getScheme() {
      return new ConsumeDataTupleScheme();
    }
  }

  private static class ConsumeDataTupleScheme extends TupleScheme<ConsumeData> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, ConsumeData struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeI64(struct.consumeId);
      oprot.writeI64(struct.orderId);
      oprot.writeI64(struct.kugouId);
      oprot.writeI32(struct.consumeType);
      oprot.writeI32(struct.status);
      oprot.writeI64(struct.addTime);
      oprot.writeI64(struct.updateTime);
      BitSet optionals = new BitSet();
      if (struct.isSetConsumeItem()) {
        optionals.set(0);
      }
      if (struct.isSetConsumeNum()) {
        optionals.set(1);
      }
      if (struct.isSetRealConsumeNum()) {
        optionals.set(2);
      }
      if (struct.isSetExt()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetConsumeItem()) {
        oprot.writeI64(struct.consumeItem);
      }
      if (struct.isSetConsumeNum()) {
        oprot.writeString(struct.consumeNum);
      }
      if (struct.isSetRealConsumeNum()) {
        oprot.writeString(struct.realConsumeNum);
      }
      if (struct.isSetExt()) {
        oprot.writeString(struct.ext);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, ConsumeData struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.consumeId = iprot.readI64();
      struct.setConsumeIdIsSet(true);
      struct.orderId = iprot.readI64();
      struct.setOrderIdIsSet(true);
      struct.kugouId = iprot.readI64();
      struct.setKugouIdIsSet(true);
      struct.consumeType = iprot.readI32();
      struct.setConsumeTypeIsSet(true);
      struct.status = iprot.readI32();
      struct.setStatusIsSet(true);
      struct.addTime = iprot.readI64();
      struct.setAddTimeIsSet(true);
      struct.updateTime = iprot.readI64();
      struct.setUpdateTimeIsSet(true);
      BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.consumeItem = iprot.readI64();
        struct.setConsumeItemIsSet(true);
      }
      if (incoming.get(1)) {
        struct.consumeNum = iprot.readString();
        struct.setConsumeNumIsSet(true);
      }
      if (incoming.get(2)) {
        struct.realConsumeNum = iprot.readString();
        struct.setRealConsumeNumIsSet(true);
      }
      if (incoming.get(3)) {
        struct.ext = iprot.readString();
        struct.setExtIsSet(true);
      }
    }
  }

}

