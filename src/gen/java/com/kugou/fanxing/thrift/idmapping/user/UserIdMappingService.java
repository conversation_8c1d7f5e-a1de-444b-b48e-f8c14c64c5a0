/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.thrift.idmapping.user;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2019-10-21")
public class UserIdMappingService {

  public interface Iface {

    public Map<Long,Long> getUserIdMappingByKugouIds(List<Long> kugouIds) throws org.apache.thrift.TException;

    public Map<Long,Long> getKugouIdMappingByUserIds(List<Long> userIds) throws org.apache.thrift.TException;

    public long getUserIdByKugouId(long kugouId) throws org.apache.thrift.TException;

    public long getKugouIdByUserId(long userId) throws org.apache.thrift.TException;

    public int addUserIdMapping(long kugouId, long userId) throws org.apache.thrift.TException;

  }

  public interface AsyncIface {

    public void getUserIdMappingByKugouIds(List<Long> kugouIds, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getKugouIdMappingByUserIds(List<Long> userIds, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUserIdByKugouId(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getKugouIdByUserId(long userId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void addUserIdMapping(long kugouId, long userId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

  }

  public static class Client extends org.apache.thrift.TServiceClient implements Iface {
    public static class Factory implements org.apache.thrift.TServiceClientFactory<Client> {
      public Factory() {}
      public Client getClient(org.apache.thrift.protocol.TProtocol prot) {
        return new Client(prot);
      }
      public Client getClient(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
        return new Client(iprot, oprot);
      }
    }

    public Client(org.apache.thrift.protocol.TProtocol prot)
    {
      super(prot, prot);
    }

    public Client(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
      super(iprot, oprot);
    }

    public Map<Long,Long> getUserIdMappingByKugouIds(List<Long> kugouIds) throws org.apache.thrift.TException
    {
      send_getUserIdMappingByKugouIds(kugouIds);
      return recv_getUserIdMappingByKugouIds();
    }

    public void send_getUserIdMappingByKugouIds(List<Long> kugouIds) throws org.apache.thrift.TException
    {
      getUserIdMappingByKugouIds_args args = new getUserIdMappingByKugouIds_args();
      args.setKugouIds(kugouIds);
      sendBase("getUserIdMappingByKugouIds", args);
    }

    public Map<Long,Long> recv_getUserIdMappingByKugouIds() throws org.apache.thrift.TException
    {
      getUserIdMappingByKugouIds_result result = new getUserIdMappingByKugouIds_result();
      receiveBase(result, "getUserIdMappingByKugouIds");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUserIdMappingByKugouIds failed: unknown result");
    }

    public Map<Long,Long> getKugouIdMappingByUserIds(List<Long> userIds) throws org.apache.thrift.TException
    {
      send_getKugouIdMappingByUserIds(userIds);
      return recv_getKugouIdMappingByUserIds();
    }

    public void send_getKugouIdMappingByUserIds(List<Long> userIds) throws org.apache.thrift.TException
    {
      getKugouIdMappingByUserIds_args args = new getKugouIdMappingByUserIds_args();
      args.setUserIds(userIds);
      sendBase("getKugouIdMappingByUserIds", args);
    }

    public Map<Long,Long> recv_getKugouIdMappingByUserIds() throws org.apache.thrift.TException
    {
      getKugouIdMappingByUserIds_result result = new getKugouIdMappingByUserIds_result();
      receiveBase(result, "getKugouIdMappingByUserIds");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getKugouIdMappingByUserIds failed: unknown result");
    }

    public long getUserIdByKugouId(long kugouId) throws org.apache.thrift.TException
    {
      send_getUserIdByKugouId(kugouId);
      return recv_getUserIdByKugouId();
    }

    public void send_getUserIdByKugouId(long kugouId) throws org.apache.thrift.TException
    {
      getUserIdByKugouId_args args = new getUserIdByKugouId_args();
      args.setKugouId(kugouId);
      sendBase("getUserIdByKugouId", args);
    }

    public long recv_getUserIdByKugouId() throws org.apache.thrift.TException
    {
      getUserIdByKugouId_result result = new getUserIdByKugouId_result();
      receiveBase(result, "getUserIdByKugouId");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUserIdByKugouId failed: unknown result");
    }

    public long getKugouIdByUserId(long userId) throws org.apache.thrift.TException
    {
      send_getKugouIdByUserId(userId);
      return recv_getKugouIdByUserId();
    }

    public void send_getKugouIdByUserId(long userId) throws org.apache.thrift.TException
    {
      getKugouIdByUserId_args args = new getKugouIdByUserId_args();
      args.setUserId(userId);
      sendBase("getKugouIdByUserId", args);
    }

    public long recv_getKugouIdByUserId() throws org.apache.thrift.TException
    {
      getKugouIdByUserId_result result = new getKugouIdByUserId_result();
      receiveBase(result, "getKugouIdByUserId");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getKugouIdByUserId failed: unknown result");
    }

    public int addUserIdMapping(long kugouId, long userId) throws org.apache.thrift.TException
    {
      send_addUserIdMapping(kugouId, userId);
      return recv_addUserIdMapping();
    }

    public void send_addUserIdMapping(long kugouId, long userId) throws org.apache.thrift.TException
    {
      addUserIdMapping_args args = new addUserIdMapping_args();
      args.setKugouId(kugouId);
      args.setUserId(userId);
      sendBase("addUserIdMapping", args);
    }

    public int recv_addUserIdMapping() throws org.apache.thrift.TException
    {
      addUserIdMapping_result result = new addUserIdMapping_result();
      receiveBase(result, "addUserIdMapping");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "addUserIdMapping failed: unknown result");
    }

  }
  public static class AsyncClient extends org.apache.thrift.async.TAsyncClient implements AsyncIface {
    public static class Factory implements org.apache.thrift.async.TAsyncClientFactory<AsyncClient> {
      private org.apache.thrift.async.TAsyncClientManager clientManager;
      private org.apache.thrift.protocol.TProtocolFactory protocolFactory;
      public Factory(org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.protocol.TProtocolFactory protocolFactory) {
        this.clientManager = clientManager;
        this.protocolFactory = protocolFactory;
      }
      public AsyncClient getAsyncClient(org.apache.thrift.transport.TNonblockingTransport transport) {
        return new AsyncClient(protocolFactory, clientManager, transport);
      }
    }

    public AsyncClient(org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.transport.TNonblockingTransport transport) {
      super(protocolFactory, clientManager, transport);
    }

    public void getUserIdMappingByKugouIds(List<Long> kugouIds, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUserIdMappingByKugouIds_call method_call = new getUserIdMappingByKugouIds_call(kugouIds, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUserIdMappingByKugouIds_call extends org.apache.thrift.async.TAsyncMethodCall {
      private List<Long> kugouIds;
      public getUserIdMappingByKugouIds_call(List<Long> kugouIds, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouIds = kugouIds;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUserIdMappingByKugouIds", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUserIdMappingByKugouIds_args args = new getUserIdMappingByKugouIds_args();
        args.setKugouIds(kugouIds);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public Map<Long,Long> getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUserIdMappingByKugouIds();
      }
    }

    public void getKugouIdMappingByUserIds(List<Long> userIds, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getKugouIdMappingByUserIds_call method_call = new getKugouIdMappingByUserIds_call(userIds, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getKugouIdMappingByUserIds_call extends org.apache.thrift.async.TAsyncMethodCall {
      private List<Long> userIds;
      public getKugouIdMappingByUserIds_call(List<Long> userIds, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.userIds = userIds;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getKugouIdMappingByUserIds", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getKugouIdMappingByUserIds_args args = new getKugouIdMappingByUserIds_args();
        args.setUserIds(userIds);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public Map<Long,Long> getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getKugouIdMappingByUserIds();
      }
    }

    public void getUserIdByKugouId(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUserIdByKugouId_call method_call = new getUserIdByKugouId_call(kugouId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUserIdByKugouId_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      public getUserIdByKugouId_call(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUserIdByKugouId", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUserIdByKugouId_args args = new getUserIdByKugouId_args();
        args.setKugouId(kugouId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public long getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUserIdByKugouId();
      }
    }

    public void getKugouIdByUserId(long userId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getKugouIdByUserId_call method_call = new getKugouIdByUserId_call(userId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getKugouIdByUserId_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long userId;
      public getKugouIdByUserId_call(long userId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.userId = userId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getKugouIdByUserId", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getKugouIdByUserId_args args = new getKugouIdByUserId_args();
        args.setUserId(userId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public long getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getKugouIdByUserId();
      }
    }

    public void addUserIdMapping(long kugouId, long userId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      addUserIdMapping_call method_call = new addUserIdMapping_call(kugouId, userId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class addUserIdMapping_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      private long userId;
      public addUserIdMapping_call(long kugouId, long userId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
        this.userId = userId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("addUserIdMapping", org.apache.thrift.protocol.TMessageType.CALL, 0));
        addUserIdMapping_args args = new addUserIdMapping_args();
        args.setKugouId(kugouId);
        args.setUserId(userId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public int getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_addUserIdMapping();
      }
    }

  }

  public static class Processor<I extends Iface> extends org.apache.thrift.TBaseProcessor<I> implements org.apache.thrift.TProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());
    public Processor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>>()));
    }

    protected Processor(I iface, Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends Iface> Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> getProcessMap(Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      processMap.put("getUserIdMappingByKugouIds", new getUserIdMappingByKugouIds());
      processMap.put("getKugouIdMappingByUserIds", new getKugouIdMappingByUserIds());
      processMap.put("getUserIdByKugouId", new getUserIdByKugouId());
      processMap.put("getKugouIdByUserId", new getKugouIdByUserId());
      processMap.put("addUserIdMapping", new addUserIdMapping());
      return processMap;
    }

    public static class getUserIdMappingByKugouIds<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUserIdMappingByKugouIds_args> {
      public getUserIdMappingByKugouIds() {
        super("getUserIdMappingByKugouIds");
      }

      public getUserIdMappingByKugouIds_args getEmptyArgsInstance() {
        return new getUserIdMappingByKugouIds_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUserIdMappingByKugouIds_result getResult(I iface, getUserIdMappingByKugouIds_args args) throws org.apache.thrift.TException {
        getUserIdMappingByKugouIds_result result = new getUserIdMappingByKugouIds_result();
        result.success = iface.getUserIdMappingByKugouIds(args.kugouIds);
        return result;
      }
    }

    public static class getKugouIdMappingByUserIds<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getKugouIdMappingByUserIds_args> {
      public getKugouIdMappingByUserIds() {
        super("getKugouIdMappingByUserIds");
      }

      public getKugouIdMappingByUserIds_args getEmptyArgsInstance() {
        return new getKugouIdMappingByUserIds_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getKugouIdMappingByUserIds_result getResult(I iface, getKugouIdMappingByUserIds_args args) throws org.apache.thrift.TException {
        getKugouIdMappingByUserIds_result result = new getKugouIdMappingByUserIds_result();
        result.success = iface.getKugouIdMappingByUserIds(args.userIds);
        return result;
      }
    }

    public static class getUserIdByKugouId<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUserIdByKugouId_args> {
      public getUserIdByKugouId() {
        super("getUserIdByKugouId");
      }

      public getUserIdByKugouId_args getEmptyArgsInstance() {
        return new getUserIdByKugouId_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUserIdByKugouId_result getResult(I iface, getUserIdByKugouId_args args) throws org.apache.thrift.TException {
        getUserIdByKugouId_result result = new getUserIdByKugouId_result();
        result.success = iface.getUserIdByKugouId(args.kugouId);
        result.setSuccessIsSet(true);
        return result;
      }
    }

    public static class getKugouIdByUserId<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getKugouIdByUserId_args> {
      public getKugouIdByUserId() {
        super("getKugouIdByUserId");
      }

      public getKugouIdByUserId_args getEmptyArgsInstance() {
        return new getKugouIdByUserId_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getKugouIdByUserId_result getResult(I iface, getKugouIdByUserId_args args) throws org.apache.thrift.TException {
        getKugouIdByUserId_result result = new getKugouIdByUserId_result();
        result.success = iface.getKugouIdByUserId(args.userId);
        result.setSuccessIsSet(true);
        return result;
      }
    }

    public static class addUserIdMapping<I extends Iface> extends org.apache.thrift.ProcessFunction<I, addUserIdMapping_args> {
      public addUserIdMapping() {
        super("addUserIdMapping");
      }

      public addUserIdMapping_args getEmptyArgsInstance() {
        return new addUserIdMapping_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public addUserIdMapping_result getResult(I iface, addUserIdMapping_args args) throws org.apache.thrift.TException {
        addUserIdMapping_result result = new addUserIdMapping_result();
        result.success = iface.addUserIdMapping(args.kugouId, args.userId);
        result.setSuccessIsSet(true);
        return result;
      }
    }

  }

  public static class AsyncProcessor<I extends AsyncIface> extends org.apache.thrift.TBaseAsyncProcessor<I> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());
    public AsyncProcessor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.AsyncProcessFunction<I, ? extends org.apache.thrift.TBase, ?>>()));
    }

    protected AsyncProcessor(I iface, Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends AsyncIface> Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase,?>> getProcessMap(Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      processMap.put("getUserIdMappingByKugouIds", new getUserIdMappingByKugouIds());
      processMap.put("getKugouIdMappingByUserIds", new getKugouIdMappingByUserIds());
      processMap.put("getUserIdByKugouId", new getUserIdByKugouId());
      processMap.put("getKugouIdByUserId", new getKugouIdByUserId());
      processMap.put("addUserIdMapping", new addUserIdMapping());
      return processMap;
    }

    public static class getUserIdMappingByKugouIds<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUserIdMappingByKugouIds_args, Map<Long,Long>> {
      public getUserIdMappingByKugouIds() {
        super("getUserIdMappingByKugouIds");
      }

      public getUserIdMappingByKugouIds_args getEmptyArgsInstance() {
        return new getUserIdMappingByKugouIds_args();
      }

      public AsyncMethodCallback<Map<Long,Long>> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<Map<Long,Long>>() { 
          public void onComplete(Map<Long,Long> o) {
            getUserIdMappingByKugouIds_result result = new getUserIdMappingByKugouIds_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUserIdMappingByKugouIds_result result = new getUserIdMappingByKugouIds_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUserIdMappingByKugouIds_args args, org.apache.thrift.async.AsyncMethodCallback<Map<Long,Long>> resultHandler) throws TException {
        iface.getUserIdMappingByKugouIds(args.kugouIds,resultHandler);
      }
    }

    public static class getKugouIdMappingByUserIds<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getKugouIdMappingByUserIds_args, Map<Long,Long>> {
      public getKugouIdMappingByUserIds() {
        super("getKugouIdMappingByUserIds");
      }

      public getKugouIdMappingByUserIds_args getEmptyArgsInstance() {
        return new getKugouIdMappingByUserIds_args();
      }

      public AsyncMethodCallback<Map<Long,Long>> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<Map<Long,Long>>() { 
          public void onComplete(Map<Long,Long> o) {
            getKugouIdMappingByUserIds_result result = new getKugouIdMappingByUserIds_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getKugouIdMappingByUserIds_result result = new getKugouIdMappingByUserIds_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getKugouIdMappingByUserIds_args args, org.apache.thrift.async.AsyncMethodCallback<Map<Long,Long>> resultHandler) throws TException {
        iface.getKugouIdMappingByUserIds(args.userIds,resultHandler);
      }
    }

    public static class getUserIdByKugouId<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUserIdByKugouId_args, Long> {
      public getUserIdByKugouId() {
        super("getUserIdByKugouId");
      }

      public getUserIdByKugouId_args getEmptyArgsInstance() {
        return new getUserIdByKugouId_args();
      }

      public AsyncMethodCallback<Long> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<Long>() { 
          public void onComplete(Long o) {
            getUserIdByKugouId_result result = new getUserIdByKugouId_result();
            result.success = o;
            result.setSuccessIsSet(true);
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUserIdByKugouId_result result = new getUserIdByKugouId_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUserIdByKugouId_args args, org.apache.thrift.async.AsyncMethodCallback<Long> resultHandler) throws TException {
        iface.getUserIdByKugouId(args.kugouId,resultHandler);
      }
    }

    public static class getKugouIdByUserId<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getKugouIdByUserId_args, Long> {
      public getKugouIdByUserId() {
        super("getKugouIdByUserId");
      }

      public getKugouIdByUserId_args getEmptyArgsInstance() {
        return new getKugouIdByUserId_args();
      }

      public AsyncMethodCallback<Long> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<Long>() { 
          public void onComplete(Long o) {
            getKugouIdByUserId_result result = new getKugouIdByUserId_result();
            result.success = o;
            result.setSuccessIsSet(true);
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getKugouIdByUserId_result result = new getKugouIdByUserId_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getKugouIdByUserId_args args, org.apache.thrift.async.AsyncMethodCallback<Long> resultHandler) throws TException {
        iface.getKugouIdByUserId(args.userId,resultHandler);
      }
    }

    public static class addUserIdMapping<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, addUserIdMapping_args, Integer> {
      public addUserIdMapping() {
        super("addUserIdMapping");
      }

      public addUserIdMapping_args getEmptyArgsInstance() {
        return new addUserIdMapping_args();
      }

      public AsyncMethodCallback<Integer> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<Integer>() { 
          public void onComplete(Integer o) {
            addUserIdMapping_result result = new addUserIdMapping_result();
            result.success = o;
            result.setSuccessIsSet(true);
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            addUserIdMapping_result result = new addUserIdMapping_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, addUserIdMapping_args args, org.apache.thrift.async.AsyncMethodCallback<Integer> resultHandler) throws TException {
        iface.addUserIdMapping(args.kugouId, args.userId,resultHandler);
      }
    }

  }

  public static class getUserIdMappingByKugouIds_args implements org.apache.thrift.TBase<getUserIdMappingByKugouIds_args, getUserIdMappingByKugouIds_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUserIdMappingByKugouIds_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserIdMappingByKugouIds_args");

    private static final org.apache.thrift.protocol.TField KUGOU_IDS_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouIds", org.apache.thrift.protocol.TType.LIST, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserIdMappingByKugouIds_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserIdMappingByKugouIds_argsTupleSchemeFactory());
    }

    public List<Long> kugouIds; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_IDS((short)1, "kugouIds");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_IDS
            return KUGOU_IDS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_IDS, new org.apache.thrift.meta_data.FieldMetaData("kugouIds", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
              new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserIdMappingByKugouIds_args.class, metaDataMap);
    }

    public getUserIdMappingByKugouIds_args() {
    }

    public getUserIdMappingByKugouIds_args(
      List<Long> kugouIds)
    {
      this();
      this.kugouIds = kugouIds;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserIdMappingByKugouIds_args(getUserIdMappingByKugouIds_args other) {
      if (other.isSetKugouIds()) {
        List<Long> __this__kugouIds = new ArrayList<Long>(other.kugouIds);
        this.kugouIds = __this__kugouIds;
      }
    }

    public getUserIdMappingByKugouIds_args deepCopy() {
      return new getUserIdMappingByKugouIds_args(this);
    }

    @Override
    public void clear() {
      this.kugouIds = null;
    }

    public int getKugouIdsSize() {
      return (this.kugouIds == null) ? 0 : this.kugouIds.size();
    }

    public java.util.Iterator<Long> getKugouIdsIterator() {
      return (this.kugouIds == null) ? null : this.kugouIds.iterator();
    }

    public void addToKugouIds(long elem) {
      if (this.kugouIds == null) {
        this.kugouIds = new ArrayList<Long>();
      }
      this.kugouIds.add(elem);
    }

    public List<Long> getKugouIds() {
      return this.kugouIds;
    }

    public getUserIdMappingByKugouIds_args setKugouIds(List<Long> kugouIds) {
      this.kugouIds = kugouIds;
      return this;
    }

    public void unsetKugouIds() {
      this.kugouIds = null;
    }

    /** Returns true if field kugouIds is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouIds() {
      return this.kugouIds != null;
    }

    public void setKugouIdsIsSet(boolean value) {
      if (!value) {
        this.kugouIds = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_IDS:
        if (value == null) {
          unsetKugouIds();
        } else {
          setKugouIds((List<Long>)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_IDS:
        return getKugouIds();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_IDS:
        return isSetKugouIds();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserIdMappingByKugouIds_args)
        return this.equals((getUserIdMappingByKugouIds_args)that);
      return false;
    }

    public boolean equals(getUserIdMappingByKugouIds_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouIds = true && this.isSetKugouIds();
      boolean that_present_kugouIds = true && that.isSetKugouIds();
      if (this_present_kugouIds || that_present_kugouIds) {
        if (!(this_present_kugouIds && that_present_kugouIds))
          return false;
        if (!this.kugouIds.equals(that.kugouIds))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouIds = true && (isSetKugouIds());
      list.add(present_kugouIds);
      if (present_kugouIds)
        list.add(kugouIds);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserIdMappingByKugouIds_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouIds()).compareTo(other.isSetKugouIds());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouIds()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouIds, other.kugouIds);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserIdMappingByKugouIds_args(");
      boolean first = true;

      sb.append("kugouIds:");
      if (this.kugouIds == null) {
        sb.append("null");
      } else {
        sb.append(this.kugouIds);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserIdMappingByKugouIds_argsStandardSchemeFactory implements SchemeFactory {
      public getUserIdMappingByKugouIds_argsStandardScheme getScheme() {
        return new getUserIdMappingByKugouIds_argsStandardScheme();
      }
    }

    private static class getUserIdMappingByKugouIds_argsStandardScheme extends StandardScheme<getUserIdMappingByKugouIds_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserIdMappingByKugouIds_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_IDS
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                  struct.kugouIds = new ArrayList<Long>(_list0.size);
                  long _elem1;
                  for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                  {
                    _elem1 = iprot.readI64();
                    struct.kugouIds.add(_elem1);
                  }
                  iprot.readListEnd();
                }
                struct.setKugouIdsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserIdMappingByKugouIds_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.kugouIds != null) {
          oprot.writeFieldBegin(KUGOU_IDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.kugouIds.size()));
            for (long _iter3 : struct.kugouIds)
            {
              oprot.writeI64(_iter3);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserIdMappingByKugouIds_argsTupleSchemeFactory implements SchemeFactory {
      public getUserIdMappingByKugouIds_argsTupleScheme getScheme() {
        return new getUserIdMappingByKugouIds_argsTupleScheme();
      }
    }

    private static class getUserIdMappingByKugouIds_argsTupleScheme extends TupleScheme<getUserIdMappingByKugouIds_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserIdMappingByKugouIds_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouIds()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetKugouIds()) {
          {
            oprot.writeI32(struct.kugouIds.size());
            for (long _iter4 : struct.kugouIds)
            {
              oprot.writeI64(_iter4);
            }
          }
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserIdMappingByKugouIds_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          {
            org.apache.thrift.protocol.TList _list5 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
            struct.kugouIds = new ArrayList<Long>(_list5.size);
            long _elem6;
            for (int _i7 = 0; _i7 < _list5.size; ++_i7)
            {
              _elem6 = iprot.readI64();
              struct.kugouIds.add(_elem6);
            }
          }
          struct.setKugouIdsIsSet(true);
        }
      }
    }

  }

  public static class getUserIdMappingByKugouIds_result implements org.apache.thrift.TBase<getUserIdMappingByKugouIds_result, getUserIdMappingByKugouIds_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUserIdMappingByKugouIds_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserIdMappingByKugouIds_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.MAP, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserIdMappingByKugouIds_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserIdMappingByKugouIds_resultTupleSchemeFactory());
    }

    public Map<Long,Long> success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
              new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64), 
              new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserIdMappingByKugouIds_result.class, metaDataMap);
    }

    public getUserIdMappingByKugouIds_result() {
    }

    public getUserIdMappingByKugouIds_result(
      Map<Long,Long> success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserIdMappingByKugouIds_result(getUserIdMappingByKugouIds_result other) {
      if (other.isSetSuccess()) {
        Map<Long,Long> __this__success = new HashMap<Long,Long>(other.success);
        this.success = __this__success;
      }
    }

    public getUserIdMappingByKugouIds_result deepCopy() {
      return new getUserIdMappingByKugouIds_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public int getSuccessSize() {
      return (this.success == null) ? 0 : this.success.size();
    }

    public void putToSuccess(long key, long val) {
      if (this.success == null) {
        this.success = new HashMap<Long,Long>();
      }
      this.success.put(key, val);
    }

    public Map<Long,Long> getSuccess() {
      return this.success;
    }

    public getUserIdMappingByKugouIds_result setSuccess(Map<Long,Long> success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((Map<Long,Long>)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserIdMappingByKugouIds_result)
        return this.equals((getUserIdMappingByKugouIds_result)that);
      return false;
    }

    public boolean equals(getUserIdMappingByKugouIds_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserIdMappingByKugouIds_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserIdMappingByKugouIds_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserIdMappingByKugouIds_resultStandardSchemeFactory implements SchemeFactory {
      public getUserIdMappingByKugouIds_resultStandardScheme getScheme() {
        return new getUserIdMappingByKugouIds_resultStandardScheme();
      }
    }

    private static class getUserIdMappingByKugouIds_resultStandardScheme extends StandardScheme<getUserIdMappingByKugouIds_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserIdMappingByKugouIds_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
                {
                  org.apache.thrift.protocol.TMap _map8 = iprot.readMapBegin();
                  struct.success = new HashMap<Long,Long>(2*_map8.size);
                  long _key9;
                  long _val10;
                  for (int _i11 = 0; _i11 < _map8.size; ++_i11)
                  {
                    _key9 = iprot.readI64();
                    _val10 = iprot.readI64();
                    struct.success.put(_key9, _val10);
                  }
                  iprot.readMapEnd();
                }
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserIdMappingByKugouIds_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          {
            oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.I64, struct.success.size()));
            for (Map.Entry<Long, Long> _iter12 : struct.success.entrySet())
            {
              oprot.writeI64(_iter12.getKey());
              oprot.writeI64(_iter12.getValue());
            }
            oprot.writeMapEnd();
          }
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserIdMappingByKugouIds_resultTupleSchemeFactory implements SchemeFactory {
      public getUserIdMappingByKugouIds_resultTupleScheme getScheme() {
        return new getUserIdMappingByKugouIds_resultTupleScheme();
      }
    }

    private static class getUserIdMappingByKugouIds_resultTupleScheme extends TupleScheme<getUserIdMappingByKugouIds_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserIdMappingByKugouIds_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          {
            oprot.writeI32(struct.success.size());
            for (Map.Entry<Long, Long> _iter13 : struct.success.entrySet())
            {
              oprot.writeI64(_iter13.getKey());
              oprot.writeI64(_iter13.getValue());
            }
          }
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserIdMappingByKugouIds_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          {
            org.apache.thrift.protocol.TMap _map14 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.I64, iprot.readI32());
            struct.success = new HashMap<Long,Long>(2*_map14.size);
            long _key15;
            long _val16;
            for (int _i17 = 0; _i17 < _map14.size; ++_i17)
            {
              _key15 = iprot.readI64();
              _val16 = iprot.readI64();
              struct.success.put(_key15, _val16);
            }
          }
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getKugouIdMappingByUserIds_args implements org.apache.thrift.TBase<getKugouIdMappingByUserIds_args, getKugouIdMappingByUserIds_args._Fields>, java.io.Serializable, Cloneable, Comparable<getKugouIdMappingByUserIds_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getKugouIdMappingByUserIds_args");

    private static final org.apache.thrift.protocol.TField USER_IDS_FIELD_DESC = new org.apache.thrift.protocol.TField("userIds", org.apache.thrift.protocol.TType.LIST, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getKugouIdMappingByUserIds_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getKugouIdMappingByUserIds_argsTupleSchemeFactory());
    }

    public List<Long> userIds; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      USER_IDS((short)1, "userIds");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // USER_IDS
            return USER_IDS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.USER_IDS, new org.apache.thrift.meta_data.FieldMetaData("userIds", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
              new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getKugouIdMappingByUserIds_args.class, metaDataMap);
    }

    public getKugouIdMappingByUserIds_args() {
    }

    public getKugouIdMappingByUserIds_args(
      List<Long> userIds)
    {
      this();
      this.userIds = userIds;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getKugouIdMappingByUserIds_args(getKugouIdMappingByUserIds_args other) {
      if (other.isSetUserIds()) {
        List<Long> __this__userIds = new ArrayList<Long>(other.userIds);
        this.userIds = __this__userIds;
      }
    }

    public getKugouIdMappingByUserIds_args deepCopy() {
      return new getKugouIdMappingByUserIds_args(this);
    }

    @Override
    public void clear() {
      this.userIds = null;
    }

    public int getUserIdsSize() {
      return (this.userIds == null) ? 0 : this.userIds.size();
    }

    public java.util.Iterator<Long> getUserIdsIterator() {
      return (this.userIds == null) ? null : this.userIds.iterator();
    }

    public void addToUserIds(long elem) {
      if (this.userIds == null) {
        this.userIds = new ArrayList<Long>();
      }
      this.userIds.add(elem);
    }

    public List<Long> getUserIds() {
      return this.userIds;
    }

    public getKugouIdMappingByUserIds_args setUserIds(List<Long> userIds) {
      this.userIds = userIds;
      return this;
    }

    public void unsetUserIds() {
      this.userIds = null;
    }

    /** Returns true if field userIds is set (has been assigned a value) and false otherwise */
    public boolean isSetUserIds() {
      return this.userIds != null;
    }

    public void setUserIdsIsSet(boolean value) {
      if (!value) {
        this.userIds = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case USER_IDS:
        if (value == null) {
          unsetUserIds();
        } else {
          setUserIds((List<Long>)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case USER_IDS:
        return getUserIds();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case USER_IDS:
        return isSetUserIds();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getKugouIdMappingByUserIds_args)
        return this.equals((getKugouIdMappingByUserIds_args)that);
      return false;
    }

    public boolean equals(getKugouIdMappingByUserIds_args that) {
      if (that == null)
        return false;

      boolean this_present_userIds = true && this.isSetUserIds();
      boolean that_present_userIds = true && that.isSetUserIds();
      if (this_present_userIds || that_present_userIds) {
        if (!(this_present_userIds && that_present_userIds))
          return false;
        if (!this.userIds.equals(that.userIds))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_userIds = true && (isSetUserIds());
      list.add(present_userIds);
      if (present_userIds)
        list.add(userIds);

      return list.hashCode();
    }

    @Override
    public int compareTo(getKugouIdMappingByUserIds_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetUserIds()).compareTo(other.isSetUserIds());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetUserIds()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.userIds, other.userIds);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getKugouIdMappingByUserIds_args(");
      boolean first = true;

      sb.append("userIds:");
      if (this.userIds == null) {
        sb.append("null");
      } else {
        sb.append(this.userIds);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getKugouIdMappingByUserIds_argsStandardSchemeFactory implements SchemeFactory {
      public getKugouIdMappingByUserIds_argsStandardScheme getScheme() {
        return new getKugouIdMappingByUserIds_argsStandardScheme();
      }
    }

    private static class getKugouIdMappingByUserIds_argsStandardScheme extends StandardScheme<getKugouIdMappingByUserIds_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getKugouIdMappingByUserIds_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // USER_IDS
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list18 = iprot.readListBegin();
                  struct.userIds = new ArrayList<Long>(_list18.size);
                  long _elem19;
                  for (int _i20 = 0; _i20 < _list18.size; ++_i20)
                  {
                    _elem19 = iprot.readI64();
                    struct.userIds.add(_elem19);
                  }
                  iprot.readListEnd();
                }
                struct.setUserIdsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getKugouIdMappingByUserIds_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.userIds != null) {
          oprot.writeFieldBegin(USER_IDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.userIds.size()));
            for (long _iter21 : struct.userIds)
            {
              oprot.writeI64(_iter21);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getKugouIdMappingByUserIds_argsTupleSchemeFactory implements SchemeFactory {
      public getKugouIdMappingByUserIds_argsTupleScheme getScheme() {
        return new getKugouIdMappingByUserIds_argsTupleScheme();
      }
    }

    private static class getKugouIdMappingByUserIds_argsTupleScheme extends TupleScheme<getKugouIdMappingByUserIds_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getKugouIdMappingByUserIds_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetUserIds()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetUserIds()) {
          {
            oprot.writeI32(struct.userIds.size());
            for (long _iter22 : struct.userIds)
            {
              oprot.writeI64(_iter22);
            }
          }
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getKugouIdMappingByUserIds_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          {
            org.apache.thrift.protocol.TList _list23 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
            struct.userIds = new ArrayList<Long>(_list23.size);
            long _elem24;
            for (int _i25 = 0; _i25 < _list23.size; ++_i25)
            {
              _elem24 = iprot.readI64();
              struct.userIds.add(_elem24);
            }
          }
          struct.setUserIdsIsSet(true);
        }
      }
    }

  }

  public static class getKugouIdMappingByUserIds_result implements org.apache.thrift.TBase<getKugouIdMappingByUserIds_result, getKugouIdMappingByUserIds_result._Fields>, java.io.Serializable, Cloneable, Comparable<getKugouIdMappingByUserIds_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getKugouIdMappingByUserIds_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.MAP, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getKugouIdMappingByUserIds_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getKugouIdMappingByUserIds_resultTupleSchemeFactory());
    }

    public Map<Long,Long> success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
              new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64), 
              new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getKugouIdMappingByUserIds_result.class, metaDataMap);
    }

    public getKugouIdMappingByUserIds_result() {
    }

    public getKugouIdMappingByUserIds_result(
      Map<Long,Long> success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getKugouIdMappingByUserIds_result(getKugouIdMappingByUserIds_result other) {
      if (other.isSetSuccess()) {
        Map<Long,Long> __this__success = new HashMap<Long,Long>(other.success);
        this.success = __this__success;
      }
    }

    public getKugouIdMappingByUserIds_result deepCopy() {
      return new getKugouIdMappingByUserIds_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public int getSuccessSize() {
      return (this.success == null) ? 0 : this.success.size();
    }

    public void putToSuccess(long key, long val) {
      if (this.success == null) {
        this.success = new HashMap<Long,Long>();
      }
      this.success.put(key, val);
    }

    public Map<Long,Long> getSuccess() {
      return this.success;
    }

    public getKugouIdMappingByUserIds_result setSuccess(Map<Long,Long> success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((Map<Long,Long>)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getKugouIdMappingByUserIds_result)
        return this.equals((getKugouIdMappingByUserIds_result)that);
      return false;
    }

    public boolean equals(getKugouIdMappingByUserIds_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getKugouIdMappingByUserIds_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getKugouIdMappingByUserIds_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getKugouIdMappingByUserIds_resultStandardSchemeFactory implements SchemeFactory {
      public getKugouIdMappingByUserIds_resultStandardScheme getScheme() {
        return new getKugouIdMappingByUserIds_resultStandardScheme();
      }
    }

    private static class getKugouIdMappingByUserIds_resultStandardScheme extends StandardScheme<getKugouIdMappingByUserIds_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getKugouIdMappingByUserIds_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
                {
                  org.apache.thrift.protocol.TMap _map26 = iprot.readMapBegin();
                  struct.success = new HashMap<Long,Long>(2*_map26.size);
                  long _key27;
                  long _val28;
                  for (int _i29 = 0; _i29 < _map26.size; ++_i29)
                  {
                    _key27 = iprot.readI64();
                    _val28 = iprot.readI64();
                    struct.success.put(_key27, _val28);
                  }
                  iprot.readMapEnd();
                }
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getKugouIdMappingByUserIds_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          {
            oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.I64, struct.success.size()));
            for (Map.Entry<Long, Long> _iter30 : struct.success.entrySet())
            {
              oprot.writeI64(_iter30.getKey());
              oprot.writeI64(_iter30.getValue());
            }
            oprot.writeMapEnd();
          }
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getKugouIdMappingByUserIds_resultTupleSchemeFactory implements SchemeFactory {
      public getKugouIdMappingByUserIds_resultTupleScheme getScheme() {
        return new getKugouIdMappingByUserIds_resultTupleScheme();
      }
    }

    private static class getKugouIdMappingByUserIds_resultTupleScheme extends TupleScheme<getKugouIdMappingByUserIds_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getKugouIdMappingByUserIds_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          {
            oprot.writeI32(struct.success.size());
            for (Map.Entry<Long, Long> _iter31 : struct.success.entrySet())
            {
              oprot.writeI64(_iter31.getKey());
              oprot.writeI64(_iter31.getValue());
            }
          }
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getKugouIdMappingByUserIds_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          {
            org.apache.thrift.protocol.TMap _map32 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.I64, iprot.readI32());
            struct.success = new HashMap<Long,Long>(2*_map32.size);
            long _key33;
            long _val34;
            for (int _i35 = 0; _i35 < _map32.size; ++_i35)
            {
              _key33 = iprot.readI64();
              _val34 = iprot.readI64();
              struct.success.put(_key33, _val34);
            }
          }
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUserIdByKugouId_args implements org.apache.thrift.TBase<getUserIdByKugouId_args, getUserIdByKugouId_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUserIdByKugouId_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserIdByKugouId_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserIdByKugouId_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserIdByKugouId_argsTupleSchemeFactory());
    }

    public long kugouId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserIdByKugouId_args.class, metaDataMap);
    }

    public getUserIdByKugouId_args() {
    }

    public getUserIdByKugouId_args(
      long kugouId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserIdByKugouId_args(getUserIdByKugouId_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
    }

    public getUserIdByKugouId_args deepCopy() {
      return new getUserIdByKugouId_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public getUserIdByKugouId_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserIdByKugouId_args)
        return this.equals((getUserIdByKugouId_args)that);
      return false;
    }

    public boolean equals(getUserIdByKugouId_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserIdByKugouId_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserIdByKugouId_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserIdByKugouId_argsStandardSchemeFactory implements SchemeFactory {
      public getUserIdByKugouId_argsStandardScheme getScheme() {
        return new getUserIdByKugouId_argsStandardScheme();
      }
    }

    private static class getUserIdByKugouId_argsStandardScheme extends StandardScheme<getUserIdByKugouId_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserIdByKugouId_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserIdByKugouId_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserIdByKugouId_argsTupleSchemeFactory implements SchemeFactory {
      public getUserIdByKugouId_argsTupleScheme getScheme() {
        return new getUserIdByKugouId_argsTupleScheme();
      }
    }

    private static class getUserIdByKugouId_argsTupleScheme extends TupleScheme<getUserIdByKugouId_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserIdByKugouId_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouId()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetKugouId()) {
          oprot.writeI64(struct.kugouId);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserIdByKugouId_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.kugouId = iprot.readI64();
          struct.setKugouIdIsSet(true);
        }
      }
    }

  }

  public static class getUserIdByKugouId_result implements org.apache.thrift.TBase<getUserIdByKugouId_result, getUserIdByKugouId_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUserIdByKugouId_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserIdByKugouId_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.I64, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserIdByKugouId_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserIdByKugouId_resultTupleSchemeFactory());
    }

    public long success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __SUCCESS_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserIdByKugouId_result.class, metaDataMap);
    }

    public getUserIdByKugouId_result() {
    }

    public getUserIdByKugouId_result(
      long success)
    {
      this();
      this.success = success;
      setSuccessIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserIdByKugouId_result(getUserIdByKugouId_result other) {
      __isset_bitfield = other.__isset_bitfield;
      this.success = other.success;
    }

    public getUserIdByKugouId_result deepCopy() {
      return new getUserIdByKugouId_result(this);
    }

    @Override
    public void clear() {
      setSuccessIsSet(false);
      this.success = 0;
    }

    public long getSuccess() {
      return this.success;
    }

    public getUserIdByKugouId_result setSuccess(long success) {
      this.success = success;
      setSuccessIsSet(true);
      return this;
    }

    public void unsetSuccess() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SUCCESS_ISSET_ID);
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return EncodingUtils.testBit(__isset_bitfield, __SUCCESS_ISSET_ID);
    }

    public void setSuccessIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SUCCESS_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserIdByKugouId_result)
        return this.equals((getUserIdByKugouId_result)that);
      return false;
    }

    public boolean equals(getUserIdByKugouId_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true;
      boolean that_present_success = true;
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (this.success != that.success)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true;
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserIdByKugouId_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserIdByKugouId_result(");
      boolean first = true;

      sb.append("success:");
      sb.append(this.success);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserIdByKugouId_resultStandardSchemeFactory implements SchemeFactory {
      public getUserIdByKugouId_resultStandardScheme getScheme() {
        return new getUserIdByKugouId_resultStandardScheme();
      }
    }

    private static class getUserIdByKugouId_resultStandardScheme extends StandardScheme<getUserIdByKugouId_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserIdByKugouId_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.success = iprot.readI64();
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserIdByKugouId_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.isSetSuccess()) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          oprot.writeI64(struct.success);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserIdByKugouId_resultTupleSchemeFactory implements SchemeFactory {
      public getUserIdByKugouId_resultTupleScheme getScheme() {
        return new getUserIdByKugouId_resultTupleScheme();
      }
    }

    private static class getUserIdByKugouId_resultTupleScheme extends TupleScheme<getUserIdByKugouId_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserIdByKugouId_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          oprot.writeI64(struct.success);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserIdByKugouId_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = iprot.readI64();
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getKugouIdByUserId_args implements org.apache.thrift.TBase<getKugouIdByUserId_args, getKugouIdByUserId_args._Fields>, java.io.Serializable, Cloneable, Comparable<getKugouIdByUserId_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getKugouIdByUserId_args");

    private static final org.apache.thrift.protocol.TField USER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("userId", org.apache.thrift.protocol.TType.I64, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getKugouIdByUserId_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getKugouIdByUserId_argsTupleSchemeFactory());
    }

    public long userId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      USER_ID((short)1, "userId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // USER_ID
            return USER_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __USERID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.USER_ID, new org.apache.thrift.meta_data.FieldMetaData("userId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getKugouIdByUserId_args.class, metaDataMap);
    }

    public getKugouIdByUserId_args() {
    }

    public getKugouIdByUserId_args(
      long userId)
    {
      this();
      this.userId = userId;
      setUserIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getKugouIdByUserId_args(getKugouIdByUserId_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.userId = other.userId;
    }

    public getKugouIdByUserId_args deepCopy() {
      return new getKugouIdByUserId_args(this);
    }

    @Override
    public void clear() {
      setUserIdIsSet(false);
      this.userId = 0;
    }

    public long getUserId() {
      return this.userId;
    }

    public getKugouIdByUserId_args setUserId(long userId) {
      this.userId = userId;
      setUserIdIsSet(true);
      return this;
    }

    public void unsetUserId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __USERID_ISSET_ID);
    }

    /** Returns true if field userId is set (has been assigned a value) and false otherwise */
    public boolean isSetUserId() {
      return EncodingUtils.testBit(__isset_bitfield, __USERID_ISSET_ID);
    }

    public void setUserIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __USERID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case USER_ID:
        if (value == null) {
          unsetUserId();
        } else {
          setUserId((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case USER_ID:
        return getUserId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case USER_ID:
        return isSetUserId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getKugouIdByUserId_args)
        return this.equals((getKugouIdByUserId_args)that);
      return false;
    }

    public boolean equals(getKugouIdByUserId_args that) {
      if (that == null)
        return false;

      boolean this_present_userId = true;
      boolean that_present_userId = true;
      if (this_present_userId || that_present_userId) {
        if (!(this_present_userId && that_present_userId))
          return false;
        if (this.userId != that.userId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_userId = true;
      list.add(present_userId);
      if (present_userId)
        list.add(userId);

      return list.hashCode();
    }

    @Override
    public int compareTo(getKugouIdByUserId_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetUserId()).compareTo(other.isSetUserId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetUserId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.userId, other.userId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getKugouIdByUserId_args(");
      boolean first = true;

      sb.append("userId:");
      sb.append(this.userId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getKugouIdByUserId_argsStandardSchemeFactory implements SchemeFactory {
      public getKugouIdByUserId_argsStandardScheme getScheme() {
        return new getKugouIdByUserId_argsStandardScheme();
      }
    }

    private static class getKugouIdByUserId_argsStandardScheme extends StandardScheme<getKugouIdByUserId_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getKugouIdByUserId_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // USER_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.userId = iprot.readI64();
                struct.setUserIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getKugouIdByUserId_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(USER_ID_FIELD_DESC);
        oprot.writeI64(struct.userId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getKugouIdByUserId_argsTupleSchemeFactory implements SchemeFactory {
      public getKugouIdByUserId_argsTupleScheme getScheme() {
        return new getKugouIdByUserId_argsTupleScheme();
      }
    }

    private static class getKugouIdByUserId_argsTupleScheme extends TupleScheme<getKugouIdByUserId_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getKugouIdByUserId_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetUserId()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetUserId()) {
          oprot.writeI64(struct.userId);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getKugouIdByUserId_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.userId = iprot.readI64();
          struct.setUserIdIsSet(true);
        }
      }
    }

  }

  public static class getKugouIdByUserId_result implements org.apache.thrift.TBase<getKugouIdByUserId_result, getKugouIdByUserId_result._Fields>, java.io.Serializable, Cloneable, Comparable<getKugouIdByUserId_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getKugouIdByUserId_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.I64, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getKugouIdByUserId_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getKugouIdByUserId_resultTupleSchemeFactory());
    }

    public long success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __SUCCESS_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getKugouIdByUserId_result.class, metaDataMap);
    }

    public getKugouIdByUserId_result() {
    }

    public getKugouIdByUserId_result(
      long success)
    {
      this();
      this.success = success;
      setSuccessIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getKugouIdByUserId_result(getKugouIdByUserId_result other) {
      __isset_bitfield = other.__isset_bitfield;
      this.success = other.success;
    }

    public getKugouIdByUserId_result deepCopy() {
      return new getKugouIdByUserId_result(this);
    }

    @Override
    public void clear() {
      setSuccessIsSet(false);
      this.success = 0;
    }

    public long getSuccess() {
      return this.success;
    }

    public getKugouIdByUserId_result setSuccess(long success) {
      this.success = success;
      setSuccessIsSet(true);
      return this;
    }

    public void unsetSuccess() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SUCCESS_ISSET_ID);
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return EncodingUtils.testBit(__isset_bitfield, __SUCCESS_ISSET_ID);
    }

    public void setSuccessIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SUCCESS_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getKugouIdByUserId_result)
        return this.equals((getKugouIdByUserId_result)that);
      return false;
    }

    public boolean equals(getKugouIdByUserId_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true;
      boolean that_present_success = true;
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (this.success != that.success)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true;
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getKugouIdByUserId_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getKugouIdByUserId_result(");
      boolean first = true;

      sb.append("success:");
      sb.append(this.success);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getKugouIdByUserId_resultStandardSchemeFactory implements SchemeFactory {
      public getKugouIdByUserId_resultStandardScheme getScheme() {
        return new getKugouIdByUserId_resultStandardScheme();
      }
    }

    private static class getKugouIdByUserId_resultStandardScheme extends StandardScheme<getKugouIdByUserId_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getKugouIdByUserId_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.success = iprot.readI64();
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getKugouIdByUserId_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.isSetSuccess()) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          oprot.writeI64(struct.success);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getKugouIdByUserId_resultTupleSchemeFactory implements SchemeFactory {
      public getKugouIdByUserId_resultTupleScheme getScheme() {
        return new getKugouIdByUserId_resultTupleScheme();
      }
    }

    private static class getKugouIdByUserId_resultTupleScheme extends TupleScheme<getKugouIdByUserId_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getKugouIdByUserId_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          oprot.writeI64(struct.success);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getKugouIdByUserId_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = iprot.readI64();
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class addUserIdMapping_args implements org.apache.thrift.TBase<addUserIdMapping_args, addUserIdMapping_args._Fields>, java.io.Serializable, Cloneable, Comparable<addUserIdMapping_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("addUserIdMapping_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);
    private static final org.apache.thrift.protocol.TField USER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("userId", org.apache.thrift.protocol.TType.I64, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new addUserIdMapping_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new addUserIdMapping_argsTupleSchemeFactory());
    }

    public long kugouId; // required
    public long userId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId"),
      USER_ID((short)2, "userId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          case 2: // USER_ID
            return USER_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private static final int __USERID_ISSET_ID = 1;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.USER_ID, new org.apache.thrift.meta_data.FieldMetaData("userId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(addUserIdMapping_args.class, metaDataMap);
    }

    public addUserIdMapping_args() {
    }

    public addUserIdMapping_args(
      long kugouId,
      long userId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      this.userId = userId;
      setUserIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public addUserIdMapping_args(addUserIdMapping_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
      this.userId = other.userId;
    }

    public addUserIdMapping_args deepCopy() {
      return new addUserIdMapping_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
      setUserIdIsSet(false);
      this.userId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public addUserIdMapping_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public long getUserId() {
      return this.userId;
    }

    public addUserIdMapping_args setUserId(long userId) {
      this.userId = userId;
      setUserIdIsSet(true);
      return this;
    }

    public void unsetUserId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __USERID_ISSET_ID);
    }

    /** Returns true if field userId is set (has been assigned a value) and false otherwise */
    public boolean isSetUserId() {
      return EncodingUtils.testBit(__isset_bitfield, __USERID_ISSET_ID);
    }

    public void setUserIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __USERID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      case USER_ID:
        if (value == null) {
          unsetUserId();
        } else {
          setUserId((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      case USER_ID:
        return getUserId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      case USER_ID:
        return isSetUserId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof addUserIdMapping_args)
        return this.equals((addUserIdMapping_args)that);
      return false;
    }

    public boolean equals(addUserIdMapping_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      boolean this_present_userId = true;
      boolean that_present_userId = true;
      if (this_present_userId || that_present_userId) {
        if (!(this_present_userId && that_present_userId))
          return false;
        if (this.userId != that.userId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      boolean present_userId = true;
      list.add(present_userId);
      if (present_userId)
        list.add(userId);

      return list.hashCode();
    }

    @Override
    public int compareTo(addUserIdMapping_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetUserId()).compareTo(other.isSetUserId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetUserId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.userId, other.userId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("addUserIdMapping_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      if (!first) sb.append(", ");
      sb.append("userId:");
      sb.append(this.userId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class addUserIdMapping_argsStandardSchemeFactory implements SchemeFactory {
      public addUserIdMapping_argsStandardScheme getScheme() {
        return new addUserIdMapping_argsStandardScheme();
      }
    }

    private static class addUserIdMapping_argsStandardScheme extends StandardScheme<addUserIdMapping_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, addUserIdMapping_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // USER_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.userId = iprot.readI64();
                struct.setUserIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, addUserIdMapping_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(USER_ID_FIELD_DESC);
        oprot.writeI64(struct.userId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class addUserIdMapping_argsTupleSchemeFactory implements SchemeFactory {
      public addUserIdMapping_argsTupleScheme getScheme() {
        return new addUserIdMapping_argsTupleScheme();
      }
    }

    private static class addUserIdMapping_argsTupleScheme extends TupleScheme<addUserIdMapping_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, addUserIdMapping_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouId()) {
          optionals.set(0);
        }
        if (struct.isSetUserId()) {
          optionals.set(1);
        }
        oprot.writeBitSet(optionals, 2);
        if (struct.isSetKugouId()) {
          oprot.writeI64(struct.kugouId);
        }
        if (struct.isSetUserId()) {
          oprot.writeI64(struct.userId);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, addUserIdMapping_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(2);
        if (incoming.get(0)) {
          struct.kugouId = iprot.readI64();
          struct.setKugouIdIsSet(true);
        }
        if (incoming.get(1)) {
          struct.userId = iprot.readI64();
          struct.setUserIdIsSet(true);
        }
      }
    }

  }

  public static class addUserIdMapping_result implements org.apache.thrift.TBase<addUserIdMapping_result, addUserIdMapping_result._Fields>, java.io.Serializable, Cloneable, Comparable<addUserIdMapping_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("addUserIdMapping_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.I32, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new addUserIdMapping_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new addUserIdMapping_resultTupleSchemeFactory());
    }

    public int success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __SUCCESS_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(addUserIdMapping_result.class, metaDataMap);
    }

    public addUserIdMapping_result() {
    }

    public addUserIdMapping_result(
      int success)
    {
      this();
      this.success = success;
      setSuccessIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public addUserIdMapping_result(addUserIdMapping_result other) {
      __isset_bitfield = other.__isset_bitfield;
      this.success = other.success;
    }

    public addUserIdMapping_result deepCopy() {
      return new addUserIdMapping_result(this);
    }

    @Override
    public void clear() {
      setSuccessIsSet(false);
      this.success = 0;
    }

    public int getSuccess() {
      return this.success;
    }

    public addUserIdMapping_result setSuccess(int success) {
      this.success = success;
      setSuccessIsSet(true);
      return this;
    }

    public void unsetSuccess() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SUCCESS_ISSET_ID);
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return EncodingUtils.testBit(__isset_bitfield, __SUCCESS_ISSET_ID);
    }

    public void setSuccessIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SUCCESS_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((Integer)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof addUserIdMapping_result)
        return this.equals((addUserIdMapping_result)that);
      return false;
    }

    public boolean equals(addUserIdMapping_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true;
      boolean that_present_success = true;
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (this.success != that.success)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true;
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(addUserIdMapping_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("addUserIdMapping_result(");
      boolean first = true;

      sb.append("success:");
      sb.append(this.success);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class addUserIdMapping_resultStandardSchemeFactory implements SchemeFactory {
      public addUserIdMapping_resultStandardScheme getScheme() {
        return new addUserIdMapping_resultStandardScheme();
      }
    }

    private static class addUserIdMapping_resultStandardScheme extends StandardScheme<addUserIdMapping_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, addUserIdMapping_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.success = iprot.readI32();
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, addUserIdMapping_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.isSetSuccess()) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          oprot.writeI32(struct.success);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class addUserIdMapping_resultTupleSchemeFactory implements SchemeFactory {
      public addUserIdMapping_resultTupleScheme getScheme() {
        return new addUserIdMapping_resultTupleScheme();
      }
    }

    private static class addUserIdMapping_resultTupleScheme extends TupleScheme<addUserIdMapping_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, addUserIdMapping_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          oprot.writeI32(struct.success);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, addUserIdMapping_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = iprot.readI32();
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

}
