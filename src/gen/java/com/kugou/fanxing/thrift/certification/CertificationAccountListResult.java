/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.thrift.certification;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
/**
 * 实名认证结构
 * 
 */
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2020-02-10")
public class CertificationAccountListResult implements org.apache.thrift.TBase<CertificationAccountListResult, CertificationAccountListResult._Fields>, java.io.Serializable, Cloneable, Comparable<CertificationAccountListResult> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CertificationAccountListResult");

  private static final org.apache.thrift.protocol.TField KUGOU_ID_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouIdList", org.apache.thrift.protocol.TType.LIST, (short)1);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new CertificationAccountListResultStandardSchemeFactory());
    schemes.put(TupleScheme.class, new CertificationAccountListResultTupleSchemeFactory());
  }

  public List<Long> kugouIdList; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    KUGOU_ID_LIST((short)1, "kugouIdList");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // KUGOU_ID_LIST
          return KUGOU_ID_LIST;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.KUGOU_ID_LIST, new org.apache.thrift.meta_data.FieldMetaData("kugouIdList", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CertificationAccountListResult.class, metaDataMap);
  }

  public CertificationAccountListResult() {
  }

  public CertificationAccountListResult(
    List<Long> kugouIdList)
  {
    this();
    this.kugouIdList = kugouIdList;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CertificationAccountListResult(CertificationAccountListResult other) {
    if (other.isSetKugouIdList()) {
      List<Long> __this__kugouIdList = new ArrayList<Long>(other.kugouIdList);
      this.kugouIdList = __this__kugouIdList;
    }
  }

  public CertificationAccountListResult deepCopy() {
    return new CertificationAccountListResult(this);
  }

  @Override
  public void clear() {
    this.kugouIdList = null;
  }

  public int getKugouIdListSize() {
    return (this.kugouIdList == null) ? 0 : this.kugouIdList.size();
  }

  public java.util.Iterator<Long> getKugouIdListIterator() {
    return (this.kugouIdList == null) ? null : this.kugouIdList.iterator();
  }

  public void addToKugouIdList(long elem) {
    if (this.kugouIdList == null) {
      this.kugouIdList = new ArrayList<Long>();
    }
    this.kugouIdList.add(elem);
  }

  public List<Long> getKugouIdList() {
    return this.kugouIdList;
  }

  public CertificationAccountListResult setKugouIdList(List<Long> kugouIdList) {
    this.kugouIdList = kugouIdList;
    return this;
  }

  public void unsetKugouIdList() {
    this.kugouIdList = null;
  }

  /** Returns true if field kugouIdList is set (has been assigned a value) and false otherwise */
  public boolean isSetKugouIdList() {
    return this.kugouIdList != null;
  }

  public void setKugouIdListIsSet(boolean value) {
    if (!value) {
      this.kugouIdList = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case KUGOU_ID_LIST:
      if (value == null) {
        unsetKugouIdList();
      } else {
        setKugouIdList((List<Long>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case KUGOU_ID_LIST:
      return getKugouIdList();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case KUGOU_ID_LIST:
      return isSetKugouIdList();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof CertificationAccountListResult)
      return this.equals((CertificationAccountListResult)that);
    return false;
  }

  public boolean equals(CertificationAccountListResult that) {
    if (that == null)
      return false;

    boolean this_present_kugouIdList = true && this.isSetKugouIdList();
    boolean that_present_kugouIdList = true && that.isSetKugouIdList();
    if (this_present_kugouIdList || that_present_kugouIdList) {
      if (!(this_present_kugouIdList && that_present_kugouIdList))
        return false;
      if (!this.kugouIdList.equals(that.kugouIdList))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_kugouIdList = true && (isSetKugouIdList());
    list.add(present_kugouIdList);
    if (present_kugouIdList)
      list.add(kugouIdList);

    return list.hashCode();
  }

  @Override
  public int compareTo(CertificationAccountListResult other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetKugouIdList()).compareTo(other.isSetKugouIdList());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKugouIdList()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouIdList, other.kugouIdList);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("CertificationAccountListResult(");
    boolean first = true;

    sb.append("kugouIdList:");
    if (this.kugouIdList == null) {
      sb.append("null");
    } else {
      sb.append(this.kugouIdList);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (kugouIdList == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouIdList' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CertificationAccountListResultStandardSchemeFactory implements SchemeFactory {
    public CertificationAccountListResultStandardScheme getScheme() {
      return new CertificationAccountListResultStandardScheme();
    }
  }

  private static class CertificationAccountListResultStandardScheme extends StandardScheme<CertificationAccountListResult> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CertificationAccountListResult struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // KUGOU_ID_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list8 = iprot.readListBegin();
                struct.kugouIdList = new ArrayList<Long>(_list8.size);
                long _elem9;
                for (int _i10 = 0; _i10 < _list8.size; ++_i10)
                {
                  _elem9 = iprot.readI64();
                  struct.kugouIdList.add(_elem9);
                }
                iprot.readListEnd();
              }
              struct.setKugouIdListIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CertificationAccountListResult struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.kugouIdList != null) {
        oprot.writeFieldBegin(KUGOU_ID_LIST_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.kugouIdList.size()));
          for (long _iter11 : struct.kugouIdList)
          {
            oprot.writeI64(_iter11);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CertificationAccountListResultTupleSchemeFactory implements SchemeFactory {
    public CertificationAccountListResultTupleScheme getScheme() {
      return new CertificationAccountListResultTupleScheme();
    }
  }

  private static class CertificationAccountListResultTupleScheme extends TupleScheme<CertificationAccountListResult> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CertificationAccountListResult struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      {
        oprot.writeI32(struct.kugouIdList.size());
        for (long _iter12 : struct.kugouIdList)
        {
          oprot.writeI64(_iter12);
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CertificationAccountListResult struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      {
        org.apache.thrift.protocol.TList _list13 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
        struct.kugouIdList = new ArrayList<Long>(_list13.size);
        long _elem14;
        for (int _i15 = 0; _i15 < _list13.size; ++_i15)
        {
          _elem14 = iprot.readI64();
          struct.kugouIdList.add(_elem14);
        }
      }
      struct.setKugouIdListIsSet(true);
    }
  }

}

