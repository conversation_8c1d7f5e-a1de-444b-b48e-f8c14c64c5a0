/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.thrift.certification;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2020-02-10")
public class CertificationAuditRequest implements org.apache.thrift.TBase<CertificationAuditRequest, CertificationAuditRequest._Fields>, java.io.Serializable, Cloneable, Comparable<CertificationAuditRequest> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CertificationAuditRequest");

  private static final org.apache.thrift.protocol.TField TRANSACTION_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("transactionId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField RESULT_FIELD_DESC = new org.apache.thrift.protocol.TField("result", org.apache.thrift.protocol.TType.BOOL, (short)2);
  private static final org.apache.thrift.protocol.TField REASON_FIELD_DESC = new org.apache.thrift.protocol.TField("reason", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField ADMIN_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("adminId", org.apache.thrift.protocol.TType.I32, (short)4);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new CertificationAuditRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new CertificationAuditRequestTupleSchemeFactory());
  }

  /**
   * 实名申请交易订单号
   */
  public String transactionId; // required
  /**
   * 实名结果，true：通过，false：不通过
   */
  public boolean result; // required
  /**
   * 审核原因，不通过时为必填
   */
  public String reason; // optional
  /**
   * 管理员ID
   */
  public int adminId; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 实名申请交易订单号
     */
    TRANSACTION_ID((short)1, "transactionId"),
    /**
     * 实名结果，true：通过，false：不通过
     */
    RESULT((short)2, "result"),
    /**
     * 审核原因，不通过时为必填
     */
    REASON((short)3, "reason"),
    /**
     * 管理员ID
     */
    ADMIN_ID((short)4, "adminId");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // TRANSACTION_ID
          return TRANSACTION_ID;
        case 2: // RESULT
          return RESULT;
        case 3: // REASON
          return REASON;
        case 4: // ADMIN_ID
          return ADMIN_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __RESULT_ISSET_ID = 0;
  private static final int __ADMINID_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.REASON};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.TRANSACTION_ID, new org.apache.thrift.meta_data.FieldMetaData("transactionId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.RESULT, new org.apache.thrift.meta_data.FieldMetaData("result", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.REASON, new org.apache.thrift.meta_data.FieldMetaData("reason", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ADMIN_ID, new org.apache.thrift.meta_data.FieldMetaData("adminId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CertificationAuditRequest.class, metaDataMap);
  }

  public CertificationAuditRequest() {
  }

  public CertificationAuditRequest(
    String transactionId,
    boolean result,
    int adminId)
  {
    this();
    this.transactionId = transactionId;
    this.result = result;
    setResultIsSet(true);
    this.adminId = adminId;
    setAdminIdIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CertificationAuditRequest(CertificationAuditRequest other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetTransactionId()) {
      this.transactionId = other.transactionId;
    }
    this.result = other.result;
    if (other.isSetReason()) {
      this.reason = other.reason;
    }
    this.adminId = other.adminId;
  }

  public CertificationAuditRequest deepCopy() {
    return new CertificationAuditRequest(this);
  }

  @Override
  public void clear() {
    this.transactionId = null;
    setResultIsSet(false);
    this.result = false;
    this.reason = null;
    setAdminIdIsSet(false);
    this.adminId = 0;
  }

  /**
   * 实名申请交易订单号
   */
  public String getTransactionId() {
    return this.transactionId;
  }

  /**
   * 实名申请交易订单号
   */
  public CertificationAuditRequest setTransactionId(String transactionId) {
    this.transactionId = transactionId;
    return this;
  }

  public void unsetTransactionId() {
    this.transactionId = null;
  }

  /** Returns true if field transactionId is set (has been assigned a value) and false otherwise */
  public boolean isSetTransactionId() {
    return this.transactionId != null;
  }

  public void setTransactionIdIsSet(boolean value) {
    if (!value) {
      this.transactionId = null;
    }
  }

  /**
   * 实名结果，true：通过，false：不通过
   */
  public boolean isResult() {
    return this.result;
  }

  /**
   * 实名结果，true：通过，false：不通过
   */
  public CertificationAuditRequest setResult(boolean result) {
    this.result = result;
    setResultIsSet(true);
    return this;
  }

  public void unsetResult() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RESULT_ISSET_ID);
  }

  /** Returns true if field result is set (has been assigned a value) and false otherwise */
  public boolean isSetResult() {
    return EncodingUtils.testBit(__isset_bitfield, __RESULT_ISSET_ID);
  }

  public void setResultIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RESULT_ISSET_ID, value);
  }

  /**
   * 审核原因，不通过时为必填
   */
  public String getReason() {
    return this.reason;
  }

  /**
   * 审核原因，不通过时为必填
   */
  public CertificationAuditRequest setReason(String reason) {
    this.reason = reason;
    return this;
  }

  public void unsetReason() {
    this.reason = null;
  }

  /** Returns true if field reason is set (has been assigned a value) and false otherwise */
  public boolean isSetReason() {
    return this.reason != null;
  }

  public void setReasonIsSet(boolean value) {
    if (!value) {
      this.reason = null;
    }
  }

  /**
   * 管理员ID
   */
  public int getAdminId() {
    return this.adminId;
  }

  /**
   * 管理员ID
   */
  public CertificationAuditRequest setAdminId(int adminId) {
    this.adminId = adminId;
    setAdminIdIsSet(true);
    return this;
  }

  public void unsetAdminId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ADMINID_ISSET_ID);
  }

  /** Returns true if field adminId is set (has been assigned a value) and false otherwise */
  public boolean isSetAdminId() {
    return EncodingUtils.testBit(__isset_bitfield, __ADMINID_ISSET_ID);
  }

  public void setAdminIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ADMINID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case TRANSACTION_ID:
      if (value == null) {
        unsetTransactionId();
      } else {
        setTransactionId((String)value);
      }
      break;

    case RESULT:
      if (value == null) {
        unsetResult();
      } else {
        setResult((Boolean)value);
      }
      break;

    case REASON:
      if (value == null) {
        unsetReason();
      } else {
        setReason((String)value);
      }
      break;

    case ADMIN_ID:
      if (value == null) {
        unsetAdminId();
      } else {
        setAdminId((Integer)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case TRANSACTION_ID:
      return getTransactionId();

    case RESULT:
      return isResult();

    case REASON:
      return getReason();

    case ADMIN_ID:
      return getAdminId();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case TRANSACTION_ID:
      return isSetTransactionId();
    case RESULT:
      return isSetResult();
    case REASON:
      return isSetReason();
    case ADMIN_ID:
      return isSetAdminId();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof CertificationAuditRequest)
      return this.equals((CertificationAuditRequest)that);
    return false;
  }

  public boolean equals(CertificationAuditRequest that) {
    if (that == null)
      return false;

    boolean this_present_transactionId = true && this.isSetTransactionId();
    boolean that_present_transactionId = true && that.isSetTransactionId();
    if (this_present_transactionId || that_present_transactionId) {
      if (!(this_present_transactionId && that_present_transactionId))
        return false;
      if (!this.transactionId.equals(that.transactionId))
        return false;
    }

    boolean this_present_result = true;
    boolean that_present_result = true;
    if (this_present_result || that_present_result) {
      if (!(this_present_result && that_present_result))
        return false;
      if (this.result != that.result)
        return false;
    }

    boolean this_present_reason = true && this.isSetReason();
    boolean that_present_reason = true && that.isSetReason();
    if (this_present_reason || that_present_reason) {
      if (!(this_present_reason && that_present_reason))
        return false;
      if (!this.reason.equals(that.reason))
        return false;
    }

    boolean this_present_adminId = true;
    boolean that_present_adminId = true;
    if (this_present_adminId || that_present_adminId) {
      if (!(this_present_adminId && that_present_adminId))
        return false;
      if (this.adminId != that.adminId)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_transactionId = true && (isSetTransactionId());
    list.add(present_transactionId);
    if (present_transactionId)
      list.add(transactionId);

    boolean present_result = true;
    list.add(present_result);
    if (present_result)
      list.add(result);

    boolean present_reason = true && (isSetReason());
    list.add(present_reason);
    if (present_reason)
      list.add(reason);

    boolean present_adminId = true;
    list.add(present_adminId);
    if (present_adminId)
      list.add(adminId);

    return list.hashCode();
  }

  @Override
  public int compareTo(CertificationAuditRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetTransactionId()).compareTo(other.isSetTransactionId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTransactionId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.transactionId, other.transactionId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetResult()).compareTo(other.isSetResult());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetResult()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.result, other.result);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetReason()).compareTo(other.isSetReason());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetReason()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.reason, other.reason);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAdminId()).compareTo(other.isSetAdminId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAdminId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.adminId, other.adminId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("CertificationAuditRequest(");
    boolean first = true;

    sb.append("transactionId:");
    if (this.transactionId == null) {
      sb.append("null");
    } else {
      sb.append(this.transactionId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("result:");
    sb.append(this.result);
    first = false;
    if (isSetReason()) {
      if (!first) sb.append(", ");
      sb.append("reason:");
      if (this.reason == null) {
        sb.append("null");
      } else {
        sb.append(this.reason);
      }
      first = false;
    }
    if (!first) sb.append(", ");
    sb.append("adminId:");
    sb.append(this.adminId);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (transactionId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'transactionId' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'result' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'adminId' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CertificationAuditRequestStandardSchemeFactory implements SchemeFactory {
    public CertificationAuditRequestStandardScheme getScheme() {
      return new CertificationAuditRequestStandardScheme();
    }
  }

  private static class CertificationAuditRequestStandardScheme extends StandardScheme<CertificationAuditRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CertificationAuditRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // TRANSACTION_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.transactionId = iprot.readString();
              struct.setTransactionIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RESULT
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.result = iprot.readBool();
              struct.setResultIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // REASON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.reason = iprot.readString();
              struct.setReasonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // ADMIN_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.adminId = iprot.readI32();
              struct.setAdminIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetResult()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'result' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetAdminId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'adminId' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CertificationAuditRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.transactionId != null) {
        oprot.writeFieldBegin(TRANSACTION_ID_FIELD_DESC);
        oprot.writeString(struct.transactionId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(RESULT_FIELD_DESC);
      oprot.writeBool(struct.result);
      oprot.writeFieldEnd();
      if (struct.reason != null) {
        if (struct.isSetReason()) {
          oprot.writeFieldBegin(REASON_FIELD_DESC);
          oprot.writeString(struct.reason);
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldBegin(ADMIN_ID_FIELD_DESC);
      oprot.writeI32(struct.adminId);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CertificationAuditRequestTupleSchemeFactory implements SchemeFactory {
    public CertificationAuditRequestTupleScheme getScheme() {
      return new CertificationAuditRequestTupleScheme();
    }
  }

  private static class CertificationAuditRequestTupleScheme extends TupleScheme<CertificationAuditRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CertificationAuditRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeString(struct.transactionId);
      oprot.writeBool(struct.result);
      oprot.writeI32(struct.adminId);
      BitSet optionals = new BitSet();
      if (struct.isSetReason()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetReason()) {
        oprot.writeString(struct.reason);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CertificationAuditRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.transactionId = iprot.readString();
      struct.setTransactionIdIsSet(true);
      struct.result = iprot.readBool();
      struct.setResultIsSet(true);
      struct.adminId = iprot.readI32();
      struct.setAdminIdIsSet(true);
      BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        struct.reason = iprot.readString();
        struct.setReasonIsSet(true);
      }
    }
  }

}

