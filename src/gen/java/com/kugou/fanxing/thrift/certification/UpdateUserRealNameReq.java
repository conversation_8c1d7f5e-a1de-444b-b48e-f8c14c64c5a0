/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.thrift.certification;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2020-02-10")
public class UpdateUserRealNameReq implements org.apache.thrift.TBase<UpdateUserRealNameReq, UpdateUserRealNameReq._Fields>, java.io.Serializable, Cloneable, Comparable<UpdateUserRealNameReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("UpdateUserRealNameReq");

  private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField OLD_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("oldName", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField APP_CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("appCode", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField NEW_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("newName", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField SIGN_FIELD_DESC = new org.apache.thrift.protocol.TField("sign", org.apache.thrift.protocol.TType.STRING, (short)6);
  private static final org.apache.thrift.protocol.TField TIMESTAMP_FIELD_DESC = new org.apache.thrift.protocol.TField("timestamp", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField ADMIN_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("adminId", org.apache.thrift.protocol.TType.I64, (short)8);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new UpdateUserRealNameReqStandardSchemeFactory());
    schemes.put(TupleScheme.class, new UpdateUserRealNameReqTupleSchemeFactory());
  }

  public long kugouId; // required
  public String oldName; // required
  public String appCode; // required
  public String newName; // required
  public String sign; // required
  public long timestamp; // required
  public long adminId; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    KUGOU_ID((short)1, "kugouId"),
    OLD_NAME((short)3, "oldName"),
    APP_CODE((short)4, "appCode"),
    NEW_NAME((short)5, "newName"),
    SIGN((short)6, "sign"),
    TIMESTAMP((short)7, "timestamp"),
    ADMIN_ID((short)8, "adminId");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // KUGOU_ID
          return KUGOU_ID;
        case 3: // OLD_NAME
          return OLD_NAME;
        case 4: // APP_CODE
          return APP_CODE;
        case 5: // NEW_NAME
          return NEW_NAME;
        case 6: // SIGN
          return SIGN;
        case 7: // TIMESTAMP
          return TIMESTAMP;
        case 8: // ADMIN_ID
          return ADMIN_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __KUGOUID_ISSET_ID = 0;
  private static final int __TIMESTAMP_ISSET_ID = 1;
  private static final int __ADMINID_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.OLD_NAME, new org.apache.thrift.meta_data.FieldMetaData("oldName", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.APP_CODE, new org.apache.thrift.meta_data.FieldMetaData("appCode", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.NEW_NAME, new org.apache.thrift.meta_data.FieldMetaData("newName", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SIGN, new org.apache.thrift.meta_data.FieldMetaData("sign", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.TIMESTAMP, new org.apache.thrift.meta_data.FieldMetaData("timestamp", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ADMIN_ID, new org.apache.thrift.meta_data.FieldMetaData("adminId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(UpdateUserRealNameReq.class, metaDataMap);
  }

  public UpdateUserRealNameReq() {
  }

  public UpdateUserRealNameReq(
    long kugouId,
    String oldName,
    String appCode,
    String newName,
    String sign,
    long timestamp,
    long adminId)
  {
    this();
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    this.oldName = oldName;
    this.appCode = appCode;
    this.newName = newName;
    this.sign = sign;
    this.timestamp = timestamp;
    setTimestampIsSet(true);
    this.adminId = adminId;
    setAdminIdIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public UpdateUserRealNameReq(UpdateUserRealNameReq other) {
    __isset_bitfield = other.__isset_bitfield;
    this.kugouId = other.kugouId;
    if (other.isSetOldName()) {
      this.oldName = other.oldName;
    }
    if (other.isSetAppCode()) {
      this.appCode = other.appCode;
    }
    if (other.isSetNewName()) {
      this.newName = other.newName;
    }
    if (other.isSetSign()) {
      this.sign = other.sign;
    }
    this.timestamp = other.timestamp;
    this.adminId = other.adminId;
  }

  public UpdateUserRealNameReq deepCopy() {
    return new UpdateUserRealNameReq(this);
  }

  @Override
  public void clear() {
    setKugouIdIsSet(false);
    this.kugouId = 0;
    this.oldName = null;
    this.appCode = null;
    this.newName = null;
    this.sign = null;
    setTimestampIsSet(false);
    this.timestamp = 0;
    setAdminIdIsSet(false);
    this.adminId = 0;
  }

  public long getKugouId() {
    return this.kugouId;
  }

  public UpdateUserRealNameReq setKugouId(long kugouId) {
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    return this;
  }

  public void unsetKugouId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
  public boolean isSetKugouId() {
    return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  public void setKugouIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
  }

  public String getOldName() {
    return this.oldName;
  }

  public UpdateUserRealNameReq setOldName(String oldName) {
    this.oldName = oldName;
    return this;
  }

  public void unsetOldName() {
    this.oldName = null;
  }

  /** Returns true if field oldName is set (has been assigned a value) and false otherwise */
  public boolean isSetOldName() {
    return this.oldName != null;
  }

  public void setOldNameIsSet(boolean value) {
    if (!value) {
      this.oldName = null;
    }
  }

  public String getAppCode() {
    return this.appCode;
  }

  public UpdateUserRealNameReq setAppCode(String appCode) {
    this.appCode = appCode;
    return this;
  }

  public void unsetAppCode() {
    this.appCode = null;
  }

  /** Returns true if field appCode is set (has been assigned a value) and false otherwise */
  public boolean isSetAppCode() {
    return this.appCode != null;
  }

  public void setAppCodeIsSet(boolean value) {
    if (!value) {
      this.appCode = null;
    }
  }

  public String getNewName() {
    return this.newName;
  }

  public UpdateUserRealNameReq setNewName(String newName) {
    this.newName = newName;
    return this;
  }

  public void unsetNewName() {
    this.newName = null;
  }

  /** Returns true if field newName is set (has been assigned a value) and false otherwise */
  public boolean isSetNewName() {
    return this.newName != null;
  }

  public void setNewNameIsSet(boolean value) {
    if (!value) {
      this.newName = null;
    }
  }

  public String getSign() {
    return this.sign;
  }

  public UpdateUserRealNameReq setSign(String sign) {
    this.sign = sign;
    return this;
  }

  public void unsetSign() {
    this.sign = null;
  }

  /** Returns true if field sign is set (has been assigned a value) and false otherwise */
  public boolean isSetSign() {
    return this.sign != null;
  }

  public void setSignIsSet(boolean value) {
    if (!value) {
      this.sign = null;
    }
  }

  public long getTimestamp() {
    return this.timestamp;
  }

  public UpdateUserRealNameReq setTimestamp(long timestamp) {
    this.timestamp = timestamp;
    setTimestampIsSet(true);
    return this;
  }

  public void unsetTimestamp() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
  }

  /** Returns true if field timestamp is set (has been assigned a value) and false otherwise */
  public boolean isSetTimestamp() {
    return EncodingUtils.testBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
  }

  public void setTimestampIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TIMESTAMP_ISSET_ID, value);
  }

  public long getAdminId() {
    return this.adminId;
  }

  public UpdateUserRealNameReq setAdminId(long adminId) {
    this.adminId = adminId;
    setAdminIdIsSet(true);
    return this;
  }

  public void unsetAdminId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ADMINID_ISSET_ID);
  }

  /** Returns true if field adminId is set (has been assigned a value) and false otherwise */
  public boolean isSetAdminId() {
    return EncodingUtils.testBit(__isset_bitfield, __ADMINID_ISSET_ID);
  }

  public void setAdminIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ADMINID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case KUGOU_ID:
      if (value == null) {
        unsetKugouId();
      } else {
        setKugouId((Long)value);
      }
      break;

    case OLD_NAME:
      if (value == null) {
        unsetOldName();
      } else {
        setOldName((String)value);
      }
      break;

    case APP_CODE:
      if (value == null) {
        unsetAppCode();
      } else {
        setAppCode((String)value);
      }
      break;

    case NEW_NAME:
      if (value == null) {
        unsetNewName();
      } else {
        setNewName((String)value);
      }
      break;

    case SIGN:
      if (value == null) {
        unsetSign();
      } else {
        setSign((String)value);
      }
      break;

    case TIMESTAMP:
      if (value == null) {
        unsetTimestamp();
      } else {
        setTimestamp((Long)value);
      }
      break;

    case ADMIN_ID:
      if (value == null) {
        unsetAdminId();
      } else {
        setAdminId((Long)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case KUGOU_ID:
      return getKugouId();

    case OLD_NAME:
      return getOldName();

    case APP_CODE:
      return getAppCode();

    case NEW_NAME:
      return getNewName();

    case SIGN:
      return getSign();

    case TIMESTAMP:
      return getTimestamp();

    case ADMIN_ID:
      return getAdminId();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case KUGOU_ID:
      return isSetKugouId();
    case OLD_NAME:
      return isSetOldName();
    case APP_CODE:
      return isSetAppCode();
    case NEW_NAME:
      return isSetNewName();
    case SIGN:
      return isSetSign();
    case TIMESTAMP:
      return isSetTimestamp();
    case ADMIN_ID:
      return isSetAdminId();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof UpdateUserRealNameReq)
      return this.equals((UpdateUserRealNameReq)that);
    return false;
  }

  public boolean equals(UpdateUserRealNameReq that) {
    if (that == null)
      return false;

    boolean this_present_kugouId = true;
    boolean that_present_kugouId = true;
    if (this_present_kugouId || that_present_kugouId) {
      if (!(this_present_kugouId && that_present_kugouId))
        return false;
      if (this.kugouId != that.kugouId)
        return false;
    }

    boolean this_present_oldName = true && this.isSetOldName();
    boolean that_present_oldName = true && that.isSetOldName();
    if (this_present_oldName || that_present_oldName) {
      if (!(this_present_oldName && that_present_oldName))
        return false;
      if (!this.oldName.equals(that.oldName))
        return false;
    }

    boolean this_present_appCode = true && this.isSetAppCode();
    boolean that_present_appCode = true && that.isSetAppCode();
    if (this_present_appCode || that_present_appCode) {
      if (!(this_present_appCode && that_present_appCode))
        return false;
      if (!this.appCode.equals(that.appCode))
        return false;
    }

    boolean this_present_newName = true && this.isSetNewName();
    boolean that_present_newName = true && that.isSetNewName();
    if (this_present_newName || that_present_newName) {
      if (!(this_present_newName && that_present_newName))
        return false;
      if (!this.newName.equals(that.newName))
        return false;
    }

    boolean this_present_sign = true && this.isSetSign();
    boolean that_present_sign = true && that.isSetSign();
    if (this_present_sign || that_present_sign) {
      if (!(this_present_sign && that_present_sign))
        return false;
      if (!this.sign.equals(that.sign))
        return false;
    }

    boolean this_present_timestamp = true;
    boolean that_present_timestamp = true;
    if (this_present_timestamp || that_present_timestamp) {
      if (!(this_present_timestamp && that_present_timestamp))
        return false;
      if (this.timestamp != that.timestamp)
        return false;
    }

    boolean this_present_adminId = true;
    boolean that_present_adminId = true;
    if (this_present_adminId || that_present_adminId) {
      if (!(this_present_adminId && that_present_adminId))
        return false;
      if (this.adminId != that.adminId)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_kugouId = true;
    list.add(present_kugouId);
    if (present_kugouId)
      list.add(kugouId);

    boolean present_oldName = true && (isSetOldName());
    list.add(present_oldName);
    if (present_oldName)
      list.add(oldName);

    boolean present_appCode = true && (isSetAppCode());
    list.add(present_appCode);
    if (present_appCode)
      list.add(appCode);

    boolean present_newName = true && (isSetNewName());
    list.add(present_newName);
    if (present_newName)
      list.add(newName);

    boolean present_sign = true && (isSetSign());
    list.add(present_sign);
    if (present_sign)
      list.add(sign);

    boolean present_timestamp = true;
    list.add(present_timestamp);
    if (present_timestamp)
      list.add(timestamp);

    boolean present_adminId = true;
    list.add(present_adminId);
    if (present_adminId)
      list.add(adminId);

    return list.hashCode();
  }

  @Override
  public int compareTo(UpdateUserRealNameReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKugouId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetOldName()).compareTo(other.isSetOldName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOldName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.oldName, other.oldName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAppCode()).compareTo(other.isSetAppCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appCode, other.appCode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetNewName()).compareTo(other.isSetNewName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetNewName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.newName, other.newName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSign()).compareTo(other.isSetSign());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSign()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sign, other.sign);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTimestamp()).compareTo(other.isSetTimestamp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTimestamp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.timestamp, other.timestamp);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAdminId()).compareTo(other.isSetAdminId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAdminId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.adminId, other.adminId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("UpdateUserRealNameReq(");
    boolean first = true;

    sb.append("kugouId:");
    sb.append(this.kugouId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("oldName:");
    if (this.oldName == null) {
      sb.append("null");
    } else {
      sb.append(this.oldName);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("appCode:");
    if (this.appCode == null) {
      sb.append("null");
    } else {
      sb.append(this.appCode);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("newName:");
    if (this.newName == null) {
      sb.append("null");
    } else {
      sb.append(this.newName);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("sign:");
    if (this.sign == null) {
      sb.append("null");
    } else {
      sb.append(this.sign);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("timestamp:");
    sb.append(this.timestamp);
    first = false;
    if (!first) sb.append(", ");
    sb.append("adminId:");
    sb.append(this.adminId);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
    if (oldName == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'oldName' was not present! Struct: " + toString());
    }
    if (appCode == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'appCode' was not present! Struct: " + toString());
    }
    if (newName == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'newName' was not present! Struct: " + toString());
    }
    if (sign == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'sign' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'timestamp' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'adminId' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class UpdateUserRealNameReqStandardSchemeFactory implements SchemeFactory {
    public UpdateUserRealNameReqStandardScheme getScheme() {
      return new UpdateUserRealNameReqStandardScheme();
    }
  }

  private static class UpdateUserRealNameReqStandardScheme extends StandardScheme<UpdateUserRealNameReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, UpdateUserRealNameReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // KUGOU_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.kugouId = iprot.readI64();
              struct.setKugouIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // OLD_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.oldName = iprot.readString();
              struct.setOldNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // APP_CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.appCode = iprot.readString();
              struct.setAppCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // NEW_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.newName = iprot.readString();
              struct.setNewNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // SIGN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.sign = iprot.readString();
              struct.setSignIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // TIMESTAMP
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.timestamp = iprot.readI64();
              struct.setTimestampIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // ADMIN_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.adminId = iprot.readI64();
              struct.setAdminIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetKugouId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetTimestamp()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'timestamp' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetAdminId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'adminId' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, UpdateUserRealNameReq struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
      oprot.writeI64(struct.kugouId);
      oprot.writeFieldEnd();
      if (struct.oldName != null) {
        oprot.writeFieldBegin(OLD_NAME_FIELD_DESC);
        oprot.writeString(struct.oldName);
        oprot.writeFieldEnd();
      }
      if (struct.appCode != null) {
        oprot.writeFieldBegin(APP_CODE_FIELD_DESC);
        oprot.writeString(struct.appCode);
        oprot.writeFieldEnd();
      }
      if (struct.newName != null) {
        oprot.writeFieldBegin(NEW_NAME_FIELD_DESC);
        oprot.writeString(struct.newName);
        oprot.writeFieldEnd();
      }
      if (struct.sign != null) {
        oprot.writeFieldBegin(SIGN_FIELD_DESC);
        oprot.writeString(struct.sign);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(TIMESTAMP_FIELD_DESC);
      oprot.writeI64(struct.timestamp);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ADMIN_ID_FIELD_DESC);
      oprot.writeI64(struct.adminId);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class UpdateUserRealNameReqTupleSchemeFactory implements SchemeFactory {
    public UpdateUserRealNameReqTupleScheme getScheme() {
      return new UpdateUserRealNameReqTupleScheme();
    }
  }

  private static class UpdateUserRealNameReqTupleScheme extends TupleScheme<UpdateUserRealNameReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, UpdateUserRealNameReq struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeI64(struct.kugouId);
      oprot.writeString(struct.oldName);
      oprot.writeString(struct.appCode);
      oprot.writeString(struct.newName);
      oprot.writeString(struct.sign);
      oprot.writeI64(struct.timestamp);
      oprot.writeI64(struct.adminId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, UpdateUserRealNameReq struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.kugouId = iprot.readI64();
      struct.setKugouIdIsSet(true);
      struct.oldName = iprot.readString();
      struct.setOldNameIsSet(true);
      struct.appCode = iprot.readString();
      struct.setAppCodeIsSet(true);
      struct.newName = iprot.readString();
      struct.setNewNameIsSet(true);
      struct.sign = iprot.readString();
      struct.setSignIsSet(true);
      struct.timestamp = iprot.readI64();
      struct.setTimestampIsSet(true);
      struct.adminId = iprot.readI64();
      struct.setAdminIdIsSet(true);
    }
  }

}

