/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.thrift.plat.user;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-03-05")
public class UserPlatService {

  /**
   * 用户平台服务
   * 
   */
  public interface Iface {

    /**
     * * 保存用户信息
     *    *
     * 
     * @param userVO
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResSave saveUser(com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO) throws org.apache.thrift.TException;

    /**
     * 更新用户信息
     * 
     * 
     * @param userVO
     * @param token
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResSave update(com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO, String token) throws org.apache.thrift.TException;

    /**
     * kugouId列表获取用户信息列表,限制100个id
     * 
     * 
     * @param kugouIds
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersByKugouIds(List<Long> kugouIds) throws org.apache.thrift.TException;

    /**
     * 根据kugouId查找用户信息
     * 
     * 
     * @param kugouId
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResUser getUserByKugouId(long kugouId) throws org.apache.thrift.TException;

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser getSingleUserByKugouId(long kugouId) throws org.apache.thrift.TException;

    /**
     * 根据kugouId初始化用户信息
     * 
     * 
     * @param kugouId
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg initUserByKugouId(long kugouId) throws org.apache.thrift.TException;

    /**
     * 根据kugouId初始化用户信息，并返回用户信息
     * 
     * 
     * @param kugouId
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResUser initUserByKugouIdV2(long kugouId) throws org.apache.thrift.TException;

    /**
     * 根据用户名查找用户信息
     *  
     * 
     * @param userName
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResUser getUserByUserName(String userName) throws org.apache.thrift.TException;

    /**
     * 根据用户名列表获取用户信息列表,限制100个用户名
     * 
     * 
     * @param userNames
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersByUserNames(List<String> userNames) throws org.apache.thrift.TException;

    /**
     * 根据昵称查找用户信息
     * 
     * 
     * @param nickName
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResUser getUserByNickName(String nickName) throws org.apache.thrift.TException;

    /**
     * 根据昵称列表获取用户信息列表,限制100个用户名
     * 
     * 
     * @param nickNames
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersByNickNames(List<String> nickNames) throws org.apache.thrift.TException;

    /**
     * 是否存在昵称
     * 
     * 
     * @param nickName
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg isExistNickName(String nickName) throws org.apache.thrift.TException;

    /**
     * 批量动态获取用户信息，参数2的userVO对象，需要返回的字段，随意设置一个值，就会在查询结果中返回；如果查询不需要返回，则不要设置值
     * 另外，参数DynamicResponseUserVO是必填的，如果不设置值，则不返回任何信息
     * *
     * 
     * @param kugouIds
     * @param dynamicResponseUserVO
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUserVOsByKugouIdsDynamic(List<Long> kugouIds, com.kugou.fanxing.thrift.plat.user.vo.DynamicResponseUserVO dynamicResponseUserVO) throws org.apache.thrift.TException;

  }

  public interface AsyncIface {

    public void saveUser(com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void update(com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO, String token, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUsersByKugouIds(List<Long> kugouIds, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUserByKugouId(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getSingleUserByKugouId(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void initUserByKugouId(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void initUserByKugouIdV2(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUserByUserName(String userName, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUsersByUserNames(List<String> userNames, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUserByNickName(String nickName, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUsersByNickNames(List<String> nickNames, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void isExistNickName(String nickName, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUserVOsByKugouIdsDynamic(List<Long> kugouIds, com.kugou.fanxing.thrift.plat.user.vo.DynamicResponseUserVO dynamicResponseUserVO, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

  }

  public static class Client extends org.apache.thrift.TServiceClient implements Iface {
    public static class Factory implements org.apache.thrift.TServiceClientFactory<Client> {
      public Factory() {}
      public Client getClient(org.apache.thrift.protocol.TProtocol prot) {
        return new Client(prot);
      }
      public Client getClient(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
        return new Client(iprot, oprot);
      }
    }

    public Client(org.apache.thrift.protocol.TProtocol prot)
    {
      super(prot, prot);
    }

    public Client(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
      super(iprot, oprot);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResSave saveUser(com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO) throws org.apache.thrift.TException
    {
      send_saveUser(userVO);
      return recv_saveUser();
    }

    public void send_saveUser(com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO) throws org.apache.thrift.TException
    {
      saveUser_args args = new saveUser_args();
      args.setUserVO(userVO);
      sendBase("saveUser", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResSave recv_saveUser() throws org.apache.thrift.TException
    {
      saveUser_result result = new saveUser_result();
      receiveBase(result, "saveUser");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "saveUser failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResSave update(com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO, String token) throws org.apache.thrift.TException
    {
      send_update(userVO, token);
      return recv_update();
    }

    public void send_update(com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO, String token) throws org.apache.thrift.TException
    {
      update_args args = new update_args();
      args.setUserVO(userVO);
      args.setToken(token);
      sendBase("update", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResSave recv_update() throws org.apache.thrift.TException
    {
      update_result result = new update_result();
      receiveBase(result, "update");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "update failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersByKugouIds(List<Long> kugouIds) throws org.apache.thrift.TException
    {
      send_getUsersByKugouIds(kugouIds);
      return recv_getUsersByKugouIds();
    }

    public void send_getUsersByKugouIds(List<Long> kugouIds) throws org.apache.thrift.TException
    {
      getUsersByKugouIds_args args = new getUsersByKugouIds_args();
      args.setKugouIds(kugouIds);
      sendBase("getUsersByKugouIds", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser recv_getUsersByKugouIds() throws org.apache.thrift.TException
    {
      getUsersByKugouIds_result result = new getUsersByKugouIds_result();
      receiveBase(result, "getUsersByKugouIds");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUsersByKugouIds failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser getUserByKugouId(long kugouId) throws org.apache.thrift.TException
    {
      send_getUserByKugouId(kugouId);
      return recv_getUserByKugouId();
    }

    public void send_getUserByKugouId(long kugouId) throws org.apache.thrift.TException
    {
      getUserByKugouId_args args = new getUserByKugouId_args();
      args.setKugouId(kugouId);
      sendBase("getUserByKugouId", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser recv_getUserByKugouId() throws org.apache.thrift.TException
    {
      getUserByKugouId_result result = new getUserByKugouId_result();
      receiveBase(result, "getUserByKugouId");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUserByKugouId failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser getSingleUserByKugouId(long kugouId) throws org.apache.thrift.TException
    {
      send_getSingleUserByKugouId(kugouId);
      return recv_getSingleUserByKugouId();
    }

    public void send_getSingleUserByKugouId(long kugouId) throws org.apache.thrift.TException
    {
      getSingleUserByKugouId_args args = new getSingleUserByKugouId_args();
      args.setKugouId(kugouId);
      sendBase("getSingleUserByKugouId", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser recv_getSingleUserByKugouId() throws org.apache.thrift.TException
    {
      getSingleUserByKugouId_result result = new getSingleUserByKugouId_result();
      receiveBase(result, "getSingleUserByKugouId");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getSingleUserByKugouId failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg initUserByKugouId(long kugouId) throws org.apache.thrift.TException
    {
      send_initUserByKugouId(kugouId);
      return recv_initUserByKugouId();
    }

    public void send_initUserByKugouId(long kugouId) throws org.apache.thrift.TException
    {
      initUserByKugouId_args args = new initUserByKugouId_args();
      args.setKugouId(kugouId);
      sendBase("initUserByKugouId", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg recv_initUserByKugouId() throws org.apache.thrift.TException
    {
      initUserByKugouId_result result = new initUserByKugouId_result();
      receiveBase(result, "initUserByKugouId");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "initUserByKugouId failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser initUserByKugouIdV2(long kugouId) throws org.apache.thrift.TException
    {
      send_initUserByKugouIdV2(kugouId);
      return recv_initUserByKugouIdV2();
    }

    public void send_initUserByKugouIdV2(long kugouId) throws org.apache.thrift.TException
    {
      initUserByKugouIdV2_args args = new initUserByKugouIdV2_args();
      args.setKugouId(kugouId);
      sendBase("initUserByKugouIdV2", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser recv_initUserByKugouIdV2() throws org.apache.thrift.TException
    {
      initUserByKugouIdV2_result result = new initUserByKugouIdV2_result();
      receiveBase(result, "initUserByKugouIdV2");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "initUserByKugouIdV2 failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser getUserByUserName(String userName) throws org.apache.thrift.TException
    {
      send_getUserByUserName(userName);
      return recv_getUserByUserName();
    }

    public void send_getUserByUserName(String userName) throws org.apache.thrift.TException
    {
      getUserByUserName_args args = new getUserByUserName_args();
      args.setUserName(userName);
      sendBase("getUserByUserName", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser recv_getUserByUserName() throws org.apache.thrift.TException
    {
      getUserByUserName_result result = new getUserByUserName_result();
      receiveBase(result, "getUserByUserName");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUserByUserName failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersByUserNames(List<String> userNames) throws org.apache.thrift.TException
    {
      send_getUsersByUserNames(userNames);
      return recv_getUsersByUserNames();
    }

    public void send_getUsersByUserNames(List<String> userNames) throws org.apache.thrift.TException
    {
      getUsersByUserNames_args args = new getUsersByUserNames_args();
      args.setUserNames(userNames);
      sendBase("getUsersByUserNames", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser recv_getUsersByUserNames() throws org.apache.thrift.TException
    {
      getUsersByUserNames_result result = new getUsersByUserNames_result();
      receiveBase(result, "getUsersByUserNames");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUsersByUserNames failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser getUserByNickName(String nickName) throws org.apache.thrift.TException
    {
      send_getUserByNickName(nickName);
      return recv_getUserByNickName();
    }

    public void send_getUserByNickName(String nickName) throws org.apache.thrift.TException
    {
      getUserByNickName_args args = new getUserByNickName_args();
      args.setNickName(nickName);
      sendBase("getUserByNickName", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser recv_getUserByNickName() throws org.apache.thrift.TException
    {
      getUserByNickName_result result = new getUserByNickName_result();
      receiveBase(result, "getUserByNickName");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUserByNickName failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersByNickNames(List<String> nickNames) throws org.apache.thrift.TException
    {
      send_getUsersByNickNames(nickNames);
      return recv_getUsersByNickNames();
    }

    public void send_getUsersByNickNames(List<String> nickNames) throws org.apache.thrift.TException
    {
      getUsersByNickNames_args args = new getUsersByNickNames_args();
      args.setNickNames(nickNames);
      sendBase("getUsersByNickNames", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser recv_getUsersByNickNames() throws org.apache.thrift.TException
    {
      getUsersByNickNames_result result = new getUsersByNickNames_result();
      receiveBase(result, "getUsersByNickNames");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUsersByNickNames failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg isExistNickName(String nickName) throws org.apache.thrift.TException
    {
      send_isExistNickName(nickName);
      return recv_isExistNickName();
    }

    public void send_isExistNickName(String nickName) throws org.apache.thrift.TException
    {
      isExistNickName_args args = new isExistNickName_args();
      args.setNickName(nickName);
      sendBase("isExistNickName", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg recv_isExistNickName() throws org.apache.thrift.TException
    {
      isExistNickName_result result = new isExistNickName_result();
      receiveBase(result, "isExistNickName");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "isExistNickName failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUserVOsByKugouIdsDynamic(List<Long> kugouIds, com.kugou.fanxing.thrift.plat.user.vo.DynamicResponseUserVO dynamicResponseUserVO) throws org.apache.thrift.TException
    {
      send_getUserVOsByKugouIdsDynamic(kugouIds, dynamicResponseUserVO);
      return recv_getUserVOsByKugouIdsDynamic();
    }

    public void send_getUserVOsByKugouIdsDynamic(List<Long> kugouIds, com.kugou.fanxing.thrift.plat.user.vo.DynamicResponseUserVO dynamicResponseUserVO) throws org.apache.thrift.TException
    {
      getUserVOsByKugouIdsDynamic_args args = new getUserVOsByKugouIdsDynamic_args();
      args.setKugouIds(kugouIds);
      args.setDynamicResponseUserVO(dynamicResponseUserVO);
      sendBase("getUserVOsByKugouIdsDynamic", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser recv_getUserVOsByKugouIdsDynamic() throws org.apache.thrift.TException
    {
      getUserVOsByKugouIdsDynamic_result result = new getUserVOsByKugouIdsDynamic_result();
      receiveBase(result, "getUserVOsByKugouIdsDynamic");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUserVOsByKugouIdsDynamic failed: unknown result");
    }

  }
  public static class AsyncClient extends org.apache.thrift.async.TAsyncClient implements AsyncIface {
    public static class Factory implements org.apache.thrift.async.TAsyncClientFactory<AsyncClient> {
      private org.apache.thrift.async.TAsyncClientManager clientManager;
      private org.apache.thrift.protocol.TProtocolFactory protocolFactory;
      public Factory(org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.protocol.TProtocolFactory protocolFactory) {
        this.clientManager = clientManager;
        this.protocolFactory = protocolFactory;
      }
      public AsyncClient getAsyncClient(org.apache.thrift.transport.TNonblockingTransport transport) {
        return new AsyncClient(protocolFactory, clientManager, transport);
      }
    }

    public AsyncClient(org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.transport.TNonblockingTransport transport) {
      super(protocolFactory, clientManager, transport);
    }

    public void saveUser(com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      saveUser_call method_call = new saveUser_call(userVO, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class saveUser_call extends org.apache.thrift.async.TAsyncMethodCall {
      private com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO;
      public saveUser_call(com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.userVO = userVO;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("saveUser", org.apache.thrift.protocol.TMessageType.CALL, 0));
        saveUser_args args = new saveUser_args();
        args.setUserVO(userVO);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResSave getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_saveUser();
      }
    }

    public void update(com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO, String token, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      update_call method_call = new update_call(userVO, token, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class update_call extends org.apache.thrift.async.TAsyncMethodCall {
      private com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO;
      private String token;
      public update_call(com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO, String token, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.userVO = userVO;
        this.token = token;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("update", org.apache.thrift.protocol.TMessageType.CALL, 0));
        update_args args = new update_args();
        args.setUserVO(userVO);
        args.setToken(token);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResSave getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_update();
      }
    }

    public void getUsersByKugouIds(List<Long> kugouIds, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUsersByKugouIds_call method_call = new getUsersByKugouIds_call(kugouIds, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUsersByKugouIds_call extends org.apache.thrift.async.TAsyncMethodCall {
      private List<Long> kugouIds;
      public getUsersByKugouIds_call(List<Long> kugouIds, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouIds = kugouIds;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUsersByKugouIds", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUsersByKugouIds_args args = new getUsersByKugouIds_args();
        args.setKugouIds(kugouIds);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUsersByKugouIds();
      }
    }

    public void getUserByKugouId(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUserByKugouId_call method_call = new getUserByKugouId_call(kugouId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUserByKugouId_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      public getUserByKugouId_call(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUserByKugouId", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUserByKugouId_args args = new getUserByKugouId_args();
        args.setKugouId(kugouId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResUser getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUserByKugouId();
      }
    }

    public void getSingleUserByKugouId(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getSingleUserByKugouId_call method_call = new getSingleUserByKugouId_call(kugouId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getSingleUserByKugouId_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      public getSingleUserByKugouId_call(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getSingleUserByKugouId", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getSingleUserByKugouId_args args = new getSingleUserByKugouId_args();
        args.setKugouId(kugouId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResUser getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getSingleUserByKugouId();
      }
    }

    public void initUserByKugouId(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      initUserByKugouId_call method_call = new initUserByKugouId_call(kugouId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class initUserByKugouId_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      public initUserByKugouId_call(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("initUserByKugouId", org.apache.thrift.protocol.TMessageType.CALL, 0));
        initUserByKugouId_args args = new initUserByKugouId_args();
        args.setKugouId(kugouId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_initUserByKugouId();
      }
    }

    public void initUserByKugouIdV2(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      initUserByKugouIdV2_call method_call = new initUserByKugouIdV2_call(kugouId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class initUserByKugouIdV2_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      public initUserByKugouIdV2_call(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("initUserByKugouIdV2", org.apache.thrift.protocol.TMessageType.CALL, 0));
        initUserByKugouIdV2_args args = new initUserByKugouIdV2_args();
        args.setKugouId(kugouId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResUser getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_initUserByKugouIdV2();
      }
    }

    public void getUserByUserName(String userName, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUserByUserName_call method_call = new getUserByUserName_call(userName, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUserByUserName_call extends org.apache.thrift.async.TAsyncMethodCall {
      private String userName;
      public getUserByUserName_call(String userName, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.userName = userName;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUserByUserName", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUserByUserName_args args = new getUserByUserName_args();
        args.setUserName(userName);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResUser getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUserByUserName();
      }
    }

    public void getUsersByUserNames(List<String> userNames, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUsersByUserNames_call method_call = new getUsersByUserNames_call(userNames, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUsersByUserNames_call extends org.apache.thrift.async.TAsyncMethodCall {
      private List<String> userNames;
      public getUsersByUserNames_call(List<String> userNames, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.userNames = userNames;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUsersByUserNames", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUsersByUserNames_args args = new getUsersByUserNames_args();
        args.setUserNames(userNames);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUsersByUserNames();
      }
    }

    public void getUserByNickName(String nickName, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUserByNickName_call method_call = new getUserByNickName_call(nickName, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUserByNickName_call extends org.apache.thrift.async.TAsyncMethodCall {
      private String nickName;
      public getUserByNickName_call(String nickName, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.nickName = nickName;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUserByNickName", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUserByNickName_args args = new getUserByNickName_args();
        args.setNickName(nickName);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResUser getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUserByNickName();
      }
    }

    public void getUsersByNickNames(List<String> nickNames, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUsersByNickNames_call method_call = new getUsersByNickNames_call(nickNames, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUsersByNickNames_call extends org.apache.thrift.async.TAsyncMethodCall {
      private List<String> nickNames;
      public getUsersByNickNames_call(List<String> nickNames, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.nickNames = nickNames;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUsersByNickNames", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUsersByNickNames_args args = new getUsersByNickNames_args();
        args.setNickNames(nickNames);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUsersByNickNames();
      }
    }

    public void isExistNickName(String nickName, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      isExistNickName_call method_call = new isExistNickName_call(nickName, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class isExistNickName_call extends org.apache.thrift.async.TAsyncMethodCall {
      private String nickName;
      public isExistNickName_call(String nickName, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.nickName = nickName;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("isExistNickName", org.apache.thrift.protocol.TMessageType.CALL, 0));
        isExistNickName_args args = new isExistNickName_args();
        args.setNickName(nickName);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_isExistNickName();
      }
    }

    public void getUserVOsByKugouIdsDynamic(List<Long> kugouIds, com.kugou.fanxing.thrift.plat.user.vo.DynamicResponseUserVO dynamicResponseUserVO, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUserVOsByKugouIdsDynamic_call method_call = new getUserVOsByKugouIdsDynamic_call(kugouIds, dynamicResponseUserVO, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUserVOsByKugouIdsDynamic_call extends org.apache.thrift.async.TAsyncMethodCall {
      private List<Long> kugouIds;
      private com.kugou.fanxing.thrift.plat.user.vo.DynamicResponseUserVO dynamicResponseUserVO;
      public getUserVOsByKugouIdsDynamic_call(List<Long> kugouIds, com.kugou.fanxing.thrift.plat.user.vo.DynamicResponseUserVO dynamicResponseUserVO, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouIds = kugouIds;
        this.dynamicResponseUserVO = dynamicResponseUserVO;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUserVOsByKugouIdsDynamic", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUserVOsByKugouIdsDynamic_args args = new getUserVOsByKugouIdsDynamic_args();
        args.setKugouIds(kugouIds);
        args.setDynamicResponseUserVO(dynamicResponseUserVO);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUserVOsByKugouIdsDynamic();
      }
    }

  }

  public static class Processor<I extends Iface> extends org.apache.thrift.TBaseProcessor<I> implements org.apache.thrift.TProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());
    public Processor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>>()));
    }

    protected Processor(I iface, Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends Iface> Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> getProcessMap(Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      processMap.put("saveUser", new saveUser());
      processMap.put("update", new update());
      processMap.put("getUsersByKugouIds", new getUsersByKugouIds());
      processMap.put("getUserByKugouId", new getUserByKugouId());
      processMap.put("getSingleUserByKugouId", new getSingleUserByKugouId());
      processMap.put("initUserByKugouId", new initUserByKugouId());
      processMap.put("initUserByKugouIdV2", new initUserByKugouIdV2());
      processMap.put("getUserByUserName", new getUserByUserName());
      processMap.put("getUsersByUserNames", new getUsersByUserNames());
      processMap.put("getUserByNickName", new getUserByNickName());
      processMap.put("getUsersByNickNames", new getUsersByNickNames());
      processMap.put("isExistNickName", new isExistNickName());
      processMap.put("getUserVOsByKugouIdsDynamic", new getUserVOsByKugouIdsDynamic());
      return processMap;
    }

    public static class saveUser<I extends Iface> extends org.apache.thrift.ProcessFunction<I, saveUser_args> {
      public saveUser() {
        super("saveUser");
      }

      public saveUser_args getEmptyArgsInstance() {
        return new saveUser_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public saveUser_result getResult(I iface, saveUser_args args) throws org.apache.thrift.TException {
        saveUser_result result = new saveUser_result();
        result.success = iface.saveUser(args.userVO);
        return result;
      }
    }

    public static class update<I extends Iface> extends org.apache.thrift.ProcessFunction<I, update_args> {
      public update() {
        super("update");
      }

      public update_args getEmptyArgsInstance() {
        return new update_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public update_result getResult(I iface, update_args args) throws org.apache.thrift.TException {
        update_result result = new update_result();
        result.success = iface.update(args.userVO, args.token);
        return result;
      }
    }

    public static class getUsersByKugouIds<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUsersByKugouIds_args> {
      public getUsersByKugouIds() {
        super("getUsersByKugouIds");
      }

      public getUsersByKugouIds_args getEmptyArgsInstance() {
        return new getUsersByKugouIds_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUsersByKugouIds_result getResult(I iface, getUsersByKugouIds_args args) throws org.apache.thrift.TException {
        getUsersByKugouIds_result result = new getUsersByKugouIds_result();
        result.success = iface.getUsersByKugouIds(args.kugouIds);
        return result;
      }
    }

    public static class getUserByKugouId<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUserByKugouId_args> {
      public getUserByKugouId() {
        super("getUserByKugouId");
      }

      public getUserByKugouId_args getEmptyArgsInstance() {
        return new getUserByKugouId_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUserByKugouId_result getResult(I iface, getUserByKugouId_args args) throws org.apache.thrift.TException {
        getUserByKugouId_result result = new getUserByKugouId_result();
        result.success = iface.getUserByKugouId(args.kugouId);
        return result;
      }
    }

    public static class getSingleUserByKugouId<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getSingleUserByKugouId_args> {
      public getSingleUserByKugouId() {
        super("getSingleUserByKugouId");
      }

      public getSingleUserByKugouId_args getEmptyArgsInstance() {
        return new getSingleUserByKugouId_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getSingleUserByKugouId_result getResult(I iface, getSingleUserByKugouId_args args) throws org.apache.thrift.TException {
        getSingleUserByKugouId_result result = new getSingleUserByKugouId_result();
        result.success = iface.getSingleUserByKugouId(args.kugouId);
        return result;
      }
    }

    public static class initUserByKugouId<I extends Iface> extends org.apache.thrift.ProcessFunction<I, initUserByKugouId_args> {
      public initUserByKugouId() {
        super("initUserByKugouId");
      }

      public initUserByKugouId_args getEmptyArgsInstance() {
        return new initUserByKugouId_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public initUserByKugouId_result getResult(I iface, initUserByKugouId_args args) throws org.apache.thrift.TException {
        initUserByKugouId_result result = new initUserByKugouId_result();
        result.success = iface.initUserByKugouId(args.kugouId);
        return result;
      }
    }

    public static class initUserByKugouIdV2<I extends Iface> extends org.apache.thrift.ProcessFunction<I, initUserByKugouIdV2_args> {
      public initUserByKugouIdV2() {
        super("initUserByKugouIdV2");
      }

      public initUserByKugouIdV2_args getEmptyArgsInstance() {
        return new initUserByKugouIdV2_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public initUserByKugouIdV2_result getResult(I iface, initUserByKugouIdV2_args args) throws org.apache.thrift.TException {
        initUserByKugouIdV2_result result = new initUserByKugouIdV2_result();
        result.success = iface.initUserByKugouIdV2(args.kugouId);
        return result;
      }
    }

    public static class getUserByUserName<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUserByUserName_args> {
      public getUserByUserName() {
        super("getUserByUserName");
      }

      public getUserByUserName_args getEmptyArgsInstance() {
        return new getUserByUserName_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUserByUserName_result getResult(I iface, getUserByUserName_args args) throws org.apache.thrift.TException {
        getUserByUserName_result result = new getUserByUserName_result();
        result.success = iface.getUserByUserName(args.userName);
        return result;
      }
    }

    public static class getUsersByUserNames<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUsersByUserNames_args> {
      public getUsersByUserNames() {
        super("getUsersByUserNames");
      }

      public getUsersByUserNames_args getEmptyArgsInstance() {
        return new getUsersByUserNames_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUsersByUserNames_result getResult(I iface, getUsersByUserNames_args args) throws org.apache.thrift.TException {
        getUsersByUserNames_result result = new getUsersByUserNames_result();
        result.success = iface.getUsersByUserNames(args.userNames);
        return result;
      }
    }

    public static class getUserByNickName<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUserByNickName_args> {
      public getUserByNickName() {
        super("getUserByNickName");
      }

      public getUserByNickName_args getEmptyArgsInstance() {
        return new getUserByNickName_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUserByNickName_result getResult(I iface, getUserByNickName_args args) throws org.apache.thrift.TException {
        getUserByNickName_result result = new getUserByNickName_result();
        result.success = iface.getUserByNickName(args.nickName);
        return result;
      }
    }

    public static class getUsersByNickNames<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUsersByNickNames_args> {
      public getUsersByNickNames() {
        super("getUsersByNickNames");
      }

      public getUsersByNickNames_args getEmptyArgsInstance() {
        return new getUsersByNickNames_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUsersByNickNames_result getResult(I iface, getUsersByNickNames_args args) throws org.apache.thrift.TException {
        getUsersByNickNames_result result = new getUsersByNickNames_result();
        result.success = iface.getUsersByNickNames(args.nickNames);
        return result;
      }
    }

    public static class isExistNickName<I extends Iface> extends org.apache.thrift.ProcessFunction<I, isExistNickName_args> {
      public isExistNickName() {
        super("isExistNickName");
      }

      public isExistNickName_args getEmptyArgsInstance() {
        return new isExistNickName_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public isExistNickName_result getResult(I iface, isExistNickName_args args) throws org.apache.thrift.TException {
        isExistNickName_result result = new isExistNickName_result();
        result.success = iface.isExistNickName(args.nickName);
        return result;
      }
    }

    public static class getUserVOsByKugouIdsDynamic<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUserVOsByKugouIdsDynamic_args> {
      public getUserVOsByKugouIdsDynamic() {
        super("getUserVOsByKugouIdsDynamic");
      }

      public getUserVOsByKugouIdsDynamic_args getEmptyArgsInstance() {
        return new getUserVOsByKugouIdsDynamic_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUserVOsByKugouIdsDynamic_result getResult(I iface, getUserVOsByKugouIdsDynamic_args args) throws org.apache.thrift.TException {
        getUserVOsByKugouIdsDynamic_result result = new getUserVOsByKugouIdsDynamic_result();
        result.success = iface.getUserVOsByKugouIdsDynamic(args.kugouIds, args.dynamicResponseUserVO);
        return result;
      }
    }

  }

  public static class AsyncProcessor<I extends AsyncIface> extends org.apache.thrift.TBaseAsyncProcessor<I> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());
    public AsyncProcessor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.AsyncProcessFunction<I, ? extends org.apache.thrift.TBase, ?>>()));
    }

    protected AsyncProcessor(I iface, Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends AsyncIface> Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase,?>> getProcessMap(Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      processMap.put("saveUser", new saveUser());
      processMap.put("update", new update());
      processMap.put("getUsersByKugouIds", new getUsersByKugouIds());
      processMap.put("getUserByKugouId", new getUserByKugouId());
      processMap.put("getSingleUserByKugouId", new getSingleUserByKugouId());
      processMap.put("initUserByKugouId", new initUserByKugouId());
      processMap.put("initUserByKugouIdV2", new initUserByKugouIdV2());
      processMap.put("getUserByUserName", new getUserByUserName());
      processMap.put("getUsersByUserNames", new getUsersByUserNames());
      processMap.put("getUserByNickName", new getUserByNickName());
      processMap.put("getUsersByNickNames", new getUsersByNickNames());
      processMap.put("isExistNickName", new isExistNickName());
      processMap.put("getUserVOsByKugouIdsDynamic", new getUserVOsByKugouIdsDynamic());
      return processMap;
    }

    public static class saveUser<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, saveUser_args, com.kugou.fanxing.thrift.plat.user.vo.ResSave> {
      public saveUser() {
        super("saveUser");
      }

      public saveUser_args getEmptyArgsInstance() {
        return new saveUser_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResSave> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResSave>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResSave o) {
            saveUser_result result = new saveUser_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            saveUser_result result = new saveUser_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, saveUser_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResSave> resultHandler) throws TException {
        iface.saveUser(args.userVO,resultHandler);
      }
    }

    public static class update<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, update_args, com.kugou.fanxing.thrift.plat.user.vo.ResSave> {
      public update() {
        super("update");
      }

      public update_args getEmptyArgsInstance() {
        return new update_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResSave> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResSave>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResSave o) {
            update_result result = new update_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            update_result result = new update_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, update_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResSave> resultHandler) throws TException {
        iface.update(args.userVO, args.token,resultHandler);
      }
    }

    public static class getUsersByKugouIds<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUsersByKugouIds_args, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> {
      public getUsersByKugouIds() {
        super("getUsersByKugouIds");
      }

      public getUsersByKugouIds_args getEmptyArgsInstance() {
        return new getUsersByKugouIds_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser o) {
            getUsersByKugouIds_result result = new getUsersByKugouIds_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUsersByKugouIds_result result = new getUsersByKugouIds_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUsersByKugouIds_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> resultHandler) throws TException {
        iface.getUsersByKugouIds(args.kugouIds,resultHandler);
      }
    }

    public static class getUserByKugouId<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUserByKugouId_args, com.kugou.fanxing.thrift.plat.user.vo.ResUser> {
      public getUserByKugouId() {
        super("getUserByKugouId");
      }

      public getUserByKugouId_args getEmptyArgsInstance() {
        return new getUserByKugouId_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUser> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUser>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResUser o) {
            getUserByKugouId_result result = new getUserByKugouId_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUserByKugouId_result result = new getUserByKugouId_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUserByKugouId_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUser> resultHandler) throws TException {
        iface.getUserByKugouId(args.kugouId,resultHandler);
      }
    }

    public static class getSingleUserByKugouId<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getSingleUserByKugouId_args, com.kugou.fanxing.thrift.plat.user.vo.ResUser> {
      public getSingleUserByKugouId() {
        super("getSingleUserByKugouId");
      }

      public getSingleUserByKugouId_args getEmptyArgsInstance() {
        return new getSingleUserByKugouId_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUser> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUser>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResUser o) {
            getSingleUserByKugouId_result result = new getSingleUserByKugouId_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getSingleUserByKugouId_result result = new getSingleUserByKugouId_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getSingleUserByKugouId_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUser> resultHandler) throws TException {
        iface.getSingleUserByKugouId(args.kugouId,resultHandler);
      }
    }

    public static class initUserByKugouId<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, initUserByKugouId_args, com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg> {
      public initUserByKugouId() {
        super("initUserByKugouId");
      }

      public initUserByKugouId_args getEmptyArgsInstance() {
        return new initUserByKugouId_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg o) {
            initUserByKugouId_result result = new initUserByKugouId_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            initUserByKugouId_result result = new initUserByKugouId_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, initUserByKugouId_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg> resultHandler) throws TException {
        iface.initUserByKugouId(args.kugouId,resultHandler);
      }
    }

    public static class initUserByKugouIdV2<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, initUserByKugouIdV2_args, com.kugou.fanxing.thrift.plat.user.vo.ResUser> {
      public initUserByKugouIdV2() {
        super("initUserByKugouIdV2");
      }

      public initUserByKugouIdV2_args getEmptyArgsInstance() {
        return new initUserByKugouIdV2_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUser> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUser>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResUser o) {
            initUserByKugouIdV2_result result = new initUserByKugouIdV2_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            initUserByKugouIdV2_result result = new initUserByKugouIdV2_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, initUserByKugouIdV2_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUser> resultHandler) throws TException {
        iface.initUserByKugouIdV2(args.kugouId,resultHandler);
      }
    }

    public static class getUserByUserName<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUserByUserName_args, com.kugou.fanxing.thrift.plat.user.vo.ResUser> {
      public getUserByUserName() {
        super("getUserByUserName");
      }

      public getUserByUserName_args getEmptyArgsInstance() {
        return new getUserByUserName_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUser> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUser>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResUser o) {
            getUserByUserName_result result = new getUserByUserName_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUserByUserName_result result = new getUserByUserName_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUserByUserName_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUser> resultHandler) throws TException {
        iface.getUserByUserName(args.userName,resultHandler);
      }
    }

    public static class getUsersByUserNames<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUsersByUserNames_args, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> {
      public getUsersByUserNames() {
        super("getUsersByUserNames");
      }

      public getUsersByUserNames_args getEmptyArgsInstance() {
        return new getUsersByUserNames_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser o) {
            getUsersByUserNames_result result = new getUsersByUserNames_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUsersByUserNames_result result = new getUsersByUserNames_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUsersByUserNames_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> resultHandler) throws TException {
        iface.getUsersByUserNames(args.userNames,resultHandler);
      }
    }

    public static class getUserByNickName<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUserByNickName_args, com.kugou.fanxing.thrift.plat.user.vo.ResUser> {
      public getUserByNickName() {
        super("getUserByNickName");
      }

      public getUserByNickName_args getEmptyArgsInstance() {
        return new getUserByNickName_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUser> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUser>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResUser o) {
            getUserByNickName_result result = new getUserByNickName_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUserByNickName_result result = new getUserByNickName_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUserByNickName_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUser> resultHandler) throws TException {
        iface.getUserByNickName(args.nickName,resultHandler);
      }
    }

    public static class getUsersByNickNames<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUsersByNickNames_args, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> {
      public getUsersByNickNames() {
        super("getUsersByNickNames");
      }

      public getUsersByNickNames_args getEmptyArgsInstance() {
        return new getUsersByNickNames_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser o) {
            getUsersByNickNames_result result = new getUsersByNickNames_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUsersByNickNames_result result = new getUsersByNickNames_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUsersByNickNames_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> resultHandler) throws TException {
        iface.getUsersByNickNames(args.nickNames,resultHandler);
      }
    }

    public static class isExistNickName<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, isExistNickName_args, com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg> {
      public isExistNickName() {
        super("isExistNickName");
      }

      public isExistNickName_args getEmptyArgsInstance() {
        return new isExistNickName_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg o) {
            isExistNickName_result result = new isExistNickName_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            isExistNickName_result result = new isExistNickName_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, isExistNickName_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg> resultHandler) throws TException {
        iface.isExistNickName(args.nickName,resultHandler);
      }
    }

    public static class getUserVOsByKugouIdsDynamic<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUserVOsByKugouIdsDynamic_args, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> {
      public getUserVOsByKugouIdsDynamic() {
        super("getUserVOsByKugouIdsDynamic");
      }

      public getUserVOsByKugouIdsDynamic_args getEmptyArgsInstance() {
        return new getUserVOsByKugouIdsDynamic_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser o) {
            getUserVOsByKugouIdsDynamic_result result = new getUserVOsByKugouIdsDynamic_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUserVOsByKugouIdsDynamic_result result = new getUserVOsByKugouIdsDynamic_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUserVOsByKugouIdsDynamic_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> resultHandler) throws TException {
        iface.getUserVOsByKugouIdsDynamic(args.kugouIds, args.dynamicResponseUserVO,resultHandler);
      }
    }

  }

  public static class saveUser_args implements org.apache.thrift.TBase<saveUser_args, saveUser_args._Fields>, java.io.Serializable, Cloneable, Comparable<saveUser_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("saveUser_args");

    private static final org.apache.thrift.protocol.TField USER_VO_FIELD_DESC = new org.apache.thrift.protocol.TField("userVO", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new saveUser_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new saveUser_argsTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      USER_VO((short)1, "userVO");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // USER_VO
            return USER_VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.USER_VO, new org.apache.thrift.meta_data.FieldMetaData("userVO", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.UserVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(saveUser_args.class, metaDataMap);
    }

    public saveUser_args() {
    }

    public saveUser_args(
      com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO)
    {
      this();
      this.userVO = userVO;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public saveUser_args(saveUser_args other) {
      if (other.isSetUserVO()) {
        this.userVO = new com.kugou.fanxing.thrift.plat.user.vo.UserVO(other.userVO);
      }
    }

    public saveUser_args deepCopy() {
      return new saveUser_args(this);
    }

    @Override
    public void clear() {
      this.userVO = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.UserVO getUserVO() {
      return this.userVO;
    }

    public saveUser_args setUserVO(com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO) {
      this.userVO = userVO;
      return this;
    }

    public void unsetUserVO() {
      this.userVO = null;
    }

    /** Returns true if field userVO is set (has been assigned a value) and false otherwise */
    public boolean isSetUserVO() {
      return this.userVO != null;
    }

    public void setUserVOIsSet(boolean value) {
      if (!value) {
        this.userVO = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case USER_VO:
        if (value == null) {
          unsetUserVO();
        } else {
          setUserVO((com.kugou.fanxing.thrift.plat.user.vo.UserVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case USER_VO:
        return getUserVO();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case USER_VO:
        return isSetUserVO();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof saveUser_args)
        return this.equals((saveUser_args)that);
      return false;
    }

    public boolean equals(saveUser_args that) {
      if (that == null)
        return false;

      boolean this_present_userVO = true && this.isSetUserVO();
      boolean that_present_userVO = true && that.isSetUserVO();
      if (this_present_userVO || that_present_userVO) {
        if (!(this_present_userVO && that_present_userVO))
          return false;
        if (!this.userVO.equals(that.userVO))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_userVO = true && (isSetUserVO());
      list.add(present_userVO);
      if (present_userVO)
        list.add(userVO);

      return list.hashCode();
    }

    @Override
    public int compareTo(saveUser_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetUserVO()).compareTo(other.isSetUserVO());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetUserVO()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.userVO, other.userVO);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("saveUser_args(");
      boolean first = true;

      sb.append("userVO:");
      if (this.userVO == null) {
        sb.append("null");
      } else {
        sb.append(this.userVO);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (userVO != null) {
        userVO.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class saveUser_argsStandardSchemeFactory implements SchemeFactory {
      public saveUser_argsStandardScheme getScheme() {
        return new saveUser_argsStandardScheme();
      }
    }

    private static class saveUser_argsStandardScheme extends StandardScheme<saveUser_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, saveUser_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // USER_VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.userVO = new com.kugou.fanxing.thrift.plat.user.vo.UserVO();
                struct.userVO.read(iprot);
                struct.setUserVOIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, saveUser_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.userVO != null) {
          oprot.writeFieldBegin(USER_VO_FIELD_DESC);
          struct.userVO.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class saveUser_argsTupleSchemeFactory implements SchemeFactory {
      public saveUser_argsTupleScheme getScheme() {
        return new saveUser_argsTupleScheme();
      }
    }

    private static class saveUser_argsTupleScheme extends TupleScheme<saveUser_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, saveUser_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetUserVO()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetUserVO()) {
          struct.userVO.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, saveUser_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.userVO = new com.kugou.fanxing.thrift.plat.user.vo.UserVO();
          struct.userVO.read(iprot);
          struct.setUserVOIsSet(true);
        }
      }
    }

  }

  public static class saveUser_result implements org.apache.thrift.TBase<saveUser_result, saveUser_result._Fields>, java.io.Serializable, Cloneable, Comparable<saveUser_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("saveUser_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new saveUser_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new saveUser_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResSave success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResSave.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(saveUser_result.class, metaDataMap);
    }

    public saveUser_result() {
    }

    public saveUser_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResSave success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public saveUser_result(saveUser_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResSave(other.success);
      }
    }

    public saveUser_result deepCopy() {
      return new saveUser_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResSave getSuccess() {
      return this.success;
    }

    public saveUser_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResSave success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResSave)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof saveUser_result)
        return this.equals((saveUser_result)that);
      return false;
    }

    public boolean equals(saveUser_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(saveUser_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("saveUser_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class saveUser_resultStandardSchemeFactory implements SchemeFactory {
      public saveUser_resultStandardScheme getScheme() {
        return new saveUser_resultStandardScheme();
      }
    }

    private static class saveUser_resultStandardScheme extends StandardScheme<saveUser_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, saveUser_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResSave();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, saveUser_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class saveUser_resultTupleSchemeFactory implements SchemeFactory {
      public saveUser_resultTupleScheme getScheme() {
        return new saveUser_resultTupleScheme();
      }
    }

    private static class saveUser_resultTupleScheme extends TupleScheme<saveUser_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, saveUser_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, saveUser_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResSave();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class update_args implements org.apache.thrift.TBase<update_args, update_args._Fields>, java.io.Serializable, Cloneable, Comparable<update_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("update_args");

    private static final org.apache.thrift.protocol.TField USER_VO_FIELD_DESC = new org.apache.thrift.protocol.TField("userVO", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField TOKEN_FIELD_DESC = new org.apache.thrift.protocol.TField("token", org.apache.thrift.protocol.TType.STRING, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new update_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new update_argsTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO; // required
    public String token; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      USER_VO((short)1, "userVO"),
      TOKEN((short)2, "token");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // USER_VO
            return USER_VO;
          case 2: // TOKEN
            return TOKEN;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.USER_VO, new org.apache.thrift.meta_data.FieldMetaData("userVO", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.UserVO.class)));
      tmpMap.put(_Fields.TOKEN, new org.apache.thrift.meta_data.FieldMetaData("token", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(update_args.class, metaDataMap);
    }

    public update_args() {
    }

    public update_args(
      com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO,
      String token)
    {
      this();
      this.userVO = userVO;
      this.token = token;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public update_args(update_args other) {
      if (other.isSetUserVO()) {
        this.userVO = new com.kugou.fanxing.thrift.plat.user.vo.UserVO(other.userVO);
      }
      if (other.isSetToken()) {
        this.token = other.token;
      }
    }

    public update_args deepCopy() {
      return new update_args(this);
    }

    @Override
    public void clear() {
      this.userVO = null;
      this.token = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.UserVO getUserVO() {
      return this.userVO;
    }

    public update_args setUserVO(com.kugou.fanxing.thrift.plat.user.vo.UserVO userVO) {
      this.userVO = userVO;
      return this;
    }

    public void unsetUserVO() {
      this.userVO = null;
    }

    /** Returns true if field userVO is set (has been assigned a value) and false otherwise */
    public boolean isSetUserVO() {
      return this.userVO != null;
    }

    public void setUserVOIsSet(boolean value) {
      if (!value) {
        this.userVO = null;
      }
    }

    public String getToken() {
      return this.token;
    }

    public update_args setToken(String token) {
      this.token = token;
      return this;
    }

    public void unsetToken() {
      this.token = null;
    }

    /** Returns true if field token is set (has been assigned a value) and false otherwise */
    public boolean isSetToken() {
      return this.token != null;
    }

    public void setTokenIsSet(boolean value) {
      if (!value) {
        this.token = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case USER_VO:
        if (value == null) {
          unsetUserVO();
        } else {
          setUserVO((com.kugou.fanxing.thrift.plat.user.vo.UserVO)value);
        }
        break;

      case TOKEN:
        if (value == null) {
          unsetToken();
        } else {
          setToken((String)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case USER_VO:
        return getUserVO();

      case TOKEN:
        return getToken();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case USER_VO:
        return isSetUserVO();
      case TOKEN:
        return isSetToken();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof update_args)
        return this.equals((update_args)that);
      return false;
    }

    public boolean equals(update_args that) {
      if (that == null)
        return false;

      boolean this_present_userVO = true && this.isSetUserVO();
      boolean that_present_userVO = true && that.isSetUserVO();
      if (this_present_userVO || that_present_userVO) {
        if (!(this_present_userVO && that_present_userVO))
          return false;
        if (!this.userVO.equals(that.userVO))
          return false;
      }

      boolean this_present_token = true && this.isSetToken();
      boolean that_present_token = true && that.isSetToken();
      if (this_present_token || that_present_token) {
        if (!(this_present_token && that_present_token))
          return false;
        if (!this.token.equals(that.token))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_userVO = true && (isSetUserVO());
      list.add(present_userVO);
      if (present_userVO)
        list.add(userVO);

      boolean present_token = true && (isSetToken());
      list.add(present_token);
      if (present_token)
        list.add(token);

      return list.hashCode();
    }

    @Override
    public int compareTo(update_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetUserVO()).compareTo(other.isSetUserVO());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetUserVO()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.userVO, other.userVO);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetToken()).compareTo(other.isSetToken());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetToken()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.token, other.token);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("update_args(");
      boolean first = true;

      sb.append("userVO:");
      if (this.userVO == null) {
        sb.append("null");
      } else {
        sb.append(this.userVO);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("token:");
      if (this.token == null) {
        sb.append("null");
      } else {
        sb.append(this.token);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (userVO != null) {
        userVO.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class update_argsStandardSchemeFactory implements SchemeFactory {
      public update_argsStandardScheme getScheme() {
        return new update_argsStandardScheme();
      }
    }

    private static class update_argsStandardScheme extends StandardScheme<update_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, update_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // USER_VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.userVO = new com.kugou.fanxing.thrift.plat.user.vo.UserVO();
                struct.userVO.read(iprot);
                struct.setUserVOIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // TOKEN
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.token = iprot.readString();
                struct.setTokenIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, update_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.userVO != null) {
          oprot.writeFieldBegin(USER_VO_FIELD_DESC);
          struct.userVO.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.token != null) {
          oprot.writeFieldBegin(TOKEN_FIELD_DESC);
          oprot.writeString(struct.token);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class update_argsTupleSchemeFactory implements SchemeFactory {
      public update_argsTupleScheme getScheme() {
        return new update_argsTupleScheme();
      }
    }

    private static class update_argsTupleScheme extends TupleScheme<update_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, update_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetUserVO()) {
          optionals.set(0);
        }
        if (struct.isSetToken()) {
          optionals.set(1);
        }
        oprot.writeBitSet(optionals, 2);
        if (struct.isSetUserVO()) {
          struct.userVO.write(oprot);
        }
        if (struct.isSetToken()) {
          oprot.writeString(struct.token);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, update_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(2);
        if (incoming.get(0)) {
          struct.userVO = new com.kugou.fanxing.thrift.plat.user.vo.UserVO();
          struct.userVO.read(iprot);
          struct.setUserVOIsSet(true);
        }
        if (incoming.get(1)) {
          struct.token = iprot.readString();
          struct.setTokenIsSet(true);
        }
      }
    }

  }

  public static class update_result implements org.apache.thrift.TBase<update_result, update_result._Fields>, java.io.Serializable, Cloneable, Comparable<update_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("update_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new update_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new update_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResSave success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResSave.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(update_result.class, metaDataMap);
    }

    public update_result() {
    }

    public update_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResSave success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public update_result(update_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResSave(other.success);
      }
    }

    public update_result deepCopy() {
      return new update_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResSave getSuccess() {
      return this.success;
    }

    public update_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResSave success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResSave)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof update_result)
        return this.equals((update_result)that);
      return false;
    }

    public boolean equals(update_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(update_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("update_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class update_resultStandardSchemeFactory implements SchemeFactory {
      public update_resultStandardScheme getScheme() {
        return new update_resultStandardScheme();
      }
    }

    private static class update_resultStandardScheme extends StandardScheme<update_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, update_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResSave();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, update_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class update_resultTupleSchemeFactory implements SchemeFactory {
      public update_resultTupleScheme getScheme() {
        return new update_resultTupleScheme();
      }
    }

    private static class update_resultTupleScheme extends TupleScheme<update_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, update_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, update_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResSave();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUsersByKugouIds_args implements org.apache.thrift.TBase<getUsersByKugouIds_args, getUsersByKugouIds_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersByKugouIds_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersByKugouIds_args");

    private static final org.apache.thrift.protocol.TField KUGOU_IDS_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouIds", org.apache.thrift.protocol.TType.LIST, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersByKugouIds_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersByKugouIds_argsTupleSchemeFactory());
    }

    public List<Long> kugouIds; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_IDS((short)1, "kugouIds");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_IDS
            return KUGOU_IDS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_IDS, new org.apache.thrift.meta_data.FieldMetaData("kugouIds", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
              new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersByKugouIds_args.class, metaDataMap);
    }

    public getUsersByKugouIds_args() {
    }

    public getUsersByKugouIds_args(
      List<Long> kugouIds)
    {
      this();
      this.kugouIds = kugouIds;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersByKugouIds_args(getUsersByKugouIds_args other) {
      if (other.isSetKugouIds()) {
        List<Long> __this__kugouIds = new ArrayList<Long>(other.kugouIds);
        this.kugouIds = __this__kugouIds;
      }
    }

    public getUsersByKugouIds_args deepCopy() {
      return new getUsersByKugouIds_args(this);
    }

    @Override
    public void clear() {
      this.kugouIds = null;
    }

    public int getKugouIdsSize() {
      return (this.kugouIds == null) ? 0 : this.kugouIds.size();
    }

    public java.util.Iterator<Long> getKugouIdsIterator() {
      return (this.kugouIds == null) ? null : this.kugouIds.iterator();
    }

    public void addToKugouIds(long elem) {
      if (this.kugouIds == null) {
        this.kugouIds = new ArrayList<Long>();
      }
      this.kugouIds.add(elem);
    }

    public List<Long> getKugouIds() {
      return this.kugouIds;
    }

    public getUsersByKugouIds_args setKugouIds(List<Long> kugouIds) {
      this.kugouIds = kugouIds;
      return this;
    }

    public void unsetKugouIds() {
      this.kugouIds = null;
    }

    /** Returns true if field kugouIds is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouIds() {
      return this.kugouIds != null;
    }

    public void setKugouIdsIsSet(boolean value) {
      if (!value) {
        this.kugouIds = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_IDS:
        if (value == null) {
          unsetKugouIds();
        } else {
          setKugouIds((List<Long>)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_IDS:
        return getKugouIds();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_IDS:
        return isSetKugouIds();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersByKugouIds_args)
        return this.equals((getUsersByKugouIds_args)that);
      return false;
    }

    public boolean equals(getUsersByKugouIds_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouIds = true && this.isSetKugouIds();
      boolean that_present_kugouIds = true && that.isSetKugouIds();
      if (this_present_kugouIds || that_present_kugouIds) {
        if (!(this_present_kugouIds && that_present_kugouIds))
          return false;
        if (!this.kugouIds.equals(that.kugouIds))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouIds = true && (isSetKugouIds());
      list.add(present_kugouIds);
      if (present_kugouIds)
        list.add(kugouIds);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersByKugouIds_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouIds()).compareTo(other.isSetKugouIds());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouIds()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouIds, other.kugouIds);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersByKugouIds_args(");
      boolean first = true;

      sb.append("kugouIds:");
      if (this.kugouIds == null) {
        sb.append("null");
      } else {
        sb.append(this.kugouIds);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersByKugouIds_argsStandardSchemeFactory implements SchemeFactory {
      public getUsersByKugouIds_argsStandardScheme getScheme() {
        return new getUsersByKugouIds_argsStandardScheme();
      }
    }

    private static class getUsersByKugouIds_argsStandardScheme extends StandardScheme<getUsersByKugouIds_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersByKugouIds_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_IDS
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                  struct.kugouIds = new ArrayList<Long>(_list0.size);
                  long _elem1;
                  for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                  {
                    _elem1 = iprot.readI64();
                    struct.kugouIds.add(_elem1);
                  }
                  iprot.readListEnd();
                }
                struct.setKugouIdsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersByKugouIds_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.kugouIds != null) {
          oprot.writeFieldBegin(KUGOU_IDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.kugouIds.size()));
            for (long _iter3 : struct.kugouIds)
            {
              oprot.writeI64(_iter3);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersByKugouIds_argsTupleSchemeFactory implements SchemeFactory {
      public getUsersByKugouIds_argsTupleScheme getScheme() {
        return new getUsersByKugouIds_argsTupleScheme();
      }
    }

    private static class getUsersByKugouIds_argsTupleScheme extends TupleScheme<getUsersByKugouIds_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersByKugouIds_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouIds()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetKugouIds()) {
          {
            oprot.writeI32(struct.kugouIds.size());
            for (long _iter4 : struct.kugouIds)
            {
              oprot.writeI64(_iter4);
            }
          }
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersByKugouIds_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          {
            org.apache.thrift.protocol.TList _list5 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
            struct.kugouIds = new ArrayList<Long>(_list5.size);
            long _elem6;
            for (int _i7 = 0; _i7 < _list5.size; ++_i7)
            {
              _elem6 = iprot.readI64();
              struct.kugouIds.add(_elem6);
            }
          }
          struct.setKugouIdsIsSet(true);
        }
      }
    }

  }

  public static class getUsersByKugouIds_result implements org.apache.thrift.TBase<getUsersByKugouIds_result, getUsersByKugouIds_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersByKugouIds_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersByKugouIds_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersByKugouIds_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersByKugouIds_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersByKugouIds_result.class, metaDataMap);
    }

    public getUsersByKugouIds_result() {
    }

    public getUsersByKugouIds_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersByKugouIds_result(getUsersByKugouIds_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser(other.success);
      }
    }

    public getUsersByKugouIds_result deepCopy() {
      return new getUsersByKugouIds_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getSuccess() {
      return this.success;
    }

    public getUsersByKugouIds_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersByKugouIds_result)
        return this.equals((getUsersByKugouIds_result)that);
      return false;
    }

    public boolean equals(getUsersByKugouIds_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersByKugouIds_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersByKugouIds_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersByKugouIds_resultStandardSchemeFactory implements SchemeFactory {
      public getUsersByKugouIds_resultStandardScheme getScheme() {
        return new getUsersByKugouIds_resultStandardScheme();
      }
    }

    private static class getUsersByKugouIds_resultStandardScheme extends StandardScheme<getUsersByKugouIds_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersByKugouIds_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersByKugouIds_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersByKugouIds_resultTupleSchemeFactory implements SchemeFactory {
      public getUsersByKugouIds_resultTupleScheme getScheme() {
        return new getUsersByKugouIds_resultTupleScheme();
      }
    }

    private static class getUsersByKugouIds_resultTupleScheme extends TupleScheme<getUsersByKugouIds_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersByKugouIds_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersByKugouIds_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUserByKugouId_args implements org.apache.thrift.TBase<getUserByKugouId_args, getUserByKugouId_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUserByKugouId_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserByKugouId_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserByKugouId_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserByKugouId_argsTupleSchemeFactory());
    }

    public long kugouId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserByKugouId_args.class, metaDataMap);
    }

    public getUserByKugouId_args() {
    }

    public getUserByKugouId_args(
      long kugouId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserByKugouId_args(getUserByKugouId_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
    }

    public getUserByKugouId_args deepCopy() {
      return new getUserByKugouId_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public getUserByKugouId_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserByKugouId_args)
        return this.equals((getUserByKugouId_args)that);
      return false;
    }

    public boolean equals(getUserByKugouId_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserByKugouId_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserByKugouId_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserByKugouId_argsStandardSchemeFactory implements SchemeFactory {
      public getUserByKugouId_argsStandardScheme getScheme() {
        return new getUserByKugouId_argsStandardScheme();
      }
    }

    private static class getUserByKugouId_argsStandardScheme extends StandardScheme<getUserByKugouId_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserByKugouId_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserByKugouId_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserByKugouId_argsTupleSchemeFactory implements SchemeFactory {
      public getUserByKugouId_argsTupleScheme getScheme() {
        return new getUserByKugouId_argsTupleScheme();
      }
    }

    private static class getUserByKugouId_argsTupleScheme extends TupleScheme<getUserByKugouId_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserByKugouId_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouId()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetKugouId()) {
          oprot.writeI64(struct.kugouId);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserByKugouId_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.kugouId = iprot.readI64();
          struct.setKugouIdIsSet(true);
        }
      }
    }

  }

  public static class getUserByKugouId_result implements org.apache.thrift.TBase<getUserByKugouId_result, getUserByKugouId_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUserByKugouId_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserByKugouId_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserByKugouId_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserByKugouId_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResUser.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserByKugouId_result.class, metaDataMap);
    }

    public getUserByKugouId_result() {
    }

    public getUserByKugouId_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResUser success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserByKugouId_result(getUserByKugouId_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUser(other.success);
      }
    }

    public getUserByKugouId_result deepCopy() {
      return new getUserByKugouId_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser getSuccess() {
      return this.success;
    }

    public getUserByKugouId_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResUser success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResUser)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserByKugouId_result)
        return this.equals((getUserByKugouId_result)that);
      return false;
    }

    public boolean equals(getUserByKugouId_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserByKugouId_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserByKugouId_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserByKugouId_resultStandardSchemeFactory implements SchemeFactory {
      public getUserByKugouId_resultStandardScheme getScheme() {
        return new getUserByKugouId_resultStandardScheme();
      }
    }

    private static class getUserByKugouId_resultStandardScheme extends StandardScheme<getUserByKugouId_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserByKugouId_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUser();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserByKugouId_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserByKugouId_resultTupleSchemeFactory implements SchemeFactory {
      public getUserByKugouId_resultTupleScheme getScheme() {
        return new getUserByKugouId_resultTupleScheme();
      }
    }

    private static class getUserByKugouId_resultTupleScheme extends TupleScheme<getUserByKugouId_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserByKugouId_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserByKugouId_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUser();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getSingleUserByKugouId_args implements org.apache.thrift.TBase<getSingleUserByKugouId_args, getSingleUserByKugouId_args._Fields>, java.io.Serializable, Cloneable, Comparable<getSingleUserByKugouId_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getSingleUserByKugouId_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getSingleUserByKugouId_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getSingleUserByKugouId_argsTupleSchemeFactory());
    }

    public long kugouId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getSingleUserByKugouId_args.class, metaDataMap);
    }

    public getSingleUserByKugouId_args() {
    }

    public getSingleUserByKugouId_args(
      long kugouId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getSingleUserByKugouId_args(getSingleUserByKugouId_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
    }

    public getSingleUserByKugouId_args deepCopy() {
      return new getSingleUserByKugouId_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public getSingleUserByKugouId_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getSingleUserByKugouId_args)
        return this.equals((getSingleUserByKugouId_args)that);
      return false;
    }

    public boolean equals(getSingleUserByKugouId_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      return list.hashCode();
    }

    @Override
    public int compareTo(getSingleUserByKugouId_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getSingleUserByKugouId_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getSingleUserByKugouId_argsStandardSchemeFactory implements SchemeFactory {
      public getSingleUserByKugouId_argsStandardScheme getScheme() {
        return new getSingleUserByKugouId_argsStandardScheme();
      }
    }

    private static class getSingleUserByKugouId_argsStandardScheme extends StandardScheme<getSingleUserByKugouId_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getSingleUserByKugouId_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getSingleUserByKugouId_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getSingleUserByKugouId_argsTupleSchemeFactory implements SchemeFactory {
      public getSingleUserByKugouId_argsTupleScheme getScheme() {
        return new getSingleUserByKugouId_argsTupleScheme();
      }
    }

    private static class getSingleUserByKugouId_argsTupleScheme extends TupleScheme<getSingleUserByKugouId_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getSingleUserByKugouId_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouId()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetKugouId()) {
          oprot.writeI64(struct.kugouId);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getSingleUserByKugouId_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.kugouId = iprot.readI64();
          struct.setKugouIdIsSet(true);
        }
      }
    }

  }

  public static class getSingleUserByKugouId_result implements org.apache.thrift.TBase<getSingleUserByKugouId_result, getSingleUserByKugouId_result._Fields>, java.io.Serializable, Cloneable, Comparable<getSingleUserByKugouId_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getSingleUserByKugouId_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getSingleUserByKugouId_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getSingleUserByKugouId_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResUser.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getSingleUserByKugouId_result.class, metaDataMap);
    }

    public getSingleUserByKugouId_result() {
    }

    public getSingleUserByKugouId_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResUser success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getSingleUserByKugouId_result(getSingleUserByKugouId_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUser(other.success);
      }
    }

    public getSingleUserByKugouId_result deepCopy() {
      return new getSingleUserByKugouId_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser getSuccess() {
      return this.success;
    }

    public getSingleUserByKugouId_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResUser success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResUser)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getSingleUserByKugouId_result)
        return this.equals((getSingleUserByKugouId_result)that);
      return false;
    }

    public boolean equals(getSingleUserByKugouId_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getSingleUserByKugouId_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getSingleUserByKugouId_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getSingleUserByKugouId_resultStandardSchemeFactory implements SchemeFactory {
      public getSingleUserByKugouId_resultStandardScheme getScheme() {
        return new getSingleUserByKugouId_resultStandardScheme();
      }
    }

    private static class getSingleUserByKugouId_resultStandardScheme extends StandardScheme<getSingleUserByKugouId_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getSingleUserByKugouId_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUser();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getSingleUserByKugouId_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getSingleUserByKugouId_resultTupleSchemeFactory implements SchemeFactory {
      public getSingleUserByKugouId_resultTupleScheme getScheme() {
        return new getSingleUserByKugouId_resultTupleScheme();
      }
    }

    private static class getSingleUserByKugouId_resultTupleScheme extends TupleScheme<getSingleUserByKugouId_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getSingleUserByKugouId_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getSingleUserByKugouId_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUser();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class initUserByKugouId_args implements org.apache.thrift.TBase<initUserByKugouId_args, initUserByKugouId_args._Fields>, java.io.Serializable, Cloneable, Comparable<initUserByKugouId_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("initUserByKugouId_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new initUserByKugouId_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new initUserByKugouId_argsTupleSchemeFactory());
    }

    public long kugouId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(initUserByKugouId_args.class, metaDataMap);
    }

    public initUserByKugouId_args() {
    }

    public initUserByKugouId_args(
      long kugouId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public initUserByKugouId_args(initUserByKugouId_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
    }

    public initUserByKugouId_args deepCopy() {
      return new initUserByKugouId_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public initUserByKugouId_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof initUserByKugouId_args)
        return this.equals((initUserByKugouId_args)that);
      return false;
    }

    public boolean equals(initUserByKugouId_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      return list.hashCode();
    }

    @Override
    public int compareTo(initUserByKugouId_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("initUserByKugouId_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class initUserByKugouId_argsStandardSchemeFactory implements SchemeFactory {
      public initUserByKugouId_argsStandardScheme getScheme() {
        return new initUserByKugouId_argsStandardScheme();
      }
    }

    private static class initUserByKugouId_argsStandardScheme extends StandardScheme<initUserByKugouId_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, initUserByKugouId_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, initUserByKugouId_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class initUserByKugouId_argsTupleSchemeFactory implements SchemeFactory {
      public initUserByKugouId_argsTupleScheme getScheme() {
        return new initUserByKugouId_argsTupleScheme();
      }
    }

    private static class initUserByKugouId_argsTupleScheme extends TupleScheme<initUserByKugouId_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, initUserByKugouId_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouId()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetKugouId()) {
          oprot.writeI64(struct.kugouId);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, initUserByKugouId_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.kugouId = iprot.readI64();
          struct.setKugouIdIsSet(true);
        }
      }
    }

  }

  public static class initUserByKugouId_result implements org.apache.thrift.TBase<initUserByKugouId_result, initUserByKugouId_result._Fields>, java.io.Serializable, Cloneable, Comparable<initUserByKugouId_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("initUserByKugouId_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new initUserByKugouId_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new initUserByKugouId_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(initUserByKugouId_result.class, metaDataMap);
    }

    public initUserByKugouId_result() {
    }

    public initUserByKugouId_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public initUserByKugouId_result(initUserByKugouId_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg(other.success);
      }
    }

    public initUserByKugouId_result deepCopy() {
      return new initUserByKugouId_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg getSuccess() {
      return this.success;
    }

    public initUserByKugouId_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof initUserByKugouId_result)
        return this.equals((initUserByKugouId_result)that);
      return false;
    }

    public boolean equals(initUserByKugouId_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(initUserByKugouId_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("initUserByKugouId_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class initUserByKugouId_resultStandardSchemeFactory implements SchemeFactory {
      public initUserByKugouId_resultStandardScheme getScheme() {
        return new initUserByKugouId_resultStandardScheme();
      }
    }

    private static class initUserByKugouId_resultStandardScheme extends StandardScheme<initUserByKugouId_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, initUserByKugouId_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, initUserByKugouId_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class initUserByKugouId_resultTupleSchemeFactory implements SchemeFactory {
      public initUserByKugouId_resultTupleScheme getScheme() {
        return new initUserByKugouId_resultTupleScheme();
      }
    }

    private static class initUserByKugouId_resultTupleScheme extends TupleScheme<initUserByKugouId_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, initUserByKugouId_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, initUserByKugouId_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class initUserByKugouIdV2_args implements org.apache.thrift.TBase<initUserByKugouIdV2_args, initUserByKugouIdV2_args._Fields>, java.io.Serializable, Cloneable, Comparable<initUserByKugouIdV2_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("initUserByKugouIdV2_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new initUserByKugouIdV2_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new initUserByKugouIdV2_argsTupleSchemeFactory());
    }

    public long kugouId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(initUserByKugouIdV2_args.class, metaDataMap);
    }

    public initUserByKugouIdV2_args() {
    }

    public initUserByKugouIdV2_args(
      long kugouId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public initUserByKugouIdV2_args(initUserByKugouIdV2_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
    }

    public initUserByKugouIdV2_args deepCopy() {
      return new initUserByKugouIdV2_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public initUserByKugouIdV2_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof initUserByKugouIdV2_args)
        return this.equals((initUserByKugouIdV2_args)that);
      return false;
    }

    public boolean equals(initUserByKugouIdV2_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      return list.hashCode();
    }

    @Override
    public int compareTo(initUserByKugouIdV2_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("initUserByKugouIdV2_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class initUserByKugouIdV2_argsStandardSchemeFactory implements SchemeFactory {
      public initUserByKugouIdV2_argsStandardScheme getScheme() {
        return new initUserByKugouIdV2_argsStandardScheme();
      }
    }

    private static class initUserByKugouIdV2_argsStandardScheme extends StandardScheme<initUserByKugouIdV2_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, initUserByKugouIdV2_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, initUserByKugouIdV2_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class initUserByKugouIdV2_argsTupleSchemeFactory implements SchemeFactory {
      public initUserByKugouIdV2_argsTupleScheme getScheme() {
        return new initUserByKugouIdV2_argsTupleScheme();
      }
    }

    private static class initUserByKugouIdV2_argsTupleScheme extends TupleScheme<initUserByKugouIdV2_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, initUserByKugouIdV2_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouId()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetKugouId()) {
          oprot.writeI64(struct.kugouId);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, initUserByKugouIdV2_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.kugouId = iprot.readI64();
          struct.setKugouIdIsSet(true);
        }
      }
    }

  }

  public static class initUserByKugouIdV2_result implements org.apache.thrift.TBase<initUserByKugouIdV2_result, initUserByKugouIdV2_result._Fields>, java.io.Serializable, Cloneable, Comparable<initUserByKugouIdV2_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("initUserByKugouIdV2_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new initUserByKugouIdV2_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new initUserByKugouIdV2_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResUser.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(initUserByKugouIdV2_result.class, metaDataMap);
    }

    public initUserByKugouIdV2_result() {
    }

    public initUserByKugouIdV2_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResUser success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public initUserByKugouIdV2_result(initUserByKugouIdV2_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUser(other.success);
      }
    }

    public initUserByKugouIdV2_result deepCopy() {
      return new initUserByKugouIdV2_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser getSuccess() {
      return this.success;
    }

    public initUserByKugouIdV2_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResUser success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResUser)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof initUserByKugouIdV2_result)
        return this.equals((initUserByKugouIdV2_result)that);
      return false;
    }

    public boolean equals(initUserByKugouIdV2_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(initUserByKugouIdV2_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("initUserByKugouIdV2_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class initUserByKugouIdV2_resultStandardSchemeFactory implements SchemeFactory {
      public initUserByKugouIdV2_resultStandardScheme getScheme() {
        return new initUserByKugouIdV2_resultStandardScheme();
      }
    }

    private static class initUserByKugouIdV2_resultStandardScheme extends StandardScheme<initUserByKugouIdV2_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, initUserByKugouIdV2_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUser();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, initUserByKugouIdV2_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class initUserByKugouIdV2_resultTupleSchemeFactory implements SchemeFactory {
      public initUserByKugouIdV2_resultTupleScheme getScheme() {
        return new initUserByKugouIdV2_resultTupleScheme();
      }
    }

    private static class initUserByKugouIdV2_resultTupleScheme extends TupleScheme<initUserByKugouIdV2_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, initUserByKugouIdV2_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, initUserByKugouIdV2_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUser();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUserByUserName_args implements org.apache.thrift.TBase<getUserByUserName_args, getUserByUserName_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUserByUserName_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserByUserName_args");

    private static final org.apache.thrift.protocol.TField USER_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("userName", org.apache.thrift.protocol.TType.STRING, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserByUserName_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserByUserName_argsTupleSchemeFactory());
    }

    public String userName; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      USER_NAME((short)1, "userName");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // USER_NAME
            return USER_NAME;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.USER_NAME, new org.apache.thrift.meta_data.FieldMetaData("userName", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserByUserName_args.class, metaDataMap);
    }

    public getUserByUserName_args() {
    }

    public getUserByUserName_args(
      String userName)
    {
      this();
      this.userName = userName;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserByUserName_args(getUserByUserName_args other) {
      if (other.isSetUserName()) {
        this.userName = other.userName;
      }
    }

    public getUserByUserName_args deepCopy() {
      return new getUserByUserName_args(this);
    }

    @Override
    public void clear() {
      this.userName = null;
    }

    public String getUserName() {
      return this.userName;
    }

    public getUserByUserName_args setUserName(String userName) {
      this.userName = userName;
      return this;
    }

    public void unsetUserName() {
      this.userName = null;
    }

    /** Returns true if field userName is set (has been assigned a value) and false otherwise */
    public boolean isSetUserName() {
      return this.userName != null;
    }

    public void setUserNameIsSet(boolean value) {
      if (!value) {
        this.userName = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case USER_NAME:
        if (value == null) {
          unsetUserName();
        } else {
          setUserName((String)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case USER_NAME:
        return getUserName();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case USER_NAME:
        return isSetUserName();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserByUserName_args)
        return this.equals((getUserByUserName_args)that);
      return false;
    }

    public boolean equals(getUserByUserName_args that) {
      if (that == null)
        return false;

      boolean this_present_userName = true && this.isSetUserName();
      boolean that_present_userName = true && that.isSetUserName();
      if (this_present_userName || that_present_userName) {
        if (!(this_present_userName && that_present_userName))
          return false;
        if (!this.userName.equals(that.userName))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_userName = true && (isSetUserName());
      list.add(present_userName);
      if (present_userName)
        list.add(userName);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserByUserName_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetUserName()).compareTo(other.isSetUserName());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetUserName()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.userName, other.userName);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserByUserName_args(");
      boolean first = true;

      sb.append("userName:");
      if (this.userName == null) {
        sb.append("null");
      } else {
        sb.append(this.userName);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserByUserName_argsStandardSchemeFactory implements SchemeFactory {
      public getUserByUserName_argsStandardScheme getScheme() {
        return new getUserByUserName_argsStandardScheme();
      }
    }

    private static class getUserByUserName_argsStandardScheme extends StandardScheme<getUserByUserName_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserByUserName_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // USER_NAME
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.userName = iprot.readString();
                struct.setUserNameIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserByUserName_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.userName != null) {
          oprot.writeFieldBegin(USER_NAME_FIELD_DESC);
          oprot.writeString(struct.userName);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserByUserName_argsTupleSchemeFactory implements SchemeFactory {
      public getUserByUserName_argsTupleScheme getScheme() {
        return new getUserByUserName_argsTupleScheme();
      }
    }

    private static class getUserByUserName_argsTupleScheme extends TupleScheme<getUserByUserName_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserByUserName_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetUserName()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetUserName()) {
          oprot.writeString(struct.userName);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserByUserName_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.userName = iprot.readString();
          struct.setUserNameIsSet(true);
        }
      }
    }

  }

  public static class getUserByUserName_result implements org.apache.thrift.TBase<getUserByUserName_result, getUserByUserName_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUserByUserName_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserByUserName_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserByUserName_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserByUserName_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResUser.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserByUserName_result.class, metaDataMap);
    }

    public getUserByUserName_result() {
    }

    public getUserByUserName_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResUser success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserByUserName_result(getUserByUserName_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUser(other.success);
      }
    }

    public getUserByUserName_result deepCopy() {
      return new getUserByUserName_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser getSuccess() {
      return this.success;
    }

    public getUserByUserName_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResUser success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResUser)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserByUserName_result)
        return this.equals((getUserByUserName_result)that);
      return false;
    }

    public boolean equals(getUserByUserName_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserByUserName_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserByUserName_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserByUserName_resultStandardSchemeFactory implements SchemeFactory {
      public getUserByUserName_resultStandardScheme getScheme() {
        return new getUserByUserName_resultStandardScheme();
      }
    }

    private static class getUserByUserName_resultStandardScheme extends StandardScheme<getUserByUserName_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserByUserName_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUser();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserByUserName_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserByUserName_resultTupleSchemeFactory implements SchemeFactory {
      public getUserByUserName_resultTupleScheme getScheme() {
        return new getUserByUserName_resultTupleScheme();
      }
    }

    private static class getUserByUserName_resultTupleScheme extends TupleScheme<getUserByUserName_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserByUserName_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserByUserName_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUser();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUsersByUserNames_args implements org.apache.thrift.TBase<getUsersByUserNames_args, getUsersByUserNames_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersByUserNames_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersByUserNames_args");

    private static final org.apache.thrift.protocol.TField USER_NAMES_FIELD_DESC = new org.apache.thrift.protocol.TField("userNames", org.apache.thrift.protocol.TType.LIST, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersByUserNames_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersByUserNames_argsTupleSchemeFactory());
    }

    public List<String> userNames; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      USER_NAMES((short)1, "userNames");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // USER_NAMES
            return USER_NAMES;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.USER_NAMES, new org.apache.thrift.meta_data.FieldMetaData("userNames", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
              new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersByUserNames_args.class, metaDataMap);
    }

    public getUsersByUserNames_args() {
    }

    public getUsersByUserNames_args(
      List<String> userNames)
    {
      this();
      this.userNames = userNames;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersByUserNames_args(getUsersByUserNames_args other) {
      if (other.isSetUserNames()) {
        List<String> __this__userNames = new ArrayList<String>(other.userNames);
        this.userNames = __this__userNames;
      }
    }

    public getUsersByUserNames_args deepCopy() {
      return new getUsersByUserNames_args(this);
    }

    @Override
    public void clear() {
      this.userNames = null;
    }

    public int getUserNamesSize() {
      return (this.userNames == null) ? 0 : this.userNames.size();
    }

    public java.util.Iterator<String> getUserNamesIterator() {
      return (this.userNames == null) ? null : this.userNames.iterator();
    }

    public void addToUserNames(String elem) {
      if (this.userNames == null) {
        this.userNames = new ArrayList<String>();
      }
      this.userNames.add(elem);
    }

    public List<String> getUserNames() {
      return this.userNames;
    }

    public getUsersByUserNames_args setUserNames(List<String> userNames) {
      this.userNames = userNames;
      return this;
    }

    public void unsetUserNames() {
      this.userNames = null;
    }

    /** Returns true if field userNames is set (has been assigned a value) and false otherwise */
    public boolean isSetUserNames() {
      return this.userNames != null;
    }

    public void setUserNamesIsSet(boolean value) {
      if (!value) {
        this.userNames = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case USER_NAMES:
        if (value == null) {
          unsetUserNames();
        } else {
          setUserNames((List<String>)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case USER_NAMES:
        return getUserNames();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case USER_NAMES:
        return isSetUserNames();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersByUserNames_args)
        return this.equals((getUsersByUserNames_args)that);
      return false;
    }

    public boolean equals(getUsersByUserNames_args that) {
      if (that == null)
        return false;

      boolean this_present_userNames = true && this.isSetUserNames();
      boolean that_present_userNames = true && that.isSetUserNames();
      if (this_present_userNames || that_present_userNames) {
        if (!(this_present_userNames && that_present_userNames))
          return false;
        if (!this.userNames.equals(that.userNames))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_userNames = true && (isSetUserNames());
      list.add(present_userNames);
      if (present_userNames)
        list.add(userNames);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersByUserNames_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetUserNames()).compareTo(other.isSetUserNames());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetUserNames()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.userNames, other.userNames);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersByUserNames_args(");
      boolean first = true;

      sb.append("userNames:");
      if (this.userNames == null) {
        sb.append("null");
      } else {
        sb.append(this.userNames);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersByUserNames_argsStandardSchemeFactory implements SchemeFactory {
      public getUsersByUserNames_argsStandardScheme getScheme() {
        return new getUsersByUserNames_argsStandardScheme();
      }
    }

    private static class getUsersByUserNames_argsStandardScheme extends StandardScheme<getUsersByUserNames_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersByUserNames_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // USER_NAMES
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list8 = iprot.readListBegin();
                  struct.userNames = new ArrayList<String>(_list8.size);
                  String _elem9;
                  for (int _i10 = 0; _i10 < _list8.size; ++_i10)
                  {
                    _elem9 = iprot.readString();
                    struct.userNames.add(_elem9);
                  }
                  iprot.readListEnd();
                }
                struct.setUserNamesIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersByUserNames_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.userNames != null) {
          oprot.writeFieldBegin(USER_NAMES_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.userNames.size()));
            for (String _iter11 : struct.userNames)
            {
              oprot.writeString(_iter11);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersByUserNames_argsTupleSchemeFactory implements SchemeFactory {
      public getUsersByUserNames_argsTupleScheme getScheme() {
        return new getUsersByUserNames_argsTupleScheme();
      }
    }

    private static class getUsersByUserNames_argsTupleScheme extends TupleScheme<getUsersByUserNames_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersByUserNames_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetUserNames()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetUserNames()) {
          {
            oprot.writeI32(struct.userNames.size());
            for (String _iter12 : struct.userNames)
            {
              oprot.writeString(_iter12);
            }
          }
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersByUserNames_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          {
            org.apache.thrift.protocol.TList _list13 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
            struct.userNames = new ArrayList<String>(_list13.size);
            String _elem14;
            for (int _i15 = 0; _i15 < _list13.size; ++_i15)
            {
              _elem14 = iprot.readString();
              struct.userNames.add(_elem14);
            }
          }
          struct.setUserNamesIsSet(true);
        }
      }
    }

  }

  public static class getUsersByUserNames_result implements org.apache.thrift.TBase<getUsersByUserNames_result, getUsersByUserNames_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersByUserNames_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersByUserNames_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersByUserNames_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersByUserNames_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersByUserNames_result.class, metaDataMap);
    }

    public getUsersByUserNames_result() {
    }

    public getUsersByUserNames_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersByUserNames_result(getUsersByUserNames_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser(other.success);
      }
    }

    public getUsersByUserNames_result deepCopy() {
      return new getUsersByUserNames_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getSuccess() {
      return this.success;
    }

    public getUsersByUserNames_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersByUserNames_result)
        return this.equals((getUsersByUserNames_result)that);
      return false;
    }

    public boolean equals(getUsersByUserNames_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersByUserNames_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersByUserNames_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersByUserNames_resultStandardSchemeFactory implements SchemeFactory {
      public getUsersByUserNames_resultStandardScheme getScheme() {
        return new getUsersByUserNames_resultStandardScheme();
      }
    }

    private static class getUsersByUserNames_resultStandardScheme extends StandardScheme<getUsersByUserNames_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersByUserNames_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersByUserNames_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersByUserNames_resultTupleSchemeFactory implements SchemeFactory {
      public getUsersByUserNames_resultTupleScheme getScheme() {
        return new getUsersByUserNames_resultTupleScheme();
      }
    }

    private static class getUsersByUserNames_resultTupleScheme extends TupleScheme<getUsersByUserNames_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersByUserNames_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersByUserNames_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUserByNickName_args implements org.apache.thrift.TBase<getUserByNickName_args, getUserByNickName_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUserByNickName_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserByNickName_args");

    private static final org.apache.thrift.protocol.TField NICK_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("nickName", org.apache.thrift.protocol.TType.STRING, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserByNickName_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserByNickName_argsTupleSchemeFactory());
    }

    public String nickName; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      NICK_NAME((short)1, "nickName");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // NICK_NAME
            return NICK_NAME;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.NICK_NAME, new org.apache.thrift.meta_data.FieldMetaData("nickName", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserByNickName_args.class, metaDataMap);
    }

    public getUserByNickName_args() {
    }

    public getUserByNickName_args(
      String nickName)
    {
      this();
      this.nickName = nickName;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserByNickName_args(getUserByNickName_args other) {
      if (other.isSetNickName()) {
        this.nickName = other.nickName;
      }
    }

    public getUserByNickName_args deepCopy() {
      return new getUserByNickName_args(this);
    }

    @Override
    public void clear() {
      this.nickName = null;
    }

    public String getNickName() {
      return this.nickName;
    }

    public getUserByNickName_args setNickName(String nickName) {
      this.nickName = nickName;
      return this;
    }

    public void unsetNickName() {
      this.nickName = null;
    }

    /** Returns true if field nickName is set (has been assigned a value) and false otherwise */
    public boolean isSetNickName() {
      return this.nickName != null;
    }

    public void setNickNameIsSet(boolean value) {
      if (!value) {
        this.nickName = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case NICK_NAME:
        if (value == null) {
          unsetNickName();
        } else {
          setNickName((String)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case NICK_NAME:
        return getNickName();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case NICK_NAME:
        return isSetNickName();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserByNickName_args)
        return this.equals((getUserByNickName_args)that);
      return false;
    }

    public boolean equals(getUserByNickName_args that) {
      if (that == null)
        return false;

      boolean this_present_nickName = true && this.isSetNickName();
      boolean that_present_nickName = true && that.isSetNickName();
      if (this_present_nickName || that_present_nickName) {
        if (!(this_present_nickName && that_present_nickName))
          return false;
        if (!this.nickName.equals(that.nickName))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_nickName = true && (isSetNickName());
      list.add(present_nickName);
      if (present_nickName)
        list.add(nickName);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserByNickName_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetNickName()).compareTo(other.isSetNickName());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetNickName()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.nickName, other.nickName);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserByNickName_args(");
      boolean first = true;

      sb.append("nickName:");
      if (this.nickName == null) {
        sb.append("null");
      } else {
        sb.append(this.nickName);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserByNickName_argsStandardSchemeFactory implements SchemeFactory {
      public getUserByNickName_argsStandardScheme getScheme() {
        return new getUserByNickName_argsStandardScheme();
      }
    }

    private static class getUserByNickName_argsStandardScheme extends StandardScheme<getUserByNickName_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserByNickName_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // NICK_NAME
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.nickName = iprot.readString();
                struct.setNickNameIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserByNickName_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.nickName != null) {
          oprot.writeFieldBegin(NICK_NAME_FIELD_DESC);
          oprot.writeString(struct.nickName);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserByNickName_argsTupleSchemeFactory implements SchemeFactory {
      public getUserByNickName_argsTupleScheme getScheme() {
        return new getUserByNickName_argsTupleScheme();
      }
    }

    private static class getUserByNickName_argsTupleScheme extends TupleScheme<getUserByNickName_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserByNickName_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetNickName()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetNickName()) {
          oprot.writeString(struct.nickName);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserByNickName_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.nickName = iprot.readString();
          struct.setNickNameIsSet(true);
        }
      }
    }

  }

  public static class getUserByNickName_result implements org.apache.thrift.TBase<getUserByNickName_result, getUserByNickName_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUserByNickName_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserByNickName_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserByNickName_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserByNickName_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResUser.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserByNickName_result.class, metaDataMap);
    }

    public getUserByNickName_result() {
    }

    public getUserByNickName_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResUser success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserByNickName_result(getUserByNickName_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUser(other.success);
      }
    }

    public getUserByNickName_result deepCopy() {
      return new getUserByNickName_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUser getSuccess() {
      return this.success;
    }

    public getUserByNickName_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResUser success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResUser)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserByNickName_result)
        return this.equals((getUserByNickName_result)that);
      return false;
    }

    public boolean equals(getUserByNickName_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserByNickName_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserByNickName_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserByNickName_resultStandardSchemeFactory implements SchemeFactory {
      public getUserByNickName_resultStandardScheme getScheme() {
        return new getUserByNickName_resultStandardScheme();
      }
    }

    private static class getUserByNickName_resultStandardScheme extends StandardScheme<getUserByNickName_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserByNickName_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUser();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserByNickName_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserByNickName_resultTupleSchemeFactory implements SchemeFactory {
      public getUserByNickName_resultTupleScheme getScheme() {
        return new getUserByNickName_resultTupleScheme();
      }
    }

    private static class getUserByNickName_resultTupleScheme extends TupleScheme<getUserByNickName_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserByNickName_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserByNickName_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUser();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUsersByNickNames_args implements org.apache.thrift.TBase<getUsersByNickNames_args, getUsersByNickNames_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersByNickNames_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersByNickNames_args");

    private static final org.apache.thrift.protocol.TField NICK_NAMES_FIELD_DESC = new org.apache.thrift.protocol.TField("nickNames", org.apache.thrift.protocol.TType.LIST, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersByNickNames_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersByNickNames_argsTupleSchemeFactory());
    }

    public List<String> nickNames; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      NICK_NAMES((short)1, "nickNames");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // NICK_NAMES
            return NICK_NAMES;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.NICK_NAMES, new org.apache.thrift.meta_data.FieldMetaData("nickNames", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
              new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersByNickNames_args.class, metaDataMap);
    }

    public getUsersByNickNames_args() {
    }

    public getUsersByNickNames_args(
      List<String> nickNames)
    {
      this();
      this.nickNames = nickNames;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersByNickNames_args(getUsersByNickNames_args other) {
      if (other.isSetNickNames()) {
        List<String> __this__nickNames = new ArrayList<String>(other.nickNames);
        this.nickNames = __this__nickNames;
      }
    }

    public getUsersByNickNames_args deepCopy() {
      return new getUsersByNickNames_args(this);
    }

    @Override
    public void clear() {
      this.nickNames = null;
    }

    public int getNickNamesSize() {
      return (this.nickNames == null) ? 0 : this.nickNames.size();
    }

    public java.util.Iterator<String> getNickNamesIterator() {
      return (this.nickNames == null) ? null : this.nickNames.iterator();
    }

    public void addToNickNames(String elem) {
      if (this.nickNames == null) {
        this.nickNames = new ArrayList<String>();
      }
      this.nickNames.add(elem);
    }

    public List<String> getNickNames() {
      return this.nickNames;
    }

    public getUsersByNickNames_args setNickNames(List<String> nickNames) {
      this.nickNames = nickNames;
      return this;
    }

    public void unsetNickNames() {
      this.nickNames = null;
    }

    /** Returns true if field nickNames is set (has been assigned a value) and false otherwise */
    public boolean isSetNickNames() {
      return this.nickNames != null;
    }

    public void setNickNamesIsSet(boolean value) {
      if (!value) {
        this.nickNames = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case NICK_NAMES:
        if (value == null) {
          unsetNickNames();
        } else {
          setNickNames((List<String>)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case NICK_NAMES:
        return getNickNames();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case NICK_NAMES:
        return isSetNickNames();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersByNickNames_args)
        return this.equals((getUsersByNickNames_args)that);
      return false;
    }

    public boolean equals(getUsersByNickNames_args that) {
      if (that == null)
        return false;

      boolean this_present_nickNames = true && this.isSetNickNames();
      boolean that_present_nickNames = true && that.isSetNickNames();
      if (this_present_nickNames || that_present_nickNames) {
        if (!(this_present_nickNames && that_present_nickNames))
          return false;
        if (!this.nickNames.equals(that.nickNames))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_nickNames = true && (isSetNickNames());
      list.add(present_nickNames);
      if (present_nickNames)
        list.add(nickNames);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersByNickNames_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetNickNames()).compareTo(other.isSetNickNames());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetNickNames()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.nickNames, other.nickNames);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersByNickNames_args(");
      boolean first = true;

      sb.append("nickNames:");
      if (this.nickNames == null) {
        sb.append("null");
      } else {
        sb.append(this.nickNames);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersByNickNames_argsStandardSchemeFactory implements SchemeFactory {
      public getUsersByNickNames_argsStandardScheme getScheme() {
        return new getUsersByNickNames_argsStandardScheme();
      }
    }

    private static class getUsersByNickNames_argsStandardScheme extends StandardScheme<getUsersByNickNames_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersByNickNames_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // NICK_NAMES
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list16 = iprot.readListBegin();
                  struct.nickNames = new ArrayList<String>(_list16.size);
                  String _elem17;
                  for (int _i18 = 0; _i18 < _list16.size; ++_i18)
                  {
                    _elem17 = iprot.readString();
                    struct.nickNames.add(_elem17);
                  }
                  iprot.readListEnd();
                }
                struct.setNickNamesIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersByNickNames_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.nickNames != null) {
          oprot.writeFieldBegin(NICK_NAMES_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.nickNames.size()));
            for (String _iter19 : struct.nickNames)
            {
              oprot.writeString(_iter19);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersByNickNames_argsTupleSchemeFactory implements SchemeFactory {
      public getUsersByNickNames_argsTupleScheme getScheme() {
        return new getUsersByNickNames_argsTupleScheme();
      }
    }

    private static class getUsersByNickNames_argsTupleScheme extends TupleScheme<getUsersByNickNames_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersByNickNames_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetNickNames()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetNickNames()) {
          {
            oprot.writeI32(struct.nickNames.size());
            for (String _iter20 : struct.nickNames)
            {
              oprot.writeString(_iter20);
            }
          }
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersByNickNames_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          {
            org.apache.thrift.protocol.TList _list21 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
            struct.nickNames = new ArrayList<String>(_list21.size);
            String _elem22;
            for (int _i23 = 0; _i23 < _list21.size; ++_i23)
            {
              _elem22 = iprot.readString();
              struct.nickNames.add(_elem22);
            }
          }
          struct.setNickNamesIsSet(true);
        }
      }
    }

  }

  public static class getUsersByNickNames_result implements org.apache.thrift.TBase<getUsersByNickNames_result, getUsersByNickNames_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersByNickNames_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersByNickNames_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersByNickNames_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersByNickNames_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersByNickNames_result.class, metaDataMap);
    }

    public getUsersByNickNames_result() {
    }

    public getUsersByNickNames_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersByNickNames_result(getUsersByNickNames_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser(other.success);
      }
    }

    public getUsersByNickNames_result deepCopy() {
      return new getUsersByNickNames_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getSuccess() {
      return this.success;
    }

    public getUsersByNickNames_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersByNickNames_result)
        return this.equals((getUsersByNickNames_result)that);
      return false;
    }

    public boolean equals(getUsersByNickNames_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersByNickNames_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersByNickNames_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersByNickNames_resultStandardSchemeFactory implements SchemeFactory {
      public getUsersByNickNames_resultStandardScheme getScheme() {
        return new getUsersByNickNames_resultStandardScheme();
      }
    }

    private static class getUsersByNickNames_resultStandardScheme extends StandardScheme<getUsersByNickNames_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersByNickNames_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersByNickNames_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersByNickNames_resultTupleSchemeFactory implements SchemeFactory {
      public getUsersByNickNames_resultTupleScheme getScheme() {
        return new getUsersByNickNames_resultTupleScheme();
      }
    }

    private static class getUsersByNickNames_resultTupleScheme extends TupleScheme<getUsersByNickNames_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersByNickNames_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersByNickNames_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class isExistNickName_args implements org.apache.thrift.TBase<isExistNickName_args, isExistNickName_args._Fields>, java.io.Serializable, Cloneable, Comparable<isExistNickName_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isExistNickName_args");

    private static final org.apache.thrift.protocol.TField NICK_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("nickName", org.apache.thrift.protocol.TType.STRING, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isExistNickName_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isExistNickName_argsTupleSchemeFactory());
    }

    public String nickName; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      NICK_NAME((short)1, "nickName");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // NICK_NAME
            return NICK_NAME;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.NICK_NAME, new org.apache.thrift.meta_data.FieldMetaData("nickName", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isExistNickName_args.class, metaDataMap);
    }

    public isExistNickName_args() {
    }

    public isExistNickName_args(
      String nickName)
    {
      this();
      this.nickName = nickName;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isExistNickName_args(isExistNickName_args other) {
      if (other.isSetNickName()) {
        this.nickName = other.nickName;
      }
    }

    public isExistNickName_args deepCopy() {
      return new isExistNickName_args(this);
    }

    @Override
    public void clear() {
      this.nickName = null;
    }

    public String getNickName() {
      return this.nickName;
    }

    public isExistNickName_args setNickName(String nickName) {
      this.nickName = nickName;
      return this;
    }

    public void unsetNickName() {
      this.nickName = null;
    }

    /** Returns true if field nickName is set (has been assigned a value) and false otherwise */
    public boolean isSetNickName() {
      return this.nickName != null;
    }

    public void setNickNameIsSet(boolean value) {
      if (!value) {
        this.nickName = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case NICK_NAME:
        if (value == null) {
          unsetNickName();
        } else {
          setNickName((String)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case NICK_NAME:
        return getNickName();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case NICK_NAME:
        return isSetNickName();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isExistNickName_args)
        return this.equals((isExistNickName_args)that);
      return false;
    }

    public boolean equals(isExistNickName_args that) {
      if (that == null)
        return false;

      boolean this_present_nickName = true && this.isSetNickName();
      boolean that_present_nickName = true && that.isSetNickName();
      if (this_present_nickName || that_present_nickName) {
        if (!(this_present_nickName && that_present_nickName))
          return false;
        if (!this.nickName.equals(that.nickName))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_nickName = true && (isSetNickName());
      list.add(present_nickName);
      if (present_nickName)
        list.add(nickName);

      return list.hashCode();
    }

    @Override
    public int compareTo(isExistNickName_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetNickName()).compareTo(other.isSetNickName());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetNickName()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.nickName, other.nickName);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isExistNickName_args(");
      boolean first = true;

      sb.append("nickName:");
      if (this.nickName == null) {
        sb.append("null");
      } else {
        sb.append(this.nickName);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isExistNickName_argsStandardSchemeFactory implements SchemeFactory {
      public isExistNickName_argsStandardScheme getScheme() {
        return new isExistNickName_argsStandardScheme();
      }
    }

    private static class isExistNickName_argsStandardScheme extends StandardScheme<isExistNickName_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isExistNickName_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // NICK_NAME
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.nickName = iprot.readString();
                struct.setNickNameIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isExistNickName_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.nickName != null) {
          oprot.writeFieldBegin(NICK_NAME_FIELD_DESC);
          oprot.writeString(struct.nickName);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isExistNickName_argsTupleSchemeFactory implements SchemeFactory {
      public isExistNickName_argsTupleScheme getScheme() {
        return new isExistNickName_argsTupleScheme();
      }
    }

    private static class isExistNickName_argsTupleScheme extends TupleScheme<isExistNickName_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isExistNickName_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetNickName()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetNickName()) {
          oprot.writeString(struct.nickName);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isExistNickName_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.nickName = iprot.readString();
          struct.setNickNameIsSet(true);
        }
      }
    }

  }

  public static class isExistNickName_result implements org.apache.thrift.TBase<isExistNickName_result, isExistNickName_result._Fields>, java.io.Serializable, Cloneable, Comparable<isExistNickName_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isExistNickName_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isExistNickName_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isExistNickName_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isExistNickName_result.class, metaDataMap);
    }

    public isExistNickName_result() {
    }

    public isExistNickName_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isExistNickName_result(isExistNickName_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg(other.success);
      }
    }

    public isExistNickName_result deepCopy() {
      return new isExistNickName_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg getSuccess() {
      return this.success;
    }

    public isExistNickName_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isExistNickName_result)
        return this.equals((isExistNickName_result)that);
      return false;
    }

    public boolean equals(isExistNickName_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(isExistNickName_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isExistNickName_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isExistNickName_resultStandardSchemeFactory implements SchemeFactory {
      public isExistNickName_resultStandardScheme getScheme() {
        return new isExistNickName_resultStandardScheme();
      }
    }

    private static class isExistNickName_resultStandardScheme extends StandardScheme<isExistNickName_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isExistNickName_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isExistNickName_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isExistNickName_resultTupleSchemeFactory implements SchemeFactory {
      public isExistNickName_resultTupleScheme getScheme() {
        return new isExistNickName_resultTupleScheme();
      }
    }

    private static class isExistNickName_resultTupleScheme extends TupleScheme<isExistNickName_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isExistNickName_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isExistNickName_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResIntegerMsg();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUserVOsByKugouIdsDynamic_args implements org.apache.thrift.TBase<getUserVOsByKugouIdsDynamic_args, getUserVOsByKugouIdsDynamic_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUserVOsByKugouIdsDynamic_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserVOsByKugouIdsDynamic_args");

    private static final org.apache.thrift.protocol.TField KUGOU_IDS_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouIds", org.apache.thrift.protocol.TType.LIST, (short)1);
    private static final org.apache.thrift.protocol.TField DYNAMIC_RESPONSE_USER_VO_FIELD_DESC = new org.apache.thrift.protocol.TField("dynamicResponseUserVO", org.apache.thrift.protocol.TType.STRUCT, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserVOsByKugouIdsDynamic_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserVOsByKugouIdsDynamic_argsTupleSchemeFactory());
    }

    public List<Long> kugouIds; // required
    public com.kugou.fanxing.thrift.plat.user.vo.DynamicResponseUserVO dynamicResponseUserVO; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_IDS((short)1, "kugouIds"),
      DYNAMIC_RESPONSE_USER_VO((short)2, "dynamicResponseUserVO");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_IDS
            return KUGOU_IDS;
          case 2: // DYNAMIC_RESPONSE_USER_VO
            return DYNAMIC_RESPONSE_USER_VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_IDS, new org.apache.thrift.meta_data.FieldMetaData("kugouIds", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
              new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
      tmpMap.put(_Fields.DYNAMIC_RESPONSE_USER_VO, new org.apache.thrift.meta_data.FieldMetaData("dynamicResponseUserVO", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.DynamicResponseUserVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserVOsByKugouIdsDynamic_args.class, metaDataMap);
    }

    public getUserVOsByKugouIdsDynamic_args() {
    }

    public getUserVOsByKugouIdsDynamic_args(
      List<Long> kugouIds,
      com.kugou.fanxing.thrift.plat.user.vo.DynamicResponseUserVO dynamicResponseUserVO)
    {
      this();
      this.kugouIds = kugouIds;
      this.dynamicResponseUserVO = dynamicResponseUserVO;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserVOsByKugouIdsDynamic_args(getUserVOsByKugouIdsDynamic_args other) {
      if (other.isSetKugouIds()) {
        List<Long> __this__kugouIds = new ArrayList<Long>(other.kugouIds);
        this.kugouIds = __this__kugouIds;
      }
      if (other.isSetDynamicResponseUserVO()) {
        this.dynamicResponseUserVO = new com.kugou.fanxing.thrift.plat.user.vo.DynamicResponseUserVO(other.dynamicResponseUserVO);
      }
    }

    public getUserVOsByKugouIdsDynamic_args deepCopy() {
      return new getUserVOsByKugouIdsDynamic_args(this);
    }

    @Override
    public void clear() {
      this.kugouIds = null;
      this.dynamicResponseUserVO = null;
    }

    public int getKugouIdsSize() {
      return (this.kugouIds == null) ? 0 : this.kugouIds.size();
    }

    public java.util.Iterator<Long> getKugouIdsIterator() {
      return (this.kugouIds == null) ? null : this.kugouIds.iterator();
    }

    public void addToKugouIds(long elem) {
      if (this.kugouIds == null) {
        this.kugouIds = new ArrayList<Long>();
      }
      this.kugouIds.add(elem);
    }

    public List<Long> getKugouIds() {
      return this.kugouIds;
    }

    public getUserVOsByKugouIdsDynamic_args setKugouIds(List<Long> kugouIds) {
      this.kugouIds = kugouIds;
      return this;
    }

    public void unsetKugouIds() {
      this.kugouIds = null;
    }

    /** Returns true if field kugouIds is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouIds() {
      return this.kugouIds != null;
    }

    public void setKugouIdsIsSet(boolean value) {
      if (!value) {
        this.kugouIds = null;
      }
    }

    public com.kugou.fanxing.thrift.plat.user.vo.DynamicResponseUserVO getDynamicResponseUserVO() {
      return this.dynamicResponseUserVO;
    }

    public getUserVOsByKugouIdsDynamic_args setDynamicResponseUserVO(com.kugou.fanxing.thrift.plat.user.vo.DynamicResponseUserVO dynamicResponseUserVO) {
      this.dynamicResponseUserVO = dynamicResponseUserVO;
      return this;
    }

    public void unsetDynamicResponseUserVO() {
      this.dynamicResponseUserVO = null;
    }

    /** Returns true if field dynamicResponseUserVO is set (has been assigned a value) and false otherwise */
    public boolean isSetDynamicResponseUserVO() {
      return this.dynamicResponseUserVO != null;
    }

    public void setDynamicResponseUserVOIsSet(boolean value) {
      if (!value) {
        this.dynamicResponseUserVO = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_IDS:
        if (value == null) {
          unsetKugouIds();
        } else {
          setKugouIds((List<Long>)value);
        }
        break;

      case DYNAMIC_RESPONSE_USER_VO:
        if (value == null) {
          unsetDynamicResponseUserVO();
        } else {
          setDynamicResponseUserVO((com.kugou.fanxing.thrift.plat.user.vo.DynamicResponseUserVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_IDS:
        return getKugouIds();

      case DYNAMIC_RESPONSE_USER_VO:
        return getDynamicResponseUserVO();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_IDS:
        return isSetKugouIds();
      case DYNAMIC_RESPONSE_USER_VO:
        return isSetDynamicResponseUserVO();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserVOsByKugouIdsDynamic_args)
        return this.equals((getUserVOsByKugouIdsDynamic_args)that);
      return false;
    }

    public boolean equals(getUserVOsByKugouIdsDynamic_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouIds = true && this.isSetKugouIds();
      boolean that_present_kugouIds = true && that.isSetKugouIds();
      if (this_present_kugouIds || that_present_kugouIds) {
        if (!(this_present_kugouIds && that_present_kugouIds))
          return false;
        if (!this.kugouIds.equals(that.kugouIds))
          return false;
      }

      boolean this_present_dynamicResponseUserVO = true && this.isSetDynamicResponseUserVO();
      boolean that_present_dynamicResponseUserVO = true && that.isSetDynamicResponseUserVO();
      if (this_present_dynamicResponseUserVO || that_present_dynamicResponseUserVO) {
        if (!(this_present_dynamicResponseUserVO && that_present_dynamicResponseUserVO))
          return false;
        if (!this.dynamicResponseUserVO.equals(that.dynamicResponseUserVO))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouIds = true && (isSetKugouIds());
      list.add(present_kugouIds);
      if (present_kugouIds)
        list.add(kugouIds);

      boolean present_dynamicResponseUserVO = true && (isSetDynamicResponseUserVO());
      list.add(present_dynamicResponseUserVO);
      if (present_dynamicResponseUserVO)
        list.add(dynamicResponseUserVO);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserVOsByKugouIdsDynamic_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouIds()).compareTo(other.isSetKugouIds());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouIds()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouIds, other.kugouIds);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetDynamicResponseUserVO()).compareTo(other.isSetDynamicResponseUserVO());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetDynamicResponseUserVO()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.dynamicResponseUserVO, other.dynamicResponseUserVO);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserVOsByKugouIdsDynamic_args(");
      boolean first = true;

      sb.append("kugouIds:");
      if (this.kugouIds == null) {
        sb.append("null");
      } else {
        sb.append(this.kugouIds);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("dynamicResponseUserVO:");
      if (this.dynamicResponseUserVO == null) {
        sb.append("null");
      } else {
        sb.append(this.dynamicResponseUserVO);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (dynamicResponseUserVO != null) {
        dynamicResponseUserVO.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserVOsByKugouIdsDynamic_argsStandardSchemeFactory implements SchemeFactory {
      public getUserVOsByKugouIdsDynamic_argsStandardScheme getScheme() {
        return new getUserVOsByKugouIdsDynamic_argsStandardScheme();
      }
    }

    private static class getUserVOsByKugouIdsDynamic_argsStandardScheme extends StandardScheme<getUserVOsByKugouIdsDynamic_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserVOsByKugouIdsDynamic_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_IDS
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list24 = iprot.readListBegin();
                  struct.kugouIds = new ArrayList<Long>(_list24.size);
                  long _elem25;
                  for (int _i26 = 0; _i26 < _list24.size; ++_i26)
                  {
                    _elem25 = iprot.readI64();
                    struct.kugouIds.add(_elem25);
                  }
                  iprot.readListEnd();
                }
                struct.setKugouIdsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // DYNAMIC_RESPONSE_USER_VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.dynamicResponseUserVO = new com.kugou.fanxing.thrift.plat.user.vo.DynamicResponseUserVO();
                struct.dynamicResponseUserVO.read(iprot);
                struct.setDynamicResponseUserVOIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserVOsByKugouIdsDynamic_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.kugouIds != null) {
          oprot.writeFieldBegin(KUGOU_IDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.kugouIds.size()));
            for (long _iter27 : struct.kugouIds)
            {
              oprot.writeI64(_iter27);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        if (struct.dynamicResponseUserVO != null) {
          oprot.writeFieldBegin(DYNAMIC_RESPONSE_USER_VO_FIELD_DESC);
          struct.dynamicResponseUserVO.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserVOsByKugouIdsDynamic_argsTupleSchemeFactory implements SchemeFactory {
      public getUserVOsByKugouIdsDynamic_argsTupleScheme getScheme() {
        return new getUserVOsByKugouIdsDynamic_argsTupleScheme();
      }
    }

    private static class getUserVOsByKugouIdsDynamic_argsTupleScheme extends TupleScheme<getUserVOsByKugouIdsDynamic_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserVOsByKugouIdsDynamic_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouIds()) {
          optionals.set(0);
        }
        if (struct.isSetDynamicResponseUserVO()) {
          optionals.set(1);
        }
        oprot.writeBitSet(optionals, 2);
        if (struct.isSetKugouIds()) {
          {
            oprot.writeI32(struct.kugouIds.size());
            for (long _iter28 : struct.kugouIds)
            {
              oprot.writeI64(_iter28);
            }
          }
        }
        if (struct.isSetDynamicResponseUserVO()) {
          struct.dynamicResponseUserVO.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserVOsByKugouIdsDynamic_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(2);
        if (incoming.get(0)) {
          {
            org.apache.thrift.protocol.TList _list29 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
            struct.kugouIds = new ArrayList<Long>(_list29.size);
            long _elem30;
            for (int _i31 = 0; _i31 < _list29.size; ++_i31)
            {
              _elem30 = iprot.readI64();
              struct.kugouIds.add(_elem30);
            }
          }
          struct.setKugouIdsIsSet(true);
        }
        if (incoming.get(1)) {
          struct.dynamicResponseUserVO = new com.kugou.fanxing.thrift.plat.user.vo.DynamicResponseUserVO();
          struct.dynamicResponseUserVO.read(iprot);
          struct.setDynamicResponseUserVOIsSet(true);
        }
      }
    }

  }

  public static class getUserVOsByKugouIdsDynamic_result implements org.apache.thrift.TBase<getUserVOsByKugouIdsDynamic_result, getUserVOsByKugouIdsDynamic_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUserVOsByKugouIdsDynamic_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserVOsByKugouIdsDynamic_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserVOsByKugouIdsDynamic_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserVOsByKugouIdsDynamic_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserVOsByKugouIdsDynamic_result.class, metaDataMap);
    }

    public getUserVOsByKugouIdsDynamic_result() {
    }

    public getUserVOsByKugouIdsDynamic_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserVOsByKugouIdsDynamic_result(getUserVOsByKugouIdsDynamic_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser(other.success);
      }
    }

    public getUserVOsByKugouIdsDynamic_result deepCopy() {
      return new getUserVOsByKugouIdsDynamic_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getSuccess() {
      return this.success;
    }

    public getUserVOsByKugouIdsDynamic_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserVOsByKugouIdsDynamic_result)
        return this.equals((getUserVOsByKugouIdsDynamic_result)that);
      return false;
    }

    public boolean equals(getUserVOsByKugouIdsDynamic_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserVOsByKugouIdsDynamic_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserVOsByKugouIdsDynamic_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserVOsByKugouIdsDynamic_resultStandardSchemeFactory implements SchemeFactory {
      public getUserVOsByKugouIdsDynamic_resultStandardScheme getScheme() {
        return new getUserVOsByKugouIdsDynamic_resultStandardScheme();
      }
    }

    private static class getUserVOsByKugouIdsDynamic_resultStandardScheme extends StandardScheme<getUserVOsByKugouIdsDynamic_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserVOsByKugouIdsDynamic_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserVOsByKugouIdsDynamic_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserVOsByKugouIdsDynamic_resultTupleSchemeFactory implements SchemeFactory {
      public getUserVOsByKugouIdsDynamic_resultTupleScheme getScheme() {
        return new getUserVOsByKugouIdsDynamic_resultTupleScheme();
      }
    }

    private static class getUserVOsByKugouIdsDynamic_resultTupleScheme extends TupleScheme<getUserVOsByKugouIdsDynamic_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserVOsByKugouIdsDynamic_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserVOsByKugouIdsDynamic_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

}
