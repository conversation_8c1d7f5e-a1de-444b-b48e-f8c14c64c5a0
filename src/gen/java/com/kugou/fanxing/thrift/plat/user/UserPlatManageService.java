/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.thrift.plat.user;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-03-05")
public class UserPlatManageService {

  /**
   * 用户平台管理服务
   * 
   */
  public interface Iface {

    /**
     * 获取所有用户数量
     *  
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResCount getAllUserCount() throws org.apache.thrift.TException;

    /**
     * 根据产品线获取所有用户数量
     *  
     * 
     * @param productLineId
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResCount getUserCountByProductLine(int productLineId) throws org.apache.thrift.TException;

    /**
     * 根据产品线和状态获取所有用户数量
     *  
     * 
     * @param productLineId
     * @param status
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResCount getUserCountByProductLineAndStatus(int productLineId, int status) throws org.apache.thrift.TException;

    /**
     * 获取不同财富等级下的正常状态的用户数
     *  
     * 
     * @param richLevel
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResCount getUserCountByRichLevel(int richLevel) throws org.apache.thrift.TException;

    /**
     * 根据用户名模糊查询用户信息列表
     * 
     * 
     * @param userNameLike
     * @param pageNum
     * @param pageSize
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersByUserNameLike(String userNameLike, int pageNum, int pageSize) throws org.apache.thrift.TException;

    /**
     * 根据昵称模糊查询用户信息列表 pageNum起始为1 pageSize最大100
     * 
     * 
     * @param nickNameLike
     * @param pageNum
     * @param pageSize
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersByNickNameLike(String nickNameLike, int pageNum, int pageSize) throws org.apache.thrift.TException;

    /**
     * 获取财富等级大于入参值的用户信息列表
     * 
     * 
     * @param richLevel
     * @param pageNum
     * @param pageSize
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersRichLevelGreaterThan(int richLevel, int pageNum, int pageSize) throws org.apache.thrift.TException;

    /**
     * 获取明星等级大于入参值的用户信息列表
     * 
     * 
     * @param starLevel
     * @param pageNum
     * @param pageSize
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersStarLevelGreaterThan(int starLevel, int pageNum, int pageSize) throws org.apache.thrift.TException;

    /**
     * 通过批量酷狗ID和模糊搜索用户昵称获取用户信息,限制100
     * 
     * 
     * @param kugouIds
     * @param nickNameLike
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersByKugouIdsAndNickNameLike(List<Long> kugouIds, String nickNameLike) throws org.apache.thrift.TException;

    /**
     * 通过批量酷狗ID和大于入参明星等级获取kugouId列表,限制100
     * 
     * 
     * @param kugouIds
     * @param starLevel
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg getUsersByKugouIdsAndStarLevelGreaterThan(List<Long> kugouIds, int starLevel) throws org.apache.thrift.TException;

    /**
     * 根据注册时间区间获取用户信息列表
     * 
     * 
     * @param startTime
     * @param endTime
     * @param pageNum
     * @param pageSize
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersByAddTime(int startTime, int endTime, int pageNum, int pageSize) throws org.apache.thrift.TException;

  }

  public interface AsyncIface {

    public void getAllUserCount(org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUserCountByProductLine(int productLineId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUserCountByProductLineAndStatus(int productLineId, int status, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUserCountByRichLevel(int richLevel, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUsersByUserNameLike(String userNameLike, int pageNum, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUsersByNickNameLike(String nickNameLike, int pageNum, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUsersRichLevelGreaterThan(int richLevel, int pageNum, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUsersStarLevelGreaterThan(int starLevel, int pageNum, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUsersByKugouIdsAndNickNameLike(List<Long> kugouIds, String nickNameLike, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUsersByKugouIdsAndStarLevelGreaterThan(List<Long> kugouIds, int starLevel, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUsersByAddTime(int startTime, int endTime, int pageNum, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

  }

  public static class Client extends org.apache.thrift.TServiceClient implements Iface {
    public static class Factory implements org.apache.thrift.TServiceClientFactory<Client> {
      public Factory() {}
      public Client getClient(org.apache.thrift.protocol.TProtocol prot) {
        return new Client(prot);
      }
      public Client getClient(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
        return new Client(iprot, oprot);
      }
    }

    public Client(org.apache.thrift.protocol.TProtocol prot)
    {
      super(prot, prot);
    }

    public Client(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
      super(iprot, oprot);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCount getAllUserCount() throws org.apache.thrift.TException
    {
      send_getAllUserCount();
      return recv_getAllUserCount();
    }

    public void send_getAllUserCount() throws org.apache.thrift.TException
    {
      getAllUserCount_args args = new getAllUserCount_args();
      sendBase("getAllUserCount", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCount recv_getAllUserCount() throws org.apache.thrift.TException
    {
      getAllUserCount_result result = new getAllUserCount_result();
      receiveBase(result, "getAllUserCount");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getAllUserCount failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCount getUserCountByProductLine(int productLineId) throws org.apache.thrift.TException
    {
      send_getUserCountByProductLine(productLineId);
      return recv_getUserCountByProductLine();
    }

    public void send_getUserCountByProductLine(int productLineId) throws org.apache.thrift.TException
    {
      getUserCountByProductLine_args args = new getUserCountByProductLine_args();
      args.setProductLineId(productLineId);
      sendBase("getUserCountByProductLine", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCount recv_getUserCountByProductLine() throws org.apache.thrift.TException
    {
      getUserCountByProductLine_result result = new getUserCountByProductLine_result();
      receiveBase(result, "getUserCountByProductLine");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUserCountByProductLine failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCount getUserCountByProductLineAndStatus(int productLineId, int status) throws org.apache.thrift.TException
    {
      send_getUserCountByProductLineAndStatus(productLineId, status);
      return recv_getUserCountByProductLineAndStatus();
    }

    public void send_getUserCountByProductLineAndStatus(int productLineId, int status) throws org.apache.thrift.TException
    {
      getUserCountByProductLineAndStatus_args args = new getUserCountByProductLineAndStatus_args();
      args.setProductLineId(productLineId);
      args.setStatus(status);
      sendBase("getUserCountByProductLineAndStatus", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCount recv_getUserCountByProductLineAndStatus() throws org.apache.thrift.TException
    {
      getUserCountByProductLineAndStatus_result result = new getUserCountByProductLineAndStatus_result();
      receiveBase(result, "getUserCountByProductLineAndStatus");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUserCountByProductLineAndStatus failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCount getUserCountByRichLevel(int richLevel) throws org.apache.thrift.TException
    {
      send_getUserCountByRichLevel(richLevel);
      return recv_getUserCountByRichLevel();
    }

    public void send_getUserCountByRichLevel(int richLevel) throws org.apache.thrift.TException
    {
      getUserCountByRichLevel_args args = new getUserCountByRichLevel_args();
      args.setRichLevel(richLevel);
      sendBase("getUserCountByRichLevel", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCount recv_getUserCountByRichLevel() throws org.apache.thrift.TException
    {
      getUserCountByRichLevel_result result = new getUserCountByRichLevel_result();
      receiveBase(result, "getUserCountByRichLevel");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUserCountByRichLevel failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersByUserNameLike(String userNameLike, int pageNum, int pageSize) throws org.apache.thrift.TException
    {
      send_getUsersByUserNameLike(userNameLike, pageNum, pageSize);
      return recv_getUsersByUserNameLike();
    }

    public void send_getUsersByUserNameLike(String userNameLike, int pageNum, int pageSize) throws org.apache.thrift.TException
    {
      getUsersByUserNameLike_args args = new getUsersByUserNameLike_args();
      args.setUserNameLike(userNameLike);
      args.setPageNum(pageNum);
      args.setPageSize(pageSize);
      sendBase("getUsersByUserNameLike", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser recv_getUsersByUserNameLike() throws org.apache.thrift.TException
    {
      getUsersByUserNameLike_result result = new getUsersByUserNameLike_result();
      receiveBase(result, "getUsersByUserNameLike");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUsersByUserNameLike failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersByNickNameLike(String nickNameLike, int pageNum, int pageSize) throws org.apache.thrift.TException
    {
      send_getUsersByNickNameLike(nickNameLike, pageNum, pageSize);
      return recv_getUsersByNickNameLike();
    }

    public void send_getUsersByNickNameLike(String nickNameLike, int pageNum, int pageSize) throws org.apache.thrift.TException
    {
      getUsersByNickNameLike_args args = new getUsersByNickNameLike_args();
      args.setNickNameLike(nickNameLike);
      args.setPageNum(pageNum);
      args.setPageSize(pageSize);
      sendBase("getUsersByNickNameLike", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser recv_getUsersByNickNameLike() throws org.apache.thrift.TException
    {
      getUsersByNickNameLike_result result = new getUsersByNickNameLike_result();
      receiveBase(result, "getUsersByNickNameLike");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUsersByNickNameLike failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersRichLevelGreaterThan(int richLevel, int pageNum, int pageSize) throws org.apache.thrift.TException
    {
      send_getUsersRichLevelGreaterThan(richLevel, pageNum, pageSize);
      return recv_getUsersRichLevelGreaterThan();
    }

    public void send_getUsersRichLevelGreaterThan(int richLevel, int pageNum, int pageSize) throws org.apache.thrift.TException
    {
      getUsersRichLevelGreaterThan_args args = new getUsersRichLevelGreaterThan_args();
      args.setRichLevel(richLevel);
      args.setPageNum(pageNum);
      args.setPageSize(pageSize);
      sendBase("getUsersRichLevelGreaterThan", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser recv_getUsersRichLevelGreaterThan() throws org.apache.thrift.TException
    {
      getUsersRichLevelGreaterThan_result result = new getUsersRichLevelGreaterThan_result();
      receiveBase(result, "getUsersRichLevelGreaterThan");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUsersRichLevelGreaterThan failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersStarLevelGreaterThan(int starLevel, int pageNum, int pageSize) throws org.apache.thrift.TException
    {
      send_getUsersStarLevelGreaterThan(starLevel, pageNum, pageSize);
      return recv_getUsersStarLevelGreaterThan();
    }

    public void send_getUsersStarLevelGreaterThan(int starLevel, int pageNum, int pageSize) throws org.apache.thrift.TException
    {
      getUsersStarLevelGreaterThan_args args = new getUsersStarLevelGreaterThan_args();
      args.setStarLevel(starLevel);
      args.setPageNum(pageNum);
      args.setPageSize(pageSize);
      sendBase("getUsersStarLevelGreaterThan", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser recv_getUsersStarLevelGreaterThan() throws org.apache.thrift.TException
    {
      getUsersStarLevelGreaterThan_result result = new getUsersStarLevelGreaterThan_result();
      receiveBase(result, "getUsersStarLevelGreaterThan");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUsersStarLevelGreaterThan failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersByKugouIdsAndNickNameLike(List<Long> kugouIds, String nickNameLike) throws org.apache.thrift.TException
    {
      send_getUsersByKugouIdsAndNickNameLike(kugouIds, nickNameLike);
      return recv_getUsersByKugouIdsAndNickNameLike();
    }

    public void send_getUsersByKugouIdsAndNickNameLike(List<Long> kugouIds, String nickNameLike) throws org.apache.thrift.TException
    {
      getUsersByKugouIdsAndNickNameLike_args args = new getUsersByKugouIdsAndNickNameLike_args();
      args.setKugouIds(kugouIds);
      args.setNickNameLike(nickNameLike);
      sendBase("getUsersByKugouIdsAndNickNameLike", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser recv_getUsersByKugouIdsAndNickNameLike() throws org.apache.thrift.TException
    {
      getUsersByKugouIdsAndNickNameLike_result result = new getUsersByKugouIdsAndNickNameLike_result();
      receiveBase(result, "getUsersByKugouIdsAndNickNameLike");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUsersByKugouIdsAndNickNameLike failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg getUsersByKugouIdsAndStarLevelGreaterThan(List<Long> kugouIds, int starLevel) throws org.apache.thrift.TException
    {
      send_getUsersByKugouIdsAndStarLevelGreaterThan(kugouIds, starLevel);
      return recv_getUsersByKugouIdsAndStarLevelGreaterThan();
    }

    public void send_getUsersByKugouIdsAndStarLevelGreaterThan(List<Long> kugouIds, int starLevel) throws org.apache.thrift.TException
    {
      getUsersByKugouIdsAndStarLevelGreaterThan_args args = new getUsersByKugouIdsAndStarLevelGreaterThan_args();
      args.setKugouIds(kugouIds);
      args.setStarLevel(starLevel);
      sendBase("getUsersByKugouIdsAndStarLevelGreaterThan", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg recv_getUsersByKugouIdsAndStarLevelGreaterThan() throws org.apache.thrift.TException
    {
      getUsersByKugouIdsAndStarLevelGreaterThan_result result = new getUsersByKugouIdsAndStarLevelGreaterThan_result();
      receiveBase(result, "getUsersByKugouIdsAndStarLevelGreaterThan");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUsersByKugouIdsAndStarLevelGreaterThan failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getUsersByAddTime(int startTime, int endTime, int pageNum, int pageSize) throws org.apache.thrift.TException
    {
      send_getUsersByAddTime(startTime, endTime, pageNum, pageSize);
      return recv_getUsersByAddTime();
    }

    public void send_getUsersByAddTime(int startTime, int endTime, int pageNum, int pageSize) throws org.apache.thrift.TException
    {
      getUsersByAddTime_args args = new getUsersByAddTime_args();
      args.setStartTime(startTime);
      args.setEndTime(endTime);
      args.setPageNum(pageNum);
      args.setPageSize(pageSize);
      sendBase("getUsersByAddTime", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser recv_getUsersByAddTime() throws org.apache.thrift.TException
    {
      getUsersByAddTime_result result = new getUsersByAddTime_result();
      receiveBase(result, "getUsersByAddTime");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUsersByAddTime failed: unknown result");
    }

  }
  public static class AsyncClient extends org.apache.thrift.async.TAsyncClient implements AsyncIface {
    public static class Factory implements org.apache.thrift.async.TAsyncClientFactory<AsyncClient> {
      private org.apache.thrift.async.TAsyncClientManager clientManager;
      private org.apache.thrift.protocol.TProtocolFactory protocolFactory;
      public Factory(org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.protocol.TProtocolFactory protocolFactory) {
        this.clientManager = clientManager;
        this.protocolFactory = protocolFactory;
      }
      public AsyncClient getAsyncClient(org.apache.thrift.transport.TNonblockingTransport transport) {
        return new AsyncClient(protocolFactory, clientManager, transport);
      }
    }

    public AsyncClient(org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.transport.TNonblockingTransport transport) {
      super(protocolFactory, clientManager, transport);
    }

    public void getAllUserCount(org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getAllUserCount_call method_call = new getAllUserCount_call(resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getAllUserCount_call extends org.apache.thrift.async.TAsyncMethodCall {
      public getAllUserCount_call(org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getAllUserCount", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getAllUserCount_args args = new getAllUserCount_args();
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResCount getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getAllUserCount();
      }
    }

    public void getUserCountByProductLine(int productLineId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUserCountByProductLine_call method_call = new getUserCountByProductLine_call(productLineId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUserCountByProductLine_call extends org.apache.thrift.async.TAsyncMethodCall {
      private int productLineId;
      public getUserCountByProductLine_call(int productLineId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.productLineId = productLineId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUserCountByProductLine", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUserCountByProductLine_args args = new getUserCountByProductLine_args();
        args.setProductLineId(productLineId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResCount getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUserCountByProductLine();
      }
    }

    public void getUserCountByProductLineAndStatus(int productLineId, int status, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUserCountByProductLineAndStatus_call method_call = new getUserCountByProductLineAndStatus_call(productLineId, status, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUserCountByProductLineAndStatus_call extends org.apache.thrift.async.TAsyncMethodCall {
      private int productLineId;
      private int status;
      public getUserCountByProductLineAndStatus_call(int productLineId, int status, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.productLineId = productLineId;
        this.status = status;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUserCountByProductLineAndStatus", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUserCountByProductLineAndStatus_args args = new getUserCountByProductLineAndStatus_args();
        args.setProductLineId(productLineId);
        args.setStatus(status);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResCount getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUserCountByProductLineAndStatus();
      }
    }

    public void getUserCountByRichLevel(int richLevel, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUserCountByRichLevel_call method_call = new getUserCountByRichLevel_call(richLevel, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUserCountByRichLevel_call extends org.apache.thrift.async.TAsyncMethodCall {
      private int richLevel;
      public getUserCountByRichLevel_call(int richLevel, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.richLevel = richLevel;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUserCountByRichLevel", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUserCountByRichLevel_args args = new getUserCountByRichLevel_args();
        args.setRichLevel(richLevel);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResCount getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUserCountByRichLevel();
      }
    }

    public void getUsersByUserNameLike(String userNameLike, int pageNum, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUsersByUserNameLike_call method_call = new getUsersByUserNameLike_call(userNameLike, pageNum, pageSize, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUsersByUserNameLike_call extends org.apache.thrift.async.TAsyncMethodCall {
      private String userNameLike;
      private int pageNum;
      private int pageSize;
      public getUsersByUserNameLike_call(String userNameLike, int pageNum, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.userNameLike = userNameLike;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUsersByUserNameLike", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUsersByUserNameLike_args args = new getUsersByUserNameLike_args();
        args.setUserNameLike(userNameLike);
        args.setPageNum(pageNum);
        args.setPageSize(pageSize);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUsersByUserNameLike();
      }
    }

    public void getUsersByNickNameLike(String nickNameLike, int pageNum, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUsersByNickNameLike_call method_call = new getUsersByNickNameLike_call(nickNameLike, pageNum, pageSize, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUsersByNickNameLike_call extends org.apache.thrift.async.TAsyncMethodCall {
      private String nickNameLike;
      private int pageNum;
      private int pageSize;
      public getUsersByNickNameLike_call(String nickNameLike, int pageNum, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.nickNameLike = nickNameLike;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUsersByNickNameLike", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUsersByNickNameLike_args args = new getUsersByNickNameLike_args();
        args.setNickNameLike(nickNameLike);
        args.setPageNum(pageNum);
        args.setPageSize(pageSize);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUsersByNickNameLike();
      }
    }

    public void getUsersRichLevelGreaterThan(int richLevel, int pageNum, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUsersRichLevelGreaterThan_call method_call = new getUsersRichLevelGreaterThan_call(richLevel, pageNum, pageSize, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUsersRichLevelGreaterThan_call extends org.apache.thrift.async.TAsyncMethodCall {
      private int richLevel;
      private int pageNum;
      private int pageSize;
      public getUsersRichLevelGreaterThan_call(int richLevel, int pageNum, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.richLevel = richLevel;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUsersRichLevelGreaterThan", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUsersRichLevelGreaterThan_args args = new getUsersRichLevelGreaterThan_args();
        args.setRichLevel(richLevel);
        args.setPageNum(pageNum);
        args.setPageSize(pageSize);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUsersRichLevelGreaterThan();
      }
    }

    public void getUsersStarLevelGreaterThan(int starLevel, int pageNum, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUsersStarLevelGreaterThan_call method_call = new getUsersStarLevelGreaterThan_call(starLevel, pageNum, pageSize, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUsersStarLevelGreaterThan_call extends org.apache.thrift.async.TAsyncMethodCall {
      private int starLevel;
      private int pageNum;
      private int pageSize;
      public getUsersStarLevelGreaterThan_call(int starLevel, int pageNum, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.starLevel = starLevel;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUsersStarLevelGreaterThan", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUsersStarLevelGreaterThan_args args = new getUsersStarLevelGreaterThan_args();
        args.setStarLevel(starLevel);
        args.setPageNum(pageNum);
        args.setPageSize(pageSize);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUsersStarLevelGreaterThan();
      }
    }

    public void getUsersByKugouIdsAndNickNameLike(List<Long> kugouIds, String nickNameLike, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUsersByKugouIdsAndNickNameLike_call method_call = new getUsersByKugouIdsAndNickNameLike_call(kugouIds, nickNameLike, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUsersByKugouIdsAndNickNameLike_call extends org.apache.thrift.async.TAsyncMethodCall {
      private List<Long> kugouIds;
      private String nickNameLike;
      public getUsersByKugouIdsAndNickNameLike_call(List<Long> kugouIds, String nickNameLike, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouIds = kugouIds;
        this.nickNameLike = nickNameLike;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUsersByKugouIdsAndNickNameLike", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUsersByKugouIdsAndNickNameLike_args args = new getUsersByKugouIdsAndNickNameLike_args();
        args.setKugouIds(kugouIds);
        args.setNickNameLike(nickNameLike);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUsersByKugouIdsAndNickNameLike();
      }
    }

    public void getUsersByKugouIdsAndStarLevelGreaterThan(List<Long> kugouIds, int starLevel, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUsersByKugouIdsAndStarLevelGreaterThan_call method_call = new getUsersByKugouIdsAndStarLevelGreaterThan_call(kugouIds, starLevel, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUsersByKugouIdsAndStarLevelGreaterThan_call extends org.apache.thrift.async.TAsyncMethodCall {
      private List<Long> kugouIds;
      private int starLevel;
      public getUsersByKugouIdsAndStarLevelGreaterThan_call(List<Long> kugouIds, int starLevel, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouIds = kugouIds;
        this.starLevel = starLevel;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUsersByKugouIdsAndStarLevelGreaterThan", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUsersByKugouIdsAndStarLevelGreaterThan_args args = new getUsersByKugouIdsAndStarLevelGreaterThan_args();
        args.setKugouIds(kugouIds);
        args.setStarLevel(starLevel);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUsersByKugouIdsAndStarLevelGreaterThan();
      }
    }

    public void getUsersByAddTime(int startTime, int endTime, int pageNum, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUsersByAddTime_call method_call = new getUsersByAddTime_call(startTime, endTime, pageNum, pageSize, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUsersByAddTime_call extends org.apache.thrift.async.TAsyncMethodCall {
      private int startTime;
      private int endTime;
      private int pageNum;
      private int pageSize;
      public getUsersByAddTime_call(int startTime, int endTime, int pageNum, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.startTime = startTime;
        this.endTime = endTime;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUsersByAddTime", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUsersByAddTime_args args = new getUsersByAddTime_args();
        args.setStartTime(startTime);
        args.setEndTime(endTime);
        args.setPageNum(pageNum);
        args.setPageSize(pageSize);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUsersByAddTime();
      }
    }

  }

  public static class Processor<I extends Iface> extends org.apache.thrift.TBaseProcessor<I> implements org.apache.thrift.TProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());
    public Processor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>>()));
    }

    protected Processor(I iface, Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends Iface> Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> getProcessMap(Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      processMap.put("getAllUserCount", new getAllUserCount());
      processMap.put("getUserCountByProductLine", new getUserCountByProductLine());
      processMap.put("getUserCountByProductLineAndStatus", new getUserCountByProductLineAndStatus());
      processMap.put("getUserCountByRichLevel", new getUserCountByRichLevel());
      processMap.put("getUsersByUserNameLike", new getUsersByUserNameLike());
      processMap.put("getUsersByNickNameLike", new getUsersByNickNameLike());
      processMap.put("getUsersRichLevelGreaterThan", new getUsersRichLevelGreaterThan());
      processMap.put("getUsersStarLevelGreaterThan", new getUsersStarLevelGreaterThan());
      processMap.put("getUsersByKugouIdsAndNickNameLike", new getUsersByKugouIdsAndNickNameLike());
      processMap.put("getUsersByKugouIdsAndStarLevelGreaterThan", new getUsersByKugouIdsAndStarLevelGreaterThan());
      processMap.put("getUsersByAddTime", new getUsersByAddTime());
      return processMap;
    }

    public static class getAllUserCount<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getAllUserCount_args> {
      public getAllUserCount() {
        super("getAllUserCount");
      }

      public getAllUserCount_args getEmptyArgsInstance() {
        return new getAllUserCount_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getAllUserCount_result getResult(I iface, getAllUserCount_args args) throws org.apache.thrift.TException {
        getAllUserCount_result result = new getAllUserCount_result();
        result.success = iface.getAllUserCount();
        return result;
      }
    }

    public static class getUserCountByProductLine<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUserCountByProductLine_args> {
      public getUserCountByProductLine() {
        super("getUserCountByProductLine");
      }

      public getUserCountByProductLine_args getEmptyArgsInstance() {
        return new getUserCountByProductLine_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUserCountByProductLine_result getResult(I iface, getUserCountByProductLine_args args) throws org.apache.thrift.TException {
        getUserCountByProductLine_result result = new getUserCountByProductLine_result();
        result.success = iface.getUserCountByProductLine(args.productLineId);
        return result;
      }
    }

    public static class getUserCountByProductLineAndStatus<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUserCountByProductLineAndStatus_args> {
      public getUserCountByProductLineAndStatus() {
        super("getUserCountByProductLineAndStatus");
      }

      public getUserCountByProductLineAndStatus_args getEmptyArgsInstance() {
        return new getUserCountByProductLineAndStatus_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUserCountByProductLineAndStatus_result getResult(I iface, getUserCountByProductLineAndStatus_args args) throws org.apache.thrift.TException {
        getUserCountByProductLineAndStatus_result result = new getUserCountByProductLineAndStatus_result();
        result.success = iface.getUserCountByProductLineAndStatus(args.productLineId, args.status);
        return result;
      }
    }

    public static class getUserCountByRichLevel<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUserCountByRichLevel_args> {
      public getUserCountByRichLevel() {
        super("getUserCountByRichLevel");
      }

      public getUserCountByRichLevel_args getEmptyArgsInstance() {
        return new getUserCountByRichLevel_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUserCountByRichLevel_result getResult(I iface, getUserCountByRichLevel_args args) throws org.apache.thrift.TException {
        getUserCountByRichLevel_result result = new getUserCountByRichLevel_result();
        result.success = iface.getUserCountByRichLevel(args.richLevel);
        return result;
      }
    }

    public static class getUsersByUserNameLike<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUsersByUserNameLike_args> {
      public getUsersByUserNameLike() {
        super("getUsersByUserNameLike");
      }

      public getUsersByUserNameLike_args getEmptyArgsInstance() {
        return new getUsersByUserNameLike_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUsersByUserNameLike_result getResult(I iface, getUsersByUserNameLike_args args) throws org.apache.thrift.TException {
        getUsersByUserNameLike_result result = new getUsersByUserNameLike_result();
        result.success = iface.getUsersByUserNameLike(args.userNameLike, args.pageNum, args.pageSize);
        return result;
      }
    }

    public static class getUsersByNickNameLike<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUsersByNickNameLike_args> {
      public getUsersByNickNameLike() {
        super("getUsersByNickNameLike");
      }

      public getUsersByNickNameLike_args getEmptyArgsInstance() {
        return new getUsersByNickNameLike_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUsersByNickNameLike_result getResult(I iface, getUsersByNickNameLike_args args) throws org.apache.thrift.TException {
        getUsersByNickNameLike_result result = new getUsersByNickNameLike_result();
        result.success = iface.getUsersByNickNameLike(args.nickNameLike, args.pageNum, args.pageSize);
        return result;
      }
    }

    public static class getUsersRichLevelGreaterThan<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUsersRichLevelGreaterThan_args> {
      public getUsersRichLevelGreaterThan() {
        super("getUsersRichLevelGreaterThan");
      }

      public getUsersRichLevelGreaterThan_args getEmptyArgsInstance() {
        return new getUsersRichLevelGreaterThan_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUsersRichLevelGreaterThan_result getResult(I iface, getUsersRichLevelGreaterThan_args args) throws org.apache.thrift.TException {
        getUsersRichLevelGreaterThan_result result = new getUsersRichLevelGreaterThan_result();
        result.success = iface.getUsersRichLevelGreaterThan(args.richLevel, args.pageNum, args.pageSize);
        return result;
      }
    }

    public static class getUsersStarLevelGreaterThan<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUsersStarLevelGreaterThan_args> {
      public getUsersStarLevelGreaterThan() {
        super("getUsersStarLevelGreaterThan");
      }

      public getUsersStarLevelGreaterThan_args getEmptyArgsInstance() {
        return new getUsersStarLevelGreaterThan_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUsersStarLevelGreaterThan_result getResult(I iface, getUsersStarLevelGreaterThan_args args) throws org.apache.thrift.TException {
        getUsersStarLevelGreaterThan_result result = new getUsersStarLevelGreaterThan_result();
        result.success = iface.getUsersStarLevelGreaterThan(args.starLevel, args.pageNum, args.pageSize);
        return result;
      }
    }

    public static class getUsersByKugouIdsAndNickNameLike<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUsersByKugouIdsAndNickNameLike_args> {
      public getUsersByKugouIdsAndNickNameLike() {
        super("getUsersByKugouIdsAndNickNameLike");
      }

      public getUsersByKugouIdsAndNickNameLike_args getEmptyArgsInstance() {
        return new getUsersByKugouIdsAndNickNameLike_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUsersByKugouIdsAndNickNameLike_result getResult(I iface, getUsersByKugouIdsAndNickNameLike_args args) throws org.apache.thrift.TException {
        getUsersByKugouIdsAndNickNameLike_result result = new getUsersByKugouIdsAndNickNameLike_result();
        result.success = iface.getUsersByKugouIdsAndNickNameLike(args.kugouIds, args.nickNameLike);
        return result;
      }
    }

    public static class getUsersByKugouIdsAndStarLevelGreaterThan<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUsersByKugouIdsAndStarLevelGreaterThan_args> {
      public getUsersByKugouIdsAndStarLevelGreaterThan() {
        super("getUsersByKugouIdsAndStarLevelGreaterThan");
      }

      public getUsersByKugouIdsAndStarLevelGreaterThan_args getEmptyArgsInstance() {
        return new getUsersByKugouIdsAndStarLevelGreaterThan_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUsersByKugouIdsAndStarLevelGreaterThan_result getResult(I iface, getUsersByKugouIdsAndStarLevelGreaterThan_args args) throws org.apache.thrift.TException {
        getUsersByKugouIdsAndStarLevelGreaterThan_result result = new getUsersByKugouIdsAndStarLevelGreaterThan_result();
        result.success = iface.getUsersByKugouIdsAndStarLevelGreaterThan(args.kugouIds, args.starLevel);
        return result;
      }
    }

    public static class getUsersByAddTime<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUsersByAddTime_args> {
      public getUsersByAddTime() {
        super("getUsersByAddTime");
      }

      public getUsersByAddTime_args getEmptyArgsInstance() {
        return new getUsersByAddTime_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUsersByAddTime_result getResult(I iface, getUsersByAddTime_args args) throws org.apache.thrift.TException {
        getUsersByAddTime_result result = new getUsersByAddTime_result();
        result.success = iface.getUsersByAddTime(args.startTime, args.endTime, args.pageNum, args.pageSize);
        return result;
      }
    }

  }

  public static class AsyncProcessor<I extends AsyncIface> extends org.apache.thrift.TBaseAsyncProcessor<I> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());
    public AsyncProcessor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.AsyncProcessFunction<I, ? extends org.apache.thrift.TBase, ?>>()));
    }

    protected AsyncProcessor(I iface, Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends AsyncIface> Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase,?>> getProcessMap(Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      processMap.put("getAllUserCount", new getAllUserCount());
      processMap.put("getUserCountByProductLine", new getUserCountByProductLine());
      processMap.put("getUserCountByProductLineAndStatus", new getUserCountByProductLineAndStatus());
      processMap.put("getUserCountByRichLevel", new getUserCountByRichLevel());
      processMap.put("getUsersByUserNameLike", new getUsersByUserNameLike());
      processMap.put("getUsersByNickNameLike", new getUsersByNickNameLike());
      processMap.put("getUsersRichLevelGreaterThan", new getUsersRichLevelGreaterThan());
      processMap.put("getUsersStarLevelGreaterThan", new getUsersStarLevelGreaterThan());
      processMap.put("getUsersByKugouIdsAndNickNameLike", new getUsersByKugouIdsAndNickNameLike());
      processMap.put("getUsersByKugouIdsAndStarLevelGreaterThan", new getUsersByKugouIdsAndStarLevelGreaterThan());
      processMap.put("getUsersByAddTime", new getUsersByAddTime());
      return processMap;
    }

    public static class getAllUserCount<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getAllUserCount_args, com.kugou.fanxing.thrift.plat.user.vo.ResCount> {
      public getAllUserCount() {
        super("getAllUserCount");
      }

      public getAllUserCount_args getEmptyArgsInstance() {
        return new getAllUserCount_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCount> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCount>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResCount o) {
            getAllUserCount_result result = new getAllUserCount_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getAllUserCount_result result = new getAllUserCount_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getAllUserCount_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCount> resultHandler) throws TException {
        iface.getAllUserCount(resultHandler);
      }
    }

    public static class getUserCountByProductLine<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUserCountByProductLine_args, com.kugou.fanxing.thrift.plat.user.vo.ResCount> {
      public getUserCountByProductLine() {
        super("getUserCountByProductLine");
      }

      public getUserCountByProductLine_args getEmptyArgsInstance() {
        return new getUserCountByProductLine_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCount> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCount>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResCount o) {
            getUserCountByProductLine_result result = new getUserCountByProductLine_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUserCountByProductLine_result result = new getUserCountByProductLine_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUserCountByProductLine_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCount> resultHandler) throws TException {
        iface.getUserCountByProductLine(args.productLineId,resultHandler);
      }
    }

    public static class getUserCountByProductLineAndStatus<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUserCountByProductLineAndStatus_args, com.kugou.fanxing.thrift.plat.user.vo.ResCount> {
      public getUserCountByProductLineAndStatus() {
        super("getUserCountByProductLineAndStatus");
      }

      public getUserCountByProductLineAndStatus_args getEmptyArgsInstance() {
        return new getUserCountByProductLineAndStatus_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCount> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCount>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResCount o) {
            getUserCountByProductLineAndStatus_result result = new getUserCountByProductLineAndStatus_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUserCountByProductLineAndStatus_result result = new getUserCountByProductLineAndStatus_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUserCountByProductLineAndStatus_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCount> resultHandler) throws TException {
        iface.getUserCountByProductLineAndStatus(args.productLineId, args.status,resultHandler);
      }
    }

    public static class getUserCountByRichLevel<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUserCountByRichLevel_args, com.kugou.fanxing.thrift.plat.user.vo.ResCount> {
      public getUserCountByRichLevel() {
        super("getUserCountByRichLevel");
      }

      public getUserCountByRichLevel_args getEmptyArgsInstance() {
        return new getUserCountByRichLevel_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCount> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCount>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResCount o) {
            getUserCountByRichLevel_result result = new getUserCountByRichLevel_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUserCountByRichLevel_result result = new getUserCountByRichLevel_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUserCountByRichLevel_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCount> resultHandler) throws TException {
        iface.getUserCountByRichLevel(args.richLevel,resultHandler);
      }
    }

    public static class getUsersByUserNameLike<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUsersByUserNameLike_args, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> {
      public getUsersByUserNameLike() {
        super("getUsersByUserNameLike");
      }

      public getUsersByUserNameLike_args getEmptyArgsInstance() {
        return new getUsersByUserNameLike_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser o) {
            getUsersByUserNameLike_result result = new getUsersByUserNameLike_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUsersByUserNameLike_result result = new getUsersByUserNameLike_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUsersByUserNameLike_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> resultHandler) throws TException {
        iface.getUsersByUserNameLike(args.userNameLike, args.pageNum, args.pageSize,resultHandler);
      }
    }

    public static class getUsersByNickNameLike<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUsersByNickNameLike_args, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> {
      public getUsersByNickNameLike() {
        super("getUsersByNickNameLike");
      }

      public getUsersByNickNameLike_args getEmptyArgsInstance() {
        return new getUsersByNickNameLike_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser o) {
            getUsersByNickNameLike_result result = new getUsersByNickNameLike_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUsersByNickNameLike_result result = new getUsersByNickNameLike_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUsersByNickNameLike_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> resultHandler) throws TException {
        iface.getUsersByNickNameLike(args.nickNameLike, args.pageNum, args.pageSize,resultHandler);
      }
    }

    public static class getUsersRichLevelGreaterThan<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUsersRichLevelGreaterThan_args, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> {
      public getUsersRichLevelGreaterThan() {
        super("getUsersRichLevelGreaterThan");
      }

      public getUsersRichLevelGreaterThan_args getEmptyArgsInstance() {
        return new getUsersRichLevelGreaterThan_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser o) {
            getUsersRichLevelGreaterThan_result result = new getUsersRichLevelGreaterThan_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUsersRichLevelGreaterThan_result result = new getUsersRichLevelGreaterThan_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUsersRichLevelGreaterThan_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> resultHandler) throws TException {
        iface.getUsersRichLevelGreaterThan(args.richLevel, args.pageNum, args.pageSize,resultHandler);
      }
    }

    public static class getUsersStarLevelGreaterThan<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUsersStarLevelGreaterThan_args, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> {
      public getUsersStarLevelGreaterThan() {
        super("getUsersStarLevelGreaterThan");
      }

      public getUsersStarLevelGreaterThan_args getEmptyArgsInstance() {
        return new getUsersStarLevelGreaterThan_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser o) {
            getUsersStarLevelGreaterThan_result result = new getUsersStarLevelGreaterThan_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUsersStarLevelGreaterThan_result result = new getUsersStarLevelGreaterThan_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUsersStarLevelGreaterThan_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> resultHandler) throws TException {
        iface.getUsersStarLevelGreaterThan(args.starLevel, args.pageNum, args.pageSize,resultHandler);
      }
    }

    public static class getUsersByKugouIdsAndNickNameLike<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUsersByKugouIdsAndNickNameLike_args, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> {
      public getUsersByKugouIdsAndNickNameLike() {
        super("getUsersByKugouIdsAndNickNameLike");
      }

      public getUsersByKugouIdsAndNickNameLike_args getEmptyArgsInstance() {
        return new getUsersByKugouIdsAndNickNameLike_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser o) {
            getUsersByKugouIdsAndNickNameLike_result result = new getUsersByKugouIdsAndNickNameLike_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUsersByKugouIdsAndNickNameLike_result result = new getUsersByKugouIdsAndNickNameLike_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUsersByKugouIdsAndNickNameLike_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> resultHandler) throws TException {
        iface.getUsersByKugouIdsAndNickNameLike(args.kugouIds, args.nickNameLike,resultHandler);
      }
    }

    public static class getUsersByKugouIdsAndStarLevelGreaterThan<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUsersByKugouIdsAndStarLevelGreaterThan_args, com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg> {
      public getUsersByKugouIdsAndStarLevelGreaterThan() {
        super("getUsersByKugouIdsAndStarLevelGreaterThan");
      }

      public getUsersByKugouIdsAndStarLevelGreaterThan_args getEmptyArgsInstance() {
        return new getUsersByKugouIdsAndStarLevelGreaterThan_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg o) {
            getUsersByKugouIdsAndStarLevelGreaterThan_result result = new getUsersByKugouIdsAndStarLevelGreaterThan_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUsersByKugouIdsAndStarLevelGreaterThan_result result = new getUsersByKugouIdsAndStarLevelGreaterThan_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUsersByKugouIdsAndStarLevelGreaterThan_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg> resultHandler) throws TException {
        iface.getUsersByKugouIdsAndStarLevelGreaterThan(args.kugouIds, args.starLevel,resultHandler);
      }
    }

    public static class getUsersByAddTime<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUsersByAddTime_args, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> {
      public getUsersByAddTime() {
        super("getUsersByAddTime");
      }

      public getUsersByAddTime_args getEmptyArgsInstance() {
        return new getUsersByAddTime_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser o) {
            getUsersByAddTime_result result = new getUsersByAddTime_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUsersByAddTime_result result = new getUsersByAddTime_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUsersByAddTime_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser> resultHandler) throws TException {
        iface.getUsersByAddTime(args.startTime, args.endTime, args.pageNum, args.pageSize,resultHandler);
      }
    }

  }

  public static class getAllUserCount_args implements org.apache.thrift.TBase<getAllUserCount_args, getAllUserCount_args._Fields>, java.io.Serializable, Cloneable, Comparable<getAllUserCount_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getAllUserCount_args");


    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getAllUserCount_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getAllUserCount_argsTupleSchemeFactory());
    }


    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
;

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getAllUserCount_args.class, metaDataMap);
    }

    public getAllUserCount_args() {
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getAllUserCount_args(getAllUserCount_args other) {
    }

    public getAllUserCount_args deepCopy() {
      return new getAllUserCount_args(this);
    }

    @Override
    public void clear() {
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getAllUserCount_args)
        return this.equals((getAllUserCount_args)that);
      return false;
    }

    public boolean equals(getAllUserCount_args that) {
      if (that == null)
        return false;

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      return list.hashCode();
    }

    @Override
    public int compareTo(getAllUserCount_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getAllUserCount_args(");
      boolean first = true;

      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getAllUserCount_argsStandardSchemeFactory implements SchemeFactory {
      public getAllUserCount_argsStandardScheme getScheme() {
        return new getAllUserCount_argsStandardScheme();
      }
    }

    private static class getAllUserCount_argsStandardScheme extends StandardScheme<getAllUserCount_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getAllUserCount_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getAllUserCount_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getAllUserCount_argsTupleSchemeFactory implements SchemeFactory {
      public getAllUserCount_argsTupleScheme getScheme() {
        return new getAllUserCount_argsTupleScheme();
      }
    }

    private static class getAllUserCount_argsTupleScheme extends TupleScheme<getAllUserCount_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getAllUserCount_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getAllUserCount_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
      }
    }

  }

  public static class getAllUserCount_result implements org.apache.thrift.TBase<getAllUserCount_result, getAllUserCount_result._Fields>, java.io.Serializable, Cloneable, Comparable<getAllUserCount_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getAllUserCount_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getAllUserCount_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getAllUserCount_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCount success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResCount.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getAllUserCount_result.class, metaDataMap);
    }

    public getAllUserCount_result() {
    }

    public getAllUserCount_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResCount success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getAllUserCount_result(getAllUserCount_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCount(other.success);
      }
    }

    public getAllUserCount_result deepCopy() {
      return new getAllUserCount_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCount getSuccess() {
      return this.success;
    }

    public getAllUserCount_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResCount success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResCount)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getAllUserCount_result)
        return this.equals((getAllUserCount_result)that);
      return false;
    }

    public boolean equals(getAllUserCount_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getAllUserCount_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getAllUserCount_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getAllUserCount_resultStandardSchemeFactory implements SchemeFactory {
      public getAllUserCount_resultStandardScheme getScheme() {
        return new getAllUserCount_resultStandardScheme();
      }
    }

    private static class getAllUserCount_resultStandardScheme extends StandardScheme<getAllUserCount_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getAllUserCount_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCount();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getAllUserCount_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getAllUserCount_resultTupleSchemeFactory implements SchemeFactory {
      public getAllUserCount_resultTupleScheme getScheme() {
        return new getAllUserCount_resultTupleScheme();
      }
    }

    private static class getAllUserCount_resultTupleScheme extends TupleScheme<getAllUserCount_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getAllUserCount_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getAllUserCount_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCount();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUserCountByProductLine_args implements org.apache.thrift.TBase<getUserCountByProductLine_args, getUserCountByProductLine_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUserCountByProductLine_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserCountByProductLine_args");

    private static final org.apache.thrift.protocol.TField PRODUCT_LINE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("productLineId", org.apache.thrift.protocol.TType.I32, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserCountByProductLine_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserCountByProductLine_argsTupleSchemeFactory());
    }

    public int productLineId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      PRODUCT_LINE_ID((short)1, "productLineId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // PRODUCT_LINE_ID
            return PRODUCT_LINE_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __PRODUCTLINEID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.PRODUCT_LINE_ID, new org.apache.thrift.meta_data.FieldMetaData("productLineId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserCountByProductLine_args.class, metaDataMap);
    }

    public getUserCountByProductLine_args() {
    }

    public getUserCountByProductLine_args(
      int productLineId)
    {
      this();
      this.productLineId = productLineId;
      setProductLineIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserCountByProductLine_args(getUserCountByProductLine_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.productLineId = other.productLineId;
    }

    public getUserCountByProductLine_args deepCopy() {
      return new getUserCountByProductLine_args(this);
    }

    @Override
    public void clear() {
      setProductLineIdIsSet(false);
      this.productLineId = 0;
    }

    public int getProductLineId() {
      return this.productLineId;
    }

    public getUserCountByProductLine_args setProductLineId(int productLineId) {
      this.productLineId = productLineId;
      setProductLineIdIsSet(true);
      return this;
    }

    public void unsetProductLineId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PRODUCTLINEID_ISSET_ID);
    }

    /** Returns true if field productLineId is set (has been assigned a value) and false otherwise */
    public boolean isSetProductLineId() {
      return EncodingUtils.testBit(__isset_bitfield, __PRODUCTLINEID_ISSET_ID);
    }

    public void setProductLineIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PRODUCTLINEID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case PRODUCT_LINE_ID:
        if (value == null) {
          unsetProductLineId();
        } else {
          setProductLineId((Integer)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case PRODUCT_LINE_ID:
        return getProductLineId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case PRODUCT_LINE_ID:
        return isSetProductLineId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserCountByProductLine_args)
        return this.equals((getUserCountByProductLine_args)that);
      return false;
    }

    public boolean equals(getUserCountByProductLine_args that) {
      if (that == null)
        return false;

      boolean this_present_productLineId = true;
      boolean that_present_productLineId = true;
      if (this_present_productLineId || that_present_productLineId) {
        if (!(this_present_productLineId && that_present_productLineId))
          return false;
        if (this.productLineId != that.productLineId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_productLineId = true;
      list.add(present_productLineId);
      if (present_productLineId)
        list.add(productLineId);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserCountByProductLine_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetProductLineId()).compareTo(other.isSetProductLineId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetProductLineId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.productLineId, other.productLineId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserCountByProductLine_args(");
      boolean first = true;

      sb.append("productLineId:");
      sb.append(this.productLineId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserCountByProductLine_argsStandardSchemeFactory implements SchemeFactory {
      public getUserCountByProductLine_argsStandardScheme getScheme() {
        return new getUserCountByProductLine_argsStandardScheme();
      }
    }

    private static class getUserCountByProductLine_argsStandardScheme extends StandardScheme<getUserCountByProductLine_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserCountByProductLine_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // PRODUCT_LINE_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.productLineId = iprot.readI32();
                struct.setProductLineIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserCountByProductLine_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(PRODUCT_LINE_ID_FIELD_DESC);
        oprot.writeI32(struct.productLineId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserCountByProductLine_argsTupleSchemeFactory implements SchemeFactory {
      public getUserCountByProductLine_argsTupleScheme getScheme() {
        return new getUserCountByProductLine_argsTupleScheme();
      }
    }

    private static class getUserCountByProductLine_argsTupleScheme extends TupleScheme<getUserCountByProductLine_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserCountByProductLine_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetProductLineId()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetProductLineId()) {
          oprot.writeI32(struct.productLineId);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserCountByProductLine_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.productLineId = iprot.readI32();
          struct.setProductLineIdIsSet(true);
        }
      }
    }

  }

  public static class getUserCountByProductLine_result implements org.apache.thrift.TBase<getUserCountByProductLine_result, getUserCountByProductLine_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUserCountByProductLine_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserCountByProductLine_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserCountByProductLine_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserCountByProductLine_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCount success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResCount.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserCountByProductLine_result.class, metaDataMap);
    }

    public getUserCountByProductLine_result() {
    }

    public getUserCountByProductLine_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResCount success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserCountByProductLine_result(getUserCountByProductLine_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCount(other.success);
      }
    }

    public getUserCountByProductLine_result deepCopy() {
      return new getUserCountByProductLine_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCount getSuccess() {
      return this.success;
    }

    public getUserCountByProductLine_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResCount success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResCount)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserCountByProductLine_result)
        return this.equals((getUserCountByProductLine_result)that);
      return false;
    }

    public boolean equals(getUserCountByProductLine_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserCountByProductLine_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserCountByProductLine_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserCountByProductLine_resultStandardSchemeFactory implements SchemeFactory {
      public getUserCountByProductLine_resultStandardScheme getScheme() {
        return new getUserCountByProductLine_resultStandardScheme();
      }
    }

    private static class getUserCountByProductLine_resultStandardScheme extends StandardScheme<getUserCountByProductLine_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserCountByProductLine_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCount();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserCountByProductLine_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserCountByProductLine_resultTupleSchemeFactory implements SchemeFactory {
      public getUserCountByProductLine_resultTupleScheme getScheme() {
        return new getUserCountByProductLine_resultTupleScheme();
      }
    }

    private static class getUserCountByProductLine_resultTupleScheme extends TupleScheme<getUserCountByProductLine_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserCountByProductLine_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserCountByProductLine_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCount();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUserCountByProductLineAndStatus_args implements org.apache.thrift.TBase<getUserCountByProductLineAndStatus_args, getUserCountByProductLineAndStatus_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUserCountByProductLineAndStatus_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserCountByProductLineAndStatus_args");

    private static final org.apache.thrift.protocol.TField PRODUCT_LINE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("productLineId", org.apache.thrift.protocol.TType.I32, (short)1);
    private static final org.apache.thrift.protocol.TField STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("status", org.apache.thrift.protocol.TType.I32, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserCountByProductLineAndStatus_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserCountByProductLineAndStatus_argsTupleSchemeFactory());
    }

    public int productLineId; // required
    public int status; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      PRODUCT_LINE_ID((short)1, "productLineId"),
      STATUS((short)2, "status");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // PRODUCT_LINE_ID
            return PRODUCT_LINE_ID;
          case 2: // STATUS
            return STATUS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __PRODUCTLINEID_ISSET_ID = 0;
    private static final int __STATUS_ISSET_ID = 1;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.PRODUCT_LINE_ID, new org.apache.thrift.meta_data.FieldMetaData("productLineId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      tmpMap.put(_Fields.STATUS, new org.apache.thrift.meta_data.FieldMetaData("status", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserCountByProductLineAndStatus_args.class, metaDataMap);
    }

    public getUserCountByProductLineAndStatus_args() {
    }

    public getUserCountByProductLineAndStatus_args(
      int productLineId,
      int status)
    {
      this();
      this.productLineId = productLineId;
      setProductLineIdIsSet(true);
      this.status = status;
      setStatusIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserCountByProductLineAndStatus_args(getUserCountByProductLineAndStatus_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.productLineId = other.productLineId;
      this.status = other.status;
    }

    public getUserCountByProductLineAndStatus_args deepCopy() {
      return new getUserCountByProductLineAndStatus_args(this);
    }

    @Override
    public void clear() {
      setProductLineIdIsSet(false);
      this.productLineId = 0;
      setStatusIsSet(false);
      this.status = 0;
    }

    public int getProductLineId() {
      return this.productLineId;
    }

    public getUserCountByProductLineAndStatus_args setProductLineId(int productLineId) {
      this.productLineId = productLineId;
      setProductLineIdIsSet(true);
      return this;
    }

    public void unsetProductLineId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PRODUCTLINEID_ISSET_ID);
    }

    /** Returns true if field productLineId is set (has been assigned a value) and false otherwise */
    public boolean isSetProductLineId() {
      return EncodingUtils.testBit(__isset_bitfield, __PRODUCTLINEID_ISSET_ID);
    }

    public void setProductLineIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PRODUCTLINEID_ISSET_ID, value);
    }

    public int getStatus() {
      return this.status;
    }

    public getUserCountByProductLineAndStatus_args setStatus(int status) {
      this.status = status;
      setStatusIsSet(true);
      return this;
    }

    public void unsetStatus() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STATUS_ISSET_ID);
    }

    /** Returns true if field status is set (has been assigned a value) and false otherwise */
    public boolean isSetStatus() {
      return EncodingUtils.testBit(__isset_bitfield, __STATUS_ISSET_ID);
    }

    public void setStatusIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STATUS_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case PRODUCT_LINE_ID:
        if (value == null) {
          unsetProductLineId();
        } else {
          setProductLineId((Integer)value);
        }
        break;

      case STATUS:
        if (value == null) {
          unsetStatus();
        } else {
          setStatus((Integer)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case PRODUCT_LINE_ID:
        return getProductLineId();

      case STATUS:
        return getStatus();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case PRODUCT_LINE_ID:
        return isSetProductLineId();
      case STATUS:
        return isSetStatus();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserCountByProductLineAndStatus_args)
        return this.equals((getUserCountByProductLineAndStatus_args)that);
      return false;
    }

    public boolean equals(getUserCountByProductLineAndStatus_args that) {
      if (that == null)
        return false;

      boolean this_present_productLineId = true;
      boolean that_present_productLineId = true;
      if (this_present_productLineId || that_present_productLineId) {
        if (!(this_present_productLineId && that_present_productLineId))
          return false;
        if (this.productLineId != that.productLineId)
          return false;
      }

      boolean this_present_status = true;
      boolean that_present_status = true;
      if (this_present_status || that_present_status) {
        if (!(this_present_status && that_present_status))
          return false;
        if (this.status != that.status)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_productLineId = true;
      list.add(present_productLineId);
      if (present_productLineId)
        list.add(productLineId);

      boolean present_status = true;
      list.add(present_status);
      if (present_status)
        list.add(status);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserCountByProductLineAndStatus_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetProductLineId()).compareTo(other.isSetProductLineId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetProductLineId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.productLineId, other.productLineId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetStatus()).compareTo(other.isSetStatus());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetStatus()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.status, other.status);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserCountByProductLineAndStatus_args(");
      boolean first = true;

      sb.append("productLineId:");
      sb.append(this.productLineId);
      first = false;
      if (!first) sb.append(", ");
      sb.append("status:");
      sb.append(this.status);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserCountByProductLineAndStatus_argsStandardSchemeFactory implements SchemeFactory {
      public getUserCountByProductLineAndStatus_argsStandardScheme getScheme() {
        return new getUserCountByProductLineAndStatus_argsStandardScheme();
      }
    }

    private static class getUserCountByProductLineAndStatus_argsStandardScheme extends StandardScheme<getUserCountByProductLineAndStatus_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserCountByProductLineAndStatus_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // PRODUCT_LINE_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.productLineId = iprot.readI32();
                struct.setProductLineIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // STATUS
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.status = iprot.readI32();
                struct.setStatusIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserCountByProductLineAndStatus_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(PRODUCT_LINE_ID_FIELD_DESC);
        oprot.writeI32(struct.productLineId);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(STATUS_FIELD_DESC);
        oprot.writeI32(struct.status);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserCountByProductLineAndStatus_argsTupleSchemeFactory implements SchemeFactory {
      public getUserCountByProductLineAndStatus_argsTupleScheme getScheme() {
        return new getUserCountByProductLineAndStatus_argsTupleScheme();
      }
    }

    private static class getUserCountByProductLineAndStatus_argsTupleScheme extends TupleScheme<getUserCountByProductLineAndStatus_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserCountByProductLineAndStatus_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetProductLineId()) {
          optionals.set(0);
        }
        if (struct.isSetStatus()) {
          optionals.set(1);
        }
        oprot.writeBitSet(optionals, 2);
        if (struct.isSetProductLineId()) {
          oprot.writeI32(struct.productLineId);
        }
        if (struct.isSetStatus()) {
          oprot.writeI32(struct.status);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserCountByProductLineAndStatus_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(2);
        if (incoming.get(0)) {
          struct.productLineId = iprot.readI32();
          struct.setProductLineIdIsSet(true);
        }
        if (incoming.get(1)) {
          struct.status = iprot.readI32();
          struct.setStatusIsSet(true);
        }
      }
    }

  }

  public static class getUserCountByProductLineAndStatus_result implements org.apache.thrift.TBase<getUserCountByProductLineAndStatus_result, getUserCountByProductLineAndStatus_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUserCountByProductLineAndStatus_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserCountByProductLineAndStatus_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserCountByProductLineAndStatus_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserCountByProductLineAndStatus_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCount success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResCount.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserCountByProductLineAndStatus_result.class, metaDataMap);
    }

    public getUserCountByProductLineAndStatus_result() {
    }

    public getUserCountByProductLineAndStatus_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResCount success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserCountByProductLineAndStatus_result(getUserCountByProductLineAndStatus_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCount(other.success);
      }
    }

    public getUserCountByProductLineAndStatus_result deepCopy() {
      return new getUserCountByProductLineAndStatus_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCount getSuccess() {
      return this.success;
    }

    public getUserCountByProductLineAndStatus_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResCount success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResCount)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserCountByProductLineAndStatus_result)
        return this.equals((getUserCountByProductLineAndStatus_result)that);
      return false;
    }

    public boolean equals(getUserCountByProductLineAndStatus_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserCountByProductLineAndStatus_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserCountByProductLineAndStatus_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserCountByProductLineAndStatus_resultStandardSchemeFactory implements SchemeFactory {
      public getUserCountByProductLineAndStatus_resultStandardScheme getScheme() {
        return new getUserCountByProductLineAndStatus_resultStandardScheme();
      }
    }

    private static class getUserCountByProductLineAndStatus_resultStandardScheme extends StandardScheme<getUserCountByProductLineAndStatus_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserCountByProductLineAndStatus_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCount();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserCountByProductLineAndStatus_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserCountByProductLineAndStatus_resultTupleSchemeFactory implements SchemeFactory {
      public getUserCountByProductLineAndStatus_resultTupleScheme getScheme() {
        return new getUserCountByProductLineAndStatus_resultTupleScheme();
      }
    }

    private static class getUserCountByProductLineAndStatus_resultTupleScheme extends TupleScheme<getUserCountByProductLineAndStatus_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserCountByProductLineAndStatus_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserCountByProductLineAndStatus_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCount();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUserCountByRichLevel_args implements org.apache.thrift.TBase<getUserCountByRichLevel_args, getUserCountByRichLevel_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUserCountByRichLevel_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserCountByRichLevel_args");

    private static final org.apache.thrift.protocol.TField RICH_LEVEL_FIELD_DESC = new org.apache.thrift.protocol.TField("richLevel", org.apache.thrift.protocol.TType.I32, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserCountByRichLevel_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserCountByRichLevel_argsTupleSchemeFactory());
    }

    public int richLevel; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      RICH_LEVEL((short)1, "richLevel");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // RICH_LEVEL
            return RICH_LEVEL;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __RICHLEVEL_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.RICH_LEVEL, new org.apache.thrift.meta_data.FieldMetaData("richLevel", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserCountByRichLevel_args.class, metaDataMap);
    }

    public getUserCountByRichLevel_args() {
    }

    public getUserCountByRichLevel_args(
      int richLevel)
    {
      this();
      this.richLevel = richLevel;
      setRichLevelIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserCountByRichLevel_args(getUserCountByRichLevel_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.richLevel = other.richLevel;
    }

    public getUserCountByRichLevel_args deepCopy() {
      return new getUserCountByRichLevel_args(this);
    }

    @Override
    public void clear() {
      setRichLevelIsSet(false);
      this.richLevel = 0;
    }

    public int getRichLevel() {
      return this.richLevel;
    }

    public getUserCountByRichLevel_args setRichLevel(int richLevel) {
      this.richLevel = richLevel;
      setRichLevelIsSet(true);
      return this;
    }

    public void unsetRichLevel() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RICHLEVEL_ISSET_ID);
    }

    /** Returns true if field richLevel is set (has been assigned a value) and false otherwise */
    public boolean isSetRichLevel() {
      return EncodingUtils.testBit(__isset_bitfield, __RICHLEVEL_ISSET_ID);
    }

    public void setRichLevelIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RICHLEVEL_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case RICH_LEVEL:
        if (value == null) {
          unsetRichLevel();
        } else {
          setRichLevel((Integer)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case RICH_LEVEL:
        return getRichLevel();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case RICH_LEVEL:
        return isSetRichLevel();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserCountByRichLevel_args)
        return this.equals((getUserCountByRichLevel_args)that);
      return false;
    }

    public boolean equals(getUserCountByRichLevel_args that) {
      if (that == null)
        return false;

      boolean this_present_richLevel = true;
      boolean that_present_richLevel = true;
      if (this_present_richLevel || that_present_richLevel) {
        if (!(this_present_richLevel && that_present_richLevel))
          return false;
        if (this.richLevel != that.richLevel)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_richLevel = true;
      list.add(present_richLevel);
      if (present_richLevel)
        list.add(richLevel);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserCountByRichLevel_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRichLevel()).compareTo(other.isSetRichLevel());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRichLevel()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.richLevel, other.richLevel);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserCountByRichLevel_args(");
      boolean first = true;

      sb.append("richLevel:");
      sb.append(this.richLevel);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserCountByRichLevel_argsStandardSchemeFactory implements SchemeFactory {
      public getUserCountByRichLevel_argsStandardScheme getScheme() {
        return new getUserCountByRichLevel_argsStandardScheme();
      }
    }

    private static class getUserCountByRichLevel_argsStandardScheme extends StandardScheme<getUserCountByRichLevel_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserCountByRichLevel_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // RICH_LEVEL
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.richLevel = iprot.readI32();
                struct.setRichLevelIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserCountByRichLevel_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(RICH_LEVEL_FIELD_DESC);
        oprot.writeI32(struct.richLevel);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserCountByRichLevel_argsTupleSchemeFactory implements SchemeFactory {
      public getUserCountByRichLevel_argsTupleScheme getScheme() {
        return new getUserCountByRichLevel_argsTupleScheme();
      }
    }

    private static class getUserCountByRichLevel_argsTupleScheme extends TupleScheme<getUserCountByRichLevel_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserCountByRichLevel_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetRichLevel()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetRichLevel()) {
          oprot.writeI32(struct.richLevel);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserCountByRichLevel_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.richLevel = iprot.readI32();
          struct.setRichLevelIsSet(true);
        }
      }
    }

  }

  public static class getUserCountByRichLevel_result implements org.apache.thrift.TBase<getUserCountByRichLevel_result, getUserCountByRichLevel_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUserCountByRichLevel_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserCountByRichLevel_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserCountByRichLevel_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserCountByRichLevel_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCount success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResCount.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserCountByRichLevel_result.class, metaDataMap);
    }

    public getUserCountByRichLevel_result() {
    }

    public getUserCountByRichLevel_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResCount success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserCountByRichLevel_result(getUserCountByRichLevel_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCount(other.success);
      }
    }

    public getUserCountByRichLevel_result deepCopy() {
      return new getUserCountByRichLevel_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCount getSuccess() {
      return this.success;
    }

    public getUserCountByRichLevel_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResCount success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResCount)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserCountByRichLevel_result)
        return this.equals((getUserCountByRichLevel_result)that);
      return false;
    }

    public boolean equals(getUserCountByRichLevel_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserCountByRichLevel_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserCountByRichLevel_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserCountByRichLevel_resultStandardSchemeFactory implements SchemeFactory {
      public getUserCountByRichLevel_resultStandardScheme getScheme() {
        return new getUserCountByRichLevel_resultStandardScheme();
      }
    }

    private static class getUserCountByRichLevel_resultStandardScheme extends StandardScheme<getUserCountByRichLevel_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserCountByRichLevel_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCount();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserCountByRichLevel_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserCountByRichLevel_resultTupleSchemeFactory implements SchemeFactory {
      public getUserCountByRichLevel_resultTupleScheme getScheme() {
        return new getUserCountByRichLevel_resultTupleScheme();
      }
    }

    private static class getUserCountByRichLevel_resultTupleScheme extends TupleScheme<getUserCountByRichLevel_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserCountByRichLevel_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserCountByRichLevel_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCount();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUsersByUserNameLike_args implements org.apache.thrift.TBase<getUsersByUserNameLike_args, getUsersByUserNameLike_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersByUserNameLike_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersByUserNameLike_args");

    private static final org.apache.thrift.protocol.TField USER_NAME_LIKE_FIELD_DESC = new org.apache.thrift.protocol.TField("userNameLike", org.apache.thrift.protocol.TType.STRING, (short)1);
    private static final org.apache.thrift.protocol.TField PAGE_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("pageNum", org.apache.thrift.protocol.TType.I32, (short)2);
    private static final org.apache.thrift.protocol.TField PAGE_SIZE_FIELD_DESC = new org.apache.thrift.protocol.TField("pageSize", org.apache.thrift.protocol.TType.I32, (short)3);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersByUserNameLike_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersByUserNameLike_argsTupleSchemeFactory());
    }

    public String userNameLike; // required
    public int pageNum; // required
    public int pageSize; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      USER_NAME_LIKE((short)1, "userNameLike"),
      PAGE_NUM((short)2, "pageNum"),
      PAGE_SIZE((short)3, "pageSize");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // USER_NAME_LIKE
            return USER_NAME_LIKE;
          case 2: // PAGE_NUM
            return PAGE_NUM;
          case 3: // PAGE_SIZE
            return PAGE_SIZE;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __PAGENUM_ISSET_ID = 0;
    private static final int __PAGESIZE_ISSET_ID = 1;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.USER_NAME_LIKE, new org.apache.thrift.meta_data.FieldMetaData("userNameLike", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      tmpMap.put(_Fields.PAGE_NUM, new org.apache.thrift.meta_data.FieldMetaData("pageNum", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      tmpMap.put(_Fields.PAGE_SIZE, new org.apache.thrift.meta_data.FieldMetaData("pageSize", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersByUserNameLike_args.class, metaDataMap);
    }

    public getUsersByUserNameLike_args() {
    }

    public getUsersByUserNameLike_args(
      String userNameLike,
      int pageNum,
      int pageSize)
    {
      this();
      this.userNameLike = userNameLike;
      this.pageNum = pageNum;
      setPageNumIsSet(true);
      this.pageSize = pageSize;
      setPageSizeIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersByUserNameLike_args(getUsersByUserNameLike_args other) {
      __isset_bitfield = other.__isset_bitfield;
      if (other.isSetUserNameLike()) {
        this.userNameLike = other.userNameLike;
      }
      this.pageNum = other.pageNum;
      this.pageSize = other.pageSize;
    }

    public getUsersByUserNameLike_args deepCopy() {
      return new getUsersByUserNameLike_args(this);
    }

    @Override
    public void clear() {
      this.userNameLike = null;
      setPageNumIsSet(false);
      this.pageNum = 0;
      setPageSizeIsSet(false);
      this.pageSize = 0;
    }

    public String getUserNameLike() {
      return this.userNameLike;
    }

    public getUsersByUserNameLike_args setUserNameLike(String userNameLike) {
      this.userNameLike = userNameLike;
      return this;
    }

    public void unsetUserNameLike() {
      this.userNameLike = null;
    }

    /** Returns true if field userNameLike is set (has been assigned a value) and false otherwise */
    public boolean isSetUserNameLike() {
      return this.userNameLike != null;
    }

    public void setUserNameLikeIsSet(boolean value) {
      if (!value) {
        this.userNameLike = null;
      }
    }

    public int getPageNum() {
      return this.pageNum;
    }

    public getUsersByUserNameLike_args setPageNum(int pageNum) {
      this.pageNum = pageNum;
      setPageNumIsSet(true);
      return this;
    }

    public void unsetPageNum() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGENUM_ISSET_ID);
    }

    /** Returns true if field pageNum is set (has been assigned a value) and false otherwise */
    public boolean isSetPageNum() {
      return EncodingUtils.testBit(__isset_bitfield, __PAGENUM_ISSET_ID);
    }

    public void setPageNumIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGENUM_ISSET_ID, value);
    }

    public int getPageSize() {
      return this.pageSize;
    }

    public getUsersByUserNameLike_args setPageSize(int pageSize) {
      this.pageSize = pageSize;
      setPageSizeIsSet(true);
      return this;
    }

    public void unsetPageSize() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
    }

    /** Returns true if field pageSize is set (has been assigned a value) and false otherwise */
    public boolean isSetPageSize() {
      return EncodingUtils.testBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
    }

    public void setPageSizeIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGESIZE_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case USER_NAME_LIKE:
        if (value == null) {
          unsetUserNameLike();
        } else {
          setUserNameLike((String)value);
        }
        break;

      case PAGE_NUM:
        if (value == null) {
          unsetPageNum();
        } else {
          setPageNum((Integer)value);
        }
        break;

      case PAGE_SIZE:
        if (value == null) {
          unsetPageSize();
        } else {
          setPageSize((Integer)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case USER_NAME_LIKE:
        return getUserNameLike();

      case PAGE_NUM:
        return getPageNum();

      case PAGE_SIZE:
        return getPageSize();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case USER_NAME_LIKE:
        return isSetUserNameLike();
      case PAGE_NUM:
        return isSetPageNum();
      case PAGE_SIZE:
        return isSetPageSize();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersByUserNameLike_args)
        return this.equals((getUsersByUserNameLike_args)that);
      return false;
    }

    public boolean equals(getUsersByUserNameLike_args that) {
      if (that == null)
        return false;

      boolean this_present_userNameLike = true && this.isSetUserNameLike();
      boolean that_present_userNameLike = true && that.isSetUserNameLike();
      if (this_present_userNameLike || that_present_userNameLike) {
        if (!(this_present_userNameLike && that_present_userNameLike))
          return false;
        if (!this.userNameLike.equals(that.userNameLike))
          return false;
      }

      boolean this_present_pageNum = true;
      boolean that_present_pageNum = true;
      if (this_present_pageNum || that_present_pageNum) {
        if (!(this_present_pageNum && that_present_pageNum))
          return false;
        if (this.pageNum != that.pageNum)
          return false;
      }

      boolean this_present_pageSize = true;
      boolean that_present_pageSize = true;
      if (this_present_pageSize || that_present_pageSize) {
        if (!(this_present_pageSize && that_present_pageSize))
          return false;
        if (this.pageSize != that.pageSize)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_userNameLike = true && (isSetUserNameLike());
      list.add(present_userNameLike);
      if (present_userNameLike)
        list.add(userNameLike);

      boolean present_pageNum = true;
      list.add(present_pageNum);
      if (present_pageNum)
        list.add(pageNum);

      boolean present_pageSize = true;
      list.add(present_pageSize);
      if (present_pageSize)
        list.add(pageSize);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersByUserNameLike_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetUserNameLike()).compareTo(other.isSetUserNameLike());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetUserNameLike()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.userNameLike, other.userNameLike);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetPageNum()).compareTo(other.isSetPageNum());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetPageNum()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pageNum, other.pageNum);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetPageSize()).compareTo(other.isSetPageSize());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetPageSize()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pageSize, other.pageSize);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersByUserNameLike_args(");
      boolean first = true;

      sb.append("userNameLike:");
      if (this.userNameLike == null) {
        sb.append("null");
      } else {
        sb.append(this.userNameLike);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("pageNum:");
      sb.append(this.pageNum);
      first = false;
      if (!first) sb.append(", ");
      sb.append("pageSize:");
      sb.append(this.pageSize);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersByUserNameLike_argsStandardSchemeFactory implements SchemeFactory {
      public getUsersByUserNameLike_argsStandardScheme getScheme() {
        return new getUsersByUserNameLike_argsStandardScheme();
      }
    }

    private static class getUsersByUserNameLike_argsStandardScheme extends StandardScheme<getUsersByUserNameLike_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersByUserNameLike_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // USER_NAME_LIKE
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.userNameLike = iprot.readString();
                struct.setUserNameLikeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // PAGE_NUM
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.pageNum = iprot.readI32();
                struct.setPageNumIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // PAGE_SIZE
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.pageSize = iprot.readI32();
                struct.setPageSizeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersByUserNameLike_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.userNameLike != null) {
          oprot.writeFieldBegin(USER_NAME_LIKE_FIELD_DESC);
          oprot.writeString(struct.userNameLike);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldBegin(PAGE_NUM_FIELD_DESC);
        oprot.writeI32(struct.pageNum);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(PAGE_SIZE_FIELD_DESC);
        oprot.writeI32(struct.pageSize);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersByUserNameLike_argsTupleSchemeFactory implements SchemeFactory {
      public getUsersByUserNameLike_argsTupleScheme getScheme() {
        return new getUsersByUserNameLike_argsTupleScheme();
      }
    }

    private static class getUsersByUserNameLike_argsTupleScheme extends TupleScheme<getUsersByUserNameLike_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersByUserNameLike_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetUserNameLike()) {
          optionals.set(0);
        }
        if (struct.isSetPageNum()) {
          optionals.set(1);
        }
        if (struct.isSetPageSize()) {
          optionals.set(2);
        }
        oprot.writeBitSet(optionals, 3);
        if (struct.isSetUserNameLike()) {
          oprot.writeString(struct.userNameLike);
        }
        if (struct.isSetPageNum()) {
          oprot.writeI32(struct.pageNum);
        }
        if (struct.isSetPageSize()) {
          oprot.writeI32(struct.pageSize);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersByUserNameLike_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(3);
        if (incoming.get(0)) {
          struct.userNameLike = iprot.readString();
          struct.setUserNameLikeIsSet(true);
        }
        if (incoming.get(1)) {
          struct.pageNum = iprot.readI32();
          struct.setPageNumIsSet(true);
        }
        if (incoming.get(2)) {
          struct.pageSize = iprot.readI32();
          struct.setPageSizeIsSet(true);
        }
      }
    }

  }

  public static class getUsersByUserNameLike_result implements org.apache.thrift.TBase<getUsersByUserNameLike_result, getUsersByUserNameLike_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersByUserNameLike_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersByUserNameLike_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersByUserNameLike_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersByUserNameLike_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersByUserNameLike_result.class, metaDataMap);
    }

    public getUsersByUserNameLike_result() {
    }

    public getUsersByUserNameLike_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersByUserNameLike_result(getUsersByUserNameLike_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser(other.success);
      }
    }

    public getUsersByUserNameLike_result deepCopy() {
      return new getUsersByUserNameLike_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getSuccess() {
      return this.success;
    }

    public getUsersByUserNameLike_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersByUserNameLike_result)
        return this.equals((getUsersByUserNameLike_result)that);
      return false;
    }

    public boolean equals(getUsersByUserNameLike_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersByUserNameLike_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersByUserNameLike_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersByUserNameLike_resultStandardSchemeFactory implements SchemeFactory {
      public getUsersByUserNameLike_resultStandardScheme getScheme() {
        return new getUsersByUserNameLike_resultStandardScheme();
      }
    }

    private static class getUsersByUserNameLike_resultStandardScheme extends StandardScheme<getUsersByUserNameLike_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersByUserNameLike_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersByUserNameLike_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersByUserNameLike_resultTupleSchemeFactory implements SchemeFactory {
      public getUsersByUserNameLike_resultTupleScheme getScheme() {
        return new getUsersByUserNameLike_resultTupleScheme();
      }
    }

    private static class getUsersByUserNameLike_resultTupleScheme extends TupleScheme<getUsersByUserNameLike_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersByUserNameLike_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersByUserNameLike_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUsersByNickNameLike_args implements org.apache.thrift.TBase<getUsersByNickNameLike_args, getUsersByNickNameLike_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersByNickNameLike_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersByNickNameLike_args");

    private static final org.apache.thrift.protocol.TField NICK_NAME_LIKE_FIELD_DESC = new org.apache.thrift.protocol.TField("nickNameLike", org.apache.thrift.protocol.TType.STRING, (short)1);
    private static final org.apache.thrift.protocol.TField PAGE_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("pageNum", org.apache.thrift.protocol.TType.I32, (short)2);
    private static final org.apache.thrift.protocol.TField PAGE_SIZE_FIELD_DESC = new org.apache.thrift.protocol.TField("pageSize", org.apache.thrift.protocol.TType.I32, (short)3);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersByNickNameLike_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersByNickNameLike_argsTupleSchemeFactory());
    }

    public String nickNameLike; // required
    public int pageNum; // required
    public int pageSize; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      NICK_NAME_LIKE((short)1, "nickNameLike"),
      PAGE_NUM((short)2, "pageNum"),
      PAGE_SIZE((short)3, "pageSize");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // NICK_NAME_LIKE
            return NICK_NAME_LIKE;
          case 2: // PAGE_NUM
            return PAGE_NUM;
          case 3: // PAGE_SIZE
            return PAGE_SIZE;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __PAGENUM_ISSET_ID = 0;
    private static final int __PAGESIZE_ISSET_ID = 1;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.NICK_NAME_LIKE, new org.apache.thrift.meta_data.FieldMetaData("nickNameLike", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      tmpMap.put(_Fields.PAGE_NUM, new org.apache.thrift.meta_data.FieldMetaData("pageNum", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      tmpMap.put(_Fields.PAGE_SIZE, new org.apache.thrift.meta_data.FieldMetaData("pageSize", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersByNickNameLike_args.class, metaDataMap);
    }

    public getUsersByNickNameLike_args() {
    }

    public getUsersByNickNameLike_args(
      String nickNameLike,
      int pageNum,
      int pageSize)
    {
      this();
      this.nickNameLike = nickNameLike;
      this.pageNum = pageNum;
      setPageNumIsSet(true);
      this.pageSize = pageSize;
      setPageSizeIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersByNickNameLike_args(getUsersByNickNameLike_args other) {
      __isset_bitfield = other.__isset_bitfield;
      if (other.isSetNickNameLike()) {
        this.nickNameLike = other.nickNameLike;
      }
      this.pageNum = other.pageNum;
      this.pageSize = other.pageSize;
    }

    public getUsersByNickNameLike_args deepCopy() {
      return new getUsersByNickNameLike_args(this);
    }

    @Override
    public void clear() {
      this.nickNameLike = null;
      setPageNumIsSet(false);
      this.pageNum = 0;
      setPageSizeIsSet(false);
      this.pageSize = 0;
    }

    public String getNickNameLike() {
      return this.nickNameLike;
    }

    public getUsersByNickNameLike_args setNickNameLike(String nickNameLike) {
      this.nickNameLike = nickNameLike;
      return this;
    }

    public void unsetNickNameLike() {
      this.nickNameLike = null;
    }

    /** Returns true if field nickNameLike is set (has been assigned a value) and false otherwise */
    public boolean isSetNickNameLike() {
      return this.nickNameLike != null;
    }

    public void setNickNameLikeIsSet(boolean value) {
      if (!value) {
        this.nickNameLike = null;
      }
    }

    public int getPageNum() {
      return this.pageNum;
    }

    public getUsersByNickNameLike_args setPageNum(int pageNum) {
      this.pageNum = pageNum;
      setPageNumIsSet(true);
      return this;
    }

    public void unsetPageNum() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGENUM_ISSET_ID);
    }

    /** Returns true if field pageNum is set (has been assigned a value) and false otherwise */
    public boolean isSetPageNum() {
      return EncodingUtils.testBit(__isset_bitfield, __PAGENUM_ISSET_ID);
    }

    public void setPageNumIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGENUM_ISSET_ID, value);
    }

    public int getPageSize() {
      return this.pageSize;
    }

    public getUsersByNickNameLike_args setPageSize(int pageSize) {
      this.pageSize = pageSize;
      setPageSizeIsSet(true);
      return this;
    }

    public void unsetPageSize() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
    }

    /** Returns true if field pageSize is set (has been assigned a value) and false otherwise */
    public boolean isSetPageSize() {
      return EncodingUtils.testBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
    }

    public void setPageSizeIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGESIZE_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case NICK_NAME_LIKE:
        if (value == null) {
          unsetNickNameLike();
        } else {
          setNickNameLike((String)value);
        }
        break;

      case PAGE_NUM:
        if (value == null) {
          unsetPageNum();
        } else {
          setPageNum((Integer)value);
        }
        break;

      case PAGE_SIZE:
        if (value == null) {
          unsetPageSize();
        } else {
          setPageSize((Integer)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case NICK_NAME_LIKE:
        return getNickNameLike();

      case PAGE_NUM:
        return getPageNum();

      case PAGE_SIZE:
        return getPageSize();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case NICK_NAME_LIKE:
        return isSetNickNameLike();
      case PAGE_NUM:
        return isSetPageNum();
      case PAGE_SIZE:
        return isSetPageSize();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersByNickNameLike_args)
        return this.equals((getUsersByNickNameLike_args)that);
      return false;
    }

    public boolean equals(getUsersByNickNameLike_args that) {
      if (that == null)
        return false;

      boolean this_present_nickNameLike = true && this.isSetNickNameLike();
      boolean that_present_nickNameLike = true && that.isSetNickNameLike();
      if (this_present_nickNameLike || that_present_nickNameLike) {
        if (!(this_present_nickNameLike && that_present_nickNameLike))
          return false;
        if (!this.nickNameLike.equals(that.nickNameLike))
          return false;
      }

      boolean this_present_pageNum = true;
      boolean that_present_pageNum = true;
      if (this_present_pageNum || that_present_pageNum) {
        if (!(this_present_pageNum && that_present_pageNum))
          return false;
        if (this.pageNum != that.pageNum)
          return false;
      }

      boolean this_present_pageSize = true;
      boolean that_present_pageSize = true;
      if (this_present_pageSize || that_present_pageSize) {
        if (!(this_present_pageSize && that_present_pageSize))
          return false;
        if (this.pageSize != that.pageSize)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_nickNameLike = true && (isSetNickNameLike());
      list.add(present_nickNameLike);
      if (present_nickNameLike)
        list.add(nickNameLike);

      boolean present_pageNum = true;
      list.add(present_pageNum);
      if (present_pageNum)
        list.add(pageNum);

      boolean present_pageSize = true;
      list.add(present_pageSize);
      if (present_pageSize)
        list.add(pageSize);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersByNickNameLike_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetNickNameLike()).compareTo(other.isSetNickNameLike());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetNickNameLike()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.nickNameLike, other.nickNameLike);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetPageNum()).compareTo(other.isSetPageNum());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetPageNum()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pageNum, other.pageNum);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetPageSize()).compareTo(other.isSetPageSize());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetPageSize()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pageSize, other.pageSize);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersByNickNameLike_args(");
      boolean first = true;

      sb.append("nickNameLike:");
      if (this.nickNameLike == null) {
        sb.append("null");
      } else {
        sb.append(this.nickNameLike);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("pageNum:");
      sb.append(this.pageNum);
      first = false;
      if (!first) sb.append(", ");
      sb.append("pageSize:");
      sb.append(this.pageSize);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersByNickNameLike_argsStandardSchemeFactory implements SchemeFactory {
      public getUsersByNickNameLike_argsStandardScheme getScheme() {
        return new getUsersByNickNameLike_argsStandardScheme();
      }
    }

    private static class getUsersByNickNameLike_argsStandardScheme extends StandardScheme<getUsersByNickNameLike_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersByNickNameLike_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // NICK_NAME_LIKE
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.nickNameLike = iprot.readString();
                struct.setNickNameLikeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // PAGE_NUM
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.pageNum = iprot.readI32();
                struct.setPageNumIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // PAGE_SIZE
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.pageSize = iprot.readI32();
                struct.setPageSizeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersByNickNameLike_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.nickNameLike != null) {
          oprot.writeFieldBegin(NICK_NAME_LIKE_FIELD_DESC);
          oprot.writeString(struct.nickNameLike);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldBegin(PAGE_NUM_FIELD_DESC);
        oprot.writeI32(struct.pageNum);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(PAGE_SIZE_FIELD_DESC);
        oprot.writeI32(struct.pageSize);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersByNickNameLike_argsTupleSchemeFactory implements SchemeFactory {
      public getUsersByNickNameLike_argsTupleScheme getScheme() {
        return new getUsersByNickNameLike_argsTupleScheme();
      }
    }

    private static class getUsersByNickNameLike_argsTupleScheme extends TupleScheme<getUsersByNickNameLike_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersByNickNameLike_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetNickNameLike()) {
          optionals.set(0);
        }
        if (struct.isSetPageNum()) {
          optionals.set(1);
        }
        if (struct.isSetPageSize()) {
          optionals.set(2);
        }
        oprot.writeBitSet(optionals, 3);
        if (struct.isSetNickNameLike()) {
          oprot.writeString(struct.nickNameLike);
        }
        if (struct.isSetPageNum()) {
          oprot.writeI32(struct.pageNum);
        }
        if (struct.isSetPageSize()) {
          oprot.writeI32(struct.pageSize);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersByNickNameLike_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(3);
        if (incoming.get(0)) {
          struct.nickNameLike = iprot.readString();
          struct.setNickNameLikeIsSet(true);
        }
        if (incoming.get(1)) {
          struct.pageNum = iprot.readI32();
          struct.setPageNumIsSet(true);
        }
        if (incoming.get(2)) {
          struct.pageSize = iprot.readI32();
          struct.setPageSizeIsSet(true);
        }
      }
    }

  }

  public static class getUsersByNickNameLike_result implements org.apache.thrift.TBase<getUsersByNickNameLike_result, getUsersByNickNameLike_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersByNickNameLike_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersByNickNameLike_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersByNickNameLike_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersByNickNameLike_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersByNickNameLike_result.class, metaDataMap);
    }

    public getUsersByNickNameLike_result() {
    }

    public getUsersByNickNameLike_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersByNickNameLike_result(getUsersByNickNameLike_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser(other.success);
      }
    }

    public getUsersByNickNameLike_result deepCopy() {
      return new getUsersByNickNameLike_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getSuccess() {
      return this.success;
    }

    public getUsersByNickNameLike_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersByNickNameLike_result)
        return this.equals((getUsersByNickNameLike_result)that);
      return false;
    }

    public boolean equals(getUsersByNickNameLike_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersByNickNameLike_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersByNickNameLike_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersByNickNameLike_resultStandardSchemeFactory implements SchemeFactory {
      public getUsersByNickNameLike_resultStandardScheme getScheme() {
        return new getUsersByNickNameLike_resultStandardScheme();
      }
    }

    private static class getUsersByNickNameLike_resultStandardScheme extends StandardScheme<getUsersByNickNameLike_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersByNickNameLike_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersByNickNameLike_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersByNickNameLike_resultTupleSchemeFactory implements SchemeFactory {
      public getUsersByNickNameLike_resultTupleScheme getScheme() {
        return new getUsersByNickNameLike_resultTupleScheme();
      }
    }

    private static class getUsersByNickNameLike_resultTupleScheme extends TupleScheme<getUsersByNickNameLike_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersByNickNameLike_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersByNickNameLike_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUsersRichLevelGreaterThan_args implements org.apache.thrift.TBase<getUsersRichLevelGreaterThan_args, getUsersRichLevelGreaterThan_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersRichLevelGreaterThan_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersRichLevelGreaterThan_args");

    private static final org.apache.thrift.protocol.TField RICH_LEVEL_FIELD_DESC = new org.apache.thrift.protocol.TField("richLevel", org.apache.thrift.protocol.TType.I32, (short)1);
    private static final org.apache.thrift.protocol.TField PAGE_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("pageNum", org.apache.thrift.protocol.TType.I32, (short)2);
    private static final org.apache.thrift.protocol.TField PAGE_SIZE_FIELD_DESC = new org.apache.thrift.protocol.TField("pageSize", org.apache.thrift.protocol.TType.I32, (short)3);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersRichLevelGreaterThan_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersRichLevelGreaterThan_argsTupleSchemeFactory());
    }

    public int richLevel; // required
    public int pageNum; // required
    public int pageSize; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      RICH_LEVEL((short)1, "richLevel"),
      PAGE_NUM((short)2, "pageNum"),
      PAGE_SIZE((short)3, "pageSize");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // RICH_LEVEL
            return RICH_LEVEL;
          case 2: // PAGE_NUM
            return PAGE_NUM;
          case 3: // PAGE_SIZE
            return PAGE_SIZE;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __RICHLEVEL_ISSET_ID = 0;
    private static final int __PAGENUM_ISSET_ID = 1;
    private static final int __PAGESIZE_ISSET_ID = 2;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.RICH_LEVEL, new org.apache.thrift.meta_data.FieldMetaData("richLevel", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      tmpMap.put(_Fields.PAGE_NUM, new org.apache.thrift.meta_data.FieldMetaData("pageNum", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      tmpMap.put(_Fields.PAGE_SIZE, new org.apache.thrift.meta_data.FieldMetaData("pageSize", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersRichLevelGreaterThan_args.class, metaDataMap);
    }

    public getUsersRichLevelGreaterThan_args() {
    }

    public getUsersRichLevelGreaterThan_args(
      int richLevel,
      int pageNum,
      int pageSize)
    {
      this();
      this.richLevel = richLevel;
      setRichLevelIsSet(true);
      this.pageNum = pageNum;
      setPageNumIsSet(true);
      this.pageSize = pageSize;
      setPageSizeIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersRichLevelGreaterThan_args(getUsersRichLevelGreaterThan_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.richLevel = other.richLevel;
      this.pageNum = other.pageNum;
      this.pageSize = other.pageSize;
    }

    public getUsersRichLevelGreaterThan_args deepCopy() {
      return new getUsersRichLevelGreaterThan_args(this);
    }

    @Override
    public void clear() {
      setRichLevelIsSet(false);
      this.richLevel = 0;
      setPageNumIsSet(false);
      this.pageNum = 0;
      setPageSizeIsSet(false);
      this.pageSize = 0;
    }

    public int getRichLevel() {
      return this.richLevel;
    }

    public getUsersRichLevelGreaterThan_args setRichLevel(int richLevel) {
      this.richLevel = richLevel;
      setRichLevelIsSet(true);
      return this;
    }

    public void unsetRichLevel() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RICHLEVEL_ISSET_ID);
    }

    /** Returns true if field richLevel is set (has been assigned a value) and false otherwise */
    public boolean isSetRichLevel() {
      return EncodingUtils.testBit(__isset_bitfield, __RICHLEVEL_ISSET_ID);
    }

    public void setRichLevelIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RICHLEVEL_ISSET_ID, value);
    }

    public int getPageNum() {
      return this.pageNum;
    }

    public getUsersRichLevelGreaterThan_args setPageNum(int pageNum) {
      this.pageNum = pageNum;
      setPageNumIsSet(true);
      return this;
    }

    public void unsetPageNum() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGENUM_ISSET_ID);
    }

    /** Returns true if field pageNum is set (has been assigned a value) and false otherwise */
    public boolean isSetPageNum() {
      return EncodingUtils.testBit(__isset_bitfield, __PAGENUM_ISSET_ID);
    }

    public void setPageNumIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGENUM_ISSET_ID, value);
    }

    public int getPageSize() {
      return this.pageSize;
    }

    public getUsersRichLevelGreaterThan_args setPageSize(int pageSize) {
      this.pageSize = pageSize;
      setPageSizeIsSet(true);
      return this;
    }

    public void unsetPageSize() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
    }

    /** Returns true if field pageSize is set (has been assigned a value) and false otherwise */
    public boolean isSetPageSize() {
      return EncodingUtils.testBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
    }

    public void setPageSizeIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGESIZE_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case RICH_LEVEL:
        if (value == null) {
          unsetRichLevel();
        } else {
          setRichLevel((Integer)value);
        }
        break;

      case PAGE_NUM:
        if (value == null) {
          unsetPageNum();
        } else {
          setPageNum((Integer)value);
        }
        break;

      case PAGE_SIZE:
        if (value == null) {
          unsetPageSize();
        } else {
          setPageSize((Integer)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case RICH_LEVEL:
        return getRichLevel();

      case PAGE_NUM:
        return getPageNum();

      case PAGE_SIZE:
        return getPageSize();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case RICH_LEVEL:
        return isSetRichLevel();
      case PAGE_NUM:
        return isSetPageNum();
      case PAGE_SIZE:
        return isSetPageSize();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersRichLevelGreaterThan_args)
        return this.equals((getUsersRichLevelGreaterThan_args)that);
      return false;
    }

    public boolean equals(getUsersRichLevelGreaterThan_args that) {
      if (that == null)
        return false;

      boolean this_present_richLevel = true;
      boolean that_present_richLevel = true;
      if (this_present_richLevel || that_present_richLevel) {
        if (!(this_present_richLevel && that_present_richLevel))
          return false;
        if (this.richLevel != that.richLevel)
          return false;
      }

      boolean this_present_pageNum = true;
      boolean that_present_pageNum = true;
      if (this_present_pageNum || that_present_pageNum) {
        if (!(this_present_pageNum && that_present_pageNum))
          return false;
        if (this.pageNum != that.pageNum)
          return false;
      }

      boolean this_present_pageSize = true;
      boolean that_present_pageSize = true;
      if (this_present_pageSize || that_present_pageSize) {
        if (!(this_present_pageSize && that_present_pageSize))
          return false;
        if (this.pageSize != that.pageSize)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_richLevel = true;
      list.add(present_richLevel);
      if (present_richLevel)
        list.add(richLevel);

      boolean present_pageNum = true;
      list.add(present_pageNum);
      if (present_pageNum)
        list.add(pageNum);

      boolean present_pageSize = true;
      list.add(present_pageSize);
      if (present_pageSize)
        list.add(pageSize);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersRichLevelGreaterThan_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRichLevel()).compareTo(other.isSetRichLevel());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRichLevel()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.richLevel, other.richLevel);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetPageNum()).compareTo(other.isSetPageNum());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetPageNum()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pageNum, other.pageNum);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetPageSize()).compareTo(other.isSetPageSize());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetPageSize()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pageSize, other.pageSize);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersRichLevelGreaterThan_args(");
      boolean first = true;

      sb.append("richLevel:");
      sb.append(this.richLevel);
      first = false;
      if (!first) sb.append(", ");
      sb.append("pageNum:");
      sb.append(this.pageNum);
      first = false;
      if (!first) sb.append(", ");
      sb.append("pageSize:");
      sb.append(this.pageSize);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersRichLevelGreaterThan_argsStandardSchemeFactory implements SchemeFactory {
      public getUsersRichLevelGreaterThan_argsStandardScheme getScheme() {
        return new getUsersRichLevelGreaterThan_argsStandardScheme();
      }
    }

    private static class getUsersRichLevelGreaterThan_argsStandardScheme extends StandardScheme<getUsersRichLevelGreaterThan_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersRichLevelGreaterThan_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // RICH_LEVEL
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.richLevel = iprot.readI32();
                struct.setRichLevelIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // PAGE_NUM
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.pageNum = iprot.readI32();
                struct.setPageNumIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // PAGE_SIZE
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.pageSize = iprot.readI32();
                struct.setPageSizeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersRichLevelGreaterThan_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(RICH_LEVEL_FIELD_DESC);
        oprot.writeI32(struct.richLevel);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(PAGE_NUM_FIELD_DESC);
        oprot.writeI32(struct.pageNum);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(PAGE_SIZE_FIELD_DESC);
        oprot.writeI32(struct.pageSize);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersRichLevelGreaterThan_argsTupleSchemeFactory implements SchemeFactory {
      public getUsersRichLevelGreaterThan_argsTupleScheme getScheme() {
        return new getUsersRichLevelGreaterThan_argsTupleScheme();
      }
    }

    private static class getUsersRichLevelGreaterThan_argsTupleScheme extends TupleScheme<getUsersRichLevelGreaterThan_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersRichLevelGreaterThan_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetRichLevel()) {
          optionals.set(0);
        }
        if (struct.isSetPageNum()) {
          optionals.set(1);
        }
        if (struct.isSetPageSize()) {
          optionals.set(2);
        }
        oprot.writeBitSet(optionals, 3);
        if (struct.isSetRichLevel()) {
          oprot.writeI32(struct.richLevel);
        }
        if (struct.isSetPageNum()) {
          oprot.writeI32(struct.pageNum);
        }
        if (struct.isSetPageSize()) {
          oprot.writeI32(struct.pageSize);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersRichLevelGreaterThan_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(3);
        if (incoming.get(0)) {
          struct.richLevel = iprot.readI32();
          struct.setRichLevelIsSet(true);
        }
        if (incoming.get(1)) {
          struct.pageNum = iprot.readI32();
          struct.setPageNumIsSet(true);
        }
        if (incoming.get(2)) {
          struct.pageSize = iprot.readI32();
          struct.setPageSizeIsSet(true);
        }
      }
    }

  }

  public static class getUsersRichLevelGreaterThan_result implements org.apache.thrift.TBase<getUsersRichLevelGreaterThan_result, getUsersRichLevelGreaterThan_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersRichLevelGreaterThan_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersRichLevelGreaterThan_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersRichLevelGreaterThan_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersRichLevelGreaterThan_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersRichLevelGreaterThan_result.class, metaDataMap);
    }

    public getUsersRichLevelGreaterThan_result() {
    }

    public getUsersRichLevelGreaterThan_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersRichLevelGreaterThan_result(getUsersRichLevelGreaterThan_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser(other.success);
      }
    }

    public getUsersRichLevelGreaterThan_result deepCopy() {
      return new getUsersRichLevelGreaterThan_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getSuccess() {
      return this.success;
    }

    public getUsersRichLevelGreaterThan_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersRichLevelGreaterThan_result)
        return this.equals((getUsersRichLevelGreaterThan_result)that);
      return false;
    }

    public boolean equals(getUsersRichLevelGreaterThan_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersRichLevelGreaterThan_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersRichLevelGreaterThan_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersRichLevelGreaterThan_resultStandardSchemeFactory implements SchemeFactory {
      public getUsersRichLevelGreaterThan_resultStandardScheme getScheme() {
        return new getUsersRichLevelGreaterThan_resultStandardScheme();
      }
    }

    private static class getUsersRichLevelGreaterThan_resultStandardScheme extends StandardScheme<getUsersRichLevelGreaterThan_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersRichLevelGreaterThan_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersRichLevelGreaterThan_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersRichLevelGreaterThan_resultTupleSchemeFactory implements SchemeFactory {
      public getUsersRichLevelGreaterThan_resultTupleScheme getScheme() {
        return new getUsersRichLevelGreaterThan_resultTupleScheme();
      }
    }

    private static class getUsersRichLevelGreaterThan_resultTupleScheme extends TupleScheme<getUsersRichLevelGreaterThan_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersRichLevelGreaterThan_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersRichLevelGreaterThan_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUsersStarLevelGreaterThan_args implements org.apache.thrift.TBase<getUsersStarLevelGreaterThan_args, getUsersStarLevelGreaterThan_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersStarLevelGreaterThan_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersStarLevelGreaterThan_args");

    private static final org.apache.thrift.protocol.TField STAR_LEVEL_FIELD_DESC = new org.apache.thrift.protocol.TField("starLevel", org.apache.thrift.protocol.TType.I32, (short)1);
    private static final org.apache.thrift.protocol.TField PAGE_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("pageNum", org.apache.thrift.protocol.TType.I32, (short)2);
    private static final org.apache.thrift.protocol.TField PAGE_SIZE_FIELD_DESC = new org.apache.thrift.protocol.TField("pageSize", org.apache.thrift.protocol.TType.I32, (short)3);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersStarLevelGreaterThan_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersStarLevelGreaterThan_argsTupleSchemeFactory());
    }

    public int starLevel; // required
    public int pageNum; // required
    public int pageSize; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      STAR_LEVEL((short)1, "starLevel"),
      PAGE_NUM((short)2, "pageNum"),
      PAGE_SIZE((short)3, "pageSize");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // STAR_LEVEL
            return STAR_LEVEL;
          case 2: // PAGE_NUM
            return PAGE_NUM;
          case 3: // PAGE_SIZE
            return PAGE_SIZE;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __STARLEVEL_ISSET_ID = 0;
    private static final int __PAGENUM_ISSET_ID = 1;
    private static final int __PAGESIZE_ISSET_ID = 2;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.STAR_LEVEL, new org.apache.thrift.meta_data.FieldMetaData("starLevel", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      tmpMap.put(_Fields.PAGE_NUM, new org.apache.thrift.meta_data.FieldMetaData("pageNum", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      tmpMap.put(_Fields.PAGE_SIZE, new org.apache.thrift.meta_data.FieldMetaData("pageSize", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersStarLevelGreaterThan_args.class, metaDataMap);
    }

    public getUsersStarLevelGreaterThan_args() {
    }

    public getUsersStarLevelGreaterThan_args(
      int starLevel,
      int pageNum,
      int pageSize)
    {
      this();
      this.starLevel = starLevel;
      setStarLevelIsSet(true);
      this.pageNum = pageNum;
      setPageNumIsSet(true);
      this.pageSize = pageSize;
      setPageSizeIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersStarLevelGreaterThan_args(getUsersStarLevelGreaterThan_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.starLevel = other.starLevel;
      this.pageNum = other.pageNum;
      this.pageSize = other.pageSize;
    }

    public getUsersStarLevelGreaterThan_args deepCopy() {
      return new getUsersStarLevelGreaterThan_args(this);
    }

    @Override
    public void clear() {
      setStarLevelIsSet(false);
      this.starLevel = 0;
      setPageNumIsSet(false);
      this.pageNum = 0;
      setPageSizeIsSet(false);
      this.pageSize = 0;
    }

    public int getStarLevel() {
      return this.starLevel;
    }

    public getUsersStarLevelGreaterThan_args setStarLevel(int starLevel) {
      this.starLevel = starLevel;
      setStarLevelIsSet(true);
      return this;
    }

    public void unsetStarLevel() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STARLEVEL_ISSET_ID);
    }

    /** Returns true if field starLevel is set (has been assigned a value) and false otherwise */
    public boolean isSetStarLevel() {
      return EncodingUtils.testBit(__isset_bitfield, __STARLEVEL_ISSET_ID);
    }

    public void setStarLevelIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STARLEVEL_ISSET_ID, value);
    }

    public int getPageNum() {
      return this.pageNum;
    }

    public getUsersStarLevelGreaterThan_args setPageNum(int pageNum) {
      this.pageNum = pageNum;
      setPageNumIsSet(true);
      return this;
    }

    public void unsetPageNum() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGENUM_ISSET_ID);
    }

    /** Returns true if field pageNum is set (has been assigned a value) and false otherwise */
    public boolean isSetPageNum() {
      return EncodingUtils.testBit(__isset_bitfield, __PAGENUM_ISSET_ID);
    }

    public void setPageNumIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGENUM_ISSET_ID, value);
    }

    public int getPageSize() {
      return this.pageSize;
    }

    public getUsersStarLevelGreaterThan_args setPageSize(int pageSize) {
      this.pageSize = pageSize;
      setPageSizeIsSet(true);
      return this;
    }

    public void unsetPageSize() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
    }

    /** Returns true if field pageSize is set (has been assigned a value) and false otherwise */
    public boolean isSetPageSize() {
      return EncodingUtils.testBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
    }

    public void setPageSizeIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGESIZE_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case STAR_LEVEL:
        if (value == null) {
          unsetStarLevel();
        } else {
          setStarLevel((Integer)value);
        }
        break;

      case PAGE_NUM:
        if (value == null) {
          unsetPageNum();
        } else {
          setPageNum((Integer)value);
        }
        break;

      case PAGE_SIZE:
        if (value == null) {
          unsetPageSize();
        } else {
          setPageSize((Integer)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case STAR_LEVEL:
        return getStarLevel();

      case PAGE_NUM:
        return getPageNum();

      case PAGE_SIZE:
        return getPageSize();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case STAR_LEVEL:
        return isSetStarLevel();
      case PAGE_NUM:
        return isSetPageNum();
      case PAGE_SIZE:
        return isSetPageSize();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersStarLevelGreaterThan_args)
        return this.equals((getUsersStarLevelGreaterThan_args)that);
      return false;
    }

    public boolean equals(getUsersStarLevelGreaterThan_args that) {
      if (that == null)
        return false;

      boolean this_present_starLevel = true;
      boolean that_present_starLevel = true;
      if (this_present_starLevel || that_present_starLevel) {
        if (!(this_present_starLevel && that_present_starLevel))
          return false;
        if (this.starLevel != that.starLevel)
          return false;
      }

      boolean this_present_pageNum = true;
      boolean that_present_pageNum = true;
      if (this_present_pageNum || that_present_pageNum) {
        if (!(this_present_pageNum && that_present_pageNum))
          return false;
        if (this.pageNum != that.pageNum)
          return false;
      }

      boolean this_present_pageSize = true;
      boolean that_present_pageSize = true;
      if (this_present_pageSize || that_present_pageSize) {
        if (!(this_present_pageSize && that_present_pageSize))
          return false;
        if (this.pageSize != that.pageSize)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_starLevel = true;
      list.add(present_starLevel);
      if (present_starLevel)
        list.add(starLevel);

      boolean present_pageNum = true;
      list.add(present_pageNum);
      if (present_pageNum)
        list.add(pageNum);

      boolean present_pageSize = true;
      list.add(present_pageSize);
      if (present_pageSize)
        list.add(pageSize);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersStarLevelGreaterThan_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetStarLevel()).compareTo(other.isSetStarLevel());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetStarLevel()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.starLevel, other.starLevel);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetPageNum()).compareTo(other.isSetPageNum());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetPageNum()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pageNum, other.pageNum);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetPageSize()).compareTo(other.isSetPageSize());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetPageSize()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pageSize, other.pageSize);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersStarLevelGreaterThan_args(");
      boolean first = true;

      sb.append("starLevel:");
      sb.append(this.starLevel);
      first = false;
      if (!first) sb.append(", ");
      sb.append("pageNum:");
      sb.append(this.pageNum);
      first = false;
      if (!first) sb.append(", ");
      sb.append("pageSize:");
      sb.append(this.pageSize);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersStarLevelGreaterThan_argsStandardSchemeFactory implements SchemeFactory {
      public getUsersStarLevelGreaterThan_argsStandardScheme getScheme() {
        return new getUsersStarLevelGreaterThan_argsStandardScheme();
      }
    }

    private static class getUsersStarLevelGreaterThan_argsStandardScheme extends StandardScheme<getUsersStarLevelGreaterThan_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersStarLevelGreaterThan_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // STAR_LEVEL
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.starLevel = iprot.readI32();
                struct.setStarLevelIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // PAGE_NUM
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.pageNum = iprot.readI32();
                struct.setPageNumIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // PAGE_SIZE
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.pageSize = iprot.readI32();
                struct.setPageSizeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersStarLevelGreaterThan_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(STAR_LEVEL_FIELD_DESC);
        oprot.writeI32(struct.starLevel);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(PAGE_NUM_FIELD_DESC);
        oprot.writeI32(struct.pageNum);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(PAGE_SIZE_FIELD_DESC);
        oprot.writeI32(struct.pageSize);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersStarLevelGreaterThan_argsTupleSchemeFactory implements SchemeFactory {
      public getUsersStarLevelGreaterThan_argsTupleScheme getScheme() {
        return new getUsersStarLevelGreaterThan_argsTupleScheme();
      }
    }

    private static class getUsersStarLevelGreaterThan_argsTupleScheme extends TupleScheme<getUsersStarLevelGreaterThan_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersStarLevelGreaterThan_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetStarLevel()) {
          optionals.set(0);
        }
        if (struct.isSetPageNum()) {
          optionals.set(1);
        }
        if (struct.isSetPageSize()) {
          optionals.set(2);
        }
        oprot.writeBitSet(optionals, 3);
        if (struct.isSetStarLevel()) {
          oprot.writeI32(struct.starLevel);
        }
        if (struct.isSetPageNum()) {
          oprot.writeI32(struct.pageNum);
        }
        if (struct.isSetPageSize()) {
          oprot.writeI32(struct.pageSize);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersStarLevelGreaterThan_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(3);
        if (incoming.get(0)) {
          struct.starLevel = iprot.readI32();
          struct.setStarLevelIsSet(true);
        }
        if (incoming.get(1)) {
          struct.pageNum = iprot.readI32();
          struct.setPageNumIsSet(true);
        }
        if (incoming.get(2)) {
          struct.pageSize = iprot.readI32();
          struct.setPageSizeIsSet(true);
        }
      }
    }

  }

  public static class getUsersStarLevelGreaterThan_result implements org.apache.thrift.TBase<getUsersStarLevelGreaterThan_result, getUsersStarLevelGreaterThan_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersStarLevelGreaterThan_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersStarLevelGreaterThan_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersStarLevelGreaterThan_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersStarLevelGreaterThan_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersStarLevelGreaterThan_result.class, metaDataMap);
    }

    public getUsersStarLevelGreaterThan_result() {
    }

    public getUsersStarLevelGreaterThan_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersStarLevelGreaterThan_result(getUsersStarLevelGreaterThan_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser(other.success);
      }
    }

    public getUsersStarLevelGreaterThan_result deepCopy() {
      return new getUsersStarLevelGreaterThan_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getSuccess() {
      return this.success;
    }

    public getUsersStarLevelGreaterThan_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersStarLevelGreaterThan_result)
        return this.equals((getUsersStarLevelGreaterThan_result)that);
      return false;
    }

    public boolean equals(getUsersStarLevelGreaterThan_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersStarLevelGreaterThan_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersStarLevelGreaterThan_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersStarLevelGreaterThan_resultStandardSchemeFactory implements SchemeFactory {
      public getUsersStarLevelGreaterThan_resultStandardScheme getScheme() {
        return new getUsersStarLevelGreaterThan_resultStandardScheme();
      }
    }

    private static class getUsersStarLevelGreaterThan_resultStandardScheme extends StandardScheme<getUsersStarLevelGreaterThan_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersStarLevelGreaterThan_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersStarLevelGreaterThan_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersStarLevelGreaterThan_resultTupleSchemeFactory implements SchemeFactory {
      public getUsersStarLevelGreaterThan_resultTupleScheme getScheme() {
        return new getUsersStarLevelGreaterThan_resultTupleScheme();
      }
    }

    private static class getUsersStarLevelGreaterThan_resultTupleScheme extends TupleScheme<getUsersStarLevelGreaterThan_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersStarLevelGreaterThan_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersStarLevelGreaterThan_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUsersByKugouIdsAndNickNameLike_args implements org.apache.thrift.TBase<getUsersByKugouIdsAndNickNameLike_args, getUsersByKugouIdsAndNickNameLike_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersByKugouIdsAndNickNameLike_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersByKugouIdsAndNickNameLike_args");

    private static final org.apache.thrift.protocol.TField KUGOU_IDS_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouIds", org.apache.thrift.protocol.TType.LIST, (short)1);
    private static final org.apache.thrift.protocol.TField NICK_NAME_LIKE_FIELD_DESC = new org.apache.thrift.protocol.TField("nickNameLike", org.apache.thrift.protocol.TType.STRING, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersByKugouIdsAndNickNameLike_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersByKugouIdsAndNickNameLike_argsTupleSchemeFactory());
    }

    public List<Long> kugouIds; // required
    public String nickNameLike; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_IDS((short)1, "kugouIds"),
      NICK_NAME_LIKE((short)2, "nickNameLike");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_IDS
            return KUGOU_IDS;
          case 2: // NICK_NAME_LIKE
            return NICK_NAME_LIKE;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_IDS, new org.apache.thrift.meta_data.FieldMetaData("kugouIds", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
              new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
      tmpMap.put(_Fields.NICK_NAME_LIKE, new org.apache.thrift.meta_data.FieldMetaData("nickNameLike", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersByKugouIdsAndNickNameLike_args.class, metaDataMap);
    }

    public getUsersByKugouIdsAndNickNameLike_args() {
    }

    public getUsersByKugouIdsAndNickNameLike_args(
      List<Long> kugouIds,
      String nickNameLike)
    {
      this();
      this.kugouIds = kugouIds;
      this.nickNameLike = nickNameLike;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersByKugouIdsAndNickNameLike_args(getUsersByKugouIdsAndNickNameLike_args other) {
      if (other.isSetKugouIds()) {
        List<Long> __this__kugouIds = new ArrayList<Long>(other.kugouIds);
        this.kugouIds = __this__kugouIds;
      }
      if (other.isSetNickNameLike()) {
        this.nickNameLike = other.nickNameLike;
      }
    }

    public getUsersByKugouIdsAndNickNameLike_args deepCopy() {
      return new getUsersByKugouIdsAndNickNameLike_args(this);
    }

    @Override
    public void clear() {
      this.kugouIds = null;
      this.nickNameLike = null;
    }

    public int getKugouIdsSize() {
      return (this.kugouIds == null) ? 0 : this.kugouIds.size();
    }

    public java.util.Iterator<Long> getKugouIdsIterator() {
      return (this.kugouIds == null) ? null : this.kugouIds.iterator();
    }

    public void addToKugouIds(long elem) {
      if (this.kugouIds == null) {
        this.kugouIds = new ArrayList<Long>();
      }
      this.kugouIds.add(elem);
    }

    public List<Long> getKugouIds() {
      return this.kugouIds;
    }

    public getUsersByKugouIdsAndNickNameLike_args setKugouIds(List<Long> kugouIds) {
      this.kugouIds = kugouIds;
      return this;
    }

    public void unsetKugouIds() {
      this.kugouIds = null;
    }

    /** Returns true if field kugouIds is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouIds() {
      return this.kugouIds != null;
    }

    public void setKugouIdsIsSet(boolean value) {
      if (!value) {
        this.kugouIds = null;
      }
    }

    public String getNickNameLike() {
      return this.nickNameLike;
    }

    public getUsersByKugouIdsAndNickNameLike_args setNickNameLike(String nickNameLike) {
      this.nickNameLike = nickNameLike;
      return this;
    }

    public void unsetNickNameLike() {
      this.nickNameLike = null;
    }

    /** Returns true if field nickNameLike is set (has been assigned a value) and false otherwise */
    public boolean isSetNickNameLike() {
      return this.nickNameLike != null;
    }

    public void setNickNameLikeIsSet(boolean value) {
      if (!value) {
        this.nickNameLike = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_IDS:
        if (value == null) {
          unsetKugouIds();
        } else {
          setKugouIds((List<Long>)value);
        }
        break;

      case NICK_NAME_LIKE:
        if (value == null) {
          unsetNickNameLike();
        } else {
          setNickNameLike((String)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_IDS:
        return getKugouIds();

      case NICK_NAME_LIKE:
        return getNickNameLike();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_IDS:
        return isSetKugouIds();
      case NICK_NAME_LIKE:
        return isSetNickNameLike();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersByKugouIdsAndNickNameLike_args)
        return this.equals((getUsersByKugouIdsAndNickNameLike_args)that);
      return false;
    }

    public boolean equals(getUsersByKugouIdsAndNickNameLike_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouIds = true && this.isSetKugouIds();
      boolean that_present_kugouIds = true && that.isSetKugouIds();
      if (this_present_kugouIds || that_present_kugouIds) {
        if (!(this_present_kugouIds && that_present_kugouIds))
          return false;
        if (!this.kugouIds.equals(that.kugouIds))
          return false;
      }

      boolean this_present_nickNameLike = true && this.isSetNickNameLike();
      boolean that_present_nickNameLike = true && that.isSetNickNameLike();
      if (this_present_nickNameLike || that_present_nickNameLike) {
        if (!(this_present_nickNameLike && that_present_nickNameLike))
          return false;
        if (!this.nickNameLike.equals(that.nickNameLike))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouIds = true && (isSetKugouIds());
      list.add(present_kugouIds);
      if (present_kugouIds)
        list.add(kugouIds);

      boolean present_nickNameLike = true && (isSetNickNameLike());
      list.add(present_nickNameLike);
      if (present_nickNameLike)
        list.add(nickNameLike);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersByKugouIdsAndNickNameLike_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouIds()).compareTo(other.isSetKugouIds());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouIds()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouIds, other.kugouIds);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetNickNameLike()).compareTo(other.isSetNickNameLike());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetNickNameLike()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.nickNameLike, other.nickNameLike);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersByKugouIdsAndNickNameLike_args(");
      boolean first = true;

      sb.append("kugouIds:");
      if (this.kugouIds == null) {
        sb.append("null");
      } else {
        sb.append(this.kugouIds);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("nickNameLike:");
      if (this.nickNameLike == null) {
        sb.append("null");
      } else {
        sb.append(this.nickNameLike);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersByKugouIdsAndNickNameLike_argsStandardSchemeFactory implements SchemeFactory {
      public getUsersByKugouIdsAndNickNameLike_argsStandardScheme getScheme() {
        return new getUsersByKugouIdsAndNickNameLike_argsStandardScheme();
      }
    }

    private static class getUsersByKugouIdsAndNickNameLike_argsStandardScheme extends StandardScheme<getUsersByKugouIdsAndNickNameLike_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersByKugouIdsAndNickNameLike_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_IDS
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list32 = iprot.readListBegin();
                  struct.kugouIds = new ArrayList<Long>(_list32.size);
                  long _elem33;
                  for (int _i34 = 0; _i34 < _list32.size; ++_i34)
                  {
                    _elem33 = iprot.readI64();
                    struct.kugouIds.add(_elem33);
                  }
                  iprot.readListEnd();
                }
                struct.setKugouIdsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // NICK_NAME_LIKE
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.nickNameLike = iprot.readString();
                struct.setNickNameLikeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersByKugouIdsAndNickNameLike_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.kugouIds != null) {
          oprot.writeFieldBegin(KUGOU_IDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.kugouIds.size()));
            for (long _iter35 : struct.kugouIds)
            {
              oprot.writeI64(_iter35);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        if (struct.nickNameLike != null) {
          oprot.writeFieldBegin(NICK_NAME_LIKE_FIELD_DESC);
          oprot.writeString(struct.nickNameLike);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersByKugouIdsAndNickNameLike_argsTupleSchemeFactory implements SchemeFactory {
      public getUsersByKugouIdsAndNickNameLike_argsTupleScheme getScheme() {
        return new getUsersByKugouIdsAndNickNameLike_argsTupleScheme();
      }
    }

    private static class getUsersByKugouIdsAndNickNameLike_argsTupleScheme extends TupleScheme<getUsersByKugouIdsAndNickNameLike_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersByKugouIdsAndNickNameLike_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouIds()) {
          optionals.set(0);
        }
        if (struct.isSetNickNameLike()) {
          optionals.set(1);
        }
        oprot.writeBitSet(optionals, 2);
        if (struct.isSetKugouIds()) {
          {
            oprot.writeI32(struct.kugouIds.size());
            for (long _iter36 : struct.kugouIds)
            {
              oprot.writeI64(_iter36);
            }
          }
        }
        if (struct.isSetNickNameLike()) {
          oprot.writeString(struct.nickNameLike);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersByKugouIdsAndNickNameLike_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(2);
        if (incoming.get(0)) {
          {
            org.apache.thrift.protocol.TList _list37 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
            struct.kugouIds = new ArrayList<Long>(_list37.size);
            long _elem38;
            for (int _i39 = 0; _i39 < _list37.size; ++_i39)
            {
              _elem38 = iprot.readI64();
              struct.kugouIds.add(_elem38);
            }
          }
          struct.setKugouIdsIsSet(true);
        }
        if (incoming.get(1)) {
          struct.nickNameLike = iprot.readString();
          struct.setNickNameLikeIsSet(true);
        }
      }
    }

  }

  public static class getUsersByKugouIdsAndNickNameLike_result implements org.apache.thrift.TBase<getUsersByKugouIdsAndNickNameLike_result, getUsersByKugouIdsAndNickNameLike_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersByKugouIdsAndNickNameLike_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersByKugouIdsAndNickNameLike_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersByKugouIdsAndNickNameLike_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersByKugouIdsAndNickNameLike_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersByKugouIdsAndNickNameLike_result.class, metaDataMap);
    }

    public getUsersByKugouIdsAndNickNameLike_result() {
    }

    public getUsersByKugouIdsAndNickNameLike_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersByKugouIdsAndNickNameLike_result(getUsersByKugouIdsAndNickNameLike_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser(other.success);
      }
    }

    public getUsersByKugouIdsAndNickNameLike_result deepCopy() {
      return new getUsersByKugouIdsAndNickNameLike_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getSuccess() {
      return this.success;
    }

    public getUsersByKugouIdsAndNickNameLike_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersByKugouIdsAndNickNameLike_result)
        return this.equals((getUsersByKugouIdsAndNickNameLike_result)that);
      return false;
    }

    public boolean equals(getUsersByKugouIdsAndNickNameLike_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersByKugouIdsAndNickNameLike_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersByKugouIdsAndNickNameLike_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersByKugouIdsAndNickNameLike_resultStandardSchemeFactory implements SchemeFactory {
      public getUsersByKugouIdsAndNickNameLike_resultStandardScheme getScheme() {
        return new getUsersByKugouIdsAndNickNameLike_resultStandardScheme();
      }
    }

    private static class getUsersByKugouIdsAndNickNameLike_resultStandardScheme extends StandardScheme<getUsersByKugouIdsAndNickNameLike_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersByKugouIdsAndNickNameLike_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersByKugouIdsAndNickNameLike_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersByKugouIdsAndNickNameLike_resultTupleSchemeFactory implements SchemeFactory {
      public getUsersByKugouIdsAndNickNameLike_resultTupleScheme getScheme() {
        return new getUsersByKugouIdsAndNickNameLike_resultTupleScheme();
      }
    }

    private static class getUsersByKugouIdsAndNickNameLike_resultTupleScheme extends TupleScheme<getUsersByKugouIdsAndNickNameLike_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersByKugouIdsAndNickNameLike_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersByKugouIdsAndNickNameLike_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUsersByKugouIdsAndStarLevelGreaterThan_args implements org.apache.thrift.TBase<getUsersByKugouIdsAndStarLevelGreaterThan_args, getUsersByKugouIdsAndStarLevelGreaterThan_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersByKugouIdsAndStarLevelGreaterThan_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersByKugouIdsAndStarLevelGreaterThan_args");

    private static final org.apache.thrift.protocol.TField KUGOU_IDS_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouIds", org.apache.thrift.protocol.TType.LIST, (short)1);
    private static final org.apache.thrift.protocol.TField STAR_LEVEL_FIELD_DESC = new org.apache.thrift.protocol.TField("starLevel", org.apache.thrift.protocol.TType.I32, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersByKugouIdsAndStarLevelGreaterThan_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersByKugouIdsAndStarLevelGreaterThan_argsTupleSchemeFactory());
    }

    public List<Long> kugouIds; // required
    public int starLevel; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_IDS((short)1, "kugouIds"),
      STAR_LEVEL((short)2, "starLevel");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_IDS
            return KUGOU_IDS;
          case 2: // STAR_LEVEL
            return STAR_LEVEL;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __STARLEVEL_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_IDS, new org.apache.thrift.meta_data.FieldMetaData("kugouIds", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
              new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
      tmpMap.put(_Fields.STAR_LEVEL, new org.apache.thrift.meta_data.FieldMetaData("starLevel", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersByKugouIdsAndStarLevelGreaterThan_args.class, metaDataMap);
    }

    public getUsersByKugouIdsAndStarLevelGreaterThan_args() {
    }

    public getUsersByKugouIdsAndStarLevelGreaterThan_args(
      List<Long> kugouIds,
      int starLevel)
    {
      this();
      this.kugouIds = kugouIds;
      this.starLevel = starLevel;
      setStarLevelIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersByKugouIdsAndStarLevelGreaterThan_args(getUsersByKugouIdsAndStarLevelGreaterThan_args other) {
      __isset_bitfield = other.__isset_bitfield;
      if (other.isSetKugouIds()) {
        List<Long> __this__kugouIds = new ArrayList<Long>(other.kugouIds);
        this.kugouIds = __this__kugouIds;
      }
      this.starLevel = other.starLevel;
    }

    public getUsersByKugouIdsAndStarLevelGreaterThan_args deepCopy() {
      return new getUsersByKugouIdsAndStarLevelGreaterThan_args(this);
    }

    @Override
    public void clear() {
      this.kugouIds = null;
      setStarLevelIsSet(false);
      this.starLevel = 0;
    }

    public int getKugouIdsSize() {
      return (this.kugouIds == null) ? 0 : this.kugouIds.size();
    }

    public java.util.Iterator<Long> getKugouIdsIterator() {
      return (this.kugouIds == null) ? null : this.kugouIds.iterator();
    }

    public void addToKugouIds(long elem) {
      if (this.kugouIds == null) {
        this.kugouIds = new ArrayList<Long>();
      }
      this.kugouIds.add(elem);
    }

    public List<Long> getKugouIds() {
      return this.kugouIds;
    }

    public getUsersByKugouIdsAndStarLevelGreaterThan_args setKugouIds(List<Long> kugouIds) {
      this.kugouIds = kugouIds;
      return this;
    }

    public void unsetKugouIds() {
      this.kugouIds = null;
    }

    /** Returns true if field kugouIds is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouIds() {
      return this.kugouIds != null;
    }

    public void setKugouIdsIsSet(boolean value) {
      if (!value) {
        this.kugouIds = null;
      }
    }

    public int getStarLevel() {
      return this.starLevel;
    }

    public getUsersByKugouIdsAndStarLevelGreaterThan_args setStarLevel(int starLevel) {
      this.starLevel = starLevel;
      setStarLevelIsSet(true);
      return this;
    }

    public void unsetStarLevel() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STARLEVEL_ISSET_ID);
    }

    /** Returns true if field starLevel is set (has been assigned a value) and false otherwise */
    public boolean isSetStarLevel() {
      return EncodingUtils.testBit(__isset_bitfield, __STARLEVEL_ISSET_ID);
    }

    public void setStarLevelIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STARLEVEL_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_IDS:
        if (value == null) {
          unsetKugouIds();
        } else {
          setKugouIds((List<Long>)value);
        }
        break;

      case STAR_LEVEL:
        if (value == null) {
          unsetStarLevel();
        } else {
          setStarLevel((Integer)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_IDS:
        return getKugouIds();

      case STAR_LEVEL:
        return getStarLevel();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_IDS:
        return isSetKugouIds();
      case STAR_LEVEL:
        return isSetStarLevel();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersByKugouIdsAndStarLevelGreaterThan_args)
        return this.equals((getUsersByKugouIdsAndStarLevelGreaterThan_args)that);
      return false;
    }

    public boolean equals(getUsersByKugouIdsAndStarLevelGreaterThan_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouIds = true && this.isSetKugouIds();
      boolean that_present_kugouIds = true && that.isSetKugouIds();
      if (this_present_kugouIds || that_present_kugouIds) {
        if (!(this_present_kugouIds && that_present_kugouIds))
          return false;
        if (!this.kugouIds.equals(that.kugouIds))
          return false;
      }

      boolean this_present_starLevel = true;
      boolean that_present_starLevel = true;
      if (this_present_starLevel || that_present_starLevel) {
        if (!(this_present_starLevel && that_present_starLevel))
          return false;
        if (this.starLevel != that.starLevel)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouIds = true && (isSetKugouIds());
      list.add(present_kugouIds);
      if (present_kugouIds)
        list.add(kugouIds);

      boolean present_starLevel = true;
      list.add(present_starLevel);
      if (present_starLevel)
        list.add(starLevel);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersByKugouIdsAndStarLevelGreaterThan_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouIds()).compareTo(other.isSetKugouIds());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouIds()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouIds, other.kugouIds);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetStarLevel()).compareTo(other.isSetStarLevel());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetStarLevel()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.starLevel, other.starLevel);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersByKugouIdsAndStarLevelGreaterThan_args(");
      boolean first = true;

      sb.append("kugouIds:");
      if (this.kugouIds == null) {
        sb.append("null");
      } else {
        sb.append(this.kugouIds);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("starLevel:");
      sb.append(this.starLevel);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersByKugouIdsAndStarLevelGreaterThan_argsStandardSchemeFactory implements SchemeFactory {
      public getUsersByKugouIdsAndStarLevelGreaterThan_argsStandardScheme getScheme() {
        return new getUsersByKugouIdsAndStarLevelGreaterThan_argsStandardScheme();
      }
    }

    private static class getUsersByKugouIdsAndStarLevelGreaterThan_argsStandardScheme extends StandardScheme<getUsersByKugouIdsAndStarLevelGreaterThan_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersByKugouIdsAndStarLevelGreaterThan_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_IDS
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list40 = iprot.readListBegin();
                  struct.kugouIds = new ArrayList<Long>(_list40.size);
                  long _elem41;
                  for (int _i42 = 0; _i42 < _list40.size; ++_i42)
                  {
                    _elem41 = iprot.readI64();
                    struct.kugouIds.add(_elem41);
                  }
                  iprot.readListEnd();
                }
                struct.setKugouIdsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // STAR_LEVEL
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.starLevel = iprot.readI32();
                struct.setStarLevelIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersByKugouIdsAndStarLevelGreaterThan_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.kugouIds != null) {
          oprot.writeFieldBegin(KUGOU_IDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.kugouIds.size()));
            for (long _iter43 : struct.kugouIds)
            {
              oprot.writeI64(_iter43);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        oprot.writeFieldBegin(STAR_LEVEL_FIELD_DESC);
        oprot.writeI32(struct.starLevel);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersByKugouIdsAndStarLevelGreaterThan_argsTupleSchemeFactory implements SchemeFactory {
      public getUsersByKugouIdsAndStarLevelGreaterThan_argsTupleScheme getScheme() {
        return new getUsersByKugouIdsAndStarLevelGreaterThan_argsTupleScheme();
      }
    }

    private static class getUsersByKugouIdsAndStarLevelGreaterThan_argsTupleScheme extends TupleScheme<getUsersByKugouIdsAndStarLevelGreaterThan_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersByKugouIdsAndStarLevelGreaterThan_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouIds()) {
          optionals.set(0);
        }
        if (struct.isSetStarLevel()) {
          optionals.set(1);
        }
        oprot.writeBitSet(optionals, 2);
        if (struct.isSetKugouIds()) {
          {
            oprot.writeI32(struct.kugouIds.size());
            for (long _iter44 : struct.kugouIds)
            {
              oprot.writeI64(_iter44);
            }
          }
        }
        if (struct.isSetStarLevel()) {
          oprot.writeI32(struct.starLevel);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersByKugouIdsAndStarLevelGreaterThan_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(2);
        if (incoming.get(0)) {
          {
            org.apache.thrift.protocol.TList _list45 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
            struct.kugouIds = new ArrayList<Long>(_list45.size);
            long _elem46;
            for (int _i47 = 0; _i47 < _list45.size; ++_i47)
            {
              _elem46 = iprot.readI64();
              struct.kugouIds.add(_elem46);
            }
          }
          struct.setKugouIdsIsSet(true);
        }
        if (incoming.get(1)) {
          struct.starLevel = iprot.readI32();
          struct.setStarLevelIsSet(true);
        }
      }
    }

  }

  public static class getUsersByKugouIdsAndStarLevelGreaterThan_result implements org.apache.thrift.TBase<getUsersByKugouIdsAndStarLevelGreaterThan_result, getUsersByKugouIdsAndStarLevelGreaterThan_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersByKugouIdsAndStarLevelGreaterThan_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersByKugouIdsAndStarLevelGreaterThan_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersByKugouIdsAndStarLevelGreaterThan_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersByKugouIdsAndStarLevelGreaterThan_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersByKugouIdsAndStarLevelGreaterThan_result.class, metaDataMap);
    }

    public getUsersByKugouIdsAndStarLevelGreaterThan_result() {
    }

    public getUsersByKugouIdsAndStarLevelGreaterThan_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersByKugouIdsAndStarLevelGreaterThan_result(getUsersByKugouIdsAndStarLevelGreaterThan_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg(other.success);
      }
    }

    public getUsersByKugouIdsAndStarLevelGreaterThan_result deepCopy() {
      return new getUsersByKugouIdsAndStarLevelGreaterThan_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg getSuccess() {
      return this.success;
    }

    public getUsersByKugouIdsAndStarLevelGreaterThan_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersByKugouIdsAndStarLevelGreaterThan_result)
        return this.equals((getUsersByKugouIdsAndStarLevelGreaterThan_result)that);
      return false;
    }

    public boolean equals(getUsersByKugouIdsAndStarLevelGreaterThan_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersByKugouIdsAndStarLevelGreaterThan_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersByKugouIdsAndStarLevelGreaterThan_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersByKugouIdsAndStarLevelGreaterThan_resultStandardSchemeFactory implements SchemeFactory {
      public getUsersByKugouIdsAndStarLevelGreaterThan_resultStandardScheme getScheme() {
        return new getUsersByKugouIdsAndStarLevelGreaterThan_resultStandardScheme();
      }
    }

    private static class getUsersByKugouIdsAndStarLevelGreaterThan_resultStandardScheme extends StandardScheme<getUsersByKugouIdsAndStarLevelGreaterThan_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersByKugouIdsAndStarLevelGreaterThan_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersByKugouIdsAndStarLevelGreaterThan_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersByKugouIdsAndStarLevelGreaterThan_resultTupleSchemeFactory implements SchemeFactory {
      public getUsersByKugouIdsAndStarLevelGreaterThan_resultTupleScheme getScheme() {
        return new getUsersByKugouIdsAndStarLevelGreaterThan_resultTupleScheme();
      }
    }

    private static class getUsersByKugouIdsAndStarLevelGreaterThan_resultTupleScheme extends TupleScheme<getUsersByKugouIdsAndStarLevelGreaterThan_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersByKugouIdsAndStarLevelGreaterThan_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersByKugouIdsAndStarLevelGreaterThan_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResLongListMsg();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUsersByAddTime_args implements org.apache.thrift.TBase<getUsersByAddTime_args, getUsersByAddTime_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersByAddTime_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersByAddTime_args");

    private static final org.apache.thrift.protocol.TField START_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("startTime", org.apache.thrift.protocol.TType.I32, (short)1);
    private static final org.apache.thrift.protocol.TField END_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("endTime", org.apache.thrift.protocol.TType.I32, (short)2);
    private static final org.apache.thrift.protocol.TField PAGE_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("pageNum", org.apache.thrift.protocol.TType.I32, (short)3);
    private static final org.apache.thrift.protocol.TField PAGE_SIZE_FIELD_DESC = new org.apache.thrift.protocol.TField("pageSize", org.apache.thrift.protocol.TType.I32, (short)4);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersByAddTime_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersByAddTime_argsTupleSchemeFactory());
    }

    public int startTime; // required
    public int endTime; // required
    public int pageNum; // required
    public int pageSize; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      START_TIME((short)1, "startTime"),
      END_TIME((short)2, "endTime"),
      PAGE_NUM((short)3, "pageNum"),
      PAGE_SIZE((short)4, "pageSize");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // START_TIME
            return START_TIME;
          case 2: // END_TIME
            return END_TIME;
          case 3: // PAGE_NUM
            return PAGE_NUM;
          case 4: // PAGE_SIZE
            return PAGE_SIZE;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __STARTTIME_ISSET_ID = 0;
    private static final int __ENDTIME_ISSET_ID = 1;
    private static final int __PAGENUM_ISSET_ID = 2;
    private static final int __PAGESIZE_ISSET_ID = 3;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.START_TIME, new org.apache.thrift.meta_data.FieldMetaData("startTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      tmpMap.put(_Fields.END_TIME, new org.apache.thrift.meta_data.FieldMetaData("endTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      tmpMap.put(_Fields.PAGE_NUM, new org.apache.thrift.meta_data.FieldMetaData("pageNum", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      tmpMap.put(_Fields.PAGE_SIZE, new org.apache.thrift.meta_data.FieldMetaData("pageSize", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersByAddTime_args.class, metaDataMap);
    }

    public getUsersByAddTime_args() {
    }

    public getUsersByAddTime_args(
      int startTime,
      int endTime,
      int pageNum,
      int pageSize)
    {
      this();
      this.startTime = startTime;
      setStartTimeIsSet(true);
      this.endTime = endTime;
      setEndTimeIsSet(true);
      this.pageNum = pageNum;
      setPageNumIsSet(true);
      this.pageSize = pageSize;
      setPageSizeIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersByAddTime_args(getUsersByAddTime_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.startTime = other.startTime;
      this.endTime = other.endTime;
      this.pageNum = other.pageNum;
      this.pageSize = other.pageSize;
    }

    public getUsersByAddTime_args deepCopy() {
      return new getUsersByAddTime_args(this);
    }

    @Override
    public void clear() {
      setStartTimeIsSet(false);
      this.startTime = 0;
      setEndTimeIsSet(false);
      this.endTime = 0;
      setPageNumIsSet(false);
      this.pageNum = 0;
      setPageSizeIsSet(false);
      this.pageSize = 0;
    }

    public int getStartTime() {
      return this.startTime;
    }

    public getUsersByAddTime_args setStartTime(int startTime) {
      this.startTime = startTime;
      setStartTimeIsSet(true);
      return this;
    }

    public void unsetStartTime() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STARTTIME_ISSET_ID);
    }

    /** Returns true if field startTime is set (has been assigned a value) and false otherwise */
    public boolean isSetStartTime() {
      return EncodingUtils.testBit(__isset_bitfield, __STARTTIME_ISSET_ID);
    }

    public void setStartTimeIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STARTTIME_ISSET_ID, value);
    }

    public int getEndTime() {
      return this.endTime;
    }

    public getUsersByAddTime_args setEndTime(int endTime) {
      this.endTime = endTime;
      setEndTimeIsSet(true);
      return this;
    }

    public void unsetEndTime() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ENDTIME_ISSET_ID);
    }

    /** Returns true if field endTime is set (has been assigned a value) and false otherwise */
    public boolean isSetEndTime() {
      return EncodingUtils.testBit(__isset_bitfield, __ENDTIME_ISSET_ID);
    }

    public void setEndTimeIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ENDTIME_ISSET_ID, value);
    }

    public int getPageNum() {
      return this.pageNum;
    }

    public getUsersByAddTime_args setPageNum(int pageNum) {
      this.pageNum = pageNum;
      setPageNumIsSet(true);
      return this;
    }

    public void unsetPageNum() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGENUM_ISSET_ID);
    }

    /** Returns true if field pageNum is set (has been assigned a value) and false otherwise */
    public boolean isSetPageNum() {
      return EncodingUtils.testBit(__isset_bitfield, __PAGENUM_ISSET_ID);
    }

    public void setPageNumIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGENUM_ISSET_ID, value);
    }

    public int getPageSize() {
      return this.pageSize;
    }

    public getUsersByAddTime_args setPageSize(int pageSize) {
      this.pageSize = pageSize;
      setPageSizeIsSet(true);
      return this;
    }

    public void unsetPageSize() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
    }

    /** Returns true if field pageSize is set (has been assigned a value) and false otherwise */
    public boolean isSetPageSize() {
      return EncodingUtils.testBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
    }

    public void setPageSizeIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGESIZE_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case START_TIME:
        if (value == null) {
          unsetStartTime();
        } else {
          setStartTime((Integer)value);
        }
        break;

      case END_TIME:
        if (value == null) {
          unsetEndTime();
        } else {
          setEndTime((Integer)value);
        }
        break;

      case PAGE_NUM:
        if (value == null) {
          unsetPageNum();
        } else {
          setPageNum((Integer)value);
        }
        break;

      case PAGE_SIZE:
        if (value == null) {
          unsetPageSize();
        } else {
          setPageSize((Integer)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case START_TIME:
        return getStartTime();

      case END_TIME:
        return getEndTime();

      case PAGE_NUM:
        return getPageNum();

      case PAGE_SIZE:
        return getPageSize();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case START_TIME:
        return isSetStartTime();
      case END_TIME:
        return isSetEndTime();
      case PAGE_NUM:
        return isSetPageNum();
      case PAGE_SIZE:
        return isSetPageSize();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersByAddTime_args)
        return this.equals((getUsersByAddTime_args)that);
      return false;
    }

    public boolean equals(getUsersByAddTime_args that) {
      if (that == null)
        return false;

      boolean this_present_startTime = true;
      boolean that_present_startTime = true;
      if (this_present_startTime || that_present_startTime) {
        if (!(this_present_startTime && that_present_startTime))
          return false;
        if (this.startTime != that.startTime)
          return false;
      }

      boolean this_present_endTime = true;
      boolean that_present_endTime = true;
      if (this_present_endTime || that_present_endTime) {
        if (!(this_present_endTime && that_present_endTime))
          return false;
        if (this.endTime != that.endTime)
          return false;
      }

      boolean this_present_pageNum = true;
      boolean that_present_pageNum = true;
      if (this_present_pageNum || that_present_pageNum) {
        if (!(this_present_pageNum && that_present_pageNum))
          return false;
        if (this.pageNum != that.pageNum)
          return false;
      }

      boolean this_present_pageSize = true;
      boolean that_present_pageSize = true;
      if (this_present_pageSize || that_present_pageSize) {
        if (!(this_present_pageSize && that_present_pageSize))
          return false;
        if (this.pageSize != that.pageSize)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_startTime = true;
      list.add(present_startTime);
      if (present_startTime)
        list.add(startTime);

      boolean present_endTime = true;
      list.add(present_endTime);
      if (present_endTime)
        list.add(endTime);

      boolean present_pageNum = true;
      list.add(present_pageNum);
      if (present_pageNum)
        list.add(pageNum);

      boolean present_pageSize = true;
      list.add(present_pageSize);
      if (present_pageSize)
        list.add(pageSize);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersByAddTime_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetStartTime()).compareTo(other.isSetStartTime());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetStartTime()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.startTime, other.startTime);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetEndTime()).compareTo(other.isSetEndTime());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetEndTime()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.endTime, other.endTime);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetPageNum()).compareTo(other.isSetPageNum());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetPageNum()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pageNum, other.pageNum);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetPageSize()).compareTo(other.isSetPageSize());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetPageSize()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pageSize, other.pageSize);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersByAddTime_args(");
      boolean first = true;

      sb.append("startTime:");
      sb.append(this.startTime);
      first = false;
      if (!first) sb.append(", ");
      sb.append("endTime:");
      sb.append(this.endTime);
      first = false;
      if (!first) sb.append(", ");
      sb.append("pageNum:");
      sb.append(this.pageNum);
      first = false;
      if (!first) sb.append(", ");
      sb.append("pageSize:");
      sb.append(this.pageSize);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersByAddTime_argsStandardSchemeFactory implements SchemeFactory {
      public getUsersByAddTime_argsStandardScheme getScheme() {
        return new getUsersByAddTime_argsStandardScheme();
      }
    }

    private static class getUsersByAddTime_argsStandardScheme extends StandardScheme<getUsersByAddTime_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersByAddTime_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // START_TIME
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.startTime = iprot.readI32();
                struct.setStartTimeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // END_TIME
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.endTime = iprot.readI32();
                struct.setEndTimeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // PAGE_NUM
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.pageNum = iprot.readI32();
                struct.setPageNumIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 4: // PAGE_SIZE
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.pageSize = iprot.readI32();
                struct.setPageSizeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersByAddTime_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(START_TIME_FIELD_DESC);
        oprot.writeI32(struct.startTime);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(END_TIME_FIELD_DESC);
        oprot.writeI32(struct.endTime);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(PAGE_NUM_FIELD_DESC);
        oprot.writeI32(struct.pageNum);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(PAGE_SIZE_FIELD_DESC);
        oprot.writeI32(struct.pageSize);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersByAddTime_argsTupleSchemeFactory implements SchemeFactory {
      public getUsersByAddTime_argsTupleScheme getScheme() {
        return new getUsersByAddTime_argsTupleScheme();
      }
    }

    private static class getUsersByAddTime_argsTupleScheme extends TupleScheme<getUsersByAddTime_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersByAddTime_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetStartTime()) {
          optionals.set(0);
        }
        if (struct.isSetEndTime()) {
          optionals.set(1);
        }
        if (struct.isSetPageNum()) {
          optionals.set(2);
        }
        if (struct.isSetPageSize()) {
          optionals.set(3);
        }
        oprot.writeBitSet(optionals, 4);
        if (struct.isSetStartTime()) {
          oprot.writeI32(struct.startTime);
        }
        if (struct.isSetEndTime()) {
          oprot.writeI32(struct.endTime);
        }
        if (struct.isSetPageNum()) {
          oprot.writeI32(struct.pageNum);
        }
        if (struct.isSetPageSize()) {
          oprot.writeI32(struct.pageSize);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersByAddTime_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(4);
        if (incoming.get(0)) {
          struct.startTime = iprot.readI32();
          struct.setStartTimeIsSet(true);
        }
        if (incoming.get(1)) {
          struct.endTime = iprot.readI32();
          struct.setEndTimeIsSet(true);
        }
        if (incoming.get(2)) {
          struct.pageNum = iprot.readI32();
          struct.setPageNumIsSet(true);
        }
        if (incoming.get(3)) {
          struct.pageSize = iprot.readI32();
          struct.setPageSizeIsSet(true);
        }
      }
    }

  }

  public static class getUsersByAddTime_result implements org.apache.thrift.TBase<getUsersByAddTime_result, getUsersByAddTime_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUsersByAddTime_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUsersByAddTime_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUsersByAddTime_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUsersByAddTime_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUsersByAddTime_result.class, metaDataMap);
    }

    public getUsersByAddTime_result() {
    }

    public getUsersByAddTime_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUsersByAddTime_result(getUsersByAddTime_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser(other.success);
      }
    }

    public getUsersByAddTime_result deepCopy() {
      return new getUsersByAddTime_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser getSuccess() {
      return this.success;
    }

    public getUsersByAddTime_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUsersByAddTime_result)
        return this.equals((getUsersByAddTime_result)that);
      return false;
    }

    public boolean equals(getUsersByAddTime_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUsersByAddTime_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUsersByAddTime_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUsersByAddTime_resultStandardSchemeFactory implements SchemeFactory {
      public getUsersByAddTime_resultStandardScheme getScheme() {
        return new getUsersByAddTime_resultStandardScheme();
      }
    }

    private static class getUsersByAddTime_resultStandardScheme extends StandardScheme<getUsersByAddTime_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUsersByAddTime_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUsersByAddTime_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUsersByAddTime_resultTupleSchemeFactory implements SchemeFactory {
      public getUsersByAddTime_resultTupleScheme getScheme() {
        return new getUsersByAddTime_resultTupleScheme();
      }
    }

    private static class getUsersByAddTime_resultTupleScheme extends TupleScheme<getUsersByAddTime_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUsersByAddTime_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUsersByAddTime_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMutiUser();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

}
