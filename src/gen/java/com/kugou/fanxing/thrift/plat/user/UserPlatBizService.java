/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.thrift.plat.user;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-03-05")
public class UserPlatBizService {

  public interface Iface {

    /**
     * 判断用户是否绑定手机号码 data返回 1 绑定 0 没绑定 null程序出问题
     * 
     * 
     * @param kugouId
     * @param clientIp
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg isBindMobileNum(long kugouId, String clientIp) throws org.apache.thrift.TException;

    /**
     * 获取用户风险&&判断用户手机是否有改变
     * responsecode 判断最后一位为 0 请求成功 为 1失败
     * data返回
     * --riskLevel       危险度 1：高；2：中；3：低；4：正常    返回 null程序出问题
     * --isBindMobileNum 绑定手机  1 绑定 0 没绑定              返回 null程序出问题
     * 
     * 
     * @param kugouId
     */
    public com.kugou.fanxing.thrift.plat.user.vo.MResMsg risksQueryAndIsBindMobileNum(long kugouId) throws org.apache.thrift.TException;

    /**
     * 获取用户手机号码
     * responsecode 判断最后一位为 0 请求成功 为 1失败
     * data 手机号码 返回null说明没手机号码
     * 
     * 
     * @param kugouId
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg getMobileNum(long kugouId) throws org.apache.thrift.TException;

    /**
     * 获取用户手机号码
     * responsecode 判断最后一位为 0 请求成功 为 1失败
     * data 手机号码 返回null说明没手机号码
     * 
     * 
     * @param kugouId
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse getMobileNumV1(long kugouId) throws org.apache.thrift.TException;

    /**
     * 实时获取用户手机号码，默认限流100
     * responsecode 判断最后一位为 0 请求成功 为 1失败
     * data 手机号码 返回null说明没手机号码
     * 
     * 
     * @param kugouId
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse getMobileNumInRealTime(long kugouId) throws org.apache.thrift.TException;

    /**
     * * 获取大酷狗用户信息
     * * responsecode 判断最后一位为 0 请求成功 为 1失败
     * 
     * * data json格式字段包括如下{
     *     private long userid;//为kugouId
     *     private String username;//酷狗用户名
     *     private String truename;//用户真实姓名
     *     private String nickname;//酷狗用户昵称
     *     private int sex;//性别
     *     private String pic;//酷狗用户头像url
     *     private int score;//用户积分
     *     private int vip_type;//vip类型 0=非vip 1=砖石vip 3=黄金vip
     *     private String vip_begin_time;//vip起始时间
     *     private String vip_end_time;//vip结束时间(不等于空 并且大于当前时间 就是有效VIP )
     *     private String reg_time;//注册时间
     *     private String province;//省份
     *     private String city;//城市
     *     private String memo;//个人说明
     *     private String signature;//个性签名
     *     private String last_login_time;//最后登录时间
     *     private String birthday;//生日2011-01-01
     *     private String vip_clearday;//月结日
     *     private String security_email;//安全邮箱
     *     private String login_email;//登录邮箱
     *     private String login_mobile;//登录手机
     *     private int question_id;//密保问题id
     *     private String servertime;//服务器时间 2011-01-01 12:12:12
     *     private int exp;//当前经验}
     * *
     * 
     * @param kugouId
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg getUserInfoFromKugou(long kugouId) throws org.apache.thrift.TException;

    /**
     * private long userid;//kugouId
     * private String username;//酷狗用户名
     * private String truename;//用户真实姓名
     * private String nickname;//酷狗用户昵称
     * private int sex;//性别
     * private String pic;//酷狗用户头像url
     * private int score;//用户积分
     * private int vip_type;//vip类型 0=非vip 1=砖石vip 3=黄金vip
     * private String vip_begin_time;//vip起始时间
     * private String vip_end_time;//vip结束时间
     * private String svip_begin_time;//豪华VIP起始时间
     * private String svip_end_time;//豪华VIP结束时间
     * private String reg_time;//注册时间
     * private String province;//省份
     * private String city;//城市
     * private String memo;//个人说明
     * private String signature;//个性签名
     * private String last_login_time;//最后登录时间
     * private String birthday;//生日2011-01-01
     * private String vip_clearday;//月结日
     * private String security_email;//安全邮箱
     * private String login_email;//登录邮箱
     * private String login_mobile;//登录手机（打星串）
     * private String login_mobile_finger;//登录手机（指纹串）
     * private String login_mobile_encrypt;//登录手机（加密串）
     * private int question_id;//密保问题id
     * private String servertime;//服务器时间 2011-01-01 12:12:12
     * private int exp;//当前经验
     * private String tags;//标签信息
     * private int risk;//1:正常 2:有密码泄露风险
     * private int m_type;//包月类型 <=0 非音乐包 1=音乐包 2=赠送音乐包 3=豪华音乐包 4=赠送豪华音乐包
     * private String m_begin_time;//包月起始时间，格式：2013-12-24 13:45:33; 没有就为空字符串
     * private String m_end_time;//包月结束时间，格式：2014-01-05 13:45:33; 没有就为空字符串
     * private int cmdid;//命令号
     * *
     * 
     * @param kugouId
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg getUserInfoFromKugouV1(long kugouId) throws org.apache.thrift.TException;

    /**
     * 发送验证码
     * 
     * 
     * @param request
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse sendCaptcha(com.kugou.fanxing.thrift.plat.user.vo.UserCaptchaVo request) throws org.apache.thrift.TException;

    /**
     * 核验验证码
     * 
     * 
     * @param request
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse checkCaptcha(com.kugou.fanxing.thrift.plat.user.vo.CheckUserCaptchaVo request) throws org.apache.thrift.TException;

    /**
     * 绑定手机，该处的验证码非sendCaptcha的验证码，而是调用大酷狗发送的验证码，核验验证码也是大酷狗的服务
     * 
     * 
     * @param request
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse bindPhone(com.kugou.fanxing.thrift.plat.user.vo.BindMobilePhoneVo request) throws org.apache.thrift.TException;

    /**
     * 绑定手机以后，在一段时间内可以重复核验绑定手机的信息,但是需要先调用绑定手机接口
     * 
     * 
     * @param request
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse checkBindPhoneInfo(com.kugou.fanxing.thrift.plat.user.vo.CheckBindMobilePhoneVo request) throws org.apache.thrift.TException;

    /**
     * 转调大酷狗接口：检查用户的注销状态
     * 
     * 
     * @param kugouId
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse getUserCancelStatus(long kugouId) throws org.apache.thrift.TException;

    /**
     * 转调大酷狗接口：批量查询用户的生日，限制50
     * 
     * 
     * @param kugouIds
     */
    public com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse batchGetUserBirthDay(List<Long> kugouIds) throws org.apache.thrift.TException;

    /**
     * 转调大酷狗接口：根据酷狗id发送短信
     * 
     * 
     * @param request
     */
    public com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp sendSmsByKugouId(com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq request) throws org.apache.thrift.TException;

  }

  public interface AsyncIface {

    public void isBindMobileNum(long kugouId, String clientIp, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void risksQueryAndIsBindMobileNum(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getMobileNum(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getMobileNumV1(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getMobileNumInRealTime(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUserInfoFromKugou(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUserInfoFromKugouV1(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void sendCaptcha(com.kugou.fanxing.thrift.plat.user.vo.UserCaptchaVo request, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void checkCaptcha(com.kugou.fanxing.thrift.plat.user.vo.CheckUserCaptchaVo request, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void bindPhone(com.kugou.fanxing.thrift.plat.user.vo.BindMobilePhoneVo request, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void checkBindPhoneInfo(com.kugou.fanxing.thrift.plat.user.vo.CheckBindMobilePhoneVo request, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUserCancelStatus(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void batchGetUserBirthDay(List<Long> kugouIds, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void sendSmsByKugouId(com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq request, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

  }

  public static class Client extends org.apache.thrift.TServiceClient implements Iface {
    public static class Factory implements org.apache.thrift.TServiceClientFactory<Client> {
      public Factory() {}
      public Client getClient(org.apache.thrift.protocol.TProtocol prot) {
        return new Client(prot);
      }
      public Client getClient(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
        return new Client(iprot, oprot);
      }
    }

    public Client(org.apache.thrift.protocol.TProtocol prot)
    {
      super(prot, prot);
    }

    public Client(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
      super(iprot, oprot);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg isBindMobileNum(long kugouId, String clientIp) throws org.apache.thrift.TException
    {
      send_isBindMobileNum(kugouId, clientIp);
      return recv_isBindMobileNum();
    }

    public void send_isBindMobileNum(long kugouId, String clientIp) throws org.apache.thrift.TException
    {
      isBindMobileNum_args args = new isBindMobileNum_args();
      args.setKugouId(kugouId);
      args.setClientIp(clientIp);
      sendBase("isBindMobileNum", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg recv_isBindMobileNum() throws org.apache.thrift.TException
    {
      isBindMobileNum_result result = new isBindMobileNum_result();
      receiveBase(result, "isBindMobileNum");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "isBindMobileNum failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.MResMsg risksQueryAndIsBindMobileNum(long kugouId) throws org.apache.thrift.TException
    {
      send_risksQueryAndIsBindMobileNum(kugouId);
      return recv_risksQueryAndIsBindMobileNum();
    }

    public void send_risksQueryAndIsBindMobileNum(long kugouId) throws org.apache.thrift.TException
    {
      risksQueryAndIsBindMobileNum_args args = new risksQueryAndIsBindMobileNum_args();
      args.setKugouId(kugouId);
      sendBase("risksQueryAndIsBindMobileNum", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.MResMsg recv_risksQueryAndIsBindMobileNum() throws org.apache.thrift.TException
    {
      risksQueryAndIsBindMobileNum_result result = new risksQueryAndIsBindMobileNum_result();
      receiveBase(result, "risksQueryAndIsBindMobileNum");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "risksQueryAndIsBindMobileNum failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg getMobileNum(long kugouId) throws org.apache.thrift.TException
    {
      send_getMobileNum(kugouId);
      return recv_getMobileNum();
    }

    public void send_getMobileNum(long kugouId) throws org.apache.thrift.TException
    {
      getMobileNum_args args = new getMobileNum_args();
      args.setKugouId(kugouId);
      sendBase("getMobileNum", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg recv_getMobileNum() throws org.apache.thrift.TException
    {
      getMobileNum_result result = new getMobileNum_result();
      receiveBase(result, "getMobileNum");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getMobileNum failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse getMobileNumV1(long kugouId) throws org.apache.thrift.TException
    {
      send_getMobileNumV1(kugouId);
      return recv_getMobileNumV1();
    }

    public void send_getMobileNumV1(long kugouId) throws org.apache.thrift.TException
    {
      getMobileNumV1_args args = new getMobileNumV1_args();
      args.setKugouId(kugouId);
      sendBase("getMobileNumV1", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse recv_getMobileNumV1() throws org.apache.thrift.TException
    {
      getMobileNumV1_result result = new getMobileNumV1_result();
      receiveBase(result, "getMobileNumV1");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getMobileNumV1 failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse getMobileNumInRealTime(long kugouId) throws org.apache.thrift.TException
    {
      send_getMobileNumInRealTime(kugouId);
      return recv_getMobileNumInRealTime();
    }

    public void send_getMobileNumInRealTime(long kugouId) throws org.apache.thrift.TException
    {
      getMobileNumInRealTime_args args = new getMobileNumInRealTime_args();
      args.setKugouId(kugouId);
      sendBase("getMobileNumInRealTime", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse recv_getMobileNumInRealTime() throws org.apache.thrift.TException
    {
      getMobileNumInRealTime_result result = new getMobileNumInRealTime_result();
      receiveBase(result, "getMobileNumInRealTime");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getMobileNumInRealTime failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg getUserInfoFromKugou(long kugouId) throws org.apache.thrift.TException
    {
      send_getUserInfoFromKugou(kugouId);
      return recv_getUserInfoFromKugou();
    }

    public void send_getUserInfoFromKugou(long kugouId) throws org.apache.thrift.TException
    {
      getUserInfoFromKugou_args args = new getUserInfoFromKugou_args();
      args.setKugouId(kugouId);
      sendBase("getUserInfoFromKugou", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg recv_getUserInfoFromKugou() throws org.apache.thrift.TException
    {
      getUserInfoFromKugou_result result = new getUserInfoFromKugou_result();
      receiveBase(result, "getUserInfoFromKugou");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUserInfoFromKugou failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg getUserInfoFromKugouV1(long kugouId) throws org.apache.thrift.TException
    {
      send_getUserInfoFromKugouV1(kugouId);
      return recv_getUserInfoFromKugouV1();
    }

    public void send_getUserInfoFromKugouV1(long kugouId) throws org.apache.thrift.TException
    {
      getUserInfoFromKugouV1_args args = new getUserInfoFromKugouV1_args();
      args.setKugouId(kugouId);
      sendBase("getUserInfoFromKugouV1", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg recv_getUserInfoFromKugouV1() throws org.apache.thrift.TException
    {
      getUserInfoFromKugouV1_result result = new getUserInfoFromKugouV1_result();
      receiveBase(result, "getUserInfoFromKugouV1");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUserInfoFromKugouV1 failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse sendCaptcha(com.kugou.fanxing.thrift.plat.user.vo.UserCaptchaVo request) throws org.apache.thrift.TException
    {
      send_sendCaptcha(request);
      return recv_sendCaptcha();
    }

    public void send_sendCaptcha(com.kugou.fanxing.thrift.plat.user.vo.UserCaptchaVo request) throws org.apache.thrift.TException
    {
      sendCaptcha_args args = new sendCaptcha_args();
      args.setRequest(request);
      sendBase("sendCaptcha", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse recv_sendCaptcha() throws org.apache.thrift.TException
    {
      sendCaptcha_result result = new sendCaptcha_result();
      receiveBase(result, "sendCaptcha");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "sendCaptcha failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse checkCaptcha(com.kugou.fanxing.thrift.plat.user.vo.CheckUserCaptchaVo request) throws org.apache.thrift.TException
    {
      send_checkCaptcha(request);
      return recv_checkCaptcha();
    }

    public void send_checkCaptcha(com.kugou.fanxing.thrift.plat.user.vo.CheckUserCaptchaVo request) throws org.apache.thrift.TException
    {
      checkCaptcha_args args = new checkCaptcha_args();
      args.setRequest(request);
      sendBase("checkCaptcha", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse recv_checkCaptcha() throws org.apache.thrift.TException
    {
      checkCaptcha_result result = new checkCaptcha_result();
      receiveBase(result, "checkCaptcha");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "checkCaptcha failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse bindPhone(com.kugou.fanxing.thrift.plat.user.vo.BindMobilePhoneVo request) throws org.apache.thrift.TException
    {
      send_bindPhone(request);
      return recv_bindPhone();
    }

    public void send_bindPhone(com.kugou.fanxing.thrift.plat.user.vo.BindMobilePhoneVo request) throws org.apache.thrift.TException
    {
      bindPhone_args args = new bindPhone_args();
      args.setRequest(request);
      sendBase("bindPhone", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse recv_bindPhone() throws org.apache.thrift.TException
    {
      bindPhone_result result = new bindPhone_result();
      receiveBase(result, "bindPhone");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "bindPhone failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse checkBindPhoneInfo(com.kugou.fanxing.thrift.plat.user.vo.CheckBindMobilePhoneVo request) throws org.apache.thrift.TException
    {
      send_checkBindPhoneInfo(request);
      return recv_checkBindPhoneInfo();
    }

    public void send_checkBindPhoneInfo(com.kugou.fanxing.thrift.plat.user.vo.CheckBindMobilePhoneVo request) throws org.apache.thrift.TException
    {
      checkBindPhoneInfo_args args = new checkBindPhoneInfo_args();
      args.setRequest(request);
      sendBase("checkBindPhoneInfo", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse recv_checkBindPhoneInfo() throws org.apache.thrift.TException
    {
      checkBindPhoneInfo_result result = new checkBindPhoneInfo_result();
      receiveBase(result, "checkBindPhoneInfo");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "checkBindPhoneInfo failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse getUserCancelStatus(long kugouId) throws org.apache.thrift.TException
    {
      send_getUserCancelStatus(kugouId);
      return recv_getUserCancelStatus();
    }

    public void send_getUserCancelStatus(long kugouId) throws org.apache.thrift.TException
    {
      getUserCancelStatus_args args = new getUserCancelStatus_args();
      args.setKugouId(kugouId);
      sendBase("getUserCancelStatus", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse recv_getUserCancelStatus() throws org.apache.thrift.TException
    {
      getUserCancelStatus_result result = new getUserCancelStatus_result();
      receiveBase(result, "getUserCancelStatus");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUserCancelStatus failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse batchGetUserBirthDay(List<Long> kugouIds) throws org.apache.thrift.TException
    {
      send_batchGetUserBirthDay(kugouIds);
      return recv_batchGetUserBirthDay();
    }

    public void send_batchGetUserBirthDay(List<Long> kugouIds) throws org.apache.thrift.TException
    {
      batchGetUserBirthDay_args args = new batchGetUserBirthDay_args();
      args.setKugouIds(kugouIds);
      sendBase("batchGetUserBirthDay", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse recv_batchGetUserBirthDay() throws org.apache.thrift.TException
    {
      batchGetUserBirthDay_result result = new batchGetUserBirthDay_result();
      receiveBase(result, "batchGetUserBirthDay");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "batchGetUserBirthDay failed: unknown result");
    }

    public com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp sendSmsByKugouId(com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq request) throws org.apache.thrift.TException
    {
      send_sendSmsByKugouId(request);
      return recv_sendSmsByKugouId();
    }

    public void send_sendSmsByKugouId(com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq request) throws org.apache.thrift.TException
    {
      sendSmsByKugouId_args args = new sendSmsByKugouId_args();
      args.setRequest(request);
      sendBase("sendSmsByKugouId", args);
    }

    public com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp recv_sendSmsByKugouId() throws org.apache.thrift.TException
    {
      sendSmsByKugouId_result result = new sendSmsByKugouId_result();
      receiveBase(result, "sendSmsByKugouId");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "sendSmsByKugouId failed: unknown result");
    }

  }
  public static class AsyncClient extends org.apache.thrift.async.TAsyncClient implements AsyncIface {
    public static class Factory implements org.apache.thrift.async.TAsyncClientFactory<AsyncClient> {
      private org.apache.thrift.async.TAsyncClientManager clientManager;
      private org.apache.thrift.protocol.TProtocolFactory protocolFactory;
      public Factory(org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.protocol.TProtocolFactory protocolFactory) {
        this.clientManager = clientManager;
        this.protocolFactory = protocolFactory;
      }
      public AsyncClient getAsyncClient(org.apache.thrift.transport.TNonblockingTransport transport) {
        return new AsyncClient(protocolFactory, clientManager, transport);
      }
    }

    public AsyncClient(org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.transport.TNonblockingTransport transport) {
      super(protocolFactory, clientManager, transport);
    }

    public void isBindMobileNum(long kugouId, String clientIp, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      isBindMobileNum_call method_call = new isBindMobileNum_call(kugouId, clientIp, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class isBindMobileNum_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      private String clientIp;
      public isBindMobileNum_call(long kugouId, String clientIp, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
        this.clientIp = clientIp;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("isBindMobileNum", org.apache.thrift.protocol.TMessageType.CALL, 0));
        isBindMobileNum_args args = new isBindMobileNum_args();
        args.setKugouId(kugouId);
        args.setClientIp(clientIp);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResMsg getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_isBindMobileNum();
      }
    }

    public void risksQueryAndIsBindMobileNum(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      risksQueryAndIsBindMobileNum_call method_call = new risksQueryAndIsBindMobileNum_call(kugouId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class risksQueryAndIsBindMobileNum_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      public risksQueryAndIsBindMobileNum_call(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("risksQueryAndIsBindMobileNum", org.apache.thrift.protocol.TMessageType.CALL, 0));
        risksQueryAndIsBindMobileNum_args args = new risksQueryAndIsBindMobileNum_args();
        args.setKugouId(kugouId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.MResMsg getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_risksQueryAndIsBindMobileNum();
      }
    }

    public void getMobileNum(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getMobileNum_call method_call = new getMobileNum_call(kugouId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getMobileNum_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      public getMobileNum_call(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getMobileNum", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getMobileNum_args args = new getMobileNum_args();
        args.setKugouId(kugouId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResMsg getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getMobileNum();
      }
    }

    public void getMobileNumV1(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getMobileNumV1_call method_call = new getMobileNumV1_call(kugouId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getMobileNumV1_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      public getMobileNumV1_call(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getMobileNumV1", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getMobileNumV1_args args = new getMobileNumV1_args();
        args.setKugouId(kugouId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getMobileNumV1();
      }
    }

    public void getMobileNumInRealTime(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getMobileNumInRealTime_call method_call = new getMobileNumInRealTime_call(kugouId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getMobileNumInRealTime_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      public getMobileNumInRealTime_call(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getMobileNumInRealTime", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getMobileNumInRealTime_args args = new getMobileNumInRealTime_args();
        args.setKugouId(kugouId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getMobileNumInRealTime();
      }
    }

    public void getUserInfoFromKugou(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUserInfoFromKugou_call method_call = new getUserInfoFromKugou_call(kugouId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUserInfoFromKugou_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      public getUserInfoFromKugou_call(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUserInfoFromKugou", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUserInfoFromKugou_args args = new getUserInfoFromKugou_args();
        args.setKugouId(kugouId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResMsg getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUserInfoFromKugou();
      }
    }

    public void getUserInfoFromKugouV1(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUserInfoFromKugouV1_call method_call = new getUserInfoFromKugouV1_call(kugouId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUserInfoFromKugouV1_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      public getUserInfoFromKugouV1_call(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUserInfoFromKugouV1", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUserInfoFromKugouV1_args args = new getUserInfoFromKugouV1_args();
        args.setKugouId(kugouId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResMsg getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUserInfoFromKugouV1();
      }
    }

    public void sendCaptcha(com.kugou.fanxing.thrift.plat.user.vo.UserCaptchaVo request, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      sendCaptcha_call method_call = new sendCaptcha_call(request, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class sendCaptcha_call extends org.apache.thrift.async.TAsyncMethodCall {
      private com.kugou.fanxing.thrift.plat.user.vo.UserCaptchaVo request;
      public sendCaptcha_call(com.kugou.fanxing.thrift.plat.user.vo.UserCaptchaVo request, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.request = request;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("sendCaptcha", org.apache.thrift.protocol.TMessageType.CALL, 0));
        sendCaptcha_args args = new sendCaptcha_args();
        args.setRequest(request);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_sendCaptcha();
      }
    }

    public void checkCaptcha(com.kugou.fanxing.thrift.plat.user.vo.CheckUserCaptchaVo request, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      checkCaptcha_call method_call = new checkCaptcha_call(request, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class checkCaptcha_call extends org.apache.thrift.async.TAsyncMethodCall {
      private com.kugou.fanxing.thrift.plat.user.vo.CheckUserCaptchaVo request;
      public checkCaptcha_call(com.kugou.fanxing.thrift.plat.user.vo.CheckUserCaptchaVo request, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.request = request;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("checkCaptcha", org.apache.thrift.protocol.TMessageType.CALL, 0));
        checkCaptcha_args args = new checkCaptcha_args();
        args.setRequest(request);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_checkCaptcha();
      }
    }

    public void bindPhone(com.kugou.fanxing.thrift.plat.user.vo.BindMobilePhoneVo request, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      bindPhone_call method_call = new bindPhone_call(request, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class bindPhone_call extends org.apache.thrift.async.TAsyncMethodCall {
      private com.kugou.fanxing.thrift.plat.user.vo.BindMobilePhoneVo request;
      public bindPhone_call(com.kugou.fanxing.thrift.plat.user.vo.BindMobilePhoneVo request, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.request = request;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("bindPhone", org.apache.thrift.protocol.TMessageType.CALL, 0));
        bindPhone_args args = new bindPhone_args();
        args.setRequest(request);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_bindPhone();
      }
    }

    public void checkBindPhoneInfo(com.kugou.fanxing.thrift.plat.user.vo.CheckBindMobilePhoneVo request, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      checkBindPhoneInfo_call method_call = new checkBindPhoneInfo_call(request, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class checkBindPhoneInfo_call extends org.apache.thrift.async.TAsyncMethodCall {
      private com.kugou.fanxing.thrift.plat.user.vo.CheckBindMobilePhoneVo request;
      public checkBindPhoneInfo_call(com.kugou.fanxing.thrift.plat.user.vo.CheckBindMobilePhoneVo request, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.request = request;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("checkBindPhoneInfo", org.apache.thrift.protocol.TMessageType.CALL, 0));
        checkBindPhoneInfo_args args = new checkBindPhoneInfo_args();
        args.setRequest(request);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_checkBindPhoneInfo();
      }
    }

    public void getUserCancelStatus(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUserCancelStatus_call method_call = new getUserCancelStatus_call(kugouId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUserCancelStatus_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      public getUserCancelStatus_call(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUserCancelStatus", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUserCancelStatus_args args = new getUserCancelStatus_args();
        args.setKugouId(kugouId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUserCancelStatus();
      }
    }

    public void batchGetUserBirthDay(List<Long> kugouIds, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      batchGetUserBirthDay_call method_call = new batchGetUserBirthDay_call(kugouIds, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class batchGetUserBirthDay_call extends org.apache.thrift.async.TAsyncMethodCall {
      private List<Long> kugouIds;
      public batchGetUserBirthDay_call(List<Long> kugouIds, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouIds = kugouIds;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("batchGetUserBirthDay", org.apache.thrift.protocol.TMessageType.CALL, 0));
        batchGetUserBirthDay_args args = new batchGetUserBirthDay_args();
        args.setKugouIds(kugouIds);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_batchGetUserBirthDay();
      }
    }

    public void sendSmsByKugouId(com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq request, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      sendSmsByKugouId_call method_call = new sendSmsByKugouId_call(request, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class sendSmsByKugouId_call extends org.apache.thrift.async.TAsyncMethodCall {
      private com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq request;
      public sendSmsByKugouId_call(com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq request, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.request = request;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("sendSmsByKugouId", org.apache.thrift.protocol.TMessageType.CALL, 0));
        sendSmsByKugouId_args args = new sendSmsByKugouId_args();
        args.setRequest(request);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_sendSmsByKugouId();
      }
    }

  }

  public static class Processor<I extends Iface> extends org.apache.thrift.TBaseProcessor<I> implements org.apache.thrift.TProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());
    public Processor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>>()));
    }

    protected Processor(I iface, Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends Iface> Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> getProcessMap(Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      processMap.put("isBindMobileNum", new isBindMobileNum());
      processMap.put("risksQueryAndIsBindMobileNum", new risksQueryAndIsBindMobileNum());
      processMap.put("getMobileNum", new getMobileNum());
      processMap.put("getMobileNumV1", new getMobileNumV1());
      processMap.put("getMobileNumInRealTime", new getMobileNumInRealTime());
      processMap.put("getUserInfoFromKugou", new getUserInfoFromKugou());
      processMap.put("getUserInfoFromKugouV1", new getUserInfoFromKugouV1());
      processMap.put("sendCaptcha", new sendCaptcha());
      processMap.put("checkCaptcha", new checkCaptcha());
      processMap.put("bindPhone", new bindPhone());
      processMap.put("checkBindPhoneInfo", new checkBindPhoneInfo());
      processMap.put("getUserCancelStatus", new getUserCancelStatus());
      processMap.put("batchGetUserBirthDay", new batchGetUserBirthDay());
      processMap.put("sendSmsByKugouId", new sendSmsByKugouId());
      return processMap;
    }

    public static class isBindMobileNum<I extends Iface> extends org.apache.thrift.ProcessFunction<I, isBindMobileNum_args> {
      public isBindMobileNum() {
        super("isBindMobileNum");
      }

      public isBindMobileNum_args getEmptyArgsInstance() {
        return new isBindMobileNum_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public isBindMobileNum_result getResult(I iface, isBindMobileNum_args args) throws org.apache.thrift.TException {
        isBindMobileNum_result result = new isBindMobileNum_result();
        result.success = iface.isBindMobileNum(args.kugouId, args.clientIp);
        return result;
      }
    }

    public static class risksQueryAndIsBindMobileNum<I extends Iface> extends org.apache.thrift.ProcessFunction<I, risksQueryAndIsBindMobileNum_args> {
      public risksQueryAndIsBindMobileNum() {
        super("risksQueryAndIsBindMobileNum");
      }

      public risksQueryAndIsBindMobileNum_args getEmptyArgsInstance() {
        return new risksQueryAndIsBindMobileNum_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public risksQueryAndIsBindMobileNum_result getResult(I iface, risksQueryAndIsBindMobileNum_args args) throws org.apache.thrift.TException {
        risksQueryAndIsBindMobileNum_result result = new risksQueryAndIsBindMobileNum_result();
        result.success = iface.risksQueryAndIsBindMobileNum(args.kugouId);
        return result;
      }
    }

    public static class getMobileNum<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getMobileNum_args> {
      public getMobileNum() {
        super("getMobileNum");
      }

      public getMobileNum_args getEmptyArgsInstance() {
        return new getMobileNum_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getMobileNum_result getResult(I iface, getMobileNum_args args) throws org.apache.thrift.TException {
        getMobileNum_result result = new getMobileNum_result();
        result.success = iface.getMobileNum(args.kugouId);
        return result;
      }
    }

    public static class getMobileNumV1<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getMobileNumV1_args> {
      public getMobileNumV1() {
        super("getMobileNumV1");
      }

      public getMobileNumV1_args getEmptyArgsInstance() {
        return new getMobileNumV1_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getMobileNumV1_result getResult(I iface, getMobileNumV1_args args) throws org.apache.thrift.TException {
        getMobileNumV1_result result = new getMobileNumV1_result();
        result.success = iface.getMobileNumV1(args.kugouId);
        return result;
      }
    }

    public static class getMobileNumInRealTime<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getMobileNumInRealTime_args> {
      public getMobileNumInRealTime() {
        super("getMobileNumInRealTime");
      }

      public getMobileNumInRealTime_args getEmptyArgsInstance() {
        return new getMobileNumInRealTime_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getMobileNumInRealTime_result getResult(I iface, getMobileNumInRealTime_args args) throws org.apache.thrift.TException {
        getMobileNumInRealTime_result result = new getMobileNumInRealTime_result();
        result.success = iface.getMobileNumInRealTime(args.kugouId);
        return result;
      }
    }

    public static class getUserInfoFromKugou<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUserInfoFromKugou_args> {
      public getUserInfoFromKugou() {
        super("getUserInfoFromKugou");
      }

      public getUserInfoFromKugou_args getEmptyArgsInstance() {
        return new getUserInfoFromKugou_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUserInfoFromKugou_result getResult(I iface, getUserInfoFromKugou_args args) throws org.apache.thrift.TException {
        getUserInfoFromKugou_result result = new getUserInfoFromKugou_result();
        result.success = iface.getUserInfoFromKugou(args.kugouId);
        return result;
      }
    }

    public static class getUserInfoFromKugouV1<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUserInfoFromKugouV1_args> {
      public getUserInfoFromKugouV1() {
        super("getUserInfoFromKugouV1");
      }

      public getUserInfoFromKugouV1_args getEmptyArgsInstance() {
        return new getUserInfoFromKugouV1_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUserInfoFromKugouV1_result getResult(I iface, getUserInfoFromKugouV1_args args) throws org.apache.thrift.TException {
        getUserInfoFromKugouV1_result result = new getUserInfoFromKugouV1_result();
        result.success = iface.getUserInfoFromKugouV1(args.kugouId);
        return result;
      }
    }

    public static class sendCaptcha<I extends Iface> extends org.apache.thrift.ProcessFunction<I, sendCaptcha_args> {
      public sendCaptcha() {
        super("sendCaptcha");
      }

      public sendCaptcha_args getEmptyArgsInstance() {
        return new sendCaptcha_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public sendCaptcha_result getResult(I iface, sendCaptcha_args args) throws org.apache.thrift.TException {
        sendCaptcha_result result = new sendCaptcha_result();
        result.success = iface.sendCaptcha(args.request);
        return result;
      }
    }

    public static class checkCaptcha<I extends Iface> extends org.apache.thrift.ProcessFunction<I, checkCaptcha_args> {
      public checkCaptcha() {
        super("checkCaptcha");
      }

      public checkCaptcha_args getEmptyArgsInstance() {
        return new checkCaptcha_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public checkCaptcha_result getResult(I iface, checkCaptcha_args args) throws org.apache.thrift.TException {
        checkCaptcha_result result = new checkCaptcha_result();
        result.success = iface.checkCaptcha(args.request);
        return result;
      }
    }

    public static class bindPhone<I extends Iface> extends org.apache.thrift.ProcessFunction<I, bindPhone_args> {
      public bindPhone() {
        super("bindPhone");
      }

      public bindPhone_args getEmptyArgsInstance() {
        return new bindPhone_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public bindPhone_result getResult(I iface, bindPhone_args args) throws org.apache.thrift.TException {
        bindPhone_result result = new bindPhone_result();
        result.success = iface.bindPhone(args.request);
        return result;
      }
    }

    public static class checkBindPhoneInfo<I extends Iface> extends org.apache.thrift.ProcessFunction<I, checkBindPhoneInfo_args> {
      public checkBindPhoneInfo() {
        super("checkBindPhoneInfo");
      }

      public checkBindPhoneInfo_args getEmptyArgsInstance() {
        return new checkBindPhoneInfo_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public checkBindPhoneInfo_result getResult(I iface, checkBindPhoneInfo_args args) throws org.apache.thrift.TException {
        checkBindPhoneInfo_result result = new checkBindPhoneInfo_result();
        result.success = iface.checkBindPhoneInfo(args.request);
        return result;
      }
    }

    public static class getUserCancelStatus<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUserCancelStatus_args> {
      public getUserCancelStatus() {
        super("getUserCancelStatus");
      }

      public getUserCancelStatus_args getEmptyArgsInstance() {
        return new getUserCancelStatus_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUserCancelStatus_result getResult(I iface, getUserCancelStatus_args args) throws org.apache.thrift.TException {
        getUserCancelStatus_result result = new getUserCancelStatus_result();
        result.success = iface.getUserCancelStatus(args.kugouId);
        return result;
      }
    }

    public static class batchGetUserBirthDay<I extends Iface> extends org.apache.thrift.ProcessFunction<I, batchGetUserBirthDay_args> {
      public batchGetUserBirthDay() {
        super("batchGetUserBirthDay");
      }

      public batchGetUserBirthDay_args getEmptyArgsInstance() {
        return new batchGetUserBirthDay_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public batchGetUserBirthDay_result getResult(I iface, batchGetUserBirthDay_args args) throws org.apache.thrift.TException {
        batchGetUserBirthDay_result result = new batchGetUserBirthDay_result();
        result.success = iface.batchGetUserBirthDay(args.kugouIds);
        return result;
      }
    }

    public static class sendSmsByKugouId<I extends Iface> extends org.apache.thrift.ProcessFunction<I, sendSmsByKugouId_args> {
      public sendSmsByKugouId() {
        super("sendSmsByKugouId");
      }

      public sendSmsByKugouId_args getEmptyArgsInstance() {
        return new sendSmsByKugouId_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public sendSmsByKugouId_result getResult(I iface, sendSmsByKugouId_args args) throws org.apache.thrift.TException {
        sendSmsByKugouId_result result = new sendSmsByKugouId_result();
        result.success = iface.sendSmsByKugouId(args.request);
        return result;
      }
    }

  }

  public static class AsyncProcessor<I extends AsyncIface> extends org.apache.thrift.TBaseAsyncProcessor<I> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());
    public AsyncProcessor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.AsyncProcessFunction<I, ? extends org.apache.thrift.TBase, ?>>()));
    }

    protected AsyncProcessor(I iface, Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends AsyncIface> Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase,?>> getProcessMap(Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      processMap.put("isBindMobileNum", new isBindMobileNum());
      processMap.put("risksQueryAndIsBindMobileNum", new risksQueryAndIsBindMobileNum());
      processMap.put("getMobileNum", new getMobileNum());
      processMap.put("getMobileNumV1", new getMobileNumV1());
      processMap.put("getMobileNumInRealTime", new getMobileNumInRealTime());
      processMap.put("getUserInfoFromKugou", new getUserInfoFromKugou());
      processMap.put("getUserInfoFromKugouV1", new getUserInfoFromKugouV1());
      processMap.put("sendCaptcha", new sendCaptcha());
      processMap.put("checkCaptcha", new checkCaptcha());
      processMap.put("bindPhone", new bindPhone());
      processMap.put("checkBindPhoneInfo", new checkBindPhoneInfo());
      processMap.put("getUserCancelStatus", new getUserCancelStatus());
      processMap.put("batchGetUserBirthDay", new batchGetUserBirthDay());
      processMap.put("sendSmsByKugouId", new sendSmsByKugouId());
      return processMap;
    }

    public static class isBindMobileNum<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, isBindMobileNum_args, com.kugou.fanxing.thrift.plat.user.vo.ResMsg> {
      public isBindMobileNum() {
        super("isBindMobileNum");
      }

      public isBindMobileNum_args getEmptyArgsInstance() {
        return new isBindMobileNum_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMsg> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMsg>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResMsg o) {
            isBindMobileNum_result result = new isBindMobileNum_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            isBindMobileNum_result result = new isBindMobileNum_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, isBindMobileNum_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMsg> resultHandler) throws TException {
        iface.isBindMobileNum(args.kugouId, args.clientIp,resultHandler);
      }
    }

    public static class risksQueryAndIsBindMobileNum<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, risksQueryAndIsBindMobileNum_args, com.kugou.fanxing.thrift.plat.user.vo.MResMsg> {
      public risksQueryAndIsBindMobileNum() {
        super("risksQueryAndIsBindMobileNum");
      }

      public risksQueryAndIsBindMobileNum_args getEmptyArgsInstance() {
        return new risksQueryAndIsBindMobileNum_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.MResMsg> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.MResMsg>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.MResMsg o) {
            risksQueryAndIsBindMobileNum_result result = new risksQueryAndIsBindMobileNum_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            risksQueryAndIsBindMobileNum_result result = new risksQueryAndIsBindMobileNum_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, risksQueryAndIsBindMobileNum_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.MResMsg> resultHandler) throws TException {
        iface.risksQueryAndIsBindMobileNum(args.kugouId,resultHandler);
      }
    }

    public static class getMobileNum<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getMobileNum_args, com.kugou.fanxing.thrift.plat.user.vo.ResMsg> {
      public getMobileNum() {
        super("getMobileNum");
      }

      public getMobileNum_args getEmptyArgsInstance() {
        return new getMobileNum_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMsg> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMsg>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResMsg o) {
            getMobileNum_result result = new getMobileNum_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getMobileNum_result result = new getMobileNum_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getMobileNum_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMsg> resultHandler) throws TException {
        iface.getMobileNum(args.kugouId,resultHandler);
      }
    }

    public static class getMobileNumV1<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getMobileNumV1_args, com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse> {
      public getMobileNumV1() {
        super("getMobileNumV1");
      }

      public getMobileNumV1_args getEmptyArgsInstance() {
        return new getMobileNumV1_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse o) {
            getMobileNumV1_result result = new getMobileNumV1_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getMobileNumV1_result result = new getMobileNumV1_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getMobileNumV1_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse> resultHandler) throws TException {
        iface.getMobileNumV1(args.kugouId,resultHandler);
      }
    }

    public static class getMobileNumInRealTime<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getMobileNumInRealTime_args, com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse> {
      public getMobileNumInRealTime() {
        super("getMobileNumInRealTime");
      }

      public getMobileNumInRealTime_args getEmptyArgsInstance() {
        return new getMobileNumInRealTime_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse o) {
            getMobileNumInRealTime_result result = new getMobileNumInRealTime_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getMobileNumInRealTime_result result = new getMobileNumInRealTime_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getMobileNumInRealTime_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse> resultHandler) throws TException {
        iface.getMobileNumInRealTime(args.kugouId,resultHandler);
      }
    }

    public static class getUserInfoFromKugou<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUserInfoFromKugou_args, com.kugou.fanxing.thrift.plat.user.vo.ResMsg> {
      public getUserInfoFromKugou() {
        super("getUserInfoFromKugou");
      }

      public getUserInfoFromKugou_args getEmptyArgsInstance() {
        return new getUserInfoFromKugou_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMsg> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMsg>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResMsg o) {
            getUserInfoFromKugou_result result = new getUserInfoFromKugou_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUserInfoFromKugou_result result = new getUserInfoFromKugou_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUserInfoFromKugou_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMsg> resultHandler) throws TException {
        iface.getUserInfoFromKugou(args.kugouId,resultHandler);
      }
    }

    public static class getUserInfoFromKugouV1<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUserInfoFromKugouV1_args, com.kugou.fanxing.thrift.plat.user.vo.ResMsg> {
      public getUserInfoFromKugouV1() {
        super("getUserInfoFromKugouV1");
      }

      public getUserInfoFromKugouV1_args getEmptyArgsInstance() {
        return new getUserInfoFromKugouV1_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMsg> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMsg>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResMsg o) {
            getUserInfoFromKugouV1_result result = new getUserInfoFromKugouV1_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUserInfoFromKugouV1_result result = new getUserInfoFromKugouV1_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUserInfoFromKugouV1_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResMsg> resultHandler) throws TException {
        iface.getUserInfoFromKugouV1(args.kugouId,resultHandler);
      }
    }

    public static class sendCaptcha<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, sendCaptcha_args, com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse> {
      public sendCaptcha() {
        super("sendCaptcha");
      }

      public sendCaptcha_args getEmptyArgsInstance() {
        return new sendCaptcha_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse o) {
            sendCaptcha_result result = new sendCaptcha_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            sendCaptcha_result result = new sendCaptcha_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, sendCaptcha_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse> resultHandler) throws TException {
        iface.sendCaptcha(args.request,resultHandler);
      }
    }

    public static class checkCaptcha<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, checkCaptcha_args, com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse> {
      public checkCaptcha() {
        super("checkCaptcha");
      }

      public checkCaptcha_args getEmptyArgsInstance() {
        return new checkCaptcha_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse o) {
            checkCaptcha_result result = new checkCaptcha_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            checkCaptcha_result result = new checkCaptcha_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, checkCaptcha_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse> resultHandler) throws TException {
        iface.checkCaptcha(args.request,resultHandler);
      }
    }

    public static class bindPhone<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, bindPhone_args, com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse> {
      public bindPhone() {
        super("bindPhone");
      }

      public bindPhone_args getEmptyArgsInstance() {
        return new bindPhone_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse o) {
            bindPhone_result result = new bindPhone_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            bindPhone_result result = new bindPhone_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, bindPhone_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse> resultHandler) throws TException {
        iface.bindPhone(args.request,resultHandler);
      }
    }

    public static class checkBindPhoneInfo<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, checkBindPhoneInfo_args, com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse> {
      public checkBindPhoneInfo() {
        super("checkBindPhoneInfo");
      }

      public checkBindPhoneInfo_args getEmptyArgsInstance() {
        return new checkBindPhoneInfo_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse o) {
            checkBindPhoneInfo_result result = new checkBindPhoneInfo_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            checkBindPhoneInfo_result result = new checkBindPhoneInfo_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, checkBindPhoneInfo_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse> resultHandler) throws TException {
        iface.checkBindPhoneInfo(args.request,resultHandler);
      }
    }

    public static class getUserCancelStatus<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUserCancelStatus_args, com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse> {
      public getUserCancelStatus() {
        super("getUserCancelStatus");
      }

      public getUserCancelStatus_args getEmptyArgsInstance() {
        return new getUserCancelStatus_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse o) {
            getUserCancelStatus_result result = new getUserCancelStatus_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUserCancelStatus_result result = new getUserCancelStatus_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUserCancelStatus_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse> resultHandler) throws TException {
        iface.getUserCancelStatus(args.kugouId,resultHandler);
      }
    }

    public static class batchGetUserBirthDay<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, batchGetUserBirthDay_args, com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse> {
      public batchGetUserBirthDay() {
        super("batchGetUserBirthDay");
      }

      public batchGetUserBirthDay_args getEmptyArgsInstance() {
        return new batchGetUserBirthDay_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse o) {
            batchGetUserBirthDay_result result = new batchGetUserBirthDay_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            batchGetUserBirthDay_result result = new batchGetUserBirthDay_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, batchGetUserBirthDay_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse> resultHandler) throws TException {
        iface.batchGetUserBirthDay(args.kugouIds,resultHandler);
      }
    }

    public static class sendSmsByKugouId<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, sendSmsByKugouId_args, com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp> {
      public sendSmsByKugouId() {
        super("sendSmsByKugouId");
      }

      public sendSmsByKugouId_args getEmptyArgsInstance() {
        return new sendSmsByKugouId_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp>() { 
          public void onComplete(com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp o) {
            sendSmsByKugouId_result result = new sendSmsByKugouId_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            sendSmsByKugouId_result result = new sendSmsByKugouId_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, sendSmsByKugouId_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp> resultHandler) throws TException {
        iface.sendSmsByKugouId(args.request,resultHandler);
      }
    }

  }

  public static class isBindMobileNum_args implements org.apache.thrift.TBase<isBindMobileNum_args, isBindMobileNum_args._Fields>, java.io.Serializable, Cloneable, Comparable<isBindMobileNum_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isBindMobileNum_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);
    private static final org.apache.thrift.protocol.TField CLIENT_IP_FIELD_DESC = new org.apache.thrift.protocol.TField("clientIp", org.apache.thrift.protocol.TType.STRING, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isBindMobileNum_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isBindMobileNum_argsTupleSchemeFactory());
    }

    public long kugouId; // required
    public String clientIp; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId"),
      CLIENT_IP((short)2, "clientIp");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          case 2: // CLIENT_IP
            return CLIENT_IP;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.CLIENT_IP, new org.apache.thrift.meta_data.FieldMetaData("clientIp", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isBindMobileNum_args.class, metaDataMap);
    }

    public isBindMobileNum_args() {
    }

    public isBindMobileNum_args(
      long kugouId,
      String clientIp)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      this.clientIp = clientIp;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isBindMobileNum_args(isBindMobileNum_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
      if (other.isSetClientIp()) {
        this.clientIp = other.clientIp;
      }
    }

    public isBindMobileNum_args deepCopy() {
      return new isBindMobileNum_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
      this.clientIp = null;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public isBindMobileNum_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public String getClientIp() {
      return this.clientIp;
    }

    public isBindMobileNum_args setClientIp(String clientIp) {
      this.clientIp = clientIp;
      return this;
    }

    public void unsetClientIp() {
      this.clientIp = null;
    }

    /** Returns true if field clientIp is set (has been assigned a value) and false otherwise */
    public boolean isSetClientIp() {
      return this.clientIp != null;
    }

    public void setClientIpIsSet(boolean value) {
      if (!value) {
        this.clientIp = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      case CLIENT_IP:
        if (value == null) {
          unsetClientIp();
        } else {
          setClientIp((String)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      case CLIENT_IP:
        return getClientIp();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      case CLIENT_IP:
        return isSetClientIp();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isBindMobileNum_args)
        return this.equals((isBindMobileNum_args)that);
      return false;
    }

    public boolean equals(isBindMobileNum_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      boolean this_present_clientIp = true && this.isSetClientIp();
      boolean that_present_clientIp = true && that.isSetClientIp();
      if (this_present_clientIp || that_present_clientIp) {
        if (!(this_present_clientIp && that_present_clientIp))
          return false;
        if (!this.clientIp.equals(that.clientIp))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      boolean present_clientIp = true && (isSetClientIp());
      list.add(present_clientIp);
      if (present_clientIp)
        list.add(clientIp);

      return list.hashCode();
    }

    @Override
    public int compareTo(isBindMobileNum_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetClientIp()).compareTo(other.isSetClientIp());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetClientIp()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.clientIp, other.clientIp);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isBindMobileNum_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      if (!first) sb.append(", ");
      sb.append("clientIp:");
      if (this.clientIp == null) {
        sb.append("null");
      } else {
        sb.append(this.clientIp);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
      if (clientIp == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'clientIp' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isBindMobileNum_argsStandardSchemeFactory implements SchemeFactory {
      public isBindMobileNum_argsStandardScheme getScheme() {
        return new isBindMobileNum_argsStandardScheme();
      }
    }

    private static class isBindMobileNum_argsStandardScheme extends StandardScheme<isBindMobileNum_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isBindMobileNum_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // CLIENT_IP
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.clientIp = iprot.readString();
                struct.setClientIpIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        if (!struct.isSetKugouId()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
        }
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isBindMobileNum_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        if (struct.clientIp != null) {
          oprot.writeFieldBegin(CLIENT_IP_FIELD_DESC);
          oprot.writeString(struct.clientIp);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isBindMobileNum_argsTupleSchemeFactory implements SchemeFactory {
      public isBindMobileNum_argsTupleScheme getScheme() {
        return new isBindMobileNum_argsTupleScheme();
      }
    }

    private static class isBindMobileNum_argsTupleScheme extends TupleScheme<isBindMobileNum_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isBindMobileNum_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        oprot.writeI64(struct.kugouId);
        oprot.writeString(struct.clientIp);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isBindMobileNum_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.kugouId = iprot.readI64();
        struct.setKugouIdIsSet(true);
        struct.clientIp = iprot.readString();
        struct.setClientIpIsSet(true);
      }
    }

  }

  public static class isBindMobileNum_result implements org.apache.thrift.TBase<isBindMobileNum_result, isBindMobileNum_result._Fields>, java.io.Serializable, Cloneable, Comparable<isBindMobileNum_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isBindMobileNum_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isBindMobileNum_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isBindMobileNum_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResMsg.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isBindMobileNum_result.class, metaDataMap);
    }

    public isBindMobileNum_result() {
    }

    public isBindMobileNum_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResMsg success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isBindMobileNum_result(isBindMobileNum_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMsg(other.success);
      }
    }

    public isBindMobileNum_result deepCopy() {
      return new isBindMobileNum_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg getSuccess() {
      return this.success;
    }

    public isBindMobileNum_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResMsg success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResMsg)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isBindMobileNum_result)
        return this.equals((isBindMobileNum_result)that);
      return false;
    }

    public boolean equals(isBindMobileNum_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(isBindMobileNum_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isBindMobileNum_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isBindMobileNum_resultStandardSchemeFactory implements SchemeFactory {
      public isBindMobileNum_resultStandardScheme getScheme() {
        return new isBindMobileNum_resultStandardScheme();
      }
    }

    private static class isBindMobileNum_resultStandardScheme extends StandardScheme<isBindMobileNum_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isBindMobileNum_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMsg();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isBindMobileNum_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isBindMobileNum_resultTupleSchemeFactory implements SchemeFactory {
      public isBindMobileNum_resultTupleScheme getScheme() {
        return new isBindMobileNum_resultTupleScheme();
      }
    }

    private static class isBindMobileNum_resultTupleScheme extends TupleScheme<isBindMobileNum_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isBindMobileNum_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isBindMobileNum_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMsg();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class risksQueryAndIsBindMobileNum_args implements org.apache.thrift.TBase<risksQueryAndIsBindMobileNum_args, risksQueryAndIsBindMobileNum_args._Fields>, java.io.Serializable, Cloneable, Comparable<risksQueryAndIsBindMobileNum_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("risksQueryAndIsBindMobileNum_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new risksQueryAndIsBindMobileNum_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new risksQueryAndIsBindMobileNum_argsTupleSchemeFactory());
    }

    public long kugouId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(risksQueryAndIsBindMobileNum_args.class, metaDataMap);
    }

    public risksQueryAndIsBindMobileNum_args() {
    }

    public risksQueryAndIsBindMobileNum_args(
      long kugouId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public risksQueryAndIsBindMobileNum_args(risksQueryAndIsBindMobileNum_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
    }

    public risksQueryAndIsBindMobileNum_args deepCopy() {
      return new risksQueryAndIsBindMobileNum_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public risksQueryAndIsBindMobileNum_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof risksQueryAndIsBindMobileNum_args)
        return this.equals((risksQueryAndIsBindMobileNum_args)that);
      return false;
    }

    public boolean equals(risksQueryAndIsBindMobileNum_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      return list.hashCode();
    }

    @Override
    public int compareTo(risksQueryAndIsBindMobileNum_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("risksQueryAndIsBindMobileNum_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class risksQueryAndIsBindMobileNum_argsStandardSchemeFactory implements SchemeFactory {
      public risksQueryAndIsBindMobileNum_argsStandardScheme getScheme() {
        return new risksQueryAndIsBindMobileNum_argsStandardScheme();
      }
    }

    private static class risksQueryAndIsBindMobileNum_argsStandardScheme extends StandardScheme<risksQueryAndIsBindMobileNum_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, risksQueryAndIsBindMobileNum_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, risksQueryAndIsBindMobileNum_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class risksQueryAndIsBindMobileNum_argsTupleSchemeFactory implements SchemeFactory {
      public risksQueryAndIsBindMobileNum_argsTupleScheme getScheme() {
        return new risksQueryAndIsBindMobileNum_argsTupleScheme();
      }
    }

    private static class risksQueryAndIsBindMobileNum_argsTupleScheme extends TupleScheme<risksQueryAndIsBindMobileNum_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, risksQueryAndIsBindMobileNum_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouId()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetKugouId()) {
          oprot.writeI64(struct.kugouId);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, risksQueryAndIsBindMobileNum_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.kugouId = iprot.readI64();
          struct.setKugouIdIsSet(true);
        }
      }
    }

  }

  public static class risksQueryAndIsBindMobileNum_result implements org.apache.thrift.TBase<risksQueryAndIsBindMobileNum_result, risksQueryAndIsBindMobileNum_result._Fields>, java.io.Serializable, Cloneable, Comparable<risksQueryAndIsBindMobileNum_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("risksQueryAndIsBindMobileNum_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new risksQueryAndIsBindMobileNum_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new risksQueryAndIsBindMobileNum_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.MResMsg success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.MResMsg.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(risksQueryAndIsBindMobileNum_result.class, metaDataMap);
    }

    public risksQueryAndIsBindMobileNum_result() {
    }

    public risksQueryAndIsBindMobileNum_result(
      com.kugou.fanxing.thrift.plat.user.vo.MResMsg success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public risksQueryAndIsBindMobileNum_result(risksQueryAndIsBindMobileNum_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.MResMsg(other.success);
      }
    }

    public risksQueryAndIsBindMobileNum_result deepCopy() {
      return new risksQueryAndIsBindMobileNum_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.MResMsg getSuccess() {
      return this.success;
    }

    public risksQueryAndIsBindMobileNum_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.MResMsg success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.MResMsg)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof risksQueryAndIsBindMobileNum_result)
        return this.equals((risksQueryAndIsBindMobileNum_result)that);
      return false;
    }

    public boolean equals(risksQueryAndIsBindMobileNum_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(risksQueryAndIsBindMobileNum_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("risksQueryAndIsBindMobileNum_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class risksQueryAndIsBindMobileNum_resultStandardSchemeFactory implements SchemeFactory {
      public risksQueryAndIsBindMobileNum_resultStandardScheme getScheme() {
        return new risksQueryAndIsBindMobileNum_resultStandardScheme();
      }
    }

    private static class risksQueryAndIsBindMobileNum_resultStandardScheme extends StandardScheme<risksQueryAndIsBindMobileNum_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, risksQueryAndIsBindMobileNum_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.MResMsg();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, risksQueryAndIsBindMobileNum_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class risksQueryAndIsBindMobileNum_resultTupleSchemeFactory implements SchemeFactory {
      public risksQueryAndIsBindMobileNum_resultTupleScheme getScheme() {
        return new risksQueryAndIsBindMobileNum_resultTupleScheme();
      }
    }

    private static class risksQueryAndIsBindMobileNum_resultTupleScheme extends TupleScheme<risksQueryAndIsBindMobileNum_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, risksQueryAndIsBindMobileNum_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, risksQueryAndIsBindMobileNum_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.MResMsg();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getMobileNum_args implements org.apache.thrift.TBase<getMobileNum_args, getMobileNum_args._Fields>, java.io.Serializable, Cloneable, Comparable<getMobileNum_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getMobileNum_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getMobileNum_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getMobileNum_argsTupleSchemeFactory());
    }

    public long kugouId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getMobileNum_args.class, metaDataMap);
    }

    public getMobileNum_args() {
    }

    public getMobileNum_args(
      long kugouId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getMobileNum_args(getMobileNum_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
    }

    public getMobileNum_args deepCopy() {
      return new getMobileNum_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public getMobileNum_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getMobileNum_args)
        return this.equals((getMobileNum_args)that);
      return false;
    }

    public boolean equals(getMobileNum_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      return list.hashCode();
    }

    @Override
    public int compareTo(getMobileNum_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getMobileNum_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getMobileNum_argsStandardSchemeFactory implements SchemeFactory {
      public getMobileNum_argsStandardScheme getScheme() {
        return new getMobileNum_argsStandardScheme();
      }
    }

    private static class getMobileNum_argsStandardScheme extends StandardScheme<getMobileNum_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getMobileNum_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getMobileNum_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getMobileNum_argsTupleSchemeFactory implements SchemeFactory {
      public getMobileNum_argsTupleScheme getScheme() {
        return new getMobileNum_argsTupleScheme();
      }
    }

    private static class getMobileNum_argsTupleScheme extends TupleScheme<getMobileNum_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getMobileNum_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouId()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetKugouId()) {
          oprot.writeI64(struct.kugouId);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getMobileNum_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.kugouId = iprot.readI64();
          struct.setKugouIdIsSet(true);
        }
      }
    }

  }

  public static class getMobileNum_result implements org.apache.thrift.TBase<getMobileNum_result, getMobileNum_result._Fields>, java.io.Serializable, Cloneable, Comparable<getMobileNum_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getMobileNum_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getMobileNum_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getMobileNum_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResMsg.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getMobileNum_result.class, metaDataMap);
    }

    public getMobileNum_result() {
    }

    public getMobileNum_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResMsg success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getMobileNum_result(getMobileNum_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMsg(other.success);
      }
    }

    public getMobileNum_result deepCopy() {
      return new getMobileNum_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg getSuccess() {
      return this.success;
    }

    public getMobileNum_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResMsg success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResMsg)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getMobileNum_result)
        return this.equals((getMobileNum_result)that);
      return false;
    }

    public boolean equals(getMobileNum_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getMobileNum_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getMobileNum_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getMobileNum_resultStandardSchemeFactory implements SchemeFactory {
      public getMobileNum_resultStandardScheme getScheme() {
        return new getMobileNum_resultStandardScheme();
      }
    }

    private static class getMobileNum_resultStandardScheme extends StandardScheme<getMobileNum_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getMobileNum_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMsg();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getMobileNum_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getMobileNum_resultTupleSchemeFactory implements SchemeFactory {
      public getMobileNum_resultTupleScheme getScheme() {
        return new getMobileNum_resultTupleScheme();
      }
    }

    private static class getMobileNum_resultTupleScheme extends TupleScheme<getMobileNum_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getMobileNum_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getMobileNum_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMsg();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getMobileNumV1_args implements org.apache.thrift.TBase<getMobileNumV1_args, getMobileNumV1_args._Fields>, java.io.Serializable, Cloneable, Comparable<getMobileNumV1_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getMobileNumV1_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getMobileNumV1_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getMobileNumV1_argsTupleSchemeFactory());
    }

    public long kugouId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getMobileNumV1_args.class, metaDataMap);
    }

    public getMobileNumV1_args() {
    }

    public getMobileNumV1_args(
      long kugouId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getMobileNumV1_args(getMobileNumV1_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
    }

    public getMobileNumV1_args deepCopy() {
      return new getMobileNumV1_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public getMobileNumV1_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getMobileNumV1_args)
        return this.equals((getMobileNumV1_args)that);
      return false;
    }

    public boolean equals(getMobileNumV1_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      return list.hashCode();
    }

    @Override
    public int compareTo(getMobileNumV1_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getMobileNumV1_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getMobileNumV1_argsStandardSchemeFactory implements SchemeFactory {
      public getMobileNumV1_argsStandardScheme getScheme() {
        return new getMobileNumV1_argsStandardScheme();
      }
    }

    private static class getMobileNumV1_argsStandardScheme extends StandardScheme<getMobileNumV1_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getMobileNumV1_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getMobileNumV1_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getMobileNumV1_argsTupleSchemeFactory implements SchemeFactory {
      public getMobileNumV1_argsTupleScheme getScheme() {
        return new getMobileNumV1_argsTupleScheme();
      }
    }

    private static class getMobileNumV1_argsTupleScheme extends TupleScheme<getMobileNumV1_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getMobileNumV1_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouId()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetKugouId()) {
          oprot.writeI64(struct.kugouId);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getMobileNumV1_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.kugouId = iprot.readI64();
          struct.setKugouIdIsSet(true);
        }
      }
    }

  }

  public static class getMobileNumV1_result implements org.apache.thrift.TBase<getMobileNumV1_result, getMobileNumV1_result._Fields>, java.io.Serializable, Cloneable, Comparable<getMobileNumV1_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getMobileNumV1_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getMobileNumV1_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getMobileNumV1_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getMobileNumV1_result.class, metaDataMap);
    }

    public getMobileNumV1_result() {
    }

    public getMobileNumV1_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getMobileNumV1_result(getMobileNumV1_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse(other.success);
      }
    }

    public getMobileNumV1_result deepCopy() {
      return new getMobileNumV1_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse getSuccess() {
      return this.success;
    }

    public getMobileNumV1_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getMobileNumV1_result)
        return this.equals((getMobileNumV1_result)that);
      return false;
    }

    public boolean equals(getMobileNumV1_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getMobileNumV1_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getMobileNumV1_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getMobileNumV1_resultStandardSchemeFactory implements SchemeFactory {
      public getMobileNumV1_resultStandardScheme getScheme() {
        return new getMobileNumV1_resultStandardScheme();
      }
    }

    private static class getMobileNumV1_resultStandardScheme extends StandardScheme<getMobileNumV1_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getMobileNumV1_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getMobileNumV1_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getMobileNumV1_resultTupleSchemeFactory implements SchemeFactory {
      public getMobileNumV1_resultTupleScheme getScheme() {
        return new getMobileNumV1_resultTupleScheme();
      }
    }

    private static class getMobileNumV1_resultTupleScheme extends TupleScheme<getMobileNumV1_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getMobileNumV1_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getMobileNumV1_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getMobileNumInRealTime_args implements org.apache.thrift.TBase<getMobileNumInRealTime_args, getMobileNumInRealTime_args._Fields>, java.io.Serializable, Cloneable, Comparable<getMobileNumInRealTime_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getMobileNumInRealTime_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getMobileNumInRealTime_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getMobileNumInRealTime_argsTupleSchemeFactory());
    }

    public long kugouId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getMobileNumInRealTime_args.class, metaDataMap);
    }

    public getMobileNumInRealTime_args() {
    }

    public getMobileNumInRealTime_args(
      long kugouId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getMobileNumInRealTime_args(getMobileNumInRealTime_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
    }

    public getMobileNumInRealTime_args deepCopy() {
      return new getMobileNumInRealTime_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public getMobileNumInRealTime_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getMobileNumInRealTime_args)
        return this.equals((getMobileNumInRealTime_args)that);
      return false;
    }

    public boolean equals(getMobileNumInRealTime_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      return list.hashCode();
    }

    @Override
    public int compareTo(getMobileNumInRealTime_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getMobileNumInRealTime_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getMobileNumInRealTime_argsStandardSchemeFactory implements SchemeFactory {
      public getMobileNumInRealTime_argsStandardScheme getScheme() {
        return new getMobileNumInRealTime_argsStandardScheme();
      }
    }

    private static class getMobileNumInRealTime_argsStandardScheme extends StandardScheme<getMobileNumInRealTime_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getMobileNumInRealTime_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getMobileNumInRealTime_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getMobileNumInRealTime_argsTupleSchemeFactory implements SchemeFactory {
      public getMobileNumInRealTime_argsTupleScheme getScheme() {
        return new getMobileNumInRealTime_argsTupleScheme();
      }
    }

    private static class getMobileNumInRealTime_argsTupleScheme extends TupleScheme<getMobileNumInRealTime_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getMobileNumInRealTime_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouId()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetKugouId()) {
          oprot.writeI64(struct.kugouId);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getMobileNumInRealTime_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.kugouId = iprot.readI64();
          struct.setKugouIdIsSet(true);
        }
      }
    }

  }

  public static class getMobileNumInRealTime_result implements org.apache.thrift.TBase<getMobileNumInRealTime_result, getMobileNumInRealTime_result._Fields>, java.io.Serializable, Cloneable, Comparable<getMobileNumInRealTime_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getMobileNumInRealTime_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getMobileNumInRealTime_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getMobileNumInRealTime_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getMobileNumInRealTime_result.class, metaDataMap);
    }

    public getMobileNumInRealTime_result() {
    }

    public getMobileNumInRealTime_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getMobileNumInRealTime_result(getMobileNumInRealTime_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse(other.success);
      }
    }

    public getMobileNumInRealTime_result deepCopy() {
      return new getMobileNumInRealTime_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse getSuccess() {
      return this.success;
    }

    public getMobileNumInRealTime_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getMobileNumInRealTime_result)
        return this.equals((getMobileNumInRealTime_result)that);
      return false;
    }

    public boolean equals(getMobileNumInRealTime_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getMobileNumInRealTime_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getMobileNumInRealTime_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getMobileNumInRealTime_resultStandardSchemeFactory implements SchemeFactory {
      public getMobileNumInRealTime_resultStandardScheme getScheme() {
        return new getMobileNumInRealTime_resultStandardScheme();
      }
    }

    private static class getMobileNumInRealTime_resultStandardScheme extends StandardScheme<getMobileNumInRealTime_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getMobileNumInRealTime_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getMobileNumInRealTime_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getMobileNumInRealTime_resultTupleSchemeFactory implements SchemeFactory {
      public getMobileNumInRealTime_resultTupleScheme getScheme() {
        return new getMobileNumInRealTime_resultTupleScheme();
      }
    }

    private static class getMobileNumInRealTime_resultTupleScheme extends TupleScheme<getMobileNumInRealTime_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getMobileNumInRealTime_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getMobileNumInRealTime_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMobileResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUserInfoFromKugou_args implements org.apache.thrift.TBase<getUserInfoFromKugou_args, getUserInfoFromKugou_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUserInfoFromKugou_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserInfoFromKugou_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserInfoFromKugou_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserInfoFromKugou_argsTupleSchemeFactory());
    }

    public long kugouId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserInfoFromKugou_args.class, metaDataMap);
    }

    public getUserInfoFromKugou_args() {
    }

    public getUserInfoFromKugou_args(
      long kugouId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserInfoFromKugou_args(getUserInfoFromKugou_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
    }

    public getUserInfoFromKugou_args deepCopy() {
      return new getUserInfoFromKugou_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public getUserInfoFromKugou_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserInfoFromKugou_args)
        return this.equals((getUserInfoFromKugou_args)that);
      return false;
    }

    public boolean equals(getUserInfoFromKugou_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserInfoFromKugou_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserInfoFromKugou_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserInfoFromKugou_argsStandardSchemeFactory implements SchemeFactory {
      public getUserInfoFromKugou_argsStandardScheme getScheme() {
        return new getUserInfoFromKugou_argsStandardScheme();
      }
    }

    private static class getUserInfoFromKugou_argsStandardScheme extends StandardScheme<getUserInfoFromKugou_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserInfoFromKugou_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserInfoFromKugou_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserInfoFromKugou_argsTupleSchemeFactory implements SchemeFactory {
      public getUserInfoFromKugou_argsTupleScheme getScheme() {
        return new getUserInfoFromKugou_argsTupleScheme();
      }
    }

    private static class getUserInfoFromKugou_argsTupleScheme extends TupleScheme<getUserInfoFromKugou_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserInfoFromKugou_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouId()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetKugouId()) {
          oprot.writeI64(struct.kugouId);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserInfoFromKugou_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.kugouId = iprot.readI64();
          struct.setKugouIdIsSet(true);
        }
      }
    }

  }

  public static class getUserInfoFromKugou_result implements org.apache.thrift.TBase<getUserInfoFromKugou_result, getUserInfoFromKugou_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUserInfoFromKugou_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserInfoFromKugou_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserInfoFromKugou_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserInfoFromKugou_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResMsg.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserInfoFromKugou_result.class, metaDataMap);
    }

    public getUserInfoFromKugou_result() {
    }

    public getUserInfoFromKugou_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResMsg success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserInfoFromKugou_result(getUserInfoFromKugou_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMsg(other.success);
      }
    }

    public getUserInfoFromKugou_result deepCopy() {
      return new getUserInfoFromKugou_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg getSuccess() {
      return this.success;
    }

    public getUserInfoFromKugou_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResMsg success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResMsg)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserInfoFromKugou_result)
        return this.equals((getUserInfoFromKugou_result)that);
      return false;
    }

    public boolean equals(getUserInfoFromKugou_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserInfoFromKugou_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserInfoFromKugou_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserInfoFromKugou_resultStandardSchemeFactory implements SchemeFactory {
      public getUserInfoFromKugou_resultStandardScheme getScheme() {
        return new getUserInfoFromKugou_resultStandardScheme();
      }
    }

    private static class getUserInfoFromKugou_resultStandardScheme extends StandardScheme<getUserInfoFromKugou_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserInfoFromKugou_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMsg();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserInfoFromKugou_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserInfoFromKugou_resultTupleSchemeFactory implements SchemeFactory {
      public getUserInfoFromKugou_resultTupleScheme getScheme() {
        return new getUserInfoFromKugou_resultTupleScheme();
      }
    }

    private static class getUserInfoFromKugou_resultTupleScheme extends TupleScheme<getUserInfoFromKugou_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserInfoFromKugou_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserInfoFromKugou_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMsg();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUserInfoFromKugouV1_args implements org.apache.thrift.TBase<getUserInfoFromKugouV1_args, getUserInfoFromKugouV1_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUserInfoFromKugouV1_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserInfoFromKugouV1_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserInfoFromKugouV1_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserInfoFromKugouV1_argsTupleSchemeFactory());
    }

    public long kugouId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserInfoFromKugouV1_args.class, metaDataMap);
    }

    public getUserInfoFromKugouV1_args() {
    }

    public getUserInfoFromKugouV1_args(
      long kugouId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserInfoFromKugouV1_args(getUserInfoFromKugouV1_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
    }

    public getUserInfoFromKugouV1_args deepCopy() {
      return new getUserInfoFromKugouV1_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public getUserInfoFromKugouV1_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserInfoFromKugouV1_args)
        return this.equals((getUserInfoFromKugouV1_args)that);
      return false;
    }

    public boolean equals(getUserInfoFromKugouV1_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserInfoFromKugouV1_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserInfoFromKugouV1_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserInfoFromKugouV1_argsStandardSchemeFactory implements SchemeFactory {
      public getUserInfoFromKugouV1_argsStandardScheme getScheme() {
        return new getUserInfoFromKugouV1_argsStandardScheme();
      }
    }

    private static class getUserInfoFromKugouV1_argsStandardScheme extends StandardScheme<getUserInfoFromKugouV1_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserInfoFromKugouV1_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserInfoFromKugouV1_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserInfoFromKugouV1_argsTupleSchemeFactory implements SchemeFactory {
      public getUserInfoFromKugouV1_argsTupleScheme getScheme() {
        return new getUserInfoFromKugouV1_argsTupleScheme();
      }
    }

    private static class getUserInfoFromKugouV1_argsTupleScheme extends TupleScheme<getUserInfoFromKugouV1_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserInfoFromKugouV1_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouId()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetKugouId()) {
          oprot.writeI64(struct.kugouId);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserInfoFromKugouV1_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.kugouId = iprot.readI64();
          struct.setKugouIdIsSet(true);
        }
      }
    }

  }

  public static class getUserInfoFromKugouV1_result implements org.apache.thrift.TBase<getUserInfoFromKugouV1_result, getUserInfoFromKugouV1_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUserInfoFromKugouV1_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserInfoFromKugouV1_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserInfoFromKugouV1_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserInfoFromKugouV1_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResMsg.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserInfoFromKugouV1_result.class, metaDataMap);
    }

    public getUserInfoFromKugouV1_result() {
    }

    public getUserInfoFromKugouV1_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResMsg success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserInfoFromKugouV1_result(getUserInfoFromKugouV1_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMsg(other.success);
      }
    }

    public getUserInfoFromKugouV1_result deepCopy() {
      return new getUserInfoFromKugouV1_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResMsg getSuccess() {
      return this.success;
    }

    public getUserInfoFromKugouV1_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResMsg success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResMsg)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserInfoFromKugouV1_result)
        return this.equals((getUserInfoFromKugouV1_result)that);
      return false;
    }

    public boolean equals(getUserInfoFromKugouV1_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserInfoFromKugouV1_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserInfoFromKugouV1_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserInfoFromKugouV1_resultStandardSchemeFactory implements SchemeFactory {
      public getUserInfoFromKugouV1_resultStandardScheme getScheme() {
        return new getUserInfoFromKugouV1_resultStandardScheme();
      }
    }

    private static class getUserInfoFromKugouV1_resultStandardScheme extends StandardScheme<getUserInfoFromKugouV1_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserInfoFromKugouV1_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMsg();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserInfoFromKugouV1_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserInfoFromKugouV1_resultTupleSchemeFactory implements SchemeFactory {
      public getUserInfoFromKugouV1_resultTupleScheme getScheme() {
        return new getUserInfoFromKugouV1_resultTupleScheme();
      }
    }

    private static class getUserInfoFromKugouV1_resultTupleScheme extends TupleScheme<getUserInfoFromKugouV1_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserInfoFromKugouV1_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserInfoFromKugouV1_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResMsg();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class sendCaptcha_args implements org.apache.thrift.TBase<sendCaptcha_args, sendCaptcha_args._Fields>, java.io.Serializable, Cloneable, Comparable<sendCaptcha_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("sendCaptcha_args");

    private static final org.apache.thrift.protocol.TField REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("request", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new sendCaptcha_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new sendCaptcha_argsTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.UserCaptchaVo request; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      REQUEST((short)1, "request");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // REQUEST
            return REQUEST;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.REQUEST, new org.apache.thrift.meta_data.FieldMetaData("request", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.UserCaptchaVo.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(sendCaptcha_args.class, metaDataMap);
    }

    public sendCaptcha_args() {
    }

    public sendCaptcha_args(
      com.kugou.fanxing.thrift.plat.user.vo.UserCaptchaVo request)
    {
      this();
      this.request = request;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public sendCaptcha_args(sendCaptcha_args other) {
      if (other.isSetRequest()) {
        this.request = new com.kugou.fanxing.thrift.plat.user.vo.UserCaptchaVo(other.request);
      }
    }

    public sendCaptcha_args deepCopy() {
      return new sendCaptcha_args(this);
    }

    @Override
    public void clear() {
      this.request = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.UserCaptchaVo getRequest() {
      return this.request;
    }

    public sendCaptcha_args setRequest(com.kugou.fanxing.thrift.plat.user.vo.UserCaptchaVo request) {
      this.request = request;
      return this;
    }

    public void unsetRequest() {
      this.request = null;
    }

    /** Returns true if field request is set (has been assigned a value) and false otherwise */
    public boolean isSetRequest() {
      return this.request != null;
    }

    public void setRequestIsSet(boolean value) {
      if (!value) {
        this.request = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case REQUEST:
        if (value == null) {
          unsetRequest();
        } else {
          setRequest((com.kugou.fanxing.thrift.plat.user.vo.UserCaptchaVo)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case REQUEST:
        return getRequest();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case REQUEST:
        return isSetRequest();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof sendCaptcha_args)
        return this.equals((sendCaptcha_args)that);
      return false;
    }

    public boolean equals(sendCaptcha_args that) {
      if (that == null)
        return false;

      boolean this_present_request = true && this.isSetRequest();
      boolean that_present_request = true && that.isSetRequest();
      if (this_present_request || that_present_request) {
        if (!(this_present_request && that_present_request))
          return false;
        if (!this.request.equals(that.request))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_request = true && (isSetRequest());
      list.add(present_request);
      if (present_request)
        list.add(request);

      return list.hashCode();
    }

    @Override
    public int compareTo(sendCaptcha_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.request, other.request);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("sendCaptcha_args(");
      boolean first = true;

      sb.append("request:");
      if (this.request == null) {
        sb.append("null");
      } else {
        sb.append(this.request);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (request != null) {
        request.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class sendCaptcha_argsStandardSchemeFactory implements SchemeFactory {
      public sendCaptcha_argsStandardScheme getScheme() {
        return new sendCaptcha_argsStandardScheme();
      }
    }

    private static class sendCaptcha_argsStandardScheme extends StandardScheme<sendCaptcha_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, sendCaptcha_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.request = new com.kugou.fanxing.thrift.plat.user.vo.UserCaptchaVo();
                struct.request.read(iprot);
                struct.setRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, sendCaptcha_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.request != null) {
          oprot.writeFieldBegin(REQUEST_FIELD_DESC);
          struct.request.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class sendCaptcha_argsTupleSchemeFactory implements SchemeFactory {
      public sendCaptcha_argsTupleScheme getScheme() {
        return new sendCaptcha_argsTupleScheme();
      }
    }

    private static class sendCaptcha_argsTupleScheme extends TupleScheme<sendCaptcha_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, sendCaptcha_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetRequest()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetRequest()) {
          struct.request.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, sendCaptcha_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.request = new com.kugou.fanxing.thrift.plat.user.vo.UserCaptchaVo();
          struct.request.read(iprot);
          struct.setRequestIsSet(true);
        }
      }
    }

  }

  public static class sendCaptcha_result implements org.apache.thrift.TBase<sendCaptcha_result, sendCaptcha_result._Fields>, java.io.Serializable, Cloneable, Comparable<sendCaptcha_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("sendCaptcha_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new sendCaptcha_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new sendCaptcha_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(sendCaptcha_result.class, metaDataMap);
    }

    public sendCaptcha_result() {
    }

    public sendCaptcha_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public sendCaptcha_result(sendCaptcha_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse(other.success);
      }
    }

    public sendCaptcha_result deepCopy() {
      return new sendCaptcha_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse getSuccess() {
      return this.success;
    }

    public sendCaptcha_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof sendCaptcha_result)
        return this.equals((sendCaptcha_result)that);
      return false;
    }

    public boolean equals(sendCaptcha_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(sendCaptcha_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("sendCaptcha_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class sendCaptcha_resultStandardSchemeFactory implements SchemeFactory {
      public sendCaptcha_resultStandardScheme getScheme() {
        return new sendCaptcha_resultStandardScheme();
      }
    }

    private static class sendCaptcha_resultStandardScheme extends StandardScheme<sendCaptcha_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, sendCaptcha_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, sendCaptcha_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class sendCaptcha_resultTupleSchemeFactory implements SchemeFactory {
      public sendCaptcha_resultTupleScheme getScheme() {
        return new sendCaptcha_resultTupleScheme();
      }
    }

    private static class sendCaptcha_resultTupleScheme extends TupleScheme<sendCaptcha_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, sendCaptcha_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, sendCaptcha_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUserCaptchaResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class checkCaptcha_args implements org.apache.thrift.TBase<checkCaptcha_args, checkCaptcha_args._Fields>, java.io.Serializable, Cloneable, Comparable<checkCaptcha_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("checkCaptcha_args");

    private static final org.apache.thrift.protocol.TField REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("request", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new checkCaptcha_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new checkCaptcha_argsTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.CheckUserCaptchaVo request; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      REQUEST((short)1, "request");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // REQUEST
            return REQUEST;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.REQUEST, new org.apache.thrift.meta_data.FieldMetaData("request", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.CheckUserCaptchaVo.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(checkCaptcha_args.class, metaDataMap);
    }

    public checkCaptcha_args() {
    }

    public checkCaptcha_args(
      com.kugou.fanxing.thrift.plat.user.vo.CheckUserCaptchaVo request)
    {
      this();
      this.request = request;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public checkCaptcha_args(checkCaptcha_args other) {
      if (other.isSetRequest()) {
        this.request = new com.kugou.fanxing.thrift.plat.user.vo.CheckUserCaptchaVo(other.request);
      }
    }

    public checkCaptcha_args deepCopy() {
      return new checkCaptcha_args(this);
    }

    @Override
    public void clear() {
      this.request = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.CheckUserCaptchaVo getRequest() {
      return this.request;
    }

    public checkCaptcha_args setRequest(com.kugou.fanxing.thrift.plat.user.vo.CheckUserCaptchaVo request) {
      this.request = request;
      return this;
    }

    public void unsetRequest() {
      this.request = null;
    }

    /** Returns true if field request is set (has been assigned a value) and false otherwise */
    public boolean isSetRequest() {
      return this.request != null;
    }

    public void setRequestIsSet(boolean value) {
      if (!value) {
        this.request = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case REQUEST:
        if (value == null) {
          unsetRequest();
        } else {
          setRequest((com.kugou.fanxing.thrift.plat.user.vo.CheckUserCaptchaVo)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case REQUEST:
        return getRequest();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case REQUEST:
        return isSetRequest();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof checkCaptcha_args)
        return this.equals((checkCaptcha_args)that);
      return false;
    }

    public boolean equals(checkCaptcha_args that) {
      if (that == null)
        return false;

      boolean this_present_request = true && this.isSetRequest();
      boolean that_present_request = true && that.isSetRequest();
      if (this_present_request || that_present_request) {
        if (!(this_present_request && that_present_request))
          return false;
        if (!this.request.equals(that.request))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_request = true && (isSetRequest());
      list.add(present_request);
      if (present_request)
        list.add(request);

      return list.hashCode();
    }

    @Override
    public int compareTo(checkCaptcha_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.request, other.request);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("checkCaptcha_args(");
      boolean first = true;

      sb.append("request:");
      if (this.request == null) {
        sb.append("null");
      } else {
        sb.append(this.request);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (request != null) {
        request.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class checkCaptcha_argsStandardSchemeFactory implements SchemeFactory {
      public checkCaptcha_argsStandardScheme getScheme() {
        return new checkCaptcha_argsStandardScheme();
      }
    }

    private static class checkCaptcha_argsStandardScheme extends StandardScheme<checkCaptcha_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, checkCaptcha_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.request = new com.kugou.fanxing.thrift.plat.user.vo.CheckUserCaptchaVo();
                struct.request.read(iprot);
                struct.setRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, checkCaptcha_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.request != null) {
          oprot.writeFieldBegin(REQUEST_FIELD_DESC);
          struct.request.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class checkCaptcha_argsTupleSchemeFactory implements SchemeFactory {
      public checkCaptcha_argsTupleScheme getScheme() {
        return new checkCaptcha_argsTupleScheme();
      }
    }

    private static class checkCaptcha_argsTupleScheme extends TupleScheme<checkCaptcha_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, checkCaptcha_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetRequest()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetRequest()) {
          struct.request.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, checkCaptcha_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.request = new com.kugou.fanxing.thrift.plat.user.vo.CheckUserCaptchaVo();
          struct.request.read(iprot);
          struct.setRequestIsSet(true);
        }
      }
    }

  }

  public static class checkCaptcha_result implements org.apache.thrift.TBase<checkCaptcha_result, checkCaptcha_result._Fields>, java.io.Serializable, Cloneable, Comparable<checkCaptcha_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("checkCaptcha_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new checkCaptcha_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new checkCaptcha_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(checkCaptcha_result.class, metaDataMap);
    }

    public checkCaptcha_result() {
    }

    public checkCaptcha_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public checkCaptcha_result(checkCaptcha_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse(other.success);
      }
    }

    public checkCaptcha_result deepCopy() {
      return new checkCaptcha_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse getSuccess() {
      return this.success;
    }

    public checkCaptcha_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof checkCaptcha_result)
        return this.equals((checkCaptcha_result)that);
      return false;
    }

    public boolean equals(checkCaptcha_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(checkCaptcha_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("checkCaptcha_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class checkCaptcha_resultStandardSchemeFactory implements SchemeFactory {
      public checkCaptcha_resultStandardScheme getScheme() {
        return new checkCaptcha_resultStandardScheme();
      }
    }

    private static class checkCaptcha_resultStandardScheme extends StandardScheme<checkCaptcha_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, checkCaptcha_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, checkCaptcha_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class checkCaptcha_resultTupleSchemeFactory implements SchemeFactory {
      public checkCaptcha_resultTupleScheme getScheme() {
        return new checkCaptcha_resultTupleScheme();
      }
    }

    private static class checkCaptcha_resultTupleScheme extends TupleScheme<checkCaptcha_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, checkCaptcha_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, checkCaptcha_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCheckUserCaptchaResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class bindPhone_args implements org.apache.thrift.TBase<bindPhone_args, bindPhone_args._Fields>, java.io.Serializable, Cloneable, Comparable<bindPhone_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("bindPhone_args");

    private static final org.apache.thrift.protocol.TField REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("request", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new bindPhone_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new bindPhone_argsTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.BindMobilePhoneVo request; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      REQUEST((short)1, "request");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // REQUEST
            return REQUEST;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.REQUEST, new org.apache.thrift.meta_data.FieldMetaData("request", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.BindMobilePhoneVo.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(bindPhone_args.class, metaDataMap);
    }

    public bindPhone_args() {
    }

    public bindPhone_args(
      com.kugou.fanxing.thrift.plat.user.vo.BindMobilePhoneVo request)
    {
      this();
      this.request = request;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public bindPhone_args(bindPhone_args other) {
      if (other.isSetRequest()) {
        this.request = new com.kugou.fanxing.thrift.plat.user.vo.BindMobilePhoneVo(other.request);
      }
    }

    public bindPhone_args deepCopy() {
      return new bindPhone_args(this);
    }

    @Override
    public void clear() {
      this.request = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.BindMobilePhoneVo getRequest() {
      return this.request;
    }

    public bindPhone_args setRequest(com.kugou.fanxing.thrift.plat.user.vo.BindMobilePhoneVo request) {
      this.request = request;
      return this;
    }

    public void unsetRequest() {
      this.request = null;
    }

    /** Returns true if field request is set (has been assigned a value) and false otherwise */
    public boolean isSetRequest() {
      return this.request != null;
    }

    public void setRequestIsSet(boolean value) {
      if (!value) {
        this.request = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case REQUEST:
        if (value == null) {
          unsetRequest();
        } else {
          setRequest((com.kugou.fanxing.thrift.plat.user.vo.BindMobilePhoneVo)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case REQUEST:
        return getRequest();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case REQUEST:
        return isSetRequest();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof bindPhone_args)
        return this.equals((bindPhone_args)that);
      return false;
    }

    public boolean equals(bindPhone_args that) {
      if (that == null)
        return false;

      boolean this_present_request = true && this.isSetRequest();
      boolean that_present_request = true && that.isSetRequest();
      if (this_present_request || that_present_request) {
        if (!(this_present_request && that_present_request))
          return false;
        if (!this.request.equals(that.request))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_request = true && (isSetRequest());
      list.add(present_request);
      if (present_request)
        list.add(request);

      return list.hashCode();
    }

    @Override
    public int compareTo(bindPhone_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.request, other.request);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("bindPhone_args(");
      boolean first = true;

      sb.append("request:");
      if (this.request == null) {
        sb.append("null");
      } else {
        sb.append(this.request);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (request != null) {
        request.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class bindPhone_argsStandardSchemeFactory implements SchemeFactory {
      public bindPhone_argsStandardScheme getScheme() {
        return new bindPhone_argsStandardScheme();
      }
    }

    private static class bindPhone_argsStandardScheme extends StandardScheme<bindPhone_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, bindPhone_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.request = new com.kugou.fanxing.thrift.plat.user.vo.BindMobilePhoneVo();
                struct.request.read(iprot);
                struct.setRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, bindPhone_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.request != null) {
          oprot.writeFieldBegin(REQUEST_FIELD_DESC);
          struct.request.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class bindPhone_argsTupleSchemeFactory implements SchemeFactory {
      public bindPhone_argsTupleScheme getScheme() {
        return new bindPhone_argsTupleScheme();
      }
    }

    private static class bindPhone_argsTupleScheme extends TupleScheme<bindPhone_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, bindPhone_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetRequest()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetRequest()) {
          struct.request.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, bindPhone_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.request = new com.kugou.fanxing.thrift.plat.user.vo.BindMobilePhoneVo();
          struct.request.read(iprot);
          struct.setRequestIsSet(true);
        }
      }
    }

  }

  public static class bindPhone_result implements org.apache.thrift.TBase<bindPhone_result, bindPhone_result._Fields>, java.io.Serializable, Cloneable, Comparable<bindPhone_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("bindPhone_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new bindPhone_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new bindPhone_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(bindPhone_result.class, metaDataMap);
    }

    public bindPhone_result() {
    }

    public bindPhone_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public bindPhone_result(bindPhone_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse(other.success);
      }
    }

    public bindPhone_result deepCopy() {
      return new bindPhone_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse getSuccess() {
      return this.success;
    }

    public bindPhone_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof bindPhone_result)
        return this.equals((bindPhone_result)that);
      return false;
    }

    public boolean equals(bindPhone_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(bindPhone_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("bindPhone_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class bindPhone_resultStandardSchemeFactory implements SchemeFactory {
      public bindPhone_resultStandardScheme getScheme() {
        return new bindPhone_resultStandardScheme();
      }
    }

    private static class bindPhone_resultStandardScheme extends StandardScheme<bindPhone_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, bindPhone_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, bindPhone_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class bindPhone_resultTupleSchemeFactory implements SchemeFactory {
      public bindPhone_resultTupleScheme getScheme() {
        return new bindPhone_resultTupleScheme();
      }
    }

    private static class bindPhone_resultTupleScheme extends TupleScheme<bindPhone_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, bindPhone_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, bindPhone_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResBindMobilePhoneResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class checkBindPhoneInfo_args implements org.apache.thrift.TBase<checkBindPhoneInfo_args, checkBindPhoneInfo_args._Fields>, java.io.Serializable, Cloneable, Comparable<checkBindPhoneInfo_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("checkBindPhoneInfo_args");

    private static final org.apache.thrift.protocol.TField REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("request", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new checkBindPhoneInfo_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new checkBindPhoneInfo_argsTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.CheckBindMobilePhoneVo request; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      REQUEST((short)1, "request");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // REQUEST
            return REQUEST;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.REQUEST, new org.apache.thrift.meta_data.FieldMetaData("request", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.CheckBindMobilePhoneVo.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(checkBindPhoneInfo_args.class, metaDataMap);
    }

    public checkBindPhoneInfo_args() {
    }

    public checkBindPhoneInfo_args(
      com.kugou.fanxing.thrift.plat.user.vo.CheckBindMobilePhoneVo request)
    {
      this();
      this.request = request;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public checkBindPhoneInfo_args(checkBindPhoneInfo_args other) {
      if (other.isSetRequest()) {
        this.request = new com.kugou.fanxing.thrift.plat.user.vo.CheckBindMobilePhoneVo(other.request);
      }
    }

    public checkBindPhoneInfo_args deepCopy() {
      return new checkBindPhoneInfo_args(this);
    }

    @Override
    public void clear() {
      this.request = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.CheckBindMobilePhoneVo getRequest() {
      return this.request;
    }

    public checkBindPhoneInfo_args setRequest(com.kugou.fanxing.thrift.plat.user.vo.CheckBindMobilePhoneVo request) {
      this.request = request;
      return this;
    }

    public void unsetRequest() {
      this.request = null;
    }

    /** Returns true if field request is set (has been assigned a value) and false otherwise */
    public boolean isSetRequest() {
      return this.request != null;
    }

    public void setRequestIsSet(boolean value) {
      if (!value) {
        this.request = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case REQUEST:
        if (value == null) {
          unsetRequest();
        } else {
          setRequest((com.kugou.fanxing.thrift.plat.user.vo.CheckBindMobilePhoneVo)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case REQUEST:
        return getRequest();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case REQUEST:
        return isSetRequest();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof checkBindPhoneInfo_args)
        return this.equals((checkBindPhoneInfo_args)that);
      return false;
    }

    public boolean equals(checkBindPhoneInfo_args that) {
      if (that == null)
        return false;

      boolean this_present_request = true && this.isSetRequest();
      boolean that_present_request = true && that.isSetRequest();
      if (this_present_request || that_present_request) {
        if (!(this_present_request && that_present_request))
          return false;
        if (!this.request.equals(that.request))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_request = true && (isSetRequest());
      list.add(present_request);
      if (present_request)
        list.add(request);

      return list.hashCode();
    }

    @Override
    public int compareTo(checkBindPhoneInfo_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.request, other.request);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("checkBindPhoneInfo_args(");
      boolean first = true;

      sb.append("request:");
      if (this.request == null) {
        sb.append("null");
      } else {
        sb.append(this.request);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (request != null) {
        request.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class checkBindPhoneInfo_argsStandardSchemeFactory implements SchemeFactory {
      public checkBindPhoneInfo_argsStandardScheme getScheme() {
        return new checkBindPhoneInfo_argsStandardScheme();
      }
    }

    private static class checkBindPhoneInfo_argsStandardScheme extends StandardScheme<checkBindPhoneInfo_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, checkBindPhoneInfo_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.request = new com.kugou.fanxing.thrift.plat.user.vo.CheckBindMobilePhoneVo();
                struct.request.read(iprot);
                struct.setRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, checkBindPhoneInfo_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.request != null) {
          oprot.writeFieldBegin(REQUEST_FIELD_DESC);
          struct.request.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class checkBindPhoneInfo_argsTupleSchemeFactory implements SchemeFactory {
      public checkBindPhoneInfo_argsTupleScheme getScheme() {
        return new checkBindPhoneInfo_argsTupleScheme();
      }
    }

    private static class checkBindPhoneInfo_argsTupleScheme extends TupleScheme<checkBindPhoneInfo_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, checkBindPhoneInfo_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetRequest()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetRequest()) {
          struct.request.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, checkBindPhoneInfo_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.request = new com.kugou.fanxing.thrift.plat.user.vo.CheckBindMobilePhoneVo();
          struct.request.read(iprot);
          struct.setRequestIsSet(true);
        }
      }
    }

  }

  public static class checkBindPhoneInfo_result implements org.apache.thrift.TBase<checkBindPhoneInfo_result, checkBindPhoneInfo_result._Fields>, java.io.Serializable, Cloneable, Comparable<checkBindPhoneInfo_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("checkBindPhoneInfo_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new checkBindPhoneInfo_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new checkBindPhoneInfo_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(checkBindPhoneInfo_result.class, metaDataMap);
    }

    public checkBindPhoneInfo_result() {
    }

    public checkBindPhoneInfo_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public checkBindPhoneInfo_result(checkBindPhoneInfo_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse(other.success);
      }
    }

    public checkBindPhoneInfo_result deepCopy() {
      return new checkBindPhoneInfo_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse getSuccess() {
      return this.success;
    }

    public checkBindPhoneInfo_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof checkBindPhoneInfo_result)
        return this.equals((checkBindPhoneInfo_result)that);
      return false;
    }

    public boolean equals(checkBindPhoneInfo_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(checkBindPhoneInfo_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("checkBindPhoneInfo_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class checkBindPhoneInfo_resultStandardSchemeFactory implements SchemeFactory {
      public checkBindPhoneInfo_resultStandardScheme getScheme() {
        return new checkBindPhoneInfo_resultStandardScheme();
      }
    }

    private static class checkBindPhoneInfo_resultStandardScheme extends StandardScheme<checkBindPhoneInfo_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, checkBindPhoneInfo_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, checkBindPhoneInfo_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class checkBindPhoneInfo_resultTupleSchemeFactory implements SchemeFactory {
      public checkBindPhoneInfo_resultTupleScheme getScheme() {
        return new checkBindPhoneInfo_resultTupleScheme();
      }
    }

    private static class checkBindPhoneInfo_resultTupleScheme extends TupleScheme<checkBindPhoneInfo_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, checkBindPhoneInfo_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, checkBindPhoneInfo_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResCheckBindMobilePhoneResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUserCancelStatus_args implements org.apache.thrift.TBase<getUserCancelStatus_args, getUserCancelStatus_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUserCancelStatus_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserCancelStatus_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserCancelStatus_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserCancelStatus_argsTupleSchemeFactory());
    }

    public long kugouId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserCancelStatus_args.class, metaDataMap);
    }

    public getUserCancelStatus_args() {
    }

    public getUserCancelStatus_args(
      long kugouId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserCancelStatus_args(getUserCancelStatus_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
    }

    public getUserCancelStatus_args deepCopy() {
      return new getUserCancelStatus_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public getUserCancelStatus_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserCancelStatus_args)
        return this.equals((getUserCancelStatus_args)that);
      return false;
    }

    public boolean equals(getUserCancelStatus_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserCancelStatus_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserCancelStatus_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserCancelStatus_argsStandardSchemeFactory implements SchemeFactory {
      public getUserCancelStatus_argsStandardScheme getScheme() {
        return new getUserCancelStatus_argsStandardScheme();
      }
    }

    private static class getUserCancelStatus_argsStandardScheme extends StandardScheme<getUserCancelStatus_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserCancelStatus_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserCancelStatus_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserCancelStatus_argsTupleSchemeFactory implements SchemeFactory {
      public getUserCancelStatus_argsTupleScheme getScheme() {
        return new getUserCancelStatus_argsTupleScheme();
      }
    }

    private static class getUserCancelStatus_argsTupleScheme extends TupleScheme<getUserCancelStatus_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserCancelStatus_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouId()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetKugouId()) {
          oprot.writeI64(struct.kugouId);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserCancelStatus_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.kugouId = iprot.readI64();
          struct.setKugouIdIsSet(true);
        }
      }
    }

  }

  public static class getUserCancelStatus_result implements org.apache.thrift.TBase<getUserCancelStatus_result, getUserCancelStatus_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUserCancelStatus_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserCancelStatus_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserCancelStatus_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserCancelStatus_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserCancelStatus_result.class, metaDataMap);
    }

    public getUserCancelStatus_result() {
    }

    public getUserCancelStatus_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserCancelStatus_result(getUserCancelStatus_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse(other.success);
      }
    }

    public getUserCancelStatus_result deepCopy() {
      return new getUserCancelStatus_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse getSuccess() {
      return this.success;
    }

    public getUserCancelStatus_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserCancelStatus_result)
        return this.equals((getUserCancelStatus_result)that);
      return false;
    }

    public boolean equals(getUserCancelStatus_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserCancelStatus_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserCancelStatus_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserCancelStatus_resultStandardSchemeFactory implements SchemeFactory {
      public getUserCancelStatus_resultStandardScheme getScheme() {
        return new getUserCancelStatus_resultStandardScheme();
      }
    }

    private static class getUserCancelStatus_resultStandardScheme extends StandardScheme<getUserCancelStatus_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserCancelStatus_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserCancelStatus_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserCancelStatus_resultTupleSchemeFactory implements SchemeFactory {
      public getUserCancelStatus_resultTupleScheme getScheme() {
        return new getUserCancelStatus_resultTupleScheme();
      }
    }

    private static class getUserCancelStatus_resultTupleScheme extends TupleScheme<getUserCancelStatus_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserCancelStatus_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserCancelStatus_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUserCancelStatusResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class batchGetUserBirthDay_args implements org.apache.thrift.TBase<batchGetUserBirthDay_args, batchGetUserBirthDay_args._Fields>, java.io.Serializable, Cloneable, Comparable<batchGetUserBirthDay_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("batchGetUserBirthDay_args");

    private static final org.apache.thrift.protocol.TField KUGOU_IDS_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouIds", org.apache.thrift.protocol.TType.LIST, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new batchGetUserBirthDay_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new batchGetUserBirthDay_argsTupleSchemeFactory());
    }

    public List<Long> kugouIds; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_IDS((short)1, "kugouIds");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_IDS
            return KUGOU_IDS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_IDS, new org.apache.thrift.meta_data.FieldMetaData("kugouIds", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
              new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(batchGetUserBirthDay_args.class, metaDataMap);
    }

    public batchGetUserBirthDay_args() {
    }

    public batchGetUserBirthDay_args(
      List<Long> kugouIds)
    {
      this();
      this.kugouIds = kugouIds;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public batchGetUserBirthDay_args(batchGetUserBirthDay_args other) {
      if (other.isSetKugouIds()) {
        List<Long> __this__kugouIds = new ArrayList<Long>(other.kugouIds);
        this.kugouIds = __this__kugouIds;
      }
    }

    public batchGetUserBirthDay_args deepCopy() {
      return new batchGetUserBirthDay_args(this);
    }

    @Override
    public void clear() {
      this.kugouIds = null;
    }

    public int getKugouIdsSize() {
      return (this.kugouIds == null) ? 0 : this.kugouIds.size();
    }

    public java.util.Iterator<Long> getKugouIdsIterator() {
      return (this.kugouIds == null) ? null : this.kugouIds.iterator();
    }

    public void addToKugouIds(long elem) {
      if (this.kugouIds == null) {
        this.kugouIds = new ArrayList<Long>();
      }
      this.kugouIds.add(elem);
    }

    public List<Long> getKugouIds() {
      return this.kugouIds;
    }

    public batchGetUserBirthDay_args setKugouIds(List<Long> kugouIds) {
      this.kugouIds = kugouIds;
      return this;
    }

    public void unsetKugouIds() {
      this.kugouIds = null;
    }

    /** Returns true if field kugouIds is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouIds() {
      return this.kugouIds != null;
    }

    public void setKugouIdsIsSet(boolean value) {
      if (!value) {
        this.kugouIds = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_IDS:
        if (value == null) {
          unsetKugouIds();
        } else {
          setKugouIds((List<Long>)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_IDS:
        return getKugouIds();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_IDS:
        return isSetKugouIds();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof batchGetUserBirthDay_args)
        return this.equals((batchGetUserBirthDay_args)that);
      return false;
    }

    public boolean equals(batchGetUserBirthDay_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouIds = true && this.isSetKugouIds();
      boolean that_present_kugouIds = true && that.isSetKugouIds();
      if (this_present_kugouIds || that_present_kugouIds) {
        if (!(this_present_kugouIds && that_present_kugouIds))
          return false;
        if (!this.kugouIds.equals(that.kugouIds))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouIds = true && (isSetKugouIds());
      list.add(present_kugouIds);
      if (present_kugouIds)
        list.add(kugouIds);

      return list.hashCode();
    }

    @Override
    public int compareTo(batchGetUserBirthDay_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouIds()).compareTo(other.isSetKugouIds());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouIds()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouIds, other.kugouIds);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("batchGetUserBirthDay_args(");
      boolean first = true;

      sb.append("kugouIds:");
      if (this.kugouIds == null) {
        sb.append("null");
      } else {
        sb.append(this.kugouIds);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class batchGetUserBirthDay_argsStandardSchemeFactory implements SchemeFactory {
      public batchGetUserBirthDay_argsStandardScheme getScheme() {
        return new batchGetUserBirthDay_argsStandardScheme();
      }
    }

    private static class batchGetUserBirthDay_argsStandardScheme extends StandardScheme<batchGetUserBirthDay_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, batchGetUserBirthDay_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_IDS
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list48 = iprot.readListBegin();
                  struct.kugouIds = new ArrayList<Long>(_list48.size);
                  long _elem49;
                  for (int _i50 = 0; _i50 < _list48.size; ++_i50)
                  {
                    _elem49 = iprot.readI64();
                    struct.kugouIds.add(_elem49);
                  }
                  iprot.readListEnd();
                }
                struct.setKugouIdsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, batchGetUserBirthDay_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.kugouIds != null) {
          oprot.writeFieldBegin(KUGOU_IDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.kugouIds.size()));
            for (long _iter51 : struct.kugouIds)
            {
              oprot.writeI64(_iter51);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class batchGetUserBirthDay_argsTupleSchemeFactory implements SchemeFactory {
      public batchGetUserBirthDay_argsTupleScheme getScheme() {
        return new batchGetUserBirthDay_argsTupleScheme();
      }
    }

    private static class batchGetUserBirthDay_argsTupleScheme extends TupleScheme<batchGetUserBirthDay_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, batchGetUserBirthDay_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouIds()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetKugouIds()) {
          {
            oprot.writeI32(struct.kugouIds.size());
            for (long _iter52 : struct.kugouIds)
            {
              oprot.writeI64(_iter52);
            }
          }
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, batchGetUserBirthDay_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          {
            org.apache.thrift.protocol.TList _list53 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
            struct.kugouIds = new ArrayList<Long>(_list53.size);
            long _elem54;
            for (int _i55 = 0; _i55 < _list53.size; ++_i55)
            {
              _elem54 = iprot.readI64();
              struct.kugouIds.add(_elem54);
            }
          }
          struct.setKugouIdsIsSet(true);
        }
      }
    }

  }

  public static class batchGetUserBirthDay_result implements org.apache.thrift.TBase<batchGetUserBirthDay_result, batchGetUserBirthDay_result._Fields>, java.io.Serializable, Cloneable, Comparable<batchGetUserBirthDay_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("batchGetUserBirthDay_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new batchGetUserBirthDay_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new batchGetUserBirthDay_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(batchGetUserBirthDay_result.class, metaDataMap);
    }

    public batchGetUserBirthDay_result() {
    }

    public batchGetUserBirthDay_result(
      com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public batchGetUserBirthDay_result(batchGetUserBirthDay_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse(other.success);
      }
    }

    public batchGetUserBirthDay_result deepCopy() {
      return new batchGetUserBirthDay_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse getSuccess() {
      return this.success;
    }

    public batchGetUserBirthDay_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof batchGetUserBirthDay_result)
        return this.equals((batchGetUserBirthDay_result)that);
      return false;
    }

    public boolean equals(batchGetUserBirthDay_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(batchGetUserBirthDay_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("batchGetUserBirthDay_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class batchGetUserBirthDay_resultStandardSchemeFactory implements SchemeFactory {
      public batchGetUserBirthDay_resultStandardScheme getScheme() {
        return new batchGetUserBirthDay_resultStandardScheme();
      }
    }

    private static class batchGetUserBirthDay_resultStandardScheme extends StandardScheme<batchGetUserBirthDay_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, batchGetUserBirthDay_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, batchGetUserBirthDay_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class batchGetUserBirthDay_resultTupleSchemeFactory implements SchemeFactory {
      public batchGetUserBirthDay_resultTupleScheme getScheme() {
        return new batchGetUserBirthDay_resultTupleScheme();
      }
    }

    private static class batchGetUserBirthDay_resultTupleScheme extends TupleScheme<batchGetUserBirthDay_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, batchGetUserBirthDay_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, batchGetUserBirthDay_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.ResUserBirthDayResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class sendSmsByKugouId_args implements org.apache.thrift.TBase<sendSmsByKugouId_args, sendSmsByKugouId_args._Fields>, java.io.Serializable, Cloneable, Comparable<sendSmsByKugouId_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("sendSmsByKugouId_args");

    private static final org.apache.thrift.protocol.TField REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("request", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new sendSmsByKugouId_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new sendSmsByKugouId_argsTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq request; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      REQUEST((short)1, "request");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // REQUEST
            return REQUEST;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.REQUEST, new org.apache.thrift.meta_data.FieldMetaData("request", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(sendSmsByKugouId_args.class, metaDataMap);
    }

    public sendSmsByKugouId_args() {
    }

    public sendSmsByKugouId_args(
      com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq request)
    {
      this();
      this.request = request;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public sendSmsByKugouId_args(sendSmsByKugouId_args other) {
      if (other.isSetRequest()) {
        this.request = new com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq(other.request);
      }
    }

    public sendSmsByKugouId_args deepCopy() {
      return new sendSmsByKugouId_args(this);
    }

    @Override
    public void clear() {
      this.request = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq getRequest() {
      return this.request;
    }

    public sendSmsByKugouId_args setRequest(com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq request) {
      this.request = request;
      return this;
    }

    public void unsetRequest() {
      this.request = null;
    }

    /** Returns true if field request is set (has been assigned a value) and false otherwise */
    public boolean isSetRequest() {
      return this.request != null;
    }

    public void setRequestIsSet(boolean value) {
      if (!value) {
        this.request = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case REQUEST:
        if (value == null) {
          unsetRequest();
        } else {
          setRequest((com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case REQUEST:
        return getRequest();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case REQUEST:
        return isSetRequest();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof sendSmsByKugouId_args)
        return this.equals((sendSmsByKugouId_args)that);
      return false;
    }

    public boolean equals(sendSmsByKugouId_args that) {
      if (that == null)
        return false;

      boolean this_present_request = true && this.isSetRequest();
      boolean that_present_request = true && that.isSetRequest();
      if (this_present_request || that_present_request) {
        if (!(this_present_request && that_present_request))
          return false;
        if (!this.request.equals(that.request))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_request = true && (isSetRequest());
      list.add(present_request);
      if (present_request)
        list.add(request);

      return list.hashCode();
    }

    @Override
    public int compareTo(sendSmsByKugouId_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.request, other.request);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("sendSmsByKugouId_args(");
      boolean first = true;

      sb.append("request:");
      if (this.request == null) {
        sb.append("null");
      } else {
        sb.append(this.request);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (request != null) {
        request.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class sendSmsByKugouId_argsStandardSchemeFactory implements SchemeFactory {
      public sendSmsByKugouId_argsStandardScheme getScheme() {
        return new sendSmsByKugouId_argsStandardScheme();
      }
    }

    private static class sendSmsByKugouId_argsStandardScheme extends StandardScheme<sendSmsByKugouId_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, sendSmsByKugouId_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.request = new com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq();
                struct.request.read(iprot);
                struct.setRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, sendSmsByKugouId_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.request != null) {
          oprot.writeFieldBegin(REQUEST_FIELD_DESC);
          struct.request.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class sendSmsByKugouId_argsTupleSchemeFactory implements SchemeFactory {
      public sendSmsByKugouId_argsTupleScheme getScheme() {
        return new sendSmsByKugouId_argsTupleScheme();
      }
    }

    private static class sendSmsByKugouId_argsTupleScheme extends TupleScheme<sendSmsByKugouId_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, sendSmsByKugouId_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetRequest()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetRequest()) {
          struct.request.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, sendSmsByKugouId_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.request = new com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdReq();
          struct.request.read(iprot);
          struct.setRequestIsSet(true);
        }
      }
    }

  }

  public static class sendSmsByKugouId_result implements org.apache.thrift.TBase<sendSmsByKugouId_result, sendSmsByKugouId_result._Fields>, java.io.Serializable, Cloneable, Comparable<sendSmsByKugouId_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("sendSmsByKugouId_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new sendSmsByKugouId_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new sendSmsByKugouId_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(sendSmsByKugouId_result.class, metaDataMap);
    }

    public sendSmsByKugouId_result() {
    }

    public sendSmsByKugouId_result(
      com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public sendSmsByKugouId_result(sendSmsByKugouId_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp(other.success);
      }
    }

    public sendSmsByKugouId_result deepCopy() {
      return new sendSmsByKugouId_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp getSuccess() {
      return this.success;
    }

    public sendSmsByKugouId_result setSuccess(com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof sendSmsByKugouId_result)
        return this.equals((sendSmsByKugouId_result)that);
      return false;
    }

    public boolean equals(sendSmsByKugouId_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(sendSmsByKugouId_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("sendSmsByKugouId_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class sendSmsByKugouId_resultStandardSchemeFactory implements SchemeFactory {
      public sendSmsByKugouId_resultStandardScheme getScheme() {
        return new sendSmsByKugouId_resultStandardScheme();
      }
    }

    private static class sendSmsByKugouId_resultStandardScheme extends StandardScheme<sendSmsByKugouId_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, sendSmsByKugouId_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, sendSmsByKugouId_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class sendSmsByKugouId_resultTupleSchemeFactory implements SchemeFactory {
      public sendSmsByKugouId_resultTupleScheme getScheme() {
        return new sendSmsByKugouId_resultTupleScheme();
      }
    }

    private static class sendSmsByKugouId_resultTupleScheme extends TupleScheme<sendSmsByKugouId_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, sendSmsByKugouId_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, sendSmsByKugouId_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.plat.user.vo.SendSmsByKugouIdResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

}
