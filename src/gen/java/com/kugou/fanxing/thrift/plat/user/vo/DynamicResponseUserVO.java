/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.thrift.plat.user.vo;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
/**
 * 动态查询用户信息实体
 * 
 */
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-03-05")
public class DynamicResponseUserVO implements org.apache.thrift.TBase<DynamicResponseUserVO, DynamicResponseUserVO._Fields>, java.io.Serializable, Cloneable, Comparable<DynamicResponseUserVO> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("DynamicResponseUserVO");

  private static final org.apache.thrift.protocol.TField EMAIL_FIELD_DESC = new org.apache.thrift.protocol.TField("email", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField USER_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("userName", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField NICK_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("nickName", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField USER_LOGO_FIELD_DESC = new org.apache.thrift.protocol.TField("userLogo", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("status", org.apache.thrift.protocol.TType.I32, (short)5);
  private static final org.apache.thrift.protocol.TField SEX_FIELD_DESC = new org.apache.thrift.protocol.TField("sex", org.apache.thrift.protocol.TType.I32, (short)6);
  private static final org.apache.thrift.protocol.TField FROM_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("fromType", org.apache.thrift.protocol.TType.I32, (short)7);
  private static final org.apache.thrift.protocol.TField ADD_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("addTime", org.apache.thrift.protocol.TType.I32, (short)8);
  private static final org.apache.thrift.protocol.TField REG_IP_FIELD_DESC = new org.apache.thrift.protocol.TField("regIp", org.apache.thrift.protocol.TType.STRING, (short)9);
  private static final org.apache.thrift.protocol.TField CONSTELLATION_FIELD_DESC = new org.apache.thrift.protocol.TField("constellation", org.apache.thrift.protocol.TType.I32, (short)10);
  private static final org.apache.thrift.protocol.TField HEIGHT_FIELD_DESC = new org.apache.thrift.protocol.TField("height", org.apache.thrift.protocol.TType.I32, (short)11);
  private static final org.apache.thrift.protocol.TField WEIGHT_FIELD_DESC = new org.apache.thrift.protocol.TField("weight", org.apache.thrift.protocol.TType.I32, (short)12);
  private static final org.apache.thrift.protocol.TField WEIBO_FIELD_DESC = new org.apache.thrift.protocol.TField("weibo", org.apache.thrift.protocol.TType.STRING, (short)13);
  private static final org.apache.thrift.protocol.TField LOCATION_FIELD_DESC = new org.apache.thrift.protocol.TField("location", org.apache.thrift.protocol.TType.STRING, (short)14);
  private static final org.apache.thrift.protocol.TField BWH_FIELD_DESC = new org.apache.thrift.protocol.TField("bwh", org.apache.thrift.protocol.TType.STRING, (short)15);
  private static final org.apache.thrift.protocol.TField RICH_LEVEL_FIELD_DESC = new org.apache.thrift.protocol.TField("richLevel", org.apache.thrift.protocol.TType.I32, (short)16);
  private static final org.apache.thrift.protocol.TField STAR_LEVEL_FIELD_DESC = new org.apache.thrift.protocol.TField("starLevel", org.apache.thrift.protocol.TType.I32, (short)17);
  private static final org.apache.thrift.protocol.TField PRODUCT_LINE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("productLineId", org.apache.thrift.protocol.TType.I32, (short)18);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new DynamicResponseUserVOStandardSchemeFactory());
    schemes.put(TupleScheme.class, new DynamicResponseUserVOTupleSchemeFactory());
  }

  /**
   *     * email
   * *
   */
  public String email; // optional
  /**
   *     * 用户名
   * *
   */
  public String userName; // optional
  /**
   *     * 昵称
   * *
   */
  public String nickName; // optional
  /**
   *     * 用户logo
   * *
   */
  public String userLogo; // optional
  /**
   *     * 状态(0:正常，1:封号)
   * *
   */
  public int status; // optional
  /**
   *     * 性别(0:保密,1:男,2:女)
   * *
   */
  public int sex; // optional
  /**
   * 来源(0:官网,1:酷狗,2:qq,3:新浪,4:人人,25:天翼)
   * 
   */
  public int fromType; // optional
  /**
   *     * 注册时间
   * *
   */
  public int addTime; // optional
  /**
   *     * 注册ip
   * *
   */
  public String regIp; // optional
  /**
   *     * 星座(0:保密,1:白羊座,2:金牛座,3:双子座,4:巨蟹座,5:狮子座,6:处女座,7:天秤座,8:天蝎座,9:射手座,10:摩羯座,11:水瓶座,12:双鱼座)
   * *
   */
  public int constellation; // optional
  /**
   *     * 身高[厘米]
   * *
   */
  public int height; // optional
  /**
   *     * 体重[公斤]
   * *
   */
  public int weight; // optional
  /**
   *     * 微博
   * *
   */
  public String weibo; // optional
  /**
   * 所在地
   * 
   */
  public String location; // optional
  /**
   * 三围
   * 
   */
  public String bwh; // optional
  /**
   * 财富等级
   * 
   */
  public int richLevel; // optional
  /**
   * 明星等级
   * 
   */
  public int starLevel; // optional
  /**
   * 产品线 0:繁星（系统上线前数据）,1:繁星, 2:KuGou Live, 3:移动直播
   * 
   */
  public int productLineId; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     *     * email
     * *
     */
    EMAIL((short)1, "email"),
    /**
     *     * 用户名
     * *
     */
    USER_NAME((short)2, "userName"),
    /**
     *     * 昵称
     * *
     */
    NICK_NAME((short)3, "nickName"),
    /**
     *     * 用户logo
     * *
     */
    USER_LOGO((short)4, "userLogo"),
    /**
     *     * 状态(0:正常，1:封号)
     * *
     */
    STATUS((short)5, "status"),
    /**
     *     * 性别(0:保密,1:男,2:女)
     * *
     */
    SEX((short)6, "sex"),
    /**
     * 来源(0:官网,1:酷狗,2:qq,3:新浪,4:人人,25:天翼)
     * 
     */
    FROM_TYPE((short)7, "fromType"),
    /**
     *     * 注册时间
     * *
     */
    ADD_TIME((short)8, "addTime"),
    /**
     *     * 注册ip
     * *
     */
    REG_IP((short)9, "regIp"),
    /**
     *     * 星座(0:保密,1:白羊座,2:金牛座,3:双子座,4:巨蟹座,5:狮子座,6:处女座,7:天秤座,8:天蝎座,9:射手座,10:摩羯座,11:水瓶座,12:双鱼座)
     * *
     */
    CONSTELLATION((short)10, "constellation"),
    /**
     *     * 身高[厘米]
     * *
     */
    HEIGHT((short)11, "height"),
    /**
     *     * 体重[公斤]
     * *
     */
    WEIGHT((short)12, "weight"),
    /**
     *     * 微博
     * *
     */
    WEIBO((short)13, "weibo"),
    /**
     * 所在地
     * 
     */
    LOCATION((short)14, "location"),
    /**
     * 三围
     * 
     */
    BWH((short)15, "bwh"),
    /**
     * 财富等级
     * 
     */
    RICH_LEVEL((short)16, "richLevel"),
    /**
     * 明星等级
     * 
     */
    STAR_LEVEL((short)17, "starLevel"),
    /**
     * 产品线 0:繁星（系统上线前数据）,1:繁星, 2:KuGou Live, 3:移动直播
     * 
     */
    PRODUCT_LINE_ID((short)18, "productLineId");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // EMAIL
          return EMAIL;
        case 2: // USER_NAME
          return USER_NAME;
        case 3: // NICK_NAME
          return NICK_NAME;
        case 4: // USER_LOGO
          return USER_LOGO;
        case 5: // STATUS
          return STATUS;
        case 6: // SEX
          return SEX;
        case 7: // FROM_TYPE
          return FROM_TYPE;
        case 8: // ADD_TIME
          return ADD_TIME;
        case 9: // REG_IP
          return REG_IP;
        case 10: // CONSTELLATION
          return CONSTELLATION;
        case 11: // HEIGHT
          return HEIGHT;
        case 12: // WEIGHT
          return WEIGHT;
        case 13: // WEIBO
          return WEIBO;
        case 14: // LOCATION
          return LOCATION;
        case 15: // BWH
          return BWH;
        case 16: // RICH_LEVEL
          return RICH_LEVEL;
        case 17: // STAR_LEVEL
          return STAR_LEVEL;
        case 18: // PRODUCT_LINE_ID
          return PRODUCT_LINE_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __STATUS_ISSET_ID = 0;
  private static final int __SEX_ISSET_ID = 1;
  private static final int __FROMTYPE_ISSET_ID = 2;
  private static final int __ADDTIME_ISSET_ID = 3;
  private static final int __CONSTELLATION_ISSET_ID = 4;
  private static final int __HEIGHT_ISSET_ID = 5;
  private static final int __WEIGHT_ISSET_ID = 6;
  private static final int __RICHLEVEL_ISSET_ID = 7;
  private static final int __STARLEVEL_ISSET_ID = 8;
  private static final int __PRODUCTLINEID_ISSET_ID = 9;
  private short __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.EMAIL,_Fields.USER_NAME,_Fields.NICK_NAME,_Fields.USER_LOGO,_Fields.STATUS,_Fields.SEX,_Fields.FROM_TYPE,_Fields.ADD_TIME,_Fields.REG_IP,_Fields.CONSTELLATION,_Fields.HEIGHT,_Fields.WEIGHT,_Fields.WEIBO,_Fields.LOCATION,_Fields.BWH,_Fields.RICH_LEVEL,_Fields.STAR_LEVEL,_Fields.PRODUCT_LINE_ID};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.EMAIL, new org.apache.thrift.meta_data.FieldMetaData("email", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.USER_NAME, new org.apache.thrift.meta_data.FieldMetaData("userName", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.NICK_NAME, new org.apache.thrift.meta_data.FieldMetaData("nickName", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.USER_LOGO, new org.apache.thrift.meta_data.FieldMetaData("userLogo", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.STATUS, new org.apache.thrift.meta_data.FieldMetaData("status", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.SEX, new org.apache.thrift.meta_data.FieldMetaData("sex", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.FROM_TYPE, new org.apache.thrift.meta_data.FieldMetaData("fromType", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ADD_TIME, new org.apache.thrift.meta_data.FieldMetaData("addTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.REG_IP, new org.apache.thrift.meta_data.FieldMetaData("regIp", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CONSTELLATION, new org.apache.thrift.meta_data.FieldMetaData("constellation", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.HEIGHT, new org.apache.thrift.meta_data.FieldMetaData("height", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.WEIGHT, new org.apache.thrift.meta_data.FieldMetaData("weight", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.WEIBO, new org.apache.thrift.meta_data.FieldMetaData("weibo", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.LOCATION, new org.apache.thrift.meta_data.FieldMetaData("location", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BWH, new org.apache.thrift.meta_data.FieldMetaData("bwh", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.RICH_LEVEL, new org.apache.thrift.meta_data.FieldMetaData("richLevel", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.STAR_LEVEL, new org.apache.thrift.meta_data.FieldMetaData("starLevel", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.PRODUCT_LINE_ID, new org.apache.thrift.meta_data.FieldMetaData("productLineId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(DynamicResponseUserVO.class, metaDataMap);
  }

  public DynamicResponseUserVO() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public DynamicResponseUserVO(DynamicResponseUserVO other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetEmail()) {
      this.email = other.email;
    }
    if (other.isSetUserName()) {
      this.userName = other.userName;
    }
    if (other.isSetNickName()) {
      this.nickName = other.nickName;
    }
    if (other.isSetUserLogo()) {
      this.userLogo = other.userLogo;
    }
    this.status = other.status;
    this.sex = other.sex;
    this.fromType = other.fromType;
    this.addTime = other.addTime;
    if (other.isSetRegIp()) {
      this.regIp = other.regIp;
    }
    this.constellation = other.constellation;
    this.height = other.height;
    this.weight = other.weight;
    if (other.isSetWeibo()) {
      this.weibo = other.weibo;
    }
    if (other.isSetLocation()) {
      this.location = other.location;
    }
    if (other.isSetBwh()) {
      this.bwh = other.bwh;
    }
    this.richLevel = other.richLevel;
    this.starLevel = other.starLevel;
    this.productLineId = other.productLineId;
  }

  public DynamicResponseUserVO deepCopy() {
    return new DynamicResponseUserVO(this);
  }

  @Override
  public void clear() {
    this.email = null;
    this.userName = null;
    this.nickName = null;
    this.userLogo = null;
    setStatusIsSet(false);
    this.status = 0;
    setSexIsSet(false);
    this.sex = 0;
    setFromTypeIsSet(false);
    this.fromType = 0;
    setAddTimeIsSet(false);
    this.addTime = 0;
    this.regIp = null;
    setConstellationIsSet(false);
    this.constellation = 0;
    setHeightIsSet(false);
    this.height = 0;
    setWeightIsSet(false);
    this.weight = 0;
    this.weibo = null;
    this.location = null;
    this.bwh = null;
    setRichLevelIsSet(false);
    this.richLevel = 0;
    setStarLevelIsSet(false);
    this.starLevel = 0;
    setProductLineIdIsSet(false);
    this.productLineId = 0;
  }

  /**
   *     * email
   * *
   */
  public String getEmail() {
    return this.email;
  }

  /**
   *     * email
   * *
   */
  public DynamicResponseUserVO setEmail(String email) {
    this.email = email;
    return this;
  }

  public void unsetEmail() {
    this.email = null;
  }

  /** Returns true if field email is set (has been assigned a value) and false otherwise */
  public boolean isSetEmail() {
    return this.email != null;
  }

  public void setEmailIsSet(boolean value) {
    if (!value) {
      this.email = null;
    }
  }

  /**
   *     * 用户名
   * *
   */
  public String getUserName() {
    return this.userName;
  }

  /**
   *     * 用户名
   * *
   */
  public DynamicResponseUserVO setUserName(String userName) {
    this.userName = userName;
    return this;
  }

  public void unsetUserName() {
    this.userName = null;
  }

  /** Returns true if field userName is set (has been assigned a value) and false otherwise */
  public boolean isSetUserName() {
    return this.userName != null;
  }

  public void setUserNameIsSet(boolean value) {
    if (!value) {
      this.userName = null;
    }
  }

  /**
   *     * 昵称
   * *
   */
  public String getNickName() {
    return this.nickName;
  }

  /**
   *     * 昵称
   * *
   */
  public DynamicResponseUserVO setNickName(String nickName) {
    this.nickName = nickName;
    return this;
  }

  public void unsetNickName() {
    this.nickName = null;
  }

  /** Returns true if field nickName is set (has been assigned a value) and false otherwise */
  public boolean isSetNickName() {
    return this.nickName != null;
  }

  public void setNickNameIsSet(boolean value) {
    if (!value) {
      this.nickName = null;
    }
  }

  /**
   *     * 用户logo
   * *
   */
  public String getUserLogo() {
    return this.userLogo;
  }

  /**
   *     * 用户logo
   * *
   */
  public DynamicResponseUserVO setUserLogo(String userLogo) {
    this.userLogo = userLogo;
    return this;
  }

  public void unsetUserLogo() {
    this.userLogo = null;
  }

  /** Returns true if field userLogo is set (has been assigned a value) and false otherwise */
  public boolean isSetUserLogo() {
    return this.userLogo != null;
  }

  public void setUserLogoIsSet(boolean value) {
    if (!value) {
      this.userLogo = null;
    }
  }

  /**
   *     * 状态(0:正常，1:封号)
   * *
   */
  public int getStatus() {
    return this.status;
  }

  /**
   *     * 状态(0:正常，1:封号)
   * *
   */
  public DynamicResponseUserVO setStatus(int status) {
    this.status = status;
    setStatusIsSet(true);
    return this;
  }

  public void unsetStatus() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  /** Returns true if field status is set (has been assigned a value) and false otherwise */
  public boolean isSetStatus() {
    return EncodingUtils.testBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  public void setStatusIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STATUS_ISSET_ID, value);
  }

  /**
   *     * 性别(0:保密,1:男,2:女)
   * *
   */
  public int getSex() {
    return this.sex;
  }

  /**
   *     * 性别(0:保密,1:男,2:女)
   * *
   */
  public DynamicResponseUserVO setSex(int sex) {
    this.sex = sex;
    setSexIsSet(true);
    return this;
  }

  public void unsetSex() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SEX_ISSET_ID);
  }

  /** Returns true if field sex is set (has been assigned a value) and false otherwise */
  public boolean isSetSex() {
    return EncodingUtils.testBit(__isset_bitfield, __SEX_ISSET_ID);
  }

  public void setSexIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SEX_ISSET_ID, value);
  }

  /**
   * 来源(0:官网,1:酷狗,2:qq,3:新浪,4:人人,25:天翼)
   * 
   */
  public int getFromType() {
    return this.fromType;
  }

  /**
   * 来源(0:官网,1:酷狗,2:qq,3:新浪,4:人人,25:天翼)
   * 
   */
  public DynamicResponseUserVO setFromType(int fromType) {
    this.fromType = fromType;
    setFromTypeIsSet(true);
    return this;
  }

  public void unsetFromType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __FROMTYPE_ISSET_ID);
  }

  /** Returns true if field fromType is set (has been assigned a value) and false otherwise */
  public boolean isSetFromType() {
    return EncodingUtils.testBit(__isset_bitfield, __FROMTYPE_ISSET_ID);
  }

  public void setFromTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __FROMTYPE_ISSET_ID, value);
  }

  /**
   *     * 注册时间
   * *
   */
  public int getAddTime() {
    return this.addTime;
  }

  /**
   *     * 注册时间
   * *
   */
  public DynamicResponseUserVO setAddTime(int addTime) {
    this.addTime = addTime;
    setAddTimeIsSet(true);
    return this;
  }

  public void unsetAddTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ADDTIME_ISSET_ID);
  }

  /** Returns true if field addTime is set (has been assigned a value) and false otherwise */
  public boolean isSetAddTime() {
    return EncodingUtils.testBit(__isset_bitfield, __ADDTIME_ISSET_ID);
  }

  public void setAddTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ADDTIME_ISSET_ID, value);
  }

  /**
   *     * 注册ip
   * *
   */
  public String getRegIp() {
    return this.regIp;
  }

  /**
   *     * 注册ip
   * *
   */
  public DynamicResponseUserVO setRegIp(String regIp) {
    this.regIp = regIp;
    return this;
  }

  public void unsetRegIp() {
    this.regIp = null;
  }

  /** Returns true if field regIp is set (has been assigned a value) and false otherwise */
  public boolean isSetRegIp() {
    return this.regIp != null;
  }

  public void setRegIpIsSet(boolean value) {
    if (!value) {
      this.regIp = null;
    }
  }

  /**
   *     * 星座(0:保密,1:白羊座,2:金牛座,3:双子座,4:巨蟹座,5:狮子座,6:处女座,7:天秤座,8:天蝎座,9:射手座,10:摩羯座,11:水瓶座,12:双鱼座)
   * *
   */
  public int getConstellation() {
    return this.constellation;
  }

  /**
   *     * 星座(0:保密,1:白羊座,2:金牛座,3:双子座,4:巨蟹座,5:狮子座,6:处女座,7:天秤座,8:天蝎座,9:射手座,10:摩羯座,11:水瓶座,12:双鱼座)
   * *
   */
  public DynamicResponseUserVO setConstellation(int constellation) {
    this.constellation = constellation;
    setConstellationIsSet(true);
    return this;
  }

  public void unsetConstellation() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CONSTELLATION_ISSET_ID);
  }

  /** Returns true if field constellation is set (has been assigned a value) and false otherwise */
  public boolean isSetConstellation() {
    return EncodingUtils.testBit(__isset_bitfield, __CONSTELLATION_ISSET_ID);
  }

  public void setConstellationIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CONSTELLATION_ISSET_ID, value);
  }

  /**
   *     * 身高[厘米]
   * *
   */
  public int getHeight() {
    return this.height;
  }

  /**
   *     * 身高[厘米]
   * *
   */
  public DynamicResponseUserVO setHeight(int height) {
    this.height = height;
    setHeightIsSet(true);
    return this;
  }

  public void unsetHeight() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __HEIGHT_ISSET_ID);
  }

  /** Returns true if field height is set (has been assigned a value) and false otherwise */
  public boolean isSetHeight() {
    return EncodingUtils.testBit(__isset_bitfield, __HEIGHT_ISSET_ID);
  }

  public void setHeightIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __HEIGHT_ISSET_ID, value);
  }

  /**
   *     * 体重[公斤]
   * *
   */
  public int getWeight() {
    return this.weight;
  }

  /**
   *     * 体重[公斤]
   * *
   */
  public DynamicResponseUserVO setWeight(int weight) {
    this.weight = weight;
    setWeightIsSet(true);
    return this;
  }

  public void unsetWeight() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __WEIGHT_ISSET_ID);
  }

  /** Returns true if field weight is set (has been assigned a value) and false otherwise */
  public boolean isSetWeight() {
    return EncodingUtils.testBit(__isset_bitfield, __WEIGHT_ISSET_ID);
  }

  public void setWeightIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __WEIGHT_ISSET_ID, value);
  }

  /**
   *     * 微博
   * *
   */
  public String getWeibo() {
    return this.weibo;
  }

  /**
   *     * 微博
   * *
   */
  public DynamicResponseUserVO setWeibo(String weibo) {
    this.weibo = weibo;
    return this;
  }

  public void unsetWeibo() {
    this.weibo = null;
  }

  /** Returns true if field weibo is set (has been assigned a value) and false otherwise */
  public boolean isSetWeibo() {
    return this.weibo != null;
  }

  public void setWeiboIsSet(boolean value) {
    if (!value) {
      this.weibo = null;
    }
  }

  /**
   * 所在地
   * 
   */
  public String getLocation() {
    return this.location;
  }

  /**
   * 所在地
   * 
   */
  public DynamicResponseUserVO setLocation(String location) {
    this.location = location;
    return this;
  }

  public void unsetLocation() {
    this.location = null;
  }

  /** Returns true if field location is set (has been assigned a value) and false otherwise */
  public boolean isSetLocation() {
    return this.location != null;
  }

  public void setLocationIsSet(boolean value) {
    if (!value) {
      this.location = null;
    }
  }

  /**
   * 三围
   * 
   */
  public String getBwh() {
    return this.bwh;
  }

  /**
   * 三围
   * 
   */
  public DynamicResponseUserVO setBwh(String bwh) {
    this.bwh = bwh;
    return this;
  }

  public void unsetBwh() {
    this.bwh = null;
  }

  /** Returns true if field bwh is set (has been assigned a value) and false otherwise */
  public boolean isSetBwh() {
    return this.bwh != null;
  }

  public void setBwhIsSet(boolean value) {
    if (!value) {
      this.bwh = null;
    }
  }

  /**
   * 财富等级
   * 
   */
  public int getRichLevel() {
    return this.richLevel;
  }

  /**
   * 财富等级
   * 
   */
  public DynamicResponseUserVO setRichLevel(int richLevel) {
    this.richLevel = richLevel;
    setRichLevelIsSet(true);
    return this;
  }

  public void unsetRichLevel() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RICHLEVEL_ISSET_ID);
  }

  /** Returns true if field richLevel is set (has been assigned a value) and false otherwise */
  public boolean isSetRichLevel() {
    return EncodingUtils.testBit(__isset_bitfield, __RICHLEVEL_ISSET_ID);
  }

  public void setRichLevelIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RICHLEVEL_ISSET_ID, value);
  }

  /**
   * 明星等级
   * 
   */
  public int getStarLevel() {
    return this.starLevel;
  }

  /**
   * 明星等级
   * 
   */
  public DynamicResponseUserVO setStarLevel(int starLevel) {
    this.starLevel = starLevel;
    setStarLevelIsSet(true);
    return this;
  }

  public void unsetStarLevel() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STARLEVEL_ISSET_ID);
  }

  /** Returns true if field starLevel is set (has been assigned a value) and false otherwise */
  public boolean isSetStarLevel() {
    return EncodingUtils.testBit(__isset_bitfield, __STARLEVEL_ISSET_ID);
  }

  public void setStarLevelIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STARLEVEL_ISSET_ID, value);
  }

  /**
   * 产品线 0:繁星（系统上线前数据）,1:繁星, 2:KuGou Live, 3:移动直播
   * 
   */
  public int getProductLineId() {
    return this.productLineId;
  }

  /**
   * 产品线 0:繁星（系统上线前数据）,1:繁星, 2:KuGou Live, 3:移动直播
   * 
   */
  public DynamicResponseUserVO setProductLineId(int productLineId) {
    this.productLineId = productLineId;
    setProductLineIdIsSet(true);
    return this;
  }

  public void unsetProductLineId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PRODUCTLINEID_ISSET_ID);
  }

  /** Returns true if field productLineId is set (has been assigned a value) and false otherwise */
  public boolean isSetProductLineId() {
    return EncodingUtils.testBit(__isset_bitfield, __PRODUCTLINEID_ISSET_ID);
  }

  public void setProductLineIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PRODUCTLINEID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case EMAIL:
      if (value == null) {
        unsetEmail();
      } else {
        setEmail((String)value);
      }
      break;

    case USER_NAME:
      if (value == null) {
        unsetUserName();
      } else {
        setUserName((String)value);
      }
      break;

    case NICK_NAME:
      if (value == null) {
        unsetNickName();
      } else {
        setNickName((String)value);
      }
      break;

    case USER_LOGO:
      if (value == null) {
        unsetUserLogo();
      } else {
        setUserLogo((String)value);
      }
      break;

    case STATUS:
      if (value == null) {
        unsetStatus();
      } else {
        setStatus((Integer)value);
      }
      break;

    case SEX:
      if (value == null) {
        unsetSex();
      } else {
        setSex((Integer)value);
      }
      break;

    case FROM_TYPE:
      if (value == null) {
        unsetFromType();
      } else {
        setFromType((Integer)value);
      }
      break;

    case ADD_TIME:
      if (value == null) {
        unsetAddTime();
      } else {
        setAddTime((Integer)value);
      }
      break;

    case REG_IP:
      if (value == null) {
        unsetRegIp();
      } else {
        setRegIp((String)value);
      }
      break;

    case CONSTELLATION:
      if (value == null) {
        unsetConstellation();
      } else {
        setConstellation((Integer)value);
      }
      break;

    case HEIGHT:
      if (value == null) {
        unsetHeight();
      } else {
        setHeight((Integer)value);
      }
      break;

    case WEIGHT:
      if (value == null) {
        unsetWeight();
      } else {
        setWeight((Integer)value);
      }
      break;

    case WEIBO:
      if (value == null) {
        unsetWeibo();
      } else {
        setWeibo((String)value);
      }
      break;

    case LOCATION:
      if (value == null) {
        unsetLocation();
      } else {
        setLocation((String)value);
      }
      break;

    case BWH:
      if (value == null) {
        unsetBwh();
      } else {
        setBwh((String)value);
      }
      break;

    case RICH_LEVEL:
      if (value == null) {
        unsetRichLevel();
      } else {
        setRichLevel((Integer)value);
      }
      break;

    case STAR_LEVEL:
      if (value == null) {
        unsetStarLevel();
      } else {
        setStarLevel((Integer)value);
      }
      break;

    case PRODUCT_LINE_ID:
      if (value == null) {
        unsetProductLineId();
      } else {
        setProductLineId((Integer)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case EMAIL:
      return getEmail();

    case USER_NAME:
      return getUserName();

    case NICK_NAME:
      return getNickName();

    case USER_LOGO:
      return getUserLogo();

    case STATUS:
      return getStatus();

    case SEX:
      return getSex();

    case FROM_TYPE:
      return getFromType();

    case ADD_TIME:
      return getAddTime();

    case REG_IP:
      return getRegIp();

    case CONSTELLATION:
      return getConstellation();

    case HEIGHT:
      return getHeight();

    case WEIGHT:
      return getWeight();

    case WEIBO:
      return getWeibo();

    case LOCATION:
      return getLocation();

    case BWH:
      return getBwh();

    case RICH_LEVEL:
      return getRichLevel();

    case STAR_LEVEL:
      return getStarLevel();

    case PRODUCT_LINE_ID:
      return getProductLineId();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case EMAIL:
      return isSetEmail();
    case USER_NAME:
      return isSetUserName();
    case NICK_NAME:
      return isSetNickName();
    case USER_LOGO:
      return isSetUserLogo();
    case STATUS:
      return isSetStatus();
    case SEX:
      return isSetSex();
    case FROM_TYPE:
      return isSetFromType();
    case ADD_TIME:
      return isSetAddTime();
    case REG_IP:
      return isSetRegIp();
    case CONSTELLATION:
      return isSetConstellation();
    case HEIGHT:
      return isSetHeight();
    case WEIGHT:
      return isSetWeight();
    case WEIBO:
      return isSetWeibo();
    case LOCATION:
      return isSetLocation();
    case BWH:
      return isSetBwh();
    case RICH_LEVEL:
      return isSetRichLevel();
    case STAR_LEVEL:
      return isSetStarLevel();
    case PRODUCT_LINE_ID:
      return isSetProductLineId();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof DynamicResponseUserVO)
      return this.equals((DynamicResponseUserVO)that);
    return false;
  }

  public boolean equals(DynamicResponseUserVO that) {
    if (that == null)
      return false;

    boolean this_present_email = true && this.isSetEmail();
    boolean that_present_email = true && that.isSetEmail();
    if (this_present_email || that_present_email) {
      if (!(this_present_email && that_present_email))
        return false;
      if (!this.email.equals(that.email))
        return false;
    }

    boolean this_present_userName = true && this.isSetUserName();
    boolean that_present_userName = true && that.isSetUserName();
    if (this_present_userName || that_present_userName) {
      if (!(this_present_userName && that_present_userName))
        return false;
      if (!this.userName.equals(that.userName))
        return false;
    }

    boolean this_present_nickName = true && this.isSetNickName();
    boolean that_present_nickName = true && that.isSetNickName();
    if (this_present_nickName || that_present_nickName) {
      if (!(this_present_nickName && that_present_nickName))
        return false;
      if (!this.nickName.equals(that.nickName))
        return false;
    }

    boolean this_present_userLogo = true && this.isSetUserLogo();
    boolean that_present_userLogo = true && that.isSetUserLogo();
    if (this_present_userLogo || that_present_userLogo) {
      if (!(this_present_userLogo && that_present_userLogo))
        return false;
      if (!this.userLogo.equals(that.userLogo))
        return false;
    }

    boolean this_present_status = true && this.isSetStatus();
    boolean that_present_status = true && that.isSetStatus();
    if (this_present_status || that_present_status) {
      if (!(this_present_status && that_present_status))
        return false;
      if (this.status != that.status)
        return false;
    }

    boolean this_present_sex = true && this.isSetSex();
    boolean that_present_sex = true && that.isSetSex();
    if (this_present_sex || that_present_sex) {
      if (!(this_present_sex && that_present_sex))
        return false;
      if (this.sex != that.sex)
        return false;
    }

    boolean this_present_fromType = true && this.isSetFromType();
    boolean that_present_fromType = true && that.isSetFromType();
    if (this_present_fromType || that_present_fromType) {
      if (!(this_present_fromType && that_present_fromType))
        return false;
      if (this.fromType != that.fromType)
        return false;
    }

    boolean this_present_addTime = true && this.isSetAddTime();
    boolean that_present_addTime = true && that.isSetAddTime();
    if (this_present_addTime || that_present_addTime) {
      if (!(this_present_addTime && that_present_addTime))
        return false;
      if (this.addTime != that.addTime)
        return false;
    }

    boolean this_present_regIp = true && this.isSetRegIp();
    boolean that_present_regIp = true && that.isSetRegIp();
    if (this_present_regIp || that_present_regIp) {
      if (!(this_present_regIp && that_present_regIp))
        return false;
      if (!this.regIp.equals(that.regIp))
        return false;
    }

    boolean this_present_constellation = true && this.isSetConstellation();
    boolean that_present_constellation = true && that.isSetConstellation();
    if (this_present_constellation || that_present_constellation) {
      if (!(this_present_constellation && that_present_constellation))
        return false;
      if (this.constellation != that.constellation)
        return false;
    }

    boolean this_present_height = true && this.isSetHeight();
    boolean that_present_height = true && that.isSetHeight();
    if (this_present_height || that_present_height) {
      if (!(this_present_height && that_present_height))
        return false;
      if (this.height != that.height)
        return false;
    }

    boolean this_present_weight = true && this.isSetWeight();
    boolean that_present_weight = true && that.isSetWeight();
    if (this_present_weight || that_present_weight) {
      if (!(this_present_weight && that_present_weight))
        return false;
      if (this.weight != that.weight)
        return false;
    }

    boolean this_present_weibo = true && this.isSetWeibo();
    boolean that_present_weibo = true && that.isSetWeibo();
    if (this_present_weibo || that_present_weibo) {
      if (!(this_present_weibo && that_present_weibo))
        return false;
      if (!this.weibo.equals(that.weibo))
        return false;
    }

    boolean this_present_location = true && this.isSetLocation();
    boolean that_present_location = true && that.isSetLocation();
    if (this_present_location || that_present_location) {
      if (!(this_present_location && that_present_location))
        return false;
      if (!this.location.equals(that.location))
        return false;
    }

    boolean this_present_bwh = true && this.isSetBwh();
    boolean that_present_bwh = true && that.isSetBwh();
    if (this_present_bwh || that_present_bwh) {
      if (!(this_present_bwh && that_present_bwh))
        return false;
      if (!this.bwh.equals(that.bwh))
        return false;
    }

    boolean this_present_richLevel = true && this.isSetRichLevel();
    boolean that_present_richLevel = true && that.isSetRichLevel();
    if (this_present_richLevel || that_present_richLevel) {
      if (!(this_present_richLevel && that_present_richLevel))
        return false;
      if (this.richLevel != that.richLevel)
        return false;
    }

    boolean this_present_starLevel = true && this.isSetStarLevel();
    boolean that_present_starLevel = true && that.isSetStarLevel();
    if (this_present_starLevel || that_present_starLevel) {
      if (!(this_present_starLevel && that_present_starLevel))
        return false;
      if (this.starLevel != that.starLevel)
        return false;
    }

    boolean this_present_productLineId = true && this.isSetProductLineId();
    boolean that_present_productLineId = true && that.isSetProductLineId();
    if (this_present_productLineId || that_present_productLineId) {
      if (!(this_present_productLineId && that_present_productLineId))
        return false;
      if (this.productLineId != that.productLineId)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_email = true && (isSetEmail());
    list.add(present_email);
    if (present_email)
      list.add(email);

    boolean present_userName = true && (isSetUserName());
    list.add(present_userName);
    if (present_userName)
      list.add(userName);

    boolean present_nickName = true && (isSetNickName());
    list.add(present_nickName);
    if (present_nickName)
      list.add(nickName);

    boolean present_userLogo = true && (isSetUserLogo());
    list.add(present_userLogo);
    if (present_userLogo)
      list.add(userLogo);

    boolean present_status = true && (isSetStatus());
    list.add(present_status);
    if (present_status)
      list.add(status);

    boolean present_sex = true && (isSetSex());
    list.add(present_sex);
    if (present_sex)
      list.add(sex);

    boolean present_fromType = true && (isSetFromType());
    list.add(present_fromType);
    if (present_fromType)
      list.add(fromType);

    boolean present_addTime = true && (isSetAddTime());
    list.add(present_addTime);
    if (present_addTime)
      list.add(addTime);

    boolean present_regIp = true && (isSetRegIp());
    list.add(present_regIp);
    if (present_regIp)
      list.add(regIp);

    boolean present_constellation = true && (isSetConstellation());
    list.add(present_constellation);
    if (present_constellation)
      list.add(constellation);

    boolean present_height = true && (isSetHeight());
    list.add(present_height);
    if (present_height)
      list.add(height);

    boolean present_weight = true && (isSetWeight());
    list.add(present_weight);
    if (present_weight)
      list.add(weight);

    boolean present_weibo = true && (isSetWeibo());
    list.add(present_weibo);
    if (present_weibo)
      list.add(weibo);

    boolean present_location = true && (isSetLocation());
    list.add(present_location);
    if (present_location)
      list.add(location);

    boolean present_bwh = true && (isSetBwh());
    list.add(present_bwh);
    if (present_bwh)
      list.add(bwh);

    boolean present_richLevel = true && (isSetRichLevel());
    list.add(present_richLevel);
    if (present_richLevel)
      list.add(richLevel);

    boolean present_starLevel = true && (isSetStarLevel());
    list.add(present_starLevel);
    if (present_starLevel)
      list.add(starLevel);

    boolean present_productLineId = true && (isSetProductLineId());
    list.add(present_productLineId);
    if (present_productLineId)
      list.add(productLineId);

    return list.hashCode();
  }

  @Override
  public int compareTo(DynamicResponseUserVO other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetEmail()).compareTo(other.isSetEmail());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEmail()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.email, other.email);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUserName()).compareTo(other.isSetUserName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUserName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.userName, other.userName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetNickName()).compareTo(other.isSetNickName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetNickName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.nickName, other.nickName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUserLogo()).compareTo(other.isSetUserLogo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUserLogo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.userLogo, other.userLogo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetStatus()).compareTo(other.isSetStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.status, other.status);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSex()).compareTo(other.isSetSex());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSex()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sex, other.sex);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetFromType()).compareTo(other.isSetFromType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFromType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.fromType, other.fromType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAddTime()).compareTo(other.isSetAddTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAddTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.addTime, other.addTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRegIp()).compareTo(other.isSetRegIp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRegIp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.regIp, other.regIp);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetConstellation()).compareTo(other.isSetConstellation());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetConstellation()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.constellation, other.constellation);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetHeight()).compareTo(other.isSetHeight());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHeight()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.height, other.height);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetWeight()).compareTo(other.isSetWeight());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetWeight()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.weight, other.weight);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetWeibo()).compareTo(other.isSetWeibo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetWeibo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.weibo, other.weibo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetLocation()).compareTo(other.isSetLocation());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLocation()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.location, other.location);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBwh()).compareTo(other.isSetBwh());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBwh()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.bwh, other.bwh);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRichLevel()).compareTo(other.isSetRichLevel());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRichLevel()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.richLevel, other.richLevel);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetStarLevel()).compareTo(other.isSetStarLevel());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStarLevel()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.starLevel, other.starLevel);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetProductLineId()).compareTo(other.isSetProductLineId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetProductLineId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.productLineId, other.productLineId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("DynamicResponseUserVO(");
    boolean first = true;

    if (isSetEmail()) {
      sb.append("email:");
      if (this.email == null) {
        sb.append("null");
      } else {
        sb.append(this.email);
      }
      first = false;
    }
    if (isSetUserName()) {
      if (!first) sb.append(", ");
      sb.append("userName:");
      if (this.userName == null) {
        sb.append("null");
      } else {
        sb.append(this.userName);
      }
      first = false;
    }
    if (isSetNickName()) {
      if (!first) sb.append(", ");
      sb.append("nickName:");
      if (this.nickName == null) {
        sb.append("null");
      } else {
        sb.append(this.nickName);
      }
      first = false;
    }
    if (isSetUserLogo()) {
      if (!first) sb.append(", ");
      sb.append("userLogo:");
      if (this.userLogo == null) {
        sb.append("null");
      } else {
        sb.append(this.userLogo);
      }
      first = false;
    }
    if (isSetStatus()) {
      if (!first) sb.append(", ");
      sb.append("status:");
      sb.append(this.status);
      first = false;
    }
    if (isSetSex()) {
      if (!first) sb.append(", ");
      sb.append("sex:");
      sb.append(this.sex);
      first = false;
    }
    if (isSetFromType()) {
      if (!first) sb.append(", ");
      sb.append("fromType:");
      sb.append(this.fromType);
      first = false;
    }
    if (isSetAddTime()) {
      if (!first) sb.append(", ");
      sb.append("addTime:");
      sb.append(this.addTime);
      first = false;
    }
    if (isSetRegIp()) {
      if (!first) sb.append(", ");
      sb.append("regIp:");
      if (this.regIp == null) {
        sb.append("null");
      } else {
        sb.append(this.regIp);
      }
      first = false;
    }
    if (isSetConstellation()) {
      if (!first) sb.append(", ");
      sb.append("constellation:");
      sb.append(this.constellation);
      first = false;
    }
    if (isSetHeight()) {
      if (!first) sb.append(", ");
      sb.append("height:");
      sb.append(this.height);
      first = false;
    }
    if (isSetWeight()) {
      if (!first) sb.append(", ");
      sb.append("weight:");
      sb.append(this.weight);
      first = false;
    }
    if (isSetWeibo()) {
      if (!first) sb.append(", ");
      sb.append("weibo:");
      if (this.weibo == null) {
        sb.append("null");
      } else {
        sb.append(this.weibo);
      }
      first = false;
    }
    if (isSetLocation()) {
      if (!first) sb.append(", ");
      sb.append("location:");
      if (this.location == null) {
        sb.append("null");
      } else {
        sb.append(this.location);
      }
      first = false;
    }
    if (isSetBwh()) {
      if (!first) sb.append(", ");
      sb.append("bwh:");
      if (this.bwh == null) {
        sb.append("null");
      } else {
        sb.append(this.bwh);
      }
      first = false;
    }
    if (isSetRichLevel()) {
      if (!first) sb.append(", ");
      sb.append("richLevel:");
      sb.append(this.richLevel);
      first = false;
    }
    if (isSetStarLevel()) {
      if (!first) sb.append(", ");
      sb.append("starLevel:");
      sb.append(this.starLevel);
      first = false;
    }
    if (isSetProductLineId()) {
      if (!first) sb.append(", ");
      sb.append("productLineId:");
      sb.append(this.productLineId);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class DynamicResponseUserVOStandardSchemeFactory implements SchemeFactory {
    public DynamicResponseUserVOStandardScheme getScheme() {
      return new DynamicResponseUserVOStandardScheme();
    }
  }

  private static class DynamicResponseUserVOStandardScheme extends StandardScheme<DynamicResponseUserVO> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, DynamicResponseUserVO struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // EMAIL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.email = iprot.readString();
              struct.setEmailIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // USER_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.userName = iprot.readString();
              struct.setUserNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // NICK_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.nickName = iprot.readString();
              struct.setNickNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // USER_LOGO
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.userLogo = iprot.readString();
              struct.setUserLogoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.status = iprot.readI32();
              struct.setStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // SEX
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.sex = iprot.readI32();
              struct.setSexIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // FROM_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.fromType = iprot.readI32();
              struct.setFromTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // ADD_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.addTime = iprot.readI32();
              struct.setAddTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // REG_IP
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.regIp = iprot.readString();
              struct.setRegIpIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // CONSTELLATION
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.constellation = iprot.readI32();
              struct.setConstellationIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // HEIGHT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.height = iprot.readI32();
              struct.setHeightIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // WEIGHT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.weight = iprot.readI32();
              struct.setWeightIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // WEIBO
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.weibo = iprot.readString();
              struct.setWeiboIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 14: // LOCATION
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.location = iprot.readString();
              struct.setLocationIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // BWH
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.bwh = iprot.readString();
              struct.setBwhIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 16: // RICH_LEVEL
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.richLevel = iprot.readI32();
              struct.setRichLevelIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 17: // STAR_LEVEL
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.starLevel = iprot.readI32();
              struct.setStarLevelIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 18: // PRODUCT_LINE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.productLineId = iprot.readI32();
              struct.setProductLineIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, DynamicResponseUserVO struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.email != null) {
        if (struct.isSetEmail()) {
          oprot.writeFieldBegin(EMAIL_FIELD_DESC);
          oprot.writeString(struct.email);
          oprot.writeFieldEnd();
        }
      }
      if (struct.userName != null) {
        if (struct.isSetUserName()) {
          oprot.writeFieldBegin(USER_NAME_FIELD_DESC);
          oprot.writeString(struct.userName);
          oprot.writeFieldEnd();
        }
      }
      if (struct.nickName != null) {
        if (struct.isSetNickName()) {
          oprot.writeFieldBegin(NICK_NAME_FIELD_DESC);
          oprot.writeString(struct.nickName);
          oprot.writeFieldEnd();
        }
      }
      if (struct.userLogo != null) {
        if (struct.isSetUserLogo()) {
          oprot.writeFieldBegin(USER_LOGO_FIELD_DESC);
          oprot.writeString(struct.userLogo);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetStatus()) {
        oprot.writeFieldBegin(STATUS_FIELD_DESC);
        oprot.writeI32(struct.status);
        oprot.writeFieldEnd();
      }
      if (struct.isSetSex()) {
        oprot.writeFieldBegin(SEX_FIELD_DESC);
        oprot.writeI32(struct.sex);
        oprot.writeFieldEnd();
      }
      if (struct.isSetFromType()) {
        oprot.writeFieldBegin(FROM_TYPE_FIELD_DESC);
        oprot.writeI32(struct.fromType);
        oprot.writeFieldEnd();
      }
      if (struct.isSetAddTime()) {
        oprot.writeFieldBegin(ADD_TIME_FIELD_DESC);
        oprot.writeI32(struct.addTime);
        oprot.writeFieldEnd();
      }
      if (struct.regIp != null) {
        if (struct.isSetRegIp()) {
          oprot.writeFieldBegin(REG_IP_FIELD_DESC);
          oprot.writeString(struct.regIp);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetConstellation()) {
        oprot.writeFieldBegin(CONSTELLATION_FIELD_DESC);
        oprot.writeI32(struct.constellation);
        oprot.writeFieldEnd();
      }
      if (struct.isSetHeight()) {
        oprot.writeFieldBegin(HEIGHT_FIELD_DESC);
        oprot.writeI32(struct.height);
        oprot.writeFieldEnd();
      }
      if (struct.isSetWeight()) {
        oprot.writeFieldBegin(WEIGHT_FIELD_DESC);
        oprot.writeI32(struct.weight);
        oprot.writeFieldEnd();
      }
      if (struct.weibo != null) {
        if (struct.isSetWeibo()) {
          oprot.writeFieldBegin(WEIBO_FIELD_DESC);
          oprot.writeString(struct.weibo);
          oprot.writeFieldEnd();
        }
      }
      if (struct.location != null) {
        if (struct.isSetLocation()) {
          oprot.writeFieldBegin(LOCATION_FIELD_DESC);
          oprot.writeString(struct.location);
          oprot.writeFieldEnd();
        }
      }
      if (struct.bwh != null) {
        if (struct.isSetBwh()) {
          oprot.writeFieldBegin(BWH_FIELD_DESC);
          oprot.writeString(struct.bwh);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetRichLevel()) {
        oprot.writeFieldBegin(RICH_LEVEL_FIELD_DESC);
        oprot.writeI32(struct.richLevel);
        oprot.writeFieldEnd();
      }
      if (struct.isSetStarLevel()) {
        oprot.writeFieldBegin(STAR_LEVEL_FIELD_DESC);
        oprot.writeI32(struct.starLevel);
        oprot.writeFieldEnd();
      }
      if (struct.isSetProductLineId()) {
        oprot.writeFieldBegin(PRODUCT_LINE_ID_FIELD_DESC);
        oprot.writeI32(struct.productLineId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class DynamicResponseUserVOTupleSchemeFactory implements SchemeFactory {
    public DynamicResponseUserVOTupleScheme getScheme() {
      return new DynamicResponseUserVOTupleScheme();
    }
  }

  private static class DynamicResponseUserVOTupleScheme extends TupleScheme<DynamicResponseUserVO> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, DynamicResponseUserVO struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetEmail()) {
        optionals.set(0);
      }
      if (struct.isSetUserName()) {
        optionals.set(1);
      }
      if (struct.isSetNickName()) {
        optionals.set(2);
      }
      if (struct.isSetUserLogo()) {
        optionals.set(3);
      }
      if (struct.isSetStatus()) {
        optionals.set(4);
      }
      if (struct.isSetSex()) {
        optionals.set(5);
      }
      if (struct.isSetFromType()) {
        optionals.set(6);
      }
      if (struct.isSetAddTime()) {
        optionals.set(7);
      }
      if (struct.isSetRegIp()) {
        optionals.set(8);
      }
      if (struct.isSetConstellation()) {
        optionals.set(9);
      }
      if (struct.isSetHeight()) {
        optionals.set(10);
      }
      if (struct.isSetWeight()) {
        optionals.set(11);
      }
      if (struct.isSetWeibo()) {
        optionals.set(12);
      }
      if (struct.isSetLocation()) {
        optionals.set(13);
      }
      if (struct.isSetBwh()) {
        optionals.set(14);
      }
      if (struct.isSetRichLevel()) {
        optionals.set(15);
      }
      if (struct.isSetStarLevel()) {
        optionals.set(16);
      }
      if (struct.isSetProductLineId()) {
        optionals.set(17);
      }
      oprot.writeBitSet(optionals, 18);
      if (struct.isSetEmail()) {
        oprot.writeString(struct.email);
      }
      if (struct.isSetUserName()) {
        oprot.writeString(struct.userName);
      }
      if (struct.isSetNickName()) {
        oprot.writeString(struct.nickName);
      }
      if (struct.isSetUserLogo()) {
        oprot.writeString(struct.userLogo);
      }
      if (struct.isSetStatus()) {
        oprot.writeI32(struct.status);
      }
      if (struct.isSetSex()) {
        oprot.writeI32(struct.sex);
      }
      if (struct.isSetFromType()) {
        oprot.writeI32(struct.fromType);
      }
      if (struct.isSetAddTime()) {
        oprot.writeI32(struct.addTime);
      }
      if (struct.isSetRegIp()) {
        oprot.writeString(struct.regIp);
      }
      if (struct.isSetConstellation()) {
        oprot.writeI32(struct.constellation);
      }
      if (struct.isSetHeight()) {
        oprot.writeI32(struct.height);
      }
      if (struct.isSetWeight()) {
        oprot.writeI32(struct.weight);
      }
      if (struct.isSetWeibo()) {
        oprot.writeString(struct.weibo);
      }
      if (struct.isSetLocation()) {
        oprot.writeString(struct.location);
      }
      if (struct.isSetBwh()) {
        oprot.writeString(struct.bwh);
      }
      if (struct.isSetRichLevel()) {
        oprot.writeI32(struct.richLevel);
      }
      if (struct.isSetStarLevel()) {
        oprot.writeI32(struct.starLevel);
      }
      if (struct.isSetProductLineId()) {
        oprot.writeI32(struct.productLineId);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, DynamicResponseUserVO struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(18);
      if (incoming.get(0)) {
        struct.email = iprot.readString();
        struct.setEmailIsSet(true);
      }
      if (incoming.get(1)) {
        struct.userName = iprot.readString();
        struct.setUserNameIsSet(true);
      }
      if (incoming.get(2)) {
        struct.nickName = iprot.readString();
        struct.setNickNameIsSet(true);
      }
      if (incoming.get(3)) {
        struct.userLogo = iprot.readString();
        struct.setUserLogoIsSet(true);
      }
      if (incoming.get(4)) {
        struct.status = iprot.readI32();
        struct.setStatusIsSet(true);
      }
      if (incoming.get(5)) {
        struct.sex = iprot.readI32();
        struct.setSexIsSet(true);
      }
      if (incoming.get(6)) {
        struct.fromType = iprot.readI32();
        struct.setFromTypeIsSet(true);
      }
      if (incoming.get(7)) {
        struct.addTime = iprot.readI32();
        struct.setAddTimeIsSet(true);
      }
      if (incoming.get(8)) {
        struct.regIp = iprot.readString();
        struct.setRegIpIsSet(true);
      }
      if (incoming.get(9)) {
        struct.constellation = iprot.readI32();
        struct.setConstellationIsSet(true);
      }
      if (incoming.get(10)) {
        struct.height = iprot.readI32();
        struct.setHeightIsSet(true);
      }
      if (incoming.get(11)) {
        struct.weight = iprot.readI32();
        struct.setWeightIsSet(true);
      }
      if (incoming.get(12)) {
        struct.weibo = iprot.readString();
        struct.setWeiboIsSet(true);
      }
      if (incoming.get(13)) {
        struct.location = iprot.readString();
        struct.setLocationIsSet(true);
      }
      if (incoming.get(14)) {
        struct.bwh = iprot.readString();
        struct.setBwhIsSet(true);
      }
      if (incoming.get(15)) {
        struct.richLevel = iprot.readI32();
        struct.setRichLevelIsSet(true);
      }
      if (incoming.get(16)) {
        struct.starLevel = iprot.readI32();
        struct.setStarLevelIsSet(true);
      }
      if (incoming.get(17)) {
        struct.productLineId = iprot.readI32();
        struct.setProductLineIdIsSet(true);
      }
    }
  }

}

