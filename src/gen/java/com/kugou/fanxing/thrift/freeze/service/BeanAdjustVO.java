/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.thrift.freeze.service;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
/**
 * 管理员调整星豆
 */
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2019-11-12")
public class BeanAdjustVO implements org.apache.thrift.TBase<BeanAdjustVO, BeanAdjustVO._Fields>, java.io.Serializable, Cloneable, Comparable<BeanAdjustVO> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("BeanAdjustVO");

  private static final org.apache.thrift.protocol.TField GLOBAL_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("globalId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField ACCOUNT_CHANGE_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("accountChangeType", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField BEAN_FIELD_DESC = new org.apache.thrift.protocol.TField("bean", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField FXC_CHANGE_DESC_FIELD_DESC = new org.apache.thrift.protocol.TField("fxcChangeDesc", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField IP_FIELD_DESC = new org.apache.thrift.protocol.TField("ip", org.apache.thrift.protocol.TType.STRING, (short)6);
  private static final org.apache.thrift.protocol.TField TIMESTAMP_FIELD_DESC = new org.apache.thrift.protocol.TField("timestamp", org.apache.thrift.protocol.TType.I32, (short)7);
  private static final org.apache.thrift.protocol.TField EXT_FIELD_DESC = new org.apache.thrift.protocol.TField("ext", org.apache.thrift.protocol.TType.STRING, (short)8);
  private static final org.apache.thrift.protocol.TField SIGN_FIELD_DESC = new org.apache.thrift.protocol.TField("sign", org.apache.thrift.protocol.TType.STRING, (short)9);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new BeanAdjustVOStandardSchemeFactory());
    schemes.put(TupleScheme.class, new BeanAdjustVOTupleSchemeFactory());
  }

  public long globalId; // required
  /**
   * 用户财务变更类型
   */
  public int accountChangeType; // required
  /**
   * 变更的KugouID
   */
  public long kugouId; // required
  /**
   * 调整的星豆的值，大于0，注入虚拟星豆；小于0，扣除星豆
   */
  public String bean; // required
  /**
   * 变更描述
   */
  public String fxcChangeDesc; // required
  /**
   * 管理员IP
   */
  public String ip; // required
  /**
   * 时间戳 (秒)
   */
  public int timestamp; // required
  /**
   * 拓展字段,传JSON字符串,没有的话,传''
   */
  public String ext; // optional
  /**
   * md5(所有参数名字升序排列对应的数值拼接 + 固定密钥[加盐][跟对外提供服务的accountChangeType对应])
   */
  public String sign; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    GLOBAL_ID((short)1, "globalId"),
    /**
     * 用户财务变更类型
     */
    ACCOUNT_CHANGE_TYPE((short)2, "accountChangeType"),
    /**
     * 变更的KugouID
     */
    KUGOU_ID((short)3, "kugouId"),
    /**
     * 调整的星豆的值，大于0，注入虚拟星豆；小于0，扣除星豆
     */
    BEAN((short)4, "bean"),
    /**
     * 变更描述
     */
    FXC_CHANGE_DESC((short)5, "fxcChangeDesc"),
    /**
     * 管理员IP
     */
    IP((short)6, "ip"),
    /**
     * 时间戳 (秒)
     */
    TIMESTAMP((short)7, "timestamp"),
    /**
     * 拓展字段,传JSON字符串,没有的话,传''
     */
    EXT((short)8, "ext"),
    /**
     * md5(所有参数名字升序排列对应的数值拼接 + 固定密钥[加盐][跟对外提供服务的accountChangeType对应])
     */
    SIGN((short)9, "sign");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // GLOBAL_ID
          return GLOBAL_ID;
        case 2: // ACCOUNT_CHANGE_TYPE
          return ACCOUNT_CHANGE_TYPE;
        case 3: // KUGOU_ID
          return KUGOU_ID;
        case 4: // BEAN
          return BEAN;
        case 5: // FXC_CHANGE_DESC
          return FXC_CHANGE_DESC;
        case 6: // IP
          return IP;
        case 7: // TIMESTAMP
          return TIMESTAMP;
        case 8: // EXT
          return EXT;
        case 9: // SIGN
          return SIGN;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __GLOBALID_ISSET_ID = 0;
  private static final int __ACCOUNTCHANGETYPE_ISSET_ID = 1;
  private static final int __KUGOUID_ISSET_ID = 2;
  private static final int __TIMESTAMP_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.EXT};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.GLOBAL_ID, new org.apache.thrift.meta_data.FieldMetaData("globalId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ACCOUNT_CHANGE_TYPE, new org.apache.thrift.meta_data.FieldMetaData("accountChangeType", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.BEAN, new org.apache.thrift.meta_data.FieldMetaData("bean", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.FXC_CHANGE_DESC, new org.apache.thrift.meta_data.FieldMetaData("fxcChangeDesc", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.IP, new org.apache.thrift.meta_data.FieldMetaData("ip", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.TIMESTAMP, new org.apache.thrift.meta_data.FieldMetaData("timestamp", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.EXT, new org.apache.thrift.meta_data.FieldMetaData("ext", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SIGN, new org.apache.thrift.meta_data.FieldMetaData("sign", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(BeanAdjustVO.class, metaDataMap);
  }

  public BeanAdjustVO() {
  }

  public BeanAdjustVO(
    long globalId,
    int accountChangeType,
    long kugouId,
    String bean,
    String fxcChangeDesc,
    String ip,
    int timestamp,
    String sign)
  {
    this();
    this.globalId = globalId;
    setGlobalIdIsSet(true);
    this.accountChangeType = accountChangeType;
    setAccountChangeTypeIsSet(true);
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    this.bean = bean;
    this.fxcChangeDesc = fxcChangeDesc;
    this.ip = ip;
    this.timestamp = timestamp;
    setTimestampIsSet(true);
    this.sign = sign;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public BeanAdjustVO(BeanAdjustVO other) {
    __isset_bitfield = other.__isset_bitfield;
    this.globalId = other.globalId;
    this.accountChangeType = other.accountChangeType;
    this.kugouId = other.kugouId;
    if (other.isSetBean()) {
      this.bean = other.bean;
    }
    if (other.isSetFxcChangeDesc()) {
      this.fxcChangeDesc = other.fxcChangeDesc;
    }
    if (other.isSetIp()) {
      this.ip = other.ip;
    }
    this.timestamp = other.timestamp;
    if (other.isSetExt()) {
      this.ext = other.ext;
    }
    if (other.isSetSign()) {
      this.sign = other.sign;
    }
  }

  public BeanAdjustVO deepCopy() {
    return new BeanAdjustVO(this);
  }

  @Override
  public void clear() {
    setGlobalIdIsSet(false);
    this.globalId = 0;
    setAccountChangeTypeIsSet(false);
    this.accountChangeType = 0;
    setKugouIdIsSet(false);
    this.kugouId = 0;
    this.bean = null;
    this.fxcChangeDesc = null;
    this.ip = null;
    setTimestampIsSet(false);
    this.timestamp = 0;
    this.ext = null;
    this.sign = null;
  }

  public long getGlobalId() {
    return this.globalId;
  }

  public BeanAdjustVO setGlobalId(long globalId) {
    this.globalId = globalId;
    setGlobalIdIsSet(true);
    return this;
  }

  public void unsetGlobalId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __GLOBALID_ISSET_ID);
  }

  /** Returns true if field globalId is set (has been assigned a value) and false otherwise */
  public boolean isSetGlobalId() {
    return EncodingUtils.testBit(__isset_bitfield, __GLOBALID_ISSET_ID);
  }

  public void setGlobalIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __GLOBALID_ISSET_ID, value);
  }

  /**
   * 用户财务变更类型
   */
  public int getAccountChangeType() {
    return this.accountChangeType;
  }

  /**
   * 用户财务变更类型
   */
  public BeanAdjustVO setAccountChangeType(int accountChangeType) {
    this.accountChangeType = accountChangeType;
    setAccountChangeTypeIsSet(true);
    return this;
  }

  public void unsetAccountChangeType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACCOUNTCHANGETYPE_ISSET_ID);
  }

  /** Returns true if field accountChangeType is set (has been assigned a value) and false otherwise */
  public boolean isSetAccountChangeType() {
    return EncodingUtils.testBit(__isset_bitfield, __ACCOUNTCHANGETYPE_ISSET_ID);
  }

  public void setAccountChangeTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACCOUNTCHANGETYPE_ISSET_ID, value);
  }

  /**
   * 变更的KugouID
   */
  public long getKugouId() {
    return this.kugouId;
  }

  /**
   * 变更的KugouID
   */
  public BeanAdjustVO setKugouId(long kugouId) {
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    return this;
  }

  public void unsetKugouId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
  public boolean isSetKugouId() {
    return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  public void setKugouIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
  }

  /**
   * 调整的星豆的值，大于0，注入虚拟星豆；小于0，扣除星豆
   */
  public String getBean() {
    return this.bean;
  }

  /**
   * 调整的星豆的值，大于0，注入虚拟星豆；小于0，扣除星豆
   */
  public BeanAdjustVO setBean(String bean) {
    this.bean = bean;
    return this;
  }

  public void unsetBean() {
    this.bean = null;
  }

  /** Returns true if field bean is set (has been assigned a value) and false otherwise */
  public boolean isSetBean() {
    return this.bean != null;
  }

  public void setBeanIsSet(boolean value) {
    if (!value) {
      this.bean = null;
    }
  }

  /**
   * 变更描述
   */
  public String getFxcChangeDesc() {
    return this.fxcChangeDesc;
  }

  /**
   * 变更描述
   */
  public BeanAdjustVO setFxcChangeDesc(String fxcChangeDesc) {
    this.fxcChangeDesc = fxcChangeDesc;
    return this;
  }

  public void unsetFxcChangeDesc() {
    this.fxcChangeDesc = null;
  }

  /** Returns true if field fxcChangeDesc is set (has been assigned a value) and false otherwise */
  public boolean isSetFxcChangeDesc() {
    return this.fxcChangeDesc != null;
  }

  public void setFxcChangeDescIsSet(boolean value) {
    if (!value) {
      this.fxcChangeDesc = null;
    }
  }

  /**
   * 管理员IP
   */
  public String getIp() {
    return this.ip;
  }

  /**
   * 管理员IP
   */
  public BeanAdjustVO setIp(String ip) {
    this.ip = ip;
    return this;
  }

  public void unsetIp() {
    this.ip = null;
  }

  /** Returns true if field ip is set (has been assigned a value) and false otherwise */
  public boolean isSetIp() {
    return this.ip != null;
  }

  public void setIpIsSet(boolean value) {
    if (!value) {
      this.ip = null;
    }
  }

  /**
   * 时间戳 (秒)
   */
  public int getTimestamp() {
    return this.timestamp;
  }

  /**
   * 时间戳 (秒)
   */
  public BeanAdjustVO setTimestamp(int timestamp) {
    this.timestamp = timestamp;
    setTimestampIsSet(true);
    return this;
  }

  public void unsetTimestamp() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
  }

  /** Returns true if field timestamp is set (has been assigned a value) and false otherwise */
  public boolean isSetTimestamp() {
    return EncodingUtils.testBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
  }

  public void setTimestampIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TIMESTAMP_ISSET_ID, value);
  }

  /**
   * 拓展字段,传JSON字符串,没有的话,传''
   */
  public String getExt() {
    return this.ext;
  }

  /**
   * 拓展字段,传JSON字符串,没有的话,传''
   */
  public BeanAdjustVO setExt(String ext) {
    this.ext = ext;
    return this;
  }

  public void unsetExt() {
    this.ext = null;
  }

  /** Returns true if field ext is set (has been assigned a value) and false otherwise */
  public boolean isSetExt() {
    return this.ext != null;
  }

  public void setExtIsSet(boolean value) {
    if (!value) {
      this.ext = null;
    }
  }

  /**
   * md5(所有参数名字升序排列对应的数值拼接 + 固定密钥[加盐][跟对外提供服务的accountChangeType对应])
   */
  public String getSign() {
    return this.sign;
  }

  /**
   * md5(所有参数名字升序排列对应的数值拼接 + 固定密钥[加盐][跟对外提供服务的accountChangeType对应])
   */
  public BeanAdjustVO setSign(String sign) {
    this.sign = sign;
    return this;
  }

  public void unsetSign() {
    this.sign = null;
  }

  /** Returns true if field sign is set (has been assigned a value) and false otherwise */
  public boolean isSetSign() {
    return this.sign != null;
  }

  public void setSignIsSet(boolean value) {
    if (!value) {
      this.sign = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case GLOBAL_ID:
      if (value == null) {
        unsetGlobalId();
      } else {
        setGlobalId((Long)value);
      }
      break;

    case ACCOUNT_CHANGE_TYPE:
      if (value == null) {
        unsetAccountChangeType();
      } else {
        setAccountChangeType((Integer)value);
      }
      break;

    case KUGOU_ID:
      if (value == null) {
        unsetKugouId();
      } else {
        setKugouId((Long)value);
      }
      break;

    case BEAN:
      if (value == null) {
        unsetBean();
      } else {
        setBean((String)value);
      }
      break;

    case FXC_CHANGE_DESC:
      if (value == null) {
        unsetFxcChangeDesc();
      } else {
        setFxcChangeDesc((String)value);
      }
      break;

    case IP:
      if (value == null) {
        unsetIp();
      } else {
        setIp((String)value);
      }
      break;

    case TIMESTAMP:
      if (value == null) {
        unsetTimestamp();
      } else {
        setTimestamp((Integer)value);
      }
      break;

    case EXT:
      if (value == null) {
        unsetExt();
      } else {
        setExt((String)value);
      }
      break;

    case SIGN:
      if (value == null) {
        unsetSign();
      } else {
        setSign((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case GLOBAL_ID:
      return getGlobalId();

    case ACCOUNT_CHANGE_TYPE:
      return getAccountChangeType();

    case KUGOU_ID:
      return getKugouId();

    case BEAN:
      return getBean();

    case FXC_CHANGE_DESC:
      return getFxcChangeDesc();

    case IP:
      return getIp();

    case TIMESTAMP:
      return getTimestamp();

    case EXT:
      return getExt();

    case SIGN:
      return getSign();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case GLOBAL_ID:
      return isSetGlobalId();
    case ACCOUNT_CHANGE_TYPE:
      return isSetAccountChangeType();
    case KUGOU_ID:
      return isSetKugouId();
    case BEAN:
      return isSetBean();
    case FXC_CHANGE_DESC:
      return isSetFxcChangeDesc();
    case IP:
      return isSetIp();
    case TIMESTAMP:
      return isSetTimestamp();
    case EXT:
      return isSetExt();
    case SIGN:
      return isSetSign();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof BeanAdjustVO)
      return this.equals((BeanAdjustVO)that);
    return false;
  }

  public boolean equals(BeanAdjustVO that) {
    if (that == null)
      return false;

    boolean this_present_globalId = true;
    boolean that_present_globalId = true;
    if (this_present_globalId || that_present_globalId) {
      if (!(this_present_globalId && that_present_globalId))
        return false;
      if (this.globalId != that.globalId)
        return false;
    }

    boolean this_present_accountChangeType = true;
    boolean that_present_accountChangeType = true;
    if (this_present_accountChangeType || that_present_accountChangeType) {
      if (!(this_present_accountChangeType && that_present_accountChangeType))
        return false;
      if (this.accountChangeType != that.accountChangeType)
        return false;
    }

    boolean this_present_kugouId = true;
    boolean that_present_kugouId = true;
    if (this_present_kugouId || that_present_kugouId) {
      if (!(this_present_kugouId && that_present_kugouId))
        return false;
      if (this.kugouId != that.kugouId)
        return false;
    }

    boolean this_present_bean = true && this.isSetBean();
    boolean that_present_bean = true && that.isSetBean();
    if (this_present_bean || that_present_bean) {
      if (!(this_present_bean && that_present_bean))
        return false;
      if (!this.bean.equals(that.bean))
        return false;
    }

    boolean this_present_fxcChangeDesc = true && this.isSetFxcChangeDesc();
    boolean that_present_fxcChangeDesc = true && that.isSetFxcChangeDesc();
    if (this_present_fxcChangeDesc || that_present_fxcChangeDesc) {
      if (!(this_present_fxcChangeDesc && that_present_fxcChangeDesc))
        return false;
      if (!this.fxcChangeDesc.equals(that.fxcChangeDesc))
        return false;
    }

    boolean this_present_ip = true && this.isSetIp();
    boolean that_present_ip = true && that.isSetIp();
    if (this_present_ip || that_present_ip) {
      if (!(this_present_ip && that_present_ip))
        return false;
      if (!this.ip.equals(that.ip))
        return false;
    }

    boolean this_present_timestamp = true;
    boolean that_present_timestamp = true;
    if (this_present_timestamp || that_present_timestamp) {
      if (!(this_present_timestamp && that_present_timestamp))
        return false;
      if (this.timestamp != that.timestamp)
        return false;
    }

    boolean this_present_ext = true && this.isSetExt();
    boolean that_present_ext = true && that.isSetExt();
    if (this_present_ext || that_present_ext) {
      if (!(this_present_ext && that_present_ext))
        return false;
      if (!this.ext.equals(that.ext))
        return false;
    }

    boolean this_present_sign = true && this.isSetSign();
    boolean that_present_sign = true && that.isSetSign();
    if (this_present_sign || that_present_sign) {
      if (!(this_present_sign && that_present_sign))
        return false;
      if (!this.sign.equals(that.sign))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_globalId = true;
    list.add(present_globalId);
    if (present_globalId)
      list.add(globalId);

    boolean present_accountChangeType = true;
    list.add(present_accountChangeType);
    if (present_accountChangeType)
      list.add(accountChangeType);

    boolean present_kugouId = true;
    list.add(present_kugouId);
    if (present_kugouId)
      list.add(kugouId);

    boolean present_bean = true && (isSetBean());
    list.add(present_bean);
    if (present_bean)
      list.add(bean);

    boolean present_fxcChangeDesc = true && (isSetFxcChangeDesc());
    list.add(present_fxcChangeDesc);
    if (present_fxcChangeDesc)
      list.add(fxcChangeDesc);

    boolean present_ip = true && (isSetIp());
    list.add(present_ip);
    if (present_ip)
      list.add(ip);

    boolean present_timestamp = true;
    list.add(present_timestamp);
    if (present_timestamp)
      list.add(timestamp);

    boolean present_ext = true && (isSetExt());
    list.add(present_ext);
    if (present_ext)
      list.add(ext);

    boolean present_sign = true && (isSetSign());
    list.add(present_sign);
    if (present_sign)
      list.add(sign);

    return list.hashCode();
  }

  @Override
  public int compareTo(BeanAdjustVO other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetGlobalId()).compareTo(other.isSetGlobalId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGlobalId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.globalId, other.globalId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAccountChangeType()).compareTo(other.isSetAccountChangeType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAccountChangeType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.accountChangeType, other.accountChangeType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKugouId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBean()).compareTo(other.isSetBean());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBean()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.bean, other.bean);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetFxcChangeDesc()).compareTo(other.isSetFxcChangeDesc());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFxcChangeDesc()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.fxcChangeDesc, other.fxcChangeDesc);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIp()).compareTo(other.isSetIp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ip, other.ip);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTimestamp()).compareTo(other.isSetTimestamp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTimestamp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.timestamp, other.timestamp);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExt()).compareTo(other.isSetExt());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExt()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ext, other.ext);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSign()).compareTo(other.isSetSign());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSign()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sign, other.sign);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("BeanAdjustVO(");
    boolean first = true;

    sb.append("globalId:");
    sb.append(this.globalId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("accountChangeType:");
    sb.append(this.accountChangeType);
    first = false;
    if (!first) sb.append(", ");
    sb.append("kugouId:");
    sb.append(this.kugouId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("bean:");
    if (this.bean == null) {
      sb.append("null");
    } else {
      sb.append(this.bean);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("fxcChangeDesc:");
    if (this.fxcChangeDesc == null) {
      sb.append("null");
    } else {
      sb.append(this.fxcChangeDesc);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("ip:");
    if (this.ip == null) {
      sb.append("null");
    } else {
      sb.append(this.ip);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("timestamp:");
    sb.append(this.timestamp);
    first = false;
    if (isSetExt()) {
      if (!first) sb.append(", ");
      sb.append("ext:");
      if (this.ext == null) {
        sb.append("null");
      } else {
        sb.append(this.ext);
      }
      first = false;
    }
    if (!first) sb.append(", ");
    sb.append("sign:");
    if (this.sign == null) {
      sb.append("null");
    } else {
      sb.append(this.sign);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'globalId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'accountChangeType' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
    if (bean == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'bean' was not present! Struct: " + toString());
    }
    if (fxcChangeDesc == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'fxcChangeDesc' was not present! Struct: " + toString());
    }
    if (ip == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'ip' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'timestamp' because it's a primitive and you chose the non-beans generator.
    if (sign == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'sign' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class BeanAdjustVOStandardSchemeFactory implements SchemeFactory {
    public BeanAdjustVOStandardScheme getScheme() {
      return new BeanAdjustVOStandardScheme();
    }
  }

  private static class BeanAdjustVOStandardScheme extends StandardScheme<BeanAdjustVO> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, BeanAdjustVO struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // GLOBAL_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.globalId = iprot.readI64();
              struct.setGlobalIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ACCOUNT_CHANGE_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.accountChangeType = iprot.readI32();
              struct.setAccountChangeTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // KUGOU_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.kugouId = iprot.readI64();
              struct.setKugouIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // BEAN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.bean = iprot.readString();
              struct.setBeanIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // FXC_CHANGE_DESC
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.fxcChangeDesc = iprot.readString();
              struct.setFxcChangeDescIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // IP
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.ip = iprot.readString();
              struct.setIpIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // TIMESTAMP
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.timestamp = iprot.readI32();
              struct.setTimestampIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // EXT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.ext = iprot.readString();
              struct.setExtIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // SIGN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.sign = iprot.readString();
              struct.setSignIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetGlobalId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'globalId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetAccountChangeType()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'accountChangeType' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetKugouId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetTimestamp()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'timestamp' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, BeanAdjustVO struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(GLOBAL_ID_FIELD_DESC);
      oprot.writeI64(struct.globalId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ACCOUNT_CHANGE_TYPE_FIELD_DESC);
      oprot.writeI32(struct.accountChangeType);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
      oprot.writeI64(struct.kugouId);
      oprot.writeFieldEnd();
      if (struct.bean != null) {
        oprot.writeFieldBegin(BEAN_FIELD_DESC);
        oprot.writeString(struct.bean);
        oprot.writeFieldEnd();
      }
      if (struct.fxcChangeDesc != null) {
        oprot.writeFieldBegin(FXC_CHANGE_DESC_FIELD_DESC);
        oprot.writeString(struct.fxcChangeDesc);
        oprot.writeFieldEnd();
      }
      if (struct.ip != null) {
        oprot.writeFieldBegin(IP_FIELD_DESC);
        oprot.writeString(struct.ip);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(TIMESTAMP_FIELD_DESC);
      oprot.writeI32(struct.timestamp);
      oprot.writeFieldEnd();
      if (struct.ext != null) {
        if (struct.isSetExt()) {
          oprot.writeFieldBegin(EXT_FIELD_DESC);
          oprot.writeString(struct.ext);
          oprot.writeFieldEnd();
        }
      }
      if (struct.sign != null) {
        oprot.writeFieldBegin(SIGN_FIELD_DESC);
        oprot.writeString(struct.sign);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class BeanAdjustVOTupleSchemeFactory implements SchemeFactory {
    public BeanAdjustVOTupleScheme getScheme() {
      return new BeanAdjustVOTupleScheme();
    }
  }

  private static class BeanAdjustVOTupleScheme extends TupleScheme<BeanAdjustVO> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, BeanAdjustVO struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeI64(struct.globalId);
      oprot.writeI32(struct.accountChangeType);
      oprot.writeI64(struct.kugouId);
      oprot.writeString(struct.bean);
      oprot.writeString(struct.fxcChangeDesc);
      oprot.writeString(struct.ip);
      oprot.writeI32(struct.timestamp);
      oprot.writeString(struct.sign);
      BitSet optionals = new BitSet();
      if (struct.isSetExt()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetExt()) {
        oprot.writeString(struct.ext);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, BeanAdjustVO struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.globalId = iprot.readI64();
      struct.setGlobalIdIsSet(true);
      struct.accountChangeType = iprot.readI32();
      struct.setAccountChangeTypeIsSet(true);
      struct.kugouId = iprot.readI64();
      struct.setKugouIdIsSet(true);
      struct.bean = iprot.readString();
      struct.setBeanIsSet(true);
      struct.fxcChangeDesc = iprot.readString();
      struct.setFxcChangeDescIsSet(true);
      struct.ip = iprot.readString();
      struct.setIpIsSet(true);
      struct.timestamp = iprot.readI32();
      struct.setTimestampIsSet(true);
      struct.sign = iprot.readString();
      struct.setSignIsSet(true);
      BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        struct.ext = iprot.readString();
        struct.setExtIsSet(true);
      }
    }
  }

}

