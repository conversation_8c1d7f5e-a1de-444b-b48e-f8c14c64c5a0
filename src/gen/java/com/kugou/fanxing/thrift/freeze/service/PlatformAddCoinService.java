/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.thrift.freeze.service;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2019-11-12")
public class PlatformAddCoinService {

  /**
   * 加星币相关接口
   */
  public interface Iface {

    /**
     * 用户退币接口
     * 
     * @param vo
     */
    public com.kugou.fanxing.thrift.consume.service.ConsumeResp refund(CoinVO vo) throws org.apache.thrift.TException;

    /**
     * 赠送星币接口
     * 
     * @param vo
     */
    public com.kugou.fanxing.thrift.consume.service.ConsumeResp present(CoinVO vo) throws org.apache.thrift.TException;

    /**
     * 充值加星币接口
     * 
     * @param vo
     */
    public com.kugou.fanxing.thrift.consume.service.ConsumeResp recharge(CoinVO vo) throws org.apache.thrift.TException;

    /**
     * 星豆兑换接口
     * 
     * @param vo
     */
    public com.kugou.fanxing.thrift.consume.service.ConsumeResp beanExchange(BeanExchangeVO vo) throws org.apache.thrift.TException;

    /**
     * 管理员星币调整
     * 
     * @param vo
     */
    public com.kugou.fanxing.thrift.consume.service.ConsumeResp adminAdjustCoin(CoinAdjustVO vo) throws org.apache.thrift.TException;

    /**
     * 管理员星豆调整
     * 
     * @param vo
     */
    public com.kugou.fanxing.thrift.consume.service.ConsumeResp adminAdjustBean(BeanAdjustVO vo) throws org.apache.thrift.TException;

    /**
     * 结算加减星币
     * 
     * @param vo
     */
    public com.kugou.fanxing.thrift.consume.service.ConsumeResp beanSettlement(BeanSettlementVO vo) throws org.apache.thrift.TException;

  }

  public interface AsyncIface {

    public void refund(CoinVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void present(CoinVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void recharge(CoinVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void beanExchange(BeanExchangeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void adminAdjustCoin(CoinAdjustVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void adminAdjustBean(BeanAdjustVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void beanSettlement(BeanSettlementVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

  }

  public static class Client extends org.apache.thrift.TServiceClient implements Iface {
    public static class Factory implements org.apache.thrift.TServiceClientFactory<Client> {
      public Factory() {}
      public Client getClient(org.apache.thrift.protocol.TProtocol prot) {
        return new Client(prot);
      }
      public Client getClient(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
        return new Client(iprot, oprot);
      }
    }

    public Client(org.apache.thrift.protocol.TProtocol prot)
    {
      super(prot, prot);
    }

    public Client(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
      super(iprot, oprot);
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp refund(CoinVO vo) throws org.apache.thrift.TException
    {
      send_refund(vo);
      return recv_refund();
    }

    public void send_refund(CoinVO vo) throws org.apache.thrift.TException
    {
      refund_args args = new refund_args();
      args.setVo(vo);
      sendBase("refund", args);
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp recv_refund() throws org.apache.thrift.TException
    {
      refund_result result = new refund_result();
      receiveBase(result, "refund");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "refund failed: unknown result");
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp present(CoinVO vo) throws org.apache.thrift.TException
    {
      send_present(vo);
      return recv_present();
    }

    public void send_present(CoinVO vo) throws org.apache.thrift.TException
    {
      present_args args = new present_args();
      args.setVo(vo);
      sendBase("present", args);
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp recv_present() throws org.apache.thrift.TException
    {
      present_result result = new present_result();
      receiveBase(result, "present");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "present failed: unknown result");
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp recharge(CoinVO vo) throws org.apache.thrift.TException
    {
      send_recharge(vo);
      return recv_recharge();
    }

    public void send_recharge(CoinVO vo) throws org.apache.thrift.TException
    {
      recharge_args args = new recharge_args();
      args.setVo(vo);
      sendBase("recharge", args);
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp recv_recharge() throws org.apache.thrift.TException
    {
      recharge_result result = new recharge_result();
      receiveBase(result, "recharge");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "recharge failed: unknown result");
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp beanExchange(BeanExchangeVO vo) throws org.apache.thrift.TException
    {
      send_beanExchange(vo);
      return recv_beanExchange();
    }

    public void send_beanExchange(BeanExchangeVO vo) throws org.apache.thrift.TException
    {
      beanExchange_args args = new beanExchange_args();
      args.setVo(vo);
      sendBase("beanExchange", args);
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp recv_beanExchange() throws org.apache.thrift.TException
    {
      beanExchange_result result = new beanExchange_result();
      receiveBase(result, "beanExchange");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "beanExchange failed: unknown result");
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp adminAdjustCoin(CoinAdjustVO vo) throws org.apache.thrift.TException
    {
      send_adminAdjustCoin(vo);
      return recv_adminAdjustCoin();
    }

    public void send_adminAdjustCoin(CoinAdjustVO vo) throws org.apache.thrift.TException
    {
      adminAdjustCoin_args args = new adminAdjustCoin_args();
      args.setVo(vo);
      sendBase("adminAdjustCoin", args);
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp recv_adminAdjustCoin() throws org.apache.thrift.TException
    {
      adminAdjustCoin_result result = new adminAdjustCoin_result();
      receiveBase(result, "adminAdjustCoin");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "adminAdjustCoin failed: unknown result");
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp adminAdjustBean(BeanAdjustVO vo) throws org.apache.thrift.TException
    {
      send_adminAdjustBean(vo);
      return recv_adminAdjustBean();
    }

    public void send_adminAdjustBean(BeanAdjustVO vo) throws org.apache.thrift.TException
    {
      adminAdjustBean_args args = new adminAdjustBean_args();
      args.setVo(vo);
      sendBase("adminAdjustBean", args);
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp recv_adminAdjustBean() throws org.apache.thrift.TException
    {
      adminAdjustBean_result result = new adminAdjustBean_result();
      receiveBase(result, "adminAdjustBean");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "adminAdjustBean failed: unknown result");
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp beanSettlement(BeanSettlementVO vo) throws org.apache.thrift.TException
    {
      send_beanSettlement(vo);
      return recv_beanSettlement();
    }

    public void send_beanSettlement(BeanSettlementVO vo) throws org.apache.thrift.TException
    {
      beanSettlement_args args = new beanSettlement_args();
      args.setVo(vo);
      sendBase("beanSettlement", args);
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp recv_beanSettlement() throws org.apache.thrift.TException
    {
      beanSettlement_result result = new beanSettlement_result();
      receiveBase(result, "beanSettlement");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "beanSettlement failed: unknown result");
    }

  }
  public static class AsyncClient extends org.apache.thrift.async.TAsyncClient implements AsyncIface {
    public static class Factory implements org.apache.thrift.async.TAsyncClientFactory<AsyncClient> {
      private org.apache.thrift.async.TAsyncClientManager clientManager;
      private org.apache.thrift.protocol.TProtocolFactory protocolFactory;
      public Factory(org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.protocol.TProtocolFactory protocolFactory) {
        this.clientManager = clientManager;
        this.protocolFactory = protocolFactory;
      }
      public AsyncClient getAsyncClient(org.apache.thrift.transport.TNonblockingTransport transport) {
        return new AsyncClient(protocolFactory, clientManager, transport);
      }
    }

    public AsyncClient(org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.transport.TNonblockingTransport transport) {
      super(protocolFactory, clientManager, transport);
    }

    public void refund(CoinVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      refund_call method_call = new refund_call(vo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class refund_call extends org.apache.thrift.async.TAsyncMethodCall {
      private CoinVO vo;
      public refund_call(CoinVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.vo = vo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("refund", org.apache.thrift.protocol.TMessageType.CALL, 0));
        refund_args args = new refund_args();
        args.setVo(vo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.consume.service.ConsumeResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_refund();
      }
    }

    public void present(CoinVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      present_call method_call = new present_call(vo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class present_call extends org.apache.thrift.async.TAsyncMethodCall {
      private CoinVO vo;
      public present_call(CoinVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.vo = vo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("present", org.apache.thrift.protocol.TMessageType.CALL, 0));
        present_args args = new present_args();
        args.setVo(vo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.consume.service.ConsumeResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_present();
      }
    }

    public void recharge(CoinVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      recharge_call method_call = new recharge_call(vo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class recharge_call extends org.apache.thrift.async.TAsyncMethodCall {
      private CoinVO vo;
      public recharge_call(CoinVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.vo = vo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("recharge", org.apache.thrift.protocol.TMessageType.CALL, 0));
        recharge_args args = new recharge_args();
        args.setVo(vo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.consume.service.ConsumeResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_recharge();
      }
    }

    public void beanExchange(BeanExchangeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      beanExchange_call method_call = new beanExchange_call(vo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class beanExchange_call extends org.apache.thrift.async.TAsyncMethodCall {
      private BeanExchangeVO vo;
      public beanExchange_call(BeanExchangeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.vo = vo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("beanExchange", org.apache.thrift.protocol.TMessageType.CALL, 0));
        beanExchange_args args = new beanExchange_args();
        args.setVo(vo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.consume.service.ConsumeResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_beanExchange();
      }
    }

    public void adminAdjustCoin(CoinAdjustVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      adminAdjustCoin_call method_call = new adminAdjustCoin_call(vo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class adminAdjustCoin_call extends org.apache.thrift.async.TAsyncMethodCall {
      private CoinAdjustVO vo;
      public adminAdjustCoin_call(CoinAdjustVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.vo = vo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("adminAdjustCoin", org.apache.thrift.protocol.TMessageType.CALL, 0));
        adminAdjustCoin_args args = new adminAdjustCoin_args();
        args.setVo(vo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.consume.service.ConsumeResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_adminAdjustCoin();
      }
    }

    public void adminAdjustBean(BeanAdjustVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      adminAdjustBean_call method_call = new adminAdjustBean_call(vo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class adminAdjustBean_call extends org.apache.thrift.async.TAsyncMethodCall {
      private BeanAdjustVO vo;
      public adminAdjustBean_call(BeanAdjustVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.vo = vo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("adminAdjustBean", org.apache.thrift.protocol.TMessageType.CALL, 0));
        adminAdjustBean_args args = new adminAdjustBean_args();
        args.setVo(vo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.consume.service.ConsumeResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_adminAdjustBean();
      }
    }

    public void beanSettlement(BeanSettlementVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      beanSettlement_call method_call = new beanSettlement_call(vo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class beanSettlement_call extends org.apache.thrift.async.TAsyncMethodCall {
      private BeanSettlementVO vo;
      public beanSettlement_call(BeanSettlementVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.vo = vo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("beanSettlement", org.apache.thrift.protocol.TMessageType.CALL, 0));
        beanSettlement_args args = new beanSettlement_args();
        args.setVo(vo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.consume.service.ConsumeResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_beanSettlement();
      }
    }

  }

  public static class Processor<I extends Iface> extends org.apache.thrift.TBaseProcessor<I> implements org.apache.thrift.TProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());
    public Processor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>>()));
    }

    protected Processor(I iface, Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends Iface> Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> getProcessMap(Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      processMap.put("refund", new refund());
      processMap.put("present", new present());
      processMap.put("recharge", new recharge());
      processMap.put("beanExchange", new beanExchange());
      processMap.put("adminAdjustCoin", new adminAdjustCoin());
      processMap.put("adminAdjustBean", new adminAdjustBean());
      processMap.put("beanSettlement", new beanSettlement());
      return processMap;
    }

    public static class refund<I extends Iface> extends org.apache.thrift.ProcessFunction<I, refund_args> {
      public refund() {
        super("refund");
      }

      public refund_args getEmptyArgsInstance() {
        return new refund_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public refund_result getResult(I iface, refund_args args) throws org.apache.thrift.TException {
        refund_result result = new refund_result();
        result.success = iface.refund(args.vo);
        return result;
      }
    }

    public static class present<I extends Iface> extends org.apache.thrift.ProcessFunction<I, present_args> {
      public present() {
        super("present");
      }

      public present_args getEmptyArgsInstance() {
        return new present_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public present_result getResult(I iface, present_args args) throws org.apache.thrift.TException {
        present_result result = new present_result();
        result.success = iface.present(args.vo);
        return result;
      }
    }

    public static class recharge<I extends Iface> extends org.apache.thrift.ProcessFunction<I, recharge_args> {
      public recharge() {
        super("recharge");
      }

      public recharge_args getEmptyArgsInstance() {
        return new recharge_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public recharge_result getResult(I iface, recharge_args args) throws org.apache.thrift.TException {
        recharge_result result = new recharge_result();
        result.success = iface.recharge(args.vo);
        return result;
      }
    }

    public static class beanExchange<I extends Iface> extends org.apache.thrift.ProcessFunction<I, beanExchange_args> {
      public beanExchange() {
        super("beanExchange");
      }

      public beanExchange_args getEmptyArgsInstance() {
        return new beanExchange_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public beanExchange_result getResult(I iface, beanExchange_args args) throws org.apache.thrift.TException {
        beanExchange_result result = new beanExchange_result();
        result.success = iface.beanExchange(args.vo);
        return result;
      }
    }

    public static class adminAdjustCoin<I extends Iface> extends org.apache.thrift.ProcessFunction<I, adminAdjustCoin_args> {
      public adminAdjustCoin() {
        super("adminAdjustCoin");
      }

      public adminAdjustCoin_args getEmptyArgsInstance() {
        return new adminAdjustCoin_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public adminAdjustCoin_result getResult(I iface, adminAdjustCoin_args args) throws org.apache.thrift.TException {
        adminAdjustCoin_result result = new adminAdjustCoin_result();
        result.success = iface.adminAdjustCoin(args.vo);
        return result;
      }
    }

    public static class adminAdjustBean<I extends Iface> extends org.apache.thrift.ProcessFunction<I, adminAdjustBean_args> {
      public adminAdjustBean() {
        super("adminAdjustBean");
      }

      public adminAdjustBean_args getEmptyArgsInstance() {
        return new adminAdjustBean_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public adminAdjustBean_result getResult(I iface, adminAdjustBean_args args) throws org.apache.thrift.TException {
        adminAdjustBean_result result = new adminAdjustBean_result();
        result.success = iface.adminAdjustBean(args.vo);
        return result;
      }
    }

    public static class beanSettlement<I extends Iface> extends org.apache.thrift.ProcessFunction<I, beanSettlement_args> {
      public beanSettlement() {
        super("beanSettlement");
      }

      public beanSettlement_args getEmptyArgsInstance() {
        return new beanSettlement_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public beanSettlement_result getResult(I iface, beanSettlement_args args) throws org.apache.thrift.TException {
        beanSettlement_result result = new beanSettlement_result();
        result.success = iface.beanSettlement(args.vo);
        return result;
      }
    }

  }

  public static class AsyncProcessor<I extends AsyncIface> extends org.apache.thrift.TBaseAsyncProcessor<I> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());
    public AsyncProcessor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.AsyncProcessFunction<I, ? extends org.apache.thrift.TBase, ?>>()));
    }

    protected AsyncProcessor(I iface, Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends AsyncIface> Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase,?>> getProcessMap(Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      processMap.put("refund", new refund());
      processMap.put("present", new present());
      processMap.put("recharge", new recharge());
      processMap.put("beanExchange", new beanExchange());
      processMap.put("adminAdjustCoin", new adminAdjustCoin());
      processMap.put("adminAdjustBean", new adminAdjustBean());
      processMap.put("beanSettlement", new beanSettlement());
      return processMap;
    }

    public static class refund<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, refund_args, com.kugou.fanxing.thrift.consume.service.ConsumeResp> {
      public refund() {
        super("refund");
      }

      public refund_args getEmptyArgsInstance() {
        return new refund_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp>() { 
          public void onComplete(com.kugou.fanxing.thrift.consume.service.ConsumeResp o) {
            refund_result result = new refund_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            refund_result result = new refund_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, refund_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp> resultHandler) throws TException {
        iface.refund(args.vo,resultHandler);
      }
    }

    public static class present<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, present_args, com.kugou.fanxing.thrift.consume.service.ConsumeResp> {
      public present() {
        super("present");
      }

      public present_args getEmptyArgsInstance() {
        return new present_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp>() { 
          public void onComplete(com.kugou.fanxing.thrift.consume.service.ConsumeResp o) {
            present_result result = new present_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            present_result result = new present_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, present_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp> resultHandler) throws TException {
        iface.present(args.vo,resultHandler);
      }
    }

    public static class recharge<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, recharge_args, com.kugou.fanxing.thrift.consume.service.ConsumeResp> {
      public recharge() {
        super("recharge");
      }

      public recharge_args getEmptyArgsInstance() {
        return new recharge_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp>() { 
          public void onComplete(com.kugou.fanxing.thrift.consume.service.ConsumeResp o) {
            recharge_result result = new recharge_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            recharge_result result = new recharge_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, recharge_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp> resultHandler) throws TException {
        iface.recharge(args.vo,resultHandler);
      }
    }

    public static class beanExchange<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, beanExchange_args, com.kugou.fanxing.thrift.consume.service.ConsumeResp> {
      public beanExchange() {
        super("beanExchange");
      }

      public beanExchange_args getEmptyArgsInstance() {
        return new beanExchange_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp>() { 
          public void onComplete(com.kugou.fanxing.thrift.consume.service.ConsumeResp o) {
            beanExchange_result result = new beanExchange_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            beanExchange_result result = new beanExchange_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, beanExchange_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp> resultHandler) throws TException {
        iface.beanExchange(args.vo,resultHandler);
      }
    }

    public static class adminAdjustCoin<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, adminAdjustCoin_args, com.kugou.fanxing.thrift.consume.service.ConsumeResp> {
      public adminAdjustCoin() {
        super("adminAdjustCoin");
      }

      public adminAdjustCoin_args getEmptyArgsInstance() {
        return new adminAdjustCoin_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp>() { 
          public void onComplete(com.kugou.fanxing.thrift.consume.service.ConsumeResp o) {
            adminAdjustCoin_result result = new adminAdjustCoin_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            adminAdjustCoin_result result = new adminAdjustCoin_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, adminAdjustCoin_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp> resultHandler) throws TException {
        iface.adminAdjustCoin(args.vo,resultHandler);
      }
    }

    public static class adminAdjustBean<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, adminAdjustBean_args, com.kugou.fanxing.thrift.consume.service.ConsumeResp> {
      public adminAdjustBean() {
        super("adminAdjustBean");
      }

      public adminAdjustBean_args getEmptyArgsInstance() {
        return new adminAdjustBean_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp>() { 
          public void onComplete(com.kugou.fanxing.thrift.consume.service.ConsumeResp o) {
            adminAdjustBean_result result = new adminAdjustBean_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            adminAdjustBean_result result = new adminAdjustBean_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, adminAdjustBean_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp> resultHandler) throws TException {
        iface.adminAdjustBean(args.vo,resultHandler);
      }
    }

    public static class beanSettlement<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, beanSettlement_args, com.kugou.fanxing.thrift.consume.service.ConsumeResp> {
      public beanSettlement() {
        super("beanSettlement");
      }

      public beanSettlement_args getEmptyArgsInstance() {
        return new beanSettlement_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp>() { 
          public void onComplete(com.kugou.fanxing.thrift.consume.service.ConsumeResp o) {
            beanSettlement_result result = new beanSettlement_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            beanSettlement_result result = new beanSettlement_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, beanSettlement_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.consume.service.ConsumeResp> resultHandler) throws TException {
        iface.beanSettlement(args.vo,resultHandler);
      }
    }

  }

  public static class refund_args implements org.apache.thrift.TBase<refund_args, refund_args._Fields>, java.io.Serializable, Cloneable, Comparable<refund_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("refund_args");

    private static final org.apache.thrift.protocol.TField VO_FIELD_DESC = new org.apache.thrift.protocol.TField("vo", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new refund_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new refund_argsTupleSchemeFactory());
    }

    public CoinVO vo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      VO((short)1, "vo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // VO
            return VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.VO, new org.apache.thrift.meta_data.FieldMetaData("vo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, CoinVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(refund_args.class, metaDataMap);
    }

    public refund_args() {
    }

    public refund_args(
      CoinVO vo)
    {
      this();
      this.vo = vo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public refund_args(refund_args other) {
      if (other.isSetVo()) {
        this.vo = new CoinVO(other.vo);
      }
    }

    public refund_args deepCopy() {
      return new refund_args(this);
    }

    @Override
    public void clear() {
      this.vo = null;
    }

    public CoinVO getVo() {
      return this.vo;
    }

    public refund_args setVo(CoinVO vo) {
      this.vo = vo;
      return this;
    }

    public void unsetVo() {
      this.vo = null;
    }

    /** Returns true if field vo is set (has been assigned a value) and false otherwise */
    public boolean isSetVo() {
      return this.vo != null;
    }

    public void setVoIsSet(boolean value) {
      if (!value) {
        this.vo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case VO:
        if (value == null) {
          unsetVo();
        } else {
          setVo((CoinVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case VO:
        return getVo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case VO:
        return isSetVo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof refund_args)
        return this.equals((refund_args)that);
      return false;
    }

    public boolean equals(refund_args that) {
      if (that == null)
        return false;

      boolean this_present_vo = true && this.isSetVo();
      boolean that_present_vo = true && that.isSetVo();
      if (this_present_vo || that_present_vo) {
        if (!(this_present_vo && that_present_vo))
          return false;
        if (!this.vo.equals(that.vo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_vo = true && (isSetVo());
      list.add(present_vo);
      if (present_vo)
        list.add(vo);

      return list.hashCode();
    }

    @Override
    public int compareTo(refund_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetVo()).compareTo(other.isSetVo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetVo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.vo, other.vo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("refund_args(");
      boolean first = true;

      sb.append("vo:");
      if (this.vo == null) {
        sb.append("null");
      } else {
        sb.append(this.vo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (vo != null) {
        vo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class refund_argsStandardSchemeFactory implements SchemeFactory {
      public refund_argsStandardScheme getScheme() {
        return new refund_argsStandardScheme();
      }
    }

    private static class refund_argsStandardScheme extends StandardScheme<refund_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, refund_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.vo = new CoinVO();
                struct.vo.read(iprot);
                struct.setVoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, refund_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.vo != null) {
          oprot.writeFieldBegin(VO_FIELD_DESC);
          struct.vo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class refund_argsTupleSchemeFactory implements SchemeFactory {
      public refund_argsTupleScheme getScheme() {
        return new refund_argsTupleScheme();
      }
    }

    private static class refund_argsTupleScheme extends TupleScheme<refund_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, refund_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetVo()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetVo()) {
          struct.vo.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, refund_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.vo = new CoinVO();
          struct.vo.read(iprot);
          struct.setVoIsSet(true);
        }
      }
    }

  }

  public static class refund_result implements org.apache.thrift.TBase<refund_result, refund_result._Fields>, java.io.Serializable, Cloneable, Comparable<refund_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("refund_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new refund_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new refund_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.consume.service.ConsumeResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(refund_result.class, metaDataMap);
    }

    public refund_result() {
    }

    public refund_result(
      com.kugou.fanxing.thrift.consume.service.ConsumeResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public refund_result(refund_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp(other.success);
      }
    }

    public refund_result deepCopy() {
      return new refund_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp getSuccess() {
      return this.success;
    }

    public refund_result setSuccess(com.kugou.fanxing.thrift.consume.service.ConsumeResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.consume.service.ConsumeResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof refund_result)
        return this.equals((refund_result)that);
      return false;
    }

    public boolean equals(refund_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(refund_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("refund_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class refund_resultStandardSchemeFactory implements SchemeFactory {
      public refund_resultStandardScheme getScheme() {
        return new refund_resultStandardScheme();
      }
    }

    private static class refund_resultStandardScheme extends StandardScheme<refund_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, refund_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, refund_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class refund_resultTupleSchemeFactory implements SchemeFactory {
      public refund_resultTupleScheme getScheme() {
        return new refund_resultTupleScheme();
      }
    }

    private static class refund_resultTupleScheme extends TupleScheme<refund_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, refund_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, refund_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class present_args implements org.apache.thrift.TBase<present_args, present_args._Fields>, java.io.Serializable, Cloneable, Comparable<present_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("present_args");

    private static final org.apache.thrift.protocol.TField VO_FIELD_DESC = new org.apache.thrift.protocol.TField("vo", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new present_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new present_argsTupleSchemeFactory());
    }

    public CoinVO vo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      VO((short)1, "vo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // VO
            return VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.VO, new org.apache.thrift.meta_data.FieldMetaData("vo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, CoinVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(present_args.class, metaDataMap);
    }

    public present_args() {
    }

    public present_args(
      CoinVO vo)
    {
      this();
      this.vo = vo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public present_args(present_args other) {
      if (other.isSetVo()) {
        this.vo = new CoinVO(other.vo);
      }
    }

    public present_args deepCopy() {
      return new present_args(this);
    }

    @Override
    public void clear() {
      this.vo = null;
    }

    public CoinVO getVo() {
      return this.vo;
    }

    public present_args setVo(CoinVO vo) {
      this.vo = vo;
      return this;
    }

    public void unsetVo() {
      this.vo = null;
    }

    /** Returns true if field vo is set (has been assigned a value) and false otherwise */
    public boolean isSetVo() {
      return this.vo != null;
    }

    public void setVoIsSet(boolean value) {
      if (!value) {
        this.vo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case VO:
        if (value == null) {
          unsetVo();
        } else {
          setVo((CoinVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case VO:
        return getVo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case VO:
        return isSetVo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof present_args)
        return this.equals((present_args)that);
      return false;
    }

    public boolean equals(present_args that) {
      if (that == null)
        return false;

      boolean this_present_vo = true && this.isSetVo();
      boolean that_present_vo = true && that.isSetVo();
      if (this_present_vo || that_present_vo) {
        if (!(this_present_vo && that_present_vo))
          return false;
        if (!this.vo.equals(that.vo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_vo = true && (isSetVo());
      list.add(present_vo);
      if (present_vo)
        list.add(vo);

      return list.hashCode();
    }

    @Override
    public int compareTo(present_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetVo()).compareTo(other.isSetVo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetVo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.vo, other.vo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("present_args(");
      boolean first = true;

      sb.append("vo:");
      if (this.vo == null) {
        sb.append("null");
      } else {
        sb.append(this.vo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (vo != null) {
        vo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class present_argsStandardSchemeFactory implements SchemeFactory {
      public present_argsStandardScheme getScheme() {
        return new present_argsStandardScheme();
      }
    }

    private static class present_argsStandardScheme extends StandardScheme<present_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, present_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.vo = new CoinVO();
                struct.vo.read(iprot);
                struct.setVoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, present_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.vo != null) {
          oprot.writeFieldBegin(VO_FIELD_DESC);
          struct.vo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class present_argsTupleSchemeFactory implements SchemeFactory {
      public present_argsTupleScheme getScheme() {
        return new present_argsTupleScheme();
      }
    }

    private static class present_argsTupleScheme extends TupleScheme<present_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, present_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetVo()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetVo()) {
          struct.vo.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, present_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.vo = new CoinVO();
          struct.vo.read(iprot);
          struct.setVoIsSet(true);
        }
      }
    }

  }

  public static class present_result implements org.apache.thrift.TBase<present_result, present_result._Fields>, java.io.Serializable, Cloneable, Comparable<present_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("present_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new present_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new present_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.consume.service.ConsumeResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(present_result.class, metaDataMap);
    }

    public present_result() {
    }

    public present_result(
      com.kugou.fanxing.thrift.consume.service.ConsumeResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public present_result(present_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp(other.success);
      }
    }

    public present_result deepCopy() {
      return new present_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp getSuccess() {
      return this.success;
    }

    public present_result setSuccess(com.kugou.fanxing.thrift.consume.service.ConsumeResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.consume.service.ConsumeResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof present_result)
        return this.equals((present_result)that);
      return false;
    }

    public boolean equals(present_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(present_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("present_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class present_resultStandardSchemeFactory implements SchemeFactory {
      public present_resultStandardScheme getScheme() {
        return new present_resultStandardScheme();
      }
    }

    private static class present_resultStandardScheme extends StandardScheme<present_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, present_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, present_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class present_resultTupleSchemeFactory implements SchemeFactory {
      public present_resultTupleScheme getScheme() {
        return new present_resultTupleScheme();
      }
    }

    private static class present_resultTupleScheme extends TupleScheme<present_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, present_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, present_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class recharge_args implements org.apache.thrift.TBase<recharge_args, recharge_args._Fields>, java.io.Serializable, Cloneable, Comparable<recharge_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("recharge_args");

    private static final org.apache.thrift.protocol.TField VO_FIELD_DESC = new org.apache.thrift.protocol.TField("vo", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new recharge_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new recharge_argsTupleSchemeFactory());
    }

    public CoinVO vo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      VO((short)1, "vo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // VO
            return VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.VO, new org.apache.thrift.meta_data.FieldMetaData("vo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, CoinVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(recharge_args.class, metaDataMap);
    }

    public recharge_args() {
    }

    public recharge_args(
      CoinVO vo)
    {
      this();
      this.vo = vo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public recharge_args(recharge_args other) {
      if (other.isSetVo()) {
        this.vo = new CoinVO(other.vo);
      }
    }

    public recharge_args deepCopy() {
      return new recharge_args(this);
    }

    @Override
    public void clear() {
      this.vo = null;
    }

    public CoinVO getVo() {
      return this.vo;
    }

    public recharge_args setVo(CoinVO vo) {
      this.vo = vo;
      return this;
    }

    public void unsetVo() {
      this.vo = null;
    }

    /** Returns true if field vo is set (has been assigned a value) and false otherwise */
    public boolean isSetVo() {
      return this.vo != null;
    }

    public void setVoIsSet(boolean value) {
      if (!value) {
        this.vo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case VO:
        if (value == null) {
          unsetVo();
        } else {
          setVo((CoinVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case VO:
        return getVo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case VO:
        return isSetVo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof recharge_args)
        return this.equals((recharge_args)that);
      return false;
    }

    public boolean equals(recharge_args that) {
      if (that == null)
        return false;

      boolean this_present_vo = true && this.isSetVo();
      boolean that_present_vo = true && that.isSetVo();
      if (this_present_vo || that_present_vo) {
        if (!(this_present_vo && that_present_vo))
          return false;
        if (!this.vo.equals(that.vo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_vo = true && (isSetVo());
      list.add(present_vo);
      if (present_vo)
        list.add(vo);

      return list.hashCode();
    }

    @Override
    public int compareTo(recharge_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetVo()).compareTo(other.isSetVo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetVo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.vo, other.vo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("recharge_args(");
      boolean first = true;

      sb.append("vo:");
      if (this.vo == null) {
        sb.append("null");
      } else {
        sb.append(this.vo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (vo != null) {
        vo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class recharge_argsStandardSchemeFactory implements SchemeFactory {
      public recharge_argsStandardScheme getScheme() {
        return new recharge_argsStandardScheme();
      }
    }

    private static class recharge_argsStandardScheme extends StandardScheme<recharge_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, recharge_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.vo = new CoinVO();
                struct.vo.read(iprot);
                struct.setVoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, recharge_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.vo != null) {
          oprot.writeFieldBegin(VO_FIELD_DESC);
          struct.vo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class recharge_argsTupleSchemeFactory implements SchemeFactory {
      public recharge_argsTupleScheme getScheme() {
        return new recharge_argsTupleScheme();
      }
    }

    private static class recharge_argsTupleScheme extends TupleScheme<recharge_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, recharge_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetVo()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetVo()) {
          struct.vo.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, recharge_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.vo = new CoinVO();
          struct.vo.read(iprot);
          struct.setVoIsSet(true);
        }
      }
    }

  }

  public static class recharge_result implements org.apache.thrift.TBase<recharge_result, recharge_result._Fields>, java.io.Serializable, Cloneable, Comparable<recharge_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("recharge_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new recharge_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new recharge_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.consume.service.ConsumeResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(recharge_result.class, metaDataMap);
    }

    public recharge_result() {
    }

    public recharge_result(
      com.kugou.fanxing.thrift.consume.service.ConsumeResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public recharge_result(recharge_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp(other.success);
      }
    }

    public recharge_result deepCopy() {
      return new recharge_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp getSuccess() {
      return this.success;
    }

    public recharge_result setSuccess(com.kugou.fanxing.thrift.consume.service.ConsumeResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.consume.service.ConsumeResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof recharge_result)
        return this.equals((recharge_result)that);
      return false;
    }

    public boolean equals(recharge_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(recharge_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("recharge_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class recharge_resultStandardSchemeFactory implements SchemeFactory {
      public recharge_resultStandardScheme getScheme() {
        return new recharge_resultStandardScheme();
      }
    }

    private static class recharge_resultStandardScheme extends StandardScheme<recharge_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, recharge_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, recharge_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class recharge_resultTupleSchemeFactory implements SchemeFactory {
      public recharge_resultTupleScheme getScheme() {
        return new recharge_resultTupleScheme();
      }
    }

    private static class recharge_resultTupleScheme extends TupleScheme<recharge_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, recharge_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, recharge_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class beanExchange_args implements org.apache.thrift.TBase<beanExchange_args, beanExchange_args._Fields>, java.io.Serializable, Cloneable, Comparable<beanExchange_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("beanExchange_args");

    private static final org.apache.thrift.protocol.TField VO_FIELD_DESC = new org.apache.thrift.protocol.TField("vo", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new beanExchange_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new beanExchange_argsTupleSchemeFactory());
    }

    public BeanExchangeVO vo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      VO((short)1, "vo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // VO
            return VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.VO, new org.apache.thrift.meta_data.FieldMetaData("vo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, BeanExchangeVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(beanExchange_args.class, metaDataMap);
    }

    public beanExchange_args() {
    }

    public beanExchange_args(
      BeanExchangeVO vo)
    {
      this();
      this.vo = vo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public beanExchange_args(beanExchange_args other) {
      if (other.isSetVo()) {
        this.vo = new BeanExchangeVO(other.vo);
      }
    }

    public beanExchange_args deepCopy() {
      return new beanExchange_args(this);
    }

    @Override
    public void clear() {
      this.vo = null;
    }

    public BeanExchangeVO getVo() {
      return this.vo;
    }

    public beanExchange_args setVo(BeanExchangeVO vo) {
      this.vo = vo;
      return this;
    }

    public void unsetVo() {
      this.vo = null;
    }

    /** Returns true if field vo is set (has been assigned a value) and false otherwise */
    public boolean isSetVo() {
      return this.vo != null;
    }

    public void setVoIsSet(boolean value) {
      if (!value) {
        this.vo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case VO:
        if (value == null) {
          unsetVo();
        } else {
          setVo((BeanExchangeVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case VO:
        return getVo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case VO:
        return isSetVo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof beanExchange_args)
        return this.equals((beanExchange_args)that);
      return false;
    }

    public boolean equals(beanExchange_args that) {
      if (that == null)
        return false;

      boolean this_present_vo = true && this.isSetVo();
      boolean that_present_vo = true && that.isSetVo();
      if (this_present_vo || that_present_vo) {
        if (!(this_present_vo && that_present_vo))
          return false;
        if (!this.vo.equals(that.vo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_vo = true && (isSetVo());
      list.add(present_vo);
      if (present_vo)
        list.add(vo);

      return list.hashCode();
    }

    @Override
    public int compareTo(beanExchange_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetVo()).compareTo(other.isSetVo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetVo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.vo, other.vo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("beanExchange_args(");
      boolean first = true;

      sb.append("vo:");
      if (this.vo == null) {
        sb.append("null");
      } else {
        sb.append(this.vo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (vo != null) {
        vo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class beanExchange_argsStandardSchemeFactory implements SchemeFactory {
      public beanExchange_argsStandardScheme getScheme() {
        return new beanExchange_argsStandardScheme();
      }
    }

    private static class beanExchange_argsStandardScheme extends StandardScheme<beanExchange_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, beanExchange_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.vo = new BeanExchangeVO();
                struct.vo.read(iprot);
                struct.setVoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, beanExchange_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.vo != null) {
          oprot.writeFieldBegin(VO_FIELD_DESC);
          struct.vo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class beanExchange_argsTupleSchemeFactory implements SchemeFactory {
      public beanExchange_argsTupleScheme getScheme() {
        return new beanExchange_argsTupleScheme();
      }
    }

    private static class beanExchange_argsTupleScheme extends TupleScheme<beanExchange_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, beanExchange_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetVo()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetVo()) {
          struct.vo.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, beanExchange_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.vo = new BeanExchangeVO();
          struct.vo.read(iprot);
          struct.setVoIsSet(true);
        }
      }
    }

  }

  public static class beanExchange_result implements org.apache.thrift.TBase<beanExchange_result, beanExchange_result._Fields>, java.io.Serializable, Cloneable, Comparable<beanExchange_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("beanExchange_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new beanExchange_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new beanExchange_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.consume.service.ConsumeResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(beanExchange_result.class, metaDataMap);
    }

    public beanExchange_result() {
    }

    public beanExchange_result(
      com.kugou.fanxing.thrift.consume.service.ConsumeResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public beanExchange_result(beanExchange_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp(other.success);
      }
    }

    public beanExchange_result deepCopy() {
      return new beanExchange_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp getSuccess() {
      return this.success;
    }

    public beanExchange_result setSuccess(com.kugou.fanxing.thrift.consume.service.ConsumeResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.consume.service.ConsumeResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof beanExchange_result)
        return this.equals((beanExchange_result)that);
      return false;
    }

    public boolean equals(beanExchange_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(beanExchange_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("beanExchange_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class beanExchange_resultStandardSchemeFactory implements SchemeFactory {
      public beanExchange_resultStandardScheme getScheme() {
        return new beanExchange_resultStandardScheme();
      }
    }

    private static class beanExchange_resultStandardScheme extends StandardScheme<beanExchange_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, beanExchange_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, beanExchange_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class beanExchange_resultTupleSchemeFactory implements SchemeFactory {
      public beanExchange_resultTupleScheme getScheme() {
        return new beanExchange_resultTupleScheme();
      }
    }

    private static class beanExchange_resultTupleScheme extends TupleScheme<beanExchange_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, beanExchange_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, beanExchange_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class adminAdjustCoin_args implements org.apache.thrift.TBase<adminAdjustCoin_args, adminAdjustCoin_args._Fields>, java.io.Serializable, Cloneable, Comparable<adminAdjustCoin_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("adminAdjustCoin_args");

    private static final org.apache.thrift.protocol.TField VO_FIELD_DESC = new org.apache.thrift.protocol.TField("vo", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new adminAdjustCoin_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new adminAdjustCoin_argsTupleSchemeFactory());
    }

    public CoinAdjustVO vo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      VO((short)1, "vo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // VO
            return VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.VO, new org.apache.thrift.meta_data.FieldMetaData("vo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, CoinAdjustVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(adminAdjustCoin_args.class, metaDataMap);
    }

    public adminAdjustCoin_args() {
    }

    public adminAdjustCoin_args(
      CoinAdjustVO vo)
    {
      this();
      this.vo = vo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public adminAdjustCoin_args(adminAdjustCoin_args other) {
      if (other.isSetVo()) {
        this.vo = new CoinAdjustVO(other.vo);
      }
    }

    public adminAdjustCoin_args deepCopy() {
      return new adminAdjustCoin_args(this);
    }

    @Override
    public void clear() {
      this.vo = null;
    }

    public CoinAdjustVO getVo() {
      return this.vo;
    }

    public adminAdjustCoin_args setVo(CoinAdjustVO vo) {
      this.vo = vo;
      return this;
    }

    public void unsetVo() {
      this.vo = null;
    }

    /** Returns true if field vo is set (has been assigned a value) and false otherwise */
    public boolean isSetVo() {
      return this.vo != null;
    }

    public void setVoIsSet(boolean value) {
      if (!value) {
        this.vo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case VO:
        if (value == null) {
          unsetVo();
        } else {
          setVo((CoinAdjustVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case VO:
        return getVo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case VO:
        return isSetVo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof adminAdjustCoin_args)
        return this.equals((adminAdjustCoin_args)that);
      return false;
    }

    public boolean equals(adminAdjustCoin_args that) {
      if (that == null)
        return false;

      boolean this_present_vo = true && this.isSetVo();
      boolean that_present_vo = true && that.isSetVo();
      if (this_present_vo || that_present_vo) {
        if (!(this_present_vo && that_present_vo))
          return false;
        if (!this.vo.equals(that.vo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_vo = true && (isSetVo());
      list.add(present_vo);
      if (present_vo)
        list.add(vo);

      return list.hashCode();
    }

    @Override
    public int compareTo(adminAdjustCoin_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetVo()).compareTo(other.isSetVo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetVo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.vo, other.vo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("adminAdjustCoin_args(");
      boolean first = true;

      sb.append("vo:");
      if (this.vo == null) {
        sb.append("null");
      } else {
        sb.append(this.vo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (vo != null) {
        vo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class adminAdjustCoin_argsStandardSchemeFactory implements SchemeFactory {
      public adminAdjustCoin_argsStandardScheme getScheme() {
        return new adminAdjustCoin_argsStandardScheme();
      }
    }

    private static class adminAdjustCoin_argsStandardScheme extends StandardScheme<adminAdjustCoin_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, adminAdjustCoin_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.vo = new CoinAdjustVO();
                struct.vo.read(iprot);
                struct.setVoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, adminAdjustCoin_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.vo != null) {
          oprot.writeFieldBegin(VO_FIELD_DESC);
          struct.vo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class adminAdjustCoin_argsTupleSchemeFactory implements SchemeFactory {
      public adminAdjustCoin_argsTupleScheme getScheme() {
        return new adminAdjustCoin_argsTupleScheme();
      }
    }

    private static class adminAdjustCoin_argsTupleScheme extends TupleScheme<adminAdjustCoin_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, adminAdjustCoin_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetVo()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetVo()) {
          struct.vo.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, adminAdjustCoin_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.vo = new CoinAdjustVO();
          struct.vo.read(iprot);
          struct.setVoIsSet(true);
        }
      }
    }

  }

  public static class adminAdjustCoin_result implements org.apache.thrift.TBase<adminAdjustCoin_result, adminAdjustCoin_result._Fields>, java.io.Serializable, Cloneable, Comparable<adminAdjustCoin_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("adminAdjustCoin_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new adminAdjustCoin_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new adminAdjustCoin_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.consume.service.ConsumeResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(adminAdjustCoin_result.class, metaDataMap);
    }

    public adminAdjustCoin_result() {
    }

    public adminAdjustCoin_result(
      com.kugou.fanxing.thrift.consume.service.ConsumeResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public adminAdjustCoin_result(adminAdjustCoin_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp(other.success);
      }
    }

    public adminAdjustCoin_result deepCopy() {
      return new adminAdjustCoin_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp getSuccess() {
      return this.success;
    }

    public adminAdjustCoin_result setSuccess(com.kugou.fanxing.thrift.consume.service.ConsumeResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.consume.service.ConsumeResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof adminAdjustCoin_result)
        return this.equals((adminAdjustCoin_result)that);
      return false;
    }

    public boolean equals(adminAdjustCoin_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(adminAdjustCoin_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("adminAdjustCoin_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class adminAdjustCoin_resultStandardSchemeFactory implements SchemeFactory {
      public adminAdjustCoin_resultStandardScheme getScheme() {
        return new adminAdjustCoin_resultStandardScheme();
      }
    }

    private static class adminAdjustCoin_resultStandardScheme extends StandardScheme<adminAdjustCoin_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, adminAdjustCoin_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, adminAdjustCoin_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class adminAdjustCoin_resultTupleSchemeFactory implements SchemeFactory {
      public adminAdjustCoin_resultTupleScheme getScheme() {
        return new adminAdjustCoin_resultTupleScheme();
      }
    }

    private static class adminAdjustCoin_resultTupleScheme extends TupleScheme<adminAdjustCoin_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, adminAdjustCoin_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, adminAdjustCoin_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class adminAdjustBean_args implements org.apache.thrift.TBase<adminAdjustBean_args, adminAdjustBean_args._Fields>, java.io.Serializable, Cloneable, Comparable<adminAdjustBean_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("adminAdjustBean_args");

    private static final org.apache.thrift.protocol.TField VO_FIELD_DESC = new org.apache.thrift.protocol.TField("vo", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new adminAdjustBean_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new adminAdjustBean_argsTupleSchemeFactory());
    }

    public BeanAdjustVO vo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      VO((short)1, "vo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // VO
            return VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.VO, new org.apache.thrift.meta_data.FieldMetaData("vo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, BeanAdjustVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(adminAdjustBean_args.class, metaDataMap);
    }

    public adminAdjustBean_args() {
    }

    public adminAdjustBean_args(
      BeanAdjustVO vo)
    {
      this();
      this.vo = vo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public adminAdjustBean_args(adminAdjustBean_args other) {
      if (other.isSetVo()) {
        this.vo = new BeanAdjustVO(other.vo);
      }
    }

    public adminAdjustBean_args deepCopy() {
      return new adminAdjustBean_args(this);
    }

    @Override
    public void clear() {
      this.vo = null;
    }

    public BeanAdjustVO getVo() {
      return this.vo;
    }

    public adminAdjustBean_args setVo(BeanAdjustVO vo) {
      this.vo = vo;
      return this;
    }

    public void unsetVo() {
      this.vo = null;
    }

    /** Returns true if field vo is set (has been assigned a value) and false otherwise */
    public boolean isSetVo() {
      return this.vo != null;
    }

    public void setVoIsSet(boolean value) {
      if (!value) {
        this.vo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case VO:
        if (value == null) {
          unsetVo();
        } else {
          setVo((BeanAdjustVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case VO:
        return getVo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case VO:
        return isSetVo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof adminAdjustBean_args)
        return this.equals((adminAdjustBean_args)that);
      return false;
    }

    public boolean equals(adminAdjustBean_args that) {
      if (that == null)
        return false;

      boolean this_present_vo = true && this.isSetVo();
      boolean that_present_vo = true && that.isSetVo();
      if (this_present_vo || that_present_vo) {
        if (!(this_present_vo && that_present_vo))
          return false;
        if (!this.vo.equals(that.vo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_vo = true && (isSetVo());
      list.add(present_vo);
      if (present_vo)
        list.add(vo);

      return list.hashCode();
    }

    @Override
    public int compareTo(adminAdjustBean_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetVo()).compareTo(other.isSetVo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetVo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.vo, other.vo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("adminAdjustBean_args(");
      boolean first = true;

      sb.append("vo:");
      if (this.vo == null) {
        sb.append("null");
      } else {
        sb.append(this.vo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (vo != null) {
        vo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class adminAdjustBean_argsStandardSchemeFactory implements SchemeFactory {
      public adminAdjustBean_argsStandardScheme getScheme() {
        return new adminAdjustBean_argsStandardScheme();
      }
    }

    private static class adminAdjustBean_argsStandardScheme extends StandardScheme<adminAdjustBean_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, adminAdjustBean_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.vo = new BeanAdjustVO();
                struct.vo.read(iprot);
                struct.setVoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, adminAdjustBean_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.vo != null) {
          oprot.writeFieldBegin(VO_FIELD_DESC);
          struct.vo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class adminAdjustBean_argsTupleSchemeFactory implements SchemeFactory {
      public adminAdjustBean_argsTupleScheme getScheme() {
        return new adminAdjustBean_argsTupleScheme();
      }
    }

    private static class adminAdjustBean_argsTupleScheme extends TupleScheme<adminAdjustBean_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, adminAdjustBean_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetVo()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetVo()) {
          struct.vo.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, adminAdjustBean_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.vo = new BeanAdjustVO();
          struct.vo.read(iprot);
          struct.setVoIsSet(true);
        }
      }
    }

  }

  public static class adminAdjustBean_result implements org.apache.thrift.TBase<adminAdjustBean_result, adminAdjustBean_result._Fields>, java.io.Serializable, Cloneable, Comparable<adminAdjustBean_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("adminAdjustBean_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new adminAdjustBean_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new adminAdjustBean_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.consume.service.ConsumeResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(adminAdjustBean_result.class, metaDataMap);
    }

    public adminAdjustBean_result() {
    }

    public adminAdjustBean_result(
      com.kugou.fanxing.thrift.consume.service.ConsumeResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public adminAdjustBean_result(adminAdjustBean_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp(other.success);
      }
    }

    public adminAdjustBean_result deepCopy() {
      return new adminAdjustBean_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp getSuccess() {
      return this.success;
    }

    public adminAdjustBean_result setSuccess(com.kugou.fanxing.thrift.consume.service.ConsumeResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.consume.service.ConsumeResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof adminAdjustBean_result)
        return this.equals((adminAdjustBean_result)that);
      return false;
    }

    public boolean equals(adminAdjustBean_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(adminAdjustBean_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("adminAdjustBean_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class adminAdjustBean_resultStandardSchemeFactory implements SchemeFactory {
      public adminAdjustBean_resultStandardScheme getScheme() {
        return new adminAdjustBean_resultStandardScheme();
      }
    }

    private static class adminAdjustBean_resultStandardScheme extends StandardScheme<adminAdjustBean_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, adminAdjustBean_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, adminAdjustBean_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class adminAdjustBean_resultTupleSchemeFactory implements SchemeFactory {
      public adminAdjustBean_resultTupleScheme getScheme() {
        return new adminAdjustBean_resultTupleScheme();
      }
    }

    private static class adminAdjustBean_resultTupleScheme extends TupleScheme<adminAdjustBean_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, adminAdjustBean_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, adminAdjustBean_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class beanSettlement_args implements org.apache.thrift.TBase<beanSettlement_args, beanSettlement_args._Fields>, java.io.Serializable, Cloneable, Comparable<beanSettlement_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("beanSettlement_args");

    private static final org.apache.thrift.protocol.TField VO_FIELD_DESC = new org.apache.thrift.protocol.TField("vo", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new beanSettlement_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new beanSettlement_argsTupleSchemeFactory());
    }

    public BeanSettlementVO vo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      VO((short)1, "vo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // VO
            return VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.VO, new org.apache.thrift.meta_data.FieldMetaData("vo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, BeanSettlementVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(beanSettlement_args.class, metaDataMap);
    }

    public beanSettlement_args() {
    }

    public beanSettlement_args(
      BeanSettlementVO vo)
    {
      this();
      this.vo = vo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public beanSettlement_args(beanSettlement_args other) {
      if (other.isSetVo()) {
        this.vo = new BeanSettlementVO(other.vo);
      }
    }

    public beanSettlement_args deepCopy() {
      return new beanSettlement_args(this);
    }

    @Override
    public void clear() {
      this.vo = null;
    }

    public BeanSettlementVO getVo() {
      return this.vo;
    }

    public beanSettlement_args setVo(BeanSettlementVO vo) {
      this.vo = vo;
      return this;
    }

    public void unsetVo() {
      this.vo = null;
    }

    /** Returns true if field vo is set (has been assigned a value) and false otherwise */
    public boolean isSetVo() {
      return this.vo != null;
    }

    public void setVoIsSet(boolean value) {
      if (!value) {
        this.vo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case VO:
        if (value == null) {
          unsetVo();
        } else {
          setVo((BeanSettlementVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case VO:
        return getVo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case VO:
        return isSetVo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof beanSettlement_args)
        return this.equals((beanSettlement_args)that);
      return false;
    }

    public boolean equals(beanSettlement_args that) {
      if (that == null)
        return false;

      boolean this_present_vo = true && this.isSetVo();
      boolean that_present_vo = true && that.isSetVo();
      if (this_present_vo || that_present_vo) {
        if (!(this_present_vo && that_present_vo))
          return false;
        if (!this.vo.equals(that.vo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_vo = true && (isSetVo());
      list.add(present_vo);
      if (present_vo)
        list.add(vo);

      return list.hashCode();
    }

    @Override
    public int compareTo(beanSettlement_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetVo()).compareTo(other.isSetVo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetVo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.vo, other.vo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("beanSettlement_args(");
      boolean first = true;

      sb.append("vo:");
      if (this.vo == null) {
        sb.append("null");
      } else {
        sb.append(this.vo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (vo != null) {
        vo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class beanSettlement_argsStandardSchemeFactory implements SchemeFactory {
      public beanSettlement_argsStandardScheme getScheme() {
        return new beanSettlement_argsStandardScheme();
      }
    }

    private static class beanSettlement_argsStandardScheme extends StandardScheme<beanSettlement_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, beanSettlement_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.vo = new BeanSettlementVO();
                struct.vo.read(iprot);
                struct.setVoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, beanSettlement_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.vo != null) {
          oprot.writeFieldBegin(VO_FIELD_DESC);
          struct.vo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class beanSettlement_argsTupleSchemeFactory implements SchemeFactory {
      public beanSettlement_argsTupleScheme getScheme() {
        return new beanSettlement_argsTupleScheme();
      }
    }

    private static class beanSettlement_argsTupleScheme extends TupleScheme<beanSettlement_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, beanSettlement_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetVo()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetVo()) {
          struct.vo.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, beanSettlement_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.vo = new BeanSettlementVO();
          struct.vo.read(iprot);
          struct.setVoIsSet(true);
        }
      }
    }

  }

  public static class beanSettlement_result implements org.apache.thrift.TBase<beanSettlement_result, beanSettlement_result._Fields>, java.io.Serializable, Cloneable, Comparable<beanSettlement_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("beanSettlement_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new beanSettlement_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new beanSettlement_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.consume.service.ConsumeResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(beanSettlement_result.class, metaDataMap);
    }

    public beanSettlement_result() {
    }

    public beanSettlement_result(
      com.kugou.fanxing.thrift.consume.service.ConsumeResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public beanSettlement_result(beanSettlement_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp(other.success);
      }
    }

    public beanSettlement_result deepCopy() {
      return new beanSettlement_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.consume.service.ConsumeResp getSuccess() {
      return this.success;
    }

    public beanSettlement_result setSuccess(com.kugou.fanxing.thrift.consume.service.ConsumeResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.consume.service.ConsumeResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof beanSettlement_result)
        return this.equals((beanSettlement_result)that);
      return false;
    }

    public boolean equals(beanSettlement_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(beanSettlement_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("beanSettlement_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class beanSettlement_resultStandardSchemeFactory implements SchemeFactory {
      public beanSettlement_resultStandardScheme getScheme() {
        return new beanSettlement_resultStandardScheme();
      }
    }

    private static class beanSettlement_resultStandardScheme extends StandardScheme<beanSettlement_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, beanSettlement_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, beanSettlement_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class beanSettlement_resultTupleSchemeFactory implements SchemeFactory {
      public beanSettlement_resultTupleScheme getScheme() {
        return new beanSettlement_resultTupleScheme();
      }
    }

    private static class beanSettlement_resultTupleScheme extends TupleScheme<beanSettlement_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, beanSettlement_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, beanSettlement_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.consume.service.ConsumeResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

}
