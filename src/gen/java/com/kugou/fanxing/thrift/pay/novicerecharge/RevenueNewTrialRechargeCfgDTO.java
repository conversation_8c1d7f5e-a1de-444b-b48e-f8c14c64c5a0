/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.thrift.pay.novicerecharge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-06-23")
public class RevenueNewTrialRechargeCfgDTO implements org.apache.thrift.TBase<RevenueNewTrialRechargeCfgDTO, RevenueNewTrialRechargeCfgDTO._Fields>, java.io.Serializable, Cloneable, Comparable<RevenueNewTrialRechargeCfgDTO> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("RevenueNewTrialRechargeCfgDTO");

  private static final org.apache.thrift.protocol.TField DESC_FIELD_DESC = new org.apache.thrift.protocol.TField("desc", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField AWARD_DESC_FIELD_DESC = new org.apache.thrift.protocol.TField("awardDesc", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField ITEM_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("itemList", org.apache.thrift.protocol.TType.LIST, (short)3);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new RevenueNewTrialRechargeCfgDTOStandardSchemeFactory());
    schemes.put(TupleScheme.class, new RevenueNewTrialRechargeCfgDTOTupleSchemeFactory());
  }

  public String desc; // optional
  public String awardDesc; // optional
  public List<RevenueNewTrialRechargeCfgItemDTO> itemList; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    DESC((short)1, "desc"),
    AWARD_DESC((short)2, "awardDesc"),
    ITEM_LIST((short)3, "itemList");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // DESC
          return DESC;
        case 2: // AWARD_DESC
          return AWARD_DESC;
        case 3: // ITEM_LIST
          return ITEM_LIST;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final _Fields optionals[] = {_Fields.DESC,_Fields.AWARD_DESC,_Fields.ITEM_LIST};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.DESC, new org.apache.thrift.meta_data.FieldMetaData("desc", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.AWARD_DESC, new org.apache.thrift.meta_data.FieldMetaData("awardDesc", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ITEM_LIST, new org.apache.thrift.meta_data.FieldMetaData("itemList", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, RevenueNewTrialRechargeCfgItemDTO.class))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(RevenueNewTrialRechargeCfgDTO.class, metaDataMap);
  }

  public RevenueNewTrialRechargeCfgDTO() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public RevenueNewTrialRechargeCfgDTO(RevenueNewTrialRechargeCfgDTO other) {
    if (other.isSetDesc()) {
      this.desc = other.desc;
    }
    if (other.isSetAwardDesc()) {
      this.awardDesc = other.awardDesc;
    }
    if (other.isSetItemList()) {
      List<RevenueNewTrialRechargeCfgItemDTO> __this__itemList = new ArrayList<RevenueNewTrialRechargeCfgItemDTO>(other.itemList.size());
      for (RevenueNewTrialRechargeCfgItemDTO other_element : other.itemList) {
        __this__itemList.add(new RevenueNewTrialRechargeCfgItemDTO(other_element));
      }
      this.itemList = __this__itemList;
    }
  }

  public RevenueNewTrialRechargeCfgDTO deepCopy() {
    return new RevenueNewTrialRechargeCfgDTO(this);
  }

  @Override
  public void clear() {
    this.desc = null;
    this.awardDesc = null;
    this.itemList = null;
  }

  public String getDesc() {
    return this.desc;
  }

  public RevenueNewTrialRechargeCfgDTO setDesc(String desc) {
    this.desc = desc;
    return this;
  }

  public void unsetDesc() {
    this.desc = null;
  }

  /** Returns true if field desc is set (has been assigned a value) and false otherwise */
  public boolean isSetDesc() {
    return this.desc != null;
  }

  public void setDescIsSet(boolean value) {
    if (!value) {
      this.desc = null;
    }
  }

  public String getAwardDesc() {
    return this.awardDesc;
  }

  public RevenueNewTrialRechargeCfgDTO setAwardDesc(String awardDesc) {
    this.awardDesc = awardDesc;
    return this;
  }

  public void unsetAwardDesc() {
    this.awardDesc = null;
  }

  /** Returns true if field awardDesc is set (has been assigned a value) and false otherwise */
  public boolean isSetAwardDesc() {
    return this.awardDesc != null;
  }

  public void setAwardDescIsSet(boolean value) {
    if (!value) {
      this.awardDesc = null;
    }
  }

  public int getItemListSize() {
    return (this.itemList == null) ? 0 : this.itemList.size();
  }

  public java.util.Iterator<RevenueNewTrialRechargeCfgItemDTO> getItemListIterator() {
    return (this.itemList == null) ? null : this.itemList.iterator();
  }

  public void addToItemList(RevenueNewTrialRechargeCfgItemDTO elem) {
    if (this.itemList == null) {
      this.itemList = new ArrayList<RevenueNewTrialRechargeCfgItemDTO>();
    }
    this.itemList.add(elem);
  }

  public List<RevenueNewTrialRechargeCfgItemDTO> getItemList() {
    return this.itemList;
  }

  public RevenueNewTrialRechargeCfgDTO setItemList(List<RevenueNewTrialRechargeCfgItemDTO> itemList) {
    this.itemList = itemList;
    return this;
  }

  public void unsetItemList() {
    this.itemList = null;
  }

  /** Returns true if field itemList is set (has been assigned a value) and false otherwise */
  public boolean isSetItemList() {
    return this.itemList != null;
  }

  public void setItemListIsSet(boolean value) {
    if (!value) {
      this.itemList = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case DESC:
      if (value == null) {
        unsetDesc();
      } else {
        setDesc((String)value);
      }
      break;

    case AWARD_DESC:
      if (value == null) {
        unsetAwardDesc();
      } else {
        setAwardDesc((String)value);
      }
      break;

    case ITEM_LIST:
      if (value == null) {
        unsetItemList();
      } else {
        setItemList((List<RevenueNewTrialRechargeCfgItemDTO>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case DESC:
      return getDesc();

    case AWARD_DESC:
      return getAwardDesc();

    case ITEM_LIST:
      return getItemList();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case DESC:
      return isSetDesc();
    case AWARD_DESC:
      return isSetAwardDesc();
    case ITEM_LIST:
      return isSetItemList();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof RevenueNewTrialRechargeCfgDTO)
      return this.equals((RevenueNewTrialRechargeCfgDTO)that);
    return false;
  }

  public boolean equals(RevenueNewTrialRechargeCfgDTO that) {
    if (that == null)
      return false;

    boolean this_present_desc = true && this.isSetDesc();
    boolean that_present_desc = true && that.isSetDesc();
    if (this_present_desc || that_present_desc) {
      if (!(this_present_desc && that_present_desc))
        return false;
      if (!this.desc.equals(that.desc))
        return false;
    }

    boolean this_present_awardDesc = true && this.isSetAwardDesc();
    boolean that_present_awardDesc = true && that.isSetAwardDesc();
    if (this_present_awardDesc || that_present_awardDesc) {
      if (!(this_present_awardDesc && that_present_awardDesc))
        return false;
      if (!this.awardDesc.equals(that.awardDesc))
        return false;
    }

    boolean this_present_itemList = true && this.isSetItemList();
    boolean that_present_itemList = true && that.isSetItemList();
    if (this_present_itemList || that_present_itemList) {
      if (!(this_present_itemList && that_present_itemList))
        return false;
      if (!this.itemList.equals(that.itemList))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_desc = true && (isSetDesc());
    list.add(present_desc);
    if (present_desc)
      list.add(desc);

    boolean present_awardDesc = true && (isSetAwardDesc());
    list.add(present_awardDesc);
    if (present_awardDesc)
      list.add(awardDesc);

    boolean present_itemList = true && (isSetItemList());
    list.add(present_itemList);
    if (present_itemList)
      list.add(itemList);

    return list.hashCode();
  }

  @Override
  public int compareTo(RevenueNewTrialRechargeCfgDTO other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetDesc()).compareTo(other.isSetDesc());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDesc()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.desc, other.desc);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAwardDesc()).compareTo(other.isSetAwardDesc());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAwardDesc()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.awardDesc, other.awardDesc);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetItemList()).compareTo(other.isSetItemList());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItemList()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.itemList, other.itemList);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("RevenueNewTrialRechargeCfgDTO(");
    boolean first = true;

    if (isSetDesc()) {
      sb.append("desc:");
      if (this.desc == null) {
        sb.append("null");
      } else {
        sb.append(this.desc);
      }
      first = false;
    }
    if (isSetAwardDesc()) {
      if (!first) sb.append(", ");
      sb.append("awardDesc:");
      if (this.awardDesc == null) {
        sb.append("null");
      } else {
        sb.append(this.awardDesc);
      }
      first = false;
    }
    if (isSetItemList()) {
      if (!first) sb.append(", ");
      sb.append("itemList:");
      if (this.itemList == null) {
        sb.append("null");
      } else {
        sb.append(this.itemList);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class RevenueNewTrialRechargeCfgDTOStandardSchemeFactory implements SchemeFactory {
    public RevenueNewTrialRechargeCfgDTOStandardScheme getScheme() {
      return new RevenueNewTrialRechargeCfgDTOStandardScheme();
    }
  }

  private static class RevenueNewTrialRechargeCfgDTOStandardScheme extends StandardScheme<RevenueNewTrialRechargeCfgDTO> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, RevenueNewTrialRechargeCfgDTO struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // DESC
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.desc = iprot.readString();
              struct.setDescIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // AWARD_DESC
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.awardDesc = iprot.readString();
              struct.setAwardDescIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ITEM_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.itemList = new ArrayList<RevenueNewTrialRechargeCfgItemDTO>(_list0.size);
                RevenueNewTrialRechargeCfgItemDTO _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new RevenueNewTrialRechargeCfgItemDTO();
                  _elem1.read(iprot);
                  struct.itemList.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setItemListIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, RevenueNewTrialRechargeCfgDTO struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.desc != null) {
        if (struct.isSetDesc()) {
          oprot.writeFieldBegin(DESC_FIELD_DESC);
          oprot.writeString(struct.desc);
          oprot.writeFieldEnd();
        }
      }
      if (struct.awardDesc != null) {
        if (struct.isSetAwardDesc()) {
          oprot.writeFieldBegin(AWARD_DESC_FIELD_DESC);
          oprot.writeString(struct.awardDesc);
          oprot.writeFieldEnd();
        }
      }
      if (struct.itemList != null) {
        if (struct.isSetItemList()) {
          oprot.writeFieldBegin(ITEM_LIST_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.itemList.size()));
            for (RevenueNewTrialRechargeCfgItemDTO _iter3 : struct.itemList)
            {
              _iter3.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class RevenueNewTrialRechargeCfgDTOTupleSchemeFactory implements SchemeFactory {
    public RevenueNewTrialRechargeCfgDTOTupleScheme getScheme() {
      return new RevenueNewTrialRechargeCfgDTOTupleScheme();
    }
  }

  private static class RevenueNewTrialRechargeCfgDTOTupleScheme extends TupleScheme<RevenueNewTrialRechargeCfgDTO> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, RevenueNewTrialRechargeCfgDTO struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetDesc()) {
        optionals.set(0);
      }
      if (struct.isSetAwardDesc()) {
        optionals.set(1);
      }
      if (struct.isSetItemList()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetDesc()) {
        oprot.writeString(struct.desc);
      }
      if (struct.isSetAwardDesc()) {
        oprot.writeString(struct.awardDesc);
      }
      if (struct.isSetItemList()) {
        {
          oprot.writeI32(struct.itemList.size());
          for (RevenueNewTrialRechargeCfgItemDTO _iter4 : struct.itemList)
          {
            _iter4.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, RevenueNewTrialRechargeCfgDTO struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        struct.desc = iprot.readString();
        struct.setDescIsSet(true);
      }
      if (incoming.get(1)) {
        struct.awardDesc = iprot.readString();
        struct.setAwardDescIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TList _list5 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.itemList = new ArrayList<RevenueNewTrialRechargeCfgItemDTO>(_list5.size);
          RevenueNewTrialRechargeCfgItemDTO _elem6;
          for (int _i7 = 0; _i7 < _list5.size; ++_i7)
          {
            _elem6 = new RevenueNewTrialRechargeCfgItemDTO();
            _elem6.read(iprot);
            struct.itemList.add(_elem6);
          }
        }
        struct.setItemListIsSet(true);
      }
    }
  }

}

