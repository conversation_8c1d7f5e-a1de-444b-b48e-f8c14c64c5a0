/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.thrift.pay.v2;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-10-13")
public class PromotionDetailDto implements org.apache.thrift.TBase<PromotionDetailDto, PromotionDetailDto._Fields>, java.io.Serializable, Cloneable, Comparable<PromotionDetailDto> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PromotionDetailDto");

  private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField BUSINESS_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("businessId", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField BUSINESS_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("businessName", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField PROMO_PARAM_FIELD_DESC = new org.apache.thrift.protocol.TField("promoParam", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField ORDER_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("orderNum", org.apache.thrift.protocol.TType.I32, (short)5);
  private static final org.apache.thrift.protocol.TField PAYED_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("payedNum", org.apache.thrift.protocol.TType.I32, (short)6);
  private static final org.apache.thrift.protocol.TField TOTAL_USAGE_FIELD_DESC = new org.apache.thrift.protocol.TField("totalUsage", org.apache.thrift.protocol.TType.I32, (short)7);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new PromotionDetailDtoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new PromotionDetailDtoTupleSchemeFactory());
  }

  public long kugouId; // required
  public String businessId; // required
  public String businessName; // required
  public String promoParam; // required
  public int orderNum; // required
  public int payedNum; // required
  public int totalUsage; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    KUGOU_ID((short)1, "kugouId"),
    BUSINESS_ID((short)2, "businessId"),
    BUSINESS_NAME((short)3, "businessName"),
    PROMO_PARAM((short)4, "promoParam"),
    ORDER_NUM((short)5, "orderNum"),
    PAYED_NUM((short)6, "payedNum"),
    TOTAL_USAGE((short)7, "totalUsage");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // KUGOU_ID
          return KUGOU_ID;
        case 2: // BUSINESS_ID
          return BUSINESS_ID;
        case 3: // BUSINESS_NAME
          return BUSINESS_NAME;
        case 4: // PROMO_PARAM
          return PROMO_PARAM;
        case 5: // ORDER_NUM
          return ORDER_NUM;
        case 6: // PAYED_NUM
          return PAYED_NUM;
        case 7: // TOTAL_USAGE
          return TOTAL_USAGE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __KUGOUID_ISSET_ID = 0;
  private static final int __ORDERNUM_ISSET_ID = 1;
  private static final int __PAYEDNUM_ISSET_ID = 2;
  private static final int __TOTALUSAGE_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.BUSINESS_ID, new org.apache.thrift.meta_data.FieldMetaData("businessId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BUSINESS_NAME, new org.apache.thrift.meta_data.FieldMetaData("businessName", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PROMO_PARAM, new org.apache.thrift.meta_data.FieldMetaData("promoParam", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ORDER_NUM, new org.apache.thrift.meta_data.FieldMetaData("orderNum", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.PAYED_NUM, new org.apache.thrift.meta_data.FieldMetaData("payedNum", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TOTAL_USAGE, new org.apache.thrift.meta_data.FieldMetaData("totalUsage", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PromotionDetailDto.class, metaDataMap);
  }

  public PromotionDetailDto() {
  }

  public PromotionDetailDto(
    long kugouId,
    String businessId,
    String businessName,
    String promoParam,
    int orderNum,
    int payedNum,
    int totalUsage)
  {
    this();
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    this.businessId = businessId;
    this.businessName = businessName;
    this.promoParam = promoParam;
    this.orderNum = orderNum;
    setOrderNumIsSet(true);
    this.payedNum = payedNum;
    setPayedNumIsSet(true);
    this.totalUsage = totalUsage;
    setTotalUsageIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PromotionDetailDto(PromotionDetailDto other) {
    __isset_bitfield = other.__isset_bitfield;
    this.kugouId = other.kugouId;
    if (other.isSetBusinessId()) {
      this.businessId = other.businessId;
    }
    if (other.isSetBusinessName()) {
      this.businessName = other.businessName;
    }
    if (other.isSetPromoParam()) {
      this.promoParam = other.promoParam;
    }
    this.orderNum = other.orderNum;
    this.payedNum = other.payedNum;
    this.totalUsage = other.totalUsage;
  }

  public PromotionDetailDto deepCopy() {
    return new PromotionDetailDto(this);
  }

  @Override
  public void clear() {
    setKugouIdIsSet(false);
    this.kugouId = 0;
    this.businessId = null;
    this.businessName = null;
    this.promoParam = null;
    setOrderNumIsSet(false);
    this.orderNum = 0;
    setPayedNumIsSet(false);
    this.payedNum = 0;
    setTotalUsageIsSet(false);
    this.totalUsage = 0;
  }

  public long getKugouId() {
    return this.kugouId;
  }

  public PromotionDetailDto setKugouId(long kugouId) {
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    return this;
  }

  public void unsetKugouId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
  public boolean isSetKugouId() {
    return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  public void setKugouIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
  }

  public String getBusinessId() {
    return this.businessId;
  }

  public PromotionDetailDto setBusinessId(String businessId) {
    this.businessId = businessId;
    return this;
  }

  public void unsetBusinessId() {
    this.businessId = null;
  }

  /** Returns true if field businessId is set (has been assigned a value) and false otherwise */
  public boolean isSetBusinessId() {
    return this.businessId != null;
  }

  public void setBusinessIdIsSet(boolean value) {
    if (!value) {
      this.businessId = null;
    }
  }

  public String getBusinessName() {
    return this.businessName;
  }

  public PromotionDetailDto setBusinessName(String businessName) {
    this.businessName = businessName;
    return this;
  }

  public void unsetBusinessName() {
    this.businessName = null;
  }

  /** Returns true if field businessName is set (has been assigned a value) and false otherwise */
  public boolean isSetBusinessName() {
    return this.businessName != null;
  }

  public void setBusinessNameIsSet(boolean value) {
    if (!value) {
      this.businessName = null;
    }
  }

  public String getPromoParam() {
    return this.promoParam;
  }

  public PromotionDetailDto setPromoParam(String promoParam) {
    this.promoParam = promoParam;
    return this;
  }

  public void unsetPromoParam() {
    this.promoParam = null;
  }

  /** Returns true if field promoParam is set (has been assigned a value) and false otherwise */
  public boolean isSetPromoParam() {
    return this.promoParam != null;
  }

  public void setPromoParamIsSet(boolean value) {
    if (!value) {
      this.promoParam = null;
    }
  }

  public int getOrderNum() {
    return this.orderNum;
  }

  public PromotionDetailDto setOrderNum(int orderNum) {
    this.orderNum = orderNum;
    setOrderNumIsSet(true);
    return this;
  }

  public void unsetOrderNum() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ORDERNUM_ISSET_ID);
  }

  /** Returns true if field orderNum is set (has been assigned a value) and false otherwise */
  public boolean isSetOrderNum() {
    return EncodingUtils.testBit(__isset_bitfield, __ORDERNUM_ISSET_ID);
  }

  public void setOrderNumIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ORDERNUM_ISSET_ID, value);
  }

  public int getPayedNum() {
    return this.payedNum;
  }

  public PromotionDetailDto setPayedNum(int payedNum) {
    this.payedNum = payedNum;
    setPayedNumIsSet(true);
    return this;
  }

  public void unsetPayedNum() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAYEDNUM_ISSET_ID);
  }

  /** Returns true if field payedNum is set (has been assigned a value) and false otherwise */
  public boolean isSetPayedNum() {
    return EncodingUtils.testBit(__isset_bitfield, __PAYEDNUM_ISSET_ID);
  }

  public void setPayedNumIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAYEDNUM_ISSET_ID, value);
  }

  public int getTotalUsage() {
    return this.totalUsage;
  }

  public PromotionDetailDto setTotalUsage(int totalUsage) {
    this.totalUsage = totalUsage;
    setTotalUsageIsSet(true);
    return this;
  }

  public void unsetTotalUsage() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TOTALUSAGE_ISSET_ID);
  }

  /** Returns true if field totalUsage is set (has been assigned a value) and false otherwise */
  public boolean isSetTotalUsage() {
    return EncodingUtils.testBit(__isset_bitfield, __TOTALUSAGE_ISSET_ID);
  }

  public void setTotalUsageIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TOTALUSAGE_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case KUGOU_ID:
      if (value == null) {
        unsetKugouId();
      } else {
        setKugouId((Long)value);
      }
      break;

    case BUSINESS_ID:
      if (value == null) {
        unsetBusinessId();
      } else {
        setBusinessId((String)value);
      }
      break;

    case BUSINESS_NAME:
      if (value == null) {
        unsetBusinessName();
      } else {
        setBusinessName((String)value);
      }
      break;

    case PROMO_PARAM:
      if (value == null) {
        unsetPromoParam();
      } else {
        setPromoParam((String)value);
      }
      break;

    case ORDER_NUM:
      if (value == null) {
        unsetOrderNum();
      } else {
        setOrderNum((Integer)value);
      }
      break;

    case PAYED_NUM:
      if (value == null) {
        unsetPayedNum();
      } else {
        setPayedNum((Integer)value);
      }
      break;

    case TOTAL_USAGE:
      if (value == null) {
        unsetTotalUsage();
      } else {
        setTotalUsage((Integer)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case KUGOU_ID:
      return getKugouId();

    case BUSINESS_ID:
      return getBusinessId();

    case BUSINESS_NAME:
      return getBusinessName();

    case PROMO_PARAM:
      return getPromoParam();

    case ORDER_NUM:
      return getOrderNum();

    case PAYED_NUM:
      return getPayedNum();

    case TOTAL_USAGE:
      return getTotalUsage();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case KUGOU_ID:
      return isSetKugouId();
    case BUSINESS_ID:
      return isSetBusinessId();
    case BUSINESS_NAME:
      return isSetBusinessName();
    case PROMO_PARAM:
      return isSetPromoParam();
    case ORDER_NUM:
      return isSetOrderNum();
    case PAYED_NUM:
      return isSetPayedNum();
    case TOTAL_USAGE:
      return isSetTotalUsage();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof PromotionDetailDto)
      return this.equals((PromotionDetailDto)that);
    return false;
  }

  public boolean equals(PromotionDetailDto that) {
    if (that == null)
      return false;

    boolean this_present_kugouId = true;
    boolean that_present_kugouId = true;
    if (this_present_kugouId || that_present_kugouId) {
      if (!(this_present_kugouId && that_present_kugouId))
        return false;
      if (this.kugouId != that.kugouId)
        return false;
    }

    boolean this_present_businessId = true && this.isSetBusinessId();
    boolean that_present_businessId = true && that.isSetBusinessId();
    if (this_present_businessId || that_present_businessId) {
      if (!(this_present_businessId && that_present_businessId))
        return false;
      if (!this.businessId.equals(that.businessId))
        return false;
    }

    boolean this_present_businessName = true && this.isSetBusinessName();
    boolean that_present_businessName = true && that.isSetBusinessName();
    if (this_present_businessName || that_present_businessName) {
      if (!(this_present_businessName && that_present_businessName))
        return false;
      if (!this.businessName.equals(that.businessName))
        return false;
    }

    boolean this_present_promoParam = true && this.isSetPromoParam();
    boolean that_present_promoParam = true && that.isSetPromoParam();
    if (this_present_promoParam || that_present_promoParam) {
      if (!(this_present_promoParam && that_present_promoParam))
        return false;
      if (!this.promoParam.equals(that.promoParam))
        return false;
    }

    boolean this_present_orderNum = true;
    boolean that_present_orderNum = true;
    if (this_present_orderNum || that_present_orderNum) {
      if (!(this_present_orderNum && that_present_orderNum))
        return false;
      if (this.orderNum != that.orderNum)
        return false;
    }

    boolean this_present_payedNum = true;
    boolean that_present_payedNum = true;
    if (this_present_payedNum || that_present_payedNum) {
      if (!(this_present_payedNum && that_present_payedNum))
        return false;
      if (this.payedNum != that.payedNum)
        return false;
    }

    boolean this_present_totalUsage = true;
    boolean that_present_totalUsage = true;
    if (this_present_totalUsage || that_present_totalUsage) {
      if (!(this_present_totalUsage && that_present_totalUsage))
        return false;
      if (this.totalUsage != that.totalUsage)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_kugouId = true;
    list.add(present_kugouId);
    if (present_kugouId)
      list.add(kugouId);

    boolean present_businessId = true && (isSetBusinessId());
    list.add(present_businessId);
    if (present_businessId)
      list.add(businessId);

    boolean present_businessName = true && (isSetBusinessName());
    list.add(present_businessName);
    if (present_businessName)
      list.add(businessName);

    boolean present_promoParam = true && (isSetPromoParam());
    list.add(present_promoParam);
    if (present_promoParam)
      list.add(promoParam);

    boolean present_orderNum = true;
    list.add(present_orderNum);
    if (present_orderNum)
      list.add(orderNum);

    boolean present_payedNum = true;
    list.add(present_payedNum);
    if (present_payedNum)
      list.add(payedNum);

    boolean present_totalUsage = true;
    list.add(present_totalUsage);
    if (present_totalUsage)
      list.add(totalUsage);

    return list.hashCode();
  }

  @Override
  public int compareTo(PromotionDetailDto other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKugouId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBusinessId()).compareTo(other.isSetBusinessId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBusinessId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.businessId, other.businessId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBusinessName()).compareTo(other.isSetBusinessName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBusinessName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.businessName, other.businessName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPromoParam()).compareTo(other.isSetPromoParam());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPromoParam()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.promoParam, other.promoParam);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetOrderNum()).compareTo(other.isSetOrderNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOrderNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderNum, other.orderNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPayedNum()).compareTo(other.isSetPayedNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPayedNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.payedNum, other.payedNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTotalUsage()).compareTo(other.isSetTotalUsage());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTotalUsage()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.totalUsage, other.totalUsage);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("PromotionDetailDto(");
    boolean first = true;

    sb.append("kugouId:");
    sb.append(this.kugouId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("businessId:");
    if (this.businessId == null) {
      sb.append("null");
    } else {
      sb.append(this.businessId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("businessName:");
    if (this.businessName == null) {
      sb.append("null");
    } else {
      sb.append(this.businessName);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("promoParam:");
    if (this.promoParam == null) {
      sb.append("null");
    } else {
      sb.append(this.promoParam);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("orderNum:");
    sb.append(this.orderNum);
    first = false;
    if (!first) sb.append(", ");
    sb.append("payedNum:");
    sb.append(this.payedNum);
    first = false;
    if (!first) sb.append(", ");
    sb.append("totalUsage:");
    sb.append(this.totalUsage);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
    if (businessId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'businessId' was not present! Struct: " + toString());
    }
    if (businessName == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'businessName' was not present! Struct: " + toString());
    }
    if (promoParam == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'promoParam' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'orderNum' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'payedNum' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'totalUsage' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PromotionDetailDtoStandardSchemeFactory implements SchemeFactory {
    public PromotionDetailDtoStandardScheme getScheme() {
      return new PromotionDetailDtoStandardScheme();
    }
  }

  private static class PromotionDetailDtoStandardScheme extends StandardScheme<PromotionDetailDto> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PromotionDetailDto struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // KUGOU_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.kugouId = iprot.readI64();
              struct.setKugouIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // BUSINESS_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.businessId = iprot.readString();
              struct.setBusinessIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // BUSINESS_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.businessName = iprot.readString();
              struct.setBusinessNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // PROMO_PARAM
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.promoParam = iprot.readString();
              struct.setPromoParamIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // ORDER_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.orderNum = iprot.readI32();
              struct.setOrderNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // PAYED_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.payedNum = iprot.readI32();
              struct.setPayedNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // TOTAL_USAGE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.totalUsage = iprot.readI32();
              struct.setTotalUsageIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetKugouId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetOrderNum()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderNum' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetPayedNum()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'payedNum' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetTotalUsage()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'totalUsage' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PromotionDetailDto struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
      oprot.writeI64(struct.kugouId);
      oprot.writeFieldEnd();
      if (struct.businessId != null) {
        oprot.writeFieldBegin(BUSINESS_ID_FIELD_DESC);
        oprot.writeString(struct.businessId);
        oprot.writeFieldEnd();
      }
      if (struct.businessName != null) {
        oprot.writeFieldBegin(BUSINESS_NAME_FIELD_DESC);
        oprot.writeString(struct.businessName);
        oprot.writeFieldEnd();
      }
      if (struct.promoParam != null) {
        oprot.writeFieldBegin(PROMO_PARAM_FIELD_DESC);
        oprot.writeString(struct.promoParam);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(ORDER_NUM_FIELD_DESC);
      oprot.writeI32(struct.orderNum);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PAYED_NUM_FIELD_DESC);
      oprot.writeI32(struct.payedNum);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TOTAL_USAGE_FIELD_DESC);
      oprot.writeI32(struct.totalUsage);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PromotionDetailDtoTupleSchemeFactory implements SchemeFactory {
    public PromotionDetailDtoTupleScheme getScheme() {
      return new PromotionDetailDtoTupleScheme();
    }
  }

  private static class PromotionDetailDtoTupleScheme extends TupleScheme<PromotionDetailDto> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PromotionDetailDto struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeI64(struct.kugouId);
      oprot.writeString(struct.businessId);
      oprot.writeString(struct.businessName);
      oprot.writeString(struct.promoParam);
      oprot.writeI32(struct.orderNum);
      oprot.writeI32(struct.payedNum);
      oprot.writeI32(struct.totalUsage);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PromotionDetailDto struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.kugouId = iprot.readI64();
      struct.setKugouIdIsSet(true);
      struct.businessId = iprot.readString();
      struct.setBusinessIdIsSet(true);
      struct.businessName = iprot.readString();
      struct.setBusinessNameIsSet(true);
      struct.promoParam = iprot.readString();
      struct.setPromoParamIsSet(true);
      struct.orderNum = iprot.readI32();
      struct.setOrderNumIsSet(true);
      struct.payedNum = iprot.readI32();
      struct.setPayedNumIsSet(true);
      struct.totalUsage = iprot.readI32();
      struct.setTotalUsageIsSet(true);
    }
  }

}

