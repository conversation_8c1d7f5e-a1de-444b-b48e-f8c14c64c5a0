/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.thrift.acksocket.gather.types;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
/**
 * 批量根据kugouId发送消息入参
 * 
 */
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-10-26")
public class BatchMessage implements org.apache.thrift.TBase<BatchMessage, BatchMessage._Fields>, java.io.Serializable, Cloneable, Comparable<BatchMessage> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("BatchMessage");

  private static final org.apache.thrift.protocol.TField TO_KID_FIELD_DESC = new org.apache.thrift.protocol.TField("toKid", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField TO_UID_FIELD_DESC = new org.apache.thrift.protocol.TField("toUid", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField CONTENT_FIELD_DESC = new org.apache.thrift.protocol.TField("content", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField CMD_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("cmdId", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField MSG_OPTION_FIELD_DESC = new org.apache.thrift.protocol.TField("msgOption", org.apache.thrift.protocol.TType.STRUCT, (short)5);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new BatchMessageStandardSchemeFactory());
    schemes.put(TupleScheme.class, new BatchMessageTupleSchemeFactory());
  }

  public long toKid; // required
  public long toUid; // required
  public ByteBuffer content; // required
  public int cmdId; // required
  public MsgOption msgOption; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    TO_KID((short)1, "toKid"),
    TO_UID((short)2, "toUid"),
    CONTENT((short)3, "content"),
    CMD_ID((short)4, "cmdId"),
    MSG_OPTION((short)5, "msgOption");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // TO_KID
          return TO_KID;
        case 2: // TO_UID
          return TO_UID;
        case 3: // CONTENT
          return CONTENT;
        case 4: // CMD_ID
          return CMD_ID;
        case 5: // MSG_OPTION
          return MSG_OPTION;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __TOKID_ISSET_ID = 0;
  private static final int __TOUID_ISSET_ID = 1;
  private static final int __CMDID_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.TO_KID, new org.apache.thrift.meta_data.FieldMetaData("toKid", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TO_UID, new org.apache.thrift.meta_data.FieldMetaData("toUid", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CONTENT, new org.apache.thrift.meta_data.FieldMetaData("content", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING        , true)));
    tmpMap.put(_Fields.CMD_ID, new org.apache.thrift.meta_data.FieldMetaData("cmdId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.MSG_OPTION, new org.apache.thrift.meta_data.FieldMetaData("msgOption", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, MsgOption.class)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(BatchMessage.class, metaDataMap);
  }

  public BatchMessage() {
  }

  public BatchMessage(
    long toKid,
    long toUid,
    ByteBuffer content,
    int cmdId,
    MsgOption msgOption)
  {
    this();
    this.toKid = toKid;
    setToKidIsSet(true);
    this.toUid = toUid;
    setToUidIsSet(true);
    this.content = org.apache.thrift.TBaseHelper.copyBinary(content);
    this.cmdId = cmdId;
    setCmdIdIsSet(true);
    this.msgOption = msgOption;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public BatchMessage(BatchMessage other) {
    __isset_bitfield = other.__isset_bitfield;
    this.toKid = other.toKid;
    this.toUid = other.toUid;
    if (other.isSetContent()) {
      this.content = org.apache.thrift.TBaseHelper.copyBinary(other.content);
    }
    this.cmdId = other.cmdId;
    if (other.isSetMsgOption()) {
      this.msgOption = new MsgOption(other.msgOption);
    }
  }

  public BatchMessage deepCopy() {
    return new BatchMessage(this);
  }

  @Override
  public void clear() {
    setToKidIsSet(false);
    this.toKid = 0;
    setToUidIsSet(false);
    this.toUid = 0;
    this.content = null;
    setCmdIdIsSet(false);
    this.cmdId = 0;
    this.msgOption = null;
  }

  public long getToKid() {
    return this.toKid;
  }

  public BatchMessage setToKid(long toKid) {
    this.toKid = toKid;
    setToKidIsSet(true);
    return this;
  }

  public void unsetToKid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TOKID_ISSET_ID);
  }

  /** Returns true if field toKid is set (has been assigned a value) and false otherwise */
  public boolean isSetToKid() {
    return EncodingUtils.testBit(__isset_bitfield, __TOKID_ISSET_ID);
  }

  public void setToKidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TOKID_ISSET_ID, value);
  }

  public long getToUid() {
    return this.toUid;
  }

  public BatchMessage setToUid(long toUid) {
    this.toUid = toUid;
    setToUidIsSet(true);
    return this;
  }

  public void unsetToUid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TOUID_ISSET_ID);
  }

  /** Returns true if field toUid is set (has been assigned a value) and false otherwise */
  public boolean isSetToUid() {
    return EncodingUtils.testBit(__isset_bitfield, __TOUID_ISSET_ID);
  }

  public void setToUidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TOUID_ISSET_ID, value);
  }

  public byte[] getContent() {
    setContent(org.apache.thrift.TBaseHelper.rightSize(content));
    return content == null ? null : content.array();
  }

  public ByteBuffer bufferForContent() {
    return org.apache.thrift.TBaseHelper.copyBinary(content);
  }

  public BatchMessage setContent(byte[] content) {
    this.content = content == null ? (ByteBuffer)null : ByteBuffer.wrap(Arrays.copyOf(content, content.length));
    return this;
  }

  public BatchMessage setContent(ByteBuffer content) {
    this.content = org.apache.thrift.TBaseHelper.copyBinary(content);
    return this;
  }

  public void unsetContent() {
    this.content = null;
  }

  /** Returns true if field content is set (has been assigned a value) and false otherwise */
  public boolean isSetContent() {
    return this.content != null;
  }

  public void setContentIsSet(boolean value) {
    if (!value) {
      this.content = null;
    }
  }

  public int getCmdId() {
    return this.cmdId;
  }

  public BatchMessage setCmdId(int cmdId) {
    this.cmdId = cmdId;
    setCmdIdIsSet(true);
    return this;
  }

  public void unsetCmdId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CMDID_ISSET_ID);
  }

  /** Returns true if field cmdId is set (has been assigned a value) and false otherwise */
  public boolean isSetCmdId() {
    return EncodingUtils.testBit(__isset_bitfield, __CMDID_ISSET_ID);
  }

  public void setCmdIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CMDID_ISSET_ID, value);
  }

  public MsgOption getMsgOption() {
    return this.msgOption;
  }

  public BatchMessage setMsgOption(MsgOption msgOption) {
    this.msgOption = msgOption;
    return this;
  }

  public void unsetMsgOption() {
    this.msgOption = null;
  }

  /** Returns true if field msgOption is set (has been assigned a value) and false otherwise */
  public boolean isSetMsgOption() {
    return this.msgOption != null;
  }

  public void setMsgOptionIsSet(boolean value) {
    if (!value) {
      this.msgOption = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case TO_KID:
      if (value == null) {
        unsetToKid();
      } else {
        setToKid((Long)value);
      }
      break;

    case TO_UID:
      if (value == null) {
        unsetToUid();
      } else {
        setToUid((Long)value);
      }
      break;

    case CONTENT:
      if (value == null) {
        unsetContent();
      } else {
        setContent((ByteBuffer)value);
      }
      break;

    case CMD_ID:
      if (value == null) {
        unsetCmdId();
      } else {
        setCmdId((Integer)value);
      }
      break;

    case MSG_OPTION:
      if (value == null) {
        unsetMsgOption();
      } else {
        setMsgOption((MsgOption)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case TO_KID:
      return getToKid();

    case TO_UID:
      return getToUid();

    case CONTENT:
      return getContent();

    case CMD_ID:
      return getCmdId();

    case MSG_OPTION:
      return getMsgOption();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case TO_KID:
      return isSetToKid();
    case TO_UID:
      return isSetToUid();
    case CONTENT:
      return isSetContent();
    case CMD_ID:
      return isSetCmdId();
    case MSG_OPTION:
      return isSetMsgOption();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof BatchMessage)
      return this.equals((BatchMessage)that);
    return false;
  }

  public boolean equals(BatchMessage that) {
    if (that == null)
      return false;

    boolean this_present_toKid = true;
    boolean that_present_toKid = true;
    if (this_present_toKid || that_present_toKid) {
      if (!(this_present_toKid && that_present_toKid))
        return false;
      if (this.toKid != that.toKid)
        return false;
    }

    boolean this_present_toUid = true;
    boolean that_present_toUid = true;
    if (this_present_toUid || that_present_toUid) {
      if (!(this_present_toUid && that_present_toUid))
        return false;
      if (this.toUid != that.toUid)
        return false;
    }

    boolean this_present_content = true && this.isSetContent();
    boolean that_present_content = true && that.isSetContent();
    if (this_present_content || that_present_content) {
      if (!(this_present_content && that_present_content))
        return false;
      if (!this.content.equals(that.content))
        return false;
    }

    boolean this_present_cmdId = true;
    boolean that_present_cmdId = true;
    if (this_present_cmdId || that_present_cmdId) {
      if (!(this_present_cmdId && that_present_cmdId))
        return false;
      if (this.cmdId != that.cmdId)
        return false;
    }

    boolean this_present_msgOption = true && this.isSetMsgOption();
    boolean that_present_msgOption = true && that.isSetMsgOption();
    if (this_present_msgOption || that_present_msgOption) {
      if (!(this_present_msgOption && that_present_msgOption))
        return false;
      if (!this.msgOption.equals(that.msgOption))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_toKid = true;
    list.add(present_toKid);
    if (present_toKid)
      list.add(toKid);

    boolean present_toUid = true;
    list.add(present_toUid);
    if (present_toUid)
      list.add(toUid);

    boolean present_content = true && (isSetContent());
    list.add(present_content);
    if (present_content)
      list.add(content);

    boolean present_cmdId = true;
    list.add(present_cmdId);
    if (present_cmdId)
      list.add(cmdId);

    boolean present_msgOption = true && (isSetMsgOption());
    list.add(present_msgOption);
    if (present_msgOption)
      list.add(msgOption);

    return list.hashCode();
  }

  @Override
  public int compareTo(BatchMessage other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetToKid()).compareTo(other.isSetToKid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetToKid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.toKid, other.toKid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetToUid()).compareTo(other.isSetToUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetToUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.toUid, other.toUid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetContent()).compareTo(other.isSetContent());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetContent()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.content, other.content);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCmdId()).compareTo(other.isSetCmdId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCmdId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.cmdId, other.cmdId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMsgOption()).compareTo(other.isSetMsgOption());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMsgOption()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.msgOption, other.msgOption);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("BatchMessage(");
    boolean first = true;

    sb.append("toKid:");
    sb.append(this.toKid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("toUid:");
    sb.append(this.toUid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("content:");
    if (this.content == null) {
      sb.append("null");
    } else {
      org.apache.thrift.TBaseHelper.toString(this.content, sb);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("cmdId:");
    sb.append(this.cmdId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("msgOption:");
    if (this.msgOption == null) {
      sb.append("null");
    } else {
      sb.append(this.msgOption);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'toKid' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'toUid' because it's a primitive and you chose the non-beans generator.
    if (content == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'content' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'cmdId' because it's a primitive and you chose the non-beans generator.
    if (msgOption == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'msgOption' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
    if (msgOption != null) {
      msgOption.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class BatchMessageStandardSchemeFactory implements SchemeFactory {
    public BatchMessageStandardScheme getScheme() {
      return new BatchMessageStandardScheme();
    }
  }

  private static class BatchMessageStandardScheme extends StandardScheme<BatchMessage> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, BatchMessage struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // TO_KID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.toKid = iprot.readI64();
              struct.setToKidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // TO_UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.toUid = iprot.readI64();
              struct.setToUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // CONTENT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.content = iprot.readBinary();
              struct.setContentIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // CMD_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.cmdId = iprot.readI32();
              struct.setCmdIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // MSG_OPTION
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.msgOption = new MsgOption();
              struct.msgOption.read(iprot);
              struct.setMsgOptionIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetToKid()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'toKid' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetToUid()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'toUid' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetCmdId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'cmdId' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, BatchMessage struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(TO_KID_FIELD_DESC);
      oprot.writeI64(struct.toKid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TO_UID_FIELD_DESC);
      oprot.writeI64(struct.toUid);
      oprot.writeFieldEnd();
      if (struct.content != null) {
        oprot.writeFieldBegin(CONTENT_FIELD_DESC);
        oprot.writeBinary(struct.content);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(CMD_ID_FIELD_DESC);
      oprot.writeI32(struct.cmdId);
      oprot.writeFieldEnd();
      if (struct.msgOption != null) {
        oprot.writeFieldBegin(MSG_OPTION_FIELD_DESC);
        struct.msgOption.write(oprot);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class BatchMessageTupleSchemeFactory implements SchemeFactory {
    public BatchMessageTupleScheme getScheme() {
      return new BatchMessageTupleScheme();
    }
  }

  private static class BatchMessageTupleScheme extends TupleScheme<BatchMessage> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, BatchMessage struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeI64(struct.toKid);
      oprot.writeI64(struct.toUid);
      oprot.writeBinary(struct.content);
      oprot.writeI32(struct.cmdId);
      struct.msgOption.write(oprot);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, BatchMessage struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.toKid = iprot.readI64();
      struct.setToKidIsSet(true);
      struct.toUid = iprot.readI64();
      struct.setToUidIsSet(true);
      struct.content = iprot.readBinary();
      struct.setContentIsSet(true);
      struct.cmdId = iprot.readI32();
      struct.setCmdIdIsSet(true);
      struct.msgOption = new MsgOption();
      struct.msgOption.read(iprot);
      struct.setMsgOptionIsSet(true);
    }
  }

}

