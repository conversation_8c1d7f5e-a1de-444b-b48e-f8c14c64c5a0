/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.thrift.consume.service;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-01-12")
public class AlbumConsume implements org.apache.thrift.TBase<AlbumConsume, AlbumConsume._Fields>, java.io.Serializable, Cloneable, Comparable<AlbumConsume> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("AlbumConsume");

  private static final org.apache.thrift.protocol.TField PID_FIELD_DESC = new org.apache.thrift.protocol.TField("pid", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField SENDER_DEPARTMENT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("senderDepartmentId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField SENDER_PRODUCT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("senderProductId", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField SENDER_MINOR_PRODUCT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("senderMinorProductId", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField SENDER_HARDWARE_PLATFORM_FIELD_DESC = new org.apache.thrift.protocol.TField("senderHardwarePlatform", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField SENDER_CHANNEL_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("senderChannelId", org.apache.thrift.protocol.TType.I64, (short)6);
  private static final org.apache.thrift.protocol.TField SENDER_SUB_CHANNEL_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("senderSubChannelId", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField RECEIVER_DEPARTMENT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("receiverDepartmentId", org.apache.thrift.protocol.TType.I64, (short)8);
  private static final org.apache.thrift.protocol.TField ACCOUNT_CHANGE_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("accountChangeType", org.apache.thrift.protocol.TType.I64, (short)9);
  private static final org.apache.thrift.protocol.TField FXC_CHANGE_DESC_FIELD_DESC = new org.apache.thrift.protocol.TField("fxcChangeDesc", org.apache.thrift.protocol.TType.STRING, (short)10);
  private static final org.apache.thrift.protocol.TField COIN_FIELD_DESC = new org.apache.thrift.protocol.TField("coin", org.apache.thrift.protocol.TType.DOUBLE, (short)11);
  private static final org.apache.thrift.protocol.TField GLOBAL_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("globalId", org.apache.thrift.protocol.TType.I64, (short)12);
  private static final org.apache.thrift.protocol.TField FROM_KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("fromKugouId", org.apache.thrift.protocol.TType.I64, (short)13);
  private static final org.apache.thrift.protocol.TField TO_KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("toKugouId", org.apache.thrift.protocol.TType.I64, (short)14);
  private static final org.apache.thrift.protocol.TField TOKEN_FIELD_DESC = new org.apache.thrift.protocol.TField("token", org.apache.thrift.protocol.TType.STRING, (short)15);
  private static final org.apache.thrift.protocol.TField APPID_FIELD_DESC = new org.apache.thrift.protocol.TField("appid", org.apache.thrift.protocol.TType.I32, (short)16);
  private static final org.apache.thrift.protocol.TField ROOM_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("roomId", org.apache.thrift.protocol.TType.I32, (short)17);
  private static final org.apache.thrift.protocol.TField IP_FIELD_DESC = new org.apache.thrift.protocol.TField("ip", org.apache.thrift.protocol.TType.STRING, (short)18);
  private static final org.apache.thrift.protocol.TField USER_AGENT_FIELD_DESC = new org.apache.thrift.protocol.TField("userAgent", org.apache.thrift.protocol.TType.STRING, (short)19);
  private static final org.apache.thrift.protocol.TField CATEGORY_FIELD_DESC = new org.apache.thrift.protocol.TField("category", org.apache.thrift.protocol.TType.I32, (short)20);
  private static final org.apache.thrift.protocol.TField ACTION_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actionId", org.apache.thrift.protocol.TType.I32, (short)21);
  private static final org.apache.thrift.protocol.TField GIFT_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("giftNum", org.apache.thrift.protocol.TType.I32, (short)22);
  private static final org.apache.thrift.protocol.TField PERSONAL_DIVIDE_RATE_FIELD_DESC = new org.apache.thrift.protocol.TField("personalDivideRate", org.apache.thrift.protocol.TType.DOUBLE, (short)23);
  private static final org.apache.thrift.protocol.TField TIMESTAMP_FIELD_DESC = new org.apache.thrift.protocol.TField("timestamp", org.apache.thrift.protocol.TType.I64, (short)24);
  private static final org.apache.thrift.protocol.TField EXT_FIELD_DESC = new org.apache.thrift.protocol.TField("ext", org.apache.thrift.protocol.TType.STRING, (short)25);
  private static final org.apache.thrift.protocol.TField SIGN_FIELD_DESC = new org.apache.thrift.protocol.TField("sign", org.apache.thrift.protocol.TType.STRING, (short)26);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new AlbumConsumeStandardSchemeFactory());
    schemes.put(TupleScheme.class, new AlbumConsumeTupleSchemeFactory());
  }

  /**
   * 旧数据，暂时复制过来保存 —— 平台ID（0:网页,1:android平台,2:ios平台,3、ipad平台,5、酷狗7.0android,6、酷狗7.0IOS） @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public int pid; // required
  /**
   * 与发送者相关的部门，取值范围是 —— 0:web, 1:移动端, 2:pc客户端, 3:酷狗Live, 4:盛典 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public long senderDepartmentId; // required
  /**
   * 与发送者相关的产品线，取值范围 —— 0:普通直播间(涵盖了pc普通房+pc家族房+pk房间), 1:移动直播间, 2:MV, 3:盛典直播间, 4:寻宝, 5:花落谁家,6:屠龙,7:我们约会吧 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public long senderProductId; // required
  /**
   * 与发送者相关的次产品线，取值范围 —— 0:pc普通房, 1:pc家族房, 2:pc_pk房, 3:手机酷狗app, 4:手机繁星app1.0, 5:手机繁星app2.0, 6:手机繁星2.0备用, 7:手机繁星2.0备用2 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public long senderMinorProductId; // required
  /**
   * 与发送者相关的硬件平台，取值范围 —— 0:网页(pc_web), 1:内嵌页(酷狗pc桌面应用), 2:真人电台（fm）(酷狗pc桌面应用), 3:安卓, 4:iOS, 5:iPad, 6:Html5 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public long senderHardwarePlatform; // required
  /**
   * 与发送者相关的渠道，按各自业务选择数据区间 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public long senderChannelId; // required
  /**
   * 与发送者相关的渠道的渠道，按各自业务选择数据区间 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public long senderSubChannelId; // required
  /**
   * 与接收者有关的部门 —— 与前边的 senderDepartment 字段的取值范围一致 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public long receiverDepartmentId; // required
  /**
   * 预定业务类型id(送礼服务、守护等 , 这个id跟sign参数需要的固定密钥[加盐]一一对应)
   */
  public long accountChangeType; // required
  /**
   * 变更描述
   */
  public String fxcChangeDesc; // required
  /**
   * 星币数
   */
  public double coin; // required
  /**
   * 事件ID 用来做幂等
   */
  public long globalId; // required
  /**
   * 送礼者KugouID
   */
  public long fromKugouId; // required
  /**
   * 收礼者KugouID
   */
  public long toKugouId; // required
  /**
   * 登陆token
   */
  public String token; // required
  /**
   * 酷狗appId
   */
  public int appid; // required
  /**
   * 在哪个房间,没有的时候传0
   */
  public int roomId; // required
  /**
   * 用户真实IP
   */
  public String ip; // required
  /**
   * 用户信息
   */
  public String userAgent; // required
  /**
   * 变更类型
   */
  public int category; // required
  /**
   * 消费行为类别(0:送礼物,1:飞屏,2:广播,3:贴条,4:点歌,5:抢座,...)
   */
  public int actionId; // required
  /**
   * 礼物数量,这里只用于记录消费表
   */
  public int giftNum; // required
  /**
   * 主播分成比例，eg.100星币的礼物，主播分成比例为0.3,工会分成比例为0.1，则主播能收到的星豆为100*0.3*(1-0.1)=27,工会收到的星豆为100*0.3*0.1=3
   */
  public double personalDivideRate; // required
  /**
   * 时间戳 (秒)
   */
  public long timestamp; // required
  /**
   * 拓展字段，可为空，Json字符串
   */
  public String ext; // optional
  /**
   * md5(所有参数名字升序排列对应的数值拼接 + 固定密钥[加盐][跟对外提供服务的accountChangeType对应])
   */
  public String sign; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 旧数据，暂时复制过来保存 —— 平台ID（0:网页,1:android平台,2:ios平台,3、ipad平台,5、酷狗7.0android,6、酷狗7.0IOS） @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
     */
    PID((short)1, "pid"),
    /**
     * 与发送者相关的部门，取值范围是 —— 0:web, 1:移动端, 2:pc客户端, 3:酷狗Live, 4:盛典 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
     */
    SENDER_DEPARTMENT_ID((short)2, "senderDepartmentId"),
    /**
     * 与发送者相关的产品线，取值范围 —— 0:普通直播间(涵盖了pc普通房+pc家族房+pk房间), 1:移动直播间, 2:MV, 3:盛典直播间, 4:寻宝, 5:花落谁家,6:屠龙,7:我们约会吧 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
     */
    SENDER_PRODUCT_ID((short)3, "senderProductId"),
    /**
     * 与发送者相关的次产品线，取值范围 —— 0:pc普通房, 1:pc家族房, 2:pc_pk房, 3:手机酷狗app, 4:手机繁星app1.0, 5:手机繁星app2.0, 6:手机繁星2.0备用, 7:手机繁星2.0备用2 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
     */
    SENDER_MINOR_PRODUCT_ID((short)4, "senderMinorProductId"),
    /**
     * 与发送者相关的硬件平台，取值范围 —— 0:网页(pc_web), 1:内嵌页(酷狗pc桌面应用), 2:真人电台（fm）(酷狗pc桌面应用), 3:安卓, 4:iOS, 5:iPad, 6:Html5 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
     */
    SENDER_HARDWARE_PLATFORM((short)5, "senderHardwarePlatform"),
    /**
     * 与发送者相关的渠道，按各自业务选择数据区间 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
     */
    SENDER_CHANNEL_ID((short)6, "senderChannelId"),
    /**
     * 与发送者相关的渠道的渠道，按各自业务选择数据区间 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
     */
    SENDER_SUB_CHANNEL_ID((short)7, "senderSubChannelId"),
    /**
     * 与接收者有关的部门 —— 与前边的 senderDepartment 字段的取值范围一致 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
     */
    RECEIVER_DEPARTMENT_ID((short)8, "receiverDepartmentId"),
    /**
     * 预定业务类型id(送礼服务、守护等 , 这个id跟sign参数需要的固定密钥[加盐]一一对应)
     */
    ACCOUNT_CHANGE_TYPE((short)9, "accountChangeType"),
    /**
     * 变更描述
     */
    FXC_CHANGE_DESC((short)10, "fxcChangeDesc"),
    /**
     * 星币数
     */
    COIN((short)11, "coin"),
    /**
     * 事件ID 用来做幂等
     */
    GLOBAL_ID((short)12, "globalId"),
    /**
     * 送礼者KugouID
     */
    FROM_KUGOU_ID((short)13, "fromKugouId"),
    /**
     * 收礼者KugouID
     */
    TO_KUGOU_ID((short)14, "toKugouId"),
    /**
     * 登陆token
     */
    TOKEN((short)15, "token"),
    /**
     * 酷狗appId
     */
    APPID((short)16, "appid"),
    /**
     * 在哪个房间,没有的时候传0
     */
    ROOM_ID((short)17, "roomId"),
    /**
     * 用户真实IP
     */
    IP((short)18, "ip"),
    /**
     * 用户信息
     */
    USER_AGENT((short)19, "userAgent"),
    /**
     * 变更类型
     */
    CATEGORY((short)20, "category"),
    /**
     * 消费行为类别(0:送礼物,1:飞屏,2:广播,3:贴条,4:点歌,5:抢座,...)
     */
    ACTION_ID((short)21, "actionId"),
    /**
     * 礼物数量,这里只用于记录消费表
     */
    GIFT_NUM((short)22, "giftNum"),
    /**
     * 主播分成比例，eg.100星币的礼物，主播分成比例为0.3,工会分成比例为0.1，则主播能收到的星豆为100*0.3*(1-0.1)=27,工会收到的星豆为100*0.3*0.1=3
     */
    PERSONAL_DIVIDE_RATE((short)23, "personalDivideRate"),
    /**
     * 时间戳 (秒)
     */
    TIMESTAMP((short)24, "timestamp"),
    /**
     * 拓展字段，可为空，Json字符串
     */
    EXT((short)25, "ext"),
    /**
     * md5(所有参数名字升序排列对应的数值拼接 + 固定密钥[加盐][跟对外提供服务的accountChangeType对应])
     */
    SIGN((short)26, "sign");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // PID
          return PID;
        case 2: // SENDER_DEPARTMENT_ID
          return SENDER_DEPARTMENT_ID;
        case 3: // SENDER_PRODUCT_ID
          return SENDER_PRODUCT_ID;
        case 4: // SENDER_MINOR_PRODUCT_ID
          return SENDER_MINOR_PRODUCT_ID;
        case 5: // SENDER_HARDWARE_PLATFORM
          return SENDER_HARDWARE_PLATFORM;
        case 6: // SENDER_CHANNEL_ID
          return SENDER_CHANNEL_ID;
        case 7: // SENDER_SUB_CHANNEL_ID
          return SENDER_SUB_CHANNEL_ID;
        case 8: // RECEIVER_DEPARTMENT_ID
          return RECEIVER_DEPARTMENT_ID;
        case 9: // ACCOUNT_CHANGE_TYPE
          return ACCOUNT_CHANGE_TYPE;
        case 10: // FXC_CHANGE_DESC
          return FXC_CHANGE_DESC;
        case 11: // COIN
          return COIN;
        case 12: // GLOBAL_ID
          return GLOBAL_ID;
        case 13: // FROM_KUGOU_ID
          return FROM_KUGOU_ID;
        case 14: // TO_KUGOU_ID
          return TO_KUGOU_ID;
        case 15: // TOKEN
          return TOKEN;
        case 16: // APPID
          return APPID;
        case 17: // ROOM_ID
          return ROOM_ID;
        case 18: // IP
          return IP;
        case 19: // USER_AGENT
          return USER_AGENT;
        case 20: // CATEGORY
          return CATEGORY;
        case 21: // ACTION_ID
          return ACTION_ID;
        case 22: // GIFT_NUM
          return GIFT_NUM;
        case 23: // PERSONAL_DIVIDE_RATE
          return PERSONAL_DIVIDE_RATE;
        case 24: // TIMESTAMP
          return TIMESTAMP;
        case 25: // EXT
          return EXT;
        case 26: // SIGN
          return SIGN;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __PID_ISSET_ID = 0;
  private static final int __SENDERDEPARTMENTID_ISSET_ID = 1;
  private static final int __SENDERPRODUCTID_ISSET_ID = 2;
  private static final int __SENDERMINORPRODUCTID_ISSET_ID = 3;
  private static final int __SENDERHARDWAREPLATFORM_ISSET_ID = 4;
  private static final int __SENDERCHANNELID_ISSET_ID = 5;
  private static final int __SENDERSUBCHANNELID_ISSET_ID = 6;
  private static final int __RECEIVERDEPARTMENTID_ISSET_ID = 7;
  private static final int __ACCOUNTCHANGETYPE_ISSET_ID = 8;
  private static final int __COIN_ISSET_ID = 9;
  private static final int __GLOBALID_ISSET_ID = 10;
  private static final int __FROMKUGOUID_ISSET_ID = 11;
  private static final int __TOKUGOUID_ISSET_ID = 12;
  private static final int __APPID_ISSET_ID = 13;
  private static final int __ROOMID_ISSET_ID = 14;
  private static final int __CATEGORY_ISSET_ID = 15;
  private static final int __ACTIONID_ISSET_ID = 16;
  private static final int __GIFTNUM_ISSET_ID = 17;
  private static final int __PERSONALDIVIDERATE_ISSET_ID = 18;
  private static final int __TIMESTAMP_ISSET_ID = 19;
  private int __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.EXT};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.PID, new org.apache.thrift.meta_data.FieldMetaData("pid", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.SENDER_DEPARTMENT_ID, new org.apache.thrift.meta_data.FieldMetaData("senderDepartmentId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SENDER_PRODUCT_ID, new org.apache.thrift.meta_data.FieldMetaData("senderProductId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SENDER_MINOR_PRODUCT_ID, new org.apache.thrift.meta_data.FieldMetaData("senderMinorProductId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SENDER_HARDWARE_PLATFORM, new org.apache.thrift.meta_data.FieldMetaData("senderHardwarePlatform", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SENDER_CHANNEL_ID, new org.apache.thrift.meta_data.FieldMetaData("senderChannelId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SENDER_SUB_CHANNEL_ID, new org.apache.thrift.meta_data.FieldMetaData("senderSubChannelId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RECEIVER_DEPARTMENT_ID, new org.apache.thrift.meta_data.FieldMetaData("receiverDepartmentId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ACCOUNT_CHANGE_TYPE, new org.apache.thrift.meta_data.FieldMetaData("accountChangeType", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.FXC_CHANGE_DESC, new org.apache.thrift.meta_data.FieldMetaData("fxcChangeDesc", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.COIN, new org.apache.thrift.meta_data.FieldMetaData("coin", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.DOUBLE)));
    tmpMap.put(_Fields.GLOBAL_ID, new org.apache.thrift.meta_data.FieldMetaData("globalId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.FROM_KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("fromKugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TO_KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("toKugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TOKEN, new org.apache.thrift.meta_data.FieldMetaData("token", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.APPID, new org.apache.thrift.meta_data.FieldMetaData("appid", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ROOM_ID, new org.apache.thrift.meta_data.FieldMetaData("roomId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.IP, new org.apache.thrift.meta_data.FieldMetaData("ip", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.USER_AGENT, new org.apache.thrift.meta_data.FieldMetaData("userAgent", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CATEGORY, new org.apache.thrift.meta_data.FieldMetaData("category", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ACTION_ID, new org.apache.thrift.meta_data.FieldMetaData("actionId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.GIFT_NUM, new org.apache.thrift.meta_data.FieldMetaData("giftNum", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.PERSONAL_DIVIDE_RATE, new org.apache.thrift.meta_data.FieldMetaData("personalDivideRate", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.DOUBLE)));
    tmpMap.put(_Fields.TIMESTAMP, new org.apache.thrift.meta_data.FieldMetaData("timestamp", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXT, new org.apache.thrift.meta_data.FieldMetaData("ext", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SIGN, new org.apache.thrift.meta_data.FieldMetaData("sign", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(AlbumConsume.class, metaDataMap);
  }

  public AlbumConsume() {
  }

  public AlbumConsume(
    int pid,
    long senderDepartmentId,
    long senderProductId,
    long senderMinorProductId,
    long senderHardwarePlatform,
    long senderChannelId,
    long senderSubChannelId,
    long receiverDepartmentId,
    long accountChangeType,
    String fxcChangeDesc,
    double coin,
    long globalId,
    long fromKugouId,
    long toKugouId,
    String token,
    int appid,
    int roomId,
    String ip,
    String userAgent,
    int category,
    int actionId,
    int giftNum,
    double personalDivideRate,
    long timestamp,
    String sign)
  {
    this();
    this.pid = pid;
    setPidIsSet(true);
    this.senderDepartmentId = senderDepartmentId;
    setSenderDepartmentIdIsSet(true);
    this.senderProductId = senderProductId;
    setSenderProductIdIsSet(true);
    this.senderMinorProductId = senderMinorProductId;
    setSenderMinorProductIdIsSet(true);
    this.senderHardwarePlatform = senderHardwarePlatform;
    setSenderHardwarePlatformIsSet(true);
    this.senderChannelId = senderChannelId;
    setSenderChannelIdIsSet(true);
    this.senderSubChannelId = senderSubChannelId;
    setSenderSubChannelIdIsSet(true);
    this.receiverDepartmentId = receiverDepartmentId;
    setReceiverDepartmentIdIsSet(true);
    this.accountChangeType = accountChangeType;
    setAccountChangeTypeIsSet(true);
    this.fxcChangeDesc = fxcChangeDesc;
    this.coin = coin;
    setCoinIsSet(true);
    this.globalId = globalId;
    setGlobalIdIsSet(true);
    this.fromKugouId = fromKugouId;
    setFromKugouIdIsSet(true);
    this.toKugouId = toKugouId;
    setToKugouIdIsSet(true);
    this.token = token;
    this.appid = appid;
    setAppidIsSet(true);
    this.roomId = roomId;
    setRoomIdIsSet(true);
    this.ip = ip;
    this.userAgent = userAgent;
    this.category = category;
    setCategoryIsSet(true);
    this.actionId = actionId;
    setActionIdIsSet(true);
    this.giftNum = giftNum;
    setGiftNumIsSet(true);
    this.personalDivideRate = personalDivideRate;
    setPersonalDivideRateIsSet(true);
    this.timestamp = timestamp;
    setTimestampIsSet(true);
    this.sign = sign;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public AlbumConsume(AlbumConsume other) {
    __isset_bitfield = other.__isset_bitfield;
    this.pid = other.pid;
    this.senderDepartmentId = other.senderDepartmentId;
    this.senderProductId = other.senderProductId;
    this.senderMinorProductId = other.senderMinorProductId;
    this.senderHardwarePlatform = other.senderHardwarePlatform;
    this.senderChannelId = other.senderChannelId;
    this.senderSubChannelId = other.senderSubChannelId;
    this.receiverDepartmentId = other.receiverDepartmentId;
    this.accountChangeType = other.accountChangeType;
    if (other.isSetFxcChangeDesc()) {
      this.fxcChangeDesc = other.fxcChangeDesc;
    }
    this.coin = other.coin;
    this.globalId = other.globalId;
    this.fromKugouId = other.fromKugouId;
    this.toKugouId = other.toKugouId;
    if (other.isSetToken()) {
      this.token = other.token;
    }
    this.appid = other.appid;
    this.roomId = other.roomId;
    if (other.isSetIp()) {
      this.ip = other.ip;
    }
    if (other.isSetUserAgent()) {
      this.userAgent = other.userAgent;
    }
    this.category = other.category;
    this.actionId = other.actionId;
    this.giftNum = other.giftNum;
    this.personalDivideRate = other.personalDivideRate;
    this.timestamp = other.timestamp;
    if (other.isSetExt()) {
      this.ext = other.ext;
    }
    if (other.isSetSign()) {
      this.sign = other.sign;
    }
  }

  public AlbumConsume deepCopy() {
    return new AlbumConsume(this);
  }

  @Override
  public void clear() {
    setPidIsSet(false);
    this.pid = 0;
    setSenderDepartmentIdIsSet(false);
    this.senderDepartmentId = 0;
    setSenderProductIdIsSet(false);
    this.senderProductId = 0;
    setSenderMinorProductIdIsSet(false);
    this.senderMinorProductId = 0;
    setSenderHardwarePlatformIsSet(false);
    this.senderHardwarePlatform = 0;
    setSenderChannelIdIsSet(false);
    this.senderChannelId = 0;
    setSenderSubChannelIdIsSet(false);
    this.senderSubChannelId = 0;
    setReceiverDepartmentIdIsSet(false);
    this.receiverDepartmentId = 0;
    setAccountChangeTypeIsSet(false);
    this.accountChangeType = 0;
    this.fxcChangeDesc = null;
    setCoinIsSet(false);
    this.coin = 0.0;
    setGlobalIdIsSet(false);
    this.globalId = 0;
    setFromKugouIdIsSet(false);
    this.fromKugouId = 0;
    setToKugouIdIsSet(false);
    this.toKugouId = 0;
    this.token = null;
    setAppidIsSet(false);
    this.appid = 0;
    setRoomIdIsSet(false);
    this.roomId = 0;
    this.ip = null;
    this.userAgent = null;
    setCategoryIsSet(false);
    this.category = 0;
    setActionIdIsSet(false);
    this.actionId = 0;
    setGiftNumIsSet(false);
    this.giftNum = 0;
    setPersonalDivideRateIsSet(false);
    this.personalDivideRate = 0.0;
    setTimestampIsSet(false);
    this.timestamp = 0;
    this.ext = null;
    this.sign = null;
  }

  /**
   * 旧数据，暂时复制过来保存 —— 平台ID（0:网页,1:android平台,2:ios平台,3、ipad平台,5、酷狗7.0android,6、酷狗7.0IOS） @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public int getPid() {
    return this.pid;
  }

  /**
   * 旧数据，暂时复制过来保存 —— 平台ID（0:网页,1:android平台,2:ios平台,3、ipad平台,5、酷狗7.0android,6、酷狗7.0IOS） @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public AlbumConsume setPid(int pid) {
    this.pid = pid;
    setPidIsSet(true);
    return this;
  }

  public void unsetPid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PID_ISSET_ID);
  }

  /** Returns true if field pid is set (has been assigned a value) and false otherwise */
  public boolean isSetPid() {
    return EncodingUtils.testBit(__isset_bitfield, __PID_ISSET_ID);
  }

  public void setPidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PID_ISSET_ID, value);
  }

  /**
   * 与发送者相关的部门，取值范围是 —— 0:web, 1:移动端, 2:pc客户端, 3:酷狗Live, 4:盛典 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public long getSenderDepartmentId() {
    return this.senderDepartmentId;
  }

  /**
   * 与发送者相关的部门，取值范围是 —— 0:web, 1:移动端, 2:pc客户端, 3:酷狗Live, 4:盛典 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public AlbumConsume setSenderDepartmentId(long senderDepartmentId) {
    this.senderDepartmentId = senderDepartmentId;
    setSenderDepartmentIdIsSet(true);
    return this;
  }

  public void unsetSenderDepartmentId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SENDERDEPARTMENTID_ISSET_ID);
  }

  /** Returns true if field senderDepartmentId is set (has been assigned a value) and false otherwise */
  public boolean isSetSenderDepartmentId() {
    return EncodingUtils.testBit(__isset_bitfield, __SENDERDEPARTMENTID_ISSET_ID);
  }

  public void setSenderDepartmentIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SENDERDEPARTMENTID_ISSET_ID, value);
  }

  /**
   * 与发送者相关的产品线，取值范围 —— 0:普通直播间(涵盖了pc普通房+pc家族房+pk房间), 1:移动直播间, 2:MV, 3:盛典直播间, 4:寻宝, 5:花落谁家,6:屠龙,7:我们约会吧 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public long getSenderProductId() {
    return this.senderProductId;
  }

  /**
   * 与发送者相关的产品线，取值范围 —— 0:普通直播间(涵盖了pc普通房+pc家族房+pk房间), 1:移动直播间, 2:MV, 3:盛典直播间, 4:寻宝, 5:花落谁家,6:屠龙,7:我们约会吧 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public AlbumConsume setSenderProductId(long senderProductId) {
    this.senderProductId = senderProductId;
    setSenderProductIdIsSet(true);
    return this;
  }

  public void unsetSenderProductId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SENDERPRODUCTID_ISSET_ID);
  }

  /** Returns true if field senderProductId is set (has been assigned a value) and false otherwise */
  public boolean isSetSenderProductId() {
    return EncodingUtils.testBit(__isset_bitfield, __SENDERPRODUCTID_ISSET_ID);
  }

  public void setSenderProductIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SENDERPRODUCTID_ISSET_ID, value);
  }

  /**
   * 与发送者相关的次产品线，取值范围 —— 0:pc普通房, 1:pc家族房, 2:pc_pk房, 3:手机酷狗app, 4:手机繁星app1.0, 5:手机繁星app2.0, 6:手机繁星2.0备用, 7:手机繁星2.0备用2 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public long getSenderMinorProductId() {
    return this.senderMinorProductId;
  }

  /**
   * 与发送者相关的次产品线，取值范围 —— 0:pc普通房, 1:pc家族房, 2:pc_pk房, 3:手机酷狗app, 4:手机繁星app1.0, 5:手机繁星app2.0, 6:手机繁星2.0备用, 7:手机繁星2.0备用2 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public AlbumConsume setSenderMinorProductId(long senderMinorProductId) {
    this.senderMinorProductId = senderMinorProductId;
    setSenderMinorProductIdIsSet(true);
    return this;
  }

  public void unsetSenderMinorProductId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SENDERMINORPRODUCTID_ISSET_ID);
  }

  /** Returns true if field senderMinorProductId is set (has been assigned a value) and false otherwise */
  public boolean isSetSenderMinorProductId() {
    return EncodingUtils.testBit(__isset_bitfield, __SENDERMINORPRODUCTID_ISSET_ID);
  }

  public void setSenderMinorProductIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SENDERMINORPRODUCTID_ISSET_ID, value);
  }

  /**
   * 与发送者相关的硬件平台，取值范围 —— 0:网页(pc_web), 1:内嵌页(酷狗pc桌面应用), 2:真人电台（fm）(酷狗pc桌面应用), 3:安卓, 4:iOS, 5:iPad, 6:Html5 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public long getSenderHardwarePlatform() {
    return this.senderHardwarePlatform;
  }

  /**
   * 与发送者相关的硬件平台，取值范围 —— 0:网页(pc_web), 1:内嵌页(酷狗pc桌面应用), 2:真人电台（fm）(酷狗pc桌面应用), 3:安卓, 4:iOS, 5:iPad, 6:Html5 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public AlbumConsume setSenderHardwarePlatform(long senderHardwarePlatform) {
    this.senderHardwarePlatform = senderHardwarePlatform;
    setSenderHardwarePlatformIsSet(true);
    return this;
  }

  public void unsetSenderHardwarePlatform() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SENDERHARDWAREPLATFORM_ISSET_ID);
  }

  /** Returns true if field senderHardwarePlatform is set (has been assigned a value) and false otherwise */
  public boolean isSetSenderHardwarePlatform() {
    return EncodingUtils.testBit(__isset_bitfield, __SENDERHARDWAREPLATFORM_ISSET_ID);
  }

  public void setSenderHardwarePlatformIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SENDERHARDWAREPLATFORM_ISSET_ID, value);
  }

  /**
   * 与发送者相关的渠道，按各自业务选择数据区间 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public long getSenderChannelId() {
    return this.senderChannelId;
  }

  /**
   * 与发送者相关的渠道，按各自业务选择数据区间 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public AlbumConsume setSenderChannelId(long senderChannelId) {
    this.senderChannelId = senderChannelId;
    setSenderChannelIdIsSet(true);
    return this;
  }

  public void unsetSenderChannelId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SENDERCHANNELID_ISSET_ID);
  }

  /** Returns true if field senderChannelId is set (has been assigned a value) and false otherwise */
  public boolean isSetSenderChannelId() {
    return EncodingUtils.testBit(__isset_bitfield, __SENDERCHANNELID_ISSET_ID);
  }

  public void setSenderChannelIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SENDERCHANNELID_ISSET_ID, value);
  }

  /**
   * 与发送者相关的渠道的渠道，按各自业务选择数据区间 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public long getSenderSubChannelId() {
    return this.senderSubChannelId;
  }

  /**
   * 与发送者相关的渠道的渠道，按各自业务选择数据区间 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public AlbumConsume setSenderSubChannelId(long senderSubChannelId) {
    this.senderSubChannelId = senderSubChannelId;
    setSenderSubChannelIdIsSet(true);
    return this;
  }

  public void unsetSenderSubChannelId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SENDERSUBCHANNELID_ISSET_ID);
  }

  /** Returns true if field senderSubChannelId is set (has been assigned a value) and false otherwise */
  public boolean isSetSenderSubChannelId() {
    return EncodingUtils.testBit(__isset_bitfield, __SENDERSUBCHANNELID_ISSET_ID);
  }

  public void setSenderSubChannelIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SENDERSUBCHANNELID_ISSET_ID, value);
  }

  /**
   * 与接收者有关的部门 —— 与前边的 senderDepartment 字段的取值范围一致 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public long getReceiverDepartmentId() {
    return this.receiverDepartmentId;
  }

  /**
   * 与接收者有关的部门 —— 与前边的 senderDepartment 字段的取值范围一致 @see http://wiki.fxwork.kugou.net/doc/G-技术组/平台项目/用户资产/需求.md
   */
  public AlbumConsume setReceiverDepartmentId(long receiverDepartmentId) {
    this.receiverDepartmentId = receiverDepartmentId;
    setReceiverDepartmentIdIsSet(true);
    return this;
  }

  public void unsetReceiverDepartmentId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RECEIVERDEPARTMENTID_ISSET_ID);
  }

  /** Returns true if field receiverDepartmentId is set (has been assigned a value) and false otherwise */
  public boolean isSetReceiverDepartmentId() {
    return EncodingUtils.testBit(__isset_bitfield, __RECEIVERDEPARTMENTID_ISSET_ID);
  }

  public void setReceiverDepartmentIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RECEIVERDEPARTMENTID_ISSET_ID, value);
  }

  /**
   * 预定业务类型id(送礼服务、守护等 , 这个id跟sign参数需要的固定密钥[加盐]一一对应)
   */
  public long getAccountChangeType() {
    return this.accountChangeType;
  }

  /**
   * 预定业务类型id(送礼服务、守护等 , 这个id跟sign参数需要的固定密钥[加盐]一一对应)
   */
  public AlbumConsume setAccountChangeType(long accountChangeType) {
    this.accountChangeType = accountChangeType;
    setAccountChangeTypeIsSet(true);
    return this;
  }

  public void unsetAccountChangeType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACCOUNTCHANGETYPE_ISSET_ID);
  }

  /** Returns true if field accountChangeType is set (has been assigned a value) and false otherwise */
  public boolean isSetAccountChangeType() {
    return EncodingUtils.testBit(__isset_bitfield, __ACCOUNTCHANGETYPE_ISSET_ID);
  }

  public void setAccountChangeTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACCOUNTCHANGETYPE_ISSET_ID, value);
  }

  /**
   * 变更描述
   */
  public String getFxcChangeDesc() {
    return this.fxcChangeDesc;
  }

  /**
   * 变更描述
   */
  public AlbumConsume setFxcChangeDesc(String fxcChangeDesc) {
    this.fxcChangeDesc = fxcChangeDesc;
    return this;
  }

  public void unsetFxcChangeDesc() {
    this.fxcChangeDesc = null;
  }

  /** Returns true if field fxcChangeDesc is set (has been assigned a value) and false otherwise */
  public boolean isSetFxcChangeDesc() {
    return this.fxcChangeDesc != null;
  }

  public void setFxcChangeDescIsSet(boolean value) {
    if (!value) {
      this.fxcChangeDesc = null;
    }
  }

  /**
   * 星币数
   */
  public double getCoin() {
    return this.coin;
  }

  /**
   * 星币数
   */
  public AlbumConsume setCoin(double coin) {
    this.coin = coin;
    setCoinIsSet(true);
    return this;
  }

  public void unsetCoin() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COIN_ISSET_ID);
  }

  /** Returns true if field coin is set (has been assigned a value) and false otherwise */
  public boolean isSetCoin() {
    return EncodingUtils.testBit(__isset_bitfield, __COIN_ISSET_ID);
  }

  public void setCoinIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COIN_ISSET_ID, value);
  }

  /**
   * 事件ID 用来做幂等
   */
  public long getGlobalId() {
    return this.globalId;
  }

  /**
   * 事件ID 用来做幂等
   */
  public AlbumConsume setGlobalId(long globalId) {
    this.globalId = globalId;
    setGlobalIdIsSet(true);
    return this;
  }

  public void unsetGlobalId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __GLOBALID_ISSET_ID);
  }

  /** Returns true if field globalId is set (has been assigned a value) and false otherwise */
  public boolean isSetGlobalId() {
    return EncodingUtils.testBit(__isset_bitfield, __GLOBALID_ISSET_ID);
  }

  public void setGlobalIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __GLOBALID_ISSET_ID, value);
  }

  /**
   * 送礼者KugouID
   */
  public long getFromKugouId() {
    return this.fromKugouId;
  }

  /**
   * 送礼者KugouID
   */
  public AlbumConsume setFromKugouId(long fromKugouId) {
    this.fromKugouId = fromKugouId;
    setFromKugouIdIsSet(true);
    return this;
  }

  public void unsetFromKugouId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __FROMKUGOUID_ISSET_ID);
  }

  /** Returns true if field fromKugouId is set (has been assigned a value) and false otherwise */
  public boolean isSetFromKugouId() {
    return EncodingUtils.testBit(__isset_bitfield, __FROMKUGOUID_ISSET_ID);
  }

  public void setFromKugouIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __FROMKUGOUID_ISSET_ID, value);
  }

  /**
   * 收礼者KugouID
   */
  public long getToKugouId() {
    return this.toKugouId;
  }

  /**
   * 收礼者KugouID
   */
  public AlbumConsume setToKugouId(long toKugouId) {
    this.toKugouId = toKugouId;
    setToKugouIdIsSet(true);
    return this;
  }

  public void unsetToKugouId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TOKUGOUID_ISSET_ID);
  }

  /** Returns true if field toKugouId is set (has been assigned a value) and false otherwise */
  public boolean isSetToKugouId() {
    return EncodingUtils.testBit(__isset_bitfield, __TOKUGOUID_ISSET_ID);
  }

  public void setToKugouIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TOKUGOUID_ISSET_ID, value);
  }

  /**
   * 登陆token
   */
  public String getToken() {
    return this.token;
  }

  /**
   * 登陆token
   */
  public AlbumConsume setToken(String token) {
    this.token = token;
    return this;
  }

  public void unsetToken() {
    this.token = null;
  }

  /** Returns true if field token is set (has been assigned a value) and false otherwise */
  public boolean isSetToken() {
    return this.token != null;
  }

  public void setTokenIsSet(boolean value) {
    if (!value) {
      this.token = null;
    }
  }

  /**
   * 酷狗appId
   */
  public int getAppid() {
    return this.appid;
  }

  /**
   * 酷狗appId
   */
  public AlbumConsume setAppid(int appid) {
    this.appid = appid;
    setAppidIsSet(true);
    return this;
  }

  public void unsetAppid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __APPID_ISSET_ID);
  }

  /** Returns true if field appid is set (has been assigned a value) and false otherwise */
  public boolean isSetAppid() {
    return EncodingUtils.testBit(__isset_bitfield, __APPID_ISSET_ID);
  }

  public void setAppidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __APPID_ISSET_ID, value);
  }

  /**
   * 在哪个房间,没有的时候传0
   */
  public int getRoomId() {
    return this.roomId;
  }

  /**
   * 在哪个房间,没有的时候传0
   */
  public AlbumConsume setRoomId(int roomId) {
    this.roomId = roomId;
    setRoomIdIsSet(true);
    return this;
  }

  public void unsetRoomId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ROOMID_ISSET_ID);
  }

  /** Returns true if field roomId is set (has been assigned a value) and false otherwise */
  public boolean isSetRoomId() {
    return EncodingUtils.testBit(__isset_bitfield, __ROOMID_ISSET_ID);
  }

  public void setRoomIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ROOMID_ISSET_ID, value);
  }

  /**
   * 用户真实IP
   */
  public String getIp() {
    return this.ip;
  }

  /**
   * 用户真实IP
   */
  public AlbumConsume setIp(String ip) {
    this.ip = ip;
    return this;
  }

  public void unsetIp() {
    this.ip = null;
  }

  /** Returns true if field ip is set (has been assigned a value) and false otherwise */
  public boolean isSetIp() {
    return this.ip != null;
  }

  public void setIpIsSet(boolean value) {
    if (!value) {
      this.ip = null;
    }
  }

  /**
   * 用户信息
   */
  public String getUserAgent() {
    return this.userAgent;
  }

  /**
   * 用户信息
   */
  public AlbumConsume setUserAgent(String userAgent) {
    this.userAgent = userAgent;
    return this;
  }

  public void unsetUserAgent() {
    this.userAgent = null;
  }

  /** Returns true if field userAgent is set (has been assigned a value) and false otherwise */
  public boolean isSetUserAgent() {
    return this.userAgent != null;
  }

  public void setUserAgentIsSet(boolean value) {
    if (!value) {
      this.userAgent = null;
    }
  }

  /**
   * 变更类型
   */
  public int getCategory() {
    return this.category;
  }

  /**
   * 变更类型
   */
  public AlbumConsume setCategory(int category) {
    this.category = category;
    setCategoryIsSet(true);
    return this;
  }

  public void unsetCategory() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CATEGORY_ISSET_ID);
  }

  /** Returns true if field category is set (has been assigned a value) and false otherwise */
  public boolean isSetCategory() {
    return EncodingUtils.testBit(__isset_bitfield, __CATEGORY_ISSET_ID);
  }

  public void setCategoryIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CATEGORY_ISSET_ID, value);
  }

  /**
   * 消费行为类别(0:送礼物,1:飞屏,2:广播,3:贴条,4:点歌,5:抢座,...)
   */
  public int getActionId() {
    return this.actionId;
  }

  /**
   * 消费行为类别(0:送礼物,1:飞屏,2:广播,3:贴条,4:点歌,5:抢座,...)
   */
  public AlbumConsume setActionId(int actionId) {
    this.actionId = actionId;
    setActionIdIsSet(true);
    return this;
  }

  public void unsetActionId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTIONID_ISSET_ID);
  }

  /** Returns true if field actionId is set (has been assigned a value) and false otherwise */
  public boolean isSetActionId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTIONID_ISSET_ID);
  }

  public void setActionIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTIONID_ISSET_ID, value);
  }

  /**
   * 礼物数量,这里只用于记录消费表
   */
  public int getGiftNum() {
    return this.giftNum;
  }

  /**
   * 礼物数量,这里只用于记录消费表
   */
  public AlbumConsume setGiftNum(int giftNum) {
    this.giftNum = giftNum;
    setGiftNumIsSet(true);
    return this;
  }

  public void unsetGiftNum() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __GIFTNUM_ISSET_ID);
  }

  /** Returns true if field giftNum is set (has been assigned a value) and false otherwise */
  public boolean isSetGiftNum() {
    return EncodingUtils.testBit(__isset_bitfield, __GIFTNUM_ISSET_ID);
  }

  public void setGiftNumIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __GIFTNUM_ISSET_ID, value);
  }

  /**
   * 主播分成比例，eg.100星币的礼物，主播分成比例为0.3,工会分成比例为0.1，则主播能收到的星豆为100*0.3*(1-0.1)=27,工会收到的星豆为100*0.3*0.1=3
   */
  public double getPersonalDivideRate() {
    return this.personalDivideRate;
  }

  /**
   * 主播分成比例，eg.100星币的礼物，主播分成比例为0.3,工会分成比例为0.1，则主播能收到的星豆为100*0.3*(1-0.1)=27,工会收到的星豆为100*0.3*0.1=3
   */
  public AlbumConsume setPersonalDivideRate(double personalDivideRate) {
    this.personalDivideRate = personalDivideRate;
    setPersonalDivideRateIsSet(true);
    return this;
  }

  public void unsetPersonalDivideRate() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PERSONALDIVIDERATE_ISSET_ID);
  }

  /** Returns true if field personalDivideRate is set (has been assigned a value) and false otherwise */
  public boolean isSetPersonalDivideRate() {
    return EncodingUtils.testBit(__isset_bitfield, __PERSONALDIVIDERATE_ISSET_ID);
  }

  public void setPersonalDivideRateIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PERSONALDIVIDERATE_ISSET_ID, value);
  }

  /**
   * 时间戳 (秒)
   */
  public long getTimestamp() {
    return this.timestamp;
  }

  /**
   * 时间戳 (秒)
   */
  public AlbumConsume setTimestamp(long timestamp) {
    this.timestamp = timestamp;
    setTimestampIsSet(true);
    return this;
  }

  public void unsetTimestamp() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
  }

  /** Returns true if field timestamp is set (has been assigned a value) and false otherwise */
  public boolean isSetTimestamp() {
    return EncodingUtils.testBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
  }

  public void setTimestampIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TIMESTAMP_ISSET_ID, value);
  }

  /**
   * 拓展字段，可为空，Json字符串
   */
  public String getExt() {
    return this.ext;
  }

  /**
   * 拓展字段，可为空，Json字符串
   */
  public AlbumConsume setExt(String ext) {
    this.ext = ext;
    return this;
  }

  public void unsetExt() {
    this.ext = null;
  }

  /** Returns true if field ext is set (has been assigned a value) and false otherwise */
  public boolean isSetExt() {
    return this.ext != null;
  }

  public void setExtIsSet(boolean value) {
    if (!value) {
      this.ext = null;
    }
  }

  /**
   * md5(所有参数名字升序排列对应的数值拼接 + 固定密钥[加盐][跟对外提供服务的accountChangeType对应])
   */
  public String getSign() {
    return this.sign;
  }

  /**
   * md5(所有参数名字升序排列对应的数值拼接 + 固定密钥[加盐][跟对外提供服务的accountChangeType对应])
   */
  public AlbumConsume setSign(String sign) {
    this.sign = sign;
    return this;
  }

  public void unsetSign() {
    this.sign = null;
  }

  /** Returns true if field sign is set (has been assigned a value) and false otherwise */
  public boolean isSetSign() {
    return this.sign != null;
  }

  public void setSignIsSet(boolean value) {
    if (!value) {
      this.sign = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case PID:
      if (value == null) {
        unsetPid();
      } else {
        setPid((Integer)value);
      }
      break;

    case SENDER_DEPARTMENT_ID:
      if (value == null) {
        unsetSenderDepartmentId();
      } else {
        setSenderDepartmentId((Long)value);
      }
      break;

    case SENDER_PRODUCT_ID:
      if (value == null) {
        unsetSenderProductId();
      } else {
        setSenderProductId((Long)value);
      }
      break;

    case SENDER_MINOR_PRODUCT_ID:
      if (value == null) {
        unsetSenderMinorProductId();
      } else {
        setSenderMinorProductId((Long)value);
      }
      break;

    case SENDER_HARDWARE_PLATFORM:
      if (value == null) {
        unsetSenderHardwarePlatform();
      } else {
        setSenderHardwarePlatform((Long)value);
      }
      break;

    case SENDER_CHANNEL_ID:
      if (value == null) {
        unsetSenderChannelId();
      } else {
        setSenderChannelId((Long)value);
      }
      break;

    case SENDER_SUB_CHANNEL_ID:
      if (value == null) {
        unsetSenderSubChannelId();
      } else {
        setSenderSubChannelId((Long)value);
      }
      break;

    case RECEIVER_DEPARTMENT_ID:
      if (value == null) {
        unsetReceiverDepartmentId();
      } else {
        setReceiverDepartmentId((Long)value);
      }
      break;

    case ACCOUNT_CHANGE_TYPE:
      if (value == null) {
        unsetAccountChangeType();
      } else {
        setAccountChangeType((Long)value);
      }
      break;

    case FXC_CHANGE_DESC:
      if (value == null) {
        unsetFxcChangeDesc();
      } else {
        setFxcChangeDesc((String)value);
      }
      break;

    case COIN:
      if (value == null) {
        unsetCoin();
      } else {
        setCoin((Double)value);
      }
      break;

    case GLOBAL_ID:
      if (value == null) {
        unsetGlobalId();
      } else {
        setGlobalId((Long)value);
      }
      break;

    case FROM_KUGOU_ID:
      if (value == null) {
        unsetFromKugouId();
      } else {
        setFromKugouId((Long)value);
      }
      break;

    case TO_KUGOU_ID:
      if (value == null) {
        unsetToKugouId();
      } else {
        setToKugouId((Long)value);
      }
      break;

    case TOKEN:
      if (value == null) {
        unsetToken();
      } else {
        setToken((String)value);
      }
      break;

    case APPID:
      if (value == null) {
        unsetAppid();
      } else {
        setAppid((Integer)value);
      }
      break;

    case ROOM_ID:
      if (value == null) {
        unsetRoomId();
      } else {
        setRoomId((Integer)value);
      }
      break;

    case IP:
      if (value == null) {
        unsetIp();
      } else {
        setIp((String)value);
      }
      break;

    case USER_AGENT:
      if (value == null) {
        unsetUserAgent();
      } else {
        setUserAgent((String)value);
      }
      break;

    case CATEGORY:
      if (value == null) {
        unsetCategory();
      } else {
        setCategory((Integer)value);
      }
      break;

    case ACTION_ID:
      if (value == null) {
        unsetActionId();
      } else {
        setActionId((Integer)value);
      }
      break;

    case GIFT_NUM:
      if (value == null) {
        unsetGiftNum();
      } else {
        setGiftNum((Integer)value);
      }
      break;

    case PERSONAL_DIVIDE_RATE:
      if (value == null) {
        unsetPersonalDivideRate();
      } else {
        setPersonalDivideRate((Double)value);
      }
      break;

    case TIMESTAMP:
      if (value == null) {
        unsetTimestamp();
      } else {
        setTimestamp((Long)value);
      }
      break;

    case EXT:
      if (value == null) {
        unsetExt();
      } else {
        setExt((String)value);
      }
      break;

    case SIGN:
      if (value == null) {
        unsetSign();
      } else {
        setSign((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case PID:
      return getPid();

    case SENDER_DEPARTMENT_ID:
      return getSenderDepartmentId();

    case SENDER_PRODUCT_ID:
      return getSenderProductId();

    case SENDER_MINOR_PRODUCT_ID:
      return getSenderMinorProductId();

    case SENDER_HARDWARE_PLATFORM:
      return getSenderHardwarePlatform();

    case SENDER_CHANNEL_ID:
      return getSenderChannelId();

    case SENDER_SUB_CHANNEL_ID:
      return getSenderSubChannelId();

    case RECEIVER_DEPARTMENT_ID:
      return getReceiverDepartmentId();

    case ACCOUNT_CHANGE_TYPE:
      return getAccountChangeType();

    case FXC_CHANGE_DESC:
      return getFxcChangeDesc();

    case COIN:
      return getCoin();

    case GLOBAL_ID:
      return getGlobalId();

    case FROM_KUGOU_ID:
      return getFromKugouId();

    case TO_KUGOU_ID:
      return getToKugouId();

    case TOKEN:
      return getToken();

    case APPID:
      return getAppid();

    case ROOM_ID:
      return getRoomId();

    case IP:
      return getIp();

    case USER_AGENT:
      return getUserAgent();

    case CATEGORY:
      return getCategory();

    case ACTION_ID:
      return getActionId();

    case GIFT_NUM:
      return getGiftNum();

    case PERSONAL_DIVIDE_RATE:
      return getPersonalDivideRate();

    case TIMESTAMP:
      return getTimestamp();

    case EXT:
      return getExt();

    case SIGN:
      return getSign();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case PID:
      return isSetPid();
    case SENDER_DEPARTMENT_ID:
      return isSetSenderDepartmentId();
    case SENDER_PRODUCT_ID:
      return isSetSenderProductId();
    case SENDER_MINOR_PRODUCT_ID:
      return isSetSenderMinorProductId();
    case SENDER_HARDWARE_PLATFORM:
      return isSetSenderHardwarePlatform();
    case SENDER_CHANNEL_ID:
      return isSetSenderChannelId();
    case SENDER_SUB_CHANNEL_ID:
      return isSetSenderSubChannelId();
    case RECEIVER_DEPARTMENT_ID:
      return isSetReceiverDepartmentId();
    case ACCOUNT_CHANGE_TYPE:
      return isSetAccountChangeType();
    case FXC_CHANGE_DESC:
      return isSetFxcChangeDesc();
    case COIN:
      return isSetCoin();
    case GLOBAL_ID:
      return isSetGlobalId();
    case FROM_KUGOU_ID:
      return isSetFromKugouId();
    case TO_KUGOU_ID:
      return isSetToKugouId();
    case TOKEN:
      return isSetToken();
    case APPID:
      return isSetAppid();
    case ROOM_ID:
      return isSetRoomId();
    case IP:
      return isSetIp();
    case USER_AGENT:
      return isSetUserAgent();
    case CATEGORY:
      return isSetCategory();
    case ACTION_ID:
      return isSetActionId();
    case GIFT_NUM:
      return isSetGiftNum();
    case PERSONAL_DIVIDE_RATE:
      return isSetPersonalDivideRate();
    case TIMESTAMP:
      return isSetTimestamp();
    case EXT:
      return isSetExt();
    case SIGN:
      return isSetSign();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof AlbumConsume)
      return this.equals((AlbumConsume)that);
    return false;
  }

  public boolean equals(AlbumConsume that) {
    if (that == null)
      return false;

    boolean this_present_pid = true;
    boolean that_present_pid = true;
    if (this_present_pid || that_present_pid) {
      if (!(this_present_pid && that_present_pid))
        return false;
      if (this.pid != that.pid)
        return false;
    }

    boolean this_present_senderDepartmentId = true;
    boolean that_present_senderDepartmentId = true;
    if (this_present_senderDepartmentId || that_present_senderDepartmentId) {
      if (!(this_present_senderDepartmentId && that_present_senderDepartmentId))
        return false;
      if (this.senderDepartmentId != that.senderDepartmentId)
        return false;
    }

    boolean this_present_senderProductId = true;
    boolean that_present_senderProductId = true;
    if (this_present_senderProductId || that_present_senderProductId) {
      if (!(this_present_senderProductId && that_present_senderProductId))
        return false;
      if (this.senderProductId != that.senderProductId)
        return false;
    }

    boolean this_present_senderMinorProductId = true;
    boolean that_present_senderMinorProductId = true;
    if (this_present_senderMinorProductId || that_present_senderMinorProductId) {
      if (!(this_present_senderMinorProductId && that_present_senderMinorProductId))
        return false;
      if (this.senderMinorProductId != that.senderMinorProductId)
        return false;
    }

    boolean this_present_senderHardwarePlatform = true;
    boolean that_present_senderHardwarePlatform = true;
    if (this_present_senderHardwarePlatform || that_present_senderHardwarePlatform) {
      if (!(this_present_senderHardwarePlatform && that_present_senderHardwarePlatform))
        return false;
      if (this.senderHardwarePlatform != that.senderHardwarePlatform)
        return false;
    }

    boolean this_present_senderChannelId = true;
    boolean that_present_senderChannelId = true;
    if (this_present_senderChannelId || that_present_senderChannelId) {
      if (!(this_present_senderChannelId && that_present_senderChannelId))
        return false;
      if (this.senderChannelId != that.senderChannelId)
        return false;
    }

    boolean this_present_senderSubChannelId = true;
    boolean that_present_senderSubChannelId = true;
    if (this_present_senderSubChannelId || that_present_senderSubChannelId) {
      if (!(this_present_senderSubChannelId && that_present_senderSubChannelId))
        return false;
      if (this.senderSubChannelId != that.senderSubChannelId)
        return false;
    }

    boolean this_present_receiverDepartmentId = true;
    boolean that_present_receiverDepartmentId = true;
    if (this_present_receiverDepartmentId || that_present_receiverDepartmentId) {
      if (!(this_present_receiverDepartmentId && that_present_receiverDepartmentId))
        return false;
      if (this.receiverDepartmentId != that.receiverDepartmentId)
        return false;
    }

    boolean this_present_accountChangeType = true;
    boolean that_present_accountChangeType = true;
    if (this_present_accountChangeType || that_present_accountChangeType) {
      if (!(this_present_accountChangeType && that_present_accountChangeType))
        return false;
      if (this.accountChangeType != that.accountChangeType)
        return false;
    }

    boolean this_present_fxcChangeDesc = true && this.isSetFxcChangeDesc();
    boolean that_present_fxcChangeDesc = true && that.isSetFxcChangeDesc();
    if (this_present_fxcChangeDesc || that_present_fxcChangeDesc) {
      if (!(this_present_fxcChangeDesc && that_present_fxcChangeDesc))
        return false;
      if (!this.fxcChangeDesc.equals(that.fxcChangeDesc))
        return false;
    }

    boolean this_present_coin = true;
    boolean that_present_coin = true;
    if (this_present_coin || that_present_coin) {
      if (!(this_present_coin && that_present_coin))
        return false;
      if (this.coin != that.coin)
        return false;
    }

    boolean this_present_globalId = true;
    boolean that_present_globalId = true;
    if (this_present_globalId || that_present_globalId) {
      if (!(this_present_globalId && that_present_globalId))
        return false;
      if (this.globalId != that.globalId)
        return false;
    }

    boolean this_present_fromKugouId = true;
    boolean that_present_fromKugouId = true;
    if (this_present_fromKugouId || that_present_fromKugouId) {
      if (!(this_present_fromKugouId && that_present_fromKugouId))
        return false;
      if (this.fromKugouId != that.fromKugouId)
        return false;
    }

    boolean this_present_toKugouId = true;
    boolean that_present_toKugouId = true;
    if (this_present_toKugouId || that_present_toKugouId) {
      if (!(this_present_toKugouId && that_present_toKugouId))
        return false;
      if (this.toKugouId != that.toKugouId)
        return false;
    }

    boolean this_present_token = true && this.isSetToken();
    boolean that_present_token = true && that.isSetToken();
    if (this_present_token || that_present_token) {
      if (!(this_present_token && that_present_token))
        return false;
      if (!this.token.equals(that.token))
        return false;
    }

    boolean this_present_appid = true;
    boolean that_present_appid = true;
    if (this_present_appid || that_present_appid) {
      if (!(this_present_appid && that_present_appid))
        return false;
      if (this.appid != that.appid)
        return false;
    }

    boolean this_present_roomId = true;
    boolean that_present_roomId = true;
    if (this_present_roomId || that_present_roomId) {
      if (!(this_present_roomId && that_present_roomId))
        return false;
      if (this.roomId != that.roomId)
        return false;
    }

    boolean this_present_ip = true && this.isSetIp();
    boolean that_present_ip = true && that.isSetIp();
    if (this_present_ip || that_present_ip) {
      if (!(this_present_ip && that_present_ip))
        return false;
      if (!this.ip.equals(that.ip))
        return false;
    }

    boolean this_present_userAgent = true && this.isSetUserAgent();
    boolean that_present_userAgent = true && that.isSetUserAgent();
    if (this_present_userAgent || that_present_userAgent) {
      if (!(this_present_userAgent && that_present_userAgent))
        return false;
      if (!this.userAgent.equals(that.userAgent))
        return false;
    }

    boolean this_present_category = true;
    boolean that_present_category = true;
    if (this_present_category || that_present_category) {
      if (!(this_present_category && that_present_category))
        return false;
      if (this.category != that.category)
        return false;
    }

    boolean this_present_actionId = true;
    boolean that_present_actionId = true;
    if (this_present_actionId || that_present_actionId) {
      if (!(this_present_actionId && that_present_actionId))
        return false;
      if (this.actionId != that.actionId)
        return false;
    }

    boolean this_present_giftNum = true;
    boolean that_present_giftNum = true;
    if (this_present_giftNum || that_present_giftNum) {
      if (!(this_present_giftNum && that_present_giftNum))
        return false;
      if (this.giftNum != that.giftNum)
        return false;
    }

    boolean this_present_personalDivideRate = true;
    boolean that_present_personalDivideRate = true;
    if (this_present_personalDivideRate || that_present_personalDivideRate) {
      if (!(this_present_personalDivideRate && that_present_personalDivideRate))
        return false;
      if (this.personalDivideRate != that.personalDivideRate)
        return false;
    }

    boolean this_present_timestamp = true;
    boolean that_present_timestamp = true;
    if (this_present_timestamp || that_present_timestamp) {
      if (!(this_present_timestamp && that_present_timestamp))
        return false;
      if (this.timestamp != that.timestamp)
        return false;
    }

    boolean this_present_ext = true && this.isSetExt();
    boolean that_present_ext = true && that.isSetExt();
    if (this_present_ext || that_present_ext) {
      if (!(this_present_ext && that_present_ext))
        return false;
      if (!this.ext.equals(that.ext))
        return false;
    }

    boolean this_present_sign = true && this.isSetSign();
    boolean that_present_sign = true && that.isSetSign();
    if (this_present_sign || that_present_sign) {
      if (!(this_present_sign && that_present_sign))
        return false;
      if (!this.sign.equals(that.sign))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_pid = true;
    list.add(present_pid);
    if (present_pid)
      list.add(pid);

    boolean present_senderDepartmentId = true;
    list.add(present_senderDepartmentId);
    if (present_senderDepartmentId)
      list.add(senderDepartmentId);

    boolean present_senderProductId = true;
    list.add(present_senderProductId);
    if (present_senderProductId)
      list.add(senderProductId);

    boolean present_senderMinorProductId = true;
    list.add(present_senderMinorProductId);
    if (present_senderMinorProductId)
      list.add(senderMinorProductId);

    boolean present_senderHardwarePlatform = true;
    list.add(present_senderHardwarePlatform);
    if (present_senderHardwarePlatform)
      list.add(senderHardwarePlatform);

    boolean present_senderChannelId = true;
    list.add(present_senderChannelId);
    if (present_senderChannelId)
      list.add(senderChannelId);

    boolean present_senderSubChannelId = true;
    list.add(present_senderSubChannelId);
    if (present_senderSubChannelId)
      list.add(senderSubChannelId);

    boolean present_receiverDepartmentId = true;
    list.add(present_receiverDepartmentId);
    if (present_receiverDepartmentId)
      list.add(receiverDepartmentId);

    boolean present_accountChangeType = true;
    list.add(present_accountChangeType);
    if (present_accountChangeType)
      list.add(accountChangeType);

    boolean present_fxcChangeDesc = true && (isSetFxcChangeDesc());
    list.add(present_fxcChangeDesc);
    if (present_fxcChangeDesc)
      list.add(fxcChangeDesc);

    boolean present_coin = true;
    list.add(present_coin);
    if (present_coin)
      list.add(coin);

    boolean present_globalId = true;
    list.add(present_globalId);
    if (present_globalId)
      list.add(globalId);

    boolean present_fromKugouId = true;
    list.add(present_fromKugouId);
    if (present_fromKugouId)
      list.add(fromKugouId);

    boolean present_toKugouId = true;
    list.add(present_toKugouId);
    if (present_toKugouId)
      list.add(toKugouId);

    boolean present_token = true && (isSetToken());
    list.add(present_token);
    if (present_token)
      list.add(token);

    boolean present_appid = true;
    list.add(present_appid);
    if (present_appid)
      list.add(appid);

    boolean present_roomId = true;
    list.add(present_roomId);
    if (present_roomId)
      list.add(roomId);

    boolean present_ip = true && (isSetIp());
    list.add(present_ip);
    if (present_ip)
      list.add(ip);

    boolean present_userAgent = true && (isSetUserAgent());
    list.add(present_userAgent);
    if (present_userAgent)
      list.add(userAgent);

    boolean present_category = true;
    list.add(present_category);
    if (present_category)
      list.add(category);

    boolean present_actionId = true;
    list.add(present_actionId);
    if (present_actionId)
      list.add(actionId);

    boolean present_giftNum = true;
    list.add(present_giftNum);
    if (present_giftNum)
      list.add(giftNum);

    boolean present_personalDivideRate = true;
    list.add(present_personalDivideRate);
    if (present_personalDivideRate)
      list.add(personalDivideRate);

    boolean present_timestamp = true;
    list.add(present_timestamp);
    if (present_timestamp)
      list.add(timestamp);

    boolean present_ext = true && (isSetExt());
    list.add(present_ext);
    if (present_ext)
      list.add(ext);

    boolean present_sign = true && (isSetSign());
    list.add(present_sign);
    if (present_sign)
      list.add(sign);

    return list.hashCode();
  }

  @Override
  public int compareTo(AlbumConsume other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetPid()).compareTo(other.isSetPid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pid, other.pid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSenderDepartmentId()).compareTo(other.isSetSenderDepartmentId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSenderDepartmentId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.senderDepartmentId, other.senderDepartmentId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSenderProductId()).compareTo(other.isSetSenderProductId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSenderProductId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.senderProductId, other.senderProductId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSenderMinorProductId()).compareTo(other.isSetSenderMinorProductId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSenderMinorProductId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.senderMinorProductId, other.senderMinorProductId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSenderHardwarePlatform()).compareTo(other.isSetSenderHardwarePlatform());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSenderHardwarePlatform()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.senderHardwarePlatform, other.senderHardwarePlatform);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSenderChannelId()).compareTo(other.isSetSenderChannelId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSenderChannelId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.senderChannelId, other.senderChannelId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSenderSubChannelId()).compareTo(other.isSetSenderSubChannelId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSenderSubChannelId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.senderSubChannelId, other.senderSubChannelId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetReceiverDepartmentId()).compareTo(other.isSetReceiverDepartmentId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetReceiverDepartmentId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.receiverDepartmentId, other.receiverDepartmentId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAccountChangeType()).compareTo(other.isSetAccountChangeType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAccountChangeType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.accountChangeType, other.accountChangeType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetFxcChangeDesc()).compareTo(other.isSetFxcChangeDesc());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFxcChangeDesc()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.fxcChangeDesc, other.fxcChangeDesc);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCoin()).compareTo(other.isSetCoin());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCoin()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.coin, other.coin);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGlobalId()).compareTo(other.isSetGlobalId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGlobalId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.globalId, other.globalId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetFromKugouId()).compareTo(other.isSetFromKugouId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFromKugouId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.fromKugouId, other.fromKugouId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetToKugouId()).compareTo(other.isSetToKugouId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetToKugouId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.toKugouId, other.toKugouId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetToken()).compareTo(other.isSetToken());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetToken()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.token, other.token);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAppid()).compareTo(other.isSetAppid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appid, other.appid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoomId()).compareTo(other.isSetRoomId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoomId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roomId, other.roomId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIp()).compareTo(other.isSetIp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ip, other.ip);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUserAgent()).compareTo(other.isSetUserAgent());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUserAgent()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.userAgent, other.userAgent);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCategory()).compareTo(other.isSetCategory());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCategory()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.category, other.category);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetActionId()).compareTo(other.isSetActionId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActionId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actionId, other.actionId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGiftNum()).compareTo(other.isSetGiftNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGiftNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.giftNum, other.giftNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPersonalDivideRate()).compareTo(other.isSetPersonalDivideRate());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPersonalDivideRate()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.personalDivideRate, other.personalDivideRate);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTimestamp()).compareTo(other.isSetTimestamp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTimestamp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.timestamp, other.timestamp);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExt()).compareTo(other.isSetExt());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExt()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ext, other.ext);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSign()).compareTo(other.isSetSign());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSign()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sign, other.sign);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("AlbumConsume(");
    boolean first = true;

    sb.append("pid:");
    sb.append(this.pid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("senderDepartmentId:");
    sb.append(this.senderDepartmentId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("senderProductId:");
    sb.append(this.senderProductId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("senderMinorProductId:");
    sb.append(this.senderMinorProductId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("senderHardwarePlatform:");
    sb.append(this.senderHardwarePlatform);
    first = false;
    if (!first) sb.append(", ");
    sb.append("senderChannelId:");
    sb.append(this.senderChannelId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("senderSubChannelId:");
    sb.append(this.senderSubChannelId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("receiverDepartmentId:");
    sb.append(this.receiverDepartmentId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("accountChangeType:");
    sb.append(this.accountChangeType);
    first = false;
    if (!first) sb.append(", ");
    sb.append("fxcChangeDesc:");
    if (this.fxcChangeDesc == null) {
      sb.append("null");
    } else {
      sb.append(this.fxcChangeDesc);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("coin:");
    sb.append(this.coin);
    first = false;
    if (!first) sb.append(", ");
    sb.append("globalId:");
    sb.append(this.globalId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("fromKugouId:");
    sb.append(this.fromKugouId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("toKugouId:");
    sb.append(this.toKugouId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("token:");
    if (this.token == null) {
      sb.append("null");
    } else {
      sb.append(this.token);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("appid:");
    sb.append(this.appid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("roomId:");
    sb.append(this.roomId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("ip:");
    if (this.ip == null) {
      sb.append("null");
    } else {
      sb.append(this.ip);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("userAgent:");
    if (this.userAgent == null) {
      sb.append("null");
    } else {
      sb.append(this.userAgent);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("category:");
    sb.append(this.category);
    first = false;
    if (!first) sb.append(", ");
    sb.append("actionId:");
    sb.append(this.actionId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("giftNum:");
    sb.append(this.giftNum);
    first = false;
    if (!first) sb.append(", ");
    sb.append("personalDivideRate:");
    sb.append(this.personalDivideRate);
    first = false;
    if (!first) sb.append(", ");
    sb.append("timestamp:");
    sb.append(this.timestamp);
    first = false;
    if (isSetExt()) {
      if (!first) sb.append(", ");
      sb.append("ext:");
      if (this.ext == null) {
        sb.append("null");
      } else {
        sb.append(this.ext);
      }
      first = false;
    }
    if (!first) sb.append(", ");
    sb.append("sign:");
    if (this.sign == null) {
      sb.append("null");
    } else {
      sb.append(this.sign);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'pid' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'senderDepartmentId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'senderProductId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'senderMinorProductId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'senderHardwarePlatform' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'senderChannelId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'senderSubChannelId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'receiverDepartmentId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'accountChangeType' because it's a primitive and you chose the non-beans generator.
    if (fxcChangeDesc == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'fxcChangeDesc' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'coin' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'globalId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'fromKugouId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'toKugouId' because it's a primitive and you chose the non-beans generator.
    if (token == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'token' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'appid' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'roomId' because it's a primitive and you chose the non-beans generator.
    if (ip == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'ip' was not present! Struct: " + toString());
    }
    if (userAgent == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'userAgent' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'category' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'actionId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'giftNum' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'personalDivideRate' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'timestamp' because it's a primitive and you chose the non-beans generator.
    if (sign == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'sign' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class AlbumConsumeStandardSchemeFactory implements SchemeFactory {
    public AlbumConsumeStandardScheme getScheme() {
      return new AlbumConsumeStandardScheme();
    }
  }

  private static class AlbumConsumeStandardScheme extends StandardScheme<AlbumConsume> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, AlbumConsume struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // PID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.pid = iprot.readI32();
              struct.setPidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // SENDER_DEPARTMENT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.senderDepartmentId = iprot.readI64();
              struct.setSenderDepartmentIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // SENDER_PRODUCT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.senderProductId = iprot.readI64();
              struct.setSenderProductIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // SENDER_MINOR_PRODUCT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.senderMinorProductId = iprot.readI64();
              struct.setSenderMinorProductIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // SENDER_HARDWARE_PLATFORM
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.senderHardwarePlatform = iprot.readI64();
              struct.setSenderHardwarePlatformIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // SENDER_CHANNEL_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.senderChannelId = iprot.readI64();
              struct.setSenderChannelIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // SENDER_SUB_CHANNEL_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.senderSubChannelId = iprot.readI64();
              struct.setSenderSubChannelIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // RECEIVER_DEPARTMENT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.receiverDepartmentId = iprot.readI64();
              struct.setReceiverDepartmentIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // ACCOUNT_CHANGE_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.accountChangeType = iprot.readI64();
              struct.setAccountChangeTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // FXC_CHANGE_DESC
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.fxcChangeDesc = iprot.readString();
              struct.setFxcChangeDescIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // COIN
            if (schemeField.type == org.apache.thrift.protocol.TType.DOUBLE) {
              struct.coin = iprot.readDouble();
              struct.setCoinIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // GLOBAL_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.globalId = iprot.readI64();
              struct.setGlobalIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // FROM_KUGOU_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.fromKugouId = iprot.readI64();
              struct.setFromKugouIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 14: // TO_KUGOU_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.toKugouId = iprot.readI64();
              struct.setToKugouIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // TOKEN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.token = iprot.readString();
              struct.setTokenIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 16: // APPID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.appid = iprot.readI32();
              struct.setAppidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 17: // ROOM_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.roomId = iprot.readI32();
              struct.setRoomIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 18: // IP
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.ip = iprot.readString();
              struct.setIpIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 19: // USER_AGENT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.userAgent = iprot.readString();
              struct.setUserAgentIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 20: // CATEGORY
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.category = iprot.readI32();
              struct.setCategoryIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 21: // ACTION_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.actionId = iprot.readI32();
              struct.setActionIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 22: // GIFT_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.giftNum = iprot.readI32();
              struct.setGiftNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 23: // PERSONAL_DIVIDE_RATE
            if (schemeField.type == org.apache.thrift.protocol.TType.DOUBLE) {
              struct.personalDivideRate = iprot.readDouble();
              struct.setPersonalDivideRateIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 24: // TIMESTAMP
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.timestamp = iprot.readI64();
              struct.setTimestampIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 25: // EXT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.ext = iprot.readString();
              struct.setExtIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 26: // SIGN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.sign = iprot.readString();
              struct.setSignIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetPid()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'pid' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetSenderDepartmentId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'senderDepartmentId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetSenderProductId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'senderProductId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetSenderMinorProductId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'senderMinorProductId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetSenderHardwarePlatform()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'senderHardwarePlatform' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetSenderChannelId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'senderChannelId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetSenderSubChannelId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'senderSubChannelId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetReceiverDepartmentId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'receiverDepartmentId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetAccountChangeType()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'accountChangeType' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetCoin()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'coin' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetGlobalId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'globalId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetFromKugouId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'fromKugouId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetToKugouId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'toKugouId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetAppid()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'appid' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetRoomId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'roomId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetCategory()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'category' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetActionId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'actionId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetGiftNum()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'giftNum' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetPersonalDivideRate()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'personalDivideRate' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetTimestamp()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'timestamp' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, AlbumConsume struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(PID_FIELD_DESC);
      oprot.writeI32(struct.pid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SENDER_DEPARTMENT_ID_FIELD_DESC);
      oprot.writeI64(struct.senderDepartmentId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SENDER_PRODUCT_ID_FIELD_DESC);
      oprot.writeI64(struct.senderProductId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SENDER_MINOR_PRODUCT_ID_FIELD_DESC);
      oprot.writeI64(struct.senderMinorProductId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SENDER_HARDWARE_PLATFORM_FIELD_DESC);
      oprot.writeI64(struct.senderHardwarePlatform);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SENDER_CHANNEL_ID_FIELD_DESC);
      oprot.writeI64(struct.senderChannelId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SENDER_SUB_CHANNEL_ID_FIELD_DESC);
      oprot.writeI64(struct.senderSubChannelId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RECEIVER_DEPARTMENT_ID_FIELD_DESC);
      oprot.writeI64(struct.receiverDepartmentId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ACCOUNT_CHANGE_TYPE_FIELD_DESC);
      oprot.writeI64(struct.accountChangeType);
      oprot.writeFieldEnd();
      if (struct.fxcChangeDesc != null) {
        oprot.writeFieldBegin(FXC_CHANGE_DESC_FIELD_DESC);
        oprot.writeString(struct.fxcChangeDesc);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(COIN_FIELD_DESC);
      oprot.writeDouble(struct.coin);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(GLOBAL_ID_FIELD_DESC);
      oprot.writeI64(struct.globalId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(FROM_KUGOU_ID_FIELD_DESC);
      oprot.writeI64(struct.fromKugouId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TO_KUGOU_ID_FIELD_DESC);
      oprot.writeI64(struct.toKugouId);
      oprot.writeFieldEnd();
      if (struct.token != null) {
        oprot.writeFieldBegin(TOKEN_FIELD_DESC);
        oprot.writeString(struct.token);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(APPID_FIELD_DESC);
      oprot.writeI32(struct.appid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ROOM_ID_FIELD_DESC);
      oprot.writeI32(struct.roomId);
      oprot.writeFieldEnd();
      if (struct.ip != null) {
        oprot.writeFieldBegin(IP_FIELD_DESC);
        oprot.writeString(struct.ip);
        oprot.writeFieldEnd();
      }
      if (struct.userAgent != null) {
        oprot.writeFieldBegin(USER_AGENT_FIELD_DESC);
        oprot.writeString(struct.userAgent);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(CATEGORY_FIELD_DESC);
      oprot.writeI32(struct.category);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ACTION_ID_FIELD_DESC);
      oprot.writeI32(struct.actionId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(GIFT_NUM_FIELD_DESC);
      oprot.writeI32(struct.giftNum);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PERSONAL_DIVIDE_RATE_FIELD_DESC);
      oprot.writeDouble(struct.personalDivideRate);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TIMESTAMP_FIELD_DESC);
      oprot.writeI64(struct.timestamp);
      oprot.writeFieldEnd();
      if (struct.ext != null) {
        if (struct.isSetExt()) {
          oprot.writeFieldBegin(EXT_FIELD_DESC);
          oprot.writeString(struct.ext);
          oprot.writeFieldEnd();
        }
      }
      if (struct.sign != null) {
        oprot.writeFieldBegin(SIGN_FIELD_DESC);
        oprot.writeString(struct.sign);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class AlbumConsumeTupleSchemeFactory implements SchemeFactory {
    public AlbumConsumeTupleScheme getScheme() {
      return new AlbumConsumeTupleScheme();
    }
  }

  private static class AlbumConsumeTupleScheme extends TupleScheme<AlbumConsume> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, AlbumConsume struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeI32(struct.pid);
      oprot.writeI64(struct.senderDepartmentId);
      oprot.writeI64(struct.senderProductId);
      oprot.writeI64(struct.senderMinorProductId);
      oprot.writeI64(struct.senderHardwarePlatform);
      oprot.writeI64(struct.senderChannelId);
      oprot.writeI64(struct.senderSubChannelId);
      oprot.writeI64(struct.receiverDepartmentId);
      oprot.writeI64(struct.accountChangeType);
      oprot.writeString(struct.fxcChangeDesc);
      oprot.writeDouble(struct.coin);
      oprot.writeI64(struct.globalId);
      oprot.writeI64(struct.fromKugouId);
      oprot.writeI64(struct.toKugouId);
      oprot.writeString(struct.token);
      oprot.writeI32(struct.appid);
      oprot.writeI32(struct.roomId);
      oprot.writeString(struct.ip);
      oprot.writeString(struct.userAgent);
      oprot.writeI32(struct.category);
      oprot.writeI32(struct.actionId);
      oprot.writeI32(struct.giftNum);
      oprot.writeDouble(struct.personalDivideRate);
      oprot.writeI64(struct.timestamp);
      oprot.writeString(struct.sign);
      BitSet optionals = new BitSet();
      if (struct.isSetExt()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetExt()) {
        oprot.writeString(struct.ext);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, AlbumConsume struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.pid = iprot.readI32();
      struct.setPidIsSet(true);
      struct.senderDepartmentId = iprot.readI64();
      struct.setSenderDepartmentIdIsSet(true);
      struct.senderProductId = iprot.readI64();
      struct.setSenderProductIdIsSet(true);
      struct.senderMinorProductId = iprot.readI64();
      struct.setSenderMinorProductIdIsSet(true);
      struct.senderHardwarePlatform = iprot.readI64();
      struct.setSenderHardwarePlatformIsSet(true);
      struct.senderChannelId = iprot.readI64();
      struct.setSenderChannelIdIsSet(true);
      struct.senderSubChannelId = iprot.readI64();
      struct.setSenderSubChannelIdIsSet(true);
      struct.receiverDepartmentId = iprot.readI64();
      struct.setReceiverDepartmentIdIsSet(true);
      struct.accountChangeType = iprot.readI64();
      struct.setAccountChangeTypeIsSet(true);
      struct.fxcChangeDesc = iprot.readString();
      struct.setFxcChangeDescIsSet(true);
      struct.coin = iprot.readDouble();
      struct.setCoinIsSet(true);
      struct.globalId = iprot.readI64();
      struct.setGlobalIdIsSet(true);
      struct.fromKugouId = iprot.readI64();
      struct.setFromKugouIdIsSet(true);
      struct.toKugouId = iprot.readI64();
      struct.setToKugouIdIsSet(true);
      struct.token = iprot.readString();
      struct.setTokenIsSet(true);
      struct.appid = iprot.readI32();
      struct.setAppidIsSet(true);
      struct.roomId = iprot.readI32();
      struct.setRoomIdIsSet(true);
      struct.ip = iprot.readString();
      struct.setIpIsSet(true);
      struct.userAgent = iprot.readString();
      struct.setUserAgentIsSet(true);
      struct.category = iprot.readI32();
      struct.setCategoryIsSet(true);
      struct.actionId = iprot.readI32();
      struct.setActionIdIsSet(true);
      struct.giftNum = iprot.readI32();
      struct.setGiftNumIsSet(true);
      struct.personalDivideRate = iprot.readDouble();
      struct.setPersonalDivideRateIsSet(true);
      struct.timestamp = iprot.readI64();
      struct.setTimestampIsSet(true);
      struct.sign = iprot.readString();
      struct.setSignIsSet(true);
      BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        struct.ext = iprot.readString();
        struct.setExtIsSet(true);
      }
    }
  }

}

