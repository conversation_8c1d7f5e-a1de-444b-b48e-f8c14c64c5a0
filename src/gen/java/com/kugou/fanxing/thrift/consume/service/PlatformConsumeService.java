/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.thrift.consume.service;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-01-12")
public class PlatformConsumeService {

  public interface Iface {

    /**
     * 消费扣币接口
     * 
     * @param vo
     */
    public ConsumeResp consume(ConsumeVO vo) throws org.apache.thrift.TException;

    /**
     * 消费扣币接口(必須是充值賬戶的money)
     * 
     * @param vo
     */
    public ConsumeResp consumeStrictMoney(ConsumeVO vo) throws org.apache.thrift.TException;

    /**
     * mv送礼消费接口，存在多个酷狗Id分成的逻辑
     * 
     * @param vo
     */
    public ConsumeResp mvConsume(ConsumeVO vo) throws org.apache.thrift.TException;

    /**
     * 修改星豆接口
     * 
     * @param vo
     */
    public ConsumeResp bean(BeanVO vo) throws org.apache.thrift.TException;

    /**
     * 充扣接口
     * 
     * @param vo
     */
    public ConsumeResp rechargeFee(RechargeFeeVO vo) throws org.apache.thrift.TException;

    /**
     * 添加财务日志和消费日志，适配仓库出入仓
     * 
     * @param vo
     */
    public ConsumeResp addStorageConsumeLog(StorageConsumeVO vo) throws org.apache.thrift.TException;

    /**
     * 获得用户金钱接口，非实时，查从库
     * 
     * @param kugouId
     */
    public ConsumeResp getUserMoney(long kugouId) throws org.apache.thrift.TException;

    /**
     * 数字专辑送礼接口
     * 
     * @param vo
     */
    public ConsumeResp albumConsume(AlbumConsume vo) throws org.apache.thrift.TException;

    /**
     * 加星豆补单
     * 
     * @param appId
     * @param consumeId
     * @param timeStamp
     */
    public ConsumeResp fixBean(int appId, long consumeId, long timeStamp) throws org.apache.thrift.TException;

    /**
     * 仓库消费接口
     * 
     * @param vo
     */
    public ConsumeResp storageConsume(StorageVO vo) throws org.apache.thrift.TException;

  }

  public interface AsyncIface {

    public void consume(ConsumeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void consumeStrictMoney(ConsumeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void mvConsume(ConsumeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void bean(BeanVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void rechargeFee(RechargeFeeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void addStorageConsumeLog(StorageConsumeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getUserMoney(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void albumConsume(AlbumConsume vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void fixBean(int appId, long consumeId, long timeStamp, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void storageConsume(StorageVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

  }

  public static class Client extends org.apache.thrift.TServiceClient implements Iface {
    public static class Factory implements org.apache.thrift.TServiceClientFactory<Client> {
      public Factory() {}
      public Client getClient(org.apache.thrift.protocol.TProtocol prot) {
        return new Client(prot);
      }
      public Client getClient(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
        return new Client(iprot, oprot);
      }
    }

    public Client(org.apache.thrift.protocol.TProtocol prot)
    {
      super(prot, prot);
    }

    public Client(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
      super(iprot, oprot);
    }

    public ConsumeResp consume(ConsumeVO vo) throws org.apache.thrift.TException
    {
      send_consume(vo);
      return recv_consume();
    }

    public void send_consume(ConsumeVO vo) throws org.apache.thrift.TException
    {
      consume_args args = new consume_args();
      args.setVo(vo);
      sendBase("consume", args);
    }

    public ConsumeResp recv_consume() throws org.apache.thrift.TException
    {
      consume_result result = new consume_result();
      receiveBase(result, "consume");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "consume failed: unknown result");
    }

    public ConsumeResp consumeStrictMoney(ConsumeVO vo) throws org.apache.thrift.TException
    {
      send_consumeStrictMoney(vo);
      return recv_consumeStrictMoney();
    }

    public void send_consumeStrictMoney(ConsumeVO vo) throws org.apache.thrift.TException
    {
      consumeStrictMoney_args args = new consumeStrictMoney_args();
      args.setVo(vo);
      sendBase("consumeStrictMoney", args);
    }

    public ConsumeResp recv_consumeStrictMoney() throws org.apache.thrift.TException
    {
      consumeStrictMoney_result result = new consumeStrictMoney_result();
      receiveBase(result, "consumeStrictMoney");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "consumeStrictMoney failed: unknown result");
    }

    public ConsumeResp mvConsume(ConsumeVO vo) throws org.apache.thrift.TException
    {
      send_mvConsume(vo);
      return recv_mvConsume();
    }

    public void send_mvConsume(ConsumeVO vo) throws org.apache.thrift.TException
    {
      mvConsume_args args = new mvConsume_args();
      args.setVo(vo);
      sendBase("mvConsume", args);
    }

    public ConsumeResp recv_mvConsume() throws org.apache.thrift.TException
    {
      mvConsume_result result = new mvConsume_result();
      receiveBase(result, "mvConsume");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "mvConsume failed: unknown result");
    }

    public ConsumeResp bean(BeanVO vo) throws org.apache.thrift.TException
    {
      send_bean(vo);
      return recv_bean();
    }

    public void send_bean(BeanVO vo) throws org.apache.thrift.TException
    {
      bean_args args = new bean_args();
      args.setVo(vo);
      sendBase("bean", args);
    }

    public ConsumeResp recv_bean() throws org.apache.thrift.TException
    {
      bean_result result = new bean_result();
      receiveBase(result, "bean");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "bean failed: unknown result");
    }

    public ConsumeResp rechargeFee(RechargeFeeVO vo) throws org.apache.thrift.TException
    {
      send_rechargeFee(vo);
      return recv_rechargeFee();
    }

    public void send_rechargeFee(RechargeFeeVO vo) throws org.apache.thrift.TException
    {
      rechargeFee_args args = new rechargeFee_args();
      args.setVo(vo);
      sendBase("rechargeFee", args);
    }

    public ConsumeResp recv_rechargeFee() throws org.apache.thrift.TException
    {
      rechargeFee_result result = new rechargeFee_result();
      receiveBase(result, "rechargeFee");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "rechargeFee failed: unknown result");
    }

    public ConsumeResp addStorageConsumeLog(StorageConsumeVO vo) throws org.apache.thrift.TException
    {
      send_addStorageConsumeLog(vo);
      return recv_addStorageConsumeLog();
    }

    public void send_addStorageConsumeLog(StorageConsumeVO vo) throws org.apache.thrift.TException
    {
      addStorageConsumeLog_args args = new addStorageConsumeLog_args();
      args.setVo(vo);
      sendBase("addStorageConsumeLog", args);
    }

    public ConsumeResp recv_addStorageConsumeLog() throws org.apache.thrift.TException
    {
      addStorageConsumeLog_result result = new addStorageConsumeLog_result();
      receiveBase(result, "addStorageConsumeLog");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "addStorageConsumeLog failed: unknown result");
    }

    public ConsumeResp getUserMoney(long kugouId) throws org.apache.thrift.TException
    {
      send_getUserMoney(kugouId);
      return recv_getUserMoney();
    }

    public void send_getUserMoney(long kugouId) throws org.apache.thrift.TException
    {
      getUserMoney_args args = new getUserMoney_args();
      args.setKugouId(kugouId);
      sendBase("getUserMoney", args);
    }

    public ConsumeResp recv_getUserMoney() throws org.apache.thrift.TException
    {
      getUserMoney_result result = new getUserMoney_result();
      receiveBase(result, "getUserMoney");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getUserMoney failed: unknown result");
    }

    public ConsumeResp albumConsume(AlbumConsume vo) throws org.apache.thrift.TException
    {
      send_albumConsume(vo);
      return recv_albumConsume();
    }

    public void send_albumConsume(AlbumConsume vo) throws org.apache.thrift.TException
    {
      albumConsume_args args = new albumConsume_args();
      args.setVo(vo);
      sendBase("albumConsume", args);
    }

    public ConsumeResp recv_albumConsume() throws org.apache.thrift.TException
    {
      albumConsume_result result = new albumConsume_result();
      receiveBase(result, "albumConsume");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "albumConsume failed: unknown result");
    }

    public ConsumeResp fixBean(int appId, long consumeId, long timeStamp) throws org.apache.thrift.TException
    {
      send_fixBean(appId, consumeId, timeStamp);
      return recv_fixBean();
    }

    public void send_fixBean(int appId, long consumeId, long timeStamp) throws org.apache.thrift.TException
    {
      fixBean_args args = new fixBean_args();
      args.setAppId(appId);
      args.setConsumeId(consumeId);
      args.setTimeStamp(timeStamp);
      sendBase("fixBean", args);
    }

    public ConsumeResp recv_fixBean() throws org.apache.thrift.TException
    {
      fixBean_result result = new fixBean_result();
      receiveBase(result, "fixBean");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "fixBean failed: unknown result");
    }

    public ConsumeResp storageConsume(StorageVO vo) throws org.apache.thrift.TException
    {
      send_storageConsume(vo);
      return recv_storageConsume();
    }

    public void send_storageConsume(StorageVO vo) throws org.apache.thrift.TException
    {
      storageConsume_args args = new storageConsume_args();
      args.setVo(vo);
      sendBase("storageConsume", args);
    }

    public ConsumeResp recv_storageConsume() throws org.apache.thrift.TException
    {
      storageConsume_result result = new storageConsume_result();
      receiveBase(result, "storageConsume");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "storageConsume failed: unknown result");
    }

  }
  public static class AsyncClient extends org.apache.thrift.async.TAsyncClient implements AsyncIface {
    public static class Factory implements org.apache.thrift.async.TAsyncClientFactory<AsyncClient> {
      private org.apache.thrift.async.TAsyncClientManager clientManager;
      private org.apache.thrift.protocol.TProtocolFactory protocolFactory;
      public Factory(org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.protocol.TProtocolFactory protocolFactory) {
        this.clientManager = clientManager;
        this.protocolFactory = protocolFactory;
      }
      public AsyncClient getAsyncClient(org.apache.thrift.transport.TNonblockingTransport transport) {
        return new AsyncClient(protocolFactory, clientManager, transport);
      }
    }

    public AsyncClient(org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.transport.TNonblockingTransport transport) {
      super(protocolFactory, clientManager, transport);
    }

    public void consume(ConsumeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      consume_call method_call = new consume_call(vo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class consume_call extends org.apache.thrift.async.TAsyncMethodCall {
      private ConsumeVO vo;
      public consume_call(ConsumeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.vo = vo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("consume", org.apache.thrift.protocol.TMessageType.CALL, 0));
        consume_args args = new consume_args();
        args.setVo(vo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ConsumeResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_consume();
      }
    }

    public void consumeStrictMoney(ConsumeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      consumeStrictMoney_call method_call = new consumeStrictMoney_call(vo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class consumeStrictMoney_call extends org.apache.thrift.async.TAsyncMethodCall {
      private ConsumeVO vo;
      public consumeStrictMoney_call(ConsumeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.vo = vo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("consumeStrictMoney", org.apache.thrift.protocol.TMessageType.CALL, 0));
        consumeStrictMoney_args args = new consumeStrictMoney_args();
        args.setVo(vo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ConsumeResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_consumeStrictMoney();
      }
    }

    public void mvConsume(ConsumeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      mvConsume_call method_call = new mvConsume_call(vo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class mvConsume_call extends org.apache.thrift.async.TAsyncMethodCall {
      private ConsumeVO vo;
      public mvConsume_call(ConsumeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.vo = vo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("mvConsume", org.apache.thrift.protocol.TMessageType.CALL, 0));
        mvConsume_args args = new mvConsume_args();
        args.setVo(vo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ConsumeResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_mvConsume();
      }
    }

    public void bean(BeanVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      bean_call method_call = new bean_call(vo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class bean_call extends org.apache.thrift.async.TAsyncMethodCall {
      private BeanVO vo;
      public bean_call(BeanVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.vo = vo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("bean", org.apache.thrift.protocol.TMessageType.CALL, 0));
        bean_args args = new bean_args();
        args.setVo(vo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ConsumeResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_bean();
      }
    }

    public void rechargeFee(RechargeFeeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      rechargeFee_call method_call = new rechargeFee_call(vo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class rechargeFee_call extends org.apache.thrift.async.TAsyncMethodCall {
      private RechargeFeeVO vo;
      public rechargeFee_call(RechargeFeeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.vo = vo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("rechargeFee", org.apache.thrift.protocol.TMessageType.CALL, 0));
        rechargeFee_args args = new rechargeFee_args();
        args.setVo(vo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ConsumeResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_rechargeFee();
      }
    }

    public void addStorageConsumeLog(StorageConsumeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      addStorageConsumeLog_call method_call = new addStorageConsumeLog_call(vo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class addStorageConsumeLog_call extends org.apache.thrift.async.TAsyncMethodCall {
      private StorageConsumeVO vo;
      public addStorageConsumeLog_call(StorageConsumeVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.vo = vo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("addStorageConsumeLog", org.apache.thrift.protocol.TMessageType.CALL, 0));
        addStorageConsumeLog_args args = new addStorageConsumeLog_args();
        args.setVo(vo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ConsumeResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_addStorageConsumeLog();
      }
    }

    public void getUserMoney(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getUserMoney_call method_call = new getUserMoney_call(kugouId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getUserMoney_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      public getUserMoney_call(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getUserMoney", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getUserMoney_args args = new getUserMoney_args();
        args.setKugouId(kugouId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ConsumeResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getUserMoney();
      }
    }

    public void albumConsume(AlbumConsume vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      albumConsume_call method_call = new albumConsume_call(vo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class albumConsume_call extends org.apache.thrift.async.TAsyncMethodCall {
      private AlbumConsume vo;
      public albumConsume_call(AlbumConsume vo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.vo = vo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("albumConsume", org.apache.thrift.protocol.TMessageType.CALL, 0));
        albumConsume_args args = new albumConsume_args();
        args.setVo(vo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ConsumeResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_albumConsume();
      }
    }

    public void fixBean(int appId, long consumeId, long timeStamp, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      fixBean_call method_call = new fixBean_call(appId, consumeId, timeStamp, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class fixBean_call extends org.apache.thrift.async.TAsyncMethodCall {
      private int appId;
      private long consumeId;
      private long timeStamp;
      public fixBean_call(int appId, long consumeId, long timeStamp, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.appId = appId;
        this.consumeId = consumeId;
        this.timeStamp = timeStamp;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("fixBean", org.apache.thrift.protocol.TMessageType.CALL, 0));
        fixBean_args args = new fixBean_args();
        args.setAppId(appId);
        args.setConsumeId(consumeId);
        args.setTimeStamp(timeStamp);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ConsumeResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_fixBean();
      }
    }

    public void storageConsume(StorageVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      storageConsume_call method_call = new storageConsume_call(vo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class storageConsume_call extends org.apache.thrift.async.TAsyncMethodCall {
      private StorageVO vo;
      public storageConsume_call(StorageVO vo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.vo = vo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("storageConsume", org.apache.thrift.protocol.TMessageType.CALL, 0));
        storageConsume_args args = new storageConsume_args();
        args.setVo(vo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ConsumeResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_storageConsume();
      }
    }

  }

  public static class Processor<I extends Iface> extends org.apache.thrift.TBaseProcessor<I> implements org.apache.thrift.TProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());
    public Processor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>>()));
    }

    protected Processor(I iface, Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends Iface> Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> getProcessMap(Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      processMap.put("consume", new consume());
      processMap.put("consumeStrictMoney", new consumeStrictMoney());
      processMap.put("mvConsume", new mvConsume());
      processMap.put("bean", new bean());
      processMap.put("rechargeFee", new rechargeFee());
      processMap.put("addStorageConsumeLog", new addStorageConsumeLog());
      processMap.put("getUserMoney", new getUserMoney());
      processMap.put("albumConsume", new albumConsume());
      processMap.put("fixBean", new fixBean());
      processMap.put("storageConsume", new storageConsume());
      return processMap;
    }

    public static class consume<I extends Iface> extends org.apache.thrift.ProcessFunction<I, consume_args> {
      public consume() {
        super("consume");
      }

      public consume_args getEmptyArgsInstance() {
        return new consume_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public consume_result getResult(I iface, consume_args args) throws org.apache.thrift.TException {
        consume_result result = new consume_result();
        result.success = iface.consume(args.vo);
        return result;
      }
    }

    public static class consumeStrictMoney<I extends Iface> extends org.apache.thrift.ProcessFunction<I, consumeStrictMoney_args> {
      public consumeStrictMoney() {
        super("consumeStrictMoney");
      }

      public consumeStrictMoney_args getEmptyArgsInstance() {
        return new consumeStrictMoney_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public consumeStrictMoney_result getResult(I iface, consumeStrictMoney_args args) throws org.apache.thrift.TException {
        consumeStrictMoney_result result = new consumeStrictMoney_result();
        result.success = iface.consumeStrictMoney(args.vo);
        return result;
      }
    }

    public static class mvConsume<I extends Iface> extends org.apache.thrift.ProcessFunction<I, mvConsume_args> {
      public mvConsume() {
        super("mvConsume");
      }

      public mvConsume_args getEmptyArgsInstance() {
        return new mvConsume_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public mvConsume_result getResult(I iface, mvConsume_args args) throws org.apache.thrift.TException {
        mvConsume_result result = new mvConsume_result();
        result.success = iface.mvConsume(args.vo);
        return result;
      }
    }

    public static class bean<I extends Iface> extends org.apache.thrift.ProcessFunction<I, bean_args> {
      public bean() {
        super("bean");
      }

      public bean_args getEmptyArgsInstance() {
        return new bean_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public bean_result getResult(I iface, bean_args args) throws org.apache.thrift.TException {
        bean_result result = new bean_result();
        result.success = iface.bean(args.vo);
        return result;
      }
    }

    public static class rechargeFee<I extends Iface> extends org.apache.thrift.ProcessFunction<I, rechargeFee_args> {
      public rechargeFee() {
        super("rechargeFee");
      }

      public rechargeFee_args getEmptyArgsInstance() {
        return new rechargeFee_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public rechargeFee_result getResult(I iface, rechargeFee_args args) throws org.apache.thrift.TException {
        rechargeFee_result result = new rechargeFee_result();
        result.success = iface.rechargeFee(args.vo);
        return result;
      }
    }

    public static class addStorageConsumeLog<I extends Iface> extends org.apache.thrift.ProcessFunction<I, addStorageConsumeLog_args> {
      public addStorageConsumeLog() {
        super("addStorageConsumeLog");
      }

      public addStorageConsumeLog_args getEmptyArgsInstance() {
        return new addStorageConsumeLog_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public addStorageConsumeLog_result getResult(I iface, addStorageConsumeLog_args args) throws org.apache.thrift.TException {
        addStorageConsumeLog_result result = new addStorageConsumeLog_result();
        result.success = iface.addStorageConsumeLog(args.vo);
        return result;
      }
    }

    public static class getUserMoney<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getUserMoney_args> {
      public getUserMoney() {
        super("getUserMoney");
      }

      public getUserMoney_args getEmptyArgsInstance() {
        return new getUserMoney_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getUserMoney_result getResult(I iface, getUserMoney_args args) throws org.apache.thrift.TException {
        getUserMoney_result result = new getUserMoney_result();
        result.success = iface.getUserMoney(args.kugouId);
        return result;
      }
    }

    public static class albumConsume<I extends Iface> extends org.apache.thrift.ProcessFunction<I, albumConsume_args> {
      public albumConsume() {
        super("albumConsume");
      }

      public albumConsume_args getEmptyArgsInstance() {
        return new albumConsume_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public albumConsume_result getResult(I iface, albumConsume_args args) throws org.apache.thrift.TException {
        albumConsume_result result = new albumConsume_result();
        result.success = iface.albumConsume(args.vo);
        return result;
      }
    }

    public static class fixBean<I extends Iface> extends org.apache.thrift.ProcessFunction<I, fixBean_args> {
      public fixBean() {
        super("fixBean");
      }

      public fixBean_args getEmptyArgsInstance() {
        return new fixBean_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public fixBean_result getResult(I iface, fixBean_args args) throws org.apache.thrift.TException {
        fixBean_result result = new fixBean_result();
        result.success = iface.fixBean(args.appId, args.consumeId, args.timeStamp);
        return result;
      }
    }

    public static class storageConsume<I extends Iface> extends org.apache.thrift.ProcessFunction<I, storageConsume_args> {
      public storageConsume() {
        super("storageConsume");
      }

      public storageConsume_args getEmptyArgsInstance() {
        return new storageConsume_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public storageConsume_result getResult(I iface, storageConsume_args args) throws org.apache.thrift.TException {
        storageConsume_result result = new storageConsume_result();
        result.success = iface.storageConsume(args.vo);
        return result;
      }
    }

  }

  public static class AsyncProcessor<I extends AsyncIface> extends org.apache.thrift.TBaseAsyncProcessor<I> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());
    public AsyncProcessor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.AsyncProcessFunction<I, ? extends org.apache.thrift.TBase, ?>>()));
    }

    protected AsyncProcessor(I iface, Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends AsyncIface> Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase,?>> getProcessMap(Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      processMap.put("consume", new consume());
      processMap.put("consumeStrictMoney", new consumeStrictMoney());
      processMap.put("mvConsume", new mvConsume());
      processMap.put("bean", new bean());
      processMap.put("rechargeFee", new rechargeFee());
      processMap.put("addStorageConsumeLog", new addStorageConsumeLog());
      processMap.put("getUserMoney", new getUserMoney());
      processMap.put("albumConsume", new albumConsume());
      processMap.put("fixBean", new fixBean());
      processMap.put("storageConsume", new storageConsume());
      return processMap;
    }

    public static class consume<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, consume_args, ConsumeResp> {
      public consume() {
        super("consume");
      }

      public consume_args getEmptyArgsInstance() {
        return new consume_args();
      }

      public AsyncMethodCallback<ConsumeResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ConsumeResp>() { 
          public void onComplete(ConsumeResp o) {
            consume_result result = new consume_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            consume_result result = new consume_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, consume_args args, org.apache.thrift.async.AsyncMethodCallback<ConsumeResp> resultHandler) throws TException {
        iface.consume(args.vo,resultHandler);
      }
    }

    public static class consumeStrictMoney<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, consumeStrictMoney_args, ConsumeResp> {
      public consumeStrictMoney() {
        super("consumeStrictMoney");
      }

      public consumeStrictMoney_args getEmptyArgsInstance() {
        return new consumeStrictMoney_args();
      }

      public AsyncMethodCallback<ConsumeResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ConsumeResp>() { 
          public void onComplete(ConsumeResp o) {
            consumeStrictMoney_result result = new consumeStrictMoney_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            consumeStrictMoney_result result = new consumeStrictMoney_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, consumeStrictMoney_args args, org.apache.thrift.async.AsyncMethodCallback<ConsumeResp> resultHandler) throws TException {
        iface.consumeStrictMoney(args.vo,resultHandler);
      }
    }

    public static class mvConsume<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, mvConsume_args, ConsumeResp> {
      public mvConsume() {
        super("mvConsume");
      }

      public mvConsume_args getEmptyArgsInstance() {
        return new mvConsume_args();
      }

      public AsyncMethodCallback<ConsumeResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ConsumeResp>() { 
          public void onComplete(ConsumeResp o) {
            mvConsume_result result = new mvConsume_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            mvConsume_result result = new mvConsume_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, mvConsume_args args, org.apache.thrift.async.AsyncMethodCallback<ConsumeResp> resultHandler) throws TException {
        iface.mvConsume(args.vo,resultHandler);
      }
    }

    public static class bean<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, bean_args, ConsumeResp> {
      public bean() {
        super("bean");
      }

      public bean_args getEmptyArgsInstance() {
        return new bean_args();
      }

      public AsyncMethodCallback<ConsumeResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ConsumeResp>() { 
          public void onComplete(ConsumeResp o) {
            bean_result result = new bean_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            bean_result result = new bean_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, bean_args args, org.apache.thrift.async.AsyncMethodCallback<ConsumeResp> resultHandler) throws TException {
        iface.bean(args.vo,resultHandler);
      }
    }

    public static class rechargeFee<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, rechargeFee_args, ConsumeResp> {
      public rechargeFee() {
        super("rechargeFee");
      }

      public rechargeFee_args getEmptyArgsInstance() {
        return new rechargeFee_args();
      }

      public AsyncMethodCallback<ConsumeResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ConsumeResp>() { 
          public void onComplete(ConsumeResp o) {
            rechargeFee_result result = new rechargeFee_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            rechargeFee_result result = new rechargeFee_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, rechargeFee_args args, org.apache.thrift.async.AsyncMethodCallback<ConsumeResp> resultHandler) throws TException {
        iface.rechargeFee(args.vo,resultHandler);
      }
    }

    public static class addStorageConsumeLog<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, addStorageConsumeLog_args, ConsumeResp> {
      public addStorageConsumeLog() {
        super("addStorageConsumeLog");
      }

      public addStorageConsumeLog_args getEmptyArgsInstance() {
        return new addStorageConsumeLog_args();
      }

      public AsyncMethodCallback<ConsumeResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ConsumeResp>() { 
          public void onComplete(ConsumeResp o) {
            addStorageConsumeLog_result result = new addStorageConsumeLog_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            addStorageConsumeLog_result result = new addStorageConsumeLog_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, addStorageConsumeLog_args args, org.apache.thrift.async.AsyncMethodCallback<ConsumeResp> resultHandler) throws TException {
        iface.addStorageConsumeLog(args.vo,resultHandler);
      }
    }

    public static class getUserMoney<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getUserMoney_args, ConsumeResp> {
      public getUserMoney() {
        super("getUserMoney");
      }

      public getUserMoney_args getEmptyArgsInstance() {
        return new getUserMoney_args();
      }

      public AsyncMethodCallback<ConsumeResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ConsumeResp>() { 
          public void onComplete(ConsumeResp o) {
            getUserMoney_result result = new getUserMoney_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getUserMoney_result result = new getUserMoney_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getUserMoney_args args, org.apache.thrift.async.AsyncMethodCallback<ConsumeResp> resultHandler) throws TException {
        iface.getUserMoney(args.kugouId,resultHandler);
      }
    }

    public static class albumConsume<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, albumConsume_args, ConsumeResp> {
      public albumConsume() {
        super("albumConsume");
      }

      public albumConsume_args getEmptyArgsInstance() {
        return new albumConsume_args();
      }

      public AsyncMethodCallback<ConsumeResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ConsumeResp>() { 
          public void onComplete(ConsumeResp o) {
            albumConsume_result result = new albumConsume_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            albumConsume_result result = new albumConsume_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, albumConsume_args args, org.apache.thrift.async.AsyncMethodCallback<ConsumeResp> resultHandler) throws TException {
        iface.albumConsume(args.vo,resultHandler);
      }
    }

    public static class fixBean<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, fixBean_args, ConsumeResp> {
      public fixBean() {
        super("fixBean");
      }

      public fixBean_args getEmptyArgsInstance() {
        return new fixBean_args();
      }

      public AsyncMethodCallback<ConsumeResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ConsumeResp>() { 
          public void onComplete(ConsumeResp o) {
            fixBean_result result = new fixBean_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            fixBean_result result = new fixBean_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, fixBean_args args, org.apache.thrift.async.AsyncMethodCallback<ConsumeResp> resultHandler) throws TException {
        iface.fixBean(args.appId, args.consumeId, args.timeStamp,resultHandler);
      }
    }

    public static class storageConsume<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, storageConsume_args, ConsumeResp> {
      public storageConsume() {
        super("storageConsume");
      }

      public storageConsume_args getEmptyArgsInstance() {
        return new storageConsume_args();
      }

      public AsyncMethodCallback<ConsumeResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ConsumeResp>() { 
          public void onComplete(ConsumeResp o) {
            storageConsume_result result = new storageConsume_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            storageConsume_result result = new storageConsume_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, storageConsume_args args, org.apache.thrift.async.AsyncMethodCallback<ConsumeResp> resultHandler) throws TException {
        iface.storageConsume(args.vo,resultHandler);
      }
    }

  }

  public static class consume_args implements org.apache.thrift.TBase<consume_args, consume_args._Fields>, java.io.Serializable, Cloneable, Comparable<consume_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("consume_args");

    private static final org.apache.thrift.protocol.TField VO_FIELD_DESC = new org.apache.thrift.protocol.TField("vo", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new consume_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new consume_argsTupleSchemeFactory());
    }

    public ConsumeVO vo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      VO((short)1, "vo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // VO
            return VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.VO, new org.apache.thrift.meta_data.FieldMetaData("vo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(consume_args.class, metaDataMap);
    }

    public consume_args() {
    }

    public consume_args(
      ConsumeVO vo)
    {
      this();
      this.vo = vo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public consume_args(consume_args other) {
      if (other.isSetVo()) {
        this.vo = new ConsumeVO(other.vo);
      }
    }

    public consume_args deepCopy() {
      return new consume_args(this);
    }

    @Override
    public void clear() {
      this.vo = null;
    }

    public ConsumeVO getVo() {
      return this.vo;
    }

    public consume_args setVo(ConsumeVO vo) {
      this.vo = vo;
      return this;
    }

    public void unsetVo() {
      this.vo = null;
    }

    /** Returns true if field vo is set (has been assigned a value) and false otherwise */
    public boolean isSetVo() {
      return this.vo != null;
    }

    public void setVoIsSet(boolean value) {
      if (!value) {
        this.vo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case VO:
        if (value == null) {
          unsetVo();
        } else {
          setVo((ConsumeVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case VO:
        return getVo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case VO:
        return isSetVo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof consume_args)
        return this.equals((consume_args)that);
      return false;
    }

    public boolean equals(consume_args that) {
      if (that == null)
        return false;

      boolean this_present_vo = true && this.isSetVo();
      boolean that_present_vo = true && that.isSetVo();
      if (this_present_vo || that_present_vo) {
        if (!(this_present_vo && that_present_vo))
          return false;
        if (!this.vo.equals(that.vo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_vo = true && (isSetVo());
      list.add(present_vo);
      if (present_vo)
        list.add(vo);

      return list.hashCode();
    }

    @Override
    public int compareTo(consume_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetVo()).compareTo(other.isSetVo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetVo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.vo, other.vo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("consume_args(");
      boolean first = true;

      sb.append("vo:");
      if (this.vo == null) {
        sb.append("null");
      } else {
        sb.append(this.vo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (vo != null) {
        vo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class consume_argsStandardSchemeFactory implements SchemeFactory {
      public consume_argsStandardScheme getScheme() {
        return new consume_argsStandardScheme();
      }
    }

    private static class consume_argsStandardScheme extends StandardScheme<consume_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, consume_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.vo = new ConsumeVO();
                struct.vo.read(iprot);
                struct.setVoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, consume_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.vo != null) {
          oprot.writeFieldBegin(VO_FIELD_DESC);
          struct.vo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class consume_argsTupleSchemeFactory implements SchemeFactory {
      public consume_argsTupleScheme getScheme() {
        return new consume_argsTupleScheme();
      }
    }

    private static class consume_argsTupleScheme extends TupleScheme<consume_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, consume_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetVo()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetVo()) {
          struct.vo.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, consume_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.vo = new ConsumeVO();
          struct.vo.read(iprot);
          struct.setVoIsSet(true);
        }
      }
    }

  }

  public static class consume_result implements org.apache.thrift.TBase<consume_result, consume_result._Fields>, java.io.Serializable, Cloneable, Comparable<consume_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("consume_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new consume_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new consume_resultTupleSchemeFactory());
    }

    public ConsumeResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(consume_result.class, metaDataMap);
    }

    public consume_result() {
    }

    public consume_result(
      ConsumeResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public consume_result(consume_result other) {
      if (other.isSetSuccess()) {
        this.success = new ConsumeResp(other.success);
      }
    }

    public consume_result deepCopy() {
      return new consume_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ConsumeResp getSuccess() {
      return this.success;
    }

    public consume_result setSuccess(ConsumeResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ConsumeResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof consume_result)
        return this.equals((consume_result)that);
      return false;
    }

    public boolean equals(consume_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(consume_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("consume_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class consume_resultStandardSchemeFactory implements SchemeFactory {
      public consume_resultStandardScheme getScheme() {
        return new consume_resultStandardScheme();
      }
    }

    private static class consume_resultStandardScheme extends StandardScheme<consume_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, consume_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ConsumeResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, consume_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class consume_resultTupleSchemeFactory implements SchemeFactory {
      public consume_resultTupleScheme getScheme() {
        return new consume_resultTupleScheme();
      }
    }

    private static class consume_resultTupleScheme extends TupleScheme<consume_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, consume_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, consume_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ConsumeResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class consumeStrictMoney_args implements org.apache.thrift.TBase<consumeStrictMoney_args, consumeStrictMoney_args._Fields>, java.io.Serializable, Cloneable, Comparable<consumeStrictMoney_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("consumeStrictMoney_args");

    private static final org.apache.thrift.protocol.TField VO_FIELD_DESC = new org.apache.thrift.protocol.TField("vo", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new consumeStrictMoney_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new consumeStrictMoney_argsTupleSchemeFactory());
    }

    public ConsumeVO vo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      VO((short)1, "vo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // VO
            return VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.VO, new org.apache.thrift.meta_data.FieldMetaData("vo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(consumeStrictMoney_args.class, metaDataMap);
    }

    public consumeStrictMoney_args() {
    }

    public consumeStrictMoney_args(
      ConsumeVO vo)
    {
      this();
      this.vo = vo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public consumeStrictMoney_args(consumeStrictMoney_args other) {
      if (other.isSetVo()) {
        this.vo = new ConsumeVO(other.vo);
      }
    }

    public consumeStrictMoney_args deepCopy() {
      return new consumeStrictMoney_args(this);
    }

    @Override
    public void clear() {
      this.vo = null;
    }

    public ConsumeVO getVo() {
      return this.vo;
    }

    public consumeStrictMoney_args setVo(ConsumeVO vo) {
      this.vo = vo;
      return this;
    }

    public void unsetVo() {
      this.vo = null;
    }

    /** Returns true if field vo is set (has been assigned a value) and false otherwise */
    public boolean isSetVo() {
      return this.vo != null;
    }

    public void setVoIsSet(boolean value) {
      if (!value) {
        this.vo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case VO:
        if (value == null) {
          unsetVo();
        } else {
          setVo((ConsumeVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case VO:
        return getVo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case VO:
        return isSetVo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof consumeStrictMoney_args)
        return this.equals((consumeStrictMoney_args)that);
      return false;
    }

    public boolean equals(consumeStrictMoney_args that) {
      if (that == null)
        return false;

      boolean this_present_vo = true && this.isSetVo();
      boolean that_present_vo = true && that.isSetVo();
      if (this_present_vo || that_present_vo) {
        if (!(this_present_vo && that_present_vo))
          return false;
        if (!this.vo.equals(that.vo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_vo = true && (isSetVo());
      list.add(present_vo);
      if (present_vo)
        list.add(vo);

      return list.hashCode();
    }

    @Override
    public int compareTo(consumeStrictMoney_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetVo()).compareTo(other.isSetVo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetVo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.vo, other.vo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("consumeStrictMoney_args(");
      boolean first = true;

      sb.append("vo:");
      if (this.vo == null) {
        sb.append("null");
      } else {
        sb.append(this.vo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (vo != null) {
        vo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class consumeStrictMoney_argsStandardSchemeFactory implements SchemeFactory {
      public consumeStrictMoney_argsStandardScheme getScheme() {
        return new consumeStrictMoney_argsStandardScheme();
      }
    }

    private static class consumeStrictMoney_argsStandardScheme extends StandardScheme<consumeStrictMoney_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, consumeStrictMoney_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.vo = new ConsumeVO();
                struct.vo.read(iprot);
                struct.setVoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, consumeStrictMoney_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.vo != null) {
          oprot.writeFieldBegin(VO_FIELD_DESC);
          struct.vo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class consumeStrictMoney_argsTupleSchemeFactory implements SchemeFactory {
      public consumeStrictMoney_argsTupleScheme getScheme() {
        return new consumeStrictMoney_argsTupleScheme();
      }
    }

    private static class consumeStrictMoney_argsTupleScheme extends TupleScheme<consumeStrictMoney_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, consumeStrictMoney_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetVo()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetVo()) {
          struct.vo.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, consumeStrictMoney_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.vo = new ConsumeVO();
          struct.vo.read(iprot);
          struct.setVoIsSet(true);
        }
      }
    }

  }

  public static class consumeStrictMoney_result implements org.apache.thrift.TBase<consumeStrictMoney_result, consumeStrictMoney_result._Fields>, java.io.Serializable, Cloneable, Comparable<consumeStrictMoney_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("consumeStrictMoney_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new consumeStrictMoney_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new consumeStrictMoney_resultTupleSchemeFactory());
    }

    public ConsumeResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(consumeStrictMoney_result.class, metaDataMap);
    }

    public consumeStrictMoney_result() {
    }

    public consumeStrictMoney_result(
      ConsumeResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public consumeStrictMoney_result(consumeStrictMoney_result other) {
      if (other.isSetSuccess()) {
        this.success = new ConsumeResp(other.success);
      }
    }

    public consumeStrictMoney_result deepCopy() {
      return new consumeStrictMoney_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ConsumeResp getSuccess() {
      return this.success;
    }

    public consumeStrictMoney_result setSuccess(ConsumeResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ConsumeResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof consumeStrictMoney_result)
        return this.equals((consumeStrictMoney_result)that);
      return false;
    }

    public boolean equals(consumeStrictMoney_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(consumeStrictMoney_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("consumeStrictMoney_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class consumeStrictMoney_resultStandardSchemeFactory implements SchemeFactory {
      public consumeStrictMoney_resultStandardScheme getScheme() {
        return new consumeStrictMoney_resultStandardScheme();
      }
    }

    private static class consumeStrictMoney_resultStandardScheme extends StandardScheme<consumeStrictMoney_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, consumeStrictMoney_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ConsumeResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, consumeStrictMoney_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class consumeStrictMoney_resultTupleSchemeFactory implements SchemeFactory {
      public consumeStrictMoney_resultTupleScheme getScheme() {
        return new consumeStrictMoney_resultTupleScheme();
      }
    }

    private static class consumeStrictMoney_resultTupleScheme extends TupleScheme<consumeStrictMoney_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, consumeStrictMoney_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, consumeStrictMoney_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ConsumeResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class mvConsume_args implements org.apache.thrift.TBase<mvConsume_args, mvConsume_args._Fields>, java.io.Serializable, Cloneable, Comparable<mvConsume_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("mvConsume_args");

    private static final org.apache.thrift.protocol.TField VO_FIELD_DESC = new org.apache.thrift.protocol.TField("vo", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new mvConsume_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new mvConsume_argsTupleSchemeFactory());
    }

    public ConsumeVO vo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      VO((short)1, "vo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // VO
            return VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.VO, new org.apache.thrift.meta_data.FieldMetaData("vo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(mvConsume_args.class, metaDataMap);
    }

    public mvConsume_args() {
    }

    public mvConsume_args(
      ConsumeVO vo)
    {
      this();
      this.vo = vo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public mvConsume_args(mvConsume_args other) {
      if (other.isSetVo()) {
        this.vo = new ConsumeVO(other.vo);
      }
    }

    public mvConsume_args deepCopy() {
      return new mvConsume_args(this);
    }

    @Override
    public void clear() {
      this.vo = null;
    }

    public ConsumeVO getVo() {
      return this.vo;
    }

    public mvConsume_args setVo(ConsumeVO vo) {
      this.vo = vo;
      return this;
    }

    public void unsetVo() {
      this.vo = null;
    }

    /** Returns true if field vo is set (has been assigned a value) and false otherwise */
    public boolean isSetVo() {
      return this.vo != null;
    }

    public void setVoIsSet(boolean value) {
      if (!value) {
        this.vo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case VO:
        if (value == null) {
          unsetVo();
        } else {
          setVo((ConsumeVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case VO:
        return getVo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case VO:
        return isSetVo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof mvConsume_args)
        return this.equals((mvConsume_args)that);
      return false;
    }

    public boolean equals(mvConsume_args that) {
      if (that == null)
        return false;

      boolean this_present_vo = true && this.isSetVo();
      boolean that_present_vo = true && that.isSetVo();
      if (this_present_vo || that_present_vo) {
        if (!(this_present_vo && that_present_vo))
          return false;
        if (!this.vo.equals(that.vo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_vo = true && (isSetVo());
      list.add(present_vo);
      if (present_vo)
        list.add(vo);

      return list.hashCode();
    }

    @Override
    public int compareTo(mvConsume_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetVo()).compareTo(other.isSetVo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetVo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.vo, other.vo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("mvConsume_args(");
      boolean first = true;

      sb.append("vo:");
      if (this.vo == null) {
        sb.append("null");
      } else {
        sb.append(this.vo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (vo != null) {
        vo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class mvConsume_argsStandardSchemeFactory implements SchemeFactory {
      public mvConsume_argsStandardScheme getScheme() {
        return new mvConsume_argsStandardScheme();
      }
    }

    private static class mvConsume_argsStandardScheme extends StandardScheme<mvConsume_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, mvConsume_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.vo = new ConsumeVO();
                struct.vo.read(iprot);
                struct.setVoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, mvConsume_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.vo != null) {
          oprot.writeFieldBegin(VO_FIELD_DESC);
          struct.vo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class mvConsume_argsTupleSchemeFactory implements SchemeFactory {
      public mvConsume_argsTupleScheme getScheme() {
        return new mvConsume_argsTupleScheme();
      }
    }

    private static class mvConsume_argsTupleScheme extends TupleScheme<mvConsume_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, mvConsume_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetVo()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetVo()) {
          struct.vo.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, mvConsume_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.vo = new ConsumeVO();
          struct.vo.read(iprot);
          struct.setVoIsSet(true);
        }
      }
    }

  }

  public static class mvConsume_result implements org.apache.thrift.TBase<mvConsume_result, mvConsume_result._Fields>, java.io.Serializable, Cloneable, Comparable<mvConsume_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("mvConsume_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new mvConsume_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new mvConsume_resultTupleSchemeFactory());
    }

    public ConsumeResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(mvConsume_result.class, metaDataMap);
    }

    public mvConsume_result() {
    }

    public mvConsume_result(
      ConsumeResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public mvConsume_result(mvConsume_result other) {
      if (other.isSetSuccess()) {
        this.success = new ConsumeResp(other.success);
      }
    }

    public mvConsume_result deepCopy() {
      return new mvConsume_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ConsumeResp getSuccess() {
      return this.success;
    }

    public mvConsume_result setSuccess(ConsumeResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ConsumeResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof mvConsume_result)
        return this.equals((mvConsume_result)that);
      return false;
    }

    public boolean equals(mvConsume_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(mvConsume_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("mvConsume_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class mvConsume_resultStandardSchemeFactory implements SchemeFactory {
      public mvConsume_resultStandardScheme getScheme() {
        return new mvConsume_resultStandardScheme();
      }
    }

    private static class mvConsume_resultStandardScheme extends StandardScheme<mvConsume_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, mvConsume_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ConsumeResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, mvConsume_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class mvConsume_resultTupleSchemeFactory implements SchemeFactory {
      public mvConsume_resultTupleScheme getScheme() {
        return new mvConsume_resultTupleScheme();
      }
    }

    private static class mvConsume_resultTupleScheme extends TupleScheme<mvConsume_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, mvConsume_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, mvConsume_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ConsumeResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class bean_args implements org.apache.thrift.TBase<bean_args, bean_args._Fields>, java.io.Serializable, Cloneable, Comparable<bean_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("bean_args");

    private static final org.apache.thrift.protocol.TField VO_FIELD_DESC = new org.apache.thrift.protocol.TField("vo", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new bean_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new bean_argsTupleSchemeFactory());
    }

    public BeanVO vo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      VO((short)1, "vo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // VO
            return VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.VO, new org.apache.thrift.meta_data.FieldMetaData("vo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, BeanVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(bean_args.class, metaDataMap);
    }

    public bean_args() {
    }

    public bean_args(
      BeanVO vo)
    {
      this();
      this.vo = vo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public bean_args(bean_args other) {
      if (other.isSetVo()) {
        this.vo = new BeanVO(other.vo);
      }
    }

    public bean_args deepCopy() {
      return new bean_args(this);
    }

    @Override
    public void clear() {
      this.vo = null;
    }

    public BeanVO getVo() {
      return this.vo;
    }

    public bean_args setVo(BeanVO vo) {
      this.vo = vo;
      return this;
    }

    public void unsetVo() {
      this.vo = null;
    }

    /** Returns true if field vo is set (has been assigned a value) and false otherwise */
    public boolean isSetVo() {
      return this.vo != null;
    }

    public void setVoIsSet(boolean value) {
      if (!value) {
        this.vo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case VO:
        if (value == null) {
          unsetVo();
        } else {
          setVo((BeanVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case VO:
        return getVo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case VO:
        return isSetVo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof bean_args)
        return this.equals((bean_args)that);
      return false;
    }

    public boolean equals(bean_args that) {
      if (that == null)
        return false;

      boolean this_present_vo = true && this.isSetVo();
      boolean that_present_vo = true && that.isSetVo();
      if (this_present_vo || that_present_vo) {
        if (!(this_present_vo && that_present_vo))
          return false;
        if (!this.vo.equals(that.vo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_vo = true && (isSetVo());
      list.add(present_vo);
      if (present_vo)
        list.add(vo);

      return list.hashCode();
    }

    @Override
    public int compareTo(bean_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetVo()).compareTo(other.isSetVo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetVo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.vo, other.vo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("bean_args(");
      boolean first = true;

      sb.append("vo:");
      if (this.vo == null) {
        sb.append("null");
      } else {
        sb.append(this.vo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (vo != null) {
        vo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class bean_argsStandardSchemeFactory implements SchemeFactory {
      public bean_argsStandardScheme getScheme() {
        return new bean_argsStandardScheme();
      }
    }

    private static class bean_argsStandardScheme extends StandardScheme<bean_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, bean_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.vo = new BeanVO();
                struct.vo.read(iprot);
                struct.setVoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, bean_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.vo != null) {
          oprot.writeFieldBegin(VO_FIELD_DESC);
          struct.vo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class bean_argsTupleSchemeFactory implements SchemeFactory {
      public bean_argsTupleScheme getScheme() {
        return new bean_argsTupleScheme();
      }
    }

    private static class bean_argsTupleScheme extends TupleScheme<bean_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, bean_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetVo()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetVo()) {
          struct.vo.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, bean_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.vo = new BeanVO();
          struct.vo.read(iprot);
          struct.setVoIsSet(true);
        }
      }
    }

  }

  public static class bean_result implements org.apache.thrift.TBase<bean_result, bean_result._Fields>, java.io.Serializable, Cloneable, Comparable<bean_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("bean_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new bean_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new bean_resultTupleSchemeFactory());
    }

    public ConsumeResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(bean_result.class, metaDataMap);
    }

    public bean_result() {
    }

    public bean_result(
      ConsumeResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public bean_result(bean_result other) {
      if (other.isSetSuccess()) {
        this.success = new ConsumeResp(other.success);
      }
    }

    public bean_result deepCopy() {
      return new bean_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ConsumeResp getSuccess() {
      return this.success;
    }

    public bean_result setSuccess(ConsumeResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ConsumeResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof bean_result)
        return this.equals((bean_result)that);
      return false;
    }

    public boolean equals(bean_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(bean_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("bean_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class bean_resultStandardSchemeFactory implements SchemeFactory {
      public bean_resultStandardScheme getScheme() {
        return new bean_resultStandardScheme();
      }
    }

    private static class bean_resultStandardScheme extends StandardScheme<bean_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, bean_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ConsumeResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, bean_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class bean_resultTupleSchemeFactory implements SchemeFactory {
      public bean_resultTupleScheme getScheme() {
        return new bean_resultTupleScheme();
      }
    }

    private static class bean_resultTupleScheme extends TupleScheme<bean_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, bean_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, bean_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ConsumeResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class rechargeFee_args implements org.apache.thrift.TBase<rechargeFee_args, rechargeFee_args._Fields>, java.io.Serializable, Cloneable, Comparable<rechargeFee_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("rechargeFee_args");

    private static final org.apache.thrift.protocol.TField VO_FIELD_DESC = new org.apache.thrift.protocol.TField("vo", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new rechargeFee_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new rechargeFee_argsTupleSchemeFactory());
    }

    public RechargeFeeVO vo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      VO((short)1, "vo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // VO
            return VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.VO, new org.apache.thrift.meta_data.FieldMetaData("vo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, RechargeFeeVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(rechargeFee_args.class, metaDataMap);
    }

    public rechargeFee_args() {
    }

    public rechargeFee_args(
      RechargeFeeVO vo)
    {
      this();
      this.vo = vo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public rechargeFee_args(rechargeFee_args other) {
      if (other.isSetVo()) {
        this.vo = new RechargeFeeVO(other.vo);
      }
    }

    public rechargeFee_args deepCopy() {
      return new rechargeFee_args(this);
    }

    @Override
    public void clear() {
      this.vo = null;
    }

    public RechargeFeeVO getVo() {
      return this.vo;
    }

    public rechargeFee_args setVo(RechargeFeeVO vo) {
      this.vo = vo;
      return this;
    }

    public void unsetVo() {
      this.vo = null;
    }

    /** Returns true if field vo is set (has been assigned a value) and false otherwise */
    public boolean isSetVo() {
      return this.vo != null;
    }

    public void setVoIsSet(boolean value) {
      if (!value) {
        this.vo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case VO:
        if (value == null) {
          unsetVo();
        } else {
          setVo((RechargeFeeVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case VO:
        return getVo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case VO:
        return isSetVo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof rechargeFee_args)
        return this.equals((rechargeFee_args)that);
      return false;
    }

    public boolean equals(rechargeFee_args that) {
      if (that == null)
        return false;

      boolean this_present_vo = true && this.isSetVo();
      boolean that_present_vo = true && that.isSetVo();
      if (this_present_vo || that_present_vo) {
        if (!(this_present_vo && that_present_vo))
          return false;
        if (!this.vo.equals(that.vo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_vo = true && (isSetVo());
      list.add(present_vo);
      if (present_vo)
        list.add(vo);

      return list.hashCode();
    }

    @Override
    public int compareTo(rechargeFee_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetVo()).compareTo(other.isSetVo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetVo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.vo, other.vo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("rechargeFee_args(");
      boolean first = true;

      sb.append("vo:");
      if (this.vo == null) {
        sb.append("null");
      } else {
        sb.append(this.vo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (vo != null) {
        vo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class rechargeFee_argsStandardSchemeFactory implements SchemeFactory {
      public rechargeFee_argsStandardScheme getScheme() {
        return new rechargeFee_argsStandardScheme();
      }
    }

    private static class rechargeFee_argsStandardScheme extends StandardScheme<rechargeFee_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, rechargeFee_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.vo = new RechargeFeeVO();
                struct.vo.read(iprot);
                struct.setVoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, rechargeFee_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.vo != null) {
          oprot.writeFieldBegin(VO_FIELD_DESC);
          struct.vo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class rechargeFee_argsTupleSchemeFactory implements SchemeFactory {
      public rechargeFee_argsTupleScheme getScheme() {
        return new rechargeFee_argsTupleScheme();
      }
    }

    private static class rechargeFee_argsTupleScheme extends TupleScheme<rechargeFee_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, rechargeFee_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetVo()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetVo()) {
          struct.vo.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, rechargeFee_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.vo = new RechargeFeeVO();
          struct.vo.read(iprot);
          struct.setVoIsSet(true);
        }
      }
    }

  }

  public static class rechargeFee_result implements org.apache.thrift.TBase<rechargeFee_result, rechargeFee_result._Fields>, java.io.Serializable, Cloneable, Comparable<rechargeFee_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("rechargeFee_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new rechargeFee_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new rechargeFee_resultTupleSchemeFactory());
    }

    public ConsumeResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(rechargeFee_result.class, metaDataMap);
    }

    public rechargeFee_result() {
    }

    public rechargeFee_result(
      ConsumeResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public rechargeFee_result(rechargeFee_result other) {
      if (other.isSetSuccess()) {
        this.success = new ConsumeResp(other.success);
      }
    }

    public rechargeFee_result deepCopy() {
      return new rechargeFee_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ConsumeResp getSuccess() {
      return this.success;
    }

    public rechargeFee_result setSuccess(ConsumeResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ConsumeResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof rechargeFee_result)
        return this.equals((rechargeFee_result)that);
      return false;
    }

    public boolean equals(rechargeFee_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(rechargeFee_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("rechargeFee_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class rechargeFee_resultStandardSchemeFactory implements SchemeFactory {
      public rechargeFee_resultStandardScheme getScheme() {
        return new rechargeFee_resultStandardScheme();
      }
    }

    private static class rechargeFee_resultStandardScheme extends StandardScheme<rechargeFee_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, rechargeFee_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ConsumeResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, rechargeFee_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class rechargeFee_resultTupleSchemeFactory implements SchemeFactory {
      public rechargeFee_resultTupleScheme getScheme() {
        return new rechargeFee_resultTupleScheme();
      }
    }

    private static class rechargeFee_resultTupleScheme extends TupleScheme<rechargeFee_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, rechargeFee_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, rechargeFee_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ConsumeResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class addStorageConsumeLog_args implements org.apache.thrift.TBase<addStorageConsumeLog_args, addStorageConsumeLog_args._Fields>, java.io.Serializable, Cloneable, Comparable<addStorageConsumeLog_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("addStorageConsumeLog_args");

    private static final org.apache.thrift.protocol.TField VO_FIELD_DESC = new org.apache.thrift.protocol.TField("vo", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new addStorageConsumeLog_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new addStorageConsumeLog_argsTupleSchemeFactory());
    }

    public StorageConsumeVO vo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      VO((short)1, "vo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // VO
            return VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.VO, new org.apache.thrift.meta_data.FieldMetaData("vo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, StorageConsumeVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(addStorageConsumeLog_args.class, metaDataMap);
    }

    public addStorageConsumeLog_args() {
    }

    public addStorageConsumeLog_args(
      StorageConsumeVO vo)
    {
      this();
      this.vo = vo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public addStorageConsumeLog_args(addStorageConsumeLog_args other) {
      if (other.isSetVo()) {
        this.vo = new StorageConsumeVO(other.vo);
      }
    }

    public addStorageConsumeLog_args deepCopy() {
      return new addStorageConsumeLog_args(this);
    }

    @Override
    public void clear() {
      this.vo = null;
    }

    public StorageConsumeVO getVo() {
      return this.vo;
    }

    public addStorageConsumeLog_args setVo(StorageConsumeVO vo) {
      this.vo = vo;
      return this;
    }

    public void unsetVo() {
      this.vo = null;
    }

    /** Returns true if field vo is set (has been assigned a value) and false otherwise */
    public boolean isSetVo() {
      return this.vo != null;
    }

    public void setVoIsSet(boolean value) {
      if (!value) {
        this.vo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case VO:
        if (value == null) {
          unsetVo();
        } else {
          setVo((StorageConsumeVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case VO:
        return getVo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case VO:
        return isSetVo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof addStorageConsumeLog_args)
        return this.equals((addStorageConsumeLog_args)that);
      return false;
    }

    public boolean equals(addStorageConsumeLog_args that) {
      if (that == null)
        return false;

      boolean this_present_vo = true && this.isSetVo();
      boolean that_present_vo = true && that.isSetVo();
      if (this_present_vo || that_present_vo) {
        if (!(this_present_vo && that_present_vo))
          return false;
        if (!this.vo.equals(that.vo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_vo = true && (isSetVo());
      list.add(present_vo);
      if (present_vo)
        list.add(vo);

      return list.hashCode();
    }

    @Override
    public int compareTo(addStorageConsumeLog_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetVo()).compareTo(other.isSetVo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetVo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.vo, other.vo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("addStorageConsumeLog_args(");
      boolean first = true;

      sb.append("vo:");
      if (this.vo == null) {
        sb.append("null");
      } else {
        sb.append(this.vo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (vo != null) {
        vo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class addStorageConsumeLog_argsStandardSchemeFactory implements SchemeFactory {
      public addStorageConsumeLog_argsStandardScheme getScheme() {
        return new addStorageConsumeLog_argsStandardScheme();
      }
    }

    private static class addStorageConsumeLog_argsStandardScheme extends StandardScheme<addStorageConsumeLog_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, addStorageConsumeLog_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.vo = new StorageConsumeVO();
                struct.vo.read(iprot);
                struct.setVoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, addStorageConsumeLog_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.vo != null) {
          oprot.writeFieldBegin(VO_FIELD_DESC);
          struct.vo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class addStorageConsumeLog_argsTupleSchemeFactory implements SchemeFactory {
      public addStorageConsumeLog_argsTupleScheme getScheme() {
        return new addStorageConsumeLog_argsTupleScheme();
      }
    }

    private static class addStorageConsumeLog_argsTupleScheme extends TupleScheme<addStorageConsumeLog_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, addStorageConsumeLog_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetVo()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetVo()) {
          struct.vo.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, addStorageConsumeLog_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.vo = new StorageConsumeVO();
          struct.vo.read(iprot);
          struct.setVoIsSet(true);
        }
      }
    }

  }

  public static class addStorageConsumeLog_result implements org.apache.thrift.TBase<addStorageConsumeLog_result, addStorageConsumeLog_result._Fields>, java.io.Serializable, Cloneable, Comparable<addStorageConsumeLog_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("addStorageConsumeLog_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new addStorageConsumeLog_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new addStorageConsumeLog_resultTupleSchemeFactory());
    }

    public ConsumeResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(addStorageConsumeLog_result.class, metaDataMap);
    }

    public addStorageConsumeLog_result() {
    }

    public addStorageConsumeLog_result(
      ConsumeResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public addStorageConsumeLog_result(addStorageConsumeLog_result other) {
      if (other.isSetSuccess()) {
        this.success = new ConsumeResp(other.success);
      }
    }

    public addStorageConsumeLog_result deepCopy() {
      return new addStorageConsumeLog_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ConsumeResp getSuccess() {
      return this.success;
    }

    public addStorageConsumeLog_result setSuccess(ConsumeResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ConsumeResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof addStorageConsumeLog_result)
        return this.equals((addStorageConsumeLog_result)that);
      return false;
    }

    public boolean equals(addStorageConsumeLog_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(addStorageConsumeLog_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("addStorageConsumeLog_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class addStorageConsumeLog_resultStandardSchemeFactory implements SchemeFactory {
      public addStorageConsumeLog_resultStandardScheme getScheme() {
        return new addStorageConsumeLog_resultStandardScheme();
      }
    }

    private static class addStorageConsumeLog_resultStandardScheme extends StandardScheme<addStorageConsumeLog_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, addStorageConsumeLog_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ConsumeResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, addStorageConsumeLog_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class addStorageConsumeLog_resultTupleSchemeFactory implements SchemeFactory {
      public addStorageConsumeLog_resultTupleScheme getScheme() {
        return new addStorageConsumeLog_resultTupleScheme();
      }
    }

    private static class addStorageConsumeLog_resultTupleScheme extends TupleScheme<addStorageConsumeLog_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, addStorageConsumeLog_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, addStorageConsumeLog_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ConsumeResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getUserMoney_args implements org.apache.thrift.TBase<getUserMoney_args, getUserMoney_args._Fields>, java.io.Serializable, Cloneable, Comparable<getUserMoney_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserMoney_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserMoney_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserMoney_argsTupleSchemeFactory());
    }

    public long kugouId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserMoney_args.class, metaDataMap);
    }

    public getUserMoney_args() {
    }

    public getUserMoney_args(
      long kugouId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserMoney_args(getUserMoney_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
    }

    public getUserMoney_args deepCopy() {
      return new getUserMoney_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public getUserMoney_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserMoney_args)
        return this.equals((getUserMoney_args)that);
      return false;
    }

    public boolean equals(getUserMoney_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserMoney_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserMoney_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserMoney_argsStandardSchemeFactory implements SchemeFactory {
      public getUserMoney_argsStandardScheme getScheme() {
        return new getUserMoney_argsStandardScheme();
      }
    }

    private static class getUserMoney_argsStandardScheme extends StandardScheme<getUserMoney_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserMoney_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        if (!struct.isSetKugouId()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
        }
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserMoney_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserMoney_argsTupleSchemeFactory implements SchemeFactory {
      public getUserMoney_argsTupleScheme getScheme() {
        return new getUserMoney_argsTupleScheme();
      }
    }

    private static class getUserMoney_argsTupleScheme extends TupleScheme<getUserMoney_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserMoney_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        oprot.writeI64(struct.kugouId);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserMoney_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.kugouId = iprot.readI64();
        struct.setKugouIdIsSet(true);
      }
    }

  }

  public static class getUserMoney_result implements org.apache.thrift.TBase<getUserMoney_result, getUserMoney_result._Fields>, java.io.Serializable, Cloneable, Comparable<getUserMoney_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getUserMoney_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getUserMoney_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getUserMoney_resultTupleSchemeFactory());
    }

    public ConsumeResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getUserMoney_result.class, metaDataMap);
    }

    public getUserMoney_result() {
    }

    public getUserMoney_result(
      ConsumeResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getUserMoney_result(getUserMoney_result other) {
      if (other.isSetSuccess()) {
        this.success = new ConsumeResp(other.success);
      }
    }

    public getUserMoney_result deepCopy() {
      return new getUserMoney_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ConsumeResp getSuccess() {
      return this.success;
    }

    public getUserMoney_result setSuccess(ConsumeResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ConsumeResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getUserMoney_result)
        return this.equals((getUserMoney_result)that);
      return false;
    }

    public boolean equals(getUserMoney_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getUserMoney_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getUserMoney_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getUserMoney_resultStandardSchemeFactory implements SchemeFactory {
      public getUserMoney_resultStandardScheme getScheme() {
        return new getUserMoney_resultStandardScheme();
      }
    }

    private static class getUserMoney_resultStandardScheme extends StandardScheme<getUserMoney_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getUserMoney_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ConsumeResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getUserMoney_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getUserMoney_resultTupleSchemeFactory implements SchemeFactory {
      public getUserMoney_resultTupleScheme getScheme() {
        return new getUserMoney_resultTupleScheme();
      }
    }

    private static class getUserMoney_resultTupleScheme extends TupleScheme<getUserMoney_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getUserMoney_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getUserMoney_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ConsumeResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class albumConsume_args implements org.apache.thrift.TBase<albumConsume_args, albumConsume_args._Fields>, java.io.Serializable, Cloneable, Comparable<albumConsume_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("albumConsume_args");

    private static final org.apache.thrift.protocol.TField VO_FIELD_DESC = new org.apache.thrift.protocol.TField("vo", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new albumConsume_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new albumConsume_argsTupleSchemeFactory());
    }

    public AlbumConsume vo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      VO((short)1, "vo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // VO
            return VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.VO, new org.apache.thrift.meta_data.FieldMetaData("vo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, AlbumConsume.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(albumConsume_args.class, metaDataMap);
    }

    public albumConsume_args() {
    }

    public albumConsume_args(
      AlbumConsume vo)
    {
      this();
      this.vo = vo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public albumConsume_args(albumConsume_args other) {
      if (other.isSetVo()) {
        this.vo = new AlbumConsume(other.vo);
      }
    }

    public albumConsume_args deepCopy() {
      return new albumConsume_args(this);
    }

    @Override
    public void clear() {
      this.vo = null;
    }

    public AlbumConsume getVo() {
      return this.vo;
    }

    public albumConsume_args setVo(AlbumConsume vo) {
      this.vo = vo;
      return this;
    }

    public void unsetVo() {
      this.vo = null;
    }

    /** Returns true if field vo is set (has been assigned a value) and false otherwise */
    public boolean isSetVo() {
      return this.vo != null;
    }

    public void setVoIsSet(boolean value) {
      if (!value) {
        this.vo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case VO:
        if (value == null) {
          unsetVo();
        } else {
          setVo((AlbumConsume)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case VO:
        return getVo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case VO:
        return isSetVo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof albumConsume_args)
        return this.equals((albumConsume_args)that);
      return false;
    }

    public boolean equals(albumConsume_args that) {
      if (that == null)
        return false;

      boolean this_present_vo = true && this.isSetVo();
      boolean that_present_vo = true && that.isSetVo();
      if (this_present_vo || that_present_vo) {
        if (!(this_present_vo && that_present_vo))
          return false;
        if (!this.vo.equals(that.vo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_vo = true && (isSetVo());
      list.add(present_vo);
      if (present_vo)
        list.add(vo);

      return list.hashCode();
    }

    @Override
    public int compareTo(albumConsume_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetVo()).compareTo(other.isSetVo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetVo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.vo, other.vo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("albumConsume_args(");
      boolean first = true;

      sb.append("vo:");
      if (this.vo == null) {
        sb.append("null");
      } else {
        sb.append(this.vo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (vo != null) {
        vo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class albumConsume_argsStandardSchemeFactory implements SchemeFactory {
      public albumConsume_argsStandardScheme getScheme() {
        return new albumConsume_argsStandardScheme();
      }
    }

    private static class albumConsume_argsStandardScheme extends StandardScheme<albumConsume_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, albumConsume_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.vo = new AlbumConsume();
                struct.vo.read(iprot);
                struct.setVoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, albumConsume_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.vo != null) {
          oprot.writeFieldBegin(VO_FIELD_DESC);
          struct.vo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class albumConsume_argsTupleSchemeFactory implements SchemeFactory {
      public albumConsume_argsTupleScheme getScheme() {
        return new albumConsume_argsTupleScheme();
      }
    }

    private static class albumConsume_argsTupleScheme extends TupleScheme<albumConsume_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, albumConsume_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetVo()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetVo()) {
          struct.vo.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, albumConsume_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.vo = new AlbumConsume();
          struct.vo.read(iprot);
          struct.setVoIsSet(true);
        }
      }
    }

  }

  public static class albumConsume_result implements org.apache.thrift.TBase<albumConsume_result, albumConsume_result._Fields>, java.io.Serializable, Cloneable, Comparable<albumConsume_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("albumConsume_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new albumConsume_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new albumConsume_resultTupleSchemeFactory());
    }

    public ConsumeResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(albumConsume_result.class, metaDataMap);
    }

    public albumConsume_result() {
    }

    public albumConsume_result(
      ConsumeResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public albumConsume_result(albumConsume_result other) {
      if (other.isSetSuccess()) {
        this.success = new ConsumeResp(other.success);
      }
    }

    public albumConsume_result deepCopy() {
      return new albumConsume_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ConsumeResp getSuccess() {
      return this.success;
    }

    public albumConsume_result setSuccess(ConsumeResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ConsumeResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof albumConsume_result)
        return this.equals((albumConsume_result)that);
      return false;
    }

    public boolean equals(albumConsume_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(albumConsume_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("albumConsume_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class albumConsume_resultStandardSchemeFactory implements SchemeFactory {
      public albumConsume_resultStandardScheme getScheme() {
        return new albumConsume_resultStandardScheme();
      }
    }

    private static class albumConsume_resultStandardScheme extends StandardScheme<albumConsume_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, albumConsume_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ConsumeResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, albumConsume_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class albumConsume_resultTupleSchemeFactory implements SchemeFactory {
      public albumConsume_resultTupleScheme getScheme() {
        return new albumConsume_resultTupleScheme();
      }
    }

    private static class albumConsume_resultTupleScheme extends TupleScheme<albumConsume_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, albumConsume_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, albumConsume_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ConsumeResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class fixBean_args implements org.apache.thrift.TBase<fixBean_args, fixBean_args._Fields>, java.io.Serializable, Cloneable, Comparable<fixBean_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("fixBean_args");

    private static final org.apache.thrift.protocol.TField APP_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("appId", org.apache.thrift.protocol.TType.I32, (short)1);
    private static final org.apache.thrift.protocol.TField CONSUME_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("consumeId", org.apache.thrift.protocol.TType.I64, (short)2);
    private static final org.apache.thrift.protocol.TField TIME_STAMP_FIELD_DESC = new org.apache.thrift.protocol.TField("timeStamp", org.apache.thrift.protocol.TType.I64, (short)3);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new fixBean_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new fixBean_argsTupleSchemeFactory());
    }

    public int appId; // required
    public long consumeId; // required
    public long timeStamp; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      APP_ID((short)1, "appId"),
      CONSUME_ID((short)2, "consumeId"),
      TIME_STAMP((short)3, "timeStamp");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // APP_ID
            return APP_ID;
          case 2: // CONSUME_ID
            return CONSUME_ID;
          case 3: // TIME_STAMP
            return TIME_STAMP;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __APPID_ISSET_ID = 0;
    private static final int __CONSUMEID_ISSET_ID = 1;
    private static final int __TIMESTAMP_ISSET_ID = 2;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.APP_ID, new org.apache.thrift.meta_data.FieldMetaData("appId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      tmpMap.put(_Fields.CONSUME_ID, new org.apache.thrift.meta_data.FieldMetaData("consumeId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.TIME_STAMP, new org.apache.thrift.meta_data.FieldMetaData("timeStamp", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(fixBean_args.class, metaDataMap);
    }

    public fixBean_args() {
    }

    public fixBean_args(
      int appId,
      long consumeId,
      long timeStamp)
    {
      this();
      this.appId = appId;
      setAppIdIsSet(true);
      this.consumeId = consumeId;
      setConsumeIdIsSet(true);
      this.timeStamp = timeStamp;
      setTimeStampIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public fixBean_args(fixBean_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.appId = other.appId;
      this.consumeId = other.consumeId;
      this.timeStamp = other.timeStamp;
    }

    public fixBean_args deepCopy() {
      return new fixBean_args(this);
    }

    @Override
    public void clear() {
      setAppIdIsSet(false);
      this.appId = 0;
      setConsumeIdIsSet(false);
      this.consumeId = 0;
      setTimeStampIsSet(false);
      this.timeStamp = 0;
    }

    public int getAppId() {
      return this.appId;
    }

    public fixBean_args setAppId(int appId) {
      this.appId = appId;
      setAppIdIsSet(true);
      return this;
    }

    public void unsetAppId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __APPID_ISSET_ID);
    }

    /** Returns true if field appId is set (has been assigned a value) and false otherwise */
    public boolean isSetAppId() {
      return EncodingUtils.testBit(__isset_bitfield, __APPID_ISSET_ID);
    }

    public void setAppIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __APPID_ISSET_ID, value);
    }

    public long getConsumeId() {
      return this.consumeId;
    }

    public fixBean_args setConsumeId(long consumeId) {
      this.consumeId = consumeId;
      setConsumeIdIsSet(true);
      return this;
    }

    public void unsetConsumeId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CONSUMEID_ISSET_ID);
    }

    /** Returns true if field consumeId is set (has been assigned a value) and false otherwise */
    public boolean isSetConsumeId() {
      return EncodingUtils.testBit(__isset_bitfield, __CONSUMEID_ISSET_ID);
    }

    public void setConsumeIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CONSUMEID_ISSET_ID, value);
    }

    public long getTimeStamp() {
      return this.timeStamp;
    }

    public fixBean_args setTimeStamp(long timeStamp) {
      this.timeStamp = timeStamp;
      setTimeStampIsSet(true);
      return this;
    }

    public void unsetTimeStamp() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
    }

    /** Returns true if field timeStamp is set (has been assigned a value) and false otherwise */
    public boolean isSetTimeStamp() {
      return EncodingUtils.testBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
    }

    public void setTimeStampIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TIMESTAMP_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case APP_ID:
        if (value == null) {
          unsetAppId();
        } else {
          setAppId((Integer)value);
        }
        break;

      case CONSUME_ID:
        if (value == null) {
          unsetConsumeId();
        } else {
          setConsumeId((Long)value);
        }
        break;

      case TIME_STAMP:
        if (value == null) {
          unsetTimeStamp();
        } else {
          setTimeStamp((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case APP_ID:
        return getAppId();

      case CONSUME_ID:
        return getConsumeId();

      case TIME_STAMP:
        return getTimeStamp();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case APP_ID:
        return isSetAppId();
      case CONSUME_ID:
        return isSetConsumeId();
      case TIME_STAMP:
        return isSetTimeStamp();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof fixBean_args)
        return this.equals((fixBean_args)that);
      return false;
    }

    public boolean equals(fixBean_args that) {
      if (that == null)
        return false;

      boolean this_present_appId = true;
      boolean that_present_appId = true;
      if (this_present_appId || that_present_appId) {
        if (!(this_present_appId && that_present_appId))
          return false;
        if (this.appId != that.appId)
          return false;
      }

      boolean this_present_consumeId = true;
      boolean that_present_consumeId = true;
      if (this_present_consumeId || that_present_consumeId) {
        if (!(this_present_consumeId && that_present_consumeId))
          return false;
        if (this.consumeId != that.consumeId)
          return false;
      }

      boolean this_present_timeStamp = true;
      boolean that_present_timeStamp = true;
      if (this_present_timeStamp || that_present_timeStamp) {
        if (!(this_present_timeStamp && that_present_timeStamp))
          return false;
        if (this.timeStamp != that.timeStamp)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_appId = true;
      list.add(present_appId);
      if (present_appId)
        list.add(appId);

      boolean present_consumeId = true;
      list.add(present_consumeId);
      if (present_consumeId)
        list.add(consumeId);

      boolean present_timeStamp = true;
      list.add(present_timeStamp);
      if (present_timeStamp)
        list.add(timeStamp);

      return list.hashCode();
    }

    @Override
    public int compareTo(fixBean_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetAppId()).compareTo(other.isSetAppId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetAppId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appId, other.appId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetConsumeId()).compareTo(other.isSetConsumeId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetConsumeId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.consumeId, other.consumeId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetTimeStamp()).compareTo(other.isSetTimeStamp());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetTimeStamp()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.timeStamp, other.timeStamp);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("fixBean_args(");
      boolean first = true;

      sb.append("appId:");
      sb.append(this.appId);
      first = false;
      if (!first) sb.append(", ");
      sb.append("consumeId:");
      sb.append(this.consumeId);
      first = false;
      if (!first) sb.append(", ");
      sb.append("timeStamp:");
      sb.append(this.timeStamp);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // alas, we cannot check 'appId' because it's a primitive and you chose the non-beans generator.
      // alas, we cannot check 'consumeId' because it's a primitive and you chose the non-beans generator.
      // alas, we cannot check 'timeStamp' because it's a primitive and you chose the non-beans generator.
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class fixBean_argsStandardSchemeFactory implements SchemeFactory {
      public fixBean_argsStandardScheme getScheme() {
        return new fixBean_argsStandardScheme();
      }
    }

    private static class fixBean_argsStandardScheme extends StandardScheme<fixBean_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, fixBean_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // APP_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.appId = iprot.readI32();
                struct.setAppIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // CONSUME_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.consumeId = iprot.readI64();
                struct.setConsumeIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // TIME_STAMP
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.timeStamp = iprot.readI64();
                struct.setTimeStampIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        if (!struct.isSetAppId()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'appId' was not found in serialized data! Struct: " + toString());
        }
        if (!struct.isSetConsumeId()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'consumeId' was not found in serialized data! Struct: " + toString());
        }
        if (!struct.isSetTimeStamp()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'timeStamp' was not found in serialized data! Struct: " + toString());
        }
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, fixBean_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(APP_ID_FIELD_DESC);
        oprot.writeI32(struct.appId);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(CONSUME_ID_FIELD_DESC);
        oprot.writeI64(struct.consumeId);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(TIME_STAMP_FIELD_DESC);
        oprot.writeI64(struct.timeStamp);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class fixBean_argsTupleSchemeFactory implements SchemeFactory {
      public fixBean_argsTupleScheme getScheme() {
        return new fixBean_argsTupleScheme();
      }
    }

    private static class fixBean_argsTupleScheme extends TupleScheme<fixBean_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, fixBean_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        oprot.writeI32(struct.appId);
        oprot.writeI64(struct.consumeId);
        oprot.writeI64(struct.timeStamp);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, fixBean_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.appId = iprot.readI32();
        struct.setAppIdIsSet(true);
        struct.consumeId = iprot.readI64();
        struct.setConsumeIdIsSet(true);
        struct.timeStamp = iprot.readI64();
        struct.setTimeStampIsSet(true);
      }
    }

  }

  public static class fixBean_result implements org.apache.thrift.TBase<fixBean_result, fixBean_result._Fields>, java.io.Serializable, Cloneable, Comparable<fixBean_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("fixBean_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new fixBean_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new fixBean_resultTupleSchemeFactory());
    }

    public ConsumeResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(fixBean_result.class, metaDataMap);
    }

    public fixBean_result() {
    }

    public fixBean_result(
      ConsumeResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public fixBean_result(fixBean_result other) {
      if (other.isSetSuccess()) {
        this.success = new ConsumeResp(other.success);
      }
    }

    public fixBean_result deepCopy() {
      return new fixBean_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ConsumeResp getSuccess() {
      return this.success;
    }

    public fixBean_result setSuccess(ConsumeResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ConsumeResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof fixBean_result)
        return this.equals((fixBean_result)that);
      return false;
    }

    public boolean equals(fixBean_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(fixBean_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("fixBean_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class fixBean_resultStandardSchemeFactory implements SchemeFactory {
      public fixBean_resultStandardScheme getScheme() {
        return new fixBean_resultStandardScheme();
      }
    }

    private static class fixBean_resultStandardScheme extends StandardScheme<fixBean_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, fixBean_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ConsumeResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, fixBean_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class fixBean_resultTupleSchemeFactory implements SchemeFactory {
      public fixBean_resultTupleScheme getScheme() {
        return new fixBean_resultTupleScheme();
      }
    }

    private static class fixBean_resultTupleScheme extends TupleScheme<fixBean_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, fixBean_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, fixBean_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ConsumeResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class storageConsume_args implements org.apache.thrift.TBase<storageConsume_args, storageConsume_args._Fields>, java.io.Serializable, Cloneable, Comparable<storageConsume_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("storageConsume_args");

    private static final org.apache.thrift.protocol.TField VO_FIELD_DESC = new org.apache.thrift.protocol.TField("vo", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new storageConsume_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new storageConsume_argsTupleSchemeFactory());
    }

    public StorageVO vo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      VO((short)1, "vo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // VO
            return VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.VO, new org.apache.thrift.meta_data.FieldMetaData("vo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, StorageVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(storageConsume_args.class, metaDataMap);
    }

    public storageConsume_args() {
    }

    public storageConsume_args(
      StorageVO vo)
    {
      this();
      this.vo = vo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public storageConsume_args(storageConsume_args other) {
      if (other.isSetVo()) {
        this.vo = new StorageVO(other.vo);
      }
    }

    public storageConsume_args deepCopy() {
      return new storageConsume_args(this);
    }

    @Override
    public void clear() {
      this.vo = null;
    }

    public StorageVO getVo() {
      return this.vo;
    }

    public storageConsume_args setVo(StorageVO vo) {
      this.vo = vo;
      return this;
    }

    public void unsetVo() {
      this.vo = null;
    }

    /** Returns true if field vo is set (has been assigned a value) and false otherwise */
    public boolean isSetVo() {
      return this.vo != null;
    }

    public void setVoIsSet(boolean value) {
      if (!value) {
        this.vo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case VO:
        if (value == null) {
          unsetVo();
        } else {
          setVo((StorageVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case VO:
        return getVo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case VO:
        return isSetVo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof storageConsume_args)
        return this.equals((storageConsume_args)that);
      return false;
    }

    public boolean equals(storageConsume_args that) {
      if (that == null)
        return false;

      boolean this_present_vo = true && this.isSetVo();
      boolean that_present_vo = true && that.isSetVo();
      if (this_present_vo || that_present_vo) {
        if (!(this_present_vo && that_present_vo))
          return false;
        if (!this.vo.equals(that.vo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_vo = true && (isSetVo());
      list.add(present_vo);
      if (present_vo)
        list.add(vo);

      return list.hashCode();
    }

    @Override
    public int compareTo(storageConsume_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetVo()).compareTo(other.isSetVo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetVo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.vo, other.vo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("storageConsume_args(");
      boolean first = true;

      sb.append("vo:");
      if (this.vo == null) {
        sb.append("null");
      } else {
        sb.append(this.vo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (vo != null) {
        vo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class storageConsume_argsStandardSchemeFactory implements SchemeFactory {
      public storageConsume_argsStandardScheme getScheme() {
        return new storageConsume_argsStandardScheme();
      }
    }

    private static class storageConsume_argsStandardScheme extends StandardScheme<storageConsume_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, storageConsume_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.vo = new StorageVO();
                struct.vo.read(iprot);
                struct.setVoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, storageConsume_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.vo != null) {
          oprot.writeFieldBegin(VO_FIELD_DESC);
          struct.vo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class storageConsume_argsTupleSchemeFactory implements SchemeFactory {
      public storageConsume_argsTupleScheme getScheme() {
        return new storageConsume_argsTupleScheme();
      }
    }

    private static class storageConsume_argsTupleScheme extends TupleScheme<storageConsume_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, storageConsume_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetVo()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetVo()) {
          struct.vo.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, storageConsume_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.vo = new StorageVO();
          struct.vo.read(iprot);
          struct.setVoIsSet(true);
        }
      }
    }

  }

  public static class storageConsume_result implements org.apache.thrift.TBase<storageConsume_result, storageConsume_result._Fields>, java.io.Serializable, Cloneable, Comparable<storageConsume_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("storageConsume_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new storageConsume_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new storageConsume_resultTupleSchemeFactory());
    }

    public ConsumeResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(storageConsume_result.class, metaDataMap);
    }

    public storageConsume_result() {
    }

    public storageConsume_result(
      ConsumeResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public storageConsume_result(storageConsume_result other) {
      if (other.isSetSuccess()) {
        this.success = new ConsumeResp(other.success);
      }
    }

    public storageConsume_result deepCopy() {
      return new storageConsume_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ConsumeResp getSuccess() {
      return this.success;
    }

    public storageConsume_result setSuccess(ConsumeResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ConsumeResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof storageConsume_result)
        return this.equals((storageConsume_result)that);
      return false;
    }

    public boolean equals(storageConsume_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(storageConsume_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("storageConsume_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class storageConsume_resultStandardSchemeFactory implements SchemeFactory {
      public storageConsume_resultStandardScheme getScheme() {
        return new storageConsume_resultStandardScheme();
      }
    }

    private static class storageConsume_resultStandardScheme extends StandardScheme<storageConsume_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, storageConsume_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ConsumeResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, storageConsume_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class storageConsume_resultTupleSchemeFactory implements SchemeFactory {
      public storageConsume_resultTupleScheme getScheme() {
        return new storageConsume_resultTupleScheme();
      }
    }

    private static class storageConsume_resultTupleScheme extends TupleScheme<storageConsume_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, storageConsume_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, storageConsume_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ConsumeResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

}
