/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.thrift.banaccount.service;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2020-12-30")
public class BanAccountService {

  public interface Iface {

    /**
     * *账号是否被封-**
     * 
     * @param kugouId
     */
    public com.kugou.fanxing.thrift.banaccount.dto.Result isBanAccount(long kugouId) throws org.apache.thrift.TException;

    /**
     * *封禁设置 SUC("成功", 0), FAIL("失败", 1), PARA_ERROR("参数错误", -1),; **
     * 
     * @param ban
     */
    public com.kugou.fanxing.thrift.banaccount.dto.OperateResult setBanAccount(com.kugou.fanxing.thrift.banaccount.dto.BanAccount ban) throws org.apache.thrift.TException;

    /**
     * *IP是否被封-**
     * 
     * @param ip
     */
    public com.kugou.fanxing.thrift.banaccount.dto.Result isBanIP(String ip) throws org.apache.thrift.TException;

    /**
     * *IP封禁设置  SUC("成功", 0), FAIL("失败", 1), PARA_ERROR("参数错误", -1),; **
     * 
     * @param ban
     */
    public com.kugou.fanxing.thrift.banaccount.dto.OperateResult setBanIP(com.kugou.fanxing.thrift.banaccount.dto.BanX ban) throws org.apache.thrift.TException;

    /**
     * *device是否被封-**
     * 
     * @param deviceNo
     */
    public com.kugou.fanxing.thrift.banaccount.dto.Result isBanDeviceNo(String deviceNo) throws org.apache.thrift.TException;

    /**
     * *device是否被封封禁设置 SUC("成功", 0), FAIL("失败", 1), PARA_ERROR("参数错误", -1),; **
     * 
     * @param ban
     */
    public com.kugou.fanxing.thrift.banaccount.dto.OperateResult setBanDeviceNo(com.kugou.fanxing.thrift.banaccount.dto.BanX ban) throws org.apache.thrift.TException;

    public com.kugou.fanxing.thrift.banaccount.dto.Result isBanAccountOrIp(long kugouId, String ip) throws org.apache.thrift.TException;

    public com.kugou.fanxing.thrift.banaccount.dto.Result isBanAccountOrDeviceNo(long kugouId, String deviceNo) throws org.apache.thrift.TException;

    /**
     * *批量查询账号封禁信息,最大size为100; code0为成功,其他为失败**
     * 
     * @param kugouIdList
     */
    public com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp listBanAccountInfo(List<Long> kugouIdList) throws org.apache.thrift.TException;

  }

  public interface AsyncIface {

    public void isBanAccount(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void setBanAccount(com.kugou.fanxing.thrift.banaccount.dto.BanAccount ban, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void isBanIP(String ip, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void setBanIP(com.kugou.fanxing.thrift.banaccount.dto.BanX ban, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void isBanDeviceNo(String deviceNo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void setBanDeviceNo(com.kugou.fanxing.thrift.banaccount.dto.BanX ban, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void isBanAccountOrIp(long kugouId, String ip, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void isBanAccountOrDeviceNo(long kugouId, String deviceNo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void listBanAccountInfo(List<Long> kugouIdList, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

  }

  public static class Client extends org.apache.thrift.TServiceClient implements Iface {
    public static class Factory implements org.apache.thrift.TServiceClientFactory<Client> {
      public Factory() {}
      public Client getClient(org.apache.thrift.protocol.TProtocol prot) {
        return new Client(prot);
      }
      public Client getClient(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
        return new Client(iprot, oprot);
      }
    }

    public Client(org.apache.thrift.protocol.TProtocol prot)
    {
      super(prot, prot);
    }

    public Client(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
      super(iprot, oprot);
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result isBanAccount(long kugouId) throws org.apache.thrift.TException
    {
      send_isBanAccount(kugouId);
      return recv_isBanAccount();
    }

    public void send_isBanAccount(long kugouId) throws org.apache.thrift.TException
    {
      isBanAccount_args args = new isBanAccount_args();
      args.setKugouId(kugouId);
      sendBase("isBanAccount", args);
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result recv_isBanAccount() throws org.apache.thrift.TException
    {
      isBanAccount_result result = new isBanAccount_result();
      receiveBase(result, "isBanAccount");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "isBanAccount failed: unknown result");
    }

    public com.kugou.fanxing.thrift.banaccount.dto.OperateResult setBanAccount(com.kugou.fanxing.thrift.banaccount.dto.BanAccount ban) throws org.apache.thrift.TException
    {
      send_setBanAccount(ban);
      return recv_setBanAccount();
    }

    public void send_setBanAccount(com.kugou.fanxing.thrift.banaccount.dto.BanAccount ban) throws org.apache.thrift.TException
    {
      setBanAccount_args args = new setBanAccount_args();
      args.setBan(ban);
      sendBase("setBanAccount", args);
    }

    public com.kugou.fanxing.thrift.banaccount.dto.OperateResult recv_setBanAccount() throws org.apache.thrift.TException
    {
      setBanAccount_result result = new setBanAccount_result();
      receiveBase(result, "setBanAccount");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "setBanAccount failed: unknown result");
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result isBanIP(String ip) throws org.apache.thrift.TException
    {
      send_isBanIP(ip);
      return recv_isBanIP();
    }

    public void send_isBanIP(String ip) throws org.apache.thrift.TException
    {
      isBanIP_args args = new isBanIP_args();
      args.setIp(ip);
      sendBase("isBanIP", args);
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result recv_isBanIP() throws org.apache.thrift.TException
    {
      isBanIP_result result = new isBanIP_result();
      receiveBase(result, "isBanIP");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "isBanIP failed: unknown result");
    }

    public com.kugou.fanxing.thrift.banaccount.dto.OperateResult setBanIP(com.kugou.fanxing.thrift.banaccount.dto.BanX ban) throws org.apache.thrift.TException
    {
      send_setBanIP(ban);
      return recv_setBanIP();
    }

    public void send_setBanIP(com.kugou.fanxing.thrift.banaccount.dto.BanX ban) throws org.apache.thrift.TException
    {
      setBanIP_args args = new setBanIP_args();
      args.setBan(ban);
      sendBase("setBanIP", args);
    }

    public com.kugou.fanxing.thrift.banaccount.dto.OperateResult recv_setBanIP() throws org.apache.thrift.TException
    {
      setBanIP_result result = new setBanIP_result();
      receiveBase(result, "setBanIP");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "setBanIP failed: unknown result");
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result isBanDeviceNo(String deviceNo) throws org.apache.thrift.TException
    {
      send_isBanDeviceNo(deviceNo);
      return recv_isBanDeviceNo();
    }

    public void send_isBanDeviceNo(String deviceNo) throws org.apache.thrift.TException
    {
      isBanDeviceNo_args args = new isBanDeviceNo_args();
      args.setDeviceNo(deviceNo);
      sendBase("isBanDeviceNo", args);
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result recv_isBanDeviceNo() throws org.apache.thrift.TException
    {
      isBanDeviceNo_result result = new isBanDeviceNo_result();
      receiveBase(result, "isBanDeviceNo");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "isBanDeviceNo failed: unknown result");
    }

    public com.kugou.fanxing.thrift.banaccount.dto.OperateResult setBanDeviceNo(com.kugou.fanxing.thrift.banaccount.dto.BanX ban) throws org.apache.thrift.TException
    {
      send_setBanDeviceNo(ban);
      return recv_setBanDeviceNo();
    }

    public void send_setBanDeviceNo(com.kugou.fanxing.thrift.banaccount.dto.BanX ban) throws org.apache.thrift.TException
    {
      setBanDeviceNo_args args = new setBanDeviceNo_args();
      args.setBan(ban);
      sendBase("setBanDeviceNo", args);
    }

    public com.kugou.fanxing.thrift.banaccount.dto.OperateResult recv_setBanDeviceNo() throws org.apache.thrift.TException
    {
      setBanDeviceNo_result result = new setBanDeviceNo_result();
      receiveBase(result, "setBanDeviceNo");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "setBanDeviceNo failed: unknown result");
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result isBanAccountOrIp(long kugouId, String ip) throws org.apache.thrift.TException
    {
      send_isBanAccountOrIp(kugouId, ip);
      return recv_isBanAccountOrIp();
    }

    public void send_isBanAccountOrIp(long kugouId, String ip) throws org.apache.thrift.TException
    {
      isBanAccountOrIp_args args = new isBanAccountOrIp_args();
      args.setKugouId(kugouId);
      args.setIp(ip);
      sendBase("isBanAccountOrIp", args);
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result recv_isBanAccountOrIp() throws org.apache.thrift.TException
    {
      isBanAccountOrIp_result result = new isBanAccountOrIp_result();
      receiveBase(result, "isBanAccountOrIp");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "isBanAccountOrIp failed: unknown result");
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result isBanAccountOrDeviceNo(long kugouId, String deviceNo) throws org.apache.thrift.TException
    {
      send_isBanAccountOrDeviceNo(kugouId, deviceNo);
      return recv_isBanAccountOrDeviceNo();
    }

    public void send_isBanAccountOrDeviceNo(long kugouId, String deviceNo) throws org.apache.thrift.TException
    {
      isBanAccountOrDeviceNo_args args = new isBanAccountOrDeviceNo_args();
      args.setKugouId(kugouId);
      args.setDeviceNo(deviceNo);
      sendBase("isBanAccountOrDeviceNo", args);
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result recv_isBanAccountOrDeviceNo() throws org.apache.thrift.TException
    {
      isBanAccountOrDeviceNo_result result = new isBanAccountOrDeviceNo_result();
      receiveBase(result, "isBanAccountOrDeviceNo");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "isBanAccountOrDeviceNo failed: unknown result");
    }

    public com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp listBanAccountInfo(List<Long> kugouIdList) throws org.apache.thrift.TException
    {
      send_listBanAccountInfo(kugouIdList);
      return recv_listBanAccountInfo();
    }

    public void send_listBanAccountInfo(List<Long> kugouIdList) throws org.apache.thrift.TException
    {
      listBanAccountInfo_args args = new listBanAccountInfo_args();
      args.setKugouIdList(kugouIdList);
      sendBase("listBanAccountInfo", args);
    }

    public com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp recv_listBanAccountInfo() throws org.apache.thrift.TException
    {
      listBanAccountInfo_result result = new listBanAccountInfo_result();
      receiveBase(result, "listBanAccountInfo");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "listBanAccountInfo failed: unknown result");
    }

  }
  public static class AsyncClient extends org.apache.thrift.async.TAsyncClient implements AsyncIface {
    public static class Factory implements org.apache.thrift.async.TAsyncClientFactory<AsyncClient> {
      private org.apache.thrift.async.TAsyncClientManager clientManager;
      private org.apache.thrift.protocol.TProtocolFactory protocolFactory;
      public Factory(org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.protocol.TProtocolFactory protocolFactory) {
        this.clientManager = clientManager;
        this.protocolFactory = protocolFactory;
      }
      public AsyncClient getAsyncClient(org.apache.thrift.transport.TNonblockingTransport transport) {
        return new AsyncClient(protocolFactory, clientManager, transport);
      }
    }

    public AsyncClient(org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.transport.TNonblockingTransport transport) {
      super(protocolFactory, clientManager, transport);
    }

    public void isBanAccount(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      isBanAccount_call method_call = new isBanAccount_call(kugouId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class isBanAccount_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      public isBanAccount_call(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("isBanAccount", org.apache.thrift.protocol.TMessageType.CALL, 0));
        isBanAccount_args args = new isBanAccount_args();
        args.setKugouId(kugouId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.banaccount.dto.Result getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_isBanAccount();
      }
    }

    public void setBanAccount(com.kugou.fanxing.thrift.banaccount.dto.BanAccount ban, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      setBanAccount_call method_call = new setBanAccount_call(ban, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class setBanAccount_call extends org.apache.thrift.async.TAsyncMethodCall {
      private com.kugou.fanxing.thrift.banaccount.dto.BanAccount ban;
      public setBanAccount_call(com.kugou.fanxing.thrift.banaccount.dto.BanAccount ban, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.ban = ban;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("setBanAccount", org.apache.thrift.protocol.TMessageType.CALL, 0));
        setBanAccount_args args = new setBanAccount_args();
        args.setBan(ban);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.banaccount.dto.OperateResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_setBanAccount();
      }
    }

    public void isBanIP(String ip, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      isBanIP_call method_call = new isBanIP_call(ip, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class isBanIP_call extends org.apache.thrift.async.TAsyncMethodCall {
      private String ip;
      public isBanIP_call(String ip, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.ip = ip;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("isBanIP", org.apache.thrift.protocol.TMessageType.CALL, 0));
        isBanIP_args args = new isBanIP_args();
        args.setIp(ip);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.banaccount.dto.Result getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_isBanIP();
      }
    }

    public void setBanIP(com.kugou.fanxing.thrift.banaccount.dto.BanX ban, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      setBanIP_call method_call = new setBanIP_call(ban, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class setBanIP_call extends org.apache.thrift.async.TAsyncMethodCall {
      private com.kugou.fanxing.thrift.banaccount.dto.BanX ban;
      public setBanIP_call(com.kugou.fanxing.thrift.banaccount.dto.BanX ban, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.ban = ban;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("setBanIP", org.apache.thrift.protocol.TMessageType.CALL, 0));
        setBanIP_args args = new setBanIP_args();
        args.setBan(ban);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.banaccount.dto.OperateResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_setBanIP();
      }
    }

    public void isBanDeviceNo(String deviceNo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      isBanDeviceNo_call method_call = new isBanDeviceNo_call(deviceNo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class isBanDeviceNo_call extends org.apache.thrift.async.TAsyncMethodCall {
      private String deviceNo;
      public isBanDeviceNo_call(String deviceNo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.deviceNo = deviceNo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("isBanDeviceNo", org.apache.thrift.protocol.TMessageType.CALL, 0));
        isBanDeviceNo_args args = new isBanDeviceNo_args();
        args.setDeviceNo(deviceNo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.banaccount.dto.Result getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_isBanDeviceNo();
      }
    }

    public void setBanDeviceNo(com.kugou.fanxing.thrift.banaccount.dto.BanX ban, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      setBanDeviceNo_call method_call = new setBanDeviceNo_call(ban, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class setBanDeviceNo_call extends org.apache.thrift.async.TAsyncMethodCall {
      private com.kugou.fanxing.thrift.banaccount.dto.BanX ban;
      public setBanDeviceNo_call(com.kugou.fanxing.thrift.banaccount.dto.BanX ban, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.ban = ban;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("setBanDeviceNo", org.apache.thrift.protocol.TMessageType.CALL, 0));
        setBanDeviceNo_args args = new setBanDeviceNo_args();
        args.setBan(ban);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.banaccount.dto.OperateResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_setBanDeviceNo();
      }
    }

    public void isBanAccountOrIp(long kugouId, String ip, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      isBanAccountOrIp_call method_call = new isBanAccountOrIp_call(kugouId, ip, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class isBanAccountOrIp_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      private String ip;
      public isBanAccountOrIp_call(long kugouId, String ip, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
        this.ip = ip;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("isBanAccountOrIp", org.apache.thrift.protocol.TMessageType.CALL, 0));
        isBanAccountOrIp_args args = new isBanAccountOrIp_args();
        args.setKugouId(kugouId);
        args.setIp(ip);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.banaccount.dto.Result getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_isBanAccountOrIp();
      }
    }

    public void isBanAccountOrDeviceNo(long kugouId, String deviceNo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      isBanAccountOrDeviceNo_call method_call = new isBanAccountOrDeviceNo_call(kugouId, deviceNo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class isBanAccountOrDeviceNo_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      private String deviceNo;
      public isBanAccountOrDeviceNo_call(long kugouId, String deviceNo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
        this.deviceNo = deviceNo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("isBanAccountOrDeviceNo", org.apache.thrift.protocol.TMessageType.CALL, 0));
        isBanAccountOrDeviceNo_args args = new isBanAccountOrDeviceNo_args();
        args.setKugouId(kugouId);
        args.setDeviceNo(deviceNo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.banaccount.dto.Result getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_isBanAccountOrDeviceNo();
      }
    }

    public void listBanAccountInfo(List<Long> kugouIdList, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      listBanAccountInfo_call method_call = new listBanAccountInfo_call(kugouIdList, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class listBanAccountInfo_call extends org.apache.thrift.async.TAsyncMethodCall {
      private List<Long> kugouIdList;
      public listBanAccountInfo_call(List<Long> kugouIdList, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouIdList = kugouIdList;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("listBanAccountInfo", org.apache.thrift.protocol.TMessageType.CALL, 0));
        listBanAccountInfo_args args = new listBanAccountInfo_args();
        args.setKugouIdList(kugouIdList);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_listBanAccountInfo();
      }
    }

  }

  public static class Processor<I extends Iface> extends org.apache.thrift.TBaseProcessor<I> implements org.apache.thrift.TProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());
    public Processor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>>()));
    }

    protected Processor(I iface, Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends Iface> Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> getProcessMap(Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      processMap.put("isBanAccount", new isBanAccount());
      processMap.put("setBanAccount", new setBanAccount());
      processMap.put("isBanIP", new isBanIP());
      processMap.put("setBanIP", new setBanIP());
      processMap.put("isBanDeviceNo", new isBanDeviceNo());
      processMap.put("setBanDeviceNo", new setBanDeviceNo());
      processMap.put("isBanAccountOrIp", new isBanAccountOrIp());
      processMap.put("isBanAccountOrDeviceNo", new isBanAccountOrDeviceNo());
      processMap.put("listBanAccountInfo", new listBanAccountInfo());
      return processMap;
    }

    public static class isBanAccount<I extends Iface> extends org.apache.thrift.ProcessFunction<I, isBanAccount_args> {
      public isBanAccount() {
        super("isBanAccount");
      }

      public isBanAccount_args getEmptyArgsInstance() {
        return new isBanAccount_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public isBanAccount_result getResult(I iface, isBanAccount_args args) throws org.apache.thrift.TException {
        isBanAccount_result result = new isBanAccount_result();
        result.success = iface.isBanAccount(args.kugouId);
        return result;
      }
    }

    public static class setBanAccount<I extends Iface> extends org.apache.thrift.ProcessFunction<I, setBanAccount_args> {
      public setBanAccount() {
        super("setBanAccount");
      }

      public setBanAccount_args getEmptyArgsInstance() {
        return new setBanAccount_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public setBanAccount_result getResult(I iface, setBanAccount_args args) throws org.apache.thrift.TException {
        setBanAccount_result result = new setBanAccount_result();
        result.success = iface.setBanAccount(args.ban);
        return result;
      }
    }

    public static class isBanIP<I extends Iface> extends org.apache.thrift.ProcessFunction<I, isBanIP_args> {
      public isBanIP() {
        super("isBanIP");
      }

      public isBanIP_args getEmptyArgsInstance() {
        return new isBanIP_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public isBanIP_result getResult(I iface, isBanIP_args args) throws org.apache.thrift.TException {
        isBanIP_result result = new isBanIP_result();
        result.success = iface.isBanIP(args.ip);
        return result;
      }
    }

    public static class setBanIP<I extends Iface> extends org.apache.thrift.ProcessFunction<I, setBanIP_args> {
      public setBanIP() {
        super("setBanIP");
      }

      public setBanIP_args getEmptyArgsInstance() {
        return new setBanIP_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public setBanIP_result getResult(I iface, setBanIP_args args) throws org.apache.thrift.TException {
        setBanIP_result result = new setBanIP_result();
        result.success = iface.setBanIP(args.ban);
        return result;
      }
    }

    public static class isBanDeviceNo<I extends Iface> extends org.apache.thrift.ProcessFunction<I, isBanDeviceNo_args> {
      public isBanDeviceNo() {
        super("isBanDeviceNo");
      }

      public isBanDeviceNo_args getEmptyArgsInstance() {
        return new isBanDeviceNo_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public isBanDeviceNo_result getResult(I iface, isBanDeviceNo_args args) throws org.apache.thrift.TException {
        isBanDeviceNo_result result = new isBanDeviceNo_result();
        result.success = iface.isBanDeviceNo(args.deviceNo);
        return result;
      }
    }

    public static class setBanDeviceNo<I extends Iface> extends org.apache.thrift.ProcessFunction<I, setBanDeviceNo_args> {
      public setBanDeviceNo() {
        super("setBanDeviceNo");
      }

      public setBanDeviceNo_args getEmptyArgsInstance() {
        return new setBanDeviceNo_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public setBanDeviceNo_result getResult(I iface, setBanDeviceNo_args args) throws org.apache.thrift.TException {
        setBanDeviceNo_result result = new setBanDeviceNo_result();
        result.success = iface.setBanDeviceNo(args.ban);
        return result;
      }
    }

    public static class isBanAccountOrIp<I extends Iface> extends org.apache.thrift.ProcessFunction<I, isBanAccountOrIp_args> {
      public isBanAccountOrIp() {
        super("isBanAccountOrIp");
      }

      public isBanAccountOrIp_args getEmptyArgsInstance() {
        return new isBanAccountOrIp_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public isBanAccountOrIp_result getResult(I iface, isBanAccountOrIp_args args) throws org.apache.thrift.TException {
        isBanAccountOrIp_result result = new isBanAccountOrIp_result();
        result.success = iface.isBanAccountOrIp(args.kugouId, args.ip);
        return result;
      }
    }

    public static class isBanAccountOrDeviceNo<I extends Iface> extends org.apache.thrift.ProcessFunction<I, isBanAccountOrDeviceNo_args> {
      public isBanAccountOrDeviceNo() {
        super("isBanAccountOrDeviceNo");
      }

      public isBanAccountOrDeviceNo_args getEmptyArgsInstance() {
        return new isBanAccountOrDeviceNo_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public isBanAccountOrDeviceNo_result getResult(I iface, isBanAccountOrDeviceNo_args args) throws org.apache.thrift.TException {
        isBanAccountOrDeviceNo_result result = new isBanAccountOrDeviceNo_result();
        result.success = iface.isBanAccountOrDeviceNo(args.kugouId, args.deviceNo);
        return result;
      }
    }

    public static class listBanAccountInfo<I extends Iface> extends org.apache.thrift.ProcessFunction<I, listBanAccountInfo_args> {
      public listBanAccountInfo() {
        super("listBanAccountInfo");
      }

      public listBanAccountInfo_args getEmptyArgsInstance() {
        return new listBanAccountInfo_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public listBanAccountInfo_result getResult(I iface, listBanAccountInfo_args args) throws org.apache.thrift.TException {
        listBanAccountInfo_result result = new listBanAccountInfo_result();
        result.success = iface.listBanAccountInfo(args.kugouIdList);
        return result;
      }
    }

  }

  public static class AsyncProcessor<I extends AsyncIface> extends org.apache.thrift.TBaseAsyncProcessor<I> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());
    public AsyncProcessor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.AsyncProcessFunction<I, ? extends org.apache.thrift.TBase, ?>>()));
    }

    protected AsyncProcessor(I iface, Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends AsyncIface> Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase,?>> getProcessMap(Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      processMap.put("isBanAccount", new isBanAccount());
      processMap.put("setBanAccount", new setBanAccount());
      processMap.put("isBanIP", new isBanIP());
      processMap.put("setBanIP", new setBanIP());
      processMap.put("isBanDeviceNo", new isBanDeviceNo());
      processMap.put("setBanDeviceNo", new setBanDeviceNo());
      processMap.put("isBanAccountOrIp", new isBanAccountOrIp());
      processMap.put("isBanAccountOrDeviceNo", new isBanAccountOrDeviceNo());
      processMap.put("listBanAccountInfo", new listBanAccountInfo());
      return processMap;
    }

    public static class isBanAccount<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, isBanAccount_args, com.kugou.fanxing.thrift.banaccount.dto.Result> {
      public isBanAccount() {
        super("isBanAccount");
      }

      public isBanAccount_args getEmptyArgsInstance() {
        return new isBanAccount_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.Result> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.Result>() { 
          public void onComplete(com.kugou.fanxing.thrift.banaccount.dto.Result o) {
            isBanAccount_result result = new isBanAccount_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            isBanAccount_result result = new isBanAccount_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, isBanAccount_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.Result> resultHandler) throws TException {
        iface.isBanAccount(args.kugouId,resultHandler);
      }
    }

    public static class setBanAccount<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, setBanAccount_args, com.kugou.fanxing.thrift.banaccount.dto.OperateResult> {
      public setBanAccount() {
        super("setBanAccount");
      }

      public setBanAccount_args getEmptyArgsInstance() {
        return new setBanAccount_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.OperateResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.OperateResult>() { 
          public void onComplete(com.kugou.fanxing.thrift.banaccount.dto.OperateResult o) {
            setBanAccount_result result = new setBanAccount_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            setBanAccount_result result = new setBanAccount_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, setBanAccount_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.OperateResult> resultHandler) throws TException {
        iface.setBanAccount(args.ban,resultHandler);
      }
    }

    public static class isBanIP<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, isBanIP_args, com.kugou.fanxing.thrift.banaccount.dto.Result> {
      public isBanIP() {
        super("isBanIP");
      }

      public isBanIP_args getEmptyArgsInstance() {
        return new isBanIP_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.Result> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.Result>() { 
          public void onComplete(com.kugou.fanxing.thrift.banaccount.dto.Result o) {
            isBanIP_result result = new isBanIP_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            isBanIP_result result = new isBanIP_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, isBanIP_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.Result> resultHandler) throws TException {
        iface.isBanIP(args.ip,resultHandler);
      }
    }

    public static class setBanIP<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, setBanIP_args, com.kugou.fanxing.thrift.banaccount.dto.OperateResult> {
      public setBanIP() {
        super("setBanIP");
      }

      public setBanIP_args getEmptyArgsInstance() {
        return new setBanIP_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.OperateResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.OperateResult>() { 
          public void onComplete(com.kugou.fanxing.thrift.banaccount.dto.OperateResult o) {
            setBanIP_result result = new setBanIP_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            setBanIP_result result = new setBanIP_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, setBanIP_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.OperateResult> resultHandler) throws TException {
        iface.setBanIP(args.ban,resultHandler);
      }
    }

    public static class isBanDeviceNo<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, isBanDeviceNo_args, com.kugou.fanxing.thrift.banaccount.dto.Result> {
      public isBanDeviceNo() {
        super("isBanDeviceNo");
      }

      public isBanDeviceNo_args getEmptyArgsInstance() {
        return new isBanDeviceNo_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.Result> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.Result>() { 
          public void onComplete(com.kugou.fanxing.thrift.banaccount.dto.Result o) {
            isBanDeviceNo_result result = new isBanDeviceNo_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            isBanDeviceNo_result result = new isBanDeviceNo_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, isBanDeviceNo_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.Result> resultHandler) throws TException {
        iface.isBanDeviceNo(args.deviceNo,resultHandler);
      }
    }

    public static class setBanDeviceNo<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, setBanDeviceNo_args, com.kugou.fanxing.thrift.banaccount.dto.OperateResult> {
      public setBanDeviceNo() {
        super("setBanDeviceNo");
      }

      public setBanDeviceNo_args getEmptyArgsInstance() {
        return new setBanDeviceNo_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.OperateResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.OperateResult>() { 
          public void onComplete(com.kugou.fanxing.thrift.banaccount.dto.OperateResult o) {
            setBanDeviceNo_result result = new setBanDeviceNo_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            setBanDeviceNo_result result = new setBanDeviceNo_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, setBanDeviceNo_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.OperateResult> resultHandler) throws TException {
        iface.setBanDeviceNo(args.ban,resultHandler);
      }
    }

    public static class isBanAccountOrIp<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, isBanAccountOrIp_args, com.kugou.fanxing.thrift.banaccount.dto.Result> {
      public isBanAccountOrIp() {
        super("isBanAccountOrIp");
      }

      public isBanAccountOrIp_args getEmptyArgsInstance() {
        return new isBanAccountOrIp_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.Result> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.Result>() { 
          public void onComplete(com.kugou.fanxing.thrift.banaccount.dto.Result o) {
            isBanAccountOrIp_result result = new isBanAccountOrIp_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            isBanAccountOrIp_result result = new isBanAccountOrIp_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, isBanAccountOrIp_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.Result> resultHandler) throws TException {
        iface.isBanAccountOrIp(args.kugouId, args.ip,resultHandler);
      }
    }

    public static class isBanAccountOrDeviceNo<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, isBanAccountOrDeviceNo_args, com.kugou.fanxing.thrift.banaccount.dto.Result> {
      public isBanAccountOrDeviceNo() {
        super("isBanAccountOrDeviceNo");
      }

      public isBanAccountOrDeviceNo_args getEmptyArgsInstance() {
        return new isBanAccountOrDeviceNo_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.Result> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.Result>() { 
          public void onComplete(com.kugou.fanxing.thrift.banaccount.dto.Result o) {
            isBanAccountOrDeviceNo_result result = new isBanAccountOrDeviceNo_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            isBanAccountOrDeviceNo_result result = new isBanAccountOrDeviceNo_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, isBanAccountOrDeviceNo_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.Result> resultHandler) throws TException {
        iface.isBanAccountOrDeviceNo(args.kugouId, args.deviceNo,resultHandler);
      }
    }

    public static class listBanAccountInfo<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, listBanAccountInfo_args, com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp> {
      public listBanAccountInfo() {
        super("listBanAccountInfo");
      }

      public listBanAccountInfo_args getEmptyArgsInstance() {
        return new listBanAccountInfo_args();
      }

      public AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp>() { 
          public void onComplete(com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp o) {
            listBanAccountInfo_result result = new listBanAccountInfo_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            listBanAccountInfo_result result = new listBanAccountInfo_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, listBanAccountInfo_args args, org.apache.thrift.async.AsyncMethodCallback<com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp> resultHandler) throws TException {
        iface.listBanAccountInfo(args.kugouIdList,resultHandler);
      }
    }

  }

  public static class isBanAccount_args implements org.apache.thrift.TBase<isBanAccount_args, isBanAccount_args._Fields>, java.io.Serializable, Cloneable, Comparable<isBanAccount_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isBanAccount_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isBanAccount_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isBanAccount_argsTupleSchemeFactory());
    }

    public long kugouId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isBanAccount_args.class, metaDataMap);
    }

    public isBanAccount_args() {
    }

    public isBanAccount_args(
      long kugouId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isBanAccount_args(isBanAccount_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
    }

    public isBanAccount_args deepCopy() {
      return new isBanAccount_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public isBanAccount_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isBanAccount_args)
        return this.equals((isBanAccount_args)that);
      return false;
    }

    public boolean equals(isBanAccount_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      return list.hashCode();
    }

    @Override
    public int compareTo(isBanAccount_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isBanAccount_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isBanAccount_argsStandardSchemeFactory implements SchemeFactory {
      public isBanAccount_argsStandardScheme getScheme() {
        return new isBanAccount_argsStandardScheme();
      }
    }

    private static class isBanAccount_argsStandardScheme extends StandardScheme<isBanAccount_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isBanAccount_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        if (!struct.isSetKugouId()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
        }
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isBanAccount_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isBanAccount_argsTupleSchemeFactory implements SchemeFactory {
      public isBanAccount_argsTupleScheme getScheme() {
        return new isBanAccount_argsTupleScheme();
      }
    }

    private static class isBanAccount_argsTupleScheme extends TupleScheme<isBanAccount_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isBanAccount_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        oprot.writeI64(struct.kugouId);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isBanAccount_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.kugouId = iprot.readI64();
        struct.setKugouIdIsSet(true);
      }
    }

  }

  public static class isBanAccount_result implements org.apache.thrift.TBase<isBanAccount_result, isBanAccount_result._Fields>, java.io.Serializable, Cloneable, Comparable<isBanAccount_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isBanAccount_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isBanAccount_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isBanAccount_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.banaccount.dto.Result.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isBanAccount_result.class, metaDataMap);
    }

    public isBanAccount_result() {
    }

    public isBanAccount_result(
      com.kugou.fanxing.thrift.banaccount.dto.Result success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isBanAccount_result(isBanAccount_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.banaccount.dto.Result(other.success);
      }
    }

    public isBanAccount_result deepCopy() {
      return new isBanAccount_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result getSuccess() {
      return this.success;
    }

    public isBanAccount_result setSuccess(com.kugou.fanxing.thrift.banaccount.dto.Result success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.banaccount.dto.Result)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isBanAccount_result)
        return this.equals((isBanAccount_result)that);
      return false;
    }

    public boolean equals(isBanAccount_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(isBanAccount_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isBanAccount_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isBanAccount_resultStandardSchemeFactory implements SchemeFactory {
      public isBanAccount_resultStandardScheme getScheme() {
        return new isBanAccount_resultStandardScheme();
      }
    }

    private static class isBanAccount_resultStandardScheme extends StandardScheme<isBanAccount_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isBanAccount_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.banaccount.dto.Result();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isBanAccount_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isBanAccount_resultTupleSchemeFactory implements SchemeFactory {
      public isBanAccount_resultTupleScheme getScheme() {
        return new isBanAccount_resultTupleScheme();
      }
    }

    private static class isBanAccount_resultTupleScheme extends TupleScheme<isBanAccount_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isBanAccount_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isBanAccount_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.banaccount.dto.Result();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class setBanAccount_args implements org.apache.thrift.TBase<setBanAccount_args, setBanAccount_args._Fields>, java.io.Serializable, Cloneable, Comparable<setBanAccount_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("setBanAccount_args");

    private static final org.apache.thrift.protocol.TField BAN_FIELD_DESC = new org.apache.thrift.protocol.TField("ban", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new setBanAccount_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new setBanAccount_argsTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.banaccount.dto.BanAccount ban; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      BAN((short)1, "ban");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // BAN
            return BAN;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.BAN, new org.apache.thrift.meta_data.FieldMetaData("ban", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.banaccount.dto.BanAccount.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(setBanAccount_args.class, metaDataMap);
    }

    public setBanAccount_args() {
    }

    public setBanAccount_args(
      com.kugou.fanxing.thrift.banaccount.dto.BanAccount ban)
    {
      this();
      this.ban = ban;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public setBanAccount_args(setBanAccount_args other) {
      if (other.isSetBan()) {
        this.ban = new com.kugou.fanxing.thrift.banaccount.dto.BanAccount(other.ban);
      }
    }

    public setBanAccount_args deepCopy() {
      return new setBanAccount_args(this);
    }

    @Override
    public void clear() {
      this.ban = null;
    }

    public com.kugou.fanxing.thrift.banaccount.dto.BanAccount getBan() {
      return this.ban;
    }

    public setBanAccount_args setBan(com.kugou.fanxing.thrift.banaccount.dto.BanAccount ban) {
      this.ban = ban;
      return this;
    }

    public void unsetBan() {
      this.ban = null;
    }

    /** Returns true if field ban is set (has been assigned a value) and false otherwise */
    public boolean isSetBan() {
      return this.ban != null;
    }

    public void setBanIsSet(boolean value) {
      if (!value) {
        this.ban = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case BAN:
        if (value == null) {
          unsetBan();
        } else {
          setBan((com.kugou.fanxing.thrift.banaccount.dto.BanAccount)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case BAN:
        return getBan();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case BAN:
        return isSetBan();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof setBanAccount_args)
        return this.equals((setBanAccount_args)that);
      return false;
    }

    public boolean equals(setBanAccount_args that) {
      if (that == null)
        return false;

      boolean this_present_ban = true && this.isSetBan();
      boolean that_present_ban = true && that.isSetBan();
      if (this_present_ban || that_present_ban) {
        if (!(this_present_ban && that_present_ban))
          return false;
        if (!this.ban.equals(that.ban))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_ban = true && (isSetBan());
      list.add(present_ban);
      if (present_ban)
        list.add(ban);

      return list.hashCode();
    }

    @Override
    public int compareTo(setBanAccount_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetBan()).compareTo(other.isSetBan());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetBan()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ban, other.ban);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("setBanAccount_args(");
      boolean first = true;

      sb.append("ban:");
      if (this.ban == null) {
        sb.append("null");
      } else {
        sb.append(this.ban);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (ban == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'ban' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (ban != null) {
        ban.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class setBanAccount_argsStandardSchemeFactory implements SchemeFactory {
      public setBanAccount_argsStandardScheme getScheme() {
        return new setBanAccount_argsStandardScheme();
      }
    }

    private static class setBanAccount_argsStandardScheme extends StandardScheme<setBanAccount_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, setBanAccount_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // BAN
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.ban = new com.kugou.fanxing.thrift.banaccount.dto.BanAccount();
                struct.ban.read(iprot);
                struct.setBanIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, setBanAccount_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.ban != null) {
          oprot.writeFieldBegin(BAN_FIELD_DESC);
          struct.ban.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class setBanAccount_argsTupleSchemeFactory implements SchemeFactory {
      public setBanAccount_argsTupleScheme getScheme() {
        return new setBanAccount_argsTupleScheme();
      }
    }

    private static class setBanAccount_argsTupleScheme extends TupleScheme<setBanAccount_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, setBanAccount_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.ban.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, setBanAccount_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.ban = new com.kugou.fanxing.thrift.banaccount.dto.BanAccount();
        struct.ban.read(iprot);
        struct.setBanIsSet(true);
      }
    }

  }

  public static class setBanAccount_result implements org.apache.thrift.TBase<setBanAccount_result, setBanAccount_result._Fields>, java.io.Serializable, Cloneable, Comparable<setBanAccount_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("setBanAccount_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new setBanAccount_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new setBanAccount_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.banaccount.dto.OperateResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.banaccount.dto.OperateResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(setBanAccount_result.class, metaDataMap);
    }

    public setBanAccount_result() {
    }

    public setBanAccount_result(
      com.kugou.fanxing.thrift.banaccount.dto.OperateResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public setBanAccount_result(setBanAccount_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.banaccount.dto.OperateResult(other.success);
      }
    }

    public setBanAccount_result deepCopy() {
      return new setBanAccount_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.banaccount.dto.OperateResult getSuccess() {
      return this.success;
    }

    public setBanAccount_result setSuccess(com.kugou.fanxing.thrift.banaccount.dto.OperateResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.banaccount.dto.OperateResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof setBanAccount_result)
        return this.equals((setBanAccount_result)that);
      return false;
    }

    public boolean equals(setBanAccount_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(setBanAccount_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("setBanAccount_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class setBanAccount_resultStandardSchemeFactory implements SchemeFactory {
      public setBanAccount_resultStandardScheme getScheme() {
        return new setBanAccount_resultStandardScheme();
      }
    }

    private static class setBanAccount_resultStandardScheme extends StandardScheme<setBanAccount_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, setBanAccount_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.banaccount.dto.OperateResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, setBanAccount_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class setBanAccount_resultTupleSchemeFactory implements SchemeFactory {
      public setBanAccount_resultTupleScheme getScheme() {
        return new setBanAccount_resultTupleScheme();
      }
    }

    private static class setBanAccount_resultTupleScheme extends TupleScheme<setBanAccount_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, setBanAccount_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, setBanAccount_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.banaccount.dto.OperateResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class isBanIP_args implements org.apache.thrift.TBase<isBanIP_args, isBanIP_args._Fields>, java.io.Serializable, Cloneable, Comparable<isBanIP_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isBanIP_args");

    private static final org.apache.thrift.protocol.TField IP_FIELD_DESC = new org.apache.thrift.protocol.TField("ip", org.apache.thrift.protocol.TType.STRING, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isBanIP_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isBanIP_argsTupleSchemeFactory());
    }

    public String ip; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      IP((short)1, "ip");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // IP
            return IP;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.IP, new org.apache.thrift.meta_data.FieldMetaData("ip", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isBanIP_args.class, metaDataMap);
    }

    public isBanIP_args() {
    }

    public isBanIP_args(
      String ip)
    {
      this();
      this.ip = ip;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isBanIP_args(isBanIP_args other) {
      if (other.isSetIp()) {
        this.ip = other.ip;
      }
    }

    public isBanIP_args deepCopy() {
      return new isBanIP_args(this);
    }

    @Override
    public void clear() {
      this.ip = null;
    }

    public String getIp() {
      return this.ip;
    }

    public isBanIP_args setIp(String ip) {
      this.ip = ip;
      return this;
    }

    public void unsetIp() {
      this.ip = null;
    }

    /** Returns true if field ip is set (has been assigned a value) and false otherwise */
    public boolean isSetIp() {
      return this.ip != null;
    }

    public void setIpIsSet(boolean value) {
      if (!value) {
        this.ip = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case IP:
        if (value == null) {
          unsetIp();
        } else {
          setIp((String)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case IP:
        return getIp();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case IP:
        return isSetIp();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isBanIP_args)
        return this.equals((isBanIP_args)that);
      return false;
    }

    public boolean equals(isBanIP_args that) {
      if (that == null)
        return false;

      boolean this_present_ip = true && this.isSetIp();
      boolean that_present_ip = true && that.isSetIp();
      if (this_present_ip || that_present_ip) {
        if (!(this_present_ip && that_present_ip))
          return false;
        if (!this.ip.equals(that.ip))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_ip = true && (isSetIp());
      list.add(present_ip);
      if (present_ip)
        list.add(ip);

      return list.hashCode();
    }

    @Override
    public int compareTo(isBanIP_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetIp()).compareTo(other.isSetIp());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetIp()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ip, other.ip);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isBanIP_args(");
      boolean first = true;

      sb.append("ip:");
      if (this.ip == null) {
        sb.append("null");
      } else {
        sb.append(this.ip);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (ip == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'ip' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isBanIP_argsStandardSchemeFactory implements SchemeFactory {
      public isBanIP_argsStandardScheme getScheme() {
        return new isBanIP_argsStandardScheme();
      }
    }

    private static class isBanIP_argsStandardScheme extends StandardScheme<isBanIP_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isBanIP_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // IP
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.ip = iprot.readString();
                struct.setIpIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isBanIP_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.ip != null) {
          oprot.writeFieldBegin(IP_FIELD_DESC);
          oprot.writeString(struct.ip);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isBanIP_argsTupleSchemeFactory implements SchemeFactory {
      public isBanIP_argsTupleScheme getScheme() {
        return new isBanIP_argsTupleScheme();
      }
    }

    private static class isBanIP_argsTupleScheme extends TupleScheme<isBanIP_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isBanIP_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        oprot.writeString(struct.ip);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isBanIP_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.ip = iprot.readString();
        struct.setIpIsSet(true);
      }
    }

  }

  public static class isBanIP_result implements org.apache.thrift.TBase<isBanIP_result, isBanIP_result._Fields>, java.io.Serializable, Cloneable, Comparable<isBanIP_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isBanIP_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isBanIP_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isBanIP_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.banaccount.dto.Result.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isBanIP_result.class, metaDataMap);
    }

    public isBanIP_result() {
    }

    public isBanIP_result(
      com.kugou.fanxing.thrift.banaccount.dto.Result success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isBanIP_result(isBanIP_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.banaccount.dto.Result(other.success);
      }
    }

    public isBanIP_result deepCopy() {
      return new isBanIP_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result getSuccess() {
      return this.success;
    }

    public isBanIP_result setSuccess(com.kugou.fanxing.thrift.banaccount.dto.Result success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.banaccount.dto.Result)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isBanIP_result)
        return this.equals((isBanIP_result)that);
      return false;
    }

    public boolean equals(isBanIP_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(isBanIP_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isBanIP_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isBanIP_resultStandardSchemeFactory implements SchemeFactory {
      public isBanIP_resultStandardScheme getScheme() {
        return new isBanIP_resultStandardScheme();
      }
    }

    private static class isBanIP_resultStandardScheme extends StandardScheme<isBanIP_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isBanIP_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.banaccount.dto.Result();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isBanIP_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isBanIP_resultTupleSchemeFactory implements SchemeFactory {
      public isBanIP_resultTupleScheme getScheme() {
        return new isBanIP_resultTupleScheme();
      }
    }

    private static class isBanIP_resultTupleScheme extends TupleScheme<isBanIP_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isBanIP_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isBanIP_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.banaccount.dto.Result();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class setBanIP_args implements org.apache.thrift.TBase<setBanIP_args, setBanIP_args._Fields>, java.io.Serializable, Cloneable, Comparable<setBanIP_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("setBanIP_args");

    private static final org.apache.thrift.protocol.TField BAN_FIELD_DESC = new org.apache.thrift.protocol.TField("ban", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new setBanIP_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new setBanIP_argsTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.banaccount.dto.BanX ban; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      BAN((short)1, "ban");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // BAN
            return BAN;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.BAN, new org.apache.thrift.meta_data.FieldMetaData("ban", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.banaccount.dto.BanX.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(setBanIP_args.class, metaDataMap);
    }

    public setBanIP_args() {
    }

    public setBanIP_args(
      com.kugou.fanxing.thrift.banaccount.dto.BanX ban)
    {
      this();
      this.ban = ban;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public setBanIP_args(setBanIP_args other) {
      if (other.isSetBan()) {
        this.ban = new com.kugou.fanxing.thrift.banaccount.dto.BanX(other.ban);
      }
    }

    public setBanIP_args deepCopy() {
      return new setBanIP_args(this);
    }

    @Override
    public void clear() {
      this.ban = null;
    }

    public com.kugou.fanxing.thrift.banaccount.dto.BanX getBan() {
      return this.ban;
    }

    public setBanIP_args setBan(com.kugou.fanxing.thrift.banaccount.dto.BanX ban) {
      this.ban = ban;
      return this;
    }

    public void unsetBan() {
      this.ban = null;
    }

    /** Returns true if field ban is set (has been assigned a value) and false otherwise */
    public boolean isSetBan() {
      return this.ban != null;
    }

    public void setBanIsSet(boolean value) {
      if (!value) {
        this.ban = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case BAN:
        if (value == null) {
          unsetBan();
        } else {
          setBan((com.kugou.fanxing.thrift.banaccount.dto.BanX)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case BAN:
        return getBan();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case BAN:
        return isSetBan();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof setBanIP_args)
        return this.equals((setBanIP_args)that);
      return false;
    }

    public boolean equals(setBanIP_args that) {
      if (that == null)
        return false;

      boolean this_present_ban = true && this.isSetBan();
      boolean that_present_ban = true && that.isSetBan();
      if (this_present_ban || that_present_ban) {
        if (!(this_present_ban && that_present_ban))
          return false;
        if (!this.ban.equals(that.ban))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_ban = true && (isSetBan());
      list.add(present_ban);
      if (present_ban)
        list.add(ban);

      return list.hashCode();
    }

    @Override
    public int compareTo(setBanIP_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetBan()).compareTo(other.isSetBan());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetBan()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ban, other.ban);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("setBanIP_args(");
      boolean first = true;

      sb.append("ban:");
      if (this.ban == null) {
        sb.append("null");
      } else {
        sb.append(this.ban);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (ban == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'ban' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (ban != null) {
        ban.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class setBanIP_argsStandardSchemeFactory implements SchemeFactory {
      public setBanIP_argsStandardScheme getScheme() {
        return new setBanIP_argsStandardScheme();
      }
    }

    private static class setBanIP_argsStandardScheme extends StandardScheme<setBanIP_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, setBanIP_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // BAN
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.ban = new com.kugou.fanxing.thrift.banaccount.dto.BanX();
                struct.ban.read(iprot);
                struct.setBanIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, setBanIP_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.ban != null) {
          oprot.writeFieldBegin(BAN_FIELD_DESC);
          struct.ban.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class setBanIP_argsTupleSchemeFactory implements SchemeFactory {
      public setBanIP_argsTupleScheme getScheme() {
        return new setBanIP_argsTupleScheme();
      }
    }

    private static class setBanIP_argsTupleScheme extends TupleScheme<setBanIP_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, setBanIP_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.ban.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, setBanIP_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.ban = new com.kugou.fanxing.thrift.banaccount.dto.BanX();
        struct.ban.read(iprot);
        struct.setBanIsSet(true);
      }
    }

  }

  public static class setBanIP_result implements org.apache.thrift.TBase<setBanIP_result, setBanIP_result._Fields>, java.io.Serializable, Cloneable, Comparable<setBanIP_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("setBanIP_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new setBanIP_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new setBanIP_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.banaccount.dto.OperateResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.banaccount.dto.OperateResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(setBanIP_result.class, metaDataMap);
    }

    public setBanIP_result() {
    }

    public setBanIP_result(
      com.kugou.fanxing.thrift.banaccount.dto.OperateResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public setBanIP_result(setBanIP_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.banaccount.dto.OperateResult(other.success);
      }
    }

    public setBanIP_result deepCopy() {
      return new setBanIP_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.banaccount.dto.OperateResult getSuccess() {
      return this.success;
    }

    public setBanIP_result setSuccess(com.kugou.fanxing.thrift.banaccount.dto.OperateResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.banaccount.dto.OperateResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof setBanIP_result)
        return this.equals((setBanIP_result)that);
      return false;
    }

    public boolean equals(setBanIP_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(setBanIP_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("setBanIP_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class setBanIP_resultStandardSchemeFactory implements SchemeFactory {
      public setBanIP_resultStandardScheme getScheme() {
        return new setBanIP_resultStandardScheme();
      }
    }

    private static class setBanIP_resultStandardScheme extends StandardScheme<setBanIP_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, setBanIP_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.banaccount.dto.OperateResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, setBanIP_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class setBanIP_resultTupleSchemeFactory implements SchemeFactory {
      public setBanIP_resultTupleScheme getScheme() {
        return new setBanIP_resultTupleScheme();
      }
    }

    private static class setBanIP_resultTupleScheme extends TupleScheme<setBanIP_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, setBanIP_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, setBanIP_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.banaccount.dto.OperateResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class isBanDeviceNo_args implements org.apache.thrift.TBase<isBanDeviceNo_args, isBanDeviceNo_args._Fields>, java.io.Serializable, Cloneable, Comparable<isBanDeviceNo_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isBanDeviceNo_args");

    private static final org.apache.thrift.protocol.TField DEVICE_NO_FIELD_DESC = new org.apache.thrift.protocol.TField("deviceNo", org.apache.thrift.protocol.TType.STRING, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isBanDeviceNo_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isBanDeviceNo_argsTupleSchemeFactory());
    }

    public String deviceNo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      DEVICE_NO((short)1, "deviceNo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // DEVICE_NO
            return DEVICE_NO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.DEVICE_NO, new org.apache.thrift.meta_data.FieldMetaData("deviceNo", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isBanDeviceNo_args.class, metaDataMap);
    }

    public isBanDeviceNo_args() {
    }

    public isBanDeviceNo_args(
      String deviceNo)
    {
      this();
      this.deviceNo = deviceNo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isBanDeviceNo_args(isBanDeviceNo_args other) {
      if (other.isSetDeviceNo()) {
        this.deviceNo = other.deviceNo;
      }
    }

    public isBanDeviceNo_args deepCopy() {
      return new isBanDeviceNo_args(this);
    }

    @Override
    public void clear() {
      this.deviceNo = null;
    }

    public String getDeviceNo() {
      return this.deviceNo;
    }

    public isBanDeviceNo_args setDeviceNo(String deviceNo) {
      this.deviceNo = deviceNo;
      return this;
    }

    public void unsetDeviceNo() {
      this.deviceNo = null;
    }

    /** Returns true if field deviceNo is set (has been assigned a value) and false otherwise */
    public boolean isSetDeviceNo() {
      return this.deviceNo != null;
    }

    public void setDeviceNoIsSet(boolean value) {
      if (!value) {
        this.deviceNo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case DEVICE_NO:
        if (value == null) {
          unsetDeviceNo();
        } else {
          setDeviceNo((String)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case DEVICE_NO:
        return getDeviceNo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case DEVICE_NO:
        return isSetDeviceNo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isBanDeviceNo_args)
        return this.equals((isBanDeviceNo_args)that);
      return false;
    }

    public boolean equals(isBanDeviceNo_args that) {
      if (that == null)
        return false;

      boolean this_present_deviceNo = true && this.isSetDeviceNo();
      boolean that_present_deviceNo = true && that.isSetDeviceNo();
      if (this_present_deviceNo || that_present_deviceNo) {
        if (!(this_present_deviceNo && that_present_deviceNo))
          return false;
        if (!this.deviceNo.equals(that.deviceNo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_deviceNo = true && (isSetDeviceNo());
      list.add(present_deviceNo);
      if (present_deviceNo)
        list.add(deviceNo);

      return list.hashCode();
    }

    @Override
    public int compareTo(isBanDeviceNo_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetDeviceNo()).compareTo(other.isSetDeviceNo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetDeviceNo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.deviceNo, other.deviceNo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isBanDeviceNo_args(");
      boolean first = true;

      sb.append("deviceNo:");
      if (this.deviceNo == null) {
        sb.append("null");
      } else {
        sb.append(this.deviceNo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (deviceNo == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'deviceNo' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isBanDeviceNo_argsStandardSchemeFactory implements SchemeFactory {
      public isBanDeviceNo_argsStandardScheme getScheme() {
        return new isBanDeviceNo_argsStandardScheme();
      }
    }

    private static class isBanDeviceNo_argsStandardScheme extends StandardScheme<isBanDeviceNo_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isBanDeviceNo_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // DEVICE_NO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.deviceNo = iprot.readString();
                struct.setDeviceNoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isBanDeviceNo_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.deviceNo != null) {
          oprot.writeFieldBegin(DEVICE_NO_FIELD_DESC);
          oprot.writeString(struct.deviceNo);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isBanDeviceNo_argsTupleSchemeFactory implements SchemeFactory {
      public isBanDeviceNo_argsTupleScheme getScheme() {
        return new isBanDeviceNo_argsTupleScheme();
      }
    }

    private static class isBanDeviceNo_argsTupleScheme extends TupleScheme<isBanDeviceNo_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isBanDeviceNo_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        oprot.writeString(struct.deviceNo);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isBanDeviceNo_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.deviceNo = iprot.readString();
        struct.setDeviceNoIsSet(true);
      }
    }

  }

  public static class isBanDeviceNo_result implements org.apache.thrift.TBase<isBanDeviceNo_result, isBanDeviceNo_result._Fields>, java.io.Serializable, Cloneable, Comparable<isBanDeviceNo_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isBanDeviceNo_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isBanDeviceNo_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isBanDeviceNo_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.banaccount.dto.Result.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isBanDeviceNo_result.class, metaDataMap);
    }

    public isBanDeviceNo_result() {
    }

    public isBanDeviceNo_result(
      com.kugou.fanxing.thrift.banaccount.dto.Result success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isBanDeviceNo_result(isBanDeviceNo_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.banaccount.dto.Result(other.success);
      }
    }

    public isBanDeviceNo_result deepCopy() {
      return new isBanDeviceNo_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result getSuccess() {
      return this.success;
    }

    public isBanDeviceNo_result setSuccess(com.kugou.fanxing.thrift.banaccount.dto.Result success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.banaccount.dto.Result)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isBanDeviceNo_result)
        return this.equals((isBanDeviceNo_result)that);
      return false;
    }

    public boolean equals(isBanDeviceNo_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(isBanDeviceNo_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isBanDeviceNo_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isBanDeviceNo_resultStandardSchemeFactory implements SchemeFactory {
      public isBanDeviceNo_resultStandardScheme getScheme() {
        return new isBanDeviceNo_resultStandardScheme();
      }
    }

    private static class isBanDeviceNo_resultStandardScheme extends StandardScheme<isBanDeviceNo_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isBanDeviceNo_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.banaccount.dto.Result();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isBanDeviceNo_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isBanDeviceNo_resultTupleSchemeFactory implements SchemeFactory {
      public isBanDeviceNo_resultTupleScheme getScheme() {
        return new isBanDeviceNo_resultTupleScheme();
      }
    }

    private static class isBanDeviceNo_resultTupleScheme extends TupleScheme<isBanDeviceNo_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isBanDeviceNo_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isBanDeviceNo_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.banaccount.dto.Result();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class setBanDeviceNo_args implements org.apache.thrift.TBase<setBanDeviceNo_args, setBanDeviceNo_args._Fields>, java.io.Serializable, Cloneable, Comparable<setBanDeviceNo_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("setBanDeviceNo_args");

    private static final org.apache.thrift.protocol.TField BAN_FIELD_DESC = new org.apache.thrift.protocol.TField("ban", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new setBanDeviceNo_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new setBanDeviceNo_argsTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.banaccount.dto.BanX ban; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      BAN((short)1, "ban");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // BAN
            return BAN;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.BAN, new org.apache.thrift.meta_data.FieldMetaData("ban", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.banaccount.dto.BanX.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(setBanDeviceNo_args.class, metaDataMap);
    }

    public setBanDeviceNo_args() {
    }

    public setBanDeviceNo_args(
      com.kugou.fanxing.thrift.banaccount.dto.BanX ban)
    {
      this();
      this.ban = ban;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public setBanDeviceNo_args(setBanDeviceNo_args other) {
      if (other.isSetBan()) {
        this.ban = new com.kugou.fanxing.thrift.banaccount.dto.BanX(other.ban);
      }
    }

    public setBanDeviceNo_args deepCopy() {
      return new setBanDeviceNo_args(this);
    }

    @Override
    public void clear() {
      this.ban = null;
    }

    public com.kugou.fanxing.thrift.banaccount.dto.BanX getBan() {
      return this.ban;
    }

    public setBanDeviceNo_args setBan(com.kugou.fanxing.thrift.banaccount.dto.BanX ban) {
      this.ban = ban;
      return this;
    }

    public void unsetBan() {
      this.ban = null;
    }

    /** Returns true if field ban is set (has been assigned a value) and false otherwise */
    public boolean isSetBan() {
      return this.ban != null;
    }

    public void setBanIsSet(boolean value) {
      if (!value) {
        this.ban = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case BAN:
        if (value == null) {
          unsetBan();
        } else {
          setBan((com.kugou.fanxing.thrift.banaccount.dto.BanX)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case BAN:
        return getBan();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case BAN:
        return isSetBan();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof setBanDeviceNo_args)
        return this.equals((setBanDeviceNo_args)that);
      return false;
    }

    public boolean equals(setBanDeviceNo_args that) {
      if (that == null)
        return false;

      boolean this_present_ban = true && this.isSetBan();
      boolean that_present_ban = true && that.isSetBan();
      if (this_present_ban || that_present_ban) {
        if (!(this_present_ban && that_present_ban))
          return false;
        if (!this.ban.equals(that.ban))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_ban = true && (isSetBan());
      list.add(present_ban);
      if (present_ban)
        list.add(ban);

      return list.hashCode();
    }

    @Override
    public int compareTo(setBanDeviceNo_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetBan()).compareTo(other.isSetBan());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetBan()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ban, other.ban);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("setBanDeviceNo_args(");
      boolean first = true;

      sb.append("ban:");
      if (this.ban == null) {
        sb.append("null");
      } else {
        sb.append(this.ban);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (ban == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'ban' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (ban != null) {
        ban.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class setBanDeviceNo_argsStandardSchemeFactory implements SchemeFactory {
      public setBanDeviceNo_argsStandardScheme getScheme() {
        return new setBanDeviceNo_argsStandardScheme();
      }
    }

    private static class setBanDeviceNo_argsStandardScheme extends StandardScheme<setBanDeviceNo_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, setBanDeviceNo_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // BAN
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.ban = new com.kugou.fanxing.thrift.banaccount.dto.BanX();
                struct.ban.read(iprot);
                struct.setBanIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, setBanDeviceNo_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.ban != null) {
          oprot.writeFieldBegin(BAN_FIELD_DESC);
          struct.ban.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class setBanDeviceNo_argsTupleSchemeFactory implements SchemeFactory {
      public setBanDeviceNo_argsTupleScheme getScheme() {
        return new setBanDeviceNo_argsTupleScheme();
      }
    }

    private static class setBanDeviceNo_argsTupleScheme extends TupleScheme<setBanDeviceNo_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, setBanDeviceNo_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.ban.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, setBanDeviceNo_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.ban = new com.kugou.fanxing.thrift.banaccount.dto.BanX();
        struct.ban.read(iprot);
        struct.setBanIsSet(true);
      }
    }

  }

  public static class setBanDeviceNo_result implements org.apache.thrift.TBase<setBanDeviceNo_result, setBanDeviceNo_result._Fields>, java.io.Serializable, Cloneable, Comparable<setBanDeviceNo_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("setBanDeviceNo_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new setBanDeviceNo_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new setBanDeviceNo_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.banaccount.dto.OperateResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.banaccount.dto.OperateResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(setBanDeviceNo_result.class, metaDataMap);
    }

    public setBanDeviceNo_result() {
    }

    public setBanDeviceNo_result(
      com.kugou.fanxing.thrift.banaccount.dto.OperateResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public setBanDeviceNo_result(setBanDeviceNo_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.banaccount.dto.OperateResult(other.success);
      }
    }

    public setBanDeviceNo_result deepCopy() {
      return new setBanDeviceNo_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.banaccount.dto.OperateResult getSuccess() {
      return this.success;
    }

    public setBanDeviceNo_result setSuccess(com.kugou.fanxing.thrift.banaccount.dto.OperateResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.banaccount.dto.OperateResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof setBanDeviceNo_result)
        return this.equals((setBanDeviceNo_result)that);
      return false;
    }

    public boolean equals(setBanDeviceNo_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(setBanDeviceNo_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("setBanDeviceNo_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class setBanDeviceNo_resultStandardSchemeFactory implements SchemeFactory {
      public setBanDeviceNo_resultStandardScheme getScheme() {
        return new setBanDeviceNo_resultStandardScheme();
      }
    }

    private static class setBanDeviceNo_resultStandardScheme extends StandardScheme<setBanDeviceNo_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, setBanDeviceNo_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.banaccount.dto.OperateResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, setBanDeviceNo_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class setBanDeviceNo_resultTupleSchemeFactory implements SchemeFactory {
      public setBanDeviceNo_resultTupleScheme getScheme() {
        return new setBanDeviceNo_resultTupleScheme();
      }
    }

    private static class setBanDeviceNo_resultTupleScheme extends TupleScheme<setBanDeviceNo_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, setBanDeviceNo_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, setBanDeviceNo_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.banaccount.dto.OperateResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class isBanAccountOrIp_args implements org.apache.thrift.TBase<isBanAccountOrIp_args, isBanAccountOrIp_args._Fields>, java.io.Serializable, Cloneable, Comparable<isBanAccountOrIp_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isBanAccountOrIp_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);
    private static final org.apache.thrift.protocol.TField IP_FIELD_DESC = new org.apache.thrift.protocol.TField("ip", org.apache.thrift.protocol.TType.STRING, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isBanAccountOrIp_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isBanAccountOrIp_argsTupleSchemeFactory());
    }

    public long kugouId; // required
    public String ip; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId"),
      IP((short)2, "ip");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          case 2: // IP
            return IP;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.IP, new org.apache.thrift.meta_data.FieldMetaData("ip", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isBanAccountOrIp_args.class, metaDataMap);
    }

    public isBanAccountOrIp_args() {
    }

    public isBanAccountOrIp_args(
      long kugouId,
      String ip)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      this.ip = ip;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isBanAccountOrIp_args(isBanAccountOrIp_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
      if (other.isSetIp()) {
        this.ip = other.ip;
      }
    }

    public isBanAccountOrIp_args deepCopy() {
      return new isBanAccountOrIp_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
      this.ip = null;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public isBanAccountOrIp_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public String getIp() {
      return this.ip;
    }

    public isBanAccountOrIp_args setIp(String ip) {
      this.ip = ip;
      return this;
    }

    public void unsetIp() {
      this.ip = null;
    }

    /** Returns true if field ip is set (has been assigned a value) and false otherwise */
    public boolean isSetIp() {
      return this.ip != null;
    }

    public void setIpIsSet(boolean value) {
      if (!value) {
        this.ip = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      case IP:
        if (value == null) {
          unsetIp();
        } else {
          setIp((String)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      case IP:
        return getIp();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      case IP:
        return isSetIp();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isBanAccountOrIp_args)
        return this.equals((isBanAccountOrIp_args)that);
      return false;
    }

    public boolean equals(isBanAccountOrIp_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      boolean this_present_ip = true && this.isSetIp();
      boolean that_present_ip = true && that.isSetIp();
      if (this_present_ip || that_present_ip) {
        if (!(this_present_ip && that_present_ip))
          return false;
        if (!this.ip.equals(that.ip))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      boolean present_ip = true && (isSetIp());
      list.add(present_ip);
      if (present_ip)
        list.add(ip);

      return list.hashCode();
    }

    @Override
    public int compareTo(isBanAccountOrIp_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetIp()).compareTo(other.isSetIp());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetIp()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ip, other.ip);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isBanAccountOrIp_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      if (!first) sb.append(", ");
      sb.append("ip:");
      if (this.ip == null) {
        sb.append("null");
      } else {
        sb.append(this.ip);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
      if (ip == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'ip' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isBanAccountOrIp_argsStandardSchemeFactory implements SchemeFactory {
      public isBanAccountOrIp_argsStandardScheme getScheme() {
        return new isBanAccountOrIp_argsStandardScheme();
      }
    }

    private static class isBanAccountOrIp_argsStandardScheme extends StandardScheme<isBanAccountOrIp_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isBanAccountOrIp_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // IP
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.ip = iprot.readString();
                struct.setIpIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        if (!struct.isSetKugouId()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
        }
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isBanAccountOrIp_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        if (struct.ip != null) {
          oprot.writeFieldBegin(IP_FIELD_DESC);
          oprot.writeString(struct.ip);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isBanAccountOrIp_argsTupleSchemeFactory implements SchemeFactory {
      public isBanAccountOrIp_argsTupleScheme getScheme() {
        return new isBanAccountOrIp_argsTupleScheme();
      }
    }

    private static class isBanAccountOrIp_argsTupleScheme extends TupleScheme<isBanAccountOrIp_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isBanAccountOrIp_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        oprot.writeI64(struct.kugouId);
        oprot.writeString(struct.ip);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isBanAccountOrIp_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.kugouId = iprot.readI64();
        struct.setKugouIdIsSet(true);
        struct.ip = iprot.readString();
        struct.setIpIsSet(true);
      }
    }

  }

  public static class isBanAccountOrIp_result implements org.apache.thrift.TBase<isBanAccountOrIp_result, isBanAccountOrIp_result._Fields>, java.io.Serializable, Cloneable, Comparable<isBanAccountOrIp_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isBanAccountOrIp_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isBanAccountOrIp_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isBanAccountOrIp_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.banaccount.dto.Result.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isBanAccountOrIp_result.class, metaDataMap);
    }

    public isBanAccountOrIp_result() {
    }

    public isBanAccountOrIp_result(
      com.kugou.fanxing.thrift.banaccount.dto.Result success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isBanAccountOrIp_result(isBanAccountOrIp_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.banaccount.dto.Result(other.success);
      }
    }

    public isBanAccountOrIp_result deepCopy() {
      return new isBanAccountOrIp_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result getSuccess() {
      return this.success;
    }

    public isBanAccountOrIp_result setSuccess(com.kugou.fanxing.thrift.banaccount.dto.Result success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.banaccount.dto.Result)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isBanAccountOrIp_result)
        return this.equals((isBanAccountOrIp_result)that);
      return false;
    }

    public boolean equals(isBanAccountOrIp_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(isBanAccountOrIp_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isBanAccountOrIp_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isBanAccountOrIp_resultStandardSchemeFactory implements SchemeFactory {
      public isBanAccountOrIp_resultStandardScheme getScheme() {
        return new isBanAccountOrIp_resultStandardScheme();
      }
    }

    private static class isBanAccountOrIp_resultStandardScheme extends StandardScheme<isBanAccountOrIp_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isBanAccountOrIp_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.banaccount.dto.Result();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isBanAccountOrIp_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isBanAccountOrIp_resultTupleSchemeFactory implements SchemeFactory {
      public isBanAccountOrIp_resultTupleScheme getScheme() {
        return new isBanAccountOrIp_resultTupleScheme();
      }
    }

    private static class isBanAccountOrIp_resultTupleScheme extends TupleScheme<isBanAccountOrIp_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isBanAccountOrIp_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isBanAccountOrIp_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.banaccount.dto.Result();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class isBanAccountOrDeviceNo_args implements org.apache.thrift.TBase<isBanAccountOrDeviceNo_args, isBanAccountOrDeviceNo_args._Fields>, java.io.Serializable, Cloneable, Comparable<isBanAccountOrDeviceNo_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isBanAccountOrDeviceNo_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);
    private static final org.apache.thrift.protocol.TField DEVICE_NO_FIELD_DESC = new org.apache.thrift.protocol.TField("deviceNo", org.apache.thrift.protocol.TType.STRING, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isBanAccountOrDeviceNo_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isBanAccountOrDeviceNo_argsTupleSchemeFactory());
    }

    public long kugouId; // required
    public String deviceNo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId"),
      DEVICE_NO((short)2, "deviceNo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          case 2: // DEVICE_NO
            return DEVICE_NO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.DEVICE_NO, new org.apache.thrift.meta_data.FieldMetaData("deviceNo", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isBanAccountOrDeviceNo_args.class, metaDataMap);
    }

    public isBanAccountOrDeviceNo_args() {
    }

    public isBanAccountOrDeviceNo_args(
      long kugouId,
      String deviceNo)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      this.deviceNo = deviceNo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isBanAccountOrDeviceNo_args(isBanAccountOrDeviceNo_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
      if (other.isSetDeviceNo()) {
        this.deviceNo = other.deviceNo;
      }
    }

    public isBanAccountOrDeviceNo_args deepCopy() {
      return new isBanAccountOrDeviceNo_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
      this.deviceNo = null;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public isBanAccountOrDeviceNo_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public String getDeviceNo() {
      return this.deviceNo;
    }

    public isBanAccountOrDeviceNo_args setDeviceNo(String deviceNo) {
      this.deviceNo = deviceNo;
      return this;
    }

    public void unsetDeviceNo() {
      this.deviceNo = null;
    }

    /** Returns true if field deviceNo is set (has been assigned a value) and false otherwise */
    public boolean isSetDeviceNo() {
      return this.deviceNo != null;
    }

    public void setDeviceNoIsSet(boolean value) {
      if (!value) {
        this.deviceNo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      case DEVICE_NO:
        if (value == null) {
          unsetDeviceNo();
        } else {
          setDeviceNo((String)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      case DEVICE_NO:
        return getDeviceNo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      case DEVICE_NO:
        return isSetDeviceNo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isBanAccountOrDeviceNo_args)
        return this.equals((isBanAccountOrDeviceNo_args)that);
      return false;
    }

    public boolean equals(isBanAccountOrDeviceNo_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      boolean this_present_deviceNo = true && this.isSetDeviceNo();
      boolean that_present_deviceNo = true && that.isSetDeviceNo();
      if (this_present_deviceNo || that_present_deviceNo) {
        if (!(this_present_deviceNo && that_present_deviceNo))
          return false;
        if (!this.deviceNo.equals(that.deviceNo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      boolean present_deviceNo = true && (isSetDeviceNo());
      list.add(present_deviceNo);
      if (present_deviceNo)
        list.add(deviceNo);

      return list.hashCode();
    }

    @Override
    public int compareTo(isBanAccountOrDeviceNo_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetDeviceNo()).compareTo(other.isSetDeviceNo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetDeviceNo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.deviceNo, other.deviceNo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isBanAccountOrDeviceNo_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      if (!first) sb.append(", ");
      sb.append("deviceNo:");
      if (this.deviceNo == null) {
        sb.append("null");
      } else {
        sb.append(this.deviceNo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
      if (deviceNo == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'deviceNo' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isBanAccountOrDeviceNo_argsStandardSchemeFactory implements SchemeFactory {
      public isBanAccountOrDeviceNo_argsStandardScheme getScheme() {
        return new isBanAccountOrDeviceNo_argsStandardScheme();
      }
    }

    private static class isBanAccountOrDeviceNo_argsStandardScheme extends StandardScheme<isBanAccountOrDeviceNo_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isBanAccountOrDeviceNo_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // DEVICE_NO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.deviceNo = iprot.readString();
                struct.setDeviceNoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        if (!struct.isSetKugouId()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
        }
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isBanAccountOrDeviceNo_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        if (struct.deviceNo != null) {
          oprot.writeFieldBegin(DEVICE_NO_FIELD_DESC);
          oprot.writeString(struct.deviceNo);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isBanAccountOrDeviceNo_argsTupleSchemeFactory implements SchemeFactory {
      public isBanAccountOrDeviceNo_argsTupleScheme getScheme() {
        return new isBanAccountOrDeviceNo_argsTupleScheme();
      }
    }

    private static class isBanAccountOrDeviceNo_argsTupleScheme extends TupleScheme<isBanAccountOrDeviceNo_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isBanAccountOrDeviceNo_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        oprot.writeI64(struct.kugouId);
        oprot.writeString(struct.deviceNo);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isBanAccountOrDeviceNo_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.kugouId = iprot.readI64();
        struct.setKugouIdIsSet(true);
        struct.deviceNo = iprot.readString();
        struct.setDeviceNoIsSet(true);
      }
    }

  }

  public static class isBanAccountOrDeviceNo_result implements org.apache.thrift.TBase<isBanAccountOrDeviceNo_result, isBanAccountOrDeviceNo_result._Fields>, java.io.Serializable, Cloneable, Comparable<isBanAccountOrDeviceNo_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isBanAccountOrDeviceNo_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isBanAccountOrDeviceNo_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isBanAccountOrDeviceNo_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.banaccount.dto.Result.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isBanAccountOrDeviceNo_result.class, metaDataMap);
    }

    public isBanAccountOrDeviceNo_result() {
    }

    public isBanAccountOrDeviceNo_result(
      com.kugou.fanxing.thrift.banaccount.dto.Result success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isBanAccountOrDeviceNo_result(isBanAccountOrDeviceNo_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.banaccount.dto.Result(other.success);
      }
    }

    public isBanAccountOrDeviceNo_result deepCopy() {
      return new isBanAccountOrDeviceNo_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.banaccount.dto.Result getSuccess() {
      return this.success;
    }

    public isBanAccountOrDeviceNo_result setSuccess(com.kugou.fanxing.thrift.banaccount.dto.Result success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.banaccount.dto.Result)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isBanAccountOrDeviceNo_result)
        return this.equals((isBanAccountOrDeviceNo_result)that);
      return false;
    }

    public boolean equals(isBanAccountOrDeviceNo_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(isBanAccountOrDeviceNo_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isBanAccountOrDeviceNo_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isBanAccountOrDeviceNo_resultStandardSchemeFactory implements SchemeFactory {
      public isBanAccountOrDeviceNo_resultStandardScheme getScheme() {
        return new isBanAccountOrDeviceNo_resultStandardScheme();
      }
    }

    private static class isBanAccountOrDeviceNo_resultStandardScheme extends StandardScheme<isBanAccountOrDeviceNo_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isBanAccountOrDeviceNo_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.banaccount.dto.Result();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isBanAccountOrDeviceNo_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isBanAccountOrDeviceNo_resultTupleSchemeFactory implements SchemeFactory {
      public isBanAccountOrDeviceNo_resultTupleScheme getScheme() {
        return new isBanAccountOrDeviceNo_resultTupleScheme();
      }
    }

    private static class isBanAccountOrDeviceNo_resultTupleScheme extends TupleScheme<isBanAccountOrDeviceNo_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isBanAccountOrDeviceNo_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isBanAccountOrDeviceNo_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.banaccount.dto.Result();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class listBanAccountInfo_args implements org.apache.thrift.TBase<listBanAccountInfo_args, listBanAccountInfo_args._Fields>, java.io.Serializable, Cloneable, Comparable<listBanAccountInfo_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("listBanAccountInfo_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouIdList", org.apache.thrift.protocol.TType.LIST, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new listBanAccountInfo_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new listBanAccountInfo_argsTupleSchemeFactory());
    }

    public List<Long> kugouIdList; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID_LIST((short)1, "kugouIdList");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID_LIST
            return KUGOU_ID_LIST;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID_LIST, new org.apache.thrift.meta_data.FieldMetaData("kugouIdList", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
              new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(listBanAccountInfo_args.class, metaDataMap);
    }

    public listBanAccountInfo_args() {
    }

    public listBanAccountInfo_args(
      List<Long> kugouIdList)
    {
      this();
      this.kugouIdList = kugouIdList;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public listBanAccountInfo_args(listBanAccountInfo_args other) {
      if (other.isSetKugouIdList()) {
        List<Long> __this__kugouIdList = new ArrayList<Long>(other.kugouIdList);
        this.kugouIdList = __this__kugouIdList;
      }
    }

    public listBanAccountInfo_args deepCopy() {
      return new listBanAccountInfo_args(this);
    }

    @Override
    public void clear() {
      this.kugouIdList = null;
    }

    public int getKugouIdListSize() {
      return (this.kugouIdList == null) ? 0 : this.kugouIdList.size();
    }

    public java.util.Iterator<Long> getKugouIdListIterator() {
      return (this.kugouIdList == null) ? null : this.kugouIdList.iterator();
    }

    public void addToKugouIdList(long elem) {
      if (this.kugouIdList == null) {
        this.kugouIdList = new ArrayList<Long>();
      }
      this.kugouIdList.add(elem);
    }

    public List<Long> getKugouIdList() {
      return this.kugouIdList;
    }

    public listBanAccountInfo_args setKugouIdList(List<Long> kugouIdList) {
      this.kugouIdList = kugouIdList;
      return this;
    }

    public void unsetKugouIdList() {
      this.kugouIdList = null;
    }

    /** Returns true if field kugouIdList is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouIdList() {
      return this.kugouIdList != null;
    }

    public void setKugouIdListIsSet(boolean value) {
      if (!value) {
        this.kugouIdList = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID_LIST:
        if (value == null) {
          unsetKugouIdList();
        } else {
          setKugouIdList((List<Long>)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID_LIST:
        return getKugouIdList();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID_LIST:
        return isSetKugouIdList();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof listBanAccountInfo_args)
        return this.equals((listBanAccountInfo_args)that);
      return false;
    }

    public boolean equals(listBanAccountInfo_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouIdList = true && this.isSetKugouIdList();
      boolean that_present_kugouIdList = true && that.isSetKugouIdList();
      if (this_present_kugouIdList || that_present_kugouIdList) {
        if (!(this_present_kugouIdList && that_present_kugouIdList))
          return false;
        if (!this.kugouIdList.equals(that.kugouIdList))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouIdList = true && (isSetKugouIdList());
      list.add(present_kugouIdList);
      if (present_kugouIdList)
        list.add(kugouIdList);

      return list.hashCode();
    }

    @Override
    public int compareTo(listBanAccountInfo_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouIdList()).compareTo(other.isSetKugouIdList());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouIdList()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouIdList, other.kugouIdList);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("listBanAccountInfo_args(");
      boolean first = true;

      sb.append("kugouIdList:");
      if (this.kugouIdList == null) {
        sb.append("null");
      } else {
        sb.append(this.kugouIdList);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class listBanAccountInfo_argsStandardSchemeFactory implements SchemeFactory {
      public listBanAccountInfo_argsStandardScheme getScheme() {
        return new listBanAccountInfo_argsStandardScheme();
      }
    }

    private static class listBanAccountInfo_argsStandardScheme extends StandardScheme<listBanAccountInfo_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, listBanAccountInfo_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID_LIST
              if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                {
                  org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                  struct.kugouIdList = new ArrayList<Long>(_list0.size);
                  long _elem1;
                  for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                  {
                    _elem1 = iprot.readI64();
                    struct.kugouIdList.add(_elem1);
                  }
                  iprot.readListEnd();
                }
                struct.setKugouIdListIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, listBanAccountInfo_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.kugouIdList != null) {
          oprot.writeFieldBegin(KUGOU_ID_LIST_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.kugouIdList.size()));
            for (long _iter3 : struct.kugouIdList)
            {
              oprot.writeI64(_iter3);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class listBanAccountInfo_argsTupleSchemeFactory implements SchemeFactory {
      public listBanAccountInfo_argsTupleScheme getScheme() {
        return new listBanAccountInfo_argsTupleScheme();
      }
    }

    private static class listBanAccountInfo_argsTupleScheme extends TupleScheme<listBanAccountInfo_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, listBanAccountInfo_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouIdList()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetKugouIdList()) {
          {
            oprot.writeI32(struct.kugouIdList.size());
            for (long _iter4 : struct.kugouIdList)
            {
              oprot.writeI64(_iter4);
            }
          }
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, listBanAccountInfo_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          {
            org.apache.thrift.protocol.TList _list5 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
            struct.kugouIdList = new ArrayList<Long>(_list5.size);
            long _elem6;
            for (int _i7 = 0; _i7 < _list5.size; ++_i7)
            {
              _elem6 = iprot.readI64();
              struct.kugouIdList.add(_elem6);
            }
          }
          struct.setKugouIdListIsSet(true);
        }
      }
    }

  }

  public static class listBanAccountInfo_result implements org.apache.thrift.TBase<listBanAccountInfo_result, listBanAccountInfo_result._Fields>, java.io.Serializable, Cloneable, Comparable<listBanAccountInfo_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("listBanAccountInfo_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new listBanAccountInfo_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new listBanAccountInfo_resultTupleSchemeFactory());
    }

    public com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(listBanAccountInfo_result.class, metaDataMap);
    }

    public listBanAccountInfo_result() {
    }

    public listBanAccountInfo_result(
      com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public listBanAccountInfo_result(listBanAccountInfo_result other) {
      if (other.isSetSuccess()) {
        this.success = new com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp(other.success);
      }
    }

    public listBanAccountInfo_result deepCopy() {
      return new listBanAccountInfo_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp getSuccess() {
      return this.success;
    }

    public listBanAccountInfo_result setSuccess(com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof listBanAccountInfo_result)
        return this.equals((listBanAccountInfo_result)that);
      return false;
    }

    public boolean equals(listBanAccountInfo_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(listBanAccountInfo_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("listBanAccountInfo_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class listBanAccountInfo_resultStandardSchemeFactory implements SchemeFactory {
      public listBanAccountInfo_resultStandardScheme getScheme() {
        return new listBanAccountInfo_resultStandardScheme();
      }
    }

    private static class listBanAccountInfo_resultStandardScheme extends StandardScheme<listBanAccountInfo_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, listBanAccountInfo_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, listBanAccountInfo_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class listBanAccountInfo_resultTupleSchemeFactory implements SchemeFactory {
      public listBanAccountInfo_resultTupleScheme getScheme() {
        return new listBanAccountInfo_resultTupleScheme();
      }
    }

    private static class listBanAccountInfo_resultTupleScheme extends TupleScheme<listBanAccountInfo_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, listBanAccountInfo_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, listBanAccountInfo_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new com.kugou.fanxing.thrift.banaccount.dto.ResultMutiBanAccountResp();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

}
