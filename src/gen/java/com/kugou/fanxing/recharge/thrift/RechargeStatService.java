/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.recharge.thrift;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2020-08-19")
public class RechargeStatService {

  public interface Iface {

    /**
     * 获取用户前一年的充值金额
     * 
     * @param userYearRechargeStatRequest
     */
    public UserYearRechargeStatResponse userYearRechargeStat(UserYearRechargeStatRequest userYearRechargeStatRequest) throws org.apache.thrift.TException;

    /**
     * 获取用户历史总充值额 -- 单位星币
     * 
     * @param userEverRechargeRequest
     */
    public UserEverRechargeResponse userEverRecharge(UserEverRechargeRequest userEverRechargeRequest) throws org.apache.thrift.TException;

  }

  public interface AsyncIface {

    public void userYearRechargeStat(UserYearRechargeStatRequest userYearRechargeStatRequest, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void userEverRecharge(UserEverRechargeRequest userEverRechargeRequest, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

  }

  public static class Client extends org.apache.thrift.TServiceClient implements Iface {
    public static class Factory implements org.apache.thrift.TServiceClientFactory<Client> {
      public Factory() {}
      public Client getClient(org.apache.thrift.protocol.TProtocol prot) {
        return new Client(prot);
      }
      public Client getClient(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
        return new Client(iprot, oprot);
      }
    }

    public Client(org.apache.thrift.protocol.TProtocol prot)
    {
      super(prot, prot);
    }

    public Client(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
      super(iprot, oprot);
    }

    public UserYearRechargeStatResponse userYearRechargeStat(UserYearRechargeStatRequest userYearRechargeStatRequest) throws org.apache.thrift.TException
    {
      send_userYearRechargeStat(userYearRechargeStatRequest);
      return recv_userYearRechargeStat();
    }

    public void send_userYearRechargeStat(UserYearRechargeStatRequest userYearRechargeStatRequest) throws org.apache.thrift.TException
    {
      userYearRechargeStat_args args = new userYearRechargeStat_args();
      args.setUserYearRechargeStatRequest(userYearRechargeStatRequest);
      sendBase("userYearRechargeStat", args);
    }

    public UserYearRechargeStatResponse recv_userYearRechargeStat() throws org.apache.thrift.TException
    {
      userYearRechargeStat_result result = new userYearRechargeStat_result();
      receiveBase(result, "userYearRechargeStat");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "userYearRechargeStat failed: unknown result");
    }

    public UserEverRechargeResponse userEverRecharge(UserEverRechargeRequest userEverRechargeRequest) throws org.apache.thrift.TException
    {
      send_userEverRecharge(userEverRechargeRequest);
      return recv_userEverRecharge();
    }

    public void send_userEverRecharge(UserEverRechargeRequest userEverRechargeRequest) throws org.apache.thrift.TException
    {
      userEverRecharge_args args = new userEverRecharge_args();
      args.setUserEverRechargeRequest(userEverRechargeRequest);
      sendBase("userEverRecharge", args);
    }

    public UserEverRechargeResponse recv_userEverRecharge() throws org.apache.thrift.TException
    {
      userEverRecharge_result result = new userEverRecharge_result();
      receiveBase(result, "userEverRecharge");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "userEverRecharge failed: unknown result");
    }

  }
  public static class AsyncClient extends org.apache.thrift.async.TAsyncClient implements AsyncIface {
    public static class Factory implements org.apache.thrift.async.TAsyncClientFactory<AsyncClient> {
      private org.apache.thrift.async.TAsyncClientManager clientManager;
      private org.apache.thrift.protocol.TProtocolFactory protocolFactory;
      public Factory(org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.protocol.TProtocolFactory protocolFactory) {
        this.clientManager = clientManager;
        this.protocolFactory = protocolFactory;
      }
      public AsyncClient getAsyncClient(org.apache.thrift.transport.TNonblockingTransport transport) {
        return new AsyncClient(protocolFactory, clientManager, transport);
      }
    }

    public AsyncClient(org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.transport.TNonblockingTransport transport) {
      super(protocolFactory, clientManager, transport);
    }

    public void userYearRechargeStat(UserYearRechargeStatRequest userYearRechargeStatRequest, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      userYearRechargeStat_call method_call = new userYearRechargeStat_call(userYearRechargeStatRequest, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class userYearRechargeStat_call extends org.apache.thrift.async.TAsyncMethodCall {
      private UserYearRechargeStatRequest userYearRechargeStatRequest;
      public userYearRechargeStat_call(UserYearRechargeStatRequest userYearRechargeStatRequest, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.userYearRechargeStatRequest = userYearRechargeStatRequest;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("userYearRechargeStat", org.apache.thrift.protocol.TMessageType.CALL, 0));
        userYearRechargeStat_args args = new userYearRechargeStat_args();
        args.setUserYearRechargeStatRequest(userYearRechargeStatRequest);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public UserYearRechargeStatResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_userYearRechargeStat();
      }
    }

    public void userEverRecharge(UserEverRechargeRequest userEverRechargeRequest, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      userEverRecharge_call method_call = new userEverRecharge_call(userEverRechargeRequest, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class userEverRecharge_call extends org.apache.thrift.async.TAsyncMethodCall {
      private UserEverRechargeRequest userEverRechargeRequest;
      public userEverRecharge_call(UserEverRechargeRequest userEverRechargeRequest, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.userEverRechargeRequest = userEverRechargeRequest;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("userEverRecharge", org.apache.thrift.protocol.TMessageType.CALL, 0));
        userEverRecharge_args args = new userEverRecharge_args();
        args.setUserEverRechargeRequest(userEverRechargeRequest);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public UserEverRechargeResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_userEverRecharge();
      }
    }

  }

  public static class Processor<I extends Iface> extends org.apache.thrift.TBaseProcessor<I> implements org.apache.thrift.TProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());
    public Processor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>>()));
    }

    protected Processor(I iface, Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends Iface> Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> getProcessMap(Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      processMap.put("userYearRechargeStat", new userYearRechargeStat());
      processMap.put("userEverRecharge", new userEverRecharge());
      return processMap;
    }

    public static class userYearRechargeStat<I extends Iface> extends org.apache.thrift.ProcessFunction<I, userYearRechargeStat_args> {
      public userYearRechargeStat() {
        super("userYearRechargeStat");
      }

      public userYearRechargeStat_args getEmptyArgsInstance() {
        return new userYearRechargeStat_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public userYearRechargeStat_result getResult(I iface, userYearRechargeStat_args args) throws org.apache.thrift.TException {
        userYearRechargeStat_result result = new userYearRechargeStat_result();
        result.success = iface.userYearRechargeStat(args.userYearRechargeStatRequest);
        return result;
      }
    }

    public static class userEverRecharge<I extends Iface> extends org.apache.thrift.ProcessFunction<I, userEverRecharge_args> {
      public userEverRecharge() {
        super("userEverRecharge");
      }

      public userEverRecharge_args getEmptyArgsInstance() {
        return new userEverRecharge_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public userEverRecharge_result getResult(I iface, userEverRecharge_args args) throws org.apache.thrift.TException {
        userEverRecharge_result result = new userEverRecharge_result();
        result.success = iface.userEverRecharge(args.userEverRechargeRequest);
        return result;
      }
    }

  }

  public static class AsyncProcessor<I extends AsyncIface> extends org.apache.thrift.TBaseAsyncProcessor<I> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());
    public AsyncProcessor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.AsyncProcessFunction<I, ? extends org.apache.thrift.TBase, ?>>()));
    }

    protected AsyncProcessor(I iface, Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends AsyncIface> Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase,?>> getProcessMap(Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      processMap.put("userYearRechargeStat", new userYearRechargeStat());
      processMap.put("userEverRecharge", new userEverRecharge());
      return processMap;
    }

    public static class userYearRechargeStat<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, userYearRechargeStat_args, UserYearRechargeStatResponse> {
      public userYearRechargeStat() {
        super("userYearRechargeStat");
      }

      public userYearRechargeStat_args getEmptyArgsInstance() {
        return new userYearRechargeStat_args();
      }

      public AsyncMethodCallback<UserYearRechargeStatResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<UserYearRechargeStatResponse>() { 
          public void onComplete(UserYearRechargeStatResponse o) {
            userYearRechargeStat_result result = new userYearRechargeStat_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            userYearRechargeStat_result result = new userYearRechargeStat_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, userYearRechargeStat_args args, org.apache.thrift.async.AsyncMethodCallback<UserYearRechargeStatResponse> resultHandler) throws TException {
        iface.userYearRechargeStat(args.userYearRechargeStatRequest,resultHandler);
      }
    }

    public static class userEverRecharge<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, userEverRecharge_args, UserEverRechargeResponse> {
      public userEverRecharge() {
        super("userEverRecharge");
      }

      public userEverRecharge_args getEmptyArgsInstance() {
        return new userEverRecharge_args();
      }

      public AsyncMethodCallback<UserEverRechargeResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<UserEverRechargeResponse>() { 
          public void onComplete(UserEverRechargeResponse o) {
            userEverRecharge_result result = new userEverRecharge_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            userEverRecharge_result result = new userEverRecharge_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, userEverRecharge_args args, org.apache.thrift.async.AsyncMethodCallback<UserEverRechargeResponse> resultHandler) throws TException {
        iface.userEverRecharge(args.userEverRechargeRequest,resultHandler);
      }
    }

  }

  public static class userYearRechargeStat_args implements org.apache.thrift.TBase<userYearRechargeStat_args, userYearRechargeStat_args._Fields>, java.io.Serializable, Cloneable, Comparable<userYearRechargeStat_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("userYearRechargeStat_args");

    private static final org.apache.thrift.protocol.TField USER_YEAR_RECHARGE_STAT_REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("userYearRechargeStatRequest", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new userYearRechargeStat_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new userYearRechargeStat_argsTupleSchemeFactory());
    }

    public UserYearRechargeStatRequest userYearRechargeStatRequest; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      USER_YEAR_RECHARGE_STAT_REQUEST((short)1, "userYearRechargeStatRequest");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // USER_YEAR_RECHARGE_STAT_REQUEST
            return USER_YEAR_RECHARGE_STAT_REQUEST;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.USER_YEAR_RECHARGE_STAT_REQUEST, new org.apache.thrift.meta_data.FieldMetaData("userYearRechargeStatRequest", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, UserYearRechargeStatRequest.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(userYearRechargeStat_args.class, metaDataMap);
    }

    public userYearRechargeStat_args() {
    }

    public userYearRechargeStat_args(
      UserYearRechargeStatRequest userYearRechargeStatRequest)
    {
      this();
      this.userYearRechargeStatRequest = userYearRechargeStatRequest;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public userYearRechargeStat_args(userYearRechargeStat_args other) {
      if (other.isSetUserYearRechargeStatRequest()) {
        this.userYearRechargeStatRequest = new UserYearRechargeStatRequest(other.userYearRechargeStatRequest);
      }
    }

    public userYearRechargeStat_args deepCopy() {
      return new userYearRechargeStat_args(this);
    }

    @Override
    public void clear() {
      this.userYearRechargeStatRequest = null;
    }

    public UserYearRechargeStatRequest getUserYearRechargeStatRequest() {
      return this.userYearRechargeStatRequest;
    }

    public userYearRechargeStat_args setUserYearRechargeStatRequest(UserYearRechargeStatRequest userYearRechargeStatRequest) {
      this.userYearRechargeStatRequest = userYearRechargeStatRequest;
      return this;
    }

    public void unsetUserYearRechargeStatRequest() {
      this.userYearRechargeStatRequest = null;
    }

    /** Returns true if field userYearRechargeStatRequest is set (has been assigned a value) and false otherwise */
    public boolean isSetUserYearRechargeStatRequest() {
      return this.userYearRechargeStatRequest != null;
    }

    public void setUserYearRechargeStatRequestIsSet(boolean value) {
      if (!value) {
        this.userYearRechargeStatRequest = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case USER_YEAR_RECHARGE_STAT_REQUEST:
        if (value == null) {
          unsetUserYearRechargeStatRequest();
        } else {
          setUserYearRechargeStatRequest((UserYearRechargeStatRequest)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case USER_YEAR_RECHARGE_STAT_REQUEST:
        return getUserYearRechargeStatRequest();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case USER_YEAR_RECHARGE_STAT_REQUEST:
        return isSetUserYearRechargeStatRequest();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof userYearRechargeStat_args)
        return this.equals((userYearRechargeStat_args)that);
      return false;
    }

    public boolean equals(userYearRechargeStat_args that) {
      if (that == null)
        return false;

      boolean this_present_userYearRechargeStatRequest = true && this.isSetUserYearRechargeStatRequest();
      boolean that_present_userYearRechargeStatRequest = true && that.isSetUserYearRechargeStatRequest();
      if (this_present_userYearRechargeStatRequest || that_present_userYearRechargeStatRequest) {
        if (!(this_present_userYearRechargeStatRequest && that_present_userYearRechargeStatRequest))
          return false;
        if (!this.userYearRechargeStatRequest.equals(that.userYearRechargeStatRequest))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_userYearRechargeStatRequest = true && (isSetUserYearRechargeStatRequest());
      list.add(present_userYearRechargeStatRequest);
      if (present_userYearRechargeStatRequest)
        list.add(userYearRechargeStatRequest);

      return list.hashCode();
    }

    @Override
    public int compareTo(userYearRechargeStat_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetUserYearRechargeStatRequest()).compareTo(other.isSetUserYearRechargeStatRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetUserYearRechargeStatRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.userYearRechargeStatRequest, other.userYearRechargeStatRequest);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("userYearRechargeStat_args(");
      boolean first = true;

      sb.append("userYearRechargeStatRequest:");
      if (this.userYearRechargeStatRequest == null) {
        sb.append("null");
      } else {
        sb.append(this.userYearRechargeStatRequest);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (userYearRechargeStatRequest != null) {
        userYearRechargeStatRequest.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class userYearRechargeStat_argsStandardSchemeFactory implements SchemeFactory {
      public userYearRechargeStat_argsStandardScheme getScheme() {
        return new userYearRechargeStat_argsStandardScheme();
      }
    }

    private static class userYearRechargeStat_argsStandardScheme extends StandardScheme<userYearRechargeStat_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, userYearRechargeStat_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // USER_YEAR_RECHARGE_STAT_REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.userYearRechargeStatRequest = new UserYearRechargeStatRequest();
                struct.userYearRechargeStatRequest.read(iprot);
                struct.setUserYearRechargeStatRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, userYearRechargeStat_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.userYearRechargeStatRequest != null) {
          oprot.writeFieldBegin(USER_YEAR_RECHARGE_STAT_REQUEST_FIELD_DESC);
          struct.userYearRechargeStatRequest.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class userYearRechargeStat_argsTupleSchemeFactory implements SchemeFactory {
      public userYearRechargeStat_argsTupleScheme getScheme() {
        return new userYearRechargeStat_argsTupleScheme();
      }
    }

    private static class userYearRechargeStat_argsTupleScheme extends TupleScheme<userYearRechargeStat_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, userYearRechargeStat_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetUserYearRechargeStatRequest()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetUserYearRechargeStatRequest()) {
          struct.userYearRechargeStatRequest.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, userYearRechargeStat_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.userYearRechargeStatRequest = new UserYearRechargeStatRequest();
          struct.userYearRechargeStatRequest.read(iprot);
          struct.setUserYearRechargeStatRequestIsSet(true);
        }
      }
    }

  }

  public static class userYearRechargeStat_result implements org.apache.thrift.TBase<userYearRechargeStat_result, userYearRechargeStat_result._Fields>, java.io.Serializable, Cloneable, Comparable<userYearRechargeStat_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("userYearRechargeStat_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new userYearRechargeStat_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new userYearRechargeStat_resultTupleSchemeFactory());
    }

    public UserYearRechargeStatResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, UserYearRechargeStatResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(userYearRechargeStat_result.class, metaDataMap);
    }

    public userYearRechargeStat_result() {
    }

    public userYearRechargeStat_result(
      UserYearRechargeStatResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public userYearRechargeStat_result(userYearRechargeStat_result other) {
      if (other.isSetSuccess()) {
        this.success = new UserYearRechargeStatResponse(other.success);
      }
    }

    public userYearRechargeStat_result deepCopy() {
      return new userYearRechargeStat_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public UserYearRechargeStatResponse getSuccess() {
      return this.success;
    }

    public userYearRechargeStat_result setSuccess(UserYearRechargeStatResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((UserYearRechargeStatResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof userYearRechargeStat_result)
        return this.equals((userYearRechargeStat_result)that);
      return false;
    }

    public boolean equals(userYearRechargeStat_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(userYearRechargeStat_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("userYearRechargeStat_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class userYearRechargeStat_resultStandardSchemeFactory implements SchemeFactory {
      public userYearRechargeStat_resultStandardScheme getScheme() {
        return new userYearRechargeStat_resultStandardScheme();
      }
    }

    private static class userYearRechargeStat_resultStandardScheme extends StandardScheme<userYearRechargeStat_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, userYearRechargeStat_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new UserYearRechargeStatResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, userYearRechargeStat_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class userYearRechargeStat_resultTupleSchemeFactory implements SchemeFactory {
      public userYearRechargeStat_resultTupleScheme getScheme() {
        return new userYearRechargeStat_resultTupleScheme();
      }
    }

    private static class userYearRechargeStat_resultTupleScheme extends TupleScheme<userYearRechargeStat_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, userYearRechargeStat_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, userYearRechargeStat_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new UserYearRechargeStatResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class userEverRecharge_args implements org.apache.thrift.TBase<userEverRecharge_args, userEverRecharge_args._Fields>, java.io.Serializable, Cloneable, Comparable<userEverRecharge_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("userEverRecharge_args");

    private static final org.apache.thrift.protocol.TField USER_EVER_RECHARGE_REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("userEverRechargeRequest", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new userEverRecharge_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new userEverRecharge_argsTupleSchemeFactory());
    }

    public UserEverRechargeRequest userEverRechargeRequest; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      USER_EVER_RECHARGE_REQUEST((short)1, "userEverRechargeRequest");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // USER_EVER_RECHARGE_REQUEST
            return USER_EVER_RECHARGE_REQUEST;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.USER_EVER_RECHARGE_REQUEST, new org.apache.thrift.meta_data.FieldMetaData("userEverRechargeRequest", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, UserEverRechargeRequest.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(userEverRecharge_args.class, metaDataMap);
    }

    public userEverRecharge_args() {
    }

    public userEverRecharge_args(
      UserEverRechargeRequest userEverRechargeRequest)
    {
      this();
      this.userEverRechargeRequest = userEverRechargeRequest;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public userEverRecharge_args(userEverRecharge_args other) {
      if (other.isSetUserEverRechargeRequest()) {
        this.userEverRechargeRequest = new UserEverRechargeRequest(other.userEverRechargeRequest);
      }
    }

    public userEverRecharge_args deepCopy() {
      return new userEverRecharge_args(this);
    }

    @Override
    public void clear() {
      this.userEverRechargeRequest = null;
    }

    public UserEverRechargeRequest getUserEverRechargeRequest() {
      return this.userEverRechargeRequest;
    }

    public userEverRecharge_args setUserEverRechargeRequest(UserEverRechargeRequest userEverRechargeRequest) {
      this.userEverRechargeRequest = userEverRechargeRequest;
      return this;
    }

    public void unsetUserEverRechargeRequest() {
      this.userEverRechargeRequest = null;
    }

    /** Returns true if field userEverRechargeRequest is set (has been assigned a value) and false otherwise */
    public boolean isSetUserEverRechargeRequest() {
      return this.userEverRechargeRequest != null;
    }

    public void setUserEverRechargeRequestIsSet(boolean value) {
      if (!value) {
        this.userEverRechargeRequest = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case USER_EVER_RECHARGE_REQUEST:
        if (value == null) {
          unsetUserEverRechargeRequest();
        } else {
          setUserEverRechargeRequest((UserEverRechargeRequest)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case USER_EVER_RECHARGE_REQUEST:
        return getUserEverRechargeRequest();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case USER_EVER_RECHARGE_REQUEST:
        return isSetUserEverRechargeRequest();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof userEverRecharge_args)
        return this.equals((userEverRecharge_args)that);
      return false;
    }

    public boolean equals(userEverRecharge_args that) {
      if (that == null)
        return false;

      boolean this_present_userEverRechargeRequest = true && this.isSetUserEverRechargeRequest();
      boolean that_present_userEverRechargeRequest = true && that.isSetUserEverRechargeRequest();
      if (this_present_userEverRechargeRequest || that_present_userEverRechargeRequest) {
        if (!(this_present_userEverRechargeRequest && that_present_userEverRechargeRequest))
          return false;
        if (!this.userEverRechargeRequest.equals(that.userEverRechargeRequest))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_userEverRechargeRequest = true && (isSetUserEverRechargeRequest());
      list.add(present_userEverRechargeRequest);
      if (present_userEverRechargeRequest)
        list.add(userEverRechargeRequest);

      return list.hashCode();
    }

    @Override
    public int compareTo(userEverRecharge_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetUserEverRechargeRequest()).compareTo(other.isSetUserEverRechargeRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetUserEverRechargeRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.userEverRechargeRequest, other.userEverRechargeRequest);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("userEverRecharge_args(");
      boolean first = true;

      sb.append("userEverRechargeRequest:");
      if (this.userEverRechargeRequest == null) {
        sb.append("null");
      } else {
        sb.append(this.userEverRechargeRequest);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (userEverRechargeRequest != null) {
        userEverRechargeRequest.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class userEverRecharge_argsStandardSchemeFactory implements SchemeFactory {
      public userEverRecharge_argsStandardScheme getScheme() {
        return new userEverRecharge_argsStandardScheme();
      }
    }

    private static class userEverRecharge_argsStandardScheme extends StandardScheme<userEverRecharge_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, userEverRecharge_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // USER_EVER_RECHARGE_REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.userEverRechargeRequest = new UserEverRechargeRequest();
                struct.userEverRechargeRequest.read(iprot);
                struct.setUserEverRechargeRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, userEverRecharge_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.userEverRechargeRequest != null) {
          oprot.writeFieldBegin(USER_EVER_RECHARGE_REQUEST_FIELD_DESC);
          struct.userEverRechargeRequest.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class userEverRecharge_argsTupleSchemeFactory implements SchemeFactory {
      public userEverRecharge_argsTupleScheme getScheme() {
        return new userEverRecharge_argsTupleScheme();
      }
    }

    private static class userEverRecharge_argsTupleScheme extends TupleScheme<userEverRecharge_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, userEverRecharge_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetUserEverRechargeRequest()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetUserEverRechargeRequest()) {
          struct.userEverRechargeRequest.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, userEverRecharge_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.userEverRechargeRequest = new UserEverRechargeRequest();
          struct.userEverRechargeRequest.read(iprot);
          struct.setUserEverRechargeRequestIsSet(true);
        }
      }
    }

  }

  public static class userEverRecharge_result implements org.apache.thrift.TBase<userEverRecharge_result, userEverRecharge_result._Fields>, java.io.Serializable, Cloneable, Comparable<userEverRecharge_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("userEverRecharge_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new userEverRecharge_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new userEverRecharge_resultTupleSchemeFactory());
    }

    public UserEverRechargeResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, UserEverRechargeResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(userEverRecharge_result.class, metaDataMap);
    }

    public userEverRecharge_result() {
    }

    public userEverRecharge_result(
      UserEverRechargeResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public userEverRecharge_result(userEverRecharge_result other) {
      if (other.isSetSuccess()) {
        this.success = new UserEverRechargeResponse(other.success);
      }
    }

    public userEverRecharge_result deepCopy() {
      return new userEverRecharge_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public UserEverRechargeResponse getSuccess() {
      return this.success;
    }

    public userEverRecharge_result setSuccess(UserEverRechargeResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((UserEverRechargeResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof userEverRecharge_result)
        return this.equals((userEverRecharge_result)that);
      return false;
    }

    public boolean equals(userEverRecharge_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(userEverRecharge_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("userEverRecharge_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class userEverRecharge_resultStandardSchemeFactory implements SchemeFactory {
      public userEverRecharge_resultStandardScheme getScheme() {
        return new userEverRecharge_resultStandardScheme();
      }
    }

    private static class userEverRecharge_resultStandardScheme extends StandardScheme<userEverRecharge_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, userEverRecharge_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new UserEverRechargeResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, userEverRecharge_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class userEverRecharge_resultTupleSchemeFactory implements SchemeFactory {
      public userEverRecharge_resultTupleScheme getScheme() {
        return new userEverRecharge_resultTupleScheme();
      }
    }

    private static class userEverRecharge_resultTupleScheme extends TupleScheme<userEverRecharge_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, userEverRecharge_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, userEverRecharge_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new UserEverRechargeResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

}
