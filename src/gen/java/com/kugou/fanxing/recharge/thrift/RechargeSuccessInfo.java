/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.recharge.thrift;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-10-24")
public class RechargeSuccessInfo implements org.apache.thrift.TBase<RechargeSuccessInfo, RechargeSuccessInfo._Fields>, java.io.Serializable, Cloneable, Comparable<RechargeSuccessInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("RechargeSuccessInfo");

  private static final org.apache.thrift.protocol.TField RECHARGE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("rechargeId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField RECHARGE_ORDER_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("rechargeOrderNum", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField CONSUME_ORDER_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("consumeOrderNum", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField ADD_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("addTime", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField RECHARGE_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("rechargeTime", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)6);
  private static final org.apache.thrift.protocol.TField FROM_KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("fromKugouId", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField COIN_FIELD_DESC = new org.apache.thrift.protocol.TField("coin", org.apache.thrift.protocol.TType.STRING, (short)8);
  private static final org.apache.thrift.protocol.TField AMOUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("amount", org.apache.thrift.protocol.TType.STRING, (short)9);
  private static final org.apache.thrift.protocol.TField REAL_AMOUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("realAmount", org.apache.thrift.protocol.TType.STRING, (short)10);
  private static final org.apache.thrift.protocol.TField PAY_TYPE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("payTypeId", org.apache.thrift.protocol.TType.I32, (short)11);
  private static final org.apache.thrift.protocol.TField EXTRA_JSON_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extraJsonData", org.apache.thrift.protocol.TType.STRING, (short)12);
  private static final org.apache.thrift.protocol.TField REFER_FIELD_DESC = new org.apache.thrift.protocol.TField("refer", org.apache.thrift.protocol.TType.I32, (short)13);
  private static final org.apache.thrift.protocol.TField C_FROM_FIELD_DESC = new org.apache.thrift.protocol.TField("cFrom", org.apache.thrift.protocol.TType.I32, (short)14);
  private static final org.apache.thrift.protocol.TField CHANNEL_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("channelId", org.apache.thrift.protocol.TType.I32, (short)15);
  private static final org.apache.thrift.protocol.TField RE_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("reType", org.apache.thrift.protocol.TType.I32, (short)16);
  private static final org.apache.thrift.protocol.TField EXTEND_FIELD_DESC = new org.apache.thrift.protocol.TField("extend", org.apache.thrift.protocol.TType.STRING, (short)17);
  private static final org.apache.thrift.protocol.TField BUSINESS_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("businessId", org.apache.thrift.protocol.TType.STRING, (short)18);
  private static final org.apache.thrift.protocol.TField TRADE_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("tradeTime", org.apache.thrift.protocol.TType.I64, (short)19);
  private static final org.apache.thrift.protocol.TField TRADE_NO_FIELD_DESC = new org.apache.thrift.protocol.TField("tradeNo", org.apache.thrift.protocol.TType.STRING, (short)20);
  private static final org.apache.thrift.protocol.TField PARTNER_FIELD_DESC = new org.apache.thrift.protocol.TField("partner", org.apache.thrift.protocol.TType.STRING, (short)21);
  private static final org.apache.thrift.protocol.TField COUPON_FIELD_DESC = new org.apache.thrift.protocol.TField("coupon", org.apache.thrift.protocol.TType.STRING, (short)22);
  private static final org.apache.thrift.protocol.TField COUPON_STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("couponStatus", org.apache.thrift.protocol.TType.I32, (short)23);
  private static final org.apache.thrift.protocol.TField COUPON_ORDER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("couponOrderId", org.apache.thrift.protocol.TType.I64, (short)24);
  private static final org.apache.thrift.protocol.TField IS_SANDBOX_FIELD_DESC = new org.apache.thrift.protocol.TField("isSandbox", org.apache.thrift.protocol.TType.I32, (short)25);
  private static final org.apache.thrift.protocol.TField MONEY_FIELD_DESC = new org.apache.thrift.protocol.TField("money", org.apache.thrift.protocol.TType.STRING, (short)26);
  private static final org.apache.thrift.protocol.TField COUPON_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("couponId", org.apache.thrift.protocol.TType.I64, (short)27);
  private static final org.apache.thrift.protocol.TField SERVER_ROOM_FIELD_DESC = new org.apache.thrift.protocol.TField("serverRoom", org.apache.thrift.protocol.TType.I32, (short)28);
  private static final org.apache.thrift.protocol.TField COIN_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("coinType", org.apache.thrift.protocol.TType.I32, (short)29);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new RechargeSuccessInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new RechargeSuccessInfoTupleSchemeFactory());
  }

  public long rechargeId; // required
  /**
   * 唯一标识
   */
  public String rechargeOrderNum; // required
  /**
   * 直播订单号
   */
  public String consumeOrderNum; // required
  /**
   * 网关订单号
   */
  public long addTime; // required
  /**
   * 充值下单时间
   */
  public long rechargeTime; // required
  /**
   * 充值发货时间
   */
  public long kugouId; // required
  /**
   * 充值酷狗id
   */
  public long fromKugouId; // required
  /**
   * 谁充的
   */
  public String coin; // required
  /**
   * 充值星币
   */
  public String amount; // required
  /**
   * 充值金额
   */
  public String realAmount; // required
  /**
   * 废弃
   */
  public int payTypeId; // required
  /**
   * 充值渠道
   */
  public String extraJsonData; // required
  /**
   * 扩展属性字段
   */
  public int refer; // required
  /**
   * 废弃
   */
  public int cFrom; // required
  /**
   * 充值平台
   */
  public int channelId; // required
  /**
   * 废弃
   */
  public int reType; // required
  /**
   * 充值类型
   */
  public String extend; // required
  /**
   * 扩展信息
   */
  public String businessId; // required
  /**
   * 业务标识
   */
  public long tradeTime; // required
  /**
   * 交易时间
   */
  public String tradeNo; // required
  /**
   * 交易单号
   */
  public String partner; // required
  /**
   * 交易商户
   */
  public String coupon; // required
  /**
   * 代金券金额
   */
  public int couponStatus; // required
  /**
   * 代金券状态
   */
  public long couponOrderId; // required
  /**
   * 代金券订单号
   */
  public int isSandbox; // required
  /**
   * 是否沙盒充值
   */
  public String money; // required
  /**
   * 实付金额
   */
  public long couponId; // required
  /**
   * 代金券标识
   */
  public int serverRoom; // required
  /**
   * 废弃
   */
  public int coinType; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    RECHARGE_ID((short)1, "rechargeId"),
    /**
     * 唯一标识
     */
    RECHARGE_ORDER_NUM((short)2, "rechargeOrderNum"),
    /**
     * 直播订单号
     */
    CONSUME_ORDER_NUM((short)3, "consumeOrderNum"),
    /**
     * 网关订单号
     */
    ADD_TIME((short)4, "addTime"),
    /**
     * 充值下单时间
     */
    RECHARGE_TIME((short)5, "rechargeTime"),
    /**
     * 充值发货时间
     */
    KUGOU_ID((short)6, "kugouId"),
    /**
     * 充值酷狗id
     */
    FROM_KUGOU_ID((short)7, "fromKugouId"),
    /**
     * 谁充的
     */
    COIN((short)8, "coin"),
    /**
     * 充值星币
     */
    AMOUNT((short)9, "amount"),
    /**
     * 充值金额
     */
    REAL_AMOUNT((short)10, "realAmount"),
    /**
     * 废弃
     */
    PAY_TYPE_ID((short)11, "payTypeId"),
    /**
     * 充值渠道
     */
    EXTRA_JSON_DATA((short)12, "extraJsonData"),
    /**
     * 扩展属性字段
     */
    REFER((short)13, "refer"),
    /**
     * 废弃
     */
    C_FROM((short)14, "cFrom"),
    /**
     * 充值平台
     */
    CHANNEL_ID((short)15, "channelId"),
    /**
     * 废弃
     */
    RE_TYPE((short)16, "reType"),
    /**
     * 充值类型
     */
    EXTEND((short)17, "extend"),
    /**
     * 扩展信息
     */
    BUSINESS_ID((short)18, "businessId"),
    /**
     * 业务标识
     */
    TRADE_TIME((short)19, "tradeTime"),
    /**
     * 交易时间
     */
    TRADE_NO((short)20, "tradeNo"),
    /**
     * 交易单号
     */
    PARTNER((short)21, "partner"),
    /**
     * 交易商户
     */
    COUPON((short)22, "coupon"),
    /**
     * 代金券金额
     */
    COUPON_STATUS((short)23, "couponStatus"),
    /**
     * 代金券状态
     */
    COUPON_ORDER_ID((short)24, "couponOrderId"),
    /**
     * 代金券订单号
     */
    IS_SANDBOX((short)25, "isSandbox"),
    /**
     * 是否沙盒充值
     */
    MONEY((short)26, "money"),
    /**
     * 实付金额
     */
    COUPON_ID((short)27, "couponId"),
    /**
     * 代金券标识
     */
    SERVER_ROOM((short)28, "serverRoom"),
    /**
     * 废弃
     */
    COIN_TYPE((short)29, "coinType");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RECHARGE_ID
          return RECHARGE_ID;
        case 2: // RECHARGE_ORDER_NUM
          return RECHARGE_ORDER_NUM;
        case 3: // CONSUME_ORDER_NUM
          return CONSUME_ORDER_NUM;
        case 4: // ADD_TIME
          return ADD_TIME;
        case 5: // RECHARGE_TIME
          return RECHARGE_TIME;
        case 6: // KUGOU_ID
          return KUGOU_ID;
        case 7: // FROM_KUGOU_ID
          return FROM_KUGOU_ID;
        case 8: // COIN
          return COIN;
        case 9: // AMOUNT
          return AMOUNT;
        case 10: // REAL_AMOUNT
          return REAL_AMOUNT;
        case 11: // PAY_TYPE_ID
          return PAY_TYPE_ID;
        case 12: // EXTRA_JSON_DATA
          return EXTRA_JSON_DATA;
        case 13: // REFER
          return REFER;
        case 14: // C_FROM
          return C_FROM;
        case 15: // CHANNEL_ID
          return CHANNEL_ID;
        case 16: // RE_TYPE
          return RE_TYPE;
        case 17: // EXTEND
          return EXTEND;
        case 18: // BUSINESS_ID
          return BUSINESS_ID;
        case 19: // TRADE_TIME
          return TRADE_TIME;
        case 20: // TRADE_NO
          return TRADE_NO;
        case 21: // PARTNER
          return PARTNER;
        case 22: // COUPON
          return COUPON;
        case 23: // COUPON_STATUS
          return COUPON_STATUS;
        case 24: // COUPON_ORDER_ID
          return COUPON_ORDER_ID;
        case 25: // IS_SANDBOX
          return IS_SANDBOX;
        case 26: // MONEY
          return MONEY;
        case 27: // COUPON_ID
          return COUPON_ID;
        case 28: // SERVER_ROOM
          return SERVER_ROOM;
        case 29: // COIN_TYPE
          return COIN_TYPE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __RECHARGEID_ISSET_ID = 0;
  private static final int __ADDTIME_ISSET_ID = 1;
  private static final int __RECHARGETIME_ISSET_ID = 2;
  private static final int __KUGOUID_ISSET_ID = 3;
  private static final int __FROMKUGOUID_ISSET_ID = 4;
  private static final int __PAYTYPEID_ISSET_ID = 5;
  private static final int __REFER_ISSET_ID = 6;
  private static final int __CFROM_ISSET_ID = 7;
  private static final int __CHANNELID_ISSET_ID = 8;
  private static final int __RETYPE_ISSET_ID = 9;
  private static final int __TRADETIME_ISSET_ID = 10;
  private static final int __COUPONSTATUS_ISSET_ID = 11;
  private static final int __COUPONORDERID_ISSET_ID = 12;
  private static final int __ISSANDBOX_ISSET_ID = 13;
  private static final int __COUPONID_ISSET_ID = 14;
  private static final int __SERVERROOM_ISSET_ID = 15;
  private static final int __COINTYPE_ISSET_ID = 16;
  private int __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.COIN_TYPE};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RECHARGE_ID, new org.apache.thrift.meta_data.FieldMetaData("rechargeId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RECHARGE_ORDER_NUM, new org.apache.thrift.meta_data.FieldMetaData("rechargeOrderNum", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CONSUME_ORDER_NUM, new org.apache.thrift.meta_data.FieldMetaData("consumeOrderNum", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ADD_TIME, new org.apache.thrift.meta_data.FieldMetaData("addTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RECHARGE_TIME, new org.apache.thrift.meta_data.FieldMetaData("rechargeTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.FROM_KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("fromKugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.COIN, new org.apache.thrift.meta_data.FieldMetaData("coin", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.AMOUNT, new org.apache.thrift.meta_data.FieldMetaData("amount", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.REAL_AMOUNT, new org.apache.thrift.meta_data.FieldMetaData("realAmount", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PAY_TYPE_ID, new org.apache.thrift.meta_data.FieldMetaData("payTypeId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.EXTRA_JSON_DATA, new org.apache.thrift.meta_data.FieldMetaData("extraJsonData", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.REFER, new org.apache.thrift.meta_data.FieldMetaData("refer", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.C_FROM, new org.apache.thrift.meta_data.FieldMetaData("cFrom", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.CHANNEL_ID, new org.apache.thrift.meta_data.FieldMetaData("channelId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.RE_TYPE, new org.apache.thrift.meta_data.FieldMetaData("reType", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.EXTEND, new org.apache.thrift.meta_data.FieldMetaData("extend", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BUSINESS_ID, new org.apache.thrift.meta_data.FieldMetaData("businessId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.TRADE_TIME, new org.apache.thrift.meta_data.FieldMetaData("tradeTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TRADE_NO, new org.apache.thrift.meta_data.FieldMetaData("tradeNo", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PARTNER, new org.apache.thrift.meta_data.FieldMetaData("partner", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.COUPON, new org.apache.thrift.meta_data.FieldMetaData("coupon", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.COUPON_STATUS, new org.apache.thrift.meta_data.FieldMetaData("couponStatus", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.COUPON_ORDER_ID, new org.apache.thrift.meta_data.FieldMetaData("couponOrderId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.IS_SANDBOX, new org.apache.thrift.meta_data.FieldMetaData("isSandbox", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.MONEY, new org.apache.thrift.meta_data.FieldMetaData("money", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.COUPON_ID, new org.apache.thrift.meta_data.FieldMetaData("couponId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SERVER_ROOM, new org.apache.thrift.meta_data.FieldMetaData("serverRoom", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.COIN_TYPE, new org.apache.thrift.meta_data.FieldMetaData("coinType", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(RechargeSuccessInfo.class, metaDataMap);
  }

  public RechargeSuccessInfo() {
  }

  public RechargeSuccessInfo(
    long rechargeId,
    String rechargeOrderNum,
    String consumeOrderNum,
    long addTime,
    long rechargeTime,
    long kugouId,
    long fromKugouId,
    String coin,
    String amount,
    String realAmount,
    int payTypeId,
    String extraJsonData,
    int refer,
    int cFrom,
    int channelId,
    int reType,
    String extend,
    String businessId,
    long tradeTime,
    String tradeNo,
    String partner,
    String coupon,
    int couponStatus,
    long couponOrderId,
    int isSandbox,
    String money,
    long couponId,
    int serverRoom)
  {
    this();
    this.rechargeId = rechargeId;
    setRechargeIdIsSet(true);
    this.rechargeOrderNum = rechargeOrderNum;
    this.consumeOrderNum = consumeOrderNum;
    this.addTime = addTime;
    setAddTimeIsSet(true);
    this.rechargeTime = rechargeTime;
    setRechargeTimeIsSet(true);
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    this.fromKugouId = fromKugouId;
    setFromKugouIdIsSet(true);
    this.coin = coin;
    this.amount = amount;
    this.realAmount = realAmount;
    this.payTypeId = payTypeId;
    setPayTypeIdIsSet(true);
    this.extraJsonData = extraJsonData;
    this.refer = refer;
    setReferIsSet(true);
    this.cFrom = cFrom;
    setCFromIsSet(true);
    this.channelId = channelId;
    setChannelIdIsSet(true);
    this.reType = reType;
    setReTypeIsSet(true);
    this.extend = extend;
    this.businessId = businessId;
    this.tradeTime = tradeTime;
    setTradeTimeIsSet(true);
    this.tradeNo = tradeNo;
    this.partner = partner;
    this.coupon = coupon;
    this.couponStatus = couponStatus;
    setCouponStatusIsSet(true);
    this.couponOrderId = couponOrderId;
    setCouponOrderIdIsSet(true);
    this.isSandbox = isSandbox;
    setIsSandboxIsSet(true);
    this.money = money;
    this.couponId = couponId;
    setCouponIdIsSet(true);
    this.serverRoom = serverRoom;
    setServerRoomIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public RechargeSuccessInfo(RechargeSuccessInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.rechargeId = other.rechargeId;
    if (other.isSetRechargeOrderNum()) {
      this.rechargeOrderNum = other.rechargeOrderNum;
    }
    if (other.isSetConsumeOrderNum()) {
      this.consumeOrderNum = other.consumeOrderNum;
    }
    this.addTime = other.addTime;
    this.rechargeTime = other.rechargeTime;
    this.kugouId = other.kugouId;
    this.fromKugouId = other.fromKugouId;
    if (other.isSetCoin()) {
      this.coin = other.coin;
    }
    if (other.isSetAmount()) {
      this.amount = other.amount;
    }
    if (other.isSetRealAmount()) {
      this.realAmount = other.realAmount;
    }
    this.payTypeId = other.payTypeId;
    if (other.isSetExtraJsonData()) {
      this.extraJsonData = other.extraJsonData;
    }
    this.refer = other.refer;
    this.cFrom = other.cFrom;
    this.channelId = other.channelId;
    this.reType = other.reType;
    if (other.isSetExtend()) {
      this.extend = other.extend;
    }
    if (other.isSetBusinessId()) {
      this.businessId = other.businessId;
    }
    this.tradeTime = other.tradeTime;
    if (other.isSetTradeNo()) {
      this.tradeNo = other.tradeNo;
    }
    if (other.isSetPartner()) {
      this.partner = other.partner;
    }
    if (other.isSetCoupon()) {
      this.coupon = other.coupon;
    }
    this.couponStatus = other.couponStatus;
    this.couponOrderId = other.couponOrderId;
    this.isSandbox = other.isSandbox;
    if (other.isSetMoney()) {
      this.money = other.money;
    }
    this.couponId = other.couponId;
    this.serverRoom = other.serverRoom;
    this.coinType = other.coinType;
  }

  public RechargeSuccessInfo deepCopy() {
    return new RechargeSuccessInfo(this);
  }

  @Override
  public void clear() {
    setRechargeIdIsSet(false);
    this.rechargeId = 0;
    this.rechargeOrderNum = null;
    this.consumeOrderNum = null;
    setAddTimeIsSet(false);
    this.addTime = 0;
    setRechargeTimeIsSet(false);
    this.rechargeTime = 0;
    setKugouIdIsSet(false);
    this.kugouId = 0;
    setFromKugouIdIsSet(false);
    this.fromKugouId = 0;
    this.coin = null;
    this.amount = null;
    this.realAmount = null;
    setPayTypeIdIsSet(false);
    this.payTypeId = 0;
    this.extraJsonData = null;
    setReferIsSet(false);
    this.refer = 0;
    setCFromIsSet(false);
    this.cFrom = 0;
    setChannelIdIsSet(false);
    this.channelId = 0;
    setReTypeIsSet(false);
    this.reType = 0;
    this.extend = null;
    this.businessId = null;
    setTradeTimeIsSet(false);
    this.tradeTime = 0;
    this.tradeNo = null;
    this.partner = null;
    this.coupon = null;
    setCouponStatusIsSet(false);
    this.couponStatus = 0;
    setCouponOrderIdIsSet(false);
    this.couponOrderId = 0;
    setIsSandboxIsSet(false);
    this.isSandbox = 0;
    this.money = null;
    setCouponIdIsSet(false);
    this.couponId = 0;
    setServerRoomIsSet(false);
    this.serverRoom = 0;
    setCoinTypeIsSet(false);
    this.coinType = 0;
  }

  public long getRechargeId() {
    return this.rechargeId;
  }

  public RechargeSuccessInfo setRechargeId(long rechargeId) {
    this.rechargeId = rechargeId;
    setRechargeIdIsSet(true);
    return this;
  }

  public void unsetRechargeId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RECHARGEID_ISSET_ID);
  }

  /** Returns true if field rechargeId is set (has been assigned a value) and false otherwise */
  public boolean isSetRechargeId() {
    return EncodingUtils.testBit(__isset_bitfield, __RECHARGEID_ISSET_ID);
  }

  public void setRechargeIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RECHARGEID_ISSET_ID, value);
  }

  /**
   * 唯一标识
   */
  public String getRechargeOrderNum() {
    return this.rechargeOrderNum;
  }

  /**
   * 唯一标识
   */
  public RechargeSuccessInfo setRechargeOrderNum(String rechargeOrderNum) {
    this.rechargeOrderNum = rechargeOrderNum;
    return this;
  }

  public void unsetRechargeOrderNum() {
    this.rechargeOrderNum = null;
  }

  /** Returns true if field rechargeOrderNum is set (has been assigned a value) and false otherwise */
  public boolean isSetRechargeOrderNum() {
    return this.rechargeOrderNum != null;
  }

  public void setRechargeOrderNumIsSet(boolean value) {
    if (!value) {
      this.rechargeOrderNum = null;
    }
  }

  /**
   * 直播订单号
   */
  public String getConsumeOrderNum() {
    return this.consumeOrderNum;
  }

  /**
   * 直播订单号
   */
  public RechargeSuccessInfo setConsumeOrderNum(String consumeOrderNum) {
    this.consumeOrderNum = consumeOrderNum;
    return this;
  }

  public void unsetConsumeOrderNum() {
    this.consumeOrderNum = null;
  }

  /** Returns true if field consumeOrderNum is set (has been assigned a value) and false otherwise */
  public boolean isSetConsumeOrderNum() {
    return this.consumeOrderNum != null;
  }

  public void setConsumeOrderNumIsSet(boolean value) {
    if (!value) {
      this.consumeOrderNum = null;
    }
  }

  /**
   * 网关订单号
   */
  public long getAddTime() {
    return this.addTime;
  }

  /**
   * 网关订单号
   */
  public RechargeSuccessInfo setAddTime(long addTime) {
    this.addTime = addTime;
    setAddTimeIsSet(true);
    return this;
  }

  public void unsetAddTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ADDTIME_ISSET_ID);
  }

  /** Returns true if field addTime is set (has been assigned a value) and false otherwise */
  public boolean isSetAddTime() {
    return EncodingUtils.testBit(__isset_bitfield, __ADDTIME_ISSET_ID);
  }

  public void setAddTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ADDTIME_ISSET_ID, value);
  }

  /**
   * 充值下单时间
   */
  public long getRechargeTime() {
    return this.rechargeTime;
  }

  /**
   * 充值下单时间
   */
  public RechargeSuccessInfo setRechargeTime(long rechargeTime) {
    this.rechargeTime = rechargeTime;
    setRechargeTimeIsSet(true);
    return this;
  }

  public void unsetRechargeTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RECHARGETIME_ISSET_ID);
  }

  /** Returns true if field rechargeTime is set (has been assigned a value) and false otherwise */
  public boolean isSetRechargeTime() {
    return EncodingUtils.testBit(__isset_bitfield, __RECHARGETIME_ISSET_ID);
  }

  public void setRechargeTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RECHARGETIME_ISSET_ID, value);
  }

  /**
   * 充值发货时间
   */
  public long getKugouId() {
    return this.kugouId;
  }

  /**
   * 充值发货时间
   */
  public RechargeSuccessInfo setKugouId(long kugouId) {
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    return this;
  }

  public void unsetKugouId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
  public boolean isSetKugouId() {
    return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  public void setKugouIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
  }

  /**
   * 充值酷狗id
   */
  public long getFromKugouId() {
    return this.fromKugouId;
  }

  /**
   * 充值酷狗id
   */
  public RechargeSuccessInfo setFromKugouId(long fromKugouId) {
    this.fromKugouId = fromKugouId;
    setFromKugouIdIsSet(true);
    return this;
  }

  public void unsetFromKugouId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __FROMKUGOUID_ISSET_ID);
  }

  /** Returns true if field fromKugouId is set (has been assigned a value) and false otherwise */
  public boolean isSetFromKugouId() {
    return EncodingUtils.testBit(__isset_bitfield, __FROMKUGOUID_ISSET_ID);
  }

  public void setFromKugouIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __FROMKUGOUID_ISSET_ID, value);
  }

  /**
   * 谁充的
   */
  public String getCoin() {
    return this.coin;
  }

  /**
   * 谁充的
   */
  public RechargeSuccessInfo setCoin(String coin) {
    this.coin = coin;
    return this;
  }

  public void unsetCoin() {
    this.coin = null;
  }

  /** Returns true if field coin is set (has been assigned a value) and false otherwise */
  public boolean isSetCoin() {
    return this.coin != null;
  }

  public void setCoinIsSet(boolean value) {
    if (!value) {
      this.coin = null;
    }
  }

  /**
   * 充值星币
   */
  public String getAmount() {
    return this.amount;
  }

  /**
   * 充值星币
   */
  public RechargeSuccessInfo setAmount(String amount) {
    this.amount = amount;
    return this;
  }

  public void unsetAmount() {
    this.amount = null;
  }

  /** Returns true if field amount is set (has been assigned a value) and false otherwise */
  public boolean isSetAmount() {
    return this.amount != null;
  }

  public void setAmountIsSet(boolean value) {
    if (!value) {
      this.amount = null;
    }
  }

  /**
   * 充值金额
   */
  public String getRealAmount() {
    return this.realAmount;
  }

  /**
   * 充值金额
   */
  public RechargeSuccessInfo setRealAmount(String realAmount) {
    this.realAmount = realAmount;
    return this;
  }

  public void unsetRealAmount() {
    this.realAmount = null;
  }

  /** Returns true if field realAmount is set (has been assigned a value) and false otherwise */
  public boolean isSetRealAmount() {
    return this.realAmount != null;
  }

  public void setRealAmountIsSet(boolean value) {
    if (!value) {
      this.realAmount = null;
    }
  }

  /**
   * 废弃
   */
  public int getPayTypeId() {
    return this.payTypeId;
  }

  /**
   * 废弃
   */
  public RechargeSuccessInfo setPayTypeId(int payTypeId) {
    this.payTypeId = payTypeId;
    setPayTypeIdIsSet(true);
    return this;
  }

  public void unsetPayTypeId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAYTYPEID_ISSET_ID);
  }

  /** Returns true if field payTypeId is set (has been assigned a value) and false otherwise */
  public boolean isSetPayTypeId() {
    return EncodingUtils.testBit(__isset_bitfield, __PAYTYPEID_ISSET_ID);
  }

  public void setPayTypeIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAYTYPEID_ISSET_ID, value);
  }

  /**
   * 充值渠道
   */
  public String getExtraJsonData() {
    return this.extraJsonData;
  }

  /**
   * 充值渠道
   */
  public RechargeSuccessInfo setExtraJsonData(String extraJsonData) {
    this.extraJsonData = extraJsonData;
    return this;
  }

  public void unsetExtraJsonData() {
    this.extraJsonData = null;
  }

  /** Returns true if field extraJsonData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtraJsonData() {
    return this.extraJsonData != null;
  }

  public void setExtraJsonDataIsSet(boolean value) {
    if (!value) {
      this.extraJsonData = null;
    }
  }

  /**
   * 扩展属性字段
   */
  public int getRefer() {
    return this.refer;
  }

  /**
   * 扩展属性字段
   */
  public RechargeSuccessInfo setRefer(int refer) {
    this.refer = refer;
    setReferIsSet(true);
    return this;
  }

  public void unsetRefer() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __REFER_ISSET_ID);
  }

  /** Returns true if field refer is set (has been assigned a value) and false otherwise */
  public boolean isSetRefer() {
    return EncodingUtils.testBit(__isset_bitfield, __REFER_ISSET_ID);
  }

  public void setReferIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __REFER_ISSET_ID, value);
  }

  /**
   * 废弃
   */
  public int getCFrom() {
    return this.cFrom;
  }

  /**
   * 废弃
   */
  public RechargeSuccessInfo setCFrom(int cFrom) {
    this.cFrom = cFrom;
    setCFromIsSet(true);
    return this;
  }

  public void unsetCFrom() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CFROM_ISSET_ID);
  }

  /** Returns true if field cFrom is set (has been assigned a value) and false otherwise */
  public boolean isSetCFrom() {
    return EncodingUtils.testBit(__isset_bitfield, __CFROM_ISSET_ID);
  }

  public void setCFromIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CFROM_ISSET_ID, value);
  }

  /**
   * 充值平台
   */
  public int getChannelId() {
    return this.channelId;
  }

  /**
   * 充值平台
   */
  public RechargeSuccessInfo setChannelId(int channelId) {
    this.channelId = channelId;
    setChannelIdIsSet(true);
    return this;
  }

  public void unsetChannelId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CHANNELID_ISSET_ID);
  }

  /** Returns true if field channelId is set (has been assigned a value) and false otherwise */
  public boolean isSetChannelId() {
    return EncodingUtils.testBit(__isset_bitfield, __CHANNELID_ISSET_ID);
  }

  public void setChannelIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CHANNELID_ISSET_ID, value);
  }

  /**
   * 废弃
   */
  public int getReType() {
    return this.reType;
  }

  /**
   * 废弃
   */
  public RechargeSuccessInfo setReType(int reType) {
    this.reType = reType;
    setReTypeIsSet(true);
    return this;
  }

  public void unsetReType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RETYPE_ISSET_ID);
  }

  /** Returns true if field reType is set (has been assigned a value) and false otherwise */
  public boolean isSetReType() {
    return EncodingUtils.testBit(__isset_bitfield, __RETYPE_ISSET_ID);
  }

  public void setReTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RETYPE_ISSET_ID, value);
  }

  /**
   * 充值类型
   */
  public String getExtend() {
    return this.extend;
  }

  /**
   * 充值类型
   */
  public RechargeSuccessInfo setExtend(String extend) {
    this.extend = extend;
    return this;
  }

  public void unsetExtend() {
    this.extend = null;
  }

  /** Returns true if field extend is set (has been assigned a value) and false otherwise */
  public boolean isSetExtend() {
    return this.extend != null;
  }

  public void setExtendIsSet(boolean value) {
    if (!value) {
      this.extend = null;
    }
  }

  /**
   * 扩展信息
   */
  public String getBusinessId() {
    return this.businessId;
  }

  /**
   * 扩展信息
   */
  public RechargeSuccessInfo setBusinessId(String businessId) {
    this.businessId = businessId;
    return this;
  }

  public void unsetBusinessId() {
    this.businessId = null;
  }

  /** Returns true if field businessId is set (has been assigned a value) and false otherwise */
  public boolean isSetBusinessId() {
    return this.businessId != null;
  }

  public void setBusinessIdIsSet(boolean value) {
    if (!value) {
      this.businessId = null;
    }
  }

  /**
   * 业务标识
   */
  public long getTradeTime() {
    return this.tradeTime;
  }

  /**
   * 业务标识
   */
  public RechargeSuccessInfo setTradeTime(long tradeTime) {
    this.tradeTime = tradeTime;
    setTradeTimeIsSet(true);
    return this;
  }

  public void unsetTradeTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TRADETIME_ISSET_ID);
  }

  /** Returns true if field tradeTime is set (has been assigned a value) and false otherwise */
  public boolean isSetTradeTime() {
    return EncodingUtils.testBit(__isset_bitfield, __TRADETIME_ISSET_ID);
  }

  public void setTradeTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TRADETIME_ISSET_ID, value);
  }

  /**
   * 交易时间
   */
  public String getTradeNo() {
    return this.tradeNo;
  }

  /**
   * 交易时间
   */
  public RechargeSuccessInfo setTradeNo(String tradeNo) {
    this.tradeNo = tradeNo;
    return this;
  }

  public void unsetTradeNo() {
    this.tradeNo = null;
  }

  /** Returns true if field tradeNo is set (has been assigned a value) and false otherwise */
  public boolean isSetTradeNo() {
    return this.tradeNo != null;
  }

  public void setTradeNoIsSet(boolean value) {
    if (!value) {
      this.tradeNo = null;
    }
  }

  /**
   * 交易单号
   */
  public String getPartner() {
    return this.partner;
  }

  /**
   * 交易单号
   */
  public RechargeSuccessInfo setPartner(String partner) {
    this.partner = partner;
    return this;
  }

  public void unsetPartner() {
    this.partner = null;
  }

  /** Returns true if field partner is set (has been assigned a value) and false otherwise */
  public boolean isSetPartner() {
    return this.partner != null;
  }

  public void setPartnerIsSet(boolean value) {
    if (!value) {
      this.partner = null;
    }
  }

  /**
   * 交易商户
   */
  public String getCoupon() {
    return this.coupon;
  }

  /**
   * 交易商户
   */
  public RechargeSuccessInfo setCoupon(String coupon) {
    this.coupon = coupon;
    return this;
  }

  public void unsetCoupon() {
    this.coupon = null;
  }

  /** Returns true if field coupon is set (has been assigned a value) and false otherwise */
  public boolean isSetCoupon() {
    return this.coupon != null;
  }

  public void setCouponIsSet(boolean value) {
    if (!value) {
      this.coupon = null;
    }
  }

  /**
   * 代金券金额
   */
  public int getCouponStatus() {
    return this.couponStatus;
  }

  /**
   * 代金券金额
   */
  public RechargeSuccessInfo setCouponStatus(int couponStatus) {
    this.couponStatus = couponStatus;
    setCouponStatusIsSet(true);
    return this;
  }

  public void unsetCouponStatus() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COUPONSTATUS_ISSET_ID);
  }

  /** Returns true if field couponStatus is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponStatus() {
    return EncodingUtils.testBit(__isset_bitfield, __COUPONSTATUS_ISSET_ID);
  }

  public void setCouponStatusIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COUPONSTATUS_ISSET_ID, value);
  }

  /**
   * 代金券状态
   */
  public long getCouponOrderId() {
    return this.couponOrderId;
  }

  /**
   * 代金券状态
   */
  public RechargeSuccessInfo setCouponOrderId(long couponOrderId) {
    this.couponOrderId = couponOrderId;
    setCouponOrderIdIsSet(true);
    return this;
  }

  public void unsetCouponOrderId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COUPONORDERID_ISSET_ID);
  }

  /** Returns true if field couponOrderId is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponOrderId() {
    return EncodingUtils.testBit(__isset_bitfield, __COUPONORDERID_ISSET_ID);
  }

  public void setCouponOrderIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COUPONORDERID_ISSET_ID, value);
  }

  /**
   * 代金券订单号
   */
  public int getIsSandbox() {
    return this.isSandbox;
  }

  /**
   * 代金券订单号
   */
  public RechargeSuccessInfo setIsSandbox(int isSandbox) {
    this.isSandbox = isSandbox;
    setIsSandboxIsSet(true);
    return this;
  }

  public void unsetIsSandbox() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ISSANDBOX_ISSET_ID);
  }

  /** Returns true if field isSandbox is set (has been assigned a value) and false otherwise */
  public boolean isSetIsSandbox() {
    return EncodingUtils.testBit(__isset_bitfield, __ISSANDBOX_ISSET_ID);
  }

  public void setIsSandboxIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ISSANDBOX_ISSET_ID, value);
  }

  /**
   * 是否沙盒充值
   */
  public String getMoney() {
    return this.money;
  }

  /**
   * 是否沙盒充值
   */
  public RechargeSuccessInfo setMoney(String money) {
    this.money = money;
    return this;
  }

  public void unsetMoney() {
    this.money = null;
  }

  /** Returns true if field money is set (has been assigned a value) and false otherwise */
  public boolean isSetMoney() {
    return this.money != null;
  }

  public void setMoneyIsSet(boolean value) {
    if (!value) {
      this.money = null;
    }
  }

  /**
   * 实付金额
   */
  public long getCouponId() {
    return this.couponId;
  }

  /**
   * 实付金额
   */
  public RechargeSuccessInfo setCouponId(long couponId) {
    this.couponId = couponId;
    setCouponIdIsSet(true);
    return this;
  }

  public void unsetCouponId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COUPONID_ISSET_ID);
  }

  /** Returns true if field couponId is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponId() {
    return EncodingUtils.testBit(__isset_bitfield, __COUPONID_ISSET_ID);
  }

  public void setCouponIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COUPONID_ISSET_ID, value);
  }

  /**
   * 代金券标识
   */
  public int getServerRoom() {
    return this.serverRoom;
  }

  /**
   * 代金券标识
   */
  public RechargeSuccessInfo setServerRoom(int serverRoom) {
    this.serverRoom = serverRoom;
    setServerRoomIsSet(true);
    return this;
  }

  public void unsetServerRoom() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SERVERROOM_ISSET_ID);
  }

  /** Returns true if field serverRoom is set (has been assigned a value) and false otherwise */
  public boolean isSetServerRoom() {
    return EncodingUtils.testBit(__isset_bitfield, __SERVERROOM_ISSET_ID);
  }

  public void setServerRoomIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SERVERROOM_ISSET_ID, value);
  }

  /**
   * 废弃
   */
  public int getCoinType() {
    return this.coinType;
  }

  /**
   * 废弃
   */
  public RechargeSuccessInfo setCoinType(int coinType) {
    this.coinType = coinType;
    setCoinTypeIsSet(true);
    return this;
  }

  public void unsetCoinType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COINTYPE_ISSET_ID);
  }

  /** Returns true if field coinType is set (has been assigned a value) and false otherwise */
  public boolean isSetCoinType() {
    return EncodingUtils.testBit(__isset_bitfield, __COINTYPE_ISSET_ID);
  }

  public void setCoinTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COINTYPE_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case RECHARGE_ID:
      if (value == null) {
        unsetRechargeId();
      } else {
        setRechargeId((Long)value);
      }
      break;

    case RECHARGE_ORDER_NUM:
      if (value == null) {
        unsetRechargeOrderNum();
      } else {
        setRechargeOrderNum((String)value);
      }
      break;

    case CONSUME_ORDER_NUM:
      if (value == null) {
        unsetConsumeOrderNum();
      } else {
        setConsumeOrderNum((String)value);
      }
      break;

    case ADD_TIME:
      if (value == null) {
        unsetAddTime();
      } else {
        setAddTime((Long)value);
      }
      break;

    case RECHARGE_TIME:
      if (value == null) {
        unsetRechargeTime();
      } else {
        setRechargeTime((Long)value);
      }
      break;

    case KUGOU_ID:
      if (value == null) {
        unsetKugouId();
      } else {
        setKugouId((Long)value);
      }
      break;

    case FROM_KUGOU_ID:
      if (value == null) {
        unsetFromKugouId();
      } else {
        setFromKugouId((Long)value);
      }
      break;

    case COIN:
      if (value == null) {
        unsetCoin();
      } else {
        setCoin((String)value);
      }
      break;

    case AMOUNT:
      if (value == null) {
        unsetAmount();
      } else {
        setAmount((String)value);
      }
      break;

    case REAL_AMOUNT:
      if (value == null) {
        unsetRealAmount();
      } else {
        setRealAmount((String)value);
      }
      break;

    case PAY_TYPE_ID:
      if (value == null) {
        unsetPayTypeId();
      } else {
        setPayTypeId((Integer)value);
      }
      break;

    case EXTRA_JSON_DATA:
      if (value == null) {
        unsetExtraJsonData();
      } else {
        setExtraJsonData((String)value);
      }
      break;

    case REFER:
      if (value == null) {
        unsetRefer();
      } else {
        setRefer((Integer)value);
      }
      break;

    case C_FROM:
      if (value == null) {
        unsetCFrom();
      } else {
        setCFrom((Integer)value);
      }
      break;

    case CHANNEL_ID:
      if (value == null) {
        unsetChannelId();
      } else {
        setChannelId((Integer)value);
      }
      break;

    case RE_TYPE:
      if (value == null) {
        unsetReType();
      } else {
        setReType((Integer)value);
      }
      break;

    case EXTEND:
      if (value == null) {
        unsetExtend();
      } else {
        setExtend((String)value);
      }
      break;

    case BUSINESS_ID:
      if (value == null) {
        unsetBusinessId();
      } else {
        setBusinessId((String)value);
      }
      break;

    case TRADE_TIME:
      if (value == null) {
        unsetTradeTime();
      } else {
        setTradeTime((Long)value);
      }
      break;

    case TRADE_NO:
      if (value == null) {
        unsetTradeNo();
      } else {
        setTradeNo((String)value);
      }
      break;

    case PARTNER:
      if (value == null) {
        unsetPartner();
      } else {
        setPartner((String)value);
      }
      break;

    case COUPON:
      if (value == null) {
        unsetCoupon();
      } else {
        setCoupon((String)value);
      }
      break;

    case COUPON_STATUS:
      if (value == null) {
        unsetCouponStatus();
      } else {
        setCouponStatus((Integer)value);
      }
      break;

    case COUPON_ORDER_ID:
      if (value == null) {
        unsetCouponOrderId();
      } else {
        setCouponOrderId((Long)value);
      }
      break;

    case IS_SANDBOX:
      if (value == null) {
        unsetIsSandbox();
      } else {
        setIsSandbox((Integer)value);
      }
      break;

    case MONEY:
      if (value == null) {
        unsetMoney();
      } else {
        setMoney((String)value);
      }
      break;

    case COUPON_ID:
      if (value == null) {
        unsetCouponId();
      } else {
        setCouponId((Long)value);
      }
      break;

    case SERVER_ROOM:
      if (value == null) {
        unsetServerRoom();
      } else {
        setServerRoom((Integer)value);
      }
      break;

    case COIN_TYPE:
      if (value == null) {
        unsetCoinType();
      } else {
        setCoinType((Integer)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case RECHARGE_ID:
      return getRechargeId();

    case RECHARGE_ORDER_NUM:
      return getRechargeOrderNum();

    case CONSUME_ORDER_NUM:
      return getConsumeOrderNum();

    case ADD_TIME:
      return getAddTime();

    case RECHARGE_TIME:
      return getRechargeTime();

    case KUGOU_ID:
      return getKugouId();

    case FROM_KUGOU_ID:
      return getFromKugouId();

    case COIN:
      return getCoin();

    case AMOUNT:
      return getAmount();

    case REAL_AMOUNT:
      return getRealAmount();

    case PAY_TYPE_ID:
      return getPayTypeId();

    case EXTRA_JSON_DATA:
      return getExtraJsonData();

    case REFER:
      return getRefer();

    case C_FROM:
      return getCFrom();

    case CHANNEL_ID:
      return getChannelId();

    case RE_TYPE:
      return getReType();

    case EXTEND:
      return getExtend();

    case BUSINESS_ID:
      return getBusinessId();

    case TRADE_TIME:
      return getTradeTime();

    case TRADE_NO:
      return getTradeNo();

    case PARTNER:
      return getPartner();

    case COUPON:
      return getCoupon();

    case COUPON_STATUS:
      return getCouponStatus();

    case COUPON_ORDER_ID:
      return getCouponOrderId();

    case IS_SANDBOX:
      return getIsSandbox();

    case MONEY:
      return getMoney();

    case COUPON_ID:
      return getCouponId();

    case SERVER_ROOM:
      return getServerRoom();

    case COIN_TYPE:
      return getCoinType();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case RECHARGE_ID:
      return isSetRechargeId();
    case RECHARGE_ORDER_NUM:
      return isSetRechargeOrderNum();
    case CONSUME_ORDER_NUM:
      return isSetConsumeOrderNum();
    case ADD_TIME:
      return isSetAddTime();
    case RECHARGE_TIME:
      return isSetRechargeTime();
    case KUGOU_ID:
      return isSetKugouId();
    case FROM_KUGOU_ID:
      return isSetFromKugouId();
    case COIN:
      return isSetCoin();
    case AMOUNT:
      return isSetAmount();
    case REAL_AMOUNT:
      return isSetRealAmount();
    case PAY_TYPE_ID:
      return isSetPayTypeId();
    case EXTRA_JSON_DATA:
      return isSetExtraJsonData();
    case REFER:
      return isSetRefer();
    case C_FROM:
      return isSetCFrom();
    case CHANNEL_ID:
      return isSetChannelId();
    case RE_TYPE:
      return isSetReType();
    case EXTEND:
      return isSetExtend();
    case BUSINESS_ID:
      return isSetBusinessId();
    case TRADE_TIME:
      return isSetTradeTime();
    case TRADE_NO:
      return isSetTradeNo();
    case PARTNER:
      return isSetPartner();
    case COUPON:
      return isSetCoupon();
    case COUPON_STATUS:
      return isSetCouponStatus();
    case COUPON_ORDER_ID:
      return isSetCouponOrderId();
    case IS_SANDBOX:
      return isSetIsSandbox();
    case MONEY:
      return isSetMoney();
    case COUPON_ID:
      return isSetCouponId();
    case SERVER_ROOM:
      return isSetServerRoom();
    case COIN_TYPE:
      return isSetCoinType();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof RechargeSuccessInfo)
      return this.equals((RechargeSuccessInfo)that);
    return false;
  }

  public boolean equals(RechargeSuccessInfo that) {
    if (that == null)
      return false;

    boolean this_present_rechargeId = true;
    boolean that_present_rechargeId = true;
    if (this_present_rechargeId || that_present_rechargeId) {
      if (!(this_present_rechargeId && that_present_rechargeId))
        return false;
      if (this.rechargeId != that.rechargeId)
        return false;
    }

    boolean this_present_rechargeOrderNum = true && this.isSetRechargeOrderNum();
    boolean that_present_rechargeOrderNum = true && that.isSetRechargeOrderNum();
    if (this_present_rechargeOrderNum || that_present_rechargeOrderNum) {
      if (!(this_present_rechargeOrderNum && that_present_rechargeOrderNum))
        return false;
      if (!this.rechargeOrderNum.equals(that.rechargeOrderNum))
        return false;
    }

    boolean this_present_consumeOrderNum = true && this.isSetConsumeOrderNum();
    boolean that_present_consumeOrderNum = true && that.isSetConsumeOrderNum();
    if (this_present_consumeOrderNum || that_present_consumeOrderNum) {
      if (!(this_present_consumeOrderNum && that_present_consumeOrderNum))
        return false;
      if (!this.consumeOrderNum.equals(that.consumeOrderNum))
        return false;
    }

    boolean this_present_addTime = true;
    boolean that_present_addTime = true;
    if (this_present_addTime || that_present_addTime) {
      if (!(this_present_addTime && that_present_addTime))
        return false;
      if (this.addTime != that.addTime)
        return false;
    }

    boolean this_present_rechargeTime = true;
    boolean that_present_rechargeTime = true;
    if (this_present_rechargeTime || that_present_rechargeTime) {
      if (!(this_present_rechargeTime && that_present_rechargeTime))
        return false;
      if (this.rechargeTime != that.rechargeTime)
        return false;
    }

    boolean this_present_kugouId = true;
    boolean that_present_kugouId = true;
    if (this_present_kugouId || that_present_kugouId) {
      if (!(this_present_kugouId && that_present_kugouId))
        return false;
      if (this.kugouId != that.kugouId)
        return false;
    }

    boolean this_present_fromKugouId = true;
    boolean that_present_fromKugouId = true;
    if (this_present_fromKugouId || that_present_fromKugouId) {
      if (!(this_present_fromKugouId && that_present_fromKugouId))
        return false;
      if (this.fromKugouId != that.fromKugouId)
        return false;
    }

    boolean this_present_coin = true && this.isSetCoin();
    boolean that_present_coin = true && that.isSetCoin();
    if (this_present_coin || that_present_coin) {
      if (!(this_present_coin && that_present_coin))
        return false;
      if (!this.coin.equals(that.coin))
        return false;
    }

    boolean this_present_amount = true && this.isSetAmount();
    boolean that_present_amount = true && that.isSetAmount();
    if (this_present_amount || that_present_amount) {
      if (!(this_present_amount && that_present_amount))
        return false;
      if (!this.amount.equals(that.amount))
        return false;
    }

    boolean this_present_realAmount = true && this.isSetRealAmount();
    boolean that_present_realAmount = true && that.isSetRealAmount();
    if (this_present_realAmount || that_present_realAmount) {
      if (!(this_present_realAmount && that_present_realAmount))
        return false;
      if (!this.realAmount.equals(that.realAmount))
        return false;
    }

    boolean this_present_payTypeId = true;
    boolean that_present_payTypeId = true;
    if (this_present_payTypeId || that_present_payTypeId) {
      if (!(this_present_payTypeId && that_present_payTypeId))
        return false;
      if (this.payTypeId != that.payTypeId)
        return false;
    }

    boolean this_present_extraJsonData = true && this.isSetExtraJsonData();
    boolean that_present_extraJsonData = true && that.isSetExtraJsonData();
    if (this_present_extraJsonData || that_present_extraJsonData) {
      if (!(this_present_extraJsonData && that_present_extraJsonData))
        return false;
      if (!this.extraJsonData.equals(that.extraJsonData))
        return false;
    }

    boolean this_present_refer = true;
    boolean that_present_refer = true;
    if (this_present_refer || that_present_refer) {
      if (!(this_present_refer && that_present_refer))
        return false;
      if (this.refer != that.refer)
        return false;
    }

    boolean this_present_cFrom = true;
    boolean that_present_cFrom = true;
    if (this_present_cFrom || that_present_cFrom) {
      if (!(this_present_cFrom && that_present_cFrom))
        return false;
      if (this.cFrom != that.cFrom)
        return false;
    }

    boolean this_present_channelId = true;
    boolean that_present_channelId = true;
    if (this_present_channelId || that_present_channelId) {
      if (!(this_present_channelId && that_present_channelId))
        return false;
      if (this.channelId != that.channelId)
        return false;
    }

    boolean this_present_reType = true;
    boolean that_present_reType = true;
    if (this_present_reType || that_present_reType) {
      if (!(this_present_reType && that_present_reType))
        return false;
      if (this.reType != that.reType)
        return false;
    }

    boolean this_present_extend = true && this.isSetExtend();
    boolean that_present_extend = true && that.isSetExtend();
    if (this_present_extend || that_present_extend) {
      if (!(this_present_extend && that_present_extend))
        return false;
      if (!this.extend.equals(that.extend))
        return false;
    }

    boolean this_present_businessId = true && this.isSetBusinessId();
    boolean that_present_businessId = true && that.isSetBusinessId();
    if (this_present_businessId || that_present_businessId) {
      if (!(this_present_businessId && that_present_businessId))
        return false;
      if (!this.businessId.equals(that.businessId))
        return false;
    }

    boolean this_present_tradeTime = true;
    boolean that_present_tradeTime = true;
    if (this_present_tradeTime || that_present_tradeTime) {
      if (!(this_present_tradeTime && that_present_tradeTime))
        return false;
      if (this.tradeTime != that.tradeTime)
        return false;
    }

    boolean this_present_tradeNo = true && this.isSetTradeNo();
    boolean that_present_tradeNo = true && that.isSetTradeNo();
    if (this_present_tradeNo || that_present_tradeNo) {
      if (!(this_present_tradeNo && that_present_tradeNo))
        return false;
      if (!this.tradeNo.equals(that.tradeNo))
        return false;
    }

    boolean this_present_partner = true && this.isSetPartner();
    boolean that_present_partner = true && that.isSetPartner();
    if (this_present_partner || that_present_partner) {
      if (!(this_present_partner && that_present_partner))
        return false;
      if (!this.partner.equals(that.partner))
        return false;
    }

    boolean this_present_coupon = true && this.isSetCoupon();
    boolean that_present_coupon = true && that.isSetCoupon();
    if (this_present_coupon || that_present_coupon) {
      if (!(this_present_coupon && that_present_coupon))
        return false;
      if (!this.coupon.equals(that.coupon))
        return false;
    }

    boolean this_present_couponStatus = true;
    boolean that_present_couponStatus = true;
    if (this_present_couponStatus || that_present_couponStatus) {
      if (!(this_present_couponStatus && that_present_couponStatus))
        return false;
      if (this.couponStatus != that.couponStatus)
        return false;
    }

    boolean this_present_couponOrderId = true;
    boolean that_present_couponOrderId = true;
    if (this_present_couponOrderId || that_present_couponOrderId) {
      if (!(this_present_couponOrderId && that_present_couponOrderId))
        return false;
      if (this.couponOrderId != that.couponOrderId)
        return false;
    }

    boolean this_present_isSandbox = true;
    boolean that_present_isSandbox = true;
    if (this_present_isSandbox || that_present_isSandbox) {
      if (!(this_present_isSandbox && that_present_isSandbox))
        return false;
      if (this.isSandbox != that.isSandbox)
        return false;
    }

    boolean this_present_money = true && this.isSetMoney();
    boolean that_present_money = true && that.isSetMoney();
    if (this_present_money || that_present_money) {
      if (!(this_present_money && that_present_money))
        return false;
      if (!this.money.equals(that.money))
        return false;
    }

    boolean this_present_couponId = true;
    boolean that_present_couponId = true;
    if (this_present_couponId || that_present_couponId) {
      if (!(this_present_couponId && that_present_couponId))
        return false;
      if (this.couponId != that.couponId)
        return false;
    }

    boolean this_present_serverRoom = true;
    boolean that_present_serverRoom = true;
    if (this_present_serverRoom || that_present_serverRoom) {
      if (!(this_present_serverRoom && that_present_serverRoom))
        return false;
      if (this.serverRoom != that.serverRoom)
        return false;
    }

    boolean this_present_coinType = true && this.isSetCoinType();
    boolean that_present_coinType = true && that.isSetCoinType();
    if (this_present_coinType || that_present_coinType) {
      if (!(this_present_coinType && that_present_coinType))
        return false;
      if (this.coinType != that.coinType)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_rechargeId = true;
    list.add(present_rechargeId);
    if (present_rechargeId)
      list.add(rechargeId);

    boolean present_rechargeOrderNum = true && (isSetRechargeOrderNum());
    list.add(present_rechargeOrderNum);
    if (present_rechargeOrderNum)
      list.add(rechargeOrderNum);

    boolean present_consumeOrderNum = true && (isSetConsumeOrderNum());
    list.add(present_consumeOrderNum);
    if (present_consumeOrderNum)
      list.add(consumeOrderNum);

    boolean present_addTime = true;
    list.add(present_addTime);
    if (present_addTime)
      list.add(addTime);

    boolean present_rechargeTime = true;
    list.add(present_rechargeTime);
    if (present_rechargeTime)
      list.add(rechargeTime);

    boolean present_kugouId = true;
    list.add(present_kugouId);
    if (present_kugouId)
      list.add(kugouId);

    boolean present_fromKugouId = true;
    list.add(present_fromKugouId);
    if (present_fromKugouId)
      list.add(fromKugouId);

    boolean present_coin = true && (isSetCoin());
    list.add(present_coin);
    if (present_coin)
      list.add(coin);

    boolean present_amount = true && (isSetAmount());
    list.add(present_amount);
    if (present_amount)
      list.add(amount);

    boolean present_realAmount = true && (isSetRealAmount());
    list.add(present_realAmount);
    if (present_realAmount)
      list.add(realAmount);

    boolean present_payTypeId = true;
    list.add(present_payTypeId);
    if (present_payTypeId)
      list.add(payTypeId);

    boolean present_extraJsonData = true && (isSetExtraJsonData());
    list.add(present_extraJsonData);
    if (present_extraJsonData)
      list.add(extraJsonData);

    boolean present_refer = true;
    list.add(present_refer);
    if (present_refer)
      list.add(refer);

    boolean present_cFrom = true;
    list.add(present_cFrom);
    if (present_cFrom)
      list.add(cFrom);

    boolean present_channelId = true;
    list.add(present_channelId);
    if (present_channelId)
      list.add(channelId);

    boolean present_reType = true;
    list.add(present_reType);
    if (present_reType)
      list.add(reType);

    boolean present_extend = true && (isSetExtend());
    list.add(present_extend);
    if (present_extend)
      list.add(extend);

    boolean present_businessId = true && (isSetBusinessId());
    list.add(present_businessId);
    if (present_businessId)
      list.add(businessId);

    boolean present_tradeTime = true;
    list.add(present_tradeTime);
    if (present_tradeTime)
      list.add(tradeTime);

    boolean present_tradeNo = true && (isSetTradeNo());
    list.add(present_tradeNo);
    if (present_tradeNo)
      list.add(tradeNo);

    boolean present_partner = true && (isSetPartner());
    list.add(present_partner);
    if (present_partner)
      list.add(partner);

    boolean present_coupon = true && (isSetCoupon());
    list.add(present_coupon);
    if (present_coupon)
      list.add(coupon);

    boolean present_couponStatus = true;
    list.add(present_couponStatus);
    if (present_couponStatus)
      list.add(couponStatus);

    boolean present_couponOrderId = true;
    list.add(present_couponOrderId);
    if (present_couponOrderId)
      list.add(couponOrderId);

    boolean present_isSandbox = true;
    list.add(present_isSandbox);
    if (present_isSandbox)
      list.add(isSandbox);

    boolean present_money = true && (isSetMoney());
    list.add(present_money);
    if (present_money)
      list.add(money);

    boolean present_couponId = true;
    list.add(present_couponId);
    if (present_couponId)
      list.add(couponId);

    boolean present_serverRoom = true;
    list.add(present_serverRoom);
    if (present_serverRoom)
      list.add(serverRoom);

    boolean present_coinType = true && (isSetCoinType());
    list.add(present_coinType);
    if (present_coinType)
      list.add(coinType);

    return list.hashCode();
  }

  @Override
  public int compareTo(RechargeSuccessInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRechargeId()).compareTo(other.isSetRechargeId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRechargeId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rechargeId, other.rechargeId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRechargeOrderNum()).compareTo(other.isSetRechargeOrderNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRechargeOrderNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rechargeOrderNum, other.rechargeOrderNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetConsumeOrderNum()).compareTo(other.isSetConsumeOrderNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetConsumeOrderNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.consumeOrderNum, other.consumeOrderNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAddTime()).compareTo(other.isSetAddTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAddTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.addTime, other.addTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRechargeTime()).compareTo(other.isSetRechargeTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRechargeTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rechargeTime, other.rechargeTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKugouId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetFromKugouId()).compareTo(other.isSetFromKugouId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFromKugouId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.fromKugouId, other.fromKugouId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCoin()).compareTo(other.isSetCoin());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCoin()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.coin, other.coin);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAmount()).compareTo(other.isSetAmount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAmount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.amount, other.amount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRealAmount()).compareTo(other.isSetRealAmount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRealAmount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.realAmount, other.realAmount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPayTypeId()).compareTo(other.isSetPayTypeId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPayTypeId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.payTypeId, other.payTypeId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtraJsonData()).compareTo(other.isSetExtraJsonData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtraJsonData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extraJsonData, other.extraJsonData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRefer()).compareTo(other.isSetRefer());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRefer()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.refer, other.refer);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCFrom()).compareTo(other.isSetCFrom());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCFrom()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.cFrom, other.cFrom);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetChannelId()).compareTo(other.isSetChannelId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetChannelId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.channelId, other.channelId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetReType()).compareTo(other.isSetReType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetReType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.reType, other.reType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtend()).compareTo(other.isSetExtend());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtend()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extend, other.extend);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBusinessId()).compareTo(other.isSetBusinessId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBusinessId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.businessId, other.businessId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTradeTime()).compareTo(other.isSetTradeTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTradeTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.tradeTime, other.tradeTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTradeNo()).compareTo(other.isSetTradeNo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTradeNo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.tradeNo, other.tradeNo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPartner()).compareTo(other.isSetPartner());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPartner()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.partner, other.partner);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCoupon()).compareTo(other.isSetCoupon());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCoupon()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.coupon, other.coupon);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponStatus()).compareTo(other.isSetCouponStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponStatus, other.couponStatus);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponOrderId()).compareTo(other.isSetCouponOrderId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponOrderId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponOrderId, other.couponOrderId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIsSandbox()).compareTo(other.isSetIsSandbox());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsSandbox()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isSandbox, other.isSandbox);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMoney()).compareTo(other.isSetMoney());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMoney()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.money, other.money);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponId()).compareTo(other.isSetCouponId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponId, other.couponId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetServerRoom()).compareTo(other.isSetServerRoom());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetServerRoom()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.serverRoom, other.serverRoom);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCoinType()).compareTo(other.isSetCoinType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCoinType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.coinType, other.coinType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("RechargeSuccessInfo(");
    boolean first = true;

    sb.append("rechargeId:");
    sb.append(this.rechargeId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("rechargeOrderNum:");
    if (this.rechargeOrderNum == null) {
      sb.append("null");
    } else {
      sb.append(this.rechargeOrderNum);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("consumeOrderNum:");
    if (this.consumeOrderNum == null) {
      sb.append("null");
    } else {
      sb.append(this.consumeOrderNum);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("addTime:");
    sb.append(this.addTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("rechargeTime:");
    sb.append(this.rechargeTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("kugouId:");
    sb.append(this.kugouId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("fromKugouId:");
    sb.append(this.fromKugouId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("coin:");
    if (this.coin == null) {
      sb.append("null");
    } else {
      sb.append(this.coin);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("amount:");
    if (this.amount == null) {
      sb.append("null");
    } else {
      sb.append(this.amount);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("realAmount:");
    if (this.realAmount == null) {
      sb.append("null");
    } else {
      sb.append(this.realAmount);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("payTypeId:");
    sb.append(this.payTypeId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("extraJsonData:");
    if (this.extraJsonData == null) {
      sb.append("null");
    } else {
      sb.append(this.extraJsonData);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("refer:");
    sb.append(this.refer);
    first = false;
    if (!first) sb.append(", ");
    sb.append("cFrom:");
    sb.append(this.cFrom);
    first = false;
    if (!first) sb.append(", ");
    sb.append("channelId:");
    sb.append(this.channelId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("reType:");
    sb.append(this.reType);
    first = false;
    if (!first) sb.append(", ");
    sb.append("extend:");
    if (this.extend == null) {
      sb.append("null");
    } else {
      sb.append(this.extend);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("businessId:");
    if (this.businessId == null) {
      sb.append("null");
    } else {
      sb.append(this.businessId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("tradeTime:");
    sb.append(this.tradeTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("tradeNo:");
    if (this.tradeNo == null) {
      sb.append("null");
    } else {
      sb.append(this.tradeNo);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("partner:");
    if (this.partner == null) {
      sb.append("null");
    } else {
      sb.append(this.partner);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("coupon:");
    if (this.coupon == null) {
      sb.append("null");
    } else {
      sb.append(this.coupon);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponStatus:");
    sb.append(this.couponStatus);
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponOrderId:");
    sb.append(this.couponOrderId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("isSandbox:");
    sb.append(this.isSandbox);
    first = false;
    if (!first) sb.append(", ");
    sb.append("money:");
    if (this.money == null) {
      sb.append("null");
    } else {
      sb.append(this.money);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponId:");
    sb.append(this.couponId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("serverRoom:");
    sb.append(this.serverRoom);
    first = false;
    if (isSetCoinType()) {
      if (!first) sb.append(", ");
      sb.append("coinType:");
      sb.append(this.coinType);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'rechargeId' because it's a primitive and you chose the non-beans generator.
    if (rechargeOrderNum == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'rechargeOrderNum' was not present! Struct: " + toString());
    }
    if (consumeOrderNum == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'consumeOrderNum' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'addTime' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'rechargeTime' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'fromKugouId' because it's a primitive and you chose the non-beans generator.
    if (coin == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'coin' was not present! Struct: " + toString());
    }
    if (amount == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'amount' was not present! Struct: " + toString());
    }
    if (realAmount == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'realAmount' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'payTypeId' because it's a primitive and you chose the non-beans generator.
    if (extraJsonData == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'extraJsonData' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'refer' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'cFrom' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'channelId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'reType' because it's a primitive and you chose the non-beans generator.
    if (extend == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'extend' was not present! Struct: " + toString());
    }
    if (businessId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'businessId' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'tradeTime' because it's a primitive and you chose the non-beans generator.
    if (tradeNo == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'tradeNo' was not present! Struct: " + toString());
    }
    if (partner == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'partner' was not present! Struct: " + toString());
    }
    if (coupon == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'coupon' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'couponStatus' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'couponOrderId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'isSandbox' because it's a primitive and you chose the non-beans generator.
    if (money == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'money' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'couponId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'serverRoom' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class RechargeSuccessInfoStandardSchemeFactory implements SchemeFactory {
    public RechargeSuccessInfoStandardScheme getScheme() {
      return new RechargeSuccessInfoStandardScheme();
    }
  }

  private static class RechargeSuccessInfoStandardScheme extends StandardScheme<RechargeSuccessInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, RechargeSuccessInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // RECHARGE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rechargeId = iprot.readI64();
              struct.setRechargeIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RECHARGE_ORDER_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.rechargeOrderNum = iprot.readString();
              struct.setRechargeOrderNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // CONSUME_ORDER_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.consumeOrderNum = iprot.readString();
              struct.setConsumeOrderNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // ADD_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.addTime = iprot.readI64();
              struct.setAddTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // RECHARGE_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rechargeTime = iprot.readI64();
              struct.setRechargeTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // KUGOU_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.kugouId = iprot.readI64();
              struct.setKugouIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // FROM_KUGOU_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.fromKugouId = iprot.readI64();
              struct.setFromKugouIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // COIN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.coin = iprot.readString();
              struct.setCoinIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // AMOUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.amount = iprot.readString();
              struct.setAmountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // REAL_AMOUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.realAmount = iprot.readString();
              struct.setRealAmountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // PAY_TYPE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.payTypeId = iprot.readI32();
              struct.setPayTypeIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // EXTRA_JSON_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.extraJsonData = iprot.readString();
              struct.setExtraJsonDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // REFER
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.refer = iprot.readI32();
              struct.setReferIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 14: // C_FROM
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.cFrom = iprot.readI32();
              struct.setCFromIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // CHANNEL_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.channelId = iprot.readI32();
              struct.setChannelIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 16: // RE_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.reType = iprot.readI32();
              struct.setReTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 17: // EXTEND
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.extend = iprot.readString();
              struct.setExtendIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 18: // BUSINESS_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.businessId = iprot.readString();
              struct.setBusinessIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 19: // TRADE_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.tradeTime = iprot.readI64();
              struct.setTradeTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 20: // TRADE_NO
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.tradeNo = iprot.readString();
              struct.setTradeNoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 21: // PARTNER
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.partner = iprot.readString();
              struct.setPartnerIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 22: // COUPON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.coupon = iprot.readString();
              struct.setCouponIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 23: // COUPON_STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.couponStatus = iprot.readI32();
              struct.setCouponStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 24: // COUPON_ORDER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.couponOrderId = iprot.readI64();
              struct.setCouponOrderIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 25: // IS_SANDBOX
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.isSandbox = iprot.readI32();
              struct.setIsSandboxIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 26: // MONEY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.money = iprot.readString();
              struct.setMoneyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 27: // COUPON_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.couponId = iprot.readI64();
              struct.setCouponIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 28: // SERVER_ROOM
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.serverRoom = iprot.readI32();
              struct.setServerRoomIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 29: // COIN_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.coinType = iprot.readI32();
              struct.setCoinTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetRechargeId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'rechargeId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetAddTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'addTime' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetRechargeTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'rechargeTime' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetKugouId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetFromKugouId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'fromKugouId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetPayTypeId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'payTypeId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetRefer()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'refer' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetCFrom()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'cFrom' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetChannelId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'channelId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetReType()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'reType' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetTradeTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'tradeTime' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetCouponStatus()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'couponStatus' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetCouponOrderId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'couponOrderId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetIsSandbox()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'isSandbox' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetCouponId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'couponId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetServerRoom()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'serverRoom' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, RechargeSuccessInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(RECHARGE_ID_FIELD_DESC);
      oprot.writeI64(struct.rechargeId);
      oprot.writeFieldEnd();
      if (struct.rechargeOrderNum != null) {
        oprot.writeFieldBegin(RECHARGE_ORDER_NUM_FIELD_DESC);
        oprot.writeString(struct.rechargeOrderNum);
        oprot.writeFieldEnd();
      }
      if (struct.consumeOrderNum != null) {
        oprot.writeFieldBegin(CONSUME_ORDER_NUM_FIELD_DESC);
        oprot.writeString(struct.consumeOrderNum);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(ADD_TIME_FIELD_DESC);
      oprot.writeI64(struct.addTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RECHARGE_TIME_FIELD_DESC);
      oprot.writeI64(struct.rechargeTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
      oprot.writeI64(struct.kugouId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(FROM_KUGOU_ID_FIELD_DESC);
      oprot.writeI64(struct.fromKugouId);
      oprot.writeFieldEnd();
      if (struct.coin != null) {
        oprot.writeFieldBegin(COIN_FIELD_DESC);
        oprot.writeString(struct.coin);
        oprot.writeFieldEnd();
      }
      if (struct.amount != null) {
        oprot.writeFieldBegin(AMOUNT_FIELD_DESC);
        oprot.writeString(struct.amount);
        oprot.writeFieldEnd();
      }
      if (struct.realAmount != null) {
        oprot.writeFieldBegin(REAL_AMOUNT_FIELD_DESC);
        oprot.writeString(struct.realAmount);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(PAY_TYPE_ID_FIELD_DESC);
      oprot.writeI32(struct.payTypeId);
      oprot.writeFieldEnd();
      if (struct.extraJsonData != null) {
        oprot.writeFieldBegin(EXTRA_JSON_DATA_FIELD_DESC);
        oprot.writeString(struct.extraJsonData);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(REFER_FIELD_DESC);
      oprot.writeI32(struct.refer);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(C_FROM_FIELD_DESC);
      oprot.writeI32(struct.cFrom);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(CHANNEL_ID_FIELD_DESC);
      oprot.writeI32(struct.channelId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RE_TYPE_FIELD_DESC);
      oprot.writeI32(struct.reType);
      oprot.writeFieldEnd();
      if (struct.extend != null) {
        oprot.writeFieldBegin(EXTEND_FIELD_DESC);
        oprot.writeString(struct.extend);
        oprot.writeFieldEnd();
      }
      if (struct.businessId != null) {
        oprot.writeFieldBegin(BUSINESS_ID_FIELD_DESC);
        oprot.writeString(struct.businessId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(TRADE_TIME_FIELD_DESC);
      oprot.writeI64(struct.tradeTime);
      oprot.writeFieldEnd();
      if (struct.tradeNo != null) {
        oprot.writeFieldBegin(TRADE_NO_FIELD_DESC);
        oprot.writeString(struct.tradeNo);
        oprot.writeFieldEnd();
      }
      if (struct.partner != null) {
        oprot.writeFieldBegin(PARTNER_FIELD_DESC);
        oprot.writeString(struct.partner);
        oprot.writeFieldEnd();
      }
      if (struct.coupon != null) {
        oprot.writeFieldBegin(COUPON_FIELD_DESC);
        oprot.writeString(struct.coupon);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(COUPON_STATUS_FIELD_DESC);
      oprot.writeI32(struct.couponStatus);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(COUPON_ORDER_ID_FIELD_DESC);
      oprot.writeI64(struct.couponOrderId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(IS_SANDBOX_FIELD_DESC);
      oprot.writeI32(struct.isSandbox);
      oprot.writeFieldEnd();
      if (struct.money != null) {
        oprot.writeFieldBegin(MONEY_FIELD_DESC);
        oprot.writeString(struct.money);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(COUPON_ID_FIELD_DESC);
      oprot.writeI64(struct.couponId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SERVER_ROOM_FIELD_DESC);
      oprot.writeI32(struct.serverRoom);
      oprot.writeFieldEnd();
      if (struct.isSetCoinType()) {
        oprot.writeFieldBegin(COIN_TYPE_FIELD_DESC);
        oprot.writeI32(struct.coinType);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class RechargeSuccessInfoTupleSchemeFactory implements SchemeFactory {
    public RechargeSuccessInfoTupleScheme getScheme() {
      return new RechargeSuccessInfoTupleScheme();
    }
  }

  private static class RechargeSuccessInfoTupleScheme extends TupleScheme<RechargeSuccessInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, RechargeSuccessInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeI64(struct.rechargeId);
      oprot.writeString(struct.rechargeOrderNum);
      oprot.writeString(struct.consumeOrderNum);
      oprot.writeI64(struct.addTime);
      oprot.writeI64(struct.rechargeTime);
      oprot.writeI64(struct.kugouId);
      oprot.writeI64(struct.fromKugouId);
      oprot.writeString(struct.coin);
      oprot.writeString(struct.amount);
      oprot.writeString(struct.realAmount);
      oprot.writeI32(struct.payTypeId);
      oprot.writeString(struct.extraJsonData);
      oprot.writeI32(struct.refer);
      oprot.writeI32(struct.cFrom);
      oprot.writeI32(struct.channelId);
      oprot.writeI32(struct.reType);
      oprot.writeString(struct.extend);
      oprot.writeString(struct.businessId);
      oprot.writeI64(struct.tradeTime);
      oprot.writeString(struct.tradeNo);
      oprot.writeString(struct.partner);
      oprot.writeString(struct.coupon);
      oprot.writeI32(struct.couponStatus);
      oprot.writeI64(struct.couponOrderId);
      oprot.writeI32(struct.isSandbox);
      oprot.writeString(struct.money);
      oprot.writeI64(struct.couponId);
      oprot.writeI32(struct.serverRoom);
      BitSet optionals = new BitSet();
      if (struct.isSetCoinType()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetCoinType()) {
        oprot.writeI32(struct.coinType);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, RechargeSuccessInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.rechargeId = iprot.readI64();
      struct.setRechargeIdIsSet(true);
      struct.rechargeOrderNum = iprot.readString();
      struct.setRechargeOrderNumIsSet(true);
      struct.consumeOrderNum = iprot.readString();
      struct.setConsumeOrderNumIsSet(true);
      struct.addTime = iprot.readI64();
      struct.setAddTimeIsSet(true);
      struct.rechargeTime = iprot.readI64();
      struct.setRechargeTimeIsSet(true);
      struct.kugouId = iprot.readI64();
      struct.setKugouIdIsSet(true);
      struct.fromKugouId = iprot.readI64();
      struct.setFromKugouIdIsSet(true);
      struct.coin = iprot.readString();
      struct.setCoinIsSet(true);
      struct.amount = iprot.readString();
      struct.setAmountIsSet(true);
      struct.realAmount = iprot.readString();
      struct.setRealAmountIsSet(true);
      struct.payTypeId = iprot.readI32();
      struct.setPayTypeIdIsSet(true);
      struct.extraJsonData = iprot.readString();
      struct.setExtraJsonDataIsSet(true);
      struct.refer = iprot.readI32();
      struct.setReferIsSet(true);
      struct.cFrom = iprot.readI32();
      struct.setCFromIsSet(true);
      struct.channelId = iprot.readI32();
      struct.setChannelIdIsSet(true);
      struct.reType = iprot.readI32();
      struct.setReTypeIsSet(true);
      struct.extend = iprot.readString();
      struct.setExtendIsSet(true);
      struct.businessId = iprot.readString();
      struct.setBusinessIdIsSet(true);
      struct.tradeTime = iprot.readI64();
      struct.setTradeTimeIsSet(true);
      struct.tradeNo = iprot.readString();
      struct.setTradeNoIsSet(true);
      struct.partner = iprot.readString();
      struct.setPartnerIsSet(true);
      struct.coupon = iprot.readString();
      struct.setCouponIsSet(true);
      struct.couponStatus = iprot.readI32();
      struct.setCouponStatusIsSet(true);
      struct.couponOrderId = iprot.readI64();
      struct.setCouponOrderIdIsSet(true);
      struct.isSandbox = iprot.readI32();
      struct.setIsSandboxIsSet(true);
      struct.money = iprot.readString();
      struct.setMoneyIsSet(true);
      struct.couponId = iprot.readI64();
      struct.setCouponIdIsSet(true);
      struct.serverRoom = iprot.readI32();
      struct.setServerRoomIsSet(true);
      BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        struct.coinType = iprot.readI32();
        struct.setCoinTypeIsSet(true);
      }
    }
  }

}

