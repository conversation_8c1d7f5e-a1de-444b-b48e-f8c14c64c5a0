/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.recharge.thrift;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-10-24")
public class RechargeAcrossDTO implements org.apache.thrift.TBase<RechargeAcrossDTO, RechargeAcrossDTO._Fields>, java.io.Serializable, Cloneable, Comparable<RechargeAcrossDTO> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("RechargeAcrossDTO");

  private static final org.apache.thrift.protocol.TField PAY_TYPE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("payTypeId", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField PAY_TYPE_LABEL_FIELD_DESC = new org.apache.thrift.protocol.TField("payTypeLabel", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("status", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField STATUS_LABEL_FIELD_DESC = new org.apache.thrift.protocol.TField("statusLabel", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField RECHARGE_ORDER_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("rechargeOrderNum", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField RECHARGE_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("rechargeTime", org.apache.thrift.protocol.TType.I32, (short)6);
  private static final org.apache.thrift.protocol.TField TRADE_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("tradeTime", org.apache.thrift.protocol.TType.I32, (short)7);
  private static final org.apache.thrift.protocol.TField COIN_FIELD_DESC = new org.apache.thrift.protocol.TField("coin", org.apache.thrift.protocol.TType.STRING, (short)8);
  private static final org.apache.thrift.protocol.TField MONEY_FIELD_DESC = new org.apache.thrift.protocol.TField("money", org.apache.thrift.protocol.TType.STRING, (short)9);
  private static final org.apache.thrift.protocol.TField COUPON_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("couponId", org.apache.thrift.protocol.TType.STRING, (short)10);
  private static final org.apache.thrift.protocol.TField COUPON_FIELD_DESC = new org.apache.thrift.protocol.TField("coupon", org.apache.thrift.protocol.TType.STRING, (short)11);
  private static final org.apache.thrift.protocol.TField COUPON_STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("couponStatus", org.apache.thrift.protocol.TType.I32, (short)12);
  private static final org.apache.thrift.protocol.TField COUPON_STATUS_LABEL_FIELD_DESC = new org.apache.thrift.protocol.TField("couponStatusLabel", org.apache.thrift.protocol.TType.STRING, (short)13);
  private static final org.apache.thrift.protocol.TField COUPON_ERROR_CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("couponErrorCode", org.apache.thrift.protocol.TType.I32, (short)14);
  private static final org.apache.thrift.protocol.TField COUPON_ERROR_MSG_FIELD_DESC = new org.apache.thrift.protocol.TField("couponErrorMsg", org.apache.thrift.protocol.TType.STRING, (short)15);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new RechargeAcrossDTOStandardSchemeFactory());
    schemes.put(TupleScheme.class, new RechargeAcrossDTOTupleSchemeFactory());
  }

  public int payTypeId; // required
  public String payTypeLabel; // required
  public int status; // required
  public String statusLabel; // required
  public String rechargeOrderNum; // required
  public int rechargeTime; // required
  public int tradeTime; // required
  public String coin; // required
  public String money; // required
  public String couponId; // required
  public String coupon; // required
  public int couponStatus; // required
  public String couponStatusLabel; // required
  public int couponErrorCode; // required
  public String couponErrorMsg; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    PAY_TYPE_ID((short)1, "payTypeId"),
    PAY_TYPE_LABEL((short)2, "payTypeLabel"),
    STATUS((short)3, "status"),
    STATUS_LABEL((short)4, "statusLabel"),
    RECHARGE_ORDER_NUM((short)5, "rechargeOrderNum"),
    RECHARGE_TIME((short)6, "rechargeTime"),
    TRADE_TIME((short)7, "tradeTime"),
    COIN((short)8, "coin"),
    MONEY((short)9, "money"),
    COUPON_ID((short)10, "couponId"),
    COUPON((short)11, "coupon"),
    COUPON_STATUS((short)12, "couponStatus"),
    COUPON_STATUS_LABEL((short)13, "couponStatusLabel"),
    COUPON_ERROR_CODE((short)14, "couponErrorCode"),
    COUPON_ERROR_MSG((short)15, "couponErrorMsg");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // PAY_TYPE_ID
          return PAY_TYPE_ID;
        case 2: // PAY_TYPE_LABEL
          return PAY_TYPE_LABEL;
        case 3: // STATUS
          return STATUS;
        case 4: // STATUS_LABEL
          return STATUS_LABEL;
        case 5: // RECHARGE_ORDER_NUM
          return RECHARGE_ORDER_NUM;
        case 6: // RECHARGE_TIME
          return RECHARGE_TIME;
        case 7: // TRADE_TIME
          return TRADE_TIME;
        case 8: // COIN
          return COIN;
        case 9: // MONEY
          return MONEY;
        case 10: // COUPON_ID
          return COUPON_ID;
        case 11: // COUPON
          return COUPON;
        case 12: // COUPON_STATUS
          return COUPON_STATUS;
        case 13: // COUPON_STATUS_LABEL
          return COUPON_STATUS_LABEL;
        case 14: // COUPON_ERROR_CODE
          return COUPON_ERROR_CODE;
        case 15: // COUPON_ERROR_MSG
          return COUPON_ERROR_MSG;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __PAYTYPEID_ISSET_ID = 0;
  private static final int __STATUS_ISSET_ID = 1;
  private static final int __RECHARGETIME_ISSET_ID = 2;
  private static final int __TRADETIME_ISSET_ID = 3;
  private static final int __COUPONSTATUS_ISSET_ID = 4;
  private static final int __COUPONERRORCODE_ISSET_ID = 5;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.PAY_TYPE_ID, new org.apache.thrift.meta_data.FieldMetaData("payTypeId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.PAY_TYPE_LABEL, new org.apache.thrift.meta_data.FieldMetaData("payTypeLabel", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.STATUS, new org.apache.thrift.meta_data.FieldMetaData("status", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.STATUS_LABEL, new org.apache.thrift.meta_data.FieldMetaData("statusLabel", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.RECHARGE_ORDER_NUM, new org.apache.thrift.meta_data.FieldMetaData("rechargeOrderNum", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.RECHARGE_TIME, new org.apache.thrift.meta_data.FieldMetaData("rechargeTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TRADE_TIME, new org.apache.thrift.meta_data.FieldMetaData("tradeTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.COIN, new org.apache.thrift.meta_data.FieldMetaData("coin", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.MONEY, new org.apache.thrift.meta_data.FieldMetaData("money", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.COUPON_ID, new org.apache.thrift.meta_data.FieldMetaData("couponId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.COUPON, new org.apache.thrift.meta_data.FieldMetaData("coupon", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.COUPON_STATUS, new org.apache.thrift.meta_data.FieldMetaData("couponStatus", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.COUPON_STATUS_LABEL, new org.apache.thrift.meta_data.FieldMetaData("couponStatusLabel", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.COUPON_ERROR_CODE, new org.apache.thrift.meta_data.FieldMetaData("couponErrorCode", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.COUPON_ERROR_MSG, new org.apache.thrift.meta_data.FieldMetaData("couponErrorMsg", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(RechargeAcrossDTO.class, metaDataMap);
  }

  public RechargeAcrossDTO() {
  }

  public RechargeAcrossDTO(
    int payTypeId,
    String payTypeLabel,
    int status,
    String statusLabel,
    String rechargeOrderNum,
    int rechargeTime,
    int tradeTime,
    String coin,
    String money,
    String couponId,
    String coupon,
    int couponStatus,
    String couponStatusLabel,
    int couponErrorCode,
    String couponErrorMsg)
  {
    this();
    this.payTypeId = payTypeId;
    setPayTypeIdIsSet(true);
    this.payTypeLabel = payTypeLabel;
    this.status = status;
    setStatusIsSet(true);
    this.statusLabel = statusLabel;
    this.rechargeOrderNum = rechargeOrderNum;
    this.rechargeTime = rechargeTime;
    setRechargeTimeIsSet(true);
    this.tradeTime = tradeTime;
    setTradeTimeIsSet(true);
    this.coin = coin;
    this.money = money;
    this.couponId = couponId;
    this.coupon = coupon;
    this.couponStatus = couponStatus;
    setCouponStatusIsSet(true);
    this.couponStatusLabel = couponStatusLabel;
    this.couponErrorCode = couponErrorCode;
    setCouponErrorCodeIsSet(true);
    this.couponErrorMsg = couponErrorMsg;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public RechargeAcrossDTO(RechargeAcrossDTO other) {
    __isset_bitfield = other.__isset_bitfield;
    this.payTypeId = other.payTypeId;
    if (other.isSetPayTypeLabel()) {
      this.payTypeLabel = other.payTypeLabel;
    }
    this.status = other.status;
    if (other.isSetStatusLabel()) {
      this.statusLabel = other.statusLabel;
    }
    if (other.isSetRechargeOrderNum()) {
      this.rechargeOrderNum = other.rechargeOrderNum;
    }
    this.rechargeTime = other.rechargeTime;
    this.tradeTime = other.tradeTime;
    if (other.isSetCoin()) {
      this.coin = other.coin;
    }
    if (other.isSetMoney()) {
      this.money = other.money;
    }
    if (other.isSetCouponId()) {
      this.couponId = other.couponId;
    }
    if (other.isSetCoupon()) {
      this.coupon = other.coupon;
    }
    this.couponStatus = other.couponStatus;
    if (other.isSetCouponStatusLabel()) {
      this.couponStatusLabel = other.couponStatusLabel;
    }
    this.couponErrorCode = other.couponErrorCode;
    if (other.isSetCouponErrorMsg()) {
      this.couponErrorMsg = other.couponErrorMsg;
    }
  }

  public RechargeAcrossDTO deepCopy() {
    return new RechargeAcrossDTO(this);
  }

  @Override
  public void clear() {
    setPayTypeIdIsSet(false);
    this.payTypeId = 0;
    this.payTypeLabel = null;
    setStatusIsSet(false);
    this.status = 0;
    this.statusLabel = null;
    this.rechargeOrderNum = null;
    setRechargeTimeIsSet(false);
    this.rechargeTime = 0;
    setTradeTimeIsSet(false);
    this.tradeTime = 0;
    this.coin = null;
    this.money = null;
    this.couponId = null;
    this.coupon = null;
    setCouponStatusIsSet(false);
    this.couponStatus = 0;
    this.couponStatusLabel = null;
    setCouponErrorCodeIsSet(false);
    this.couponErrorCode = 0;
    this.couponErrorMsg = null;
  }

  public int getPayTypeId() {
    return this.payTypeId;
  }

  public RechargeAcrossDTO setPayTypeId(int payTypeId) {
    this.payTypeId = payTypeId;
    setPayTypeIdIsSet(true);
    return this;
  }

  public void unsetPayTypeId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAYTYPEID_ISSET_ID);
  }

  /** Returns true if field payTypeId is set (has been assigned a value) and false otherwise */
  public boolean isSetPayTypeId() {
    return EncodingUtils.testBit(__isset_bitfield, __PAYTYPEID_ISSET_ID);
  }

  public void setPayTypeIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAYTYPEID_ISSET_ID, value);
  }

  public String getPayTypeLabel() {
    return this.payTypeLabel;
  }

  public RechargeAcrossDTO setPayTypeLabel(String payTypeLabel) {
    this.payTypeLabel = payTypeLabel;
    return this;
  }

  public void unsetPayTypeLabel() {
    this.payTypeLabel = null;
  }

  /** Returns true if field payTypeLabel is set (has been assigned a value) and false otherwise */
  public boolean isSetPayTypeLabel() {
    return this.payTypeLabel != null;
  }

  public void setPayTypeLabelIsSet(boolean value) {
    if (!value) {
      this.payTypeLabel = null;
    }
  }

  public int getStatus() {
    return this.status;
  }

  public RechargeAcrossDTO setStatus(int status) {
    this.status = status;
    setStatusIsSet(true);
    return this;
  }

  public void unsetStatus() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  /** Returns true if field status is set (has been assigned a value) and false otherwise */
  public boolean isSetStatus() {
    return EncodingUtils.testBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  public void setStatusIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STATUS_ISSET_ID, value);
  }

  public String getStatusLabel() {
    return this.statusLabel;
  }

  public RechargeAcrossDTO setStatusLabel(String statusLabel) {
    this.statusLabel = statusLabel;
    return this;
  }

  public void unsetStatusLabel() {
    this.statusLabel = null;
  }

  /** Returns true if field statusLabel is set (has been assigned a value) and false otherwise */
  public boolean isSetStatusLabel() {
    return this.statusLabel != null;
  }

  public void setStatusLabelIsSet(boolean value) {
    if (!value) {
      this.statusLabel = null;
    }
  }

  public String getRechargeOrderNum() {
    return this.rechargeOrderNum;
  }

  public RechargeAcrossDTO setRechargeOrderNum(String rechargeOrderNum) {
    this.rechargeOrderNum = rechargeOrderNum;
    return this;
  }

  public void unsetRechargeOrderNum() {
    this.rechargeOrderNum = null;
  }

  /** Returns true if field rechargeOrderNum is set (has been assigned a value) and false otherwise */
  public boolean isSetRechargeOrderNum() {
    return this.rechargeOrderNum != null;
  }

  public void setRechargeOrderNumIsSet(boolean value) {
    if (!value) {
      this.rechargeOrderNum = null;
    }
  }

  public int getRechargeTime() {
    return this.rechargeTime;
  }

  public RechargeAcrossDTO setRechargeTime(int rechargeTime) {
    this.rechargeTime = rechargeTime;
    setRechargeTimeIsSet(true);
    return this;
  }

  public void unsetRechargeTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RECHARGETIME_ISSET_ID);
  }

  /** Returns true if field rechargeTime is set (has been assigned a value) and false otherwise */
  public boolean isSetRechargeTime() {
    return EncodingUtils.testBit(__isset_bitfield, __RECHARGETIME_ISSET_ID);
  }

  public void setRechargeTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RECHARGETIME_ISSET_ID, value);
  }

  public int getTradeTime() {
    return this.tradeTime;
  }

  public RechargeAcrossDTO setTradeTime(int tradeTime) {
    this.tradeTime = tradeTime;
    setTradeTimeIsSet(true);
    return this;
  }

  public void unsetTradeTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TRADETIME_ISSET_ID);
  }

  /** Returns true if field tradeTime is set (has been assigned a value) and false otherwise */
  public boolean isSetTradeTime() {
    return EncodingUtils.testBit(__isset_bitfield, __TRADETIME_ISSET_ID);
  }

  public void setTradeTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TRADETIME_ISSET_ID, value);
  }

  public String getCoin() {
    return this.coin;
  }

  public RechargeAcrossDTO setCoin(String coin) {
    this.coin = coin;
    return this;
  }

  public void unsetCoin() {
    this.coin = null;
  }

  /** Returns true if field coin is set (has been assigned a value) and false otherwise */
  public boolean isSetCoin() {
    return this.coin != null;
  }

  public void setCoinIsSet(boolean value) {
    if (!value) {
      this.coin = null;
    }
  }

  public String getMoney() {
    return this.money;
  }

  public RechargeAcrossDTO setMoney(String money) {
    this.money = money;
    return this;
  }

  public void unsetMoney() {
    this.money = null;
  }

  /** Returns true if field money is set (has been assigned a value) and false otherwise */
  public boolean isSetMoney() {
    return this.money != null;
  }

  public void setMoneyIsSet(boolean value) {
    if (!value) {
      this.money = null;
    }
  }

  public String getCouponId() {
    return this.couponId;
  }

  public RechargeAcrossDTO setCouponId(String couponId) {
    this.couponId = couponId;
    return this;
  }

  public void unsetCouponId() {
    this.couponId = null;
  }

  /** Returns true if field couponId is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponId() {
    return this.couponId != null;
  }

  public void setCouponIdIsSet(boolean value) {
    if (!value) {
      this.couponId = null;
    }
  }

  public String getCoupon() {
    return this.coupon;
  }

  public RechargeAcrossDTO setCoupon(String coupon) {
    this.coupon = coupon;
    return this;
  }

  public void unsetCoupon() {
    this.coupon = null;
  }

  /** Returns true if field coupon is set (has been assigned a value) and false otherwise */
  public boolean isSetCoupon() {
    return this.coupon != null;
  }

  public void setCouponIsSet(boolean value) {
    if (!value) {
      this.coupon = null;
    }
  }

  public int getCouponStatus() {
    return this.couponStatus;
  }

  public RechargeAcrossDTO setCouponStatus(int couponStatus) {
    this.couponStatus = couponStatus;
    setCouponStatusIsSet(true);
    return this;
  }

  public void unsetCouponStatus() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COUPONSTATUS_ISSET_ID);
  }

  /** Returns true if field couponStatus is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponStatus() {
    return EncodingUtils.testBit(__isset_bitfield, __COUPONSTATUS_ISSET_ID);
  }

  public void setCouponStatusIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COUPONSTATUS_ISSET_ID, value);
  }

  public String getCouponStatusLabel() {
    return this.couponStatusLabel;
  }

  public RechargeAcrossDTO setCouponStatusLabel(String couponStatusLabel) {
    this.couponStatusLabel = couponStatusLabel;
    return this;
  }

  public void unsetCouponStatusLabel() {
    this.couponStatusLabel = null;
  }

  /** Returns true if field couponStatusLabel is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponStatusLabel() {
    return this.couponStatusLabel != null;
  }

  public void setCouponStatusLabelIsSet(boolean value) {
    if (!value) {
      this.couponStatusLabel = null;
    }
  }

  public int getCouponErrorCode() {
    return this.couponErrorCode;
  }

  public RechargeAcrossDTO setCouponErrorCode(int couponErrorCode) {
    this.couponErrorCode = couponErrorCode;
    setCouponErrorCodeIsSet(true);
    return this;
  }

  public void unsetCouponErrorCode() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COUPONERRORCODE_ISSET_ID);
  }

  /** Returns true if field couponErrorCode is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponErrorCode() {
    return EncodingUtils.testBit(__isset_bitfield, __COUPONERRORCODE_ISSET_ID);
  }

  public void setCouponErrorCodeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COUPONERRORCODE_ISSET_ID, value);
  }

  public String getCouponErrorMsg() {
    return this.couponErrorMsg;
  }

  public RechargeAcrossDTO setCouponErrorMsg(String couponErrorMsg) {
    this.couponErrorMsg = couponErrorMsg;
    return this;
  }

  public void unsetCouponErrorMsg() {
    this.couponErrorMsg = null;
  }

  /** Returns true if field couponErrorMsg is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponErrorMsg() {
    return this.couponErrorMsg != null;
  }

  public void setCouponErrorMsgIsSet(boolean value) {
    if (!value) {
      this.couponErrorMsg = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case PAY_TYPE_ID:
      if (value == null) {
        unsetPayTypeId();
      } else {
        setPayTypeId((Integer)value);
      }
      break;

    case PAY_TYPE_LABEL:
      if (value == null) {
        unsetPayTypeLabel();
      } else {
        setPayTypeLabel((String)value);
      }
      break;

    case STATUS:
      if (value == null) {
        unsetStatus();
      } else {
        setStatus((Integer)value);
      }
      break;

    case STATUS_LABEL:
      if (value == null) {
        unsetStatusLabel();
      } else {
        setStatusLabel((String)value);
      }
      break;

    case RECHARGE_ORDER_NUM:
      if (value == null) {
        unsetRechargeOrderNum();
      } else {
        setRechargeOrderNum((String)value);
      }
      break;

    case RECHARGE_TIME:
      if (value == null) {
        unsetRechargeTime();
      } else {
        setRechargeTime((Integer)value);
      }
      break;

    case TRADE_TIME:
      if (value == null) {
        unsetTradeTime();
      } else {
        setTradeTime((Integer)value);
      }
      break;

    case COIN:
      if (value == null) {
        unsetCoin();
      } else {
        setCoin((String)value);
      }
      break;

    case MONEY:
      if (value == null) {
        unsetMoney();
      } else {
        setMoney((String)value);
      }
      break;

    case COUPON_ID:
      if (value == null) {
        unsetCouponId();
      } else {
        setCouponId((String)value);
      }
      break;

    case COUPON:
      if (value == null) {
        unsetCoupon();
      } else {
        setCoupon((String)value);
      }
      break;

    case COUPON_STATUS:
      if (value == null) {
        unsetCouponStatus();
      } else {
        setCouponStatus((Integer)value);
      }
      break;

    case COUPON_STATUS_LABEL:
      if (value == null) {
        unsetCouponStatusLabel();
      } else {
        setCouponStatusLabel((String)value);
      }
      break;

    case COUPON_ERROR_CODE:
      if (value == null) {
        unsetCouponErrorCode();
      } else {
        setCouponErrorCode((Integer)value);
      }
      break;

    case COUPON_ERROR_MSG:
      if (value == null) {
        unsetCouponErrorMsg();
      } else {
        setCouponErrorMsg((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case PAY_TYPE_ID:
      return getPayTypeId();

    case PAY_TYPE_LABEL:
      return getPayTypeLabel();

    case STATUS:
      return getStatus();

    case STATUS_LABEL:
      return getStatusLabel();

    case RECHARGE_ORDER_NUM:
      return getRechargeOrderNum();

    case RECHARGE_TIME:
      return getRechargeTime();

    case TRADE_TIME:
      return getTradeTime();

    case COIN:
      return getCoin();

    case MONEY:
      return getMoney();

    case COUPON_ID:
      return getCouponId();

    case COUPON:
      return getCoupon();

    case COUPON_STATUS:
      return getCouponStatus();

    case COUPON_STATUS_LABEL:
      return getCouponStatusLabel();

    case COUPON_ERROR_CODE:
      return getCouponErrorCode();

    case COUPON_ERROR_MSG:
      return getCouponErrorMsg();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case PAY_TYPE_ID:
      return isSetPayTypeId();
    case PAY_TYPE_LABEL:
      return isSetPayTypeLabel();
    case STATUS:
      return isSetStatus();
    case STATUS_LABEL:
      return isSetStatusLabel();
    case RECHARGE_ORDER_NUM:
      return isSetRechargeOrderNum();
    case RECHARGE_TIME:
      return isSetRechargeTime();
    case TRADE_TIME:
      return isSetTradeTime();
    case COIN:
      return isSetCoin();
    case MONEY:
      return isSetMoney();
    case COUPON_ID:
      return isSetCouponId();
    case COUPON:
      return isSetCoupon();
    case COUPON_STATUS:
      return isSetCouponStatus();
    case COUPON_STATUS_LABEL:
      return isSetCouponStatusLabel();
    case COUPON_ERROR_CODE:
      return isSetCouponErrorCode();
    case COUPON_ERROR_MSG:
      return isSetCouponErrorMsg();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof RechargeAcrossDTO)
      return this.equals((RechargeAcrossDTO)that);
    return false;
  }

  public boolean equals(RechargeAcrossDTO that) {
    if (that == null)
      return false;

    boolean this_present_payTypeId = true;
    boolean that_present_payTypeId = true;
    if (this_present_payTypeId || that_present_payTypeId) {
      if (!(this_present_payTypeId && that_present_payTypeId))
        return false;
      if (this.payTypeId != that.payTypeId)
        return false;
    }

    boolean this_present_payTypeLabel = true && this.isSetPayTypeLabel();
    boolean that_present_payTypeLabel = true && that.isSetPayTypeLabel();
    if (this_present_payTypeLabel || that_present_payTypeLabel) {
      if (!(this_present_payTypeLabel && that_present_payTypeLabel))
        return false;
      if (!this.payTypeLabel.equals(that.payTypeLabel))
        return false;
    }

    boolean this_present_status = true;
    boolean that_present_status = true;
    if (this_present_status || that_present_status) {
      if (!(this_present_status && that_present_status))
        return false;
      if (this.status != that.status)
        return false;
    }

    boolean this_present_statusLabel = true && this.isSetStatusLabel();
    boolean that_present_statusLabel = true && that.isSetStatusLabel();
    if (this_present_statusLabel || that_present_statusLabel) {
      if (!(this_present_statusLabel && that_present_statusLabel))
        return false;
      if (!this.statusLabel.equals(that.statusLabel))
        return false;
    }

    boolean this_present_rechargeOrderNum = true && this.isSetRechargeOrderNum();
    boolean that_present_rechargeOrderNum = true && that.isSetRechargeOrderNum();
    if (this_present_rechargeOrderNum || that_present_rechargeOrderNum) {
      if (!(this_present_rechargeOrderNum && that_present_rechargeOrderNum))
        return false;
      if (!this.rechargeOrderNum.equals(that.rechargeOrderNum))
        return false;
    }

    boolean this_present_rechargeTime = true;
    boolean that_present_rechargeTime = true;
    if (this_present_rechargeTime || that_present_rechargeTime) {
      if (!(this_present_rechargeTime && that_present_rechargeTime))
        return false;
      if (this.rechargeTime != that.rechargeTime)
        return false;
    }

    boolean this_present_tradeTime = true;
    boolean that_present_tradeTime = true;
    if (this_present_tradeTime || that_present_tradeTime) {
      if (!(this_present_tradeTime && that_present_tradeTime))
        return false;
      if (this.tradeTime != that.tradeTime)
        return false;
    }

    boolean this_present_coin = true && this.isSetCoin();
    boolean that_present_coin = true && that.isSetCoin();
    if (this_present_coin || that_present_coin) {
      if (!(this_present_coin && that_present_coin))
        return false;
      if (!this.coin.equals(that.coin))
        return false;
    }

    boolean this_present_money = true && this.isSetMoney();
    boolean that_present_money = true && that.isSetMoney();
    if (this_present_money || that_present_money) {
      if (!(this_present_money && that_present_money))
        return false;
      if (!this.money.equals(that.money))
        return false;
    }

    boolean this_present_couponId = true && this.isSetCouponId();
    boolean that_present_couponId = true && that.isSetCouponId();
    if (this_present_couponId || that_present_couponId) {
      if (!(this_present_couponId && that_present_couponId))
        return false;
      if (!this.couponId.equals(that.couponId))
        return false;
    }

    boolean this_present_coupon = true && this.isSetCoupon();
    boolean that_present_coupon = true && that.isSetCoupon();
    if (this_present_coupon || that_present_coupon) {
      if (!(this_present_coupon && that_present_coupon))
        return false;
      if (!this.coupon.equals(that.coupon))
        return false;
    }

    boolean this_present_couponStatus = true;
    boolean that_present_couponStatus = true;
    if (this_present_couponStatus || that_present_couponStatus) {
      if (!(this_present_couponStatus && that_present_couponStatus))
        return false;
      if (this.couponStatus != that.couponStatus)
        return false;
    }

    boolean this_present_couponStatusLabel = true && this.isSetCouponStatusLabel();
    boolean that_present_couponStatusLabel = true && that.isSetCouponStatusLabel();
    if (this_present_couponStatusLabel || that_present_couponStatusLabel) {
      if (!(this_present_couponStatusLabel && that_present_couponStatusLabel))
        return false;
      if (!this.couponStatusLabel.equals(that.couponStatusLabel))
        return false;
    }

    boolean this_present_couponErrorCode = true;
    boolean that_present_couponErrorCode = true;
    if (this_present_couponErrorCode || that_present_couponErrorCode) {
      if (!(this_present_couponErrorCode && that_present_couponErrorCode))
        return false;
      if (this.couponErrorCode != that.couponErrorCode)
        return false;
    }

    boolean this_present_couponErrorMsg = true && this.isSetCouponErrorMsg();
    boolean that_present_couponErrorMsg = true && that.isSetCouponErrorMsg();
    if (this_present_couponErrorMsg || that_present_couponErrorMsg) {
      if (!(this_present_couponErrorMsg && that_present_couponErrorMsg))
        return false;
      if (!this.couponErrorMsg.equals(that.couponErrorMsg))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_payTypeId = true;
    list.add(present_payTypeId);
    if (present_payTypeId)
      list.add(payTypeId);

    boolean present_payTypeLabel = true && (isSetPayTypeLabel());
    list.add(present_payTypeLabel);
    if (present_payTypeLabel)
      list.add(payTypeLabel);

    boolean present_status = true;
    list.add(present_status);
    if (present_status)
      list.add(status);

    boolean present_statusLabel = true && (isSetStatusLabel());
    list.add(present_statusLabel);
    if (present_statusLabel)
      list.add(statusLabel);

    boolean present_rechargeOrderNum = true && (isSetRechargeOrderNum());
    list.add(present_rechargeOrderNum);
    if (present_rechargeOrderNum)
      list.add(rechargeOrderNum);

    boolean present_rechargeTime = true;
    list.add(present_rechargeTime);
    if (present_rechargeTime)
      list.add(rechargeTime);

    boolean present_tradeTime = true;
    list.add(present_tradeTime);
    if (present_tradeTime)
      list.add(tradeTime);

    boolean present_coin = true && (isSetCoin());
    list.add(present_coin);
    if (present_coin)
      list.add(coin);

    boolean present_money = true && (isSetMoney());
    list.add(present_money);
    if (present_money)
      list.add(money);

    boolean present_couponId = true && (isSetCouponId());
    list.add(present_couponId);
    if (present_couponId)
      list.add(couponId);

    boolean present_coupon = true && (isSetCoupon());
    list.add(present_coupon);
    if (present_coupon)
      list.add(coupon);

    boolean present_couponStatus = true;
    list.add(present_couponStatus);
    if (present_couponStatus)
      list.add(couponStatus);

    boolean present_couponStatusLabel = true && (isSetCouponStatusLabel());
    list.add(present_couponStatusLabel);
    if (present_couponStatusLabel)
      list.add(couponStatusLabel);

    boolean present_couponErrorCode = true;
    list.add(present_couponErrorCode);
    if (present_couponErrorCode)
      list.add(couponErrorCode);

    boolean present_couponErrorMsg = true && (isSetCouponErrorMsg());
    list.add(present_couponErrorMsg);
    if (present_couponErrorMsg)
      list.add(couponErrorMsg);

    return list.hashCode();
  }

  @Override
  public int compareTo(RechargeAcrossDTO other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetPayTypeId()).compareTo(other.isSetPayTypeId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPayTypeId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.payTypeId, other.payTypeId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPayTypeLabel()).compareTo(other.isSetPayTypeLabel());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPayTypeLabel()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.payTypeLabel, other.payTypeLabel);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetStatus()).compareTo(other.isSetStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.status, other.status);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetStatusLabel()).compareTo(other.isSetStatusLabel());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStatusLabel()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.statusLabel, other.statusLabel);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRechargeOrderNum()).compareTo(other.isSetRechargeOrderNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRechargeOrderNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rechargeOrderNum, other.rechargeOrderNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRechargeTime()).compareTo(other.isSetRechargeTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRechargeTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rechargeTime, other.rechargeTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTradeTime()).compareTo(other.isSetTradeTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTradeTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.tradeTime, other.tradeTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCoin()).compareTo(other.isSetCoin());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCoin()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.coin, other.coin);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMoney()).compareTo(other.isSetMoney());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMoney()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.money, other.money);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponId()).compareTo(other.isSetCouponId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponId, other.couponId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCoupon()).compareTo(other.isSetCoupon());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCoupon()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.coupon, other.coupon);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponStatus()).compareTo(other.isSetCouponStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponStatus, other.couponStatus);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponStatusLabel()).compareTo(other.isSetCouponStatusLabel());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponStatusLabel()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponStatusLabel, other.couponStatusLabel);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponErrorCode()).compareTo(other.isSetCouponErrorCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponErrorCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponErrorCode, other.couponErrorCode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponErrorMsg()).compareTo(other.isSetCouponErrorMsg());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponErrorMsg()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponErrorMsg, other.couponErrorMsg);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("RechargeAcrossDTO(");
    boolean first = true;

    sb.append("payTypeId:");
    sb.append(this.payTypeId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("payTypeLabel:");
    if (this.payTypeLabel == null) {
      sb.append("null");
    } else {
      sb.append(this.payTypeLabel);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("status:");
    sb.append(this.status);
    first = false;
    if (!first) sb.append(", ");
    sb.append("statusLabel:");
    if (this.statusLabel == null) {
      sb.append("null");
    } else {
      sb.append(this.statusLabel);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("rechargeOrderNum:");
    if (this.rechargeOrderNum == null) {
      sb.append("null");
    } else {
      sb.append(this.rechargeOrderNum);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("rechargeTime:");
    sb.append(this.rechargeTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("tradeTime:");
    sb.append(this.tradeTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("coin:");
    if (this.coin == null) {
      sb.append("null");
    } else {
      sb.append(this.coin);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("money:");
    if (this.money == null) {
      sb.append("null");
    } else {
      sb.append(this.money);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponId:");
    if (this.couponId == null) {
      sb.append("null");
    } else {
      sb.append(this.couponId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("coupon:");
    if (this.coupon == null) {
      sb.append("null");
    } else {
      sb.append(this.coupon);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponStatus:");
    sb.append(this.couponStatus);
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponStatusLabel:");
    if (this.couponStatusLabel == null) {
      sb.append("null");
    } else {
      sb.append(this.couponStatusLabel);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponErrorCode:");
    sb.append(this.couponErrorCode);
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponErrorMsg:");
    if (this.couponErrorMsg == null) {
      sb.append("null");
    } else {
      sb.append(this.couponErrorMsg);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'payTypeId' because it's a primitive and you chose the non-beans generator.
    if (payTypeLabel == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'payTypeLabel' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'status' because it's a primitive and you chose the non-beans generator.
    if (statusLabel == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'statusLabel' was not present! Struct: " + toString());
    }
    if (rechargeOrderNum == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'rechargeOrderNum' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'rechargeTime' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'tradeTime' because it's a primitive and you chose the non-beans generator.
    if (coin == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'coin' was not present! Struct: " + toString());
    }
    if (money == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'money' was not present! Struct: " + toString());
    }
    if (couponId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'couponId' was not present! Struct: " + toString());
    }
    if (coupon == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'coupon' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'couponStatus' because it's a primitive and you chose the non-beans generator.
    if (couponStatusLabel == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'couponStatusLabel' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'couponErrorCode' because it's a primitive and you chose the non-beans generator.
    if (couponErrorMsg == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'couponErrorMsg' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class RechargeAcrossDTOStandardSchemeFactory implements SchemeFactory {
    public RechargeAcrossDTOStandardScheme getScheme() {
      return new RechargeAcrossDTOStandardScheme();
    }
  }

  private static class RechargeAcrossDTOStandardScheme extends StandardScheme<RechargeAcrossDTO> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, RechargeAcrossDTO struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // PAY_TYPE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.payTypeId = iprot.readI32();
              struct.setPayTypeIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // PAY_TYPE_LABEL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.payTypeLabel = iprot.readString();
              struct.setPayTypeLabelIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.status = iprot.readI32();
              struct.setStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // STATUS_LABEL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.statusLabel = iprot.readString();
              struct.setStatusLabelIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // RECHARGE_ORDER_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.rechargeOrderNum = iprot.readString();
              struct.setRechargeOrderNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // RECHARGE_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.rechargeTime = iprot.readI32();
              struct.setRechargeTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // TRADE_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.tradeTime = iprot.readI32();
              struct.setTradeTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // COIN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.coin = iprot.readString();
              struct.setCoinIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // MONEY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.money = iprot.readString();
              struct.setMoneyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // COUPON_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.couponId = iprot.readString();
              struct.setCouponIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // COUPON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.coupon = iprot.readString();
              struct.setCouponIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // COUPON_STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.couponStatus = iprot.readI32();
              struct.setCouponStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // COUPON_STATUS_LABEL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.couponStatusLabel = iprot.readString();
              struct.setCouponStatusLabelIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 14: // COUPON_ERROR_CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.couponErrorCode = iprot.readI32();
              struct.setCouponErrorCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // COUPON_ERROR_MSG
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.couponErrorMsg = iprot.readString();
              struct.setCouponErrorMsgIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetPayTypeId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'payTypeId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetStatus()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'status' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetRechargeTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'rechargeTime' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetTradeTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'tradeTime' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetCouponStatus()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'couponStatus' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetCouponErrorCode()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'couponErrorCode' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, RechargeAcrossDTO struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(PAY_TYPE_ID_FIELD_DESC);
      oprot.writeI32(struct.payTypeId);
      oprot.writeFieldEnd();
      if (struct.payTypeLabel != null) {
        oprot.writeFieldBegin(PAY_TYPE_LABEL_FIELD_DESC);
        oprot.writeString(struct.payTypeLabel);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(STATUS_FIELD_DESC);
      oprot.writeI32(struct.status);
      oprot.writeFieldEnd();
      if (struct.statusLabel != null) {
        oprot.writeFieldBegin(STATUS_LABEL_FIELD_DESC);
        oprot.writeString(struct.statusLabel);
        oprot.writeFieldEnd();
      }
      if (struct.rechargeOrderNum != null) {
        oprot.writeFieldBegin(RECHARGE_ORDER_NUM_FIELD_DESC);
        oprot.writeString(struct.rechargeOrderNum);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(RECHARGE_TIME_FIELD_DESC);
      oprot.writeI32(struct.rechargeTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TRADE_TIME_FIELD_DESC);
      oprot.writeI32(struct.tradeTime);
      oprot.writeFieldEnd();
      if (struct.coin != null) {
        oprot.writeFieldBegin(COIN_FIELD_DESC);
        oprot.writeString(struct.coin);
        oprot.writeFieldEnd();
      }
      if (struct.money != null) {
        oprot.writeFieldBegin(MONEY_FIELD_DESC);
        oprot.writeString(struct.money);
        oprot.writeFieldEnd();
      }
      if (struct.couponId != null) {
        oprot.writeFieldBegin(COUPON_ID_FIELD_DESC);
        oprot.writeString(struct.couponId);
        oprot.writeFieldEnd();
      }
      if (struct.coupon != null) {
        oprot.writeFieldBegin(COUPON_FIELD_DESC);
        oprot.writeString(struct.coupon);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(COUPON_STATUS_FIELD_DESC);
      oprot.writeI32(struct.couponStatus);
      oprot.writeFieldEnd();
      if (struct.couponStatusLabel != null) {
        oprot.writeFieldBegin(COUPON_STATUS_LABEL_FIELD_DESC);
        oprot.writeString(struct.couponStatusLabel);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(COUPON_ERROR_CODE_FIELD_DESC);
      oprot.writeI32(struct.couponErrorCode);
      oprot.writeFieldEnd();
      if (struct.couponErrorMsg != null) {
        oprot.writeFieldBegin(COUPON_ERROR_MSG_FIELD_DESC);
        oprot.writeString(struct.couponErrorMsg);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class RechargeAcrossDTOTupleSchemeFactory implements SchemeFactory {
    public RechargeAcrossDTOTupleScheme getScheme() {
      return new RechargeAcrossDTOTupleScheme();
    }
  }

  private static class RechargeAcrossDTOTupleScheme extends TupleScheme<RechargeAcrossDTO> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, RechargeAcrossDTO struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeI32(struct.payTypeId);
      oprot.writeString(struct.payTypeLabel);
      oprot.writeI32(struct.status);
      oprot.writeString(struct.statusLabel);
      oprot.writeString(struct.rechargeOrderNum);
      oprot.writeI32(struct.rechargeTime);
      oprot.writeI32(struct.tradeTime);
      oprot.writeString(struct.coin);
      oprot.writeString(struct.money);
      oprot.writeString(struct.couponId);
      oprot.writeString(struct.coupon);
      oprot.writeI32(struct.couponStatus);
      oprot.writeString(struct.couponStatusLabel);
      oprot.writeI32(struct.couponErrorCode);
      oprot.writeString(struct.couponErrorMsg);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, RechargeAcrossDTO struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.payTypeId = iprot.readI32();
      struct.setPayTypeIdIsSet(true);
      struct.payTypeLabel = iprot.readString();
      struct.setPayTypeLabelIsSet(true);
      struct.status = iprot.readI32();
      struct.setStatusIsSet(true);
      struct.statusLabel = iprot.readString();
      struct.setStatusLabelIsSet(true);
      struct.rechargeOrderNum = iprot.readString();
      struct.setRechargeOrderNumIsSet(true);
      struct.rechargeTime = iprot.readI32();
      struct.setRechargeTimeIsSet(true);
      struct.tradeTime = iprot.readI32();
      struct.setTradeTimeIsSet(true);
      struct.coin = iprot.readString();
      struct.setCoinIsSet(true);
      struct.money = iprot.readString();
      struct.setMoneyIsSet(true);
      struct.couponId = iprot.readString();
      struct.setCouponIdIsSet(true);
      struct.coupon = iprot.readString();
      struct.setCouponIsSet(true);
      struct.couponStatus = iprot.readI32();
      struct.setCouponStatusIsSet(true);
      struct.couponStatusLabel = iprot.readString();
      struct.setCouponStatusLabelIsSet(true);
      struct.couponErrorCode = iprot.readI32();
      struct.setCouponErrorCodeIsSet(true);
      struct.couponErrorMsg = iprot.readString();
      struct.setCouponErrorMsgIsSet(true);
    }
  }

}

