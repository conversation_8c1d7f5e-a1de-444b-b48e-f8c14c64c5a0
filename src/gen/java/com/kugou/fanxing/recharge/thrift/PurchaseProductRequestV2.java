/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.recharge.thrift;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-10-24")
public class PurchaseProductRequestV2 implements org.apache.thrift.TBase<PurchaseProductRequestV2, PurchaseProductRequestV2._Fields>, java.io.Serializable, Cloneable, Comparable<PurchaseProductRequestV2> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PurchaseProductRequestV2");

  private static final org.apache.thrift.protocol.TField BUSINESS_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("businessId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField BUSINESS_ORDER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("businessOrderId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField BUSINESS_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("businessTime", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField AMOUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("amount", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField SUBJECT_FIELD_DESC = new org.apache.thrift.protocol.TField("subject", org.apache.thrift.protocol.TType.STRING, (short)6);
  private static final org.apache.thrift.protocol.TField SYNC_URL_FIELD_DESC = new org.apache.thrift.protocol.TField("syncUrl", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField REDIRECT_URL_FIELD_DESC = new org.apache.thrift.protocol.TField("redirectUrl", org.apache.thrift.protocol.TType.STRING, (short)8);
  private static final org.apache.thrift.protocol.TField CLIENT_IP_FIELD_DESC = new org.apache.thrift.protocol.TField("clientIp", org.apache.thrift.protocol.TType.STRING, (short)9);
  private static final org.apache.thrift.protocol.TField PID_FIELD_DESC = new org.apache.thrift.protocol.TField("pid", org.apache.thrift.protocol.TType.I32, (short)10);
  private static final org.apache.thrift.protocol.TField ORDER_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("orderList", org.apache.thrift.protocol.TType.LIST, (short)11);
  private static final org.apache.thrift.protocol.TField PAY_TYPE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("payTypeId", org.apache.thrift.protocol.TType.I32, (short)12);
  private static final org.apache.thrift.protocol.TField OPEN_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("openId", org.apache.thrift.protocol.TType.STRING, (short)13);
  private static final org.apache.thrift.protocol.TField EXT_JSON_FIELD_DESC = new org.apache.thrift.protocol.TField("extJson", org.apache.thrift.protocol.TType.STRING, (short)14);
  private static final org.apache.thrift.protocol.TField SIGN_FIELD_DESC = new org.apache.thrift.protocol.TField("sign", org.apache.thrift.protocol.TType.STRING, (short)15);
  private static final org.apache.thrift.protocol.TField ORDER_EXPIRE_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("orderExpireTime", org.apache.thrift.protocol.TType.I64, (short)16);
  private static final org.apache.thrift.protocol.TField SHOW_URL_FIELD_DESC = new org.apache.thrift.protocol.TField("showUrl", org.apache.thrift.protocol.TType.STRING, (short)17);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new PurchaseProductRequestV2StandardSchemeFactory());
    schemes.put(TupleScheme.class, new PurchaseProductRequestV2TupleSchemeFactory());
  }

  /**
   * 充值业务编号（充值服务分配）
   */
  public String businessId; // required
  /**
   * 业务订单号,不用下单的默认传0，否则传globalid
   */
  public long businessOrderId; // required
  /**
   * 充值下单时间（秒）
   */
  public long businessTime; // required
  /**
   * 充值金额
   */
  public String amount; // required
  /**
   * 用户ID
   */
  public long kugouId; // required
  /**
   * 商品名称（第三方支付展示）
   */
  public String subject; // required
  /**
   * 同步回调地址（默认取网页referer）
   */
  public String syncUrl; // required
  /**
   * 重定向地址（默认:"")
   */
  public String redirectUrl; // required
  /**
   * 客户端IP
   */
  public String clientIp; // required
  /**
   * 平台编号（详见：http://c.fxwork.kugou.net/pages/viewpage.action?pageId=25156183）
   */
  public int pid; // required
  /**
   * 购物清单
   */
  public List<PurchaseOrder> orderList; // required
  /**
   * 支付类型（充值服务分配）http://c.fxwork.kugou.net/pages/viewpage.action?pageId=6916007
   */
  public int payTypeId; // required
  /**
   * 微信账号（微信支付方式传递，默认：空字符串）
   */
  public String openId; // required
  /**
   * 充值扩展（JSON格式）
   */
  public String extJson; // required
  /**
   * 参数签名
   */
  public String sign; // required
  /**
   * 订单支付过期时间（秒）
   */
  public long orderExpireTime; // optional
  /**
   * 取消支付之后的跳转地址
   */
  public String showUrl; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 充值业务编号（充值服务分配）
     */
    BUSINESS_ID((short)1, "businessId"),
    /**
     * 业务订单号,不用下单的默认传0，否则传globalid
     */
    BUSINESS_ORDER_ID((short)2, "businessOrderId"),
    /**
     * 充值下单时间（秒）
     */
    BUSINESS_TIME((short)3, "businessTime"),
    /**
     * 充值金额
     */
    AMOUNT((short)4, "amount"),
    /**
     * 用户ID
     */
    KUGOU_ID((short)5, "kugouId"),
    /**
     * 商品名称（第三方支付展示）
     */
    SUBJECT((short)6, "subject"),
    /**
     * 同步回调地址（默认取网页referer）
     */
    SYNC_URL((short)7, "syncUrl"),
    /**
     * 重定向地址（默认:"")
     */
    REDIRECT_URL((short)8, "redirectUrl"),
    /**
     * 客户端IP
     */
    CLIENT_IP((short)9, "clientIp"),
    /**
     * 平台编号（详见：http://c.fxwork.kugou.net/pages/viewpage.action?pageId=25156183）
     */
    PID((short)10, "pid"),
    /**
     * 购物清单
     */
    ORDER_LIST((short)11, "orderList"),
    /**
     * 支付类型（充值服务分配）http://c.fxwork.kugou.net/pages/viewpage.action?pageId=6916007
     */
    PAY_TYPE_ID((short)12, "payTypeId"),
    /**
     * 微信账号（微信支付方式传递，默认：空字符串）
     */
    OPEN_ID((short)13, "openId"),
    /**
     * 充值扩展（JSON格式）
     */
    EXT_JSON((short)14, "extJson"),
    /**
     * 参数签名
     */
    SIGN((short)15, "sign"),
    /**
     * 订单支付过期时间（秒）
     */
    ORDER_EXPIRE_TIME((short)16, "orderExpireTime"),
    /**
     * 取消支付之后的跳转地址
     */
    SHOW_URL((short)17, "showUrl");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // BUSINESS_ID
          return BUSINESS_ID;
        case 2: // BUSINESS_ORDER_ID
          return BUSINESS_ORDER_ID;
        case 3: // BUSINESS_TIME
          return BUSINESS_TIME;
        case 4: // AMOUNT
          return AMOUNT;
        case 5: // KUGOU_ID
          return KUGOU_ID;
        case 6: // SUBJECT
          return SUBJECT;
        case 7: // SYNC_URL
          return SYNC_URL;
        case 8: // REDIRECT_URL
          return REDIRECT_URL;
        case 9: // CLIENT_IP
          return CLIENT_IP;
        case 10: // PID
          return PID;
        case 11: // ORDER_LIST
          return ORDER_LIST;
        case 12: // PAY_TYPE_ID
          return PAY_TYPE_ID;
        case 13: // OPEN_ID
          return OPEN_ID;
        case 14: // EXT_JSON
          return EXT_JSON;
        case 15: // SIGN
          return SIGN;
        case 16: // ORDER_EXPIRE_TIME
          return ORDER_EXPIRE_TIME;
        case 17: // SHOW_URL
          return SHOW_URL;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __BUSINESSORDERID_ISSET_ID = 0;
  private static final int __BUSINESSTIME_ISSET_ID = 1;
  private static final int __KUGOUID_ISSET_ID = 2;
  private static final int __PID_ISSET_ID = 3;
  private static final int __PAYTYPEID_ISSET_ID = 4;
  private static final int __ORDEREXPIRETIME_ISSET_ID = 5;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.ORDER_EXPIRE_TIME,_Fields.SHOW_URL};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.BUSINESS_ID, new org.apache.thrift.meta_data.FieldMetaData("businessId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BUSINESS_ORDER_ID, new org.apache.thrift.meta_data.FieldMetaData("businessOrderId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.BUSINESS_TIME, new org.apache.thrift.meta_data.FieldMetaData("businessTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.AMOUNT, new org.apache.thrift.meta_data.FieldMetaData("amount", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SUBJECT, new org.apache.thrift.meta_data.FieldMetaData("subject", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SYNC_URL, new org.apache.thrift.meta_data.FieldMetaData("syncUrl", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.REDIRECT_URL, new org.apache.thrift.meta_data.FieldMetaData("redirectUrl", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CLIENT_IP, new org.apache.thrift.meta_data.FieldMetaData("clientIp", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PID, new org.apache.thrift.meta_data.FieldMetaData("pid", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ORDER_LIST, new org.apache.thrift.meta_data.FieldMetaData("orderList", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT            , "PurchaseOrder"))));
    tmpMap.put(_Fields.PAY_TYPE_ID, new org.apache.thrift.meta_data.FieldMetaData("payTypeId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.OPEN_ID, new org.apache.thrift.meta_data.FieldMetaData("openId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT_JSON, new org.apache.thrift.meta_data.FieldMetaData("extJson", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SIGN, new org.apache.thrift.meta_data.FieldMetaData("sign", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ORDER_EXPIRE_TIME, new org.apache.thrift.meta_data.FieldMetaData("orderExpireTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SHOW_URL, new org.apache.thrift.meta_data.FieldMetaData("showUrl", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PurchaseProductRequestV2.class, metaDataMap);
  }

  public PurchaseProductRequestV2() {
  }

  public PurchaseProductRequestV2(
    String businessId,
    long businessOrderId,
    long businessTime,
    String amount,
    long kugouId,
    String subject,
    String syncUrl,
    String redirectUrl,
    String clientIp,
    int pid,
    List<PurchaseOrder> orderList,
    int payTypeId,
    String openId,
    String extJson,
    String sign)
  {
    this();
    this.businessId = businessId;
    this.businessOrderId = businessOrderId;
    setBusinessOrderIdIsSet(true);
    this.businessTime = businessTime;
    setBusinessTimeIsSet(true);
    this.amount = amount;
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    this.subject = subject;
    this.syncUrl = syncUrl;
    this.redirectUrl = redirectUrl;
    this.clientIp = clientIp;
    this.pid = pid;
    setPidIsSet(true);
    this.orderList = orderList;
    this.payTypeId = payTypeId;
    setPayTypeIdIsSet(true);
    this.openId = openId;
    this.extJson = extJson;
    this.sign = sign;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PurchaseProductRequestV2(PurchaseProductRequestV2 other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetBusinessId()) {
      this.businessId = other.businessId;
    }
    this.businessOrderId = other.businessOrderId;
    this.businessTime = other.businessTime;
    if (other.isSetAmount()) {
      this.amount = other.amount;
    }
    this.kugouId = other.kugouId;
    if (other.isSetSubject()) {
      this.subject = other.subject;
    }
    if (other.isSetSyncUrl()) {
      this.syncUrl = other.syncUrl;
    }
    if (other.isSetRedirectUrl()) {
      this.redirectUrl = other.redirectUrl;
    }
    if (other.isSetClientIp()) {
      this.clientIp = other.clientIp;
    }
    this.pid = other.pid;
    if (other.isSetOrderList()) {
      List<PurchaseOrder> __this__orderList = new ArrayList<PurchaseOrder>(other.orderList.size());
      for (PurchaseOrder other_element : other.orderList) {
        __this__orderList.add(other_element);
      }
      this.orderList = __this__orderList;
    }
    this.payTypeId = other.payTypeId;
    if (other.isSetOpenId()) {
      this.openId = other.openId;
    }
    if (other.isSetExtJson()) {
      this.extJson = other.extJson;
    }
    if (other.isSetSign()) {
      this.sign = other.sign;
    }
    this.orderExpireTime = other.orderExpireTime;
    if (other.isSetShowUrl()) {
      this.showUrl = other.showUrl;
    }
  }

  public PurchaseProductRequestV2 deepCopy() {
    return new PurchaseProductRequestV2(this);
  }

  @Override
  public void clear() {
    this.businessId = null;
    setBusinessOrderIdIsSet(false);
    this.businessOrderId = 0;
    setBusinessTimeIsSet(false);
    this.businessTime = 0;
    this.amount = null;
    setKugouIdIsSet(false);
    this.kugouId = 0;
    this.subject = null;
    this.syncUrl = null;
    this.redirectUrl = null;
    this.clientIp = null;
    setPidIsSet(false);
    this.pid = 0;
    this.orderList = null;
    setPayTypeIdIsSet(false);
    this.payTypeId = 0;
    this.openId = null;
    this.extJson = null;
    this.sign = null;
    setOrderExpireTimeIsSet(false);
    this.orderExpireTime = 0;
    this.showUrl = null;
  }

  /**
   * 充值业务编号（充值服务分配）
   */
  public String getBusinessId() {
    return this.businessId;
  }

  /**
   * 充值业务编号（充值服务分配）
   */
  public PurchaseProductRequestV2 setBusinessId(String businessId) {
    this.businessId = businessId;
    return this;
  }

  public void unsetBusinessId() {
    this.businessId = null;
  }

  /** Returns true if field businessId is set (has been assigned a value) and false otherwise */
  public boolean isSetBusinessId() {
    return this.businessId != null;
  }

  public void setBusinessIdIsSet(boolean value) {
    if (!value) {
      this.businessId = null;
    }
  }

  /**
   * 业务订单号,不用下单的默认传0，否则传globalid
   */
  public long getBusinessOrderId() {
    return this.businessOrderId;
  }

  /**
   * 业务订单号,不用下单的默认传0，否则传globalid
   */
  public PurchaseProductRequestV2 setBusinessOrderId(long businessOrderId) {
    this.businessOrderId = businessOrderId;
    setBusinessOrderIdIsSet(true);
    return this;
  }

  public void unsetBusinessOrderId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __BUSINESSORDERID_ISSET_ID);
  }

  /** Returns true if field businessOrderId is set (has been assigned a value) and false otherwise */
  public boolean isSetBusinessOrderId() {
    return EncodingUtils.testBit(__isset_bitfield, __BUSINESSORDERID_ISSET_ID);
  }

  public void setBusinessOrderIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __BUSINESSORDERID_ISSET_ID, value);
  }

  /**
   * 充值下单时间（秒）
   */
  public long getBusinessTime() {
    return this.businessTime;
  }

  /**
   * 充值下单时间（秒）
   */
  public PurchaseProductRequestV2 setBusinessTime(long businessTime) {
    this.businessTime = businessTime;
    setBusinessTimeIsSet(true);
    return this;
  }

  public void unsetBusinessTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __BUSINESSTIME_ISSET_ID);
  }

  /** Returns true if field businessTime is set (has been assigned a value) and false otherwise */
  public boolean isSetBusinessTime() {
    return EncodingUtils.testBit(__isset_bitfield, __BUSINESSTIME_ISSET_ID);
  }

  public void setBusinessTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __BUSINESSTIME_ISSET_ID, value);
  }

  /**
   * 充值金额
   */
  public String getAmount() {
    return this.amount;
  }

  /**
   * 充值金额
   */
  public PurchaseProductRequestV2 setAmount(String amount) {
    this.amount = amount;
    return this;
  }

  public void unsetAmount() {
    this.amount = null;
  }

  /** Returns true if field amount is set (has been assigned a value) and false otherwise */
  public boolean isSetAmount() {
    return this.amount != null;
  }

  public void setAmountIsSet(boolean value) {
    if (!value) {
      this.amount = null;
    }
  }

  /**
   * 用户ID
   */
  public long getKugouId() {
    return this.kugouId;
  }

  /**
   * 用户ID
   */
  public PurchaseProductRequestV2 setKugouId(long kugouId) {
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    return this;
  }

  public void unsetKugouId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
  public boolean isSetKugouId() {
    return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  public void setKugouIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
  }

  /**
   * 商品名称（第三方支付展示）
   */
  public String getSubject() {
    return this.subject;
  }

  /**
   * 商品名称（第三方支付展示）
   */
  public PurchaseProductRequestV2 setSubject(String subject) {
    this.subject = subject;
    return this;
  }

  public void unsetSubject() {
    this.subject = null;
  }

  /** Returns true if field subject is set (has been assigned a value) and false otherwise */
  public boolean isSetSubject() {
    return this.subject != null;
  }

  public void setSubjectIsSet(boolean value) {
    if (!value) {
      this.subject = null;
    }
  }

  /**
   * 同步回调地址（默认取网页referer）
   */
  public String getSyncUrl() {
    return this.syncUrl;
  }

  /**
   * 同步回调地址（默认取网页referer）
   */
  public PurchaseProductRequestV2 setSyncUrl(String syncUrl) {
    this.syncUrl = syncUrl;
    return this;
  }

  public void unsetSyncUrl() {
    this.syncUrl = null;
  }

  /** Returns true if field syncUrl is set (has been assigned a value) and false otherwise */
  public boolean isSetSyncUrl() {
    return this.syncUrl != null;
  }

  public void setSyncUrlIsSet(boolean value) {
    if (!value) {
      this.syncUrl = null;
    }
  }

  /**
   * 重定向地址（默认:"")
   */
  public String getRedirectUrl() {
    return this.redirectUrl;
  }

  /**
   * 重定向地址（默认:"")
   */
  public PurchaseProductRequestV2 setRedirectUrl(String redirectUrl) {
    this.redirectUrl = redirectUrl;
    return this;
  }

  public void unsetRedirectUrl() {
    this.redirectUrl = null;
  }

  /** Returns true if field redirectUrl is set (has been assigned a value) and false otherwise */
  public boolean isSetRedirectUrl() {
    return this.redirectUrl != null;
  }

  public void setRedirectUrlIsSet(boolean value) {
    if (!value) {
      this.redirectUrl = null;
    }
  }

  /**
   * 客户端IP
   */
  public String getClientIp() {
    return this.clientIp;
  }

  /**
   * 客户端IP
   */
  public PurchaseProductRequestV2 setClientIp(String clientIp) {
    this.clientIp = clientIp;
    return this;
  }

  public void unsetClientIp() {
    this.clientIp = null;
  }

  /** Returns true if field clientIp is set (has been assigned a value) and false otherwise */
  public boolean isSetClientIp() {
    return this.clientIp != null;
  }

  public void setClientIpIsSet(boolean value) {
    if (!value) {
      this.clientIp = null;
    }
  }

  /**
   * 平台编号（详见：http://c.fxwork.kugou.net/pages/viewpage.action?pageId=25156183）
   */
  public int getPid() {
    return this.pid;
  }

  /**
   * 平台编号（详见：http://c.fxwork.kugou.net/pages/viewpage.action?pageId=25156183）
   */
  public PurchaseProductRequestV2 setPid(int pid) {
    this.pid = pid;
    setPidIsSet(true);
    return this;
  }

  public void unsetPid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PID_ISSET_ID);
  }

  /** Returns true if field pid is set (has been assigned a value) and false otherwise */
  public boolean isSetPid() {
    return EncodingUtils.testBit(__isset_bitfield, __PID_ISSET_ID);
  }

  public void setPidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PID_ISSET_ID, value);
  }

  public int getOrderListSize() {
    return (this.orderList == null) ? 0 : this.orderList.size();
  }

  public java.util.Iterator<PurchaseOrder> getOrderListIterator() {
    return (this.orderList == null) ? null : this.orderList.iterator();
  }

  public void addToOrderList(PurchaseOrder elem) {
    if (this.orderList == null) {
      this.orderList = new ArrayList<PurchaseOrder>();
    }
    this.orderList.add(elem);
  }

  /**
   * 购物清单
   */
  public List<PurchaseOrder> getOrderList() {
    return this.orderList;
  }

  /**
   * 购物清单
   */
  public PurchaseProductRequestV2 setOrderList(List<PurchaseOrder> orderList) {
    this.orderList = orderList;
    return this;
  }

  public void unsetOrderList() {
    this.orderList = null;
  }

  /** Returns true if field orderList is set (has been assigned a value) and false otherwise */
  public boolean isSetOrderList() {
    return this.orderList != null;
  }

  public void setOrderListIsSet(boolean value) {
    if (!value) {
      this.orderList = null;
    }
  }

  /**
   * 支付类型（充值服务分配）http://c.fxwork.kugou.net/pages/viewpage.action?pageId=6916007
   */
  public int getPayTypeId() {
    return this.payTypeId;
  }

  /**
   * 支付类型（充值服务分配）http://c.fxwork.kugou.net/pages/viewpage.action?pageId=6916007
   */
  public PurchaseProductRequestV2 setPayTypeId(int payTypeId) {
    this.payTypeId = payTypeId;
    setPayTypeIdIsSet(true);
    return this;
  }

  public void unsetPayTypeId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAYTYPEID_ISSET_ID);
  }

  /** Returns true if field payTypeId is set (has been assigned a value) and false otherwise */
  public boolean isSetPayTypeId() {
    return EncodingUtils.testBit(__isset_bitfield, __PAYTYPEID_ISSET_ID);
  }

  public void setPayTypeIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAYTYPEID_ISSET_ID, value);
  }

  /**
   * 微信账号（微信支付方式传递，默认：空字符串）
   */
  public String getOpenId() {
    return this.openId;
  }

  /**
   * 微信账号（微信支付方式传递，默认：空字符串）
   */
  public PurchaseProductRequestV2 setOpenId(String openId) {
    this.openId = openId;
    return this;
  }

  public void unsetOpenId() {
    this.openId = null;
  }

  /** Returns true if field openId is set (has been assigned a value) and false otherwise */
  public boolean isSetOpenId() {
    return this.openId != null;
  }

  public void setOpenIdIsSet(boolean value) {
    if (!value) {
      this.openId = null;
    }
  }

  /**
   * 充值扩展（JSON格式）
   */
  public String getExtJson() {
    return this.extJson;
  }

  /**
   * 充值扩展（JSON格式）
   */
  public PurchaseProductRequestV2 setExtJson(String extJson) {
    this.extJson = extJson;
    return this;
  }

  public void unsetExtJson() {
    this.extJson = null;
  }

  /** Returns true if field extJson is set (has been assigned a value) and false otherwise */
  public boolean isSetExtJson() {
    return this.extJson != null;
  }

  public void setExtJsonIsSet(boolean value) {
    if (!value) {
      this.extJson = null;
    }
  }

  /**
   * 参数签名
   */
  public String getSign() {
    return this.sign;
  }

  /**
   * 参数签名
   */
  public PurchaseProductRequestV2 setSign(String sign) {
    this.sign = sign;
    return this;
  }

  public void unsetSign() {
    this.sign = null;
  }

  /** Returns true if field sign is set (has been assigned a value) and false otherwise */
  public boolean isSetSign() {
    return this.sign != null;
  }

  public void setSignIsSet(boolean value) {
    if (!value) {
      this.sign = null;
    }
  }

  /**
   * 订单支付过期时间（秒）
   */
  public long getOrderExpireTime() {
    return this.orderExpireTime;
  }

  /**
   * 订单支付过期时间（秒）
   */
  public PurchaseProductRequestV2 setOrderExpireTime(long orderExpireTime) {
    this.orderExpireTime = orderExpireTime;
    setOrderExpireTimeIsSet(true);
    return this;
  }

  public void unsetOrderExpireTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ORDEREXPIRETIME_ISSET_ID);
  }

  /** Returns true if field orderExpireTime is set (has been assigned a value) and false otherwise */
  public boolean isSetOrderExpireTime() {
    return EncodingUtils.testBit(__isset_bitfield, __ORDEREXPIRETIME_ISSET_ID);
  }

  public void setOrderExpireTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ORDEREXPIRETIME_ISSET_ID, value);
  }

  /**
   * 取消支付之后的跳转地址
   */
  public String getShowUrl() {
    return this.showUrl;
  }

  /**
   * 取消支付之后的跳转地址
   */
  public PurchaseProductRequestV2 setShowUrl(String showUrl) {
    this.showUrl = showUrl;
    return this;
  }

  public void unsetShowUrl() {
    this.showUrl = null;
  }

  /** Returns true if field showUrl is set (has been assigned a value) and false otherwise */
  public boolean isSetShowUrl() {
    return this.showUrl != null;
  }

  public void setShowUrlIsSet(boolean value) {
    if (!value) {
      this.showUrl = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case BUSINESS_ID:
      if (value == null) {
        unsetBusinessId();
      } else {
        setBusinessId((String)value);
      }
      break;

    case BUSINESS_ORDER_ID:
      if (value == null) {
        unsetBusinessOrderId();
      } else {
        setBusinessOrderId((Long)value);
      }
      break;

    case BUSINESS_TIME:
      if (value == null) {
        unsetBusinessTime();
      } else {
        setBusinessTime((Long)value);
      }
      break;

    case AMOUNT:
      if (value == null) {
        unsetAmount();
      } else {
        setAmount((String)value);
      }
      break;

    case KUGOU_ID:
      if (value == null) {
        unsetKugouId();
      } else {
        setKugouId((Long)value);
      }
      break;

    case SUBJECT:
      if (value == null) {
        unsetSubject();
      } else {
        setSubject((String)value);
      }
      break;

    case SYNC_URL:
      if (value == null) {
        unsetSyncUrl();
      } else {
        setSyncUrl((String)value);
      }
      break;

    case REDIRECT_URL:
      if (value == null) {
        unsetRedirectUrl();
      } else {
        setRedirectUrl((String)value);
      }
      break;

    case CLIENT_IP:
      if (value == null) {
        unsetClientIp();
      } else {
        setClientIp((String)value);
      }
      break;

    case PID:
      if (value == null) {
        unsetPid();
      } else {
        setPid((Integer)value);
      }
      break;

    case ORDER_LIST:
      if (value == null) {
        unsetOrderList();
      } else {
        setOrderList((List<PurchaseOrder>)value);
      }
      break;

    case PAY_TYPE_ID:
      if (value == null) {
        unsetPayTypeId();
      } else {
        setPayTypeId((Integer)value);
      }
      break;

    case OPEN_ID:
      if (value == null) {
        unsetOpenId();
      } else {
        setOpenId((String)value);
      }
      break;

    case EXT_JSON:
      if (value == null) {
        unsetExtJson();
      } else {
        setExtJson((String)value);
      }
      break;

    case SIGN:
      if (value == null) {
        unsetSign();
      } else {
        setSign((String)value);
      }
      break;

    case ORDER_EXPIRE_TIME:
      if (value == null) {
        unsetOrderExpireTime();
      } else {
        setOrderExpireTime((Long)value);
      }
      break;

    case SHOW_URL:
      if (value == null) {
        unsetShowUrl();
      } else {
        setShowUrl((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case BUSINESS_ID:
      return getBusinessId();

    case BUSINESS_ORDER_ID:
      return getBusinessOrderId();

    case BUSINESS_TIME:
      return getBusinessTime();

    case AMOUNT:
      return getAmount();

    case KUGOU_ID:
      return getKugouId();

    case SUBJECT:
      return getSubject();

    case SYNC_URL:
      return getSyncUrl();

    case REDIRECT_URL:
      return getRedirectUrl();

    case CLIENT_IP:
      return getClientIp();

    case PID:
      return getPid();

    case ORDER_LIST:
      return getOrderList();

    case PAY_TYPE_ID:
      return getPayTypeId();

    case OPEN_ID:
      return getOpenId();

    case EXT_JSON:
      return getExtJson();

    case SIGN:
      return getSign();

    case ORDER_EXPIRE_TIME:
      return getOrderExpireTime();

    case SHOW_URL:
      return getShowUrl();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case BUSINESS_ID:
      return isSetBusinessId();
    case BUSINESS_ORDER_ID:
      return isSetBusinessOrderId();
    case BUSINESS_TIME:
      return isSetBusinessTime();
    case AMOUNT:
      return isSetAmount();
    case KUGOU_ID:
      return isSetKugouId();
    case SUBJECT:
      return isSetSubject();
    case SYNC_URL:
      return isSetSyncUrl();
    case REDIRECT_URL:
      return isSetRedirectUrl();
    case CLIENT_IP:
      return isSetClientIp();
    case PID:
      return isSetPid();
    case ORDER_LIST:
      return isSetOrderList();
    case PAY_TYPE_ID:
      return isSetPayTypeId();
    case OPEN_ID:
      return isSetOpenId();
    case EXT_JSON:
      return isSetExtJson();
    case SIGN:
      return isSetSign();
    case ORDER_EXPIRE_TIME:
      return isSetOrderExpireTime();
    case SHOW_URL:
      return isSetShowUrl();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof PurchaseProductRequestV2)
      return this.equals((PurchaseProductRequestV2)that);
    return false;
  }

  public boolean equals(PurchaseProductRequestV2 that) {
    if (that == null)
      return false;

    boolean this_present_businessId = true && this.isSetBusinessId();
    boolean that_present_businessId = true && that.isSetBusinessId();
    if (this_present_businessId || that_present_businessId) {
      if (!(this_present_businessId && that_present_businessId))
        return false;
      if (!this.businessId.equals(that.businessId))
        return false;
    }

    boolean this_present_businessOrderId = true;
    boolean that_present_businessOrderId = true;
    if (this_present_businessOrderId || that_present_businessOrderId) {
      if (!(this_present_businessOrderId && that_present_businessOrderId))
        return false;
      if (this.businessOrderId != that.businessOrderId)
        return false;
    }

    boolean this_present_businessTime = true;
    boolean that_present_businessTime = true;
    if (this_present_businessTime || that_present_businessTime) {
      if (!(this_present_businessTime && that_present_businessTime))
        return false;
      if (this.businessTime != that.businessTime)
        return false;
    }

    boolean this_present_amount = true && this.isSetAmount();
    boolean that_present_amount = true && that.isSetAmount();
    if (this_present_amount || that_present_amount) {
      if (!(this_present_amount && that_present_amount))
        return false;
      if (!this.amount.equals(that.amount))
        return false;
    }

    boolean this_present_kugouId = true;
    boolean that_present_kugouId = true;
    if (this_present_kugouId || that_present_kugouId) {
      if (!(this_present_kugouId && that_present_kugouId))
        return false;
      if (this.kugouId != that.kugouId)
        return false;
    }

    boolean this_present_subject = true && this.isSetSubject();
    boolean that_present_subject = true && that.isSetSubject();
    if (this_present_subject || that_present_subject) {
      if (!(this_present_subject && that_present_subject))
        return false;
      if (!this.subject.equals(that.subject))
        return false;
    }

    boolean this_present_syncUrl = true && this.isSetSyncUrl();
    boolean that_present_syncUrl = true && that.isSetSyncUrl();
    if (this_present_syncUrl || that_present_syncUrl) {
      if (!(this_present_syncUrl && that_present_syncUrl))
        return false;
      if (!this.syncUrl.equals(that.syncUrl))
        return false;
    }

    boolean this_present_redirectUrl = true && this.isSetRedirectUrl();
    boolean that_present_redirectUrl = true && that.isSetRedirectUrl();
    if (this_present_redirectUrl || that_present_redirectUrl) {
      if (!(this_present_redirectUrl && that_present_redirectUrl))
        return false;
      if (!this.redirectUrl.equals(that.redirectUrl))
        return false;
    }

    boolean this_present_clientIp = true && this.isSetClientIp();
    boolean that_present_clientIp = true && that.isSetClientIp();
    if (this_present_clientIp || that_present_clientIp) {
      if (!(this_present_clientIp && that_present_clientIp))
        return false;
      if (!this.clientIp.equals(that.clientIp))
        return false;
    }

    boolean this_present_pid = true;
    boolean that_present_pid = true;
    if (this_present_pid || that_present_pid) {
      if (!(this_present_pid && that_present_pid))
        return false;
      if (this.pid != that.pid)
        return false;
    }

    boolean this_present_orderList = true && this.isSetOrderList();
    boolean that_present_orderList = true && that.isSetOrderList();
    if (this_present_orderList || that_present_orderList) {
      if (!(this_present_orderList && that_present_orderList))
        return false;
      if (!this.orderList.equals(that.orderList))
        return false;
    }

    boolean this_present_payTypeId = true;
    boolean that_present_payTypeId = true;
    if (this_present_payTypeId || that_present_payTypeId) {
      if (!(this_present_payTypeId && that_present_payTypeId))
        return false;
      if (this.payTypeId != that.payTypeId)
        return false;
    }

    boolean this_present_openId = true && this.isSetOpenId();
    boolean that_present_openId = true && that.isSetOpenId();
    if (this_present_openId || that_present_openId) {
      if (!(this_present_openId && that_present_openId))
        return false;
      if (!this.openId.equals(that.openId))
        return false;
    }

    boolean this_present_extJson = true && this.isSetExtJson();
    boolean that_present_extJson = true && that.isSetExtJson();
    if (this_present_extJson || that_present_extJson) {
      if (!(this_present_extJson && that_present_extJson))
        return false;
      if (!this.extJson.equals(that.extJson))
        return false;
    }

    boolean this_present_sign = true && this.isSetSign();
    boolean that_present_sign = true && that.isSetSign();
    if (this_present_sign || that_present_sign) {
      if (!(this_present_sign && that_present_sign))
        return false;
      if (!this.sign.equals(that.sign))
        return false;
    }

    boolean this_present_orderExpireTime = true && this.isSetOrderExpireTime();
    boolean that_present_orderExpireTime = true && that.isSetOrderExpireTime();
    if (this_present_orderExpireTime || that_present_orderExpireTime) {
      if (!(this_present_orderExpireTime && that_present_orderExpireTime))
        return false;
      if (this.orderExpireTime != that.orderExpireTime)
        return false;
    }

    boolean this_present_showUrl = true && this.isSetShowUrl();
    boolean that_present_showUrl = true && that.isSetShowUrl();
    if (this_present_showUrl || that_present_showUrl) {
      if (!(this_present_showUrl && that_present_showUrl))
        return false;
      if (!this.showUrl.equals(that.showUrl))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_businessId = true && (isSetBusinessId());
    list.add(present_businessId);
    if (present_businessId)
      list.add(businessId);

    boolean present_businessOrderId = true;
    list.add(present_businessOrderId);
    if (present_businessOrderId)
      list.add(businessOrderId);

    boolean present_businessTime = true;
    list.add(present_businessTime);
    if (present_businessTime)
      list.add(businessTime);

    boolean present_amount = true && (isSetAmount());
    list.add(present_amount);
    if (present_amount)
      list.add(amount);

    boolean present_kugouId = true;
    list.add(present_kugouId);
    if (present_kugouId)
      list.add(kugouId);

    boolean present_subject = true && (isSetSubject());
    list.add(present_subject);
    if (present_subject)
      list.add(subject);

    boolean present_syncUrl = true && (isSetSyncUrl());
    list.add(present_syncUrl);
    if (present_syncUrl)
      list.add(syncUrl);

    boolean present_redirectUrl = true && (isSetRedirectUrl());
    list.add(present_redirectUrl);
    if (present_redirectUrl)
      list.add(redirectUrl);

    boolean present_clientIp = true && (isSetClientIp());
    list.add(present_clientIp);
    if (present_clientIp)
      list.add(clientIp);

    boolean present_pid = true;
    list.add(present_pid);
    if (present_pid)
      list.add(pid);

    boolean present_orderList = true && (isSetOrderList());
    list.add(present_orderList);
    if (present_orderList)
      list.add(orderList);

    boolean present_payTypeId = true;
    list.add(present_payTypeId);
    if (present_payTypeId)
      list.add(payTypeId);

    boolean present_openId = true && (isSetOpenId());
    list.add(present_openId);
    if (present_openId)
      list.add(openId);

    boolean present_extJson = true && (isSetExtJson());
    list.add(present_extJson);
    if (present_extJson)
      list.add(extJson);

    boolean present_sign = true && (isSetSign());
    list.add(present_sign);
    if (present_sign)
      list.add(sign);

    boolean present_orderExpireTime = true && (isSetOrderExpireTime());
    list.add(present_orderExpireTime);
    if (present_orderExpireTime)
      list.add(orderExpireTime);

    boolean present_showUrl = true && (isSetShowUrl());
    list.add(present_showUrl);
    if (present_showUrl)
      list.add(showUrl);

    return list.hashCode();
  }

  @Override
  public int compareTo(PurchaseProductRequestV2 other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetBusinessId()).compareTo(other.isSetBusinessId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBusinessId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.businessId, other.businessId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBusinessOrderId()).compareTo(other.isSetBusinessOrderId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBusinessOrderId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.businessOrderId, other.businessOrderId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBusinessTime()).compareTo(other.isSetBusinessTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBusinessTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.businessTime, other.businessTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAmount()).compareTo(other.isSetAmount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAmount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.amount, other.amount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKugouId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSubject()).compareTo(other.isSetSubject());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSubject()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.subject, other.subject);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSyncUrl()).compareTo(other.isSetSyncUrl());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSyncUrl()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.syncUrl, other.syncUrl);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRedirectUrl()).compareTo(other.isSetRedirectUrl());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRedirectUrl()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.redirectUrl, other.redirectUrl);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetClientIp()).compareTo(other.isSetClientIp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetClientIp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.clientIp, other.clientIp);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPid()).compareTo(other.isSetPid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pid, other.pid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetOrderList()).compareTo(other.isSetOrderList());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOrderList()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderList, other.orderList);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPayTypeId()).compareTo(other.isSetPayTypeId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPayTypeId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.payTypeId, other.payTypeId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetOpenId()).compareTo(other.isSetOpenId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOpenId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.openId, other.openId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtJson()).compareTo(other.isSetExtJson());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtJson()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extJson, other.extJson);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSign()).compareTo(other.isSetSign());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSign()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sign, other.sign);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetOrderExpireTime()).compareTo(other.isSetOrderExpireTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOrderExpireTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderExpireTime, other.orderExpireTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetShowUrl()).compareTo(other.isSetShowUrl());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetShowUrl()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.showUrl, other.showUrl);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("PurchaseProductRequestV2(");
    boolean first = true;

    sb.append("businessId:");
    if (this.businessId == null) {
      sb.append("null");
    } else {
      sb.append(this.businessId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("businessOrderId:");
    sb.append(this.businessOrderId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("businessTime:");
    sb.append(this.businessTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("amount:");
    if (this.amount == null) {
      sb.append("null");
    } else {
      sb.append(this.amount);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("kugouId:");
    sb.append(this.kugouId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("subject:");
    if (this.subject == null) {
      sb.append("null");
    } else {
      sb.append(this.subject);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("syncUrl:");
    if (this.syncUrl == null) {
      sb.append("null");
    } else {
      sb.append(this.syncUrl);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("redirectUrl:");
    if (this.redirectUrl == null) {
      sb.append("null");
    } else {
      sb.append(this.redirectUrl);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("clientIp:");
    if (this.clientIp == null) {
      sb.append("null");
    } else {
      sb.append(this.clientIp);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("pid:");
    sb.append(this.pid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("orderList:");
    if (this.orderList == null) {
      sb.append("null");
    } else {
      sb.append(this.orderList);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("payTypeId:");
    sb.append(this.payTypeId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("openId:");
    if (this.openId == null) {
      sb.append("null");
    } else {
      sb.append(this.openId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extJson:");
    if (this.extJson == null) {
      sb.append("null");
    } else {
      sb.append(this.extJson);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("sign:");
    if (this.sign == null) {
      sb.append("null");
    } else {
      sb.append(this.sign);
    }
    first = false;
    if (isSetOrderExpireTime()) {
      if (!first) sb.append(", ");
      sb.append("orderExpireTime:");
      sb.append(this.orderExpireTime);
      first = false;
    }
    if (isSetShowUrl()) {
      if (!first) sb.append(", ");
      sb.append("showUrl:");
      if (this.showUrl == null) {
        sb.append("null");
      } else {
        sb.append(this.showUrl);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (businessId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'businessId' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'businessOrderId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'businessTime' because it's a primitive and you chose the non-beans generator.
    if (amount == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'amount' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
    if (subject == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'subject' was not present! Struct: " + toString());
    }
    if (syncUrl == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'syncUrl' was not present! Struct: " + toString());
    }
    if (redirectUrl == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'redirectUrl' was not present! Struct: " + toString());
    }
    if (clientIp == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'clientIp' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'pid' because it's a primitive and you chose the non-beans generator.
    if (orderList == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderList' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'payTypeId' because it's a primitive and you chose the non-beans generator.
    if (openId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'openId' was not present! Struct: " + toString());
    }
    if (extJson == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'extJson' was not present! Struct: " + toString());
    }
    if (sign == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'sign' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PurchaseProductRequestV2StandardSchemeFactory implements SchemeFactory {
    public PurchaseProductRequestV2StandardScheme getScheme() {
      return new PurchaseProductRequestV2StandardScheme();
    }
  }

  private static class PurchaseProductRequestV2StandardScheme extends StandardScheme<PurchaseProductRequestV2> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PurchaseProductRequestV2 struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // BUSINESS_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.businessId = iprot.readString();
              struct.setBusinessIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // BUSINESS_ORDER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.businessOrderId = iprot.readI64();
              struct.setBusinessOrderIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // BUSINESS_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.businessTime = iprot.readI64();
              struct.setBusinessTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // AMOUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.amount = iprot.readString();
              struct.setAmountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // KUGOU_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.kugouId = iprot.readI64();
              struct.setKugouIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // SUBJECT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.subject = iprot.readString();
              struct.setSubjectIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // SYNC_URL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.syncUrl = iprot.readString();
              struct.setSyncUrlIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // REDIRECT_URL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.redirectUrl = iprot.readString();
              struct.setRedirectUrlIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // CLIENT_IP
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.clientIp = iprot.readString();
              struct.setClientIpIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // PID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.pid = iprot.readI32();
              struct.setPidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // ORDER_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list16 = iprot.readListBegin();
                struct.orderList = new ArrayList<PurchaseOrder>(_list16.size);
                PurchaseOrder _elem17;
                for (int _i18 = 0; _i18 < _list16.size; ++_i18)
                {
                  _elem17 = new PurchaseOrder();
                  _elem17.read(iprot);
                  struct.orderList.add(_elem17);
                }
                iprot.readListEnd();
              }
              struct.setOrderListIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // PAY_TYPE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.payTypeId = iprot.readI32();
              struct.setPayTypeIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // OPEN_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.openId = iprot.readString();
              struct.setOpenIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 14: // EXT_JSON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.extJson = iprot.readString();
              struct.setExtJsonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // SIGN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.sign = iprot.readString();
              struct.setSignIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 16: // ORDER_EXPIRE_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.orderExpireTime = iprot.readI64();
              struct.setOrderExpireTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 17: // SHOW_URL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.showUrl = iprot.readString();
              struct.setShowUrlIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetBusinessOrderId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'businessOrderId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetBusinessTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'businessTime' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetKugouId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetPid()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'pid' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetPayTypeId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'payTypeId' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PurchaseProductRequestV2 struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.businessId != null) {
        oprot.writeFieldBegin(BUSINESS_ID_FIELD_DESC);
        oprot.writeString(struct.businessId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(BUSINESS_ORDER_ID_FIELD_DESC);
      oprot.writeI64(struct.businessOrderId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(BUSINESS_TIME_FIELD_DESC);
      oprot.writeI64(struct.businessTime);
      oprot.writeFieldEnd();
      if (struct.amount != null) {
        oprot.writeFieldBegin(AMOUNT_FIELD_DESC);
        oprot.writeString(struct.amount);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
      oprot.writeI64(struct.kugouId);
      oprot.writeFieldEnd();
      if (struct.subject != null) {
        oprot.writeFieldBegin(SUBJECT_FIELD_DESC);
        oprot.writeString(struct.subject);
        oprot.writeFieldEnd();
      }
      if (struct.syncUrl != null) {
        oprot.writeFieldBegin(SYNC_URL_FIELD_DESC);
        oprot.writeString(struct.syncUrl);
        oprot.writeFieldEnd();
      }
      if (struct.redirectUrl != null) {
        oprot.writeFieldBegin(REDIRECT_URL_FIELD_DESC);
        oprot.writeString(struct.redirectUrl);
        oprot.writeFieldEnd();
      }
      if (struct.clientIp != null) {
        oprot.writeFieldBegin(CLIENT_IP_FIELD_DESC);
        oprot.writeString(struct.clientIp);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(PID_FIELD_DESC);
      oprot.writeI32(struct.pid);
      oprot.writeFieldEnd();
      if (struct.orderList != null) {
        oprot.writeFieldBegin(ORDER_LIST_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.orderList.size()));
          for (PurchaseOrder _iter19 : struct.orderList)
          {
            _iter19.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(PAY_TYPE_ID_FIELD_DESC);
      oprot.writeI32(struct.payTypeId);
      oprot.writeFieldEnd();
      if (struct.openId != null) {
        oprot.writeFieldBegin(OPEN_ID_FIELD_DESC);
        oprot.writeString(struct.openId);
        oprot.writeFieldEnd();
      }
      if (struct.extJson != null) {
        oprot.writeFieldBegin(EXT_JSON_FIELD_DESC);
        oprot.writeString(struct.extJson);
        oprot.writeFieldEnd();
      }
      if (struct.sign != null) {
        oprot.writeFieldBegin(SIGN_FIELD_DESC);
        oprot.writeString(struct.sign);
        oprot.writeFieldEnd();
      }
      if (struct.isSetOrderExpireTime()) {
        oprot.writeFieldBegin(ORDER_EXPIRE_TIME_FIELD_DESC);
        oprot.writeI64(struct.orderExpireTime);
        oprot.writeFieldEnd();
      }
      if (struct.showUrl != null) {
        if (struct.isSetShowUrl()) {
          oprot.writeFieldBegin(SHOW_URL_FIELD_DESC);
          oprot.writeString(struct.showUrl);
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PurchaseProductRequestV2TupleSchemeFactory implements SchemeFactory {
    public PurchaseProductRequestV2TupleScheme getScheme() {
      return new PurchaseProductRequestV2TupleScheme();
    }
  }

  private static class PurchaseProductRequestV2TupleScheme extends TupleScheme<PurchaseProductRequestV2> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PurchaseProductRequestV2 struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeString(struct.businessId);
      oprot.writeI64(struct.businessOrderId);
      oprot.writeI64(struct.businessTime);
      oprot.writeString(struct.amount);
      oprot.writeI64(struct.kugouId);
      oprot.writeString(struct.subject);
      oprot.writeString(struct.syncUrl);
      oprot.writeString(struct.redirectUrl);
      oprot.writeString(struct.clientIp);
      oprot.writeI32(struct.pid);
      {
        oprot.writeI32(struct.orderList.size());
        for (PurchaseOrder _iter20 : struct.orderList)
        {
          _iter20.write(oprot);
        }
      }
      oprot.writeI32(struct.payTypeId);
      oprot.writeString(struct.openId);
      oprot.writeString(struct.extJson);
      oprot.writeString(struct.sign);
      BitSet optionals = new BitSet();
      if (struct.isSetOrderExpireTime()) {
        optionals.set(0);
      }
      if (struct.isSetShowUrl()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetOrderExpireTime()) {
        oprot.writeI64(struct.orderExpireTime);
      }
      if (struct.isSetShowUrl()) {
        oprot.writeString(struct.showUrl);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PurchaseProductRequestV2 struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.businessId = iprot.readString();
      struct.setBusinessIdIsSet(true);
      struct.businessOrderId = iprot.readI64();
      struct.setBusinessOrderIdIsSet(true);
      struct.businessTime = iprot.readI64();
      struct.setBusinessTimeIsSet(true);
      struct.amount = iprot.readString();
      struct.setAmountIsSet(true);
      struct.kugouId = iprot.readI64();
      struct.setKugouIdIsSet(true);
      struct.subject = iprot.readString();
      struct.setSubjectIsSet(true);
      struct.syncUrl = iprot.readString();
      struct.setSyncUrlIsSet(true);
      struct.redirectUrl = iprot.readString();
      struct.setRedirectUrlIsSet(true);
      struct.clientIp = iprot.readString();
      struct.setClientIpIsSet(true);
      struct.pid = iprot.readI32();
      struct.setPidIsSet(true);
      {
        org.apache.thrift.protocol.TList _list21 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
        struct.orderList = new ArrayList<PurchaseOrder>(_list21.size);
        PurchaseOrder _elem22;
        for (int _i23 = 0; _i23 < _list21.size; ++_i23)
        {
          _elem22 = new PurchaseOrder();
          _elem22.read(iprot);
          struct.orderList.add(_elem22);
        }
      }
      struct.setOrderListIsSet(true);
      struct.payTypeId = iprot.readI32();
      struct.setPayTypeIdIsSet(true);
      struct.openId = iprot.readString();
      struct.setOpenIdIsSet(true);
      struct.extJson = iprot.readString();
      struct.setExtJsonIsSet(true);
      struct.sign = iprot.readString();
      struct.setSignIsSet(true);
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.orderExpireTime = iprot.readI64();
        struct.setOrderExpireTimeIsSet(true);
      }
      if (incoming.get(1)) {
        struct.showUrl = iprot.readString();
        struct.setShowUrlIsSet(true);
      }
    }
  }

}

