/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.recharge.thrift.callback;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-08-14")
public class PurchaseCoinForIosRequest implements org.apache.thrift.TBase<PurchaseCoinForIosRequest, PurchaseCoinForIosRequest._Fields>, java.io.Serializable, Cloneable, Comparable<PurchaseCoinForIosRequest> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PurchaseCoinForIosRequest");

  private static final org.apache.thrift.protocol.TField ORDER_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("orderNum", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField KEY_FIELD_DESC = new org.apache.thrift.protocol.TField("key", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("time", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField COIN_FIELD_DESC = new org.apache.thrift.protocol.TField("coin", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField MONEY_FIELD_DESC = new org.apache.thrift.protocol.TField("money", org.apache.thrift.protocol.TType.STRING, (short)6);
  private static final org.apache.thrift.protocol.TField SIGN_BOOK_FIELD_DESC = new org.apache.thrift.protocol.TField("signBook", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("type", org.apache.thrift.protocol.TType.I32, (short)8);
  private static final org.apache.thrift.protocol.TField GOODS_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("goodsId", org.apache.thrift.protocol.TType.STRING, (short)9);
  private static final org.apache.thrift.protocol.TField PID_FIELD_DESC = new org.apache.thrift.protocol.TField("pid", org.apache.thrift.protocol.TType.I32, (short)10);
  private static final org.apache.thrift.protocol.TField VERSION_FIELD_DESC = new org.apache.thrift.protocol.TField("version", org.apache.thrift.protocol.TType.STRING, (short)11);
  private static final org.apache.thrift.protocol.TField CHANNEL_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("channelId", org.apache.thrift.protocol.TType.I32, (short)12);
  private static final org.apache.thrift.protocol.TField IS_SANDBOX_FIELD_DESC = new org.apache.thrift.protocol.TField("isSandbox", org.apache.thrift.protocol.TType.I32, (short)13);
  private static final org.apache.thrift.protocol.TField TRADE_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("tradeTime", org.apache.thrift.protocol.TType.STRING, (short)14);
  private static final org.apache.thrift.protocol.TField TRADE_NO_FIELD_DESC = new org.apache.thrift.protocol.TField("tradeNo", org.apache.thrift.protocol.TType.STRING, (short)15);
  private static final org.apache.thrift.protocol.TField PARTNER_FIELD_DESC = new org.apache.thrift.protocol.TField("partner", org.apache.thrift.protocol.TType.STRING, (short)16);
  private static final org.apache.thrift.protocol.TField BUSINESS_EXT_FIELD_DESC = new org.apache.thrift.protocol.TField("businessExt", org.apache.thrift.protocol.TType.STRING, (short)17);
  private static final org.apache.thrift.protocol.TField CONSUME_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("consumeType", org.apache.thrift.protocol.TType.STRING, (short)18);
  private static final org.apache.thrift.protocol.TField ROOM_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("roomId", org.apache.thrift.protocol.TType.STRING, (short)19);
  private static final org.apache.thrift.protocol.TField GIFT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("giftId", org.apache.thrift.protocol.TType.STRING, (short)20);
  private static final org.apache.thrift.protocol.TField GIFT_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("giftNum", org.apache.thrift.protocol.TType.STRING, (short)21);
  private static final org.apache.thrift.protocol.TField APP_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("appId", org.apache.thrift.protocol.TType.STRING, (short)22);
  private static final org.apache.thrift.protocol.TField BUNDLE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("bundleId", org.apache.thrift.protocol.TType.STRING, (short)23);
  private static final org.apache.thrift.protocol.TField AREA_CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("areaCode", org.apache.thrift.protocol.TType.STRING, (short)24);
  private static final org.apache.thrift.protocol.TField TIME_ZONE_FIELD_DESC = new org.apache.thrift.protocol.TField("timeZone", org.apache.thrift.protocol.TType.STRING, (short)25);
  private static final org.apache.thrift.protocol.TField CURRENCY_FIELD_DESC = new org.apache.thrift.protocol.TField("currency", org.apache.thrift.protocol.TType.STRING, (short)26);
  private static final org.apache.thrift.protocol.TField CURRENCY_AMOUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("currencyAmount", org.apache.thrift.protocol.TType.STRING, (short)27);
  private static final org.apache.thrift.protocol.TField USD_AMOUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("usdAmount", org.apache.thrift.protocol.TType.STRING, (short)28);
  private static final org.apache.thrift.protocol.TField CLIENT_IP_FIELD_DESC = new org.apache.thrift.protocol.TField("clientIp", org.apache.thrift.protocol.TType.STRING, (short)29);
  private static final org.apache.thrift.protocol.TField AREA_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("areaId", org.apache.thrift.protocol.TType.I32, (short)30);
  private static final org.apache.thrift.protocol.TField OUT_TRADE_NO_FIELD_DESC = new org.apache.thrift.protocol.TField("outTradeNo", org.apache.thrift.protocol.TType.STRING, (short)31);
  private static final org.apache.thrift.protocol.TField PRODUCT_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("productType", org.apache.thrift.protocol.TType.I32, (short)32);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new PurchaseCoinForIosRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new PurchaseCoinForIosRequestTupleSchemeFactory());
  }

  public String orderNum; // required
  public String key; // required
  public long kugouId; // required
  public int time; // required
  public String coin; // required
  public String money; // required
  public String signBook; // required
  public int type; // required
  public String goodsId; // required
  public int pid; // required
  public String version; // required
  public int channelId; // required
  public int isSandbox; // required
  public String tradeTime; // required
  public String tradeNo; // required
  public String partner; // required
  public String businessExt; // required
  public String consumeType; // optional
  public String roomId; // optional
  public String giftId; // optional
  public String giftNum; // optional
  public String appId; // optional
  public String bundleId; // optional
  public String areaCode; // optional
  public String timeZone; // optional
  public String currency; // optional
  public String currencyAmount; // optional
  public String usdAmount; // optional
  public String clientIp; // optional
  public int areaId; // optional
  public String outTradeNo; // optional
  public int productType; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ORDER_NUM((short)1, "orderNum"),
    KEY((short)2, "key"),
    KUGOU_ID((short)3, "kugouId"),
    TIME((short)4, "time"),
    COIN((short)5, "coin"),
    MONEY((short)6, "money"),
    SIGN_BOOK((short)7, "signBook"),
    TYPE((short)8, "type"),
    GOODS_ID((short)9, "goodsId"),
    PID((short)10, "pid"),
    VERSION((short)11, "version"),
    CHANNEL_ID((short)12, "channelId"),
    IS_SANDBOX((short)13, "isSandbox"),
    TRADE_TIME((short)14, "tradeTime"),
    TRADE_NO((short)15, "tradeNo"),
    PARTNER((short)16, "partner"),
    BUSINESS_EXT((short)17, "businessExt"),
    CONSUME_TYPE((short)18, "consumeType"),
    ROOM_ID((short)19, "roomId"),
    GIFT_ID((short)20, "giftId"),
    GIFT_NUM((short)21, "giftNum"),
    APP_ID((short)22, "appId"),
    BUNDLE_ID((short)23, "bundleId"),
    AREA_CODE((short)24, "areaCode"),
    TIME_ZONE((short)25, "timeZone"),
    CURRENCY((short)26, "currency"),
    CURRENCY_AMOUNT((short)27, "currencyAmount"),
    USD_AMOUNT((short)28, "usdAmount"),
    CLIENT_IP((short)29, "clientIp"),
    AREA_ID((short)30, "areaId"),
    OUT_TRADE_NO((short)31, "outTradeNo"),
    PRODUCT_TYPE((short)32, "productType");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ORDER_NUM
          return ORDER_NUM;
        case 2: // KEY
          return KEY;
        case 3: // KUGOU_ID
          return KUGOU_ID;
        case 4: // TIME
          return TIME;
        case 5: // COIN
          return COIN;
        case 6: // MONEY
          return MONEY;
        case 7: // SIGN_BOOK
          return SIGN_BOOK;
        case 8: // TYPE
          return TYPE;
        case 9: // GOODS_ID
          return GOODS_ID;
        case 10: // PID
          return PID;
        case 11: // VERSION
          return VERSION;
        case 12: // CHANNEL_ID
          return CHANNEL_ID;
        case 13: // IS_SANDBOX
          return IS_SANDBOX;
        case 14: // TRADE_TIME
          return TRADE_TIME;
        case 15: // TRADE_NO
          return TRADE_NO;
        case 16: // PARTNER
          return PARTNER;
        case 17: // BUSINESS_EXT
          return BUSINESS_EXT;
        case 18: // CONSUME_TYPE
          return CONSUME_TYPE;
        case 19: // ROOM_ID
          return ROOM_ID;
        case 20: // GIFT_ID
          return GIFT_ID;
        case 21: // GIFT_NUM
          return GIFT_NUM;
        case 22: // APP_ID
          return APP_ID;
        case 23: // BUNDLE_ID
          return BUNDLE_ID;
        case 24: // AREA_CODE
          return AREA_CODE;
        case 25: // TIME_ZONE
          return TIME_ZONE;
        case 26: // CURRENCY
          return CURRENCY;
        case 27: // CURRENCY_AMOUNT
          return CURRENCY_AMOUNT;
        case 28: // USD_AMOUNT
          return USD_AMOUNT;
        case 29: // CLIENT_IP
          return CLIENT_IP;
        case 30: // AREA_ID
          return AREA_ID;
        case 31: // OUT_TRADE_NO
          return OUT_TRADE_NO;
        case 32: // PRODUCT_TYPE
          return PRODUCT_TYPE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __KUGOUID_ISSET_ID = 0;
  private static final int __TIME_ISSET_ID = 1;
  private static final int __TYPE_ISSET_ID = 2;
  private static final int __PID_ISSET_ID = 3;
  private static final int __CHANNELID_ISSET_ID = 4;
  private static final int __ISSANDBOX_ISSET_ID = 5;
  private static final int __AREAID_ISSET_ID = 6;
  private static final int __PRODUCTTYPE_ISSET_ID = 7;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.CONSUME_TYPE,_Fields.ROOM_ID,_Fields.GIFT_ID,_Fields.GIFT_NUM,_Fields.APP_ID,_Fields.BUNDLE_ID,_Fields.AREA_CODE,_Fields.TIME_ZONE,_Fields.CURRENCY,_Fields.CURRENCY_AMOUNT,_Fields.USD_AMOUNT,_Fields.CLIENT_IP,_Fields.AREA_ID,_Fields.OUT_TRADE_NO,_Fields.PRODUCT_TYPE};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ORDER_NUM, new org.apache.thrift.meta_data.FieldMetaData("orderNum", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.KEY, new org.apache.thrift.meta_data.FieldMetaData("key", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TIME, new org.apache.thrift.meta_data.FieldMetaData("time", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.COIN, new org.apache.thrift.meta_data.FieldMetaData("coin", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.MONEY, new org.apache.thrift.meta_data.FieldMetaData("money", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SIGN_BOOK, new org.apache.thrift.meta_data.FieldMetaData("signBook", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.TYPE, new org.apache.thrift.meta_data.FieldMetaData("type", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.GOODS_ID, new org.apache.thrift.meta_data.FieldMetaData("goodsId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PID, new org.apache.thrift.meta_data.FieldMetaData("pid", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.VERSION, new org.apache.thrift.meta_data.FieldMetaData("version", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CHANNEL_ID, new org.apache.thrift.meta_data.FieldMetaData("channelId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.IS_SANDBOX, new org.apache.thrift.meta_data.FieldMetaData("isSandbox", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TRADE_TIME, new org.apache.thrift.meta_data.FieldMetaData("tradeTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.TRADE_NO, new org.apache.thrift.meta_data.FieldMetaData("tradeNo", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PARTNER, new org.apache.thrift.meta_data.FieldMetaData("partner", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BUSINESS_EXT, new org.apache.thrift.meta_data.FieldMetaData("businessExt", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CONSUME_TYPE, new org.apache.thrift.meta_data.FieldMetaData("consumeType", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ROOM_ID, new org.apache.thrift.meta_data.FieldMetaData("roomId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.GIFT_ID, new org.apache.thrift.meta_data.FieldMetaData("giftId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.GIFT_NUM, new org.apache.thrift.meta_data.FieldMetaData("giftNum", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.APP_ID, new org.apache.thrift.meta_data.FieldMetaData("appId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BUNDLE_ID, new org.apache.thrift.meta_data.FieldMetaData("bundleId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.AREA_CODE, new org.apache.thrift.meta_data.FieldMetaData("areaCode", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.TIME_ZONE, new org.apache.thrift.meta_data.FieldMetaData("timeZone", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CURRENCY, new org.apache.thrift.meta_data.FieldMetaData("currency", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CURRENCY_AMOUNT, new org.apache.thrift.meta_data.FieldMetaData("currencyAmount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.USD_AMOUNT, new org.apache.thrift.meta_data.FieldMetaData("usdAmount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CLIENT_IP, new org.apache.thrift.meta_data.FieldMetaData("clientIp", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.AREA_ID, new org.apache.thrift.meta_data.FieldMetaData("areaId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.OUT_TRADE_NO, new org.apache.thrift.meta_data.FieldMetaData("outTradeNo", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PRODUCT_TYPE, new org.apache.thrift.meta_data.FieldMetaData("productType", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PurchaseCoinForIosRequest.class, metaDataMap);
  }

  public PurchaseCoinForIosRequest() {
  }

  public PurchaseCoinForIosRequest(
    String orderNum,
    String key,
    long kugouId,
    int time,
    String coin,
    String money,
    String signBook,
    int type,
    String goodsId,
    int pid,
    String version,
    int channelId,
    int isSandbox,
    String tradeTime,
    String tradeNo,
    String partner,
    String businessExt)
  {
    this();
    this.orderNum = orderNum;
    this.key = key;
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    this.time = time;
    setTimeIsSet(true);
    this.coin = coin;
    this.money = money;
    this.signBook = signBook;
    this.type = type;
    setTypeIsSet(true);
    this.goodsId = goodsId;
    this.pid = pid;
    setPidIsSet(true);
    this.version = version;
    this.channelId = channelId;
    setChannelIdIsSet(true);
    this.isSandbox = isSandbox;
    setIsSandboxIsSet(true);
    this.tradeTime = tradeTime;
    this.tradeNo = tradeNo;
    this.partner = partner;
    this.businessExt = businessExt;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PurchaseCoinForIosRequest(PurchaseCoinForIosRequest other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetOrderNum()) {
      this.orderNum = other.orderNum;
    }
    if (other.isSetKey()) {
      this.key = other.key;
    }
    this.kugouId = other.kugouId;
    this.time = other.time;
    if (other.isSetCoin()) {
      this.coin = other.coin;
    }
    if (other.isSetMoney()) {
      this.money = other.money;
    }
    if (other.isSetSignBook()) {
      this.signBook = other.signBook;
    }
    this.type = other.type;
    if (other.isSetGoodsId()) {
      this.goodsId = other.goodsId;
    }
    this.pid = other.pid;
    if (other.isSetVersion()) {
      this.version = other.version;
    }
    this.channelId = other.channelId;
    this.isSandbox = other.isSandbox;
    if (other.isSetTradeTime()) {
      this.tradeTime = other.tradeTime;
    }
    if (other.isSetTradeNo()) {
      this.tradeNo = other.tradeNo;
    }
    if (other.isSetPartner()) {
      this.partner = other.partner;
    }
    if (other.isSetBusinessExt()) {
      this.businessExt = other.businessExt;
    }
    if (other.isSetConsumeType()) {
      this.consumeType = other.consumeType;
    }
    if (other.isSetRoomId()) {
      this.roomId = other.roomId;
    }
    if (other.isSetGiftId()) {
      this.giftId = other.giftId;
    }
    if (other.isSetGiftNum()) {
      this.giftNum = other.giftNum;
    }
    if (other.isSetAppId()) {
      this.appId = other.appId;
    }
    if (other.isSetBundleId()) {
      this.bundleId = other.bundleId;
    }
    if (other.isSetAreaCode()) {
      this.areaCode = other.areaCode;
    }
    if (other.isSetTimeZone()) {
      this.timeZone = other.timeZone;
    }
    if (other.isSetCurrency()) {
      this.currency = other.currency;
    }
    if (other.isSetCurrencyAmount()) {
      this.currencyAmount = other.currencyAmount;
    }
    if (other.isSetUsdAmount()) {
      this.usdAmount = other.usdAmount;
    }
    if (other.isSetClientIp()) {
      this.clientIp = other.clientIp;
    }
    this.areaId = other.areaId;
    if (other.isSetOutTradeNo()) {
      this.outTradeNo = other.outTradeNo;
    }
    this.productType = other.productType;
  }

  public PurchaseCoinForIosRequest deepCopy() {
    return new PurchaseCoinForIosRequest(this);
  }

  @Override
  public void clear() {
    this.orderNum = null;
    this.key = null;
    setKugouIdIsSet(false);
    this.kugouId = 0;
    setTimeIsSet(false);
    this.time = 0;
    this.coin = null;
    this.money = null;
    this.signBook = null;
    setTypeIsSet(false);
    this.type = 0;
    this.goodsId = null;
    setPidIsSet(false);
    this.pid = 0;
    this.version = null;
    setChannelIdIsSet(false);
    this.channelId = 0;
    setIsSandboxIsSet(false);
    this.isSandbox = 0;
    this.tradeTime = null;
    this.tradeNo = null;
    this.partner = null;
    this.businessExt = null;
    this.consumeType = null;
    this.roomId = null;
    this.giftId = null;
    this.giftNum = null;
    this.appId = null;
    this.bundleId = null;
    this.areaCode = null;
    this.timeZone = null;
    this.currency = null;
    this.currencyAmount = null;
    this.usdAmount = null;
    this.clientIp = null;
    setAreaIdIsSet(false);
    this.areaId = 0;
    this.outTradeNo = null;
    setProductTypeIsSet(false);
    this.productType = 0;
  }

  public String getOrderNum() {
    return this.orderNum;
  }

  public PurchaseCoinForIosRequest setOrderNum(String orderNum) {
    this.orderNum = orderNum;
    return this;
  }

  public void unsetOrderNum() {
    this.orderNum = null;
  }

  /** Returns true if field orderNum is set (has been assigned a value) and false otherwise */
  public boolean isSetOrderNum() {
    return this.orderNum != null;
  }

  public void setOrderNumIsSet(boolean value) {
    if (!value) {
      this.orderNum = null;
    }
  }

  public String getKey() {
    return this.key;
  }

  public PurchaseCoinForIosRequest setKey(String key) {
    this.key = key;
    return this;
  }

  public void unsetKey() {
    this.key = null;
  }

  /** Returns true if field key is set (has been assigned a value) and false otherwise */
  public boolean isSetKey() {
    return this.key != null;
  }

  public void setKeyIsSet(boolean value) {
    if (!value) {
      this.key = null;
    }
  }

  public long getKugouId() {
    return this.kugouId;
  }

  public PurchaseCoinForIosRequest setKugouId(long kugouId) {
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    return this;
  }

  public void unsetKugouId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
  public boolean isSetKugouId() {
    return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  public void setKugouIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
  }

  public int getTime() {
    return this.time;
  }

  public PurchaseCoinForIosRequest setTime(int time) {
    this.time = time;
    setTimeIsSet(true);
    return this;
  }

  public void unsetTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TIME_ISSET_ID);
  }

  /** Returns true if field time is set (has been assigned a value) and false otherwise */
  public boolean isSetTime() {
    return EncodingUtils.testBit(__isset_bitfield, __TIME_ISSET_ID);
  }

  public void setTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TIME_ISSET_ID, value);
  }

  public String getCoin() {
    return this.coin;
  }

  public PurchaseCoinForIosRequest setCoin(String coin) {
    this.coin = coin;
    return this;
  }

  public void unsetCoin() {
    this.coin = null;
  }

  /** Returns true if field coin is set (has been assigned a value) and false otherwise */
  public boolean isSetCoin() {
    return this.coin != null;
  }

  public void setCoinIsSet(boolean value) {
    if (!value) {
      this.coin = null;
    }
  }

  public String getMoney() {
    return this.money;
  }

  public PurchaseCoinForIosRequest setMoney(String money) {
    this.money = money;
    return this;
  }

  public void unsetMoney() {
    this.money = null;
  }

  /** Returns true if field money is set (has been assigned a value) and false otherwise */
  public boolean isSetMoney() {
    return this.money != null;
  }

  public void setMoneyIsSet(boolean value) {
    if (!value) {
      this.money = null;
    }
  }

  public String getSignBook() {
    return this.signBook;
  }

  public PurchaseCoinForIosRequest setSignBook(String signBook) {
    this.signBook = signBook;
    return this;
  }

  public void unsetSignBook() {
    this.signBook = null;
  }

  /** Returns true if field signBook is set (has been assigned a value) and false otherwise */
  public boolean isSetSignBook() {
    return this.signBook != null;
  }

  public void setSignBookIsSet(boolean value) {
    if (!value) {
      this.signBook = null;
    }
  }

  public int getType() {
    return this.type;
  }

  public PurchaseCoinForIosRequest setType(int type) {
    this.type = type;
    setTypeIsSet(true);
    return this;
  }

  public void unsetType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TYPE_ISSET_ID);
  }

  /** Returns true if field type is set (has been assigned a value) and false otherwise */
  public boolean isSetType() {
    return EncodingUtils.testBit(__isset_bitfield, __TYPE_ISSET_ID);
  }

  public void setTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TYPE_ISSET_ID, value);
  }

  public String getGoodsId() {
    return this.goodsId;
  }

  public PurchaseCoinForIosRequest setGoodsId(String goodsId) {
    this.goodsId = goodsId;
    return this;
  }

  public void unsetGoodsId() {
    this.goodsId = null;
  }

  /** Returns true if field goodsId is set (has been assigned a value) and false otherwise */
  public boolean isSetGoodsId() {
    return this.goodsId != null;
  }

  public void setGoodsIdIsSet(boolean value) {
    if (!value) {
      this.goodsId = null;
    }
  }

  public int getPid() {
    return this.pid;
  }

  public PurchaseCoinForIosRequest setPid(int pid) {
    this.pid = pid;
    setPidIsSet(true);
    return this;
  }

  public void unsetPid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PID_ISSET_ID);
  }

  /** Returns true if field pid is set (has been assigned a value) and false otherwise */
  public boolean isSetPid() {
    return EncodingUtils.testBit(__isset_bitfield, __PID_ISSET_ID);
  }

  public void setPidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PID_ISSET_ID, value);
  }

  public String getVersion() {
    return this.version;
  }

  public PurchaseCoinForIosRequest setVersion(String version) {
    this.version = version;
    return this;
  }

  public void unsetVersion() {
    this.version = null;
  }

  /** Returns true if field version is set (has been assigned a value) and false otherwise */
  public boolean isSetVersion() {
    return this.version != null;
  }

  public void setVersionIsSet(boolean value) {
    if (!value) {
      this.version = null;
    }
  }

  public int getChannelId() {
    return this.channelId;
  }

  public PurchaseCoinForIosRequest setChannelId(int channelId) {
    this.channelId = channelId;
    setChannelIdIsSet(true);
    return this;
  }

  public void unsetChannelId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CHANNELID_ISSET_ID);
  }

  /** Returns true if field channelId is set (has been assigned a value) and false otherwise */
  public boolean isSetChannelId() {
    return EncodingUtils.testBit(__isset_bitfield, __CHANNELID_ISSET_ID);
  }

  public void setChannelIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CHANNELID_ISSET_ID, value);
  }

  public int getIsSandbox() {
    return this.isSandbox;
  }

  public PurchaseCoinForIosRequest setIsSandbox(int isSandbox) {
    this.isSandbox = isSandbox;
    setIsSandboxIsSet(true);
    return this;
  }

  public void unsetIsSandbox() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ISSANDBOX_ISSET_ID);
  }

  /** Returns true if field isSandbox is set (has been assigned a value) and false otherwise */
  public boolean isSetIsSandbox() {
    return EncodingUtils.testBit(__isset_bitfield, __ISSANDBOX_ISSET_ID);
  }

  public void setIsSandboxIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ISSANDBOX_ISSET_ID, value);
  }

  public String getTradeTime() {
    return this.tradeTime;
  }

  public PurchaseCoinForIosRequest setTradeTime(String tradeTime) {
    this.tradeTime = tradeTime;
    return this;
  }

  public void unsetTradeTime() {
    this.tradeTime = null;
  }

  /** Returns true if field tradeTime is set (has been assigned a value) and false otherwise */
  public boolean isSetTradeTime() {
    return this.tradeTime != null;
  }

  public void setTradeTimeIsSet(boolean value) {
    if (!value) {
      this.tradeTime = null;
    }
  }

  public String getTradeNo() {
    return this.tradeNo;
  }

  public PurchaseCoinForIosRequest setTradeNo(String tradeNo) {
    this.tradeNo = tradeNo;
    return this;
  }

  public void unsetTradeNo() {
    this.tradeNo = null;
  }

  /** Returns true if field tradeNo is set (has been assigned a value) and false otherwise */
  public boolean isSetTradeNo() {
    return this.tradeNo != null;
  }

  public void setTradeNoIsSet(boolean value) {
    if (!value) {
      this.tradeNo = null;
    }
  }

  public String getPartner() {
    return this.partner;
  }

  public PurchaseCoinForIosRequest setPartner(String partner) {
    this.partner = partner;
    return this;
  }

  public void unsetPartner() {
    this.partner = null;
  }

  /** Returns true if field partner is set (has been assigned a value) and false otherwise */
  public boolean isSetPartner() {
    return this.partner != null;
  }

  public void setPartnerIsSet(boolean value) {
    if (!value) {
      this.partner = null;
    }
  }

  public String getBusinessExt() {
    return this.businessExt;
  }

  public PurchaseCoinForIosRequest setBusinessExt(String businessExt) {
    this.businessExt = businessExt;
    return this;
  }

  public void unsetBusinessExt() {
    this.businessExt = null;
  }

  /** Returns true if field businessExt is set (has been assigned a value) and false otherwise */
  public boolean isSetBusinessExt() {
    return this.businessExt != null;
  }

  public void setBusinessExtIsSet(boolean value) {
    if (!value) {
      this.businessExt = null;
    }
  }

  public String getConsumeType() {
    return this.consumeType;
  }

  public PurchaseCoinForIosRequest setConsumeType(String consumeType) {
    this.consumeType = consumeType;
    return this;
  }

  public void unsetConsumeType() {
    this.consumeType = null;
  }

  /** Returns true if field consumeType is set (has been assigned a value) and false otherwise */
  public boolean isSetConsumeType() {
    return this.consumeType != null;
  }

  public void setConsumeTypeIsSet(boolean value) {
    if (!value) {
      this.consumeType = null;
    }
  }

  public String getRoomId() {
    return this.roomId;
  }

  public PurchaseCoinForIosRequest setRoomId(String roomId) {
    this.roomId = roomId;
    return this;
  }

  public void unsetRoomId() {
    this.roomId = null;
  }

  /** Returns true if field roomId is set (has been assigned a value) and false otherwise */
  public boolean isSetRoomId() {
    return this.roomId != null;
  }

  public void setRoomIdIsSet(boolean value) {
    if (!value) {
      this.roomId = null;
    }
  }

  public String getGiftId() {
    return this.giftId;
  }

  public PurchaseCoinForIosRequest setGiftId(String giftId) {
    this.giftId = giftId;
    return this;
  }

  public void unsetGiftId() {
    this.giftId = null;
  }

  /** Returns true if field giftId is set (has been assigned a value) and false otherwise */
  public boolean isSetGiftId() {
    return this.giftId != null;
  }

  public void setGiftIdIsSet(boolean value) {
    if (!value) {
      this.giftId = null;
    }
  }

  public String getGiftNum() {
    return this.giftNum;
  }

  public PurchaseCoinForIosRequest setGiftNum(String giftNum) {
    this.giftNum = giftNum;
    return this;
  }

  public void unsetGiftNum() {
    this.giftNum = null;
  }

  /** Returns true if field giftNum is set (has been assigned a value) and false otherwise */
  public boolean isSetGiftNum() {
    return this.giftNum != null;
  }

  public void setGiftNumIsSet(boolean value) {
    if (!value) {
      this.giftNum = null;
    }
  }

  public String getAppId() {
    return this.appId;
  }

  public PurchaseCoinForIosRequest setAppId(String appId) {
    this.appId = appId;
    return this;
  }

  public void unsetAppId() {
    this.appId = null;
  }

  /** Returns true if field appId is set (has been assigned a value) and false otherwise */
  public boolean isSetAppId() {
    return this.appId != null;
  }

  public void setAppIdIsSet(boolean value) {
    if (!value) {
      this.appId = null;
    }
  }

  public String getBundleId() {
    return this.bundleId;
  }

  public PurchaseCoinForIosRequest setBundleId(String bundleId) {
    this.bundleId = bundleId;
    return this;
  }

  public void unsetBundleId() {
    this.bundleId = null;
  }

  /** Returns true if field bundleId is set (has been assigned a value) and false otherwise */
  public boolean isSetBundleId() {
    return this.bundleId != null;
  }

  public void setBundleIdIsSet(boolean value) {
    if (!value) {
      this.bundleId = null;
    }
  }

  public String getAreaCode() {
    return this.areaCode;
  }

  public PurchaseCoinForIosRequest setAreaCode(String areaCode) {
    this.areaCode = areaCode;
    return this;
  }

  public void unsetAreaCode() {
    this.areaCode = null;
  }

  /** Returns true if field areaCode is set (has been assigned a value) and false otherwise */
  public boolean isSetAreaCode() {
    return this.areaCode != null;
  }

  public void setAreaCodeIsSet(boolean value) {
    if (!value) {
      this.areaCode = null;
    }
  }

  public String getTimeZone() {
    return this.timeZone;
  }

  public PurchaseCoinForIosRequest setTimeZone(String timeZone) {
    this.timeZone = timeZone;
    return this;
  }

  public void unsetTimeZone() {
    this.timeZone = null;
  }

  /** Returns true if field timeZone is set (has been assigned a value) and false otherwise */
  public boolean isSetTimeZone() {
    return this.timeZone != null;
  }

  public void setTimeZoneIsSet(boolean value) {
    if (!value) {
      this.timeZone = null;
    }
  }

  public String getCurrency() {
    return this.currency;
  }

  public PurchaseCoinForIosRequest setCurrency(String currency) {
    this.currency = currency;
    return this;
  }

  public void unsetCurrency() {
    this.currency = null;
  }

  /** Returns true if field currency is set (has been assigned a value) and false otherwise */
  public boolean isSetCurrency() {
    return this.currency != null;
  }

  public void setCurrencyIsSet(boolean value) {
    if (!value) {
      this.currency = null;
    }
  }

  public String getCurrencyAmount() {
    return this.currencyAmount;
  }

  public PurchaseCoinForIosRequest setCurrencyAmount(String currencyAmount) {
    this.currencyAmount = currencyAmount;
    return this;
  }

  public void unsetCurrencyAmount() {
    this.currencyAmount = null;
  }

  /** Returns true if field currencyAmount is set (has been assigned a value) and false otherwise */
  public boolean isSetCurrencyAmount() {
    return this.currencyAmount != null;
  }

  public void setCurrencyAmountIsSet(boolean value) {
    if (!value) {
      this.currencyAmount = null;
    }
  }

  public String getUsdAmount() {
    return this.usdAmount;
  }

  public PurchaseCoinForIosRequest setUsdAmount(String usdAmount) {
    this.usdAmount = usdAmount;
    return this;
  }

  public void unsetUsdAmount() {
    this.usdAmount = null;
  }

  /** Returns true if field usdAmount is set (has been assigned a value) and false otherwise */
  public boolean isSetUsdAmount() {
    return this.usdAmount != null;
  }

  public void setUsdAmountIsSet(boolean value) {
    if (!value) {
      this.usdAmount = null;
    }
  }

  public String getClientIp() {
    return this.clientIp;
  }

  public PurchaseCoinForIosRequest setClientIp(String clientIp) {
    this.clientIp = clientIp;
    return this;
  }

  public void unsetClientIp() {
    this.clientIp = null;
  }

  /** Returns true if field clientIp is set (has been assigned a value) and false otherwise */
  public boolean isSetClientIp() {
    return this.clientIp != null;
  }

  public void setClientIpIsSet(boolean value) {
    if (!value) {
      this.clientIp = null;
    }
  }

  public int getAreaId() {
    return this.areaId;
  }

  public PurchaseCoinForIosRequest setAreaId(int areaId) {
    this.areaId = areaId;
    setAreaIdIsSet(true);
    return this;
  }

  public void unsetAreaId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __AREAID_ISSET_ID);
  }

  /** Returns true if field areaId is set (has been assigned a value) and false otherwise */
  public boolean isSetAreaId() {
    return EncodingUtils.testBit(__isset_bitfield, __AREAID_ISSET_ID);
  }

  public void setAreaIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __AREAID_ISSET_ID, value);
  }

  public String getOutTradeNo() {
    return this.outTradeNo;
  }

  public PurchaseCoinForIosRequest setOutTradeNo(String outTradeNo) {
    this.outTradeNo = outTradeNo;
    return this;
  }

  public void unsetOutTradeNo() {
    this.outTradeNo = null;
  }

  /** Returns true if field outTradeNo is set (has been assigned a value) and false otherwise */
  public boolean isSetOutTradeNo() {
    return this.outTradeNo != null;
  }

  public void setOutTradeNoIsSet(boolean value) {
    if (!value) {
      this.outTradeNo = null;
    }
  }

  public int getProductType() {
    return this.productType;
  }

  public PurchaseCoinForIosRequest setProductType(int productType) {
    this.productType = productType;
    setProductTypeIsSet(true);
    return this;
  }

  public void unsetProductType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PRODUCTTYPE_ISSET_ID);
  }

  /** Returns true if field productType is set (has been assigned a value) and false otherwise */
  public boolean isSetProductType() {
    return EncodingUtils.testBit(__isset_bitfield, __PRODUCTTYPE_ISSET_ID);
  }

  public void setProductTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PRODUCTTYPE_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ORDER_NUM:
      if (value == null) {
        unsetOrderNum();
      } else {
        setOrderNum((String)value);
      }
      break;

    case KEY:
      if (value == null) {
        unsetKey();
      } else {
        setKey((String)value);
      }
      break;

    case KUGOU_ID:
      if (value == null) {
        unsetKugouId();
      } else {
        setKugouId((Long)value);
      }
      break;

    case TIME:
      if (value == null) {
        unsetTime();
      } else {
        setTime((Integer)value);
      }
      break;

    case COIN:
      if (value == null) {
        unsetCoin();
      } else {
        setCoin((String)value);
      }
      break;

    case MONEY:
      if (value == null) {
        unsetMoney();
      } else {
        setMoney((String)value);
      }
      break;

    case SIGN_BOOK:
      if (value == null) {
        unsetSignBook();
      } else {
        setSignBook((String)value);
      }
      break;

    case TYPE:
      if (value == null) {
        unsetType();
      } else {
        setType((Integer)value);
      }
      break;

    case GOODS_ID:
      if (value == null) {
        unsetGoodsId();
      } else {
        setGoodsId((String)value);
      }
      break;

    case PID:
      if (value == null) {
        unsetPid();
      } else {
        setPid((Integer)value);
      }
      break;

    case VERSION:
      if (value == null) {
        unsetVersion();
      } else {
        setVersion((String)value);
      }
      break;

    case CHANNEL_ID:
      if (value == null) {
        unsetChannelId();
      } else {
        setChannelId((Integer)value);
      }
      break;

    case IS_SANDBOX:
      if (value == null) {
        unsetIsSandbox();
      } else {
        setIsSandbox((Integer)value);
      }
      break;

    case TRADE_TIME:
      if (value == null) {
        unsetTradeTime();
      } else {
        setTradeTime((String)value);
      }
      break;

    case TRADE_NO:
      if (value == null) {
        unsetTradeNo();
      } else {
        setTradeNo((String)value);
      }
      break;

    case PARTNER:
      if (value == null) {
        unsetPartner();
      } else {
        setPartner((String)value);
      }
      break;

    case BUSINESS_EXT:
      if (value == null) {
        unsetBusinessExt();
      } else {
        setBusinessExt((String)value);
      }
      break;

    case CONSUME_TYPE:
      if (value == null) {
        unsetConsumeType();
      } else {
        setConsumeType((String)value);
      }
      break;

    case ROOM_ID:
      if (value == null) {
        unsetRoomId();
      } else {
        setRoomId((String)value);
      }
      break;

    case GIFT_ID:
      if (value == null) {
        unsetGiftId();
      } else {
        setGiftId((String)value);
      }
      break;

    case GIFT_NUM:
      if (value == null) {
        unsetGiftNum();
      } else {
        setGiftNum((String)value);
      }
      break;

    case APP_ID:
      if (value == null) {
        unsetAppId();
      } else {
        setAppId((String)value);
      }
      break;

    case BUNDLE_ID:
      if (value == null) {
        unsetBundleId();
      } else {
        setBundleId((String)value);
      }
      break;

    case AREA_CODE:
      if (value == null) {
        unsetAreaCode();
      } else {
        setAreaCode((String)value);
      }
      break;

    case TIME_ZONE:
      if (value == null) {
        unsetTimeZone();
      } else {
        setTimeZone((String)value);
      }
      break;

    case CURRENCY:
      if (value == null) {
        unsetCurrency();
      } else {
        setCurrency((String)value);
      }
      break;

    case CURRENCY_AMOUNT:
      if (value == null) {
        unsetCurrencyAmount();
      } else {
        setCurrencyAmount((String)value);
      }
      break;

    case USD_AMOUNT:
      if (value == null) {
        unsetUsdAmount();
      } else {
        setUsdAmount((String)value);
      }
      break;

    case CLIENT_IP:
      if (value == null) {
        unsetClientIp();
      } else {
        setClientIp((String)value);
      }
      break;

    case AREA_ID:
      if (value == null) {
        unsetAreaId();
      } else {
        setAreaId((Integer)value);
      }
      break;

    case OUT_TRADE_NO:
      if (value == null) {
        unsetOutTradeNo();
      } else {
        setOutTradeNo((String)value);
      }
      break;

    case PRODUCT_TYPE:
      if (value == null) {
        unsetProductType();
      } else {
        setProductType((Integer)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ORDER_NUM:
      return getOrderNum();

    case KEY:
      return getKey();

    case KUGOU_ID:
      return getKugouId();

    case TIME:
      return getTime();

    case COIN:
      return getCoin();

    case MONEY:
      return getMoney();

    case SIGN_BOOK:
      return getSignBook();

    case TYPE:
      return getType();

    case GOODS_ID:
      return getGoodsId();

    case PID:
      return getPid();

    case VERSION:
      return getVersion();

    case CHANNEL_ID:
      return getChannelId();

    case IS_SANDBOX:
      return getIsSandbox();

    case TRADE_TIME:
      return getTradeTime();

    case TRADE_NO:
      return getTradeNo();

    case PARTNER:
      return getPartner();

    case BUSINESS_EXT:
      return getBusinessExt();

    case CONSUME_TYPE:
      return getConsumeType();

    case ROOM_ID:
      return getRoomId();

    case GIFT_ID:
      return getGiftId();

    case GIFT_NUM:
      return getGiftNum();

    case APP_ID:
      return getAppId();

    case BUNDLE_ID:
      return getBundleId();

    case AREA_CODE:
      return getAreaCode();

    case TIME_ZONE:
      return getTimeZone();

    case CURRENCY:
      return getCurrency();

    case CURRENCY_AMOUNT:
      return getCurrencyAmount();

    case USD_AMOUNT:
      return getUsdAmount();

    case CLIENT_IP:
      return getClientIp();

    case AREA_ID:
      return getAreaId();

    case OUT_TRADE_NO:
      return getOutTradeNo();

    case PRODUCT_TYPE:
      return getProductType();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ORDER_NUM:
      return isSetOrderNum();
    case KEY:
      return isSetKey();
    case KUGOU_ID:
      return isSetKugouId();
    case TIME:
      return isSetTime();
    case COIN:
      return isSetCoin();
    case MONEY:
      return isSetMoney();
    case SIGN_BOOK:
      return isSetSignBook();
    case TYPE:
      return isSetType();
    case GOODS_ID:
      return isSetGoodsId();
    case PID:
      return isSetPid();
    case VERSION:
      return isSetVersion();
    case CHANNEL_ID:
      return isSetChannelId();
    case IS_SANDBOX:
      return isSetIsSandbox();
    case TRADE_TIME:
      return isSetTradeTime();
    case TRADE_NO:
      return isSetTradeNo();
    case PARTNER:
      return isSetPartner();
    case BUSINESS_EXT:
      return isSetBusinessExt();
    case CONSUME_TYPE:
      return isSetConsumeType();
    case ROOM_ID:
      return isSetRoomId();
    case GIFT_ID:
      return isSetGiftId();
    case GIFT_NUM:
      return isSetGiftNum();
    case APP_ID:
      return isSetAppId();
    case BUNDLE_ID:
      return isSetBundleId();
    case AREA_CODE:
      return isSetAreaCode();
    case TIME_ZONE:
      return isSetTimeZone();
    case CURRENCY:
      return isSetCurrency();
    case CURRENCY_AMOUNT:
      return isSetCurrencyAmount();
    case USD_AMOUNT:
      return isSetUsdAmount();
    case CLIENT_IP:
      return isSetClientIp();
    case AREA_ID:
      return isSetAreaId();
    case OUT_TRADE_NO:
      return isSetOutTradeNo();
    case PRODUCT_TYPE:
      return isSetProductType();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof PurchaseCoinForIosRequest)
      return this.equals((PurchaseCoinForIosRequest)that);
    return false;
  }

  public boolean equals(PurchaseCoinForIosRequest that) {
    if (that == null)
      return false;

    boolean this_present_orderNum = true && this.isSetOrderNum();
    boolean that_present_orderNum = true && that.isSetOrderNum();
    if (this_present_orderNum || that_present_orderNum) {
      if (!(this_present_orderNum && that_present_orderNum))
        return false;
      if (!this.orderNum.equals(that.orderNum))
        return false;
    }

    boolean this_present_key = true && this.isSetKey();
    boolean that_present_key = true && that.isSetKey();
    if (this_present_key || that_present_key) {
      if (!(this_present_key && that_present_key))
        return false;
      if (!this.key.equals(that.key))
        return false;
    }

    boolean this_present_kugouId = true;
    boolean that_present_kugouId = true;
    if (this_present_kugouId || that_present_kugouId) {
      if (!(this_present_kugouId && that_present_kugouId))
        return false;
      if (this.kugouId != that.kugouId)
        return false;
    }

    boolean this_present_time = true;
    boolean that_present_time = true;
    if (this_present_time || that_present_time) {
      if (!(this_present_time && that_present_time))
        return false;
      if (this.time != that.time)
        return false;
    }

    boolean this_present_coin = true && this.isSetCoin();
    boolean that_present_coin = true && that.isSetCoin();
    if (this_present_coin || that_present_coin) {
      if (!(this_present_coin && that_present_coin))
        return false;
      if (!this.coin.equals(that.coin))
        return false;
    }

    boolean this_present_money = true && this.isSetMoney();
    boolean that_present_money = true && that.isSetMoney();
    if (this_present_money || that_present_money) {
      if (!(this_present_money && that_present_money))
        return false;
      if (!this.money.equals(that.money))
        return false;
    }

    boolean this_present_signBook = true && this.isSetSignBook();
    boolean that_present_signBook = true && that.isSetSignBook();
    if (this_present_signBook || that_present_signBook) {
      if (!(this_present_signBook && that_present_signBook))
        return false;
      if (!this.signBook.equals(that.signBook))
        return false;
    }

    boolean this_present_type = true;
    boolean that_present_type = true;
    if (this_present_type || that_present_type) {
      if (!(this_present_type && that_present_type))
        return false;
      if (this.type != that.type)
        return false;
    }

    boolean this_present_goodsId = true && this.isSetGoodsId();
    boolean that_present_goodsId = true && that.isSetGoodsId();
    if (this_present_goodsId || that_present_goodsId) {
      if (!(this_present_goodsId && that_present_goodsId))
        return false;
      if (!this.goodsId.equals(that.goodsId))
        return false;
    }

    boolean this_present_pid = true;
    boolean that_present_pid = true;
    if (this_present_pid || that_present_pid) {
      if (!(this_present_pid && that_present_pid))
        return false;
      if (this.pid != that.pid)
        return false;
    }

    boolean this_present_version = true && this.isSetVersion();
    boolean that_present_version = true && that.isSetVersion();
    if (this_present_version || that_present_version) {
      if (!(this_present_version && that_present_version))
        return false;
      if (!this.version.equals(that.version))
        return false;
    }

    boolean this_present_channelId = true;
    boolean that_present_channelId = true;
    if (this_present_channelId || that_present_channelId) {
      if (!(this_present_channelId && that_present_channelId))
        return false;
      if (this.channelId != that.channelId)
        return false;
    }

    boolean this_present_isSandbox = true;
    boolean that_present_isSandbox = true;
    if (this_present_isSandbox || that_present_isSandbox) {
      if (!(this_present_isSandbox && that_present_isSandbox))
        return false;
      if (this.isSandbox != that.isSandbox)
        return false;
    }

    boolean this_present_tradeTime = true && this.isSetTradeTime();
    boolean that_present_tradeTime = true && that.isSetTradeTime();
    if (this_present_tradeTime || that_present_tradeTime) {
      if (!(this_present_tradeTime && that_present_tradeTime))
        return false;
      if (!this.tradeTime.equals(that.tradeTime))
        return false;
    }

    boolean this_present_tradeNo = true && this.isSetTradeNo();
    boolean that_present_tradeNo = true && that.isSetTradeNo();
    if (this_present_tradeNo || that_present_tradeNo) {
      if (!(this_present_tradeNo && that_present_tradeNo))
        return false;
      if (!this.tradeNo.equals(that.tradeNo))
        return false;
    }

    boolean this_present_partner = true && this.isSetPartner();
    boolean that_present_partner = true && that.isSetPartner();
    if (this_present_partner || that_present_partner) {
      if (!(this_present_partner && that_present_partner))
        return false;
      if (!this.partner.equals(that.partner))
        return false;
    }

    boolean this_present_businessExt = true && this.isSetBusinessExt();
    boolean that_present_businessExt = true && that.isSetBusinessExt();
    if (this_present_businessExt || that_present_businessExt) {
      if (!(this_present_businessExt && that_present_businessExt))
        return false;
      if (!this.businessExt.equals(that.businessExt))
        return false;
    }

    boolean this_present_consumeType = true && this.isSetConsumeType();
    boolean that_present_consumeType = true && that.isSetConsumeType();
    if (this_present_consumeType || that_present_consumeType) {
      if (!(this_present_consumeType && that_present_consumeType))
        return false;
      if (!this.consumeType.equals(that.consumeType))
        return false;
    }

    boolean this_present_roomId = true && this.isSetRoomId();
    boolean that_present_roomId = true && that.isSetRoomId();
    if (this_present_roomId || that_present_roomId) {
      if (!(this_present_roomId && that_present_roomId))
        return false;
      if (!this.roomId.equals(that.roomId))
        return false;
    }

    boolean this_present_giftId = true && this.isSetGiftId();
    boolean that_present_giftId = true && that.isSetGiftId();
    if (this_present_giftId || that_present_giftId) {
      if (!(this_present_giftId && that_present_giftId))
        return false;
      if (!this.giftId.equals(that.giftId))
        return false;
    }

    boolean this_present_giftNum = true && this.isSetGiftNum();
    boolean that_present_giftNum = true && that.isSetGiftNum();
    if (this_present_giftNum || that_present_giftNum) {
      if (!(this_present_giftNum && that_present_giftNum))
        return false;
      if (!this.giftNum.equals(that.giftNum))
        return false;
    }

    boolean this_present_appId = true && this.isSetAppId();
    boolean that_present_appId = true && that.isSetAppId();
    if (this_present_appId || that_present_appId) {
      if (!(this_present_appId && that_present_appId))
        return false;
      if (!this.appId.equals(that.appId))
        return false;
    }

    boolean this_present_bundleId = true && this.isSetBundleId();
    boolean that_present_bundleId = true && that.isSetBundleId();
    if (this_present_bundleId || that_present_bundleId) {
      if (!(this_present_bundleId && that_present_bundleId))
        return false;
      if (!this.bundleId.equals(that.bundleId))
        return false;
    }

    boolean this_present_areaCode = true && this.isSetAreaCode();
    boolean that_present_areaCode = true && that.isSetAreaCode();
    if (this_present_areaCode || that_present_areaCode) {
      if (!(this_present_areaCode && that_present_areaCode))
        return false;
      if (!this.areaCode.equals(that.areaCode))
        return false;
    }

    boolean this_present_timeZone = true && this.isSetTimeZone();
    boolean that_present_timeZone = true && that.isSetTimeZone();
    if (this_present_timeZone || that_present_timeZone) {
      if (!(this_present_timeZone && that_present_timeZone))
        return false;
      if (!this.timeZone.equals(that.timeZone))
        return false;
    }

    boolean this_present_currency = true && this.isSetCurrency();
    boolean that_present_currency = true && that.isSetCurrency();
    if (this_present_currency || that_present_currency) {
      if (!(this_present_currency && that_present_currency))
        return false;
      if (!this.currency.equals(that.currency))
        return false;
    }

    boolean this_present_currencyAmount = true && this.isSetCurrencyAmount();
    boolean that_present_currencyAmount = true && that.isSetCurrencyAmount();
    if (this_present_currencyAmount || that_present_currencyAmount) {
      if (!(this_present_currencyAmount && that_present_currencyAmount))
        return false;
      if (!this.currencyAmount.equals(that.currencyAmount))
        return false;
    }

    boolean this_present_usdAmount = true && this.isSetUsdAmount();
    boolean that_present_usdAmount = true && that.isSetUsdAmount();
    if (this_present_usdAmount || that_present_usdAmount) {
      if (!(this_present_usdAmount && that_present_usdAmount))
        return false;
      if (!this.usdAmount.equals(that.usdAmount))
        return false;
    }

    boolean this_present_clientIp = true && this.isSetClientIp();
    boolean that_present_clientIp = true && that.isSetClientIp();
    if (this_present_clientIp || that_present_clientIp) {
      if (!(this_present_clientIp && that_present_clientIp))
        return false;
      if (!this.clientIp.equals(that.clientIp))
        return false;
    }

    boolean this_present_areaId = true && this.isSetAreaId();
    boolean that_present_areaId = true && that.isSetAreaId();
    if (this_present_areaId || that_present_areaId) {
      if (!(this_present_areaId && that_present_areaId))
        return false;
      if (this.areaId != that.areaId)
        return false;
    }

    boolean this_present_outTradeNo = true && this.isSetOutTradeNo();
    boolean that_present_outTradeNo = true && that.isSetOutTradeNo();
    if (this_present_outTradeNo || that_present_outTradeNo) {
      if (!(this_present_outTradeNo && that_present_outTradeNo))
        return false;
      if (!this.outTradeNo.equals(that.outTradeNo))
        return false;
    }

    boolean this_present_productType = true && this.isSetProductType();
    boolean that_present_productType = true && that.isSetProductType();
    if (this_present_productType || that_present_productType) {
      if (!(this_present_productType && that_present_productType))
        return false;
      if (this.productType != that.productType)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_orderNum = true && (isSetOrderNum());
    list.add(present_orderNum);
    if (present_orderNum)
      list.add(orderNum);

    boolean present_key = true && (isSetKey());
    list.add(present_key);
    if (present_key)
      list.add(key);

    boolean present_kugouId = true;
    list.add(present_kugouId);
    if (present_kugouId)
      list.add(kugouId);

    boolean present_time = true;
    list.add(present_time);
    if (present_time)
      list.add(time);

    boolean present_coin = true && (isSetCoin());
    list.add(present_coin);
    if (present_coin)
      list.add(coin);

    boolean present_money = true && (isSetMoney());
    list.add(present_money);
    if (present_money)
      list.add(money);

    boolean present_signBook = true && (isSetSignBook());
    list.add(present_signBook);
    if (present_signBook)
      list.add(signBook);

    boolean present_type = true;
    list.add(present_type);
    if (present_type)
      list.add(type);

    boolean present_goodsId = true && (isSetGoodsId());
    list.add(present_goodsId);
    if (present_goodsId)
      list.add(goodsId);

    boolean present_pid = true;
    list.add(present_pid);
    if (present_pid)
      list.add(pid);

    boolean present_version = true && (isSetVersion());
    list.add(present_version);
    if (present_version)
      list.add(version);

    boolean present_channelId = true;
    list.add(present_channelId);
    if (present_channelId)
      list.add(channelId);

    boolean present_isSandbox = true;
    list.add(present_isSandbox);
    if (present_isSandbox)
      list.add(isSandbox);

    boolean present_tradeTime = true && (isSetTradeTime());
    list.add(present_tradeTime);
    if (present_tradeTime)
      list.add(tradeTime);

    boolean present_tradeNo = true && (isSetTradeNo());
    list.add(present_tradeNo);
    if (present_tradeNo)
      list.add(tradeNo);

    boolean present_partner = true && (isSetPartner());
    list.add(present_partner);
    if (present_partner)
      list.add(partner);

    boolean present_businessExt = true && (isSetBusinessExt());
    list.add(present_businessExt);
    if (present_businessExt)
      list.add(businessExt);

    boolean present_consumeType = true && (isSetConsumeType());
    list.add(present_consumeType);
    if (present_consumeType)
      list.add(consumeType);

    boolean present_roomId = true && (isSetRoomId());
    list.add(present_roomId);
    if (present_roomId)
      list.add(roomId);

    boolean present_giftId = true && (isSetGiftId());
    list.add(present_giftId);
    if (present_giftId)
      list.add(giftId);

    boolean present_giftNum = true && (isSetGiftNum());
    list.add(present_giftNum);
    if (present_giftNum)
      list.add(giftNum);

    boolean present_appId = true && (isSetAppId());
    list.add(present_appId);
    if (present_appId)
      list.add(appId);

    boolean present_bundleId = true && (isSetBundleId());
    list.add(present_bundleId);
    if (present_bundleId)
      list.add(bundleId);

    boolean present_areaCode = true && (isSetAreaCode());
    list.add(present_areaCode);
    if (present_areaCode)
      list.add(areaCode);

    boolean present_timeZone = true && (isSetTimeZone());
    list.add(present_timeZone);
    if (present_timeZone)
      list.add(timeZone);

    boolean present_currency = true && (isSetCurrency());
    list.add(present_currency);
    if (present_currency)
      list.add(currency);

    boolean present_currencyAmount = true && (isSetCurrencyAmount());
    list.add(present_currencyAmount);
    if (present_currencyAmount)
      list.add(currencyAmount);

    boolean present_usdAmount = true && (isSetUsdAmount());
    list.add(present_usdAmount);
    if (present_usdAmount)
      list.add(usdAmount);

    boolean present_clientIp = true && (isSetClientIp());
    list.add(present_clientIp);
    if (present_clientIp)
      list.add(clientIp);

    boolean present_areaId = true && (isSetAreaId());
    list.add(present_areaId);
    if (present_areaId)
      list.add(areaId);

    boolean present_outTradeNo = true && (isSetOutTradeNo());
    list.add(present_outTradeNo);
    if (present_outTradeNo)
      list.add(outTradeNo);

    boolean present_productType = true && (isSetProductType());
    list.add(present_productType);
    if (present_productType)
      list.add(productType);

    return list.hashCode();
  }

  @Override
  public int compareTo(PurchaseCoinForIosRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetOrderNum()).compareTo(other.isSetOrderNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOrderNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderNum, other.orderNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetKey()).compareTo(other.isSetKey());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKey()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.key, other.key);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKugouId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTime()).compareTo(other.isSetTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.time, other.time);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCoin()).compareTo(other.isSetCoin());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCoin()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.coin, other.coin);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMoney()).compareTo(other.isSetMoney());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMoney()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.money, other.money);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSignBook()).compareTo(other.isSetSignBook());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSignBook()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.signBook, other.signBook);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetType()).compareTo(other.isSetType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.type, other.type);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGoodsId()).compareTo(other.isSetGoodsId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGoodsId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.goodsId, other.goodsId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPid()).compareTo(other.isSetPid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pid, other.pid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetVersion()).compareTo(other.isSetVersion());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetVersion()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.version, other.version);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetChannelId()).compareTo(other.isSetChannelId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetChannelId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.channelId, other.channelId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIsSandbox()).compareTo(other.isSetIsSandbox());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsSandbox()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isSandbox, other.isSandbox);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTradeTime()).compareTo(other.isSetTradeTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTradeTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.tradeTime, other.tradeTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTradeNo()).compareTo(other.isSetTradeNo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTradeNo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.tradeNo, other.tradeNo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPartner()).compareTo(other.isSetPartner());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPartner()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.partner, other.partner);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBusinessExt()).compareTo(other.isSetBusinessExt());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBusinessExt()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.businessExt, other.businessExt);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetConsumeType()).compareTo(other.isSetConsumeType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetConsumeType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.consumeType, other.consumeType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoomId()).compareTo(other.isSetRoomId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoomId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roomId, other.roomId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGiftId()).compareTo(other.isSetGiftId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGiftId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.giftId, other.giftId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGiftNum()).compareTo(other.isSetGiftNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGiftNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.giftNum, other.giftNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAppId()).compareTo(other.isSetAppId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appId, other.appId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBundleId()).compareTo(other.isSetBundleId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBundleId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.bundleId, other.bundleId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAreaCode()).compareTo(other.isSetAreaCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAreaCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.areaCode, other.areaCode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTimeZone()).compareTo(other.isSetTimeZone());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTimeZone()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.timeZone, other.timeZone);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCurrency()).compareTo(other.isSetCurrency());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurrency()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.currency, other.currency);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCurrencyAmount()).compareTo(other.isSetCurrencyAmount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurrencyAmount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.currencyAmount, other.currencyAmount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUsdAmount()).compareTo(other.isSetUsdAmount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUsdAmount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.usdAmount, other.usdAmount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetClientIp()).compareTo(other.isSetClientIp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetClientIp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.clientIp, other.clientIp);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAreaId()).compareTo(other.isSetAreaId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAreaId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.areaId, other.areaId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetOutTradeNo()).compareTo(other.isSetOutTradeNo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOutTradeNo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.outTradeNo, other.outTradeNo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetProductType()).compareTo(other.isSetProductType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetProductType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.productType, other.productType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("PurchaseCoinForIosRequest(");
    boolean first = true;

    sb.append("orderNum:");
    if (this.orderNum == null) {
      sb.append("null");
    } else {
      sb.append(this.orderNum);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("key:");
    if (this.key == null) {
      sb.append("null");
    } else {
      sb.append(this.key);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("kugouId:");
    sb.append(this.kugouId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("time:");
    sb.append(this.time);
    first = false;
    if (!first) sb.append(", ");
    sb.append("coin:");
    if (this.coin == null) {
      sb.append("null");
    } else {
      sb.append(this.coin);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("money:");
    if (this.money == null) {
      sb.append("null");
    } else {
      sb.append(this.money);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("signBook:");
    if (this.signBook == null) {
      sb.append("null");
    } else {
      sb.append(this.signBook);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("type:");
    sb.append(this.type);
    first = false;
    if (!first) sb.append(", ");
    sb.append("goodsId:");
    if (this.goodsId == null) {
      sb.append("null");
    } else {
      sb.append(this.goodsId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("pid:");
    sb.append(this.pid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("version:");
    if (this.version == null) {
      sb.append("null");
    } else {
      sb.append(this.version);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("channelId:");
    sb.append(this.channelId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("isSandbox:");
    sb.append(this.isSandbox);
    first = false;
    if (!first) sb.append(", ");
    sb.append("tradeTime:");
    if (this.tradeTime == null) {
      sb.append("null");
    } else {
      sb.append(this.tradeTime);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("tradeNo:");
    if (this.tradeNo == null) {
      sb.append("null");
    } else {
      sb.append(this.tradeNo);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("partner:");
    if (this.partner == null) {
      sb.append("null");
    } else {
      sb.append(this.partner);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("businessExt:");
    if (this.businessExt == null) {
      sb.append("null");
    } else {
      sb.append(this.businessExt);
    }
    first = false;
    if (isSetConsumeType()) {
      if (!first) sb.append(", ");
      sb.append("consumeType:");
      if (this.consumeType == null) {
        sb.append("null");
      } else {
        sb.append(this.consumeType);
      }
      first = false;
    }
    if (isSetRoomId()) {
      if (!first) sb.append(", ");
      sb.append("roomId:");
      if (this.roomId == null) {
        sb.append("null");
      } else {
        sb.append(this.roomId);
      }
      first = false;
    }
    if (isSetGiftId()) {
      if (!first) sb.append(", ");
      sb.append("giftId:");
      if (this.giftId == null) {
        sb.append("null");
      } else {
        sb.append(this.giftId);
      }
      first = false;
    }
    if (isSetGiftNum()) {
      if (!first) sb.append(", ");
      sb.append("giftNum:");
      if (this.giftNum == null) {
        sb.append("null");
      } else {
        sb.append(this.giftNum);
      }
      first = false;
    }
    if (isSetAppId()) {
      if (!first) sb.append(", ");
      sb.append("appId:");
      if (this.appId == null) {
        sb.append("null");
      } else {
        sb.append(this.appId);
      }
      first = false;
    }
    if (isSetBundleId()) {
      if (!first) sb.append(", ");
      sb.append("bundleId:");
      if (this.bundleId == null) {
        sb.append("null");
      } else {
        sb.append(this.bundleId);
      }
      first = false;
    }
    if (isSetAreaCode()) {
      if (!first) sb.append(", ");
      sb.append("areaCode:");
      if (this.areaCode == null) {
        sb.append("null");
      } else {
        sb.append(this.areaCode);
      }
      first = false;
    }
    if (isSetTimeZone()) {
      if (!first) sb.append(", ");
      sb.append("timeZone:");
      if (this.timeZone == null) {
        sb.append("null");
      } else {
        sb.append(this.timeZone);
      }
      first = false;
    }
    if (isSetCurrency()) {
      if (!first) sb.append(", ");
      sb.append("currency:");
      if (this.currency == null) {
        sb.append("null");
      } else {
        sb.append(this.currency);
      }
      first = false;
    }
    if (isSetCurrencyAmount()) {
      if (!first) sb.append(", ");
      sb.append("currencyAmount:");
      if (this.currencyAmount == null) {
        sb.append("null");
      } else {
        sb.append(this.currencyAmount);
      }
      first = false;
    }
    if (isSetUsdAmount()) {
      if (!first) sb.append(", ");
      sb.append("usdAmount:");
      if (this.usdAmount == null) {
        sb.append("null");
      } else {
        sb.append(this.usdAmount);
      }
      first = false;
    }
    if (isSetClientIp()) {
      if (!first) sb.append(", ");
      sb.append("clientIp:");
      if (this.clientIp == null) {
        sb.append("null");
      } else {
        sb.append(this.clientIp);
      }
      first = false;
    }
    if (isSetAreaId()) {
      if (!first) sb.append(", ");
      sb.append("areaId:");
      sb.append(this.areaId);
      first = false;
    }
    if (isSetOutTradeNo()) {
      if (!first) sb.append(", ");
      sb.append("outTradeNo:");
      if (this.outTradeNo == null) {
        sb.append("null");
      } else {
        sb.append(this.outTradeNo);
      }
      first = false;
    }
    if (isSetProductType()) {
      if (!first) sb.append(", ");
      sb.append("productType:");
      sb.append(this.productType);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws TException {
    // check for required fields
    if (orderNum == null) {
      throw new TProtocolException("Required field 'orderNum' was not present! Struct: " + toString());
    }
    if (key == null) {
      throw new TProtocolException("Required field 'key' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'time' because it's a primitive and you chose the non-beans generator.
    if (coin == null) {
      throw new TProtocolException("Required field 'coin' was not present! Struct: " + toString());
    }
    if (money == null) {
      throw new TProtocolException("Required field 'money' was not present! Struct: " + toString());
    }
    if (signBook == null) {
      throw new TProtocolException("Required field 'signBook' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'type' because it's a primitive and you chose the non-beans generator.
    if (goodsId == null) {
      throw new TProtocolException("Required field 'goodsId' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'pid' because it's a primitive and you chose the non-beans generator.
    if (version == null) {
      throw new TProtocolException("Required field 'version' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'channelId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'isSandbox' because it's a primitive and you chose the non-beans generator.
    if (tradeTime == null) {
      throw new TProtocolException("Required field 'tradeTime' was not present! Struct: " + toString());
    }
    if (tradeNo == null) {
      throw new TProtocolException("Required field 'tradeNo' was not present! Struct: " + toString());
    }
    if (partner == null) {
      throw new TProtocolException("Required field 'partner' was not present! Struct: " + toString());
    }
    if (businessExt == null) {
      throw new TProtocolException("Required field 'businessExt' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PurchaseCoinForIosRequestStandardSchemeFactory implements SchemeFactory {
    public PurchaseCoinForIosRequestStandardScheme getScheme() {
      return new PurchaseCoinForIosRequestStandardScheme();
    }
  }

  private static class PurchaseCoinForIosRequestStandardScheme extends StandardScheme<PurchaseCoinForIosRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PurchaseCoinForIosRequest struct) throws TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ORDER_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.orderNum = iprot.readString();
              struct.setOrderNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // KEY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.key = iprot.readString();
              struct.setKeyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // KUGOU_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.kugouId = iprot.readI64();
              struct.setKugouIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.time = iprot.readI32();
              struct.setTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // COIN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.coin = iprot.readString();
              struct.setCoinIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // MONEY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.money = iprot.readString();
              struct.setMoneyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // SIGN_BOOK
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.signBook = iprot.readString();
              struct.setSignBookIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.type = iprot.readI32();
              struct.setTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // GOODS_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.goodsId = iprot.readString();
              struct.setGoodsIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // PID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.pid = iprot.readI32();
              struct.setPidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // VERSION
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.version = iprot.readString();
              struct.setVersionIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // CHANNEL_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.channelId = iprot.readI32();
              struct.setChannelIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // IS_SANDBOX
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.isSandbox = iprot.readI32();
              struct.setIsSandboxIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 14: // TRADE_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.tradeTime = iprot.readString();
              struct.setTradeTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // TRADE_NO
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.tradeNo = iprot.readString();
              struct.setTradeNoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 16: // PARTNER
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.partner = iprot.readString();
              struct.setPartnerIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 17: // BUSINESS_EXT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.businessExt = iprot.readString();
              struct.setBusinessExtIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 18: // CONSUME_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.consumeType = iprot.readString();
              struct.setConsumeTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 19: // ROOM_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.roomId = iprot.readString();
              struct.setRoomIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 20: // GIFT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.giftId = iprot.readString();
              struct.setGiftIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 21: // GIFT_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.giftNum = iprot.readString();
              struct.setGiftNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 22: // APP_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.appId = iprot.readString();
              struct.setAppIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 23: // BUNDLE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.bundleId = iprot.readString();
              struct.setBundleIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 24: // AREA_CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.areaCode = iprot.readString();
              struct.setAreaCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 25: // TIME_ZONE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.timeZone = iprot.readString();
              struct.setTimeZoneIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 26: // CURRENCY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.currency = iprot.readString();
              struct.setCurrencyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 27: // CURRENCY_AMOUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.currencyAmount = iprot.readString();
              struct.setCurrencyAmountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 28: // USD_AMOUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.usdAmount = iprot.readString();
              struct.setUsdAmountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 29: // CLIENT_IP
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.clientIp = iprot.readString();
              struct.setClientIpIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 30: // AREA_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.areaId = iprot.readI32();
              struct.setAreaIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 31: // OUT_TRADE_NO
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.outTradeNo = iprot.readString();
              struct.setOutTradeNoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 32: // PRODUCT_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.productType = iprot.readI32();
              struct.setProductTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetKugouId()) {
        throw new TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetTime()) {
        throw new TProtocolException("Required field 'time' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetType()) {
        throw new TProtocolException("Required field 'type' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetPid()) {
        throw new TProtocolException("Required field 'pid' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetChannelId()) {
        throw new TProtocolException("Required field 'channelId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetIsSandbox()) {
        throw new TProtocolException("Required field 'isSandbox' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PurchaseCoinForIosRequest struct) throws TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.orderNum != null) {
        oprot.writeFieldBegin(ORDER_NUM_FIELD_DESC);
        oprot.writeString(struct.orderNum);
        oprot.writeFieldEnd();
      }
      if (struct.key != null) {
        oprot.writeFieldBegin(KEY_FIELD_DESC);
        oprot.writeString(struct.key);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
      oprot.writeI64(struct.kugouId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TIME_FIELD_DESC);
      oprot.writeI32(struct.time);
      oprot.writeFieldEnd();
      if (struct.coin != null) {
        oprot.writeFieldBegin(COIN_FIELD_DESC);
        oprot.writeString(struct.coin);
        oprot.writeFieldEnd();
      }
      if (struct.money != null) {
        oprot.writeFieldBegin(MONEY_FIELD_DESC);
        oprot.writeString(struct.money);
        oprot.writeFieldEnd();
      }
      if (struct.signBook != null) {
        oprot.writeFieldBegin(SIGN_BOOK_FIELD_DESC);
        oprot.writeString(struct.signBook);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(TYPE_FIELD_DESC);
      oprot.writeI32(struct.type);
      oprot.writeFieldEnd();
      if (struct.goodsId != null) {
        oprot.writeFieldBegin(GOODS_ID_FIELD_DESC);
        oprot.writeString(struct.goodsId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(PID_FIELD_DESC);
      oprot.writeI32(struct.pid);
      oprot.writeFieldEnd();
      if (struct.version != null) {
        oprot.writeFieldBegin(VERSION_FIELD_DESC);
        oprot.writeString(struct.version);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(CHANNEL_ID_FIELD_DESC);
      oprot.writeI32(struct.channelId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(IS_SANDBOX_FIELD_DESC);
      oprot.writeI32(struct.isSandbox);
      oprot.writeFieldEnd();
      if (struct.tradeTime != null) {
        oprot.writeFieldBegin(TRADE_TIME_FIELD_DESC);
        oprot.writeString(struct.tradeTime);
        oprot.writeFieldEnd();
      }
      if (struct.tradeNo != null) {
        oprot.writeFieldBegin(TRADE_NO_FIELD_DESC);
        oprot.writeString(struct.tradeNo);
        oprot.writeFieldEnd();
      }
      if (struct.partner != null) {
        oprot.writeFieldBegin(PARTNER_FIELD_DESC);
        oprot.writeString(struct.partner);
        oprot.writeFieldEnd();
      }
      if (struct.businessExt != null) {
        oprot.writeFieldBegin(BUSINESS_EXT_FIELD_DESC);
        oprot.writeString(struct.businessExt);
        oprot.writeFieldEnd();
      }
      if (struct.consumeType != null) {
        if (struct.isSetConsumeType()) {
          oprot.writeFieldBegin(CONSUME_TYPE_FIELD_DESC);
          oprot.writeString(struct.consumeType);
          oprot.writeFieldEnd();
        }
      }
      if (struct.roomId != null) {
        if (struct.isSetRoomId()) {
          oprot.writeFieldBegin(ROOM_ID_FIELD_DESC);
          oprot.writeString(struct.roomId);
          oprot.writeFieldEnd();
        }
      }
      if (struct.giftId != null) {
        if (struct.isSetGiftId()) {
          oprot.writeFieldBegin(GIFT_ID_FIELD_DESC);
          oprot.writeString(struct.giftId);
          oprot.writeFieldEnd();
        }
      }
      if (struct.giftNum != null) {
        if (struct.isSetGiftNum()) {
          oprot.writeFieldBegin(GIFT_NUM_FIELD_DESC);
          oprot.writeString(struct.giftNum);
          oprot.writeFieldEnd();
        }
      }
      if (struct.appId != null) {
        if (struct.isSetAppId()) {
          oprot.writeFieldBegin(APP_ID_FIELD_DESC);
          oprot.writeString(struct.appId);
          oprot.writeFieldEnd();
        }
      }
      if (struct.bundleId != null) {
        if (struct.isSetBundleId()) {
          oprot.writeFieldBegin(BUNDLE_ID_FIELD_DESC);
          oprot.writeString(struct.bundleId);
          oprot.writeFieldEnd();
        }
      }
      if (struct.areaCode != null) {
        if (struct.isSetAreaCode()) {
          oprot.writeFieldBegin(AREA_CODE_FIELD_DESC);
          oprot.writeString(struct.areaCode);
          oprot.writeFieldEnd();
        }
      }
      if (struct.timeZone != null) {
        if (struct.isSetTimeZone()) {
          oprot.writeFieldBegin(TIME_ZONE_FIELD_DESC);
          oprot.writeString(struct.timeZone);
          oprot.writeFieldEnd();
        }
      }
      if (struct.currency != null) {
        if (struct.isSetCurrency()) {
          oprot.writeFieldBegin(CURRENCY_FIELD_DESC);
          oprot.writeString(struct.currency);
          oprot.writeFieldEnd();
        }
      }
      if (struct.currencyAmount != null) {
        if (struct.isSetCurrencyAmount()) {
          oprot.writeFieldBegin(CURRENCY_AMOUNT_FIELD_DESC);
          oprot.writeString(struct.currencyAmount);
          oprot.writeFieldEnd();
        }
      }
      if (struct.usdAmount != null) {
        if (struct.isSetUsdAmount()) {
          oprot.writeFieldBegin(USD_AMOUNT_FIELD_DESC);
          oprot.writeString(struct.usdAmount);
          oprot.writeFieldEnd();
        }
      }
      if (struct.clientIp != null) {
        if (struct.isSetClientIp()) {
          oprot.writeFieldBegin(CLIENT_IP_FIELD_DESC);
          oprot.writeString(struct.clientIp);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetAreaId()) {
        oprot.writeFieldBegin(AREA_ID_FIELD_DESC);
        oprot.writeI32(struct.areaId);
        oprot.writeFieldEnd();
      }
      if (struct.outTradeNo != null) {
        if (struct.isSetOutTradeNo()) {
          oprot.writeFieldBegin(OUT_TRADE_NO_FIELD_DESC);
          oprot.writeString(struct.outTradeNo);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetProductType()) {
        oprot.writeFieldBegin(PRODUCT_TYPE_FIELD_DESC);
        oprot.writeI32(struct.productType);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PurchaseCoinForIosRequestTupleSchemeFactory implements SchemeFactory {
    public PurchaseCoinForIosRequestTupleScheme getScheme() {
      return new PurchaseCoinForIosRequestTupleScheme();
    }
  }

  private static class PurchaseCoinForIosRequestTupleScheme extends TupleScheme<PurchaseCoinForIosRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PurchaseCoinForIosRequest struct) throws TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeString(struct.orderNum);
      oprot.writeString(struct.key);
      oprot.writeI64(struct.kugouId);
      oprot.writeI32(struct.time);
      oprot.writeString(struct.coin);
      oprot.writeString(struct.money);
      oprot.writeString(struct.signBook);
      oprot.writeI32(struct.type);
      oprot.writeString(struct.goodsId);
      oprot.writeI32(struct.pid);
      oprot.writeString(struct.version);
      oprot.writeI32(struct.channelId);
      oprot.writeI32(struct.isSandbox);
      oprot.writeString(struct.tradeTime);
      oprot.writeString(struct.tradeNo);
      oprot.writeString(struct.partner);
      oprot.writeString(struct.businessExt);
      BitSet optionals = new BitSet();
      if (struct.isSetConsumeType()) {
        optionals.set(0);
      }
      if (struct.isSetRoomId()) {
        optionals.set(1);
      }
      if (struct.isSetGiftId()) {
        optionals.set(2);
      }
      if (struct.isSetGiftNum()) {
        optionals.set(3);
      }
      if (struct.isSetAppId()) {
        optionals.set(4);
      }
      if (struct.isSetBundleId()) {
        optionals.set(5);
      }
      if (struct.isSetAreaCode()) {
        optionals.set(6);
      }
      if (struct.isSetTimeZone()) {
        optionals.set(7);
      }
      if (struct.isSetCurrency()) {
        optionals.set(8);
      }
      if (struct.isSetCurrencyAmount()) {
        optionals.set(9);
      }
      if (struct.isSetUsdAmount()) {
        optionals.set(10);
      }
      if (struct.isSetClientIp()) {
        optionals.set(11);
      }
      if (struct.isSetAreaId()) {
        optionals.set(12);
      }
      if (struct.isSetOutTradeNo()) {
        optionals.set(13);
      }
      if (struct.isSetProductType()) {
        optionals.set(14);
      }
      oprot.writeBitSet(optionals, 15);
      if (struct.isSetConsumeType()) {
        oprot.writeString(struct.consumeType);
      }
      if (struct.isSetRoomId()) {
        oprot.writeString(struct.roomId);
      }
      if (struct.isSetGiftId()) {
        oprot.writeString(struct.giftId);
      }
      if (struct.isSetGiftNum()) {
        oprot.writeString(struct.giftNum);
      }
      if (struct.isSetAppId()) {
        oprot.writeString(struct.appId);
      }
      if (struct.isSetBundleId()) {
        oprot.writeString(struct.bundleId);
      }
      if (struct.isSetAreaCode()) {
        oprot.writeString(struct.areaCode);
      }
      if (struct.isSetTimeZone()) {
        oprot.writeString(struct.timeZone);
      }
      if (struct.isSetCurrency()) {
        oprot.writeString(struct.currency);
      }
      if (struct.isSetCurrencyAmount()) {
        oprot.writeString(struct.currencyAmount);
      }
      if (struct.isSetUsdAmount()) {
        oprot.writeString(struct.usdAmount);
      }
      if (struct.isSetClientIp()) {
        oprot.writeString(struct.clientIp);
      }
      if (struct.isSetAreaId()) {
        oprot.writeI32(struct.areaId);
      }
      if (struct.isSetOutTradeNo()) {
        oprot.writeString(struct.outTradeNo);
      }
      if (struct.isSetProductType()) {
        oprot.writeI32(struct.productType);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PurchaseCoinForIosRequest struct) throws TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.orderNum = iprot.readString();
      struct.setOrderNumIsSet(true);
      struct.key = iprot.readString();
      struct.setKeyIsSet(true);
      struct.kugouId = iprot.readI64();
      struct.setKugouIdIsSet(true);
      struct.time = iprot.readI32();
      struct.setTimeIsSet(true);
      struct.coin = iprot.readString();
      struct.setCoinIsSet(true);
      struct.money = iprot.readString();
      struct.setMoneyIsSet(true);
      struct.signBook = iprot.readString();
      struct.setSignBookIsSet(true);
      struct.type = iprot.readI32();
      struct.setTypeIsSet(true);
      struct.goodsId = iprot.readString();
      struct.setGoodsIdIsSet(true);
      struct.pid = iprot.readI32();
      struct.setPidIsSet(true);
      struct.version = iprot.readString();
      struct.setVersionIsSet(true);
      struct.channelId = iprot.readI32();
      struct.setChannelIdIsSet(true);
      struct.isSandbox = iprot.readI32();
      struct.setIsSandboxIsSet(true);
      struct.tradeTime = iprot.readString();
      struct.setTradeTimeIsSet(true);
      struct.tradeNo = iprot.readString();
      struct.setTradeNoIsSet(true);
      struct.partner = iprot.readString();
      struct.setPartnerIsSet(true);
      struct.businessExt = iprot.readString();
      struct.setBusinessExtIsSet(true);
      BitSet incoming = iprot.readBitSet(15);
      if (incoming.get(0)) {
        struct.consumeType = iprot.readString();
        struct.setConsumeTypeIsSet(true);
      }
      if (incoming.get(1)) {
        struct.roomId = iprot.readString();
        struct.setRoomIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.giftId = iprot.readString();
        struct.setGiftIdIsSet(true);
      }
      if (incoming.get(3)) {
        struct.giftNum = iprot.readString();
        struct.setGiftNumIsSet(true);
      }
      if (incoming.get(4)) {
        struct.appId = iprot.readString();
        struct.setAppIdIsSet(true);
      }
      if (incoming.get(5)) {
        struct.bundleId = iprot.readString();
        struct.setBundleIdIsSet(true);
      }
      if (incoming.get(6)) {
        struct.areaCode = iprot.readString();
        struct.setAreaCodeIsSet(true);
      }
      if (incoming.get(7)) {
        struct.timeZone = iprot.readString();
        struct.setTimeZoneIsSet(true);
      }
      if (incoming.get(8)) {
        struct.currency = iprot.readString();
        struct.setCurrencyIsSet(true);
      }
      if (incoming.get(9)) {
        struct.currencyAmount = iprot.readString();
        struct.setCurrencyAmountIsSet(true);
      }
      if (incoming.get(10)) {
        struct.usdAmount = iprot.readString();
        struct.setUsdAmountIsSet(true);
      }
      if (incoming.get(11)) {
        struct.clientIp = iprot.readString();
        struct.setClientIpIsSet(true);
      }
      if (incoming.get(12)) {
        struct.areaId = iprot.readI32();
        struct.setAreaIdIsSet(true);
      }
      if (incoming.get(13)) {
        struct.outTradeNo = iprot.readString();
        struct.setOutTradeNoIsSet(true);
      }
      if (incoming.get(14)) {
        struct.productType = iprot.readI32();
        struct.setProductTypeIsSet(true);
      }
    }
  }

}

