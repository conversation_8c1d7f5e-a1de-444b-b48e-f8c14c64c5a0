/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.recharge.thrift;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-08-22")
public class RechargeThriftService {

  /**
   * 充值相关协议
   */
  public interface Iface {

    /**
     * 通过时间段来查询批量成功订单，限制时间（最大周期为5分钟之内，时间间隔为1分钟） beginTime ：开始时间戳 ;endTime：结束时间戳; lastId：第一次传值1，第二次使用第一次返回的lastId ;pageSize：每页大小，最大不超过100
     * 
     * @param beginTime
     * @param endTime
     * @param lastId
     * @param pageSize
     */
    public ResultList getRechargeList(long beginTime, long endTime, long lastId, int pageSize) throws org.apache.thrift.TException;

    /**
     * 通过时间段来查询批量成功订单，限制时间（最大周期为5分钟之内，时间间隔为1分钟） beginTime ：开始时间戳 ;endTime：结束时间戳; lastId：第一次传值1，第二次使用第一次返回的lastId ;pageSize：每页大小，最大不超过100
     * 
     * @param beginTime
     * @param endTime
     * @param lastRechargeId
     * @param batchSize
     */
    public ResultSuccessList getRechargeSuccessList(long beginTime, long endTime, long lastRechargeId, int batchSize) throws org.apache.thrift.TException;

    /**
     * 通过时间段来查询批量成功订单，限制时间（最大周期为5分钟之内，时间间隔为1分钟）
     * 
     * @param request
     */
    public ResultSuccessList getRechargeSuccessListV2(QueryRechargeSuccessRequest request) throws org.apache.thrift.TException;

    /**
     * 获取首充列表 *
     * 
     * @param beginTime
     * @param endTime
     * @param lastId
     * @param pageSize
     */
    public ResultList getFirstRechargeList(long beginTime, long endTime, long lastId, int pageSize) throws org.apache.thrift.TException;

    /**
     * 获取充值信息 *
     * 
     * @param rechargeOrderNum
     */
    public ResultInfo getRechargeInfo(String rechargeOrderNum) throws org.apache.thrift.TException;

    /**
     * 判断是否是代理,1是，0不是 *
     * 
     * @param kugouId
     */
    public AgentInfo isAgent(long kugouId) throws org.apache.thrift.TException;

    /**
     * 判断是否是天猫首充,1是，0不是 *
     * 
     * @param rechargeOrderNum
     */
    public TmallFirstRechargeInfo isTmallFirstRecharge(String rechargeOrderNum) throws org.apache.thrift.TException;

    /**
     * 获取充值信息 *
     * 
     * @param rechargeOrderNum
     */
    public ResultInfo getRechargeInfoFromMaster(String rechargeOrderNum) throws org.apache.thrift.TException;

  }

  public interface AsyncIface {

    public void getRechargeList(long beginTime, long endTime, long lastId, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getRechargeSuccessList(long beginTime, long endTime, long lastRechargeId, int batchSize, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getRechargeSuccessListV2(QueryRechargeSuccessRequest request, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getFirstRechargeList(long beginTime, long endTime, long lastId, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getRechargeInfo(String rechargeOrderNum, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void isAgent(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void isTmallFirstRecharge(String rechargeOrderNum, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getRechargeInfoFromMaster(String rechargeOrderNum, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

  }

  public static class Client extends org.apache.thrift.TServiceClient implements Iface {
    public static class Factory implements org.apache.thrift.TServiceClientFactory<Client> {
      public Factory() {}
      public Client getClient(org.apache.thrift.protocol.TProtocol prot) {
        return new Client(prot);
      }
      public Client getClient(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
        return new Client(iprot, oprot);
      }
    }

    public Client(org.apache.thrift.protocol.TProtocol prot)
    {
      super(prot, prot);
    }

    public Client(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
      super(iprot, oprot);
    }

    public ResultList getRechargeList(long beginTime, long endTime, long lastId, int pageSize) throws org.apache.thrift.TException
    {
      send_getRechargeList(beginTime, endTime, lastId, pageSize);
      return recv_getRechargeList();
    }

    public void send_getRechargeList(long beginTime, long endTime, long lastId, int pageSize) throws org.apache.thrift.TException
    {
      getRechargeList_args args = new getRechargeList_args();
      args.setBeginTime(beginTime);
      args.setEndTime(endTime);
      args.setLastId(lastId);
      args.setPageSize(pageSize);
      sendBase("getRechargeList", args);
    }

    public ResultList recv_getRechargeList() throws org.apache.thrift.TException
    {
      getRechargeList_result result = new getRechargeList_result();
      receiveBase(result, "getRechargeList");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getRechargeList failed: unknown result");
    }

    public ResultSuccessList getRechargeSuccessList(long beginTime, long endTime, long lastRechargeId, int batchSize) throws org.apache.thrift.TException
    {
      send_getRechargeSuccessList(beginTime, endTime, lastRechargeId, batchSize);
      return recv_getRechargeSuccessList();
    }

    public void send_getRechargeSuccessList(long beginTime, long endTime, long lastRechargeId, int batchSize) throws org.apache.thrift.TException
    {
      getRechargeSuccessList_args args = new getRechargeSuccessList_args();
      args.setBeginTime(beginTime);
      args.setEndTime(endTime);
      args.setLastRechargeId(lastRechargeId);
      args.setBatchSize(batchSize);
      sendBase("getRechargeSuccessList", args);
    }

    public ResultSuccessList recv_getRechargeSuccessList() throws org.apache.thrift.TException
    {
      getRechargeSuccessList_result result = new getRechargeSuccessList_result();
      receiveBase(result, "getRechargeSuccessList");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getRechargeSuccessList failed: unknown result");
    }

    public ResultSuccessList getRechargeSuccessListV2(QueryRechargeSuccessRequest request) throws org.apache.thrift.TException
    {
      send_getRechargeSuccessListV2(request);
      return recv_getRechargeSuccessListV2();
    }

    public void send_getRechargeSuccessListV2(QueryRechargeSuccessRequest request) throws org.apache.thrift.TException
    {
      getRechargeSuccessListV2_args args = new getRechargeSuccessListV2_args();
      args.setRequest(request);
      sendBase("getRechargeSuccessListV2", args);
    }

    public ResultSuccessList recv_getRechargeSuccessListV2() throws org.apache.thrift.TException
    {
      getRechargeSuccessListV2_result result = new getRechargeSuccessListV2_result();
      receiveBase(result, "getRechargeSuccessListV2");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getRechargeSuccessListV2 failed: unknown result");
    }

    public ResultList getFirstRechargeList(long beginTime, long endTime, long lastId, int pageSize) throws org.apache.thrift.TException
    {
      send_getFirstRechargeList(beginTime, endTime, lastId, pageSize);
      return recv_getFirstRechargeList();
    }

    public void send_getFirstRechargeList(long beginTime, long endTime, long lastId, int pageSize) throws org.apache.thrift.TException
    {
      getFirstRechargeList_args args = new getFirstRechargeList_args();
      args.setBeginTime(beginTime);
      args.setEndTime(endTime);
      args.setLastId(lastId);
      args.setPageSize(pageSize);
      sendBase("getFirstRechargeList", args);
    }

    public ResultList recv_getFirstRechargeList() throws org.apache.thrift.TException
    {
      getFirstRechargeList_result result = new getFirstRechargeList_result();
      receiveBase(result, "getFirstRechargeList");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getFirstRechargeList failed: unknown result");
    }

    public ResultInfo getRechargeInfo(String rechargeOrderNum) throws org.apache.thrift.TException
    {
      send_getRechargeInfo(rechargeOrderNum);
      return recv_getRechargeInfo();
    }

    public void send_getRechargeInfo(String rechargeOrderNum) throws org.apache.thrift.TException
    {
      getRechargeInfo_args args = new getRechargeInfo_args();
      args.setRechargeOrderNum(rechargeOrderNum);
      sendBase("getRechargeInfo", args);
    }

    public ResultInfo recv_getRechargeInfo() throws org.apache.thrift.TException
    {
      getRechargeInfo_result result = new getRechargeInfo_result();
      receiveBase(result, "getRechargeInfo");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getRechargeInfo failed: unknown result");
    }

    public AgentInfo isAgent(long kugouId) throws org.apache.thrift.TException
    {
      send_isAgent(kugouId);
      return recv_isAgent();
    }

    public void send_isAgent(long kugouId) throws org.apache.thrift.TException
    {
      isAgent_args args = new isAgent_args();
      args.setKugouId(kugouId);
      sendBase("isAgent", args);
    }

    public AgentInfo recv_isAgent() throws org.apache.thrift.TException
    {
      isAgent_result result = new isAgent_result();
      receiveBase(result, "isAgent");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "isAgent failed: unknown result");
    }

    public TmallFirstRechargeInfo isTmallFirstRecharge(String rechargeOrderNum) throws org.apache.thrift.TException
    {
      send_isTmallFirstRecharge(rechargeOrderNum);
      return recv_isTmallFirstRecharge();
    }

    public void send_isTmallFirstRecharge(String rechargeOrderNum) throws org.apache.thrift.TException
    {
      isTmallFirstRecharge_args args = new isTmallFirstRecharge_args();
      args.setRechargeOrderNum(rechargeOrderNum);
      sendBase("isTmallFirstRecharge", args);
    }

    public TmallFirstRechargeInfo recv_isTmallFirstRecharge() throws org.apache.thrift.TException
    {
      isTmallFirstRecharge_result result = new isTmallFirstRecharge_result();
      receiveBase(result, "isTmallFirstRecharge");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "isTmallFirstRecharge failed: unknown result");
    }

    public ResultInfo getRechargeInfoFromMaster(String rechargeOrderNum) throws org.apache.thrift.TException
    {
      send_getRechargeInfoFromMaster(rechargeOrderNum);
      return recv_getRechargeInfoFromMaster();
    }

    public void send_getRechargeInfoFromMaster(String rechargeOrderNum) throws org.apache.thrift.TException
    {
      getRechargeInfoFromMaster_args args = new getRechargeInfoFromMaster_args();
      args.setRechargeOrderNum(rechargeOrderNum);
      sendBase("getRechargeInfoFromMaster", args);
    }

    public ResultInfo recv_getRechargeInfoFromMaster() throws org.apache.thrift.TException
    {
      getRechargeInfoFromMaster_result result = new getRechargeInfoFromMaster_result();
      receiveBase(result, "getRechargeInfoFromMaster");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getRechargeInfoFromMaster failed: unknown result");
    }

  }
  public static class AsyncClient extends org.apache.thrift.async.TAsyncClient implements AsyncIface {
    public static class Factory implements org.apache.thrift.async.TAsyncClientFactory<AsyncClient> {
      private org.apache.thrift.async.TAsyncClientManager clientManager;
      private org.apache.thrift.protocol.TProtocolFactory protocolFactory;
      public Factory(org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.protocol.TProtocolFactory protocolFactory) {
        this.clientManager = clientManager;
        this.protocolFactory = protocolFactory;
      }
      public AsyncClient getAsyncClient(org.apache.thrift.transport.TNonblockingTransport transport) {
        return new AsyncClient(protocolFactory, clientManager, transport);
      }
    }

    public AsyncClient(org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.transport.TNonblockingTransport transport) {
      super(protocolFactory, clientManager, transport);
    }

    public void getRechargeList(long beginTime, long endTime, long lastId, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getRechargeList_call method_call = new getRechargeList_call(beginTime, endTime, lastId, pageSize, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getRechargeList_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long beginTime;
      private long endTime;
      private long lastId;
      private int pageSize;
      public getRechargeList_call(long beginTime, long endTime, long lastId, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.beginTime = beginTime;
        this.endTime = endTime;
        this.lastId = lastId;
        this.pageSize = pageSize;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getRechargeList", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getRechargeList_args args = new getRechargeList_args();
        args.setBeginTime(beginTime);
        args.setEndTime(endTime);
        args.setLastId(lastId);
        args.setPageSize(pageSize);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ResultList getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getRechargeList();
      }
    }

    public void getRechargeSuccessList(long beginTime, long endTime, long lastRechargeId, int batchSize, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getRechargeSuccessList_call method_call = new getRechargeSuccessList_call(beginTime, endTime, lastRechargeId, batchSize, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getRechargeSuccessList_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long beginTime;
      private long endTime;
      private long lastRechargeId;
      private int batchSize;
      public getRechargeSuccessList_call(long beginTime, long endTime, long lastRechargeId, int batchSize, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.beginTime = beginTime;
        this.endTime = endTime;
        this.lastRechargeId = lastRechargeId;
        this.batchSize = batchSize;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getRechargeSuccessList", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getRechargeSuccessList_args args = new getRechargeSuccessList_args();
        args.setBeginTime(beginTime);
        args.setEndTime(endTime);
        args.setLastRechargeId(lastRechargeId);
        args.setBatchSize(batchSize);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ResultSuccessList getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getRechargeSuccessList();
      }
    }

    public void getRechargeSuccessListV2(QueryRechargeSuccessRequest request, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getRechargeSuccessListV2_call method_call = new getRechargeSuccessListV2_call(request, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getRechargeSuccessListV2_call extends org.apache.thrift.async.TAsyncMethodCall {
      private QueryRechargeSuccessRequest request;
      public getRechargeSuccessListV2_call(QueryRechargeSuccessRequest request, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.request = request;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getRechargeSuccessListV2", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getRechargeSuccessListV2_args args = new getRechargeSuccessListV2_args();
        args.setRequest(request);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ResultSuccessList getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getRechargeSuccessListV2();
      }
    }

    public void getFirstRechargeList(long beginTime, long endTime, long lastId, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getFirstRechargeList_call method_call = new getFirstRechargeList_call(beginTime, endTime, lastId, pageSize, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getFirstRechargeList_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long beginTime;
      private long endTime;
      private long lastId;
      private int pageSize;
      public getFirstRechargeList_call(long beginTime, long endTime, long lastId, int pageSize, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.beginTime = beginTime;
        this.endTime = endTime;
        this.lastId = lastId;
        this.pageSize = pageSize;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getFirstRechargeList", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getFirstRechargeList_args args = new getFirstRechargeList_args();
        args.setBeginTime(beginTime);
        args.setEndTime(endTime);
        args.setLastId(lastId);
        args.setPageSize(pageSize);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ResultList getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getFirstRechargeList();
      }
    }

    public void getRechargeInfo(String rechargeOrderNum, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getRechargeInfo_call method_call = new getRechargeInfo_call(rechargeOrderNum, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getRechargeInfo_call extends org.apache.thrift.async.TAsyncMethodCall {
      private String rechargeOrderNum;
      public getRechargeInfo_call(String rechargeOrderNum, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.rechargeOrderNum = rechargeOrderNum;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getRechargeInfo", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getRechargeInfo_args args = new getRechargeInfo_args();
        args.setRechargeOrderNum(rechargeOrderNum);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ResultInfo getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getRechargeInfo();
      }
    }

    public void isAgent(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      isAgent_call method_call = new isAgent_call(kugouId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class isAgent_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      public isAgent_call(long kugouId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("isAgent", org.apache.thrift.protocol.TMessageType.CALL, 0));
        isAgent_args args = new isAgent_args();
        args.setKugouId(kugouId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public AgentInfo getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_isAgent();
      }
    }

    public void isTmallFirstRecharge(String rechargeOrderNum, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      isTmallFirstRecharge_call method_call = new isTmallFirstRecharge_call(rechargeOrderNum, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class isTmallFirstRecharge_call extends org.apache.thrift.async.TAsyncMethodCall {
      private String rechargeOrderNum;
      public isTmallFirstRecharge_call(String rechargeOrderNum, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.rechargeOrderNum = rechargeOrderNum;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("isTmallFirstRecharge", org.apache.thrift.protocol.TMessageType.CALL, 0));
        isTmallFirstRecharge_args args = new isTmallFirstRecharge_args();
        args.setRechargeOrderNum(rechargeOrderNum);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public TmallFirstRechargeInfo getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_isTmallFirstRecharge();
      }
    }

    public void getRechargeInfoFromMaster(String rechargeOrderNum, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getRechargeInfoFromMaster_call method_call = new getRechargeInfoFromMaster_call(rechargeOrderNum, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getRechargeInfoFromMaster_call extends org.apache.thrift.async.TAsyncMethodCall {
      private String rechargeOrderNum;
      public getRechargeInfoFromMaster_call(String rechargeOrderNum, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.rechargeOrderNum = rechargeOrderNum;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getRechargeInfoFromMaster", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getRechargeInfoFromMaster_args args = new getRechargeInfoFromMaster_args();
        args.setRechargeOrderNum(rechargeOrderNum);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ResultInfo getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getRechargeInfoFromMaster();
      }
    }

  }

  public static class Processor<I extends Iface> extends org.apache.thrift.TBaseProcessor<I> implements org.apache.thrift.TProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());
    public Processor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>>()));
    }

    protected Processor(I iface, Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends Iface> Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> getProcessMap(Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      processMap.put("getRechargeList", new getRechargeList());
      processMap.put("getRechargeSuccessList", new getRechargeSuccessList());
      processMap.put("getRechargeSuccessListV2", new getRechargeSuccessListV2());
      processMap.put("getFirstRechargeList", new getFirstRechargeList());
      processMap.put("getRechargeInfo", new getRechargeInfo());
      processMap.put("isAgent", new isAgent());
      processMap.put("isTmallFirstRecharge", new isTmallFirstRecharge());
      processMap.put("getRechargeInfoFromMaster", new getRechargeInfoFromMaster());
      return processMap;
    }

    public static class getRechargeList<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getRechargeList_args> {
      public getRechargeList() {
        super("getRechargeList");
      }

      public getRechargeList_args getEmptyArgsInstance() {
        return new getRechargeList_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getRechargeList_result getResult(I iface, getRechargeList_args args) throws org.apache.thrift.TException {
        getRechargeList_result result = new getRechargeList_result();
        result.success = iface.getRechargeList(args.beginTime, args.endTime, args.lastId, args.pageSize);
        return result;
      }
    }

    public static class getRechargeSuccessList<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getRechargeSuccessList_args> {
      public getRechargeSuccessList() {
        super("getRechargeSuccessList");
      }

      public getRechargeSuccessList_args getEmptyArgsInstance() {
        return new getRechargeSuccessList_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getRechargeSuccessList_result getResult(I iface, getRechargeSuccessList_args args) throws org.apache.thrift.TException {
        getRechargeSuccessList_result result = new getRechargeSuccessList_result();
        result.success = iface.getRechargeSuccessList(args.beginTime, args.endTime, args.lastRechargeId, args.batchSize);
        return result;
      }
    }

    public static class getRechargeSuccessListV2<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getRechargeSuccessListV2_args> {
      public getRechargeSuccessListV2() {
        super("getRechargeSuccessListV2");
      }

      public getRechargeSuccessListV2_args getEmptyArgsInstance() {
        return new getRechargeSuccessListV2_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getRechargeSuccessListV2_result getResult(I iface, getRechargeSuccessListV2_args args) throws org.apache.thrift.TException {
        getRechargeSuccessListV2_result result = new getRechargeSuccessListV2_result();
        result.success = iface.getRechargeSuccessListV2(args.request);
        return result;
      }
    }

    public static class getFirstRechargeList<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getFirstRechargeList_args> {
      public getFirstRechargeList() {
        super("getFirstRechargeList");
      }

      public getFirstRechargeList_args getEmptyArgsInstance() {
        return new getFirstRechargeList_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getFirstRechargeList_result getResult(I iface, getFirstRechargeList_args args) throws org.apache.thrift.TException {
        getFirstRechargeList_result result = new getFirstRechargeList_result();
        result.success = iface.getFirstRechargeList(args.beginTime, args.endTime, args.lastId, args.pageSize);
        return result;
      }
    }

    public static class getRechargeInfo<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getRechargeInfo_args> {
      public getRechargeInfo() {
        super("getRechargeInfo");
      }

      public getRechargeInfo_args getEmptyArgsInstance() {
        return new getRechargeInfo_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getRechargeInfo_result getResult(I iface, getRechargeInfo_args args) throws org.apache.thrift.TException {
        getRechargeInfo_result result = new getRechargeInfo_result();
        result.success = iface.getRechargeInfo(args.rechargeOrderNum);
        return result;
      }
    }

    public static class isAgent<I extends Iface> extends org.apache.thrift.ProcessFunction<I, isAgent_args> {
      public isAgent() {
        super("isAgent");
      }

      public isAgent_args getEmptyArgsInstance() {
        return new isAgent_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public isAgent_result getResult(I iface, isAgent_args args) throws org.apache.thrift.TException {
        isAgent_result result = new isAgent_result();
        result.success = iface.isAgent(args.kugouId);
        return result;
      }
    }

    public static class isTmallFirstRecharge<I extends Iface> extends org.apache.thrift.ProcessFunction<I, isTmallFirstRecharge_args> {
      public isTmallFirstRecharge() {
        super("isTmallFirstRecharge");
      }

      public isTmallFirstRecharge_args getEmptyArgsInstance() {
        return new isTmallFirstRecharge_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public isTmallFirstRecharge_result getResult(I iface, isTmallFirstRecharge_args args) throws org.apache.thrift.TException {
        isTmallFirstRecharge_result result = new isTmallFirstRecharge_result();
        result.success = iface.isTmallFirstRecharge(args.rechargeOrderNum);
        return result;
      }
    }

    public static class getRechargeInfoFromMaster<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getRechargeInfoFromMaster_args> {
      public getRechargeInfoFromMaster() {
        super("getRechargeInfoFromMaster");
      }

      public getRechargeInfoFromMaster_args getEmptyArgsInstance() {
        return new getRechargeInfoFromMaster_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getRechargeInfoFromMaster_result getResult(I iface, getRechargeInfoFromMaster_args args) throws org.apache.thrift.TException {
        getRechargeInfoFromMaster_result result = new getRechargeInfoFromMaster_result();
        result.success = iface.getRechargeInfoFromMaster(args.rechargeOrderNum);
        return result;
      }
    }

  }

  public static class AsyncProcessor<I extends AsyncIface> extends org.apache.thrift.TBaseAsyncProcessor<I> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());
    public AsyncProcessor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.AsyncProcessFunction<I, ? extends org.apache.thrift.TBase, ?>>()));
    }

    protected AsyncProcessor(I iface, Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends AsyncIface> Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase,?>> getProcessMap(Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      processMap.put("getRechargeList", new getRechargeList());
      processMap.put("getRechargeSuccessList", new getRechargeSuccessList());
      processMap.put("getRechargeSuccessListV2", new getRechargeSuccessListV2());
      processMap.put("getFirstRechargeList", new getFirstRechargeList());
      processMap.put("getRechargeInfo", new getRechargeInfo());
      processMap.put("isAgent", new isAgent());
      processMap.put("isTmallFirstRecharge", new isTmallFirstRecharge());
      processMap.put("getRechargeInfoFromMaster", new getRechargeInfoFromMaster());
      return processMap;
    }

    public static class getRechargeList<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getRechargeList_args, ResultList> {
      public getRechargeList() {
        super("getRechargeList");
      }

      public getRechargeList_args getEmptyArgsInstance() {
        return new getRechargeList_args();
      }

      public AsyncMethodCallback<ResultList> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ResultList>() { 
          public void onComplete(ResultList o) {
            getRechargeList_result result = new getRechargeList_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getRechargeList_result result = new getRechargeList_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getRechargeList_args args, org.apache.thrift.async.AsyncMethodCallback<ResultList> resultHandler) throws TException {
        iface.getRechargeList(args.beginTime, args.endTime, args.lastId, args.pageSize,resultHandler);
      }
    }

    public static class getRechargeSuccessList<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getRechargeSuccessList_args, ResultSuccessList> {
      public getRechargeSuccessList() {
        super("getRechargeSuccessList");
      }

      public getRechargeSuccessList_args getEmptyArgsInstance() {
        return new getRechargeSuccessList_args();
      }

      public AsyncMethodCallback<ResultSuccessList> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ResultSuccessList>() { 
          public void onComplete(ResultSuccessList o) {
            getRechargeSuccessList_result result = new getRechargeSuccessList_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getRechargeSuccessList_result result = new getRechargeSuccessList_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getRechargeSuccessList_args args, org.apache.thrift.async.AsyncMethodCallback<ResultSuccessList> resultHandler) throws TException {
        iface.getRechargeSuccessList(args.beginTime, args.endTime, args.lastRechargeId, args.batchSize,resultHandler);
      }
    }

    public static class getRechargeSuccessListV2<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getRechargeSuccessListV2_args, ResultSuccessList> {
      public getRechargeSuccessListV2() {
        super("getRechargeSuccessListV2");
      }

      public getRechargeSuccessListV2_args getEmptyArgsInstance() {
        return new getRechargeSuccessListV2_args();
      }

      public AsyncMethodCallback<ResultSuccessList> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ResultSuccessList>() { 
          public void onComplete(ResultSuccessList o) {
            getRechargeSuccessListV2_result result = new getRechargeSuccessListV2_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getRechargeSuccessListV2_result result = new getRechargeSuccessListV2_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getRechargeSuccessListV2_args args, org.apache.thrift.async.AsyncMethodCallback<ResultSuccessList> resultHandler) throws TException {
        iface.getRechargeSuccessListV2(args.request,resultHandler);
      }
    }

    public static class getFirstRechargeList<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getFirstRechargeList_args, ResultList> {
      public getFirstRechargeList() {
        super("getFirstRechargeList");
      }

      public getFirstRechargeList_args getEmptyArgsInstance() {
        return new getFirstRechargeList_args();
      }

      public AsyncMethodCallback<ResultList> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ResultList>() { 
          public void onComplete(ResultList o) {
            getFirstRechargeList_result result = new getFirstRechargeList_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getFirstRechargeList_result result = new getFirstRechargeList_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getFirstRechargeList_args args, org.apache.thrift.async.AsyncMethodCallback<ResultList> resultHandler) throws TException {
        iface.getFirstRechargeList(args.beginTime, args.endTime, args.lastId, args.pageSize,resultHandler);
      }
    }

    public static class getRechargeInfo<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getRechargeInfo_args, ResultInfo> {
      public getRechargeInfo() {
        super("getRechargeInfo");
      }

      public getRechargeInfo_args getEmptyArgsInstance() {
        return new getRechargeInfo_args();
      }

      public AsyncMethodCallback<ResultInfo> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ResultInfo>() { 
          public void onComplete(ResultInfo o) {
            getRechargeInfo_result result = new getRechargeInfo_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getRechargeInfo_result result = new getRechargeInfo_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getRechargeInfo_args args, org.apache.thrift.async.AsyncMethodCallback<ResultInfo> resultHandler) throws TException {
        iface.getRechargeInfo(args.rechargeOrderNum,resultHandler);
      }
    }

    public static class isAgent<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, isAgent_args, AgentInfo> {
      public isAgent() {
        super("isAgent");
      }

      public isAgent_args getEmptyArgsInstance() {
        return new isAgent_args();
      }

      public AsyncMethodCallback<AgentInfo> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<AgentInfo>() { 
          public void onComplete(AgentInfo o) {
            isAgent_result result = new isAgent_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            isAgent_result result = new isAgent_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, isAgent_args args, org.apache.thrift.async.AsyncMethodCallback<AgentInfo> resultHandler) throws TException {
        iface.isAgent(args.kugouId,resultHandler);
      }
    }

    public static class isTmallFirstRecharge<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, isTmallFirstRecharge_args, TmallFirstRechargeInfo> {
      public isTmallFirstRecharge() {
        super("isTmallFirstRecharge");
      }

      public isTmallFirstRecharge_args getEmptyArgsInstance() {
        return new isTmallFirstRecharge_args();
      }

      public AsyncMethodCallback<TmallFirstRechargeInfo> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<TmallFirstRechargeInfo>() { 
          public void onComplete(TmallFirstRechargeInfo o) {
            isTmallFirstRecharge_result result = new isTmallFirstRecharge_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            isTmallFirstRecharge_result result = new isTmallFirstRecharge_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, isTmallFirstRecharge_args args, org.apache.thrift.async.AsyncMethodCallback<TmallFirstRechargeInfo> resultHandler) throws TException {
        iface.isTmallFirstRecharge(args.rechargeOrderNum,resultHandler);
      }
    }

    public static class getRechargeInfoFromMaster<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getRechargeInfoFromMaster_args, ResultInfo> {
      public getRechargeInfoFromMaster() {
        super("getRechargeInfoFromMaster");
      }

      public getRechargeInfoFromMaster_args getEmptyArgsInstance() {
        return new getRechargeInfoFromMaster_args();
      }

      public AsyncMethodCallback<ResultInfo> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ResultInfo>() { 
          public void onComplete(ResultInfo o) {
            getRechargeInfoFromMaster_result result = new getRechargeInfoFromMaster_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getRechargeInfoFromMaster_result result = new getRechargeInfoFromMaster_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getRechargeInfoFromMaster_args args, org.apache.thrift.async.AsyncMethodCallback<ResultInfo> resultHandler) throws TException {
        iface.getRechargeInfoFromMaster(args.rechargeOrderNum,resultHandler);
      }
    }

  }

  public static class getRechargeList_args implements org.apache.thrift.TBase<getRechargeList_args, getRechargeList_args._Fields>, java.io.Serializable, Cloneable, Comparable<getRechargeList_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getRechargeList_args");

    private static final org.apache.thrift.protocol.TField BEGIN_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("beginTime", org.apache.thrift.protocol.TType.I64, (short)1);
    private static final org.apache.thrift.protocol.TField END_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("endTime", org.apache.thrift.protocol.TType.I64, (short)2);
    private static final org.apache.thrift.protocol.TField LAST_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("lastId", org.apache.thrift.protocol.TType.I64, (short)3);
    private static final org.apache.thrift.protocol.TField PAGE_SIZE_FIELD_DESC = new org.apache.thrift.protocol.TField("pageSize", org.apache.thrift.protocol.TType.I32, (short)4);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getRechargeList_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getRechargeList_argsTupleSchemeFactory());
    }

    public long beginTime; // required
    public long endTime; // required
    public long lastId; // required
    public int pageSize; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      BEGIN_TIME((short)1, "beginTime"),
      END_TIME((short)2, "endTime"),
      LAST_ID((short)3, "lastId"),
      PAGE_SIZE((short)4, "pageSize");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // BEGIN_TIME
            return BEGIN_TIME;
          case 2: // END_TIME
            return END_TIME;
          case 3: // LAST_ID
            return LAST_ID;
          case 4: // PAGE_SIZE
            return PAGE_SIZE;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __BEGINTIME_ISSET_ID = 0;
    private static final int __ENDTIME_ISSET_ID = 1;
    private static final int __LASTID_ISSET_ID = 2;
    private static final int __PAGESIZE_ISSET_ID = 3;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.BEGIN_TIME, new org.apache.thrift.meta_data.FieldMetaData("beginTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.END_TIME, new org.apache.thrift.meta_data.FieldMetaData("endTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.LAST_ID, new org.apache.thrift.meta_data.FieldMetaData("lastId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.PAGE_SIZE, new org.apache.thrift.meta_data.FieldMetaData("pageSize", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getRechargeList_args.class, metaDataMap);
    }

    public getRechargeList_args() {
    }

    public getRechargeList_args(
      long beginTime,
      long endTime,
      long lastId,
      int pageSize)
    {
      this();
      this.beginTime = beginTime;
      setBeginTimeIsSet(true);
      this.endTime = endTime;
      setEndTimeIsSet(true);
      this.lastId = lastId;
      setLastIdIsSet(true);
      this.pageSize = pageSize;
      setPageSizeIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getRechargeList_args(getRechargeList_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.beginTime = other.beginTime;
      this.endTime = other.endTime;
      this.lastId = other.lastId;
      this.pageSize = other.pageSize;
    }

    public getRechargeList_args deepCopy() {
      return new getRechargeList_args(this);
    }

    @Override
    public void clear() {
      setBeginTimeIsSet(false);
      this.beginTime = 0;
      setEndTimeIsSet(false);
      this.endTime = 0;
      setLastIdIsSet(false);
      this.lastId = 0;
      setPageSizeIsSet(false);
      this.pageSize = 0;
    }

    public long getBeginTime() {
      return this.beginTime;
    }

    public getRechargeList_args setBeginTime(long beginTime) {
      this.beginTime = beginTime;
      setBeginTimeIsSet(true);
      return this;
    }

    public void unsetBeginTime() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __BEGINTIME_ISSET_ID);
    }

    /** Returns true if field beginTime is set (has been assigned a value) and false otherwise */
    public boolean isSetBeginTime() {
      return EncodingUtils.testBit(__isset_bitfield, __BEGINTIME_ISSET_ID);
    }

    public void setBeginTimeIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __BEGINTIME_ISSET_ID, value);
    }

    public long getEndTime() {
      return this.endTime;
    }

    public getRechargeList_args setEndTime(long endTime) {
      this.endTime = endTime;
      setEndTimeIsSet(true);
      return this;
    }

    public void unsetEndTime() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ENDTIME_ISSET_ID);
    }

    /** Returns true if field endTime is set (has been assigned a value) and false otherwise */
    public boolean isSetEndTime() {
      return EncodingUtils.testBit(__isset_bitfield, __ENDTIME_ISSET_ID);
    }

    public void setEndTimeIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ENDTIME_ISSET_ID, value);
    }

    public long getLastId() {
      return this.lastId;
    }

    public getRechargeList_args setLastId(long lastId) {
      this.lastId = lastId;
      setLastIdIsSet(true);
      return this;
    }

    public void unsetLastId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __LASTID_ISSET_ID);
    }

    /** Returns true if field lastId is set (has been assigned a value) and false otherwise */
    public boolean isSetLastId() {
      return EncodingUtils.testBit(__isset_bitfield, __LASTID_ISSET_ID);
    }

    public void setLastIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __LASTID_ISSET_ID, value);
    }

    public int getPageSize() {
      return this.pageSize;
    }

    public getRechargeList_args setPageSize(int pageSize) {
      this.pageSize = pageSize;
      setPageSizeIsSet(true);
      return this;
    }

    public void unsetPageSize() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
    }

    /** Returns true if field pageSize is set (has been assigned a value) and false otherwise */
    public boolean isSetPageSize() {
      return EncodingUtils.testBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
    }

    public void setPageSizeIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGESIZE_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case BEGIN_TIME:
        if (value == null) {
          unsetBeginTime();
        } else {
          setBeginTime((Long)value);
        }
        break;

      case END_TIME:
        if (value == null) {
          unsetEndTime();
        } else {
          setEndTime((Long)value);
        }
        break;

      case LAST_ID:
        if (value == null) {
          unsetLastId();
        } else {
          setLastId((Long)value);
        }
        break;

      case PAGE_SIZE:
        if (value == null) {
          unsetPageSize();
        } else {
          setPageSize((Integer)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case BEGIN_TIME:
        return getBeginTime();

      case END_TIME:
        return getEndTime();

      case LAST_ID:
        return getLastId();

      case PAGE_SIZE:
        return getPageSize();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case BEGIN_TIME:
        return isSetBeginTime();
      case END_TIME:
        return isSetEndTime();
      case LAST_ID:
        return isSetLastId();
      case PAGE_SIZE:
        return isSetPageSize();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getRechargeList_args)
        return this.equals((getRechargeList_args)that);
      return false;
    }

    public boolean equals(getRechargeList_args that) {
      if (that == null)
        return false;

      boolean this_present_beginTime = true;
      boolean that_present_beginTime = true;
      if (this_present_beginTime || that_present_beginTime) {
        if (!(this_present_beginTime && that_present_beginTime))
          return false;
        if (this.beginTime != that.beginTime)
          return false;
      }

      boolean this_present_endTime = true;
      boolean that_present_endTime = true;
      if (this_present_endTime || that_present_endTime) {
        if (!(this_present_endTime && that_present_endTime))
          return false;
        if (this.endTime != that.endTime)
          return false;
      }

      boolean this_present_lastId = true;
      boolean that_present_lastId = true;
      if (this_present_lastId || that_present_lastId) {
        if (!(this_present_lastId && that_present_lastId))
          return false;
        if (this.lastId != that.lastId)
          return false;
      }

      boolean this_present_pageSize = true;
      boolean that_present_pageSize = true;
      if (this_present_pageSize || that_present_pageSize) {
        if (!(this_present_pageSize && that_present_pageSize))
          return false;
        if (this.pageSize != that.pageSize)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_beginTime = true;
      list.add(present_beginTime);
      if (present_beginTime)
        list.add(beginTime);

      boolean present_endTime = true;
      list.add(present_endTime);
      if (present_endTime)
        list.add(endTime);

      boolean present_lastId = true;
      list.add(present_lastId);
      if (present_lastId)
        list.add(lastId);

      boolean present_pageSize = true;
      list.add(present_pageSize);
      if (present_pageSize)
        list.add(pageSize);

      return list.hashCode();
    }

    @Override
    public int compareTo(getRechargeList_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetBeginTime()).compareTo(other.isSetBeginTime());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetBeginTime()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.beginTime, other.beginTime);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetEndTime()).compareTo(other.isSetEndTime());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetEndTime()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.endTime, other.endTime);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetLastId()).compareTo(other.isSetLastId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetLastId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.lastId, other.lastId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetPageSize()).compareTo(other.isSetPageSize());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetPageSize()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pageSize, other.pageSize);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getRechargeList_args(");
      boolean first = true;

      sb.append("beginTime:");
      sb.append(this.beginTime);
      first = false;
      if (!first) sb.append(", ");
      sb.append("endTime:");
      sb.append(this.endTime);
      first = false;
      if (!first) sb.append(", ");
      sb.append("lastId:");
      sb.append(this.lastId);
      first = false;
      if (!first) sb.append(", ");
      sb.append("pageSize:");
      sb.append(this.pageSize);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getRechargeList_argsStandardSchemeFactory implements SchemeFactory {
      public getRechargeList_argsStandardScheme getScheme() {
        return new getRechargeList_argsStandardScheme();
      }
    }

    private static class getRechargeList_argsStandardScheme extends StandardScheme<getRechargeList_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getRechargeList_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // BEGIN_TIME
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.beginTime = iprot.readI64();
                struct.setBeginTimeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // END_TIME
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.endTime = iprot.readI64();
                struct.setEndTimeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // LAST_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.lastId = iprot.readI64();
                struct.setLastIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 4: // PAGE_SIZE
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.pageSize = iprot.readI32();
                struct.setPageSizeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getRechargeList_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(BEGIN_TIME_FIELD_DESC);
        oprot.writeI64(struct.beginTime);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(END_TIME_FIELD_DESC);
        oprot.writeI64(struct.endTime);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(LAST_ID_FIELD_DESC);
        oprot.writeI64(struct.lastId);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(PAGE_SIZE_FIELD_DESC);
        oprot.writeI32(struct.pageSize);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getRechargeList_argsTupleSchemeFactory implements SchemeFactory {
      public getRechargeList_argsTupleScheme getScheme() {
        return new getRechargeList_argsTupleScheme();
      }
    }

    private static class getRechargeList_argsTupleScheme extends TupleScheme<getRechargeList_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getRechargeList_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetBeginTime()) {
          optionals.set(0);
        }
        if (struct.isSetEndTime()) {
          optionals.set(1);
        }
        if (struct.isSetLastId()) {
          optionals.set(2);
        }
        if (struct.isSetPageSize()) {
          optionals.set(3);
        }
        oprot.writeBitSet(optionals, 4);
        if (struct.isSetBeginTime()) {
          oprot.writeI64(struct.beginTime);
        }
        if (struct.isSetEndTime()) {
          oprot.writeI64(struct.endTime);
        }
        if (struct.isSetLastId()) {
          oprot.writeI64(struct.lastId);
        }
        if (struct.isSetPageSize()) {
          oprot.writeI32(struct.pageSize);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getRechargeList_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(4);
        if (incoming.get(0)) {
          struct.beginTime = iprot.readI64();
          struct.setBeginTimeIsSet(true);
        }
        if (incoming.get(1)) {
          struct.endTime = iprot.readI64();
          struct.setEndTimeIsSet(true);
        }
        if (incoming.get(2)) {
          struct.lastId = iprot.readI64();
          struct.setLastIdIsSet(true);
        }
        if (incoming.get(3)) {
          struct.pageSize = iprot.readI32();
          struct.setPageSizeIsSet(true);
        }
      }
    }

  }

  public static class getRechargeList_result implements org.apache.thrift.TBase<getRechargeList_result, getRechargeList_result._Fields>, java.io.Serializable, Cloneable, Comparable<getRechargeList_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getRechargeList_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getRechargeList_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getRechargeList_resultTupleSchemeFactory());
    }

    public ResultList success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT          , "ResultList")));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getRechargeList_result.class, metaDataMap);
    }

    public getRechargeList_result() {
    }

    public getRechargeList_result(
      ResultList success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getRechargeList_result(getRechargeList_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    public getRechargeList_result deepCopy() {
      return new getRechargeList_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ResultList getSuccess() {
      return this.success;
    }

    public getRechargeList_result setSuccess(ResultList success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ResultList)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getRechargeList_result)
        return this.equals((getRechargeList_result)that);
      return false;
    }

    public boolean equals(getRechargeList_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getRechargeList_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getRechargeList_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getRechargeList_resultStandardSchemeFactory implements SchemeFactory {
      public getRechargeList_resultStandardScheme getScheme() {
        return new getRechargeList_resultStandardScheme();
      }
    }

    private static class getRechargeList_resultStandardScheme extends StandardScheme<getRechargeList_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getRechargeList_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ResultList();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getRechargeList_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getRechargeList_resultTupleSchemeFactory implements SchemeFactory {
      public getRechargeList_resultTupleScheme getScheme() {
        return new getRechargeList_resultTupleScheme();
      }
    }

    private static class getRechargeList_resultTupleScheme extends TupleScheme<getRechargeList_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getRechargeList_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getRechargeList_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ResultList();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getRechargeSuccessList_args implements org.apache.thrift.TBase<getRechargeSuccessList_args, getRechargeSuccessList_args._Fields>, java.io.Serializable, Cloneable, Comparable<getRechargeSuccessList_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getRechargeSuccessList_args");

    private static final org.apache.thrift.protocol.TField BEGIN_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("beginTime", org.apache.thrift.protocol.TType.I64, (short)1);
    private static final org.apache.thrift.protocol.TField END_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("endTime", org.apache.thrift.protocol.TType.I64, (short)2);
    private static final org.apache.thrift.protocol.TField LAST_RECHARGE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("lastRechargeId", org.apache.thrift.protocol.TType.I64, (short)3);
    private static final org.apache.thrift.protocol.TField BATCH_SIZE_FIELD_DESC = new org.apache.thrift.protocol.TField("batchSize", org.apache.thrift.protocol.TType.I32, (short)4);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getRechargeSuccessList_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getRechargeSuccessList_argsTupleSchemeFactory());
    }

    public long beginTime; // required
    public long endTime; // required
    public long lastRechargeId; // required
    public int batchSize; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      BEGIN_TIME((short)1, "beginTime"),
      END_TIME((short)2, "endTime"),
      LAST_RECHARGE_ID((short)3, "lastRechargeId"),
      BATCH_SIZE((short)4, "batchSize");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // BEGIN_TIME
            return BEGIN_TIME;
          case 2: // END_TIME
            return END_TIME;
          case 3: // LAST_RECHARGE_ID
            return LAST_RECHARGE_ID;
          case 4: // BATCH_SIZE
            return BATCH_SIZE;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __BEGINTIME_ISSET_ID = 0;
    private static final int __ENDTIME_ISSET_ID = 1;
    private static final int __LASTRECHARGEID_ISSET_ID = 2;
    private static final int __BATCHSIZE_ISSET_ID = 3;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.BEGIN_TIME, new org.apache.thrift.meta_data.FieldMetaData("beginTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.END_TIME, new org.apache.thrift.meta_data.FieldMetaData("endTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.LAST_RECHARGE_ID, new org.apache.thrift.meta_data.FieldMetaData("lastRechargeId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.BATCH_SIZE, new org.apache.thrift.meta_data.FieldMetaData("batchSize", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getRechargeSuccessList_args.class, metaDataMap);
    }

    public getRechargeSuccessList_args() {
    }

    public getRechargeSuccessList_args(
      long beginTime,
      long endTime,
      long lastRechargeId,
      int batchSize)
    {
      this();
      this.beginTime = beginTime;
      setBeginTimeIsSet(true);
      this.endTime = endTime;
      setEndTimeIsSet(true);
      this.lastRechargeId = lastRechargeId;
      setLastRechargeIdIsSet(true);
      this.batchSize = batchSize;
      setBatchSizeIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getRechargeSuccessList_args(getRechargeSuccessList_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.beginTime = other.beginTime;
      this.endTime = other.endTime;
      this.lastRechargeId = other.lastRechargeId;
      this.batchSize = other.batchSize;
    }

    public getRechargeSuccessList_args deepCopy() {
      return new getRechargeSuccessList_args(this);
    }

    @Override
    public void clear() {
      setBeginTimeIsSet(false);
      this.beginTime = 0;
      setEndTimeIsSet(false);
      this.endTime = 0;
      setLastRechargeIdIsSet(false);
      this.lastRechargeId = 0;
      setBatchSizeIsSet(false);
      this.batchSize = 0;
    }

    public long getBeginTime() {
      return this.beginTime;
    }

    public getRechargeSuccessList_args setBeginTime(long beginTime) {
      this.beginTime = beginTime;
      setBeginTimeIsSet(true);
      return this;
    }

    public void unsetBeginTime() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __BEGINTIME_ISSET_ID);
    }

    /** Returns true if field beginTime is set (has been assigned a value) and false otherwise */
    public boolean isSetBeginTime() {
      return EncodingUtils.testBit(__isset_bitfield, __BEGINTIME_ISSET_ID);
    }

    public void setBeginTimeIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __BEGINTIME_ISSET_ID, value);
    }

    public long getEndTime() {
      return this.endTime;
    }

    public getRechargeSuccessList_args setEndTime(long endTime) {
      this.endTime = endTime;
      setEndTimeIsSet(true);
      return this;
    }

    public void unsetEndTime() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ENDTIME_ISSET_ID);
    }

    /** Returns true if field endTime is set (has been assigned a value) and false otherwise */
    public boolean isSetEndTime() {
      return EncodingUtils.testBit(__isset_bitfield, __ENDTIME_ISSET_ID);
    }

    public void setEndTimeIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ENDTIME_ISSET_ID, value);
    }

    public long getLastRechargeId() {
      return this.lastRechargeId;
    }

    public getRechargeSuccessList_args setLastRechargeId(long lastRechargeId) {
      this.lastRechargeId = lastRechargeId;
      setLastRechargeIdIsSet(true);
      return this;
    }

    public void unsetLastRechargeId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __LASTRECHARGEID_ISSET_ID);
    }

    /** Returns true if field lastRechargeId is set (has been assigned a value) and false otherwise */
    public boolean isSetLastRechargeId() {
      return EncodingUtils.testBit(__isset_bitfield, __LASTRECHARGEID_ISSET_ID);
    }

    public void setLastRechargeIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __LASTRECHARGEID_ISSET_ID, value);
    }

    public int getBatchSize() {
      return this.batchSize;
    }

    public getRechargeSuccessList_args setBatchSize(int batchSize) {
      this.batchSize = batchSize;
      setBatchSizeIsSet(true);
      return this;
    }

    public void unsetBatchSize() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __BATCHSIZE_ISSET_ID);
    }

    /** Returns true if field batchSize is set (has been assigned a value) and false otherwise */
    public boolean isSetBatchSize() {
      return EncodingUtils.testBit(__isset_bitfield, __BATCHSIZE_ISSET_ID);
    }

    public void setBatchSizeIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __BATCHSIZE_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case BEGIN_TIME:
        if (value == null) {
          unsetBeginTime();
        } else {
          setBeginTime((Long)value);
        }
        break;

      case END_TIME:
        if (value == null) {
          unsetEndTime();
        } else {
          setEndTime((Long)value);
        }
        break;

      case LAST_RECHARGE_ID:
        if (value == null) {
          unsetLastRechargeId();
        } else {
          setLastRechargeId((Long)value);
        }
        break;

      case BATCH_SIZE:
        if (value == null) {
          unsetBatchSize();
        } else {
          setBatchSize((Integer)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case BEGIN_TIME:
        return getBeginTime();

      case END_TIME:
        return getEndTime();

      case LAST_RECHARGE_ID:
        return getLastRechargeId();

      case BATCH_SIZE:
        return getBatchSize();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case BEGIN_TIME:
        return isSetBeginTime();
      case END_TIME:
        return isSetEndTime();
      case LAST_RECHARGE_ID:
        return isSetLastRechargeId();
      case BATCH_SIZE:
        return isSetBatchSize();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getRechargeSuccessList_args)
        return this.equals((getRechargeSuccessList_args)that);
      return false;
    }

    public boolean equals(getRechargeSuccessList_args that) {
      if (that == null)
        return false;

      boolean this_present_beginTime = true;
      boolean that_present_beginTime = true;
      if (this_present_beginTime || that_present_beginTime) {
        if (!(this_present_beginTime && that_present_beginTime))
          return false;
        if (this.beginTime != that.beginTime)
          return false;
      }

      boolean this_present_endTime = true;
      boolean that_present_endTime = true;
      if (this_present_endTime || that_present_endTime) {
        if (!(this_present_endTime && that_present_endTime))
          return false;
        if (this.endTime != that.endTime)
          return false;
      }

      boolean this_present_lastRechargeId = true;
      boolean that_present_lastRechargeId = true;
      if (this_present_lastRechargeId || that_present_lastRechargeId) {
        if (!(this_present_lastRechargeId && that_present_lastRechargeId))
          return false;
        if (this.lastRechargeId != that.lastRechargeId)
          return false;
      }

      boolean this_present_batchSize = true;
      boolean that_present_batchSize = true;
      if (this_present_batchSize || that_present_batchSize) {
        if (!(this_present_batchSize && that_present_batchSize))
          return false;
        if (this.batchSize != that.batchSize)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_beginTime = true;
      list.add(present_beginTime);
      if (present_beginTime)
        list.add(beginTime);

      boolean present_endTime = true;
      list.add(present_endTime);
      if (present_endTime)
        list.add(endTime);

      boolean present_lastRechargeId = true;
      list.add(present_lastRechargeId);
      if (present_lastRechargeId)
        list.add(lastRechargeId);

      boolean present_batchSize = true;
      list.add(present_batchSize);
      if (present_batchSize)
        list.add(batchSize);

      return list.hashCode();
    }

    @Override
    public int compareTo(getRechargeSuccessList_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetBeginTime()).compareTo(other.isSetBeginTime());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetBeginTime()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.beginTime, other.beginTime);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetEndTime()).compareTo(other.isSetEndTime());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetEndTime()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.endTime, other.endTime);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetLastRechargeId()).compareTo(other.isSetLastRechargeId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetLastRechargeId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.lastRechargeId, other.lastRechargeId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetBatchSize()).compareTo(other.isSetBatchSize());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetBatchSize()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.batchSize, other.batchSize);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getRechargeSuccessList_args(");
      boolean first = true;

      sb.append("beginTime:");
      sb.append(this.beginTime);
      first = false;
      if (!first) sb.append(", ");
      sb.append("endTime:");
      sb.append(this.endTime);
      first = false;
      if (!first) sb.append(", ");
      sb.append("lastRechargeId:");
      sb.append(this.lastRechargeId);
      first = false;
      if (!first) sb.append(", ");
      sb.append("batchSize:");
      sb.append(this.batchSize);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getRechargeSuccessList_argsStandardSchemeFactory implements SchemeFactory {
      public getRechargeSuccessList_argsStandardScheme getScheme() {
        return new getRechargeSuccessList_argsStandardScheme();
      }
    }

    private static class getRechargeSuccessList_argsStandardScheme extends StandardScheme<getRechargeSuccessList_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getRechargeSuccessList_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // BEGIN_TIME
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.beginTime = iprot.readI64();
                struct.setBeginTimeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // END_TIME
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.endTime = iprot.readI64();
                struct.setEndTimeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // LAST_RECHARGE_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.lastRechargeId = iprot.readI64();
                struct.setLastRechargeIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 4: // BATCH_SIZE
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.batchSize = iprot.readI32();
                struct.setBatchSizeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getRechargeSuccessList_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(BEGIN_TIME_FIELD_DESC);
        oprot.writeI64(struct.beginTime);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(END_TIME_FIELD_DESC);
        oprot.writeI64(struct.endTime);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(LAST_RECHARGE_ID_FIELD_DESC);
        oprot.writeI64(struct.lastRechargeId);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(BATCH_SIZE_FIELD_DESC);
        oprot.writeI32(struct.batchSize);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getRechargeSuccessList_argsTupleSchemeFactory implements SchemeFactory {
      public getRechargeSuccessList_argsTupleScheme getScheme() {
        return new getRechargeSuccessList_argsTupleScheme();
      }
    }

    private static class getRechargeSuccessList_argsTupleScheme extends TupleScheme<getRechargeSuccessList_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getRechargeSuccessList_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetBeginTime()) {
          optionals.set(0);
        }
        if (struct.isSetEndTime()) {
          optionals.set(1);
        }
        if (struct.isSetLastRechargeId()) {
          optionals.set(2);
        }
        if (struct.isSetBatchSize()) {
          optionals.set(3);
        }
        oprot.writeBitSet(optionals, 4);
        if (struct.isSetBeginTime()) {
          oprot.writeI64(struct.beginTime);
        }
        if (struct.isSetEndTime()) {
          oprot.writeI64(struct.endTime);
        }
        if (struct.isSetLastRechargeId()) {
          oprot.writeI64(struct.lastRechargeId);
        }
        if (struct.isSetBatchSize()) {
          oprot.writeI32(struct.batchSize);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getRechargeSuccessList_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(4);
        if (incoming.get(0)) {
          struct.beginTime = iprot.readI64();
          struct.setBeginTimeIsSet(true);
        }
        if (incoming.get(1)) {
          struct.endTime = iprot.readI64();
          struct.setEndTimeIsSet(true);
        }
        if (incoming.get(2)) {
          struct.lastRechargeId = iprot.readI64();
          struct.setLastRechargeIdIsSet(true);
        }
        if (incoming.get(3)) {
          struct.batchSize = iprot.readI32();
          struct.setBatchSizeIsSet(true);
        }
      }
    }

  }

  public static class getRechargeSuccessList_result implements org.apache.thrift.TBase<getRechargeSuccessList_result, getRechargeSuccessList_result._Fields>, java.io.Serializable, Cloneable, Comparable<getRechargeSuccessList_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getRechargeSuccessList_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getRechargeSuccessList_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getRechargeSuccessList_resultTupleSchemeFactory());
    }

    public ResultSuccessList success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT          , "ResultSuccessList")));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getRechargeSuccessList_result.class, metaDataMap);
    }

    public getRechargeSuccessList_result() {
    }

    public getRechargeSuccessList_result(
      ResultSuccessList success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getRechargeSuccessList_result(getRechargeSuccessList_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    public getRechargeSuccessList_result deepCopy() {
      return new getRechargeSuccessList_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ResultSuccessList getSuccess() {
      return this.success;
    }

    public getRechargeSuccessList_result setSuccess(ResultSuccessList success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ResultSuccessList)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getRechargeSuccessList_result)
        return this.equals((getRechargeSuccessList_result)that);
      return false;
    }

    public boolean equals(getRechargeSuccessList_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getRechargeSuccessList_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getRechargeSuccessList_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getRechargeSuccessList_resultStandardSchemeFactory implements SchemeFactory {
      public getRechargeSuccessList_resultStandardScheme getScheme() {
        return new getRechargeSuccessList_resultStandardScheme();
      }
    }

    private static class getRechargeSuccessList_resultStandardScheme extends StandardScheme<getRechargeSuccessList_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getRechargeSuccessList_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ResultSuccessList();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getRechargeSuccessList_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getRechargeSuccessList_resultTupleSchemeFactory implements SchemeFactory {
      public getRechargeSuccessList_resultTupleScheme getScheme() {
        return new getRechargeSuccessList_resultTupleScheme();
      }
    }

    private static class getRechargeSuccessList_resultTupleScheme extends TupleScheme<getRechargeSuccessList_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getRechargeSuccessList_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getRechargeSuccessList_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ResultSuccessList();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getRechargeSuccessListV2_args implements org.apache.thrift.TBase<getRechargeSuccessListV2_args, getRechargeSuccessListV2_args._Fields>, java.io.Serializable, Cloneable, Comparable<getRechargeSuccessListV2_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getRechargeSuccessListV2_args");

    private static final org.apache.thrift.protocol.TField REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("request", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getRechargeSuccessListV2_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getRechargeSuccessListV2_argsTupleSchemeFactory());
    }

    public QueryRechargeSuccessRequest request; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      REQUEST((short)1, "request");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // REQUEST
            return REQUEST;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.REQUEST, new org.apache.thrift.meta_data.FieldMetaData("request", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT          , "QueryRechargeSuccessRequest")));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getRechargeSuccessListV2_args.class, metaDataMap);
    }

    public getRechargeSuccessListV2_args() {
    }

    public getRechargeSuccessListV2_args(
      QueryRechargeSuccessRequest request)
    {
      this();
      this.request = request;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getRechargeSuccessListV2_args(getRechargeSuccessListV2_args other) {
      if (other.isSetRequest()) {
        this.request = other.request;
      }
    }

    public getRechargeSuccessListV2_args deepCopy() {
      return new getRechargeSuccessListV2_args(this);
    }

    @Override
    public void clear() {
      this.request = null;
    }

    public QueryRechargeSuccessRequest getRequest() {
      return this.request;
    }

    public getRechargeSuccessListV2_args setRequest(QueryRechargeSuccessRequest request) {
      this.request = request;
      return this;
    }

    public void unsetRequest() {
      this.request = null;
    }

    /** Returns true if field request is set (has been assigned a value) and false otherwise */
    public boolean isSetRequest() {
      return this.request != null;
    }

    public void setRequestIsSet(boolean value) {
      if (!value) {
        this.request = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case REQUEST:
        if (value == null) {
          unsetRequest();
        } else {
          setRequest((QueryRechargeSuccessRequest)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case REQUEST:
        return getRequest();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case REQUEST:
        return isSetRequest();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getRechargeSuccessListV2_args)
        return this.equals((getRechargeSuccessListV2_args)that);
      return false;
    }

    public boolean equals(getRechargeSuccessListV2_args that) {
      if (that == null)
        return false;

      boolean this_present_request = true && this.isSetRequest();
      boolean that_present_request = true && that.isSetRequest();
      if (this_present_request || that_present_request) {
        if (!(this_present_request && that_present_request))
          return false;
        if (!this.request.equals(that.request))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_request = true && (isSetRequest());
      list.add(present_request);
      if (present_request)
        list.add(request);

      return list.hashCode();
    }

    @Override
    public int compareTo(getRechargeSuccessListV2_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.request, other.request);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getRechargeSuccessListV2_args(");
      boolean first = true;

      sb.append("request:");
      if (this.request == null) {
        sb.append("null");
      } else {
        sb.append(this.request);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getRechargeSuccessListV2_argsStandardSchemeFactory implements SchemeFactory {
      public getRechargeSuccessListV2_argsStandardScheme getScheme() {
        return new getRechargeSuccessListV2_argsStandardScheme();
      }
    }

    private static class getRechargeSuccessListV2_argsStandardScheme extends StandardScheme<getRechargeSuccessListV2_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getRechargeSuccessListV2_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.request = new QueryRechargeSuccessRequest();
                struct.request.read(iprot);
                struct.setRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getRechargeSuccessListV2_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.request != null) {
          oprot.writeFieldBegin(REQUEST_FIELD_DESC);
          struct.request.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getRechargeSuccessListV2_argsTupleSchemeFactory implements SchemeFactory {
      public getRechargeSuccessListV2_argsTupleScheme getScheme() {
        return new getRechargeSuccessListV2_argsTupleScheme();
      }
    }

    private static class getRechargeSuccessListV2_argsTupleScheme extends TupleScheme<getRechargeSuccessListV2_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getRechargeSuccessListV2_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetRequest()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetRequest()) {
          struct.request.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getRechargeSuccessListV2_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.request = new QueryRechargeSuccessRequest();
          struct.request.read(iprot);
          struct.setRequestIsSet(true);
        }
      }
    }

  }

  public static class getRechargeSuccessListV2_result implements org.apache.thrift.TBase<getRechargeSuccessListV2_result, getRechargeSuccessListV2_result._Fields>, java.io.Serializable, Cloneable, Comparable<getRechargeSuccessListV2_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getRechargeSuccessListV2_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getRechargeSuccessListV2_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getRechargeSuccessListV2_resultTupleSchemeFactory());
    }

    public ResultSuccessList success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT          , "ResultSuccessList")));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getRechargeSuccessListV2_result.class, metaDataMap);
    }

    public getRechargeSuccessListV2_result() {
    }

    public getRechargeSuccessListV2_result(
      ResultSuccessList success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getRechargeSuccessListV2_result(getRechargeSuccessListV2_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    public getRechargeSuccessListV2_result deepCopy() {
      return new getRechargeSuccessListV2_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ResultSuccessList getSuccess() {
      return this.success;
    }

    public getRechargeSuccessListV2_result setSuccess(ResultSuccessList success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ResultSuccessList)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getRechargeSuccessListV2_result)
        return this.equals((getRechargeSuccessListV2_result)that);
      return false;
    }

    public boolean equals(getRechargeSuccessListV2_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getRechargeSuccessListV2_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getRechargeSuccessListV2_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getRechargeSuccessListV2_resultStandardSchemeFactory implements SchemeFactory {
      public getRechargeSuccessListV2_resultStandardScheme getScheme() {
        return new getRechargeSuccessListV2_resultStandardScheme();
      }
    }

    private static class getRechargeSuccessListV2_resultStandardScheme extends StandardScheme<getRechargeSuccessListV2_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getRechargeSuccessListV2_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ResultSuccessList();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getRechargeSuccessListV2_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getRechargeSuccessListV2_resultTupleSchemeFactory implements SchemeFactory {
      public getRechargeSuccessListV2_resultTupleScheme getScheme() {
        return new getRechargeSuccessListV2_resultTupleScheme();
      }
    }

    private static class getRechargeSuccessListV2_resultTupleScheme extends TupleScheme<getRechargeSuccessListV2_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getRechargeSuccessListV2_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getRechargeSuccessListV2_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ResultSuccessList();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getFirstRechargeList_args implements org.apache.thrift.TBase<getFirstRechargeList_args, getFirstRechargeList_args._Fields>, java.io.Serializable, Cloneable, Comparable<getFirstRechargeList_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getFirstRechargeList_args");

    private static final org.apache.thrift.protocol.TField BEGIN_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("beginTime", org.apache.thrift.protocol.TType.I64, (short)1);
    private static final org.apache.thrift.protocol.TField END_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("endTime", org.apache.thrift.protocol.TType.I64, (short)2);
    private static final org.apache.thrift.protocol.TField LAST_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("lastId", org.apache.thrift.protocol.TType.I64, (short)3);
    private static final org.apache.thrift.protocol.TField PAGE_SIZE_FIELD_DESC = new org.apache.thrift.protocol.TField("pageSize", org.apache.thrift.protocol.TType.I32, (short)4);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getFirstRechargeList_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getFirstRechargeList_argsTupleSchemeFactory());
    }

    public long beginTime; // required
    public long endTime; // required
    public long lastId; // required
    public int pageSize; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      BEGIN_TIME((short)1, "beginTime"),
      END_TIME((short)2, "endTime"),
      LAST_ID((short)3, "lastId"),
      PAGE_SIZE((short)4, "pageSize");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // BEGIN_TIME
            return BEGIN_TIME;
          case 2: // END_TIME
            return END_TIME;
          case 3: // LAST_ID
            return LAST_ID;
          case 4: // PAGE_SIZE
            return PAGE_SIZE;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __BEGINTIME_ISSET_ID = 0;
    private static final int __ENDTIME_ISSET_ID = 1;
    private static final int __LASTID_ISSET_ID = 2;
    private static final int __PAGESIZE_ISSET_ID = 3;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.BEGIN_TIME, new org.apache.thrift.meta_data.FieldMetaData("beginTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.END_TIME, new org.apache.thrift.meta_data.FieldMetaData("endTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.LAST_ID, new org.apache.thrift.meta_data.FieldMetaData("lastId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.PAGE_SIZE, new org.apache.thrift.meta_data.FieldMetaData("pageSize", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getFirstRechargeList_args.class, metaDataMap);
    }

    public getFirstRechargeList_args() {
    }

    public getFirstRechargeList_args(
      long beginTime,
      long endTime,
      long lastId,
      int pageSize)
    {
      this();
      this.beginTime = beginTime;
      setBeginTimeIsSet(true);
      this.endTime = endTime;
      setEndTimeIsSet(true);
      this.lastId = lastId;
      setLastIdIsSet(true);
      this.pageSize = pageSize;
      setPageSizeIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getFirstRechargeList_args(getFirstRechargeList_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.beginTime = other.beginTime;
      this.endTime = other.endTime;
      this.lastId = other.lastId;
      this.pageSize = other.pageSize;
    }

    public getFirstRechargeList_args deepCopy() {
      return new getFirstRechargeList_args(this);
    }

    @Override
    public void clear() {
      setBeginTimeIsSet(false);
      this.beginTime = 0;
      setEndTimeIsSet(false);
      this.endTime = 0;
      setLastIdIsSet(false);
      this.lastId = 0;
      setPageSizeIsSet(false);
      this.pageSize = 0;
    }

    public long getBeginTime() {
      return this.beginTime;
    }

    public getFirstRechargeList_args setBeginTime(long beginTime) {
      this.beginTime = beginTime;
      setBeginTimeIsSet(true);
      return this;
    }

    public void unsetBeginTime() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __BEGINTIME_ISSET_ID);
    }

    /** Returns true if field beginTime is set (has been assigned a value) and false otherwise */
    public boolean isSetBeginTime() {
      return EncodingUtils.testBit(__isset_bitfield, __BEGINTIME_ISSET_ID);
    }

    public void setBeginTimeIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __BEGINTIME_ISSET_ID, value);
    }

    public long getEndTime() {
      return this.endTime;
    }

    public getFirstRechargeList_args setEndTime(long endTime) {
      this.endTime = endTime;
      setEndTimeIsSet(true);
      return this;
    }

    public void unsetEndTime() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ENDTIME_ISSET_ID);
    }

    /** Returns true if field endTime is set (has been assigned a value) and false otherwise */
    public boolean isSetEndTime() {
      return EncodingUtils.testBit(__isset_bitfield, __ENDTIME_ISSET_ID);
    }

    public void setEndTimeIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ENDTIME_ISSET_ID, value);
    }

    public long getLastId() {
      return this.lastId;
    }

    public getFirstRechargeList_args setLastId(long lastId) {
      this.lastId = lastId;
      setLastIdIsSet(true);
      return this;
    }

    public void unsetLastId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __LASTID_ISSET_ID);
    }

    /** Returns true if field lastId is set (has been assigned a value) and false otherwise */
    public boolean isSetLastId() {
      return EncodingUtils.testBit(__isset_bitfield, __LASTID_ISSET_ID);
    }

    public void setLastIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __LASTID_ISSET_ID, value);
    }

    public int getPageSize() {
      return this.pageSize;
    }

    public getFirstRechargeList_args setPageSize(int pageSize) {
      this.pageSize = pageSize;
      setPageSizeIsSet(true);
      return this;
    }

    public void unsetPageSize() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
    }

    /** Returns true if field pageSize is set (has been assigned a value) and false otherwise */
    public boolean isSetPageSize() {
      return EncodingUtils.testBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
    }

    public void setPageSizeIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGESIZE_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case BEGIN_TIME:
        if (value == null) {
          unsetBeginTime();
        } else {
          setBeginTime((Long)value);
        }
        break;

      case END_TIME:
        if (value == null) {
          unsetEndTime();
        } else {
          setEndTime((Long)value);
        }
        break;

      case LAST_ID:
        if (value == null) {
          unsetLastId();
        } else {
          setLastId((Long)value);
        }
        break;

      case PAGE_SIZE:
        if (value == null) {
          unsetPageSize();
        } else {
          setPageSize((Integer)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case BEGIN_TIME:
        return getBeginTime();

      case END_TIME:
        return getEndTime();

      case LAST_ID:
        return getLastId();

      case PAGE_SIZE:
        return getPageSize();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case BEGIN_TIME:
        return isSetBeginTime();
      case END_TIME:
        return isSetEndTime();
      case LAST_ID:
        return isSetLastId();
      case PAGE_SIZE:
        return isSetPageSize();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getFirstRechargeList_args)
        return this.equals((getFirstRechargeList_args)that);
      return false;
    }

    public boolean equals(getFirstRechargeList_args that) {
      if (that == null)
        return false;

      boolean this_present_beginTime = true;
      boolean that_present_beginTime = true;
      if (this_present_beginTime || that_present_beginTime) {
        if (!(this_present_beginTime && that_present_beginTime))
          return false;
        if (this.beginTime != that.beginTime)
          return false;
      }

      boolean this_present_endTime = true;
      boolean that_present_endTime = true;
      if (this_present_endTime || that_present_endTime) {
        if (!(this_present_endTime && that_present_endTime))
          return false;
        if (this.endTime != that.endTime)
          return false;
      }

      boolean this_present_lastId = true;
      boolean that_present_lastId = true;
      if (this_present_lastId || that_present_lastId) {
        if (!(this_present_lastId && that_present_lastId))
          return false;
        if (this.lastId != that.lastId)
          return false;
      }

      boolean this_present_pageSize = true;
      boolean that_present_pageSize = true;
      if (this_present_pageSize || that_present_pageSize) {
        if (!(this_present_pageSize && that_present_pageSize))
          return false;
        if (this.pageSize != that.pageSize)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_beginTime = true;
      list.add(present_beginTime);
      if (present_beginTime)
        list.add(beginTime);

      boolean present_endTime = true;
      list.add(present_endTime);
      if (present_endTime)
        list.add(endTime);

      boolean present_lastId = true;
      list.add(present_lastId);
      if (present_lastId)
        list.add(lastId);

      boolean present_pageSize = true;
      list.add(present_pageSize);
      if (present_pageSize)
        list.add(pageSize);

      return list.hashCode();
    }

    @Override
    public int compareTo(getFirstRechargeList_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetBeginTime()).compareTo(other.isSetBeginTime());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetBeginTime()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.beginTime, other.beginTime);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetEndTime()).compareTo(other.isSetEndTime());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetEndTime()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.endTime, other.endTime);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetLastId()).compareTo(other.isSetLastId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetLastId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.lastId, other.lastId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetPageSize()).compareTo(other.isSetPageSize());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetPageSize()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pageSize, other.pageSize);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getFirstRechargeList_args(");
      boolean first = true;

      sb.append("beginTime:");
      sb.append(this.beginTime);
      first = false;
      if (!first) sb.append(", ");
      sb.append("endTime:");
      sb.append(this.endTime);
      first = false;
      if (!first) sb.append(", ");
      sb.append("lastId:");
      sb.append(this.lastId);
      first = false;
      if (!first) sb.append(", ");
      sb.append("pageSize:");
      sb.append(this.pageSize);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getFirstRechargeList_argsStandardSchemeFactory implements SchemeFactory {
      public getFirstRechargeList_argsStandardScheme getScheme() {
        return new getFirstRechargeList_argsStandardScheme();
      }
    }

    private static class getFirstRechargeList_argsStandardScheme extends StandardScheme<getFirstRechargeList_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getFirstRechargeList_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // BEGIN_TIME
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.beginTime = iprot.readI64();
                struct.setBeginTimeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // END_TIME
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.endTime = iprot.readI64();
                struct.setEndTimeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // LAST_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.lastId = iprot.readI64();
                struct.setLastIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 4: // PAGE_SIZE
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.pageSize = iprot.readI32();
                struct.setPageSizeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getFirstRechargeList_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(BEGIN_TIME_FIELD_DESC);
        oprot.writeI64(struct.beginTime);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(END_TIME_FIELD_DESC);
        oprot.writeI64(struct.endTime);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(LAST_ID_FIELD_DESC);
        oprot.writeI64(struct.lastId);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(PAGE_SIZE_FIELD_DESC);
        oprot.writeI32(struct.pageSize);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getFirstRechargeList_argsTupleSchemeFactory implements SchemeFactory {
      public getFirstRechargeList_argsTupleScheme getScheme() {
        return new getFirstRechargeList_argsTupleScheme();
      }
    }

    private static class getFirstRechargeList_argsTupleScheme extends TupleScheme<getFirstRechargeList_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getFirstRechargeList_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetBeginTime()) {
          optionals.set(0);
        }
        if (struct.isSetEndTime()) {
          optionals.set(1);
        }
        if (struct.isSetLastId()) {
          optionals.set(2);
        }
        if (struct.isSetPageSize()) {
          optionals.set(3);
        }
        oprot.writeBitSet(optionals, 4);
        if (struct.isSetBeginTime()) {
          oprot.writeI64(struct.beginTime);
        }
        if (struct.isSetEndTime()) {
          oprot.writeI64(struct.endTime);
        }
        if (struct.isSetLastId()) {
          oprot.writeI64(struct.lastId);
        }
        if (struct.isSetPageSize()) {
          oprot.writeI32(struct.pageSize);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getFirstRechargeList_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(4);
        if (incoming.get(0)) {
          struct.beginTime = iprot.readI64();
          struct.setBeginTimeIsSet(true);
        }
        if (incoming.get(1)) {
          struct.endTime = iprot.readI64();
          struct.setEndTimeIsSet(true);
        }
        if (incoming.get(2)) {
          struct.lastId = iprot.readI64();
          struct.setLastIdIsSet(true);
        }
        if (incoming.get(3)) {
          struct.pageSize = iprot.readI32();
          struct.setPageSizeIsSet(true);
        }
      }
    }

  }

  public static class getFirstRechargeList_result implements org.apache.thrift.TBase<getFirstRechargeList_result, getFirstRechargeList_result._Fields>, java.io.Serializable, Cloneable, Comparable<getFirstRechargeList_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getFirstRechargeList_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getFirstRechargeList_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getFirstRechargeList_resultTupleSchemeFactory());
    }

    public ResultList success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT          , "ResultList")));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getFirstRechargeList_result.class, metaDataMap);
    }

    public getFirstRechargeList_result() {
    }

    public getFirstRechargeList_result(
      ResultList success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getFirstRechargeList_result(getFirstRechargeList_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    public getFirstRechargeList_result deepCopy() {
      return new getFirstRechargeList_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ResultList getSuccess() {
      return this.success;
    }

    public getFirstRechargeList_result setSuccess(ResultList success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ResultList)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getFirstRechargeList_result)
        return this.equals((getFirstRechargeList_result)that);
      return false;
    }

    public boolean equals(getFirstRechargeList_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getFirstRechargeList_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getFirstRechargeList_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getFirstRechargeList_resultStandardSchemeFactory implements SchemeFactory {
      public getFirstRechargeList_resultStandardScheme getScheme() {
        return new getFirstRechargeList_resultStandardScheme();
      }
    }

    private static class getFirstRechargeList_resultStandardScheme extends StandardScheme<getFirstRechargeList_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getFirstRechargeList_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ResultList();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getFirstRechargeList_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getFirstRechargeList_resultTupleSchemeFactory implements SchemeFactory {
      public getFirstRechargeList_resultTupleScheme getScheme() {
        return new getFirstRechargeList_resultTupleScheme();
      }
    }

    private static class getFirstRechargeList_resultTupleScheme extends TupleScheme<getFirstRechargeList_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getFirstRechargeList_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getFirstRechargeList_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ResultList();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getRechargeInfo_args implements org.apache.thrift.TBase<getRechargeInfo_args, getRechargeInfo_args._Fields>, java.io.Serializable, Cloneable, Comparable<getRechargeInfo_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getRechargeInfo_args");

    private static final org.apache.thrift.protocol.TField RECHARGE_ORDER_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("rechargeOrderNum", org.apache.thrift.protocol.TType.STRING, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getRechargeInfo_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getRechargeInfo_argsTupleSchemeFactory());
    }

    public String rechargeOrderNum; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      RECHARGE_ORDER_NUM((short)1, "rechargeOrderNum");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // RECHARGE_ORDER_NUM
            return RECHARGE_ORDER_NUM;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.RECHARGE_ORDER_NUM, new org.apache.thrift.meta_data.FieldMetaData("rechargeOrderNum", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getRechargeInfo_args.class, metaDataMap);
    }

    public getRechargeInfo_args() {
    }

    public getRechargeInfo_args(
      String rechargeOrderNum)
    {
      this();
      this.rechargeOrderNum = rechargeOrderNum;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getRechargeInfo_args(getRechargeInfo_args other) {
      if (other.isSetRechargeOrderNum()) {
        this.rechargeOrderNum = other.rechargeOrderNum;
      }
    }

    public getRechargeInfo_args deepCopy() {
      return new getRechargeInfo_args(this);
    }

    @Override
    public void clear() {
      this.rechargeOrderNum = null;
    }

    public String getRechargeOrderNum() {
      return this.rechargeOrderNum;
    }

    public getRechargeInfo_args setRechargeOrderNum(String rechargeOrderNum) {
      this.rechargeOrderNum = rechargeOrderNum;
      return this;
    }

    public void unsetRechargeOrderNum() {
      this.rechargeOrderNum = null;
    }

    /** Returns true if field rechargeOrderNum is set (has been assigned a value) and false otherwise */
    public boolean isSetRechargeOrderNum() {
      return this.rechargeOrderNum != null;
    }

    public void setRechargeOrderNumIsSet(boolean value) {
      if (!value) {
        this.rechargeOrderNum = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case RECHARGE_ORDER_NUM:
        if (value == null) {
          unsetRechargeOrderNum();
        } else {
          setRechargeOrderNum((String)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case RECHARGE_ORDER_NUM:
        return getRechargeOrderNum();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case RECHARGE_ORDER_NUM:
        return isSetRechargeOrderNum();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getRechargeInfo_args)
        return this.equals((getRechargeInfo_args)that);
      return false;
    }

    public boolean equals(getRechargeInfo_args that) {
      if (that == null)
        return false;

      boolean this_present_rechargeOrderNum = true && this.isSetRechargeOrderNum();
      boolean that_present_rechargeOrderNum = true && that.isSetRechargeOrderNum();
      if (this_present_rechargeOrderNum || that_present_rechargeOrderNum) {
        if (!(this_present_rechargeOrderNum && that_present_rechargeOrderNum))
          return false;
        if (!this.rechargeOrderNum.equals(that.rechargeOrderNum))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_rechargeOrderNum = true && (isSetRechargeOrderNum());
      list.add(present_rechargeOrderNum);
      if (present_rechargeOrderNum)
        list.add(rechargeOrderNum);

      return list.hashCode();
    }

    @Override
    public int compareTo(getRechargeInfo_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRechargeOrderNum()).compareTo(other.isSetRechargeOrderNum());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRechargeOrderNum()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rechargeOrderNum, other.rechargeOrderNum);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getRechargeInfo_args(");
      boolean first = true;

      sb.append("rechargeOrderNum:");
      if (this.rechargeOrderNum == null) {
        sb.append("null");
      } else {
        sb.append(this.rechargeOrderNum);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getRechargeInfo_argsStandardSchemeFactory implements SchemeFactory {
      public getRechargeInfo_argsStandardScheme getScheme() {
        return new getRechargeInfo_argsStandardScheme();
      }
    }

    private static class getRechargeInfo_argsStandardScheme extends StandardScheme<getRechargeInfo_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getRechargeInfo_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // RECHARGE_ORDER_NUM
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.rechargeOrderNum = iprot.readString();
                struct.setRechargeOrderNumIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getRechargeInfo_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.rechargeOrderNum != null) {
          oprot.writeFieldBegin(RECHARGE_ORDER_NUM_FIELD_DESC);
          oprot.writeString(struct.rechargeOrderNum);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getRechargeInfo_argsTupleSchemeFactory implements SchemeFactory {
      public getRechargeInfo_argsTupleScheme getScheme() {
        return new getRechargeInfo_argsTupleScheme();
      }
    }

    private static class getRechargeInfo_argsTupleScheme extends TupleScheme<getRechargeInfo_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getRechargeInfo_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetRechargeOrderNum()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetRechargeOrderNum()) {
          oprot.writeString(struct.rechargeOrderNum);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getRechargeInfo_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.rechargeOrderNum = iprot.readString();
          struct.setRechargeOrderNumIsSet(true);
        }
      }
    }

  }

  public static class getRechargeInfo_result implements org.apache.thrift.TBase<getRechargeInfo_result, getRechargeInfo_result._Fields>, java.io.Serializable, Cloneable, Comparable<getRechargeInfo_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getRechargeInfo_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getRechargeInfo_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getRechargeInfo_resultTupleSchemeFactory());
    }

    public ResultInfo success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT          , "ResultInfo")));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getRechargeInfo_result.class, metaDataMap);
    }

    public getRechargeInfo_result() {
    }

    public getRechargeInfo_result(
      ResultInfo success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getRechargeInfo_result(getRechargeInfo_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    public getRechargeInfo_result deepCopy() {
      return new getRechargeInfo_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ResultInfo getSuccess() {
      return this.success;
    }

    public getRechargeInfo_result setSuccess(ResultInfo success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ResultInfo)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getRechargeInfo_result)
        return this.equals((getRechargeInfo_result)that);
      return false;
    }

    public boolean equals(getRechargeInfo_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getRechargeInfo_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getRechargeInfo_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getRechargeInfo_resultStandardSchemeFactory implements SchemeFactory {
      public getRechargeInfo_resultStandardScheme getScheme() {
        return new getRechargeInfo_resultStandardScheme();
      }
    }

    private static class getRechargeInfo_resultStandardScheme extends StandardScheme<getRechargeInfo_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getRechargeInfo_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ResultInfo();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getRechargeInfo_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getRechargeInfo_resultTupleSchemeFactory implements SchemeFactory {
      public getRechargeInfo_resultTupleScheme getScheme() {
        return new getRechargeInfo_resultTupleScheme();
      }
    }

    private static class getRechargeInfo_resultTupleScheme extends TupleScheme<getRechargeInfo_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getRechargeInfo_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getRechargeInfo_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ResultInfo();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class isAgent_args implements org.apache.thrift.TBase<isAgent_args, isAgent_args._Fields>, java.io.Serializable, Cloneable, Comparable<isAgent_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isAgent_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isAgent_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isAgent_argsTupleSchemeFactory());
    }

    public long kugouId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isAgent_args.class, metaDataMap);
    }

    public isAgent_args() {
    }

    public isAgent_args(
      long kugouId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isAgent_args(isAgent_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
    }

    public isAgent_args deepCopy() {
      return new isAgent_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public isAgent_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isAgent_args)
        return this.equals((isAgent_args)that);
      return false;
    }

    public boolean equals(isAgent_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      return list.hashCode();
    }

    @Override
    public int compareTo(isAgent_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isAgent_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isAgent_argsStandardSchemeFactory implements SchemeFactory {
      public isAgent_argsStandardScheme getScheme() {
        return new isAgent_argsStandardScheme();
      }
    }

    private static class isAgent_argsStandardScheme extends StandardScheme<isAgent_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isAgent_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isAgent_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isAgent_argsTupleSchemeFactory implements SchemeFactory {
      public isAgent_argsTupleScheme getScheme() {
        return new isAgent_argsTupleScheme();
      }
    }

    private static class isAgent_argsTupleScheme extends TupleScheme<isAgent_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isAgent_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetKugouId()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetKugouId()) {
          oprot.writeI64(struct.kugouId);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isAgent_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.kugouId = iprot.readI64();
          struct.setKugouIdIsSet(true);
        }
      }
    }

  }

  public static class isAgent_result implements org.apache.thrift.TBase<isAgent_result, isAgent_result._Fields>, java.io.Serializable, Cloneable, Comparable<isAgent_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isAgent_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isAgent_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isAgent_resultTupleSchemeFactory());
    }

    public AgentInfo success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT          , "AgentInfo")));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isAgent_result.class, metaDataMap);
    }

    public isAgent_result() {
    }

    public isAgent_result(
      AgentInfo success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isAgent_result(isAgent_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    public isAgent_result deepCopy() {
      return new isAgent_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public AgentInfo getSuccess() {
      return this.success;
    }

    public isAgent_result setSuccess(AgentInfo success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((AgentInfo)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isAgent_result)
        return this.equals((isAgent_result)that);
      return false;
    }

    public boolean equals(isAgent_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(isAgent_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isAgent_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isAgent_resultStandardSchemeFactory implements SchemeFactory {
      public isAgent_resultStandardScheme getScheme() {
        return new isAgent_resultStandardScheme();
      }
    }

    private static class isAgent_resultStandardScheme extends StandardScheme<isAgent_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isAgent_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new AgentInfo();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isAgent_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isAgent_resultTupleSchemeFactory implements SchemeFactory {
      public isAgent_resultTupleScheme getScheme() {
        return new isAgent_resultTupleScheme();
      }
    }

    private static class isAgent_resultTupleScheme extends TupleScheme<isAgent_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isAgent_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isAgent_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new AgentInfo();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class isTmallFirstRecharge_args implements org.apache.thrift.TBase<isTmallFirstRecharge_args, isTmallFirstRecharge_args._Fields>, java.io.Serializable, Cloneable, Comparable<isTmallFirstRecharge_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isTmallFirstRecharge_args");

    private static final org.apache.thrift.protocol.TField RECHARGE_ORDER_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("rechargeOrderNum", org.apache.thrift.protocol.TType.STRING, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isTmallFirstRecharge_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isTmallFirstRecharge_argsTupleSchemeFactory());
    }

    public String rechargeOrderNum; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      RECHARGE_ORDER_NUM((short)1, "rechargeOrderNum");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // RECHARGE_ORDER_NUM
            return RECHARGE_ORDER_NUM;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.RECHARGE_ORDER_NUM, new org.apache.thrift.meta_data.FieldMetaData("rechargeOrderNum", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isTmallFirstRecharge_args.class, metaDataMap);
    }

    public isTmallFirstRecharge_args() {
    }

    public isTmallFirstRecharge_args(
      String rechargeOrderNum)
    {
      this();
      this.rechargeOrderNum = rechargeOrderNum;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isTmallFirstRecharge_args(isTmallFirstRecharge_args other) {
      if (other.isSetRechargeOrderNum()) {
        this.rechargeOrderNum = other.rechargeOrderNum;
      }
    }

    public isTmallFirstRecharge_args deepCopy() {
      return new isTmallFirstRecharge_args(this);
    }

    @Override
    public void clear() {
      this.rechargeOrderNum = null;
    }

    public String getRechargeOrderNum() {
      return this.rechargeOrderNum;
    }

    public isTmallFirstRecharge_args setRechargeOrderNum(String rechargeOrderNum) {
      this.rechargeOrderNum = rechargeOrderNum;
      return this;
    }

    public void unsetRechargeOrderNum() {
      this.rechargeOrderNum = null;
    }

    /** Returns true if field rechargeOrderNum is set (has been assigned a value) and false otherwise */
    public boolean isSetRechargeOrderNum() {
      return this.rechargeOrderNum != null;
    }

    public void setRechargeOrderNumIsSet(boolean value) {
      if (!value) {
        this.rechargeOrderNum = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case RECHARGE_ORDER_NUM:
        if (value == null) {
          unsetRechargeOrderNum();
        } else {
          setRechargeOrderNum((String)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case RECHARGE_ORDER_NUM:
        return getRechargeOrderNum();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case RECHARGE_ORDER_NUM:
        return isSetRechargeOrderNum();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isTmallFirstRecharge_args)
        return this.equals((isTmallFirstRecharge_args)that);
      return false;
    }

    public boolean equals(isTmallFirstRecharge_args that) {
      if (that == null)
        return false;

      boolean this_present_rechargeOrderNum = true && this.isSetRechargeOrderNum();
      boolean that_present_rechargeOrderNum = true && that.isSetRechargeOrderNum();
      if (this_present_rechargeOrderNum || that_present_rechargeOrderNum) {
        if (!(this_present_rechargeOrderNum && that_present_rechargeOrderNum))
          return false;
        if (!this.rechargeOrderNum.equals(that.rechargeOrderNum))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_rechargeOrderNum = true && (isSetRechargeOrderNum());
      list.add(present_rechargeOrderNum);
      if (present_rechargeOrderNum)
        list.add(rechargeOrderNum);

      return list.hashCode();
    }

    @Override
    public int compareTo(isTmallFirstRecharge_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRechargeOrderNum()).compareTo(other.isSetRechargeOrderNum());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRechargeOrderNum()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rechargeOrderNum, other.rechargeOrderNum);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isTmallFirstRecharge_args(");
      boolean first = true;

      sb.append("rechargeOrderNum:");
      if (this.rechargeOrderNum == null) {
        sb.append("null");
      } else {
        sb.append(this.rechargeOrderNum);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isTmallFirstRecharge_argsStandardSchemeFactory implements SchemeFactory {
      public isTmallFirstRecharge_argsStandardScheme getScheme() {
        return new isTmallFirstRecharge_argsStandardScheme();
      }
    }

    private static class isTmallFirstRecharge_argsStandardScheme extends StandardScheme<isTmallFirstRecharge_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isTmallFirstRecharge_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // RECHARGE_ORDER_NUM
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.rechargeOrderNum = iprot.readString();
                struct.setRechargeOrderNumIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isTmallFirstRecharge_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.rechargeOrderNum != null) {
          oprot.writeFieldBegin(RECHARGE_ORDER_NUM_FIELD_DESC);
          oprot.writeString(struct.rechargeOrderNum);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isTmallFirstRecharge_argsTupleSchemeFactory implements SchemeFactory {
      public isTmallFirstRecharge_argsTupleScheme getScheme() {
        return new isTmallFirstRecharge_argsTupleScheme();
      }
    }

    private static class isTmallFirstRecharge_argsTupleScheme extends TupleScheme<isTmallFirstRecharge_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isTmallFirstRecharge_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetRechargeOrderNum()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetRechargeOrderNum()) {
          oprot.writeString(struct.rechargeOrderNum);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isTmallFirstRecharge_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.rechargeOrderNum = iprot.readString();
          struct.setRechargeOrderNumIsSet(true);
        }
      }
    }

  }

  public static class isTmallFirstRecharge_result implements org.apache.thrift.TBase<isTmallFirstRecharge_result, isTmallFirstRecharge_result._Fields>, java.io.Serializable, Cloneable, Comparable<isTmallFirstRecharge_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("isTmallFirstRecharge_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new isTmallFirstRecharge_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new isTmallFirstRecharge_resultTupleSchemeFactory());
    }

    public TmallFirstRechargeInfo success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT          , "TmallFirstRechargeInfo")));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(isTmallFirstRecharge_result.class, metaDataMap);
    }

    public isTmallFirstRecharge_result() {
    }

    public isTmallFirstRecharge_result(
      TmallFirstRechargeInfo success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public isTmallFirstRecharge_result(isTmallFirstRecharge_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    public isTmallFirstRecharge_result deepCopy() {
      return new isTmallFirstRecharge_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public TmallFirstRechargeInfo getSuccess() {
      return this.success;
    }

    public isTmallFirstRecharge_result setSuccess(TmallFirstRechargeInfo success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((TmallFirstRechargeInfo)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof isTmallFirstRecharge_result)
        return this.equals((isTmallFirstRecharge_result)that);
      return false;
    }

    public boolean equals(isTmallFirstRecharge_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(isTmallFirstRecharge_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("isTmallFirstRecharge_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class isTmallFirstRecharge_resultStandardSchemeFactory implements SchemeFactory {
      public isTmallFirstRecharge_resultStandardScheme getScheme() {
        return new isTmallFirstRecharge_resultStandardScheme();
      }
    }

    private static class isTmallFirstRecharge_resultStandardScheme extends StandardScheme<isTmallFirstRecharge_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, isTmallFirstRecharge_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new TmallFirstRechargeInfo();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, isTmallFirstRecharge_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class isTmallFirstRecharge_resultTupleSchemeFactory implements SchemeFactory {
      public isTmallFirstRecharge_resultTupleScheme getScheme() {
        return new isTmallFirstRecharge_resultTupleScheme();
      }
    }

    private static class isTmallFirstRecharge_resultTupleScheme extends TupleScheme<isTmallFirstRecharge_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, isTmallFirstRecharge_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, isTmallFirstRecharge_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new TmallFirstRechargeInfo();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getRechargeInfoFromMaster_args implements org.apache.thrift.TBase<getRechargeInfoFromMaster_args, getRechargeInfoFromMaster_args._Fields>, java.io.Serializable, Cloneable, Comparable<getRechargeInfoFromMaster_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getRechargeInfoFromMaster_args");

    private static final org.apache.thrift.protocol.TField RECHARGE_ORDER_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("rechargeOrderNum", org.apache.thrift.protocol.TType.STRING, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getRechargeInfoFromMaster_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getRechargeInfoFromMaster_argsTupleSchemeFactory());
    }

    public String rechargeOrderNum; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      RECHARGE_ORDER_NUM((short)1, "rechargeOrderNum");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // RECHARGE_ORDER_NUM
            return RECHARGE_ORDER_NUM;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.RECHARGE_ORDER_NUM, new org.apache.thrift.meta_data.FieldMetaData("rechargeOrderNum", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getRechargeInfoFromMaster_args.class, metaDataMap);
    }

    public getRechargeInfoFromMaster_args() {
    }

    public getRechargeInfoFromMaster_args(
      String rechargeOrderNum)
    {
      this();
      this.rechargeOrderNum = rechargeOrderNum;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getRechargeInfoFromMaster_args(getRechargeInfoFromMaster_args other) {
      if (other.isSetRechargeOrderNum()) {
        this.rechargeOrderNum = other.rechargeOrderNum;
      }
    }

    public getRechargeInfoFromMaster_args deepCopy() {
      return new getRechargeInfoFromMaster_args(this);
    }

    @Override
    public void clear() {
      this.rechargeOrderNum = null;
    }

    public String getRechargeOrderNum() {
      return this.rechargeOrderNum;
    }

    public getRechargeInfoFromMaster_args setRechargeOrderNum(String rechargeOrderNum) {
      this.rechargeOrderNum = rechargeOrderNum;
      return this;
    }

    public void unsetRechargeOrderNum() {
      this.rechargeOrderNum = null;
    }

    /** Returns true if field rechargeOrderNum is set (has been assigned a value) and false otherwise */
    public boolean isSetRechargeOrderNum() {
      return this.rechargeOrderNum != null;
    }

    public void setRechargeOrderNumIsSet(boolean value) {
      if (!value) {
        this.rechargeOrderNum = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case RECHARGE_ORDER_NUM:
        if (value == null) {
          unsetRechargeOrderNum();
        } else {
          setRechargeOrderNum((String)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case RECHARGE_ORDER_NUM:
        return getRechargeOrderNum();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case RECHARGE_ORDER_NUM:
        return isSetRechargeOrderNum();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getRechargeInfoFromMaster_args)
        return this.equals((getRechargeInfoFromMaster_args)that);
      return false;
    }

    public boolean equals(getRechargeInfoFromMaster_args that) {
      if (that == null)
        return false;

      boolean this_present_rechargeOrderNum = true && this.isSetRechargeOrderNum();
      boolean that_present_rechargeOrderNum = true && that.isSetRechargeOrderNum();
      if (this_present_rechargeOrderNum || that_present_rechargeOrderNum) {
        if (!(this_present_rechargeOrderNum && that_present_rechargeOrderNum))
          return false;
        if (!this.rechargeOrderNum.equals(that.rechargeOrderNum))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_rechargeOrderNum = true && (isSetRechargeOrderNum());
      list.add(present_rechargeOrderNum);
      if (present_rechargeOrderNum)
        list.add(rechargeOrderNum);

      return list.hashCode();
    }

    @Override
    public int compareTo(getRechargeInfoFromMaster_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRechargeOrderNum()).compareTo(other.isSetRechargeOrderNum());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRechargeOrderNum()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rechargeOrderNum, other.rechargeOrderNum);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getRechargeInfoFromMaster_args(");
      boolean first = true;

      sb.append("rechargeOrderNum:");
      if (this.rechargeOrderNum == null) {
        sb.append("null");
      } else {
        sb.append(this.rechargeOrderNum);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getRechargeInfoFromMaster_argsStandardSchemeFactory implements SchemeFactory {
      public getRechargeInfoFromMaster_argsStandardScheme getScheme() {
        return new getRechargeInfoFromMaster_argsStandardScheme();
      }
    }

    private static class getRechargeInfoFromMaster_argsStandardScheme extends StandardScheme<getRechargeInfoFromMaster_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getRechargeInfoFromMaster_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // RECHARGE_ORDER_NUM
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.rechargeOrderNum = iprot.readString();
                struct.setRechargeOrderNumIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getRechargeInfoFromMaster_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.rechargeOrderNum != null) {
          oprot.writeFieldBegin(RECHARGE_ORDER_NUM_FIELD_DESC);
          oprot.writeString(struct.rechargeOrderNum);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getRechargeInfoFromMaster_argsTupleSchemeFactory implements SchemeFactory {
      public getRechargeInfoFromMaster_argsTupleScheme getScheme() {
        return new getRechargeInfoFromMaster_argsTupleScheme();
      }
    }

    private static class getRechargeInfoFromMaster_argsTupleScheme extends TupleScheme<getRechargeInfoFromMaster_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getRechargeInfoFromMaster_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetRechargeOrderNum()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetRechargeOrderNum()) {
          oprot.writeString(struct.rechargeOrderNum);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getRechargeInfoFromMaster_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.rechargeOrderNum = iprot.readString();
          struct.setRechargeOrderNumIsSet(true);
        }
      }
    }

  }

  public static class getRechargeInfoFromMaster_result implements org.apache.thrift.TBase<getRechargeInfoFromMaster_result, getRechargeInfoFromMaster_result._Fields>, java.io.Serializable, Cloneable, Comparable<getRechargeInfoFromMaster_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getRechargeInfoFromMaster_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getRechargeInfoFromMaster_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getRechargeInfoFromMaster_resultTupleSchemeFactory());
    }

    public ResultInfo success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT          , "ResultInfo")));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getRechargeInfoFromMaster_result.class, metaDataMap);
    }

    public getRechargeInfoFromMaster_result() {
    }

    public getRechargeInfoFromMaster_result(
      ResultInfo success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getRechargeInfoFromMaster_result(getRechargeInfoFromMaster_result other) {
      if (other.isSetSuccess()) {
        this.success = other.success;
      }
    }

    public getRechargeInfoFromMaster_result deepCopy() {
      return new getRechargeInfoFromMaster_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ResultInfo getSuccess() {
      return this.success;
    }

    public getRechargeInfoFromMaster_result setSuccess(ResultInfo success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ResultInfo)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getRechargeInfoFromMaster_result)
        return this.equals((getRechargeInfoFromMaster_result)that);
      return false;
    }

    public boolean equals(getRechargeInfoFromMaster_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getRechargeInfoFromMaster_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getRechargeInfoFromMaster_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getRechargeInfoFromMaster_resultStandardSchemeFactory implements SchemeFactory {
      public getRechargeInfoFromMaster_resultStandardScheme getScheme() {
        return new getRechargeInfoFromMaster_resultStandardScheme();
      }
    }

    private static class getRechargeInfoFromMaster_resultStandardScheme extends StandardScheme<getRechargeInfoFromMaster_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getRechargeInfoFromMaster_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ResultInfo();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getRechargeInfoFromMaster_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getRechargeInfoFromMaster_resultTupleSchemeFactory implements SchemeFactory {
      public getRechargeInfoFromMaster_resultTupleScheme getScheme() {
        return new getRechargeInfoFromMaster_resultTupleScheme();
      }
    }

    private static class getRechargeInfoFromMaster_resultTupleScheme extends TupleScheme<getRechargeInfoFromMaster_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getRechargeInfoFromMaster_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getRechargeInfoFromMaster_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ResultInfo();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

}
