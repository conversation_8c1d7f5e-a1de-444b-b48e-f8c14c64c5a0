/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.finance.goods.gift;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2020-08-19")
public class SimpleGiftInfo implements org.apache.thrift.TBase<SimpleGiftInfo, SimpleGiftInfo._Fields>, java.io.Serializable, Cloneable, Comparable<SimpleGiftInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("SimpleGiftInfo");

  private static final org.apache.thrift.protocol.TField ID_FIELD_DESC = new org.apache.thrift.protocol.TField("id", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("name", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField PRICE_FIELD_DESC = new org.apache.thrift.protocol.TField("price", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField EXCHANGE_FIELD_DESC = new org.apache.thrift.protocol.TField("exchange", org.apache.thrift.protocol.TType.DOUBLE, (short)4);
  private static final org.apache.thrift.protocol.TField CATEGORY_FIELD_DESC = new org.apache.thrift.protocol.TField("category", org.apache.thrift.protocol.TType.I32, (short)5);
  private static final org.apache.thrift.protocol.TField STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("status", org.apache.thrift.protocol.TType.I32, (short)6);
  private static final org.apache.thrift.protocol.TField URL_FIELD_DESC = new org.apache.thrift.protocol.TField("url", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField PIC_FIELD_DESC = new org.apache.thrift.protocol.TField("pic", org.apache.thrift.protocol.TType.STRING, (short)8);
  private static final org.apache.thrift.protocol.TField MOBILE_IMAGE_FIELD_DESC = new org.apache.thrift.protocol.TField("mobileImage", org.apache.thrift.protocol.TType.STRING, (short)9);
  private static final org.apache.thrift.protocol.TField ORIG_PRICE_FIELD_DESC = new org.apache.thrift.protocol.TField("origPrice", org.apache.thrift.protocol.TType.I32, (short)10);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new SimpleGiftInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new SimpleGiftInfoTupleSchemeFactory());
  }

  /**
   * id
   */
  public int id; // optional
  /**
   * name
   */
  public String name; // optional
  /**
   * price 价格
   */
  public int price; // optional
  /**
   * exchange 分成
   */
  public double exchange; // optional
  /**
   * category 礼物分类
   */
  public int category; // optional
  /**
   * status 上下架，仓库礼物下架也可送
   */
  public int status; // optional
  /**
   * url
   */
  public String url; // optional
  /**
   * pic
   */
  public String pic; // optional
  /**
   * 手机端礼物图片
   */
  public String mobileImage; // optional
  /**
   * 原价 如果不打折此字段为0 如果打折此字段必须大于单价price
   */
  public int origPrice; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * id
     */
    ID((short)1, "id"),
    /**
     * name
     */
    NAME((short)2, "name"),
    /**
     * price 价格
     */
    PRICE((short)3, "price"),
    /**
     * exchange 分成
     */
    EXCHANGE((short)4, "exchange"),
    /**
     * category 礼物分类
     */
    CATEGORY((short)5, "category"),
    /**
     * status 上下架，仓库礼物下架也可送
     */
    STATUS((short)6, "status"),
    /**
     * url
     */
    URL((short)7, "url"),
    /**
     * pic
     */
    PIC((short)8, "pic"),
    /**
     * 手机端礼物图片
     */
    MOBILE_IMAGE((short)9, "mobileImage"),
    /**
     * 原价 如果不打折此字段为0 如果打折此字段必须大于单价price
     */
    ORIG_PRICE((short)10, "origPrice");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ID
          return ID;
        case 2: // NAME
          return NAME;
        case 3: // PRICE
          return PRICE;
        case 4: // EXCHANGE
          return EXCHANGE;
        case 5: // CATEGORY
          return CATEGORY;
        case 6: // STATUS
          return STATUS;
        case 7: // URL
          return URL;
        case 8: // PIC
          return PIC;
        case 9: // MOBILE_IMAGE
          return MOBILE_IMAGE;
        case 10: // ORIG_PRICE
          return ORIG_PRICE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ID_ISSET_ID = 0;
  private static final int __PRICE_ISSET_ID = 1;
  private static final int __EXCHANGE_ISSET_ID = 2;
  private static final int __CATEGORY_ISSET_ID = 3;
  private static final int __STATUS_ISSET_ID = 4;
  private static final int __ORIGPRICE_ISSET_ID = 5;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.ID,_Fields.NAME,_Fields.PRICE,_Fields.EXCHANGE,_Fields.CATEGORY,_Fields.STATUS,_Fields.URL,_Fields.PIC,_Fields.MOBILE_IMAGE,_Fields.ORIG_PRICE};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ID, new org.apache.thrift.meta_data.FieldMetaData("id", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.NAME, new org.apache.thrift.meta_data.FieldMetaData("name", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PRICE, new org.apache.thrift.meta_data.FieldMetaData("price", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.EXCHANGE, new org.apache.thrift.meta_data.FieldMetaData("exchange", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.DOUBLE)));
    tmpMap.put(_Fields.CATEGORY, new org.apache.thrift.meta_data.FieldMetaData("category", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.STATUS, new org.apache.thrift.meta_data.FieldMetaData("status", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.URL, new org.apache.thrift.meta_data.FieldMetaData("url", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PIC, new org.apache.thrift.meta_data.FieldMetaData("pic", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.MOBILE_IMAGE, new org.apache.thrift.meta_data.FieldMetaData("mobileImage", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ORIG_PRICE, new org.apache.thrift.meta_data.FieldMetaData("origPrice", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SimpleGiftInfo.class, metaDataMap);
  }

  public SimpleGiftInfo() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public SimpleGiftInfo(SimpleGiftInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.id = other.id;
    if (other.isSetName()) {
      this.name = other.name;
    }
    this.price = other.price;
    this.exchange = other.exchange;
    this.category = other.category;
    this.status = other.status;
    if (other.isSetUrl()) {
      this.url = other.url;
    }
    if (other.isSetPic()) {
      this.pic = other.pic;
    }
    if (other.isSetMobileImage()) {
      this.mobileImage = other.mobileImage;
    }
    this.origPrice = other.origPrice;
  }

  public SimpleGiftInfo deepCopy() {
    return new SimpleGiftInfo(this);
  }

  @Override
  public void clear() {
    setIdIsSet(false);
    this.id = 0;
    this.name = null;
    setPriceIsSet(false);
    this.price = 0;
    setExchangeIsSet(false);
    this.exchange = 0.0;
    setCategoryIsSet(false);
    this.category = 0;
    setStatusIsSet(false);
    this.status = 0;
    this.url = null;
    this.pic = null;
    this.mobileImage = null;
    setOrigPriceIsSet(false);
    this.origPrice = 0;
  }

  /**
   * id
   */
  public int getId() {
    return this.id;
  }

  /**
   * id
   */
  public SimpleGiftInfo setId(int id) {
    this.id = id;
    setIdIsSet(true);
    return this;
  }

  public void unsetId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ID_ISSET_ID);
  }

  /** Returns true if field id is set (has been assigned a value) and false otherwise */
  public boolean isSetId() {
    return EncodingUtils.testBit(__isset_bitfield, __ID_ISSET_ID);
  }

  public void setIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ID_ISSET_ID, value);
  }

  /**
   * name
   */
  public String getName() {
    return this.name;
  }

  /**
   * name
   */
  public SimpleGiftInfo setName(String name) {
    this.name = name;
    return this;
  }

  public void unsetName() {
    this.name = null;
  }

  /** Returns true if field name is set (has been assigned a value) and false otherwise */
  public boolean isSetName() {
    return this.name != null;
  }

  public void setNameIsSet(boolean value) {
    if (!value) {
      this.name = null;
    }
  }

  /**
   * price 价格
   */
  public int getPrice() {
    return this.price;
  }

  /**
   * price 价格
   */
  public SimpleGiftInfo setPrice(int price) {
    this.price = price;
    setPriceIsSet(true);
    return this;
  }

  public void unsetPrice() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PRICE_ISSET_ID);
  }

  /** Returns true if field price is set (has been assigned a value) and false otherwise */
  public boolean isSetPrice() {
    return EncodingUtils.testBit(__isset_bitfield, __PRICE_ISSET_ID);
  }

  public void setPriceIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PRICE_ISSET_ID, value);
  }

  /**
   * exchange 分成
   */
  public double getExchange() {
    return this.exchange;
  }

  /**
   * exchange 分成
   */
  public SimpleGiftInfo setExchange(double exchange) {
    this.exchange = exchange;
    setExchangeIsSet(true);
    return this;
  }

  public void unsetExchange() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __EXCHANGE_ISSET_ID);
  }

  /** Returns true if field exchange is set (has been assigned a value) and false otherwise */
  public boolean isSetExchange() {
    return EncodingUtils.testBit(__isset_bitfield, __EXCHANGE_ISSET_ID);
  }

  public void setExchangeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __EXCHANGE_ISSET_ID, value);
  }

  /**
   * category 礼物分类
   */
  public int getCategory() {
    return this.category;
  }

  /**
   * category 礼物分类
   */
  public SimpleGiftInfo setCategory(int category) {
    this.category = category;
    setCategoryIsSet(true);
    return this;
  }

  public void unsetCategory() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CATEGORY_ISSET_ID);
  }

  /** Returns true if field category is set (has been assigned a value) and false otherwise */
  public boolean isSetCategory() {
    return EncodingUtils.testBit(__isset_bitfield, __CATEGORY_ISSET_ID);
  }

  public void setCategoryIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CATEGORY_ISSET_ID, value);
  }

  /**
   * status 上下架，仓库礼物下架也可送
   */
  public int getStatus() {
    return this.status;
  }

  /**
   * status 上下架，仓库礼物下架也可送
   */
  public SimpleGiftInfo setStatus(int status) {
    this.status = status;
    setStatusIsSet(true);
    return this;
  }

  public void unsetStatus() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  /** Returns true if field status is set (has been assigned a value) and false otherwise */
  public boolean isSetStatus() {
    return EncodingUtils.testBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  public void setStatusIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STATUS_ISSET_ID, value);
  }

  /**
   * url
   */
  public String getUrl() {
    return this.url;
  }

  /**
   * url
   */
  public SimpleGiftInfo setUrl(String url) {
    this.url = url;
    return this;
  }

  public void unsetUrl() {
    this.url = null;
  }

  /** Returns true if field url is set (has been assigned a value) and false otherwise */
  public boolean isSetUrl() {
    return this.url != null;
  }

  public void setUrlIsSet(boolean value) {
    if (!value) {
      this.url = null;
    }
  }

  /**
   * pic
   */
  public String getPic() {
    return this.pic;
  }

  /**
   * pic
   */
  public SimpleGiftInfo setPic(String pic) {
    this.pic = pic;
    return this;
  }

  public void unsetPic() {
    this.pic = null;
  }

  /** Returns true if field pic is set (has been assigned a value) and false otherwise */
  public boolean isSetPic() {
    return this.pic != null;
  }

  public void setPicIsSet(boolean value) {
    if (!value) {
      this.pic = null;
    }
  }

  /**
   * 手机端礼物图片
   */
  public String getMobileImage() {
    return this.mobileImage;
  }

  /**
   * 手机端礼物图片
   */
  public SimpleGiftInfo setMobileImage(String mobileImage) {
    this.mobileImage = mobileImage;
    return this;
  }

  public void unsetMobileImage() {
    this.mobileImage = null;
  }

  /** Returns true if field mobileImage is set (has been assigned a value) and false otherwise */
  public boolean isSetMobileImage() {
    return this.mobileImage != null;
  }

  public void setMobileImageIsSet(boolean value) {
    if (!value) {
      this.mobileImage = null;
    }
  }

  /**
   * 原价 如果不打折此字段为0 如果打折此字段必须大于单价price
   */
  public int getOrigPrice() {
    return this.origPrice;
  }

  /**
   * 原价 如果不打折此字段为0 如果打折此字段必须大于单价price
   */
  public SimpleGiftInfo setOrigPrice(int origPrice) {
    this.origPrice = origPrice;
    setOrigPriceIsSet(true);
    return this;
  }

  public void unsetOrigPrice() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ORIGPRICE_ISSET_ID);
  }

  /** Returns true if field origPrice is set (has been assigned a value) and false otherwise */
  public boolean isSetOrigPrice() {
    return EncodingUtils.testBit(__isset_bitfield, __ORIGPRICE_ISSET_ID);
  }

  public void setOrigPriceIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ORIGPRICE_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ID:
      if (value == null) {
        unsetId();
      } else {
        setId((Integer)value);
      }
      break;

    case NAME:
      if (value == null) {
        unsetName();
      } else {
        setName((String)value);
      }
      break;

    case PRICE:
      if (value == null) {
        unsetPrice();
      } else {
        setPrice((Integer)value);
      }
      break;

    case EXCHANGE:
      if (value == null) {
        unsetExchange();
      } else {
        setExchange((Double)value);
      }
      break;

    case CATEGORY:
      if (value == null) {
        unsetCategory();
      } else {
        setCategory((Integer)value);
      }
      break;

    case STATUS:
      if (value == null) {
        unsetStatus();
      } else {
        setStatus((Integer)value);
      }
      break;

    case URL:
      if (value == null) {
        unsetUrl();
      } else {
        setUrl((String)value);
      }
      break;

    case PIC:
      if (value == null) {
        unsetPic();
      } else {
        setPic((String)value);
      }
      break;

    case MOBILE_IMAGE:
      if (value == null) {
        unsetMobileImage();
      } else {
        setMobileImage((String)value);
      }
      break;

    case ORIG_PRICE:
      if (value == null) {
        unsetOrigPrice();
      } else {
        setOrigPrice((Integer)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ID:
      return getId();

    case NAME:
      return getName();

    case PRICE:
      return getPrice();

    case EXCHANGE:
      return getExchange();

    case CATEGORY:
      return getCategory();

    case STATUS:
      return getStatus();

    case URL:
      return getUrl();

    case PIC:
      return getPic();

    case MOBILE_IMAGE:
      return getMobileImage();

    case ORIG_PRICE:
      return getOrigPrice();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ID:
      return isSetId();
    case NAME:
      return isSetName();
    case PRICE:
      return isSetPrice();
    case EXCHANGE:
      return isSetExchange();
    case CATEGORY:
      return isSetCategory();
    case STATUS:
      return isSetStatus();
    case URL:
      return isSetUrl();
    case PIC:
      return isSetPic();
    case MOBILE_IMAGE:
      return isSetMobileImage();
    case ORIG_PRICE:
      return isSetOrigPrice();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof SimpleGiftInfo)
      return this.equals((SimpleGiftInfo)that);
    return false;
  }

  public boolean equals(SimpleGiftInfo that) {
    if (that == null)
      return false;

    boolean this_present_id = true && this.isSetId();
    boolean that_present_id = true && that.isSetId();
    if (this_present_id || that_present_id) {
      if (!(this_present_id && that_present_id))
        return false;
      if (this.id != that.id)
        return false;
    }

    boolean this_present_name = true && this.isSetName();
    boolean that_present_name = true && that.isSetName();
    if (this_present_name || that_present_name) {
      if (!(this_present_name && that_present_name))
        return false;
      if (!this.name.equals(that.name))
        return false;
    }

    boolean this_present_price = true && this.isSetPrice();
    boolean that_present_price = true && that.isSetPrice();
    if (this_present_price || that_present_price) {
      if (!(this_present_price && that_present_price))
        return false;
      if (this.price != that.price)
        return false;
    }

    boolean this_present_exchange = true && this.isSetExchange();
    boolean that_present_exchange = true && that.isSetExchange();
    if (this_present_exchange || that_present_exchange) {
      if (!(this_present_exchange && that_present_exchange))
        return false;
      if (this.exchange != that.exchange)
        return false;
    }

    boolean this_present_category = true && this.isSetCategory();
    boolean that_present_category = true && that.isSetCategory();
    if (this_present_category || that_present_category) {
      if (!(this_present_category && that_present_category))
        return false;
      if (this.category != that.category)
        return false;
    }

    boolean this_present_status = true && this.isSetStatus();
    boolean that_present_status = true && that.isSetStatus();
    if (this_present_status || that_present_status) {
      if (!(this_present_status && that_present_status))
        return false;
      if (this.status != that.status)
        return false;
    }

    boolean this_present_url = true && this.isSetUrl();
    boolean that_present_url = true && that.isSetUrl();
    if (this_present_url || that_present_url) {
      if (!(this_present_url && that_present_url))
        return false;
      if (!this.url.equals(that.url))
        return false;
    }

    boolean this_present_pic = true && this.isSetPic();
    boolean that_present_pic = true && that.isSetPic();
    if (this_present_pic || that_present_pic) {
      if (!(this_present_pic && that_present_pic))
        return false;
      if (!this.pic.equals(that.pic))
        return false;
    }

    boolean this_present_mobileImage = true && this.isSetMobileImage();
    boolean that_present_mobileImage = true && that.isSetMobileImage();
    if (this_present_mobileImage || that_present_mobileImage) {
      if (!(this_present_mobileImage && that_present_mobileImage))
        return false;
      if (!this.mobileImage.equals(that.mobileImage))
        return false;
    }

    boolean this_present_origPrice = true && this.isSetOrigPrice();
    boolean that_present_origPrice = true && that.isSetOrigPrice();
    if (this_present_origPrice || that_present_origPrice) {
      if (!(this_present_origPrice && that_present_origPrice))
        return false;
      if (this.origPrice != that.origPrice)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_id = true && (isSetId());
    list.add(present_id);
    if (present_id)
      list.add(id);

    boolean present_name = true && (isSetName());
    list.add(present_name);
    if (present_name)
      list.add(name);

    boolean present_price = true && (isSetPrice());
    list.add(present_price);
    if (present_price)
      list.add(price);

    boolean present_exchange = true && (isSetExchange());
    list.add(present_exchange);
    if (present_exchange)
      list.add(exchange);

    boolean present_category = true && (isSetCategory());
    list.add(present_category);
    if (present_category)
      list.add(category);

    boolean present_status = true && (isSetStatus());
    list.add(present_status);
    if (present_status)
      list.add(status);

    boolean present_url = true && (isSetUrl());
    list.add(present_url);
    if (present_url)
      list.add(url);

    boolean present_pic = true && (isSetPic());
    list.add(present_pic);
    if (present_pic)
      list.add(pic);

    boolean present_mobileImage = true && (isSetMobileImage());
    list.add(present_mobileImage);
    if (present_mobileImage)
      list.add(mobileImage);

    boolean present_origPrice = true && (isSetOrigPrice());
    list.add(present_origPrice);
    if (present_origPrice)
      list.add(origPrice);

    return list.hashCode();
  }

  @Override
  public int compareTo(SimpleGiftInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetId()).compareTo(other.isSetId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.id, other.id);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetName()).compareTo(other.isSetName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.name, other.name);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPrice()).compareTo(other.isSetPrice());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPrice()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.price, other.price);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExchange()).compareTo(other.isSetExchange());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExchange()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.exchange, other.exchange);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCategory()).compareTo(other.isSetCategory());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCategory()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.category, other.category);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetStatus()).compareTo(other.isSetStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.status, other.status);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUrl()).compareTo(other.isSetUrl());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUrl()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.url, other.url);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPic()).compareTo(other.isSetPic());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPic()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pic, other.pic);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMobileImage()).compareTo(other.isSetMobileImage());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMobileImage()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.mobileImage, other.mobileImage);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetOrigPrice()).compareTo(other.isSetOrigPrice());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOrigPrice()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.origPrice, other.origPrice);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("SimpleGiftInfo(");
    boolean first = true;

    if (isSetId()) {
      sb.append("id:");
      sb.append(this.id);
      first = false;
    }
    if (isSetName()) {
      if (!first) sb.append(", ");
      sb.append("name:");
      if (this.name == null) {
        sb.append("null");
      } else {
        sb.append(this.name);
      }
      first = false;
    }
    if (isSetPrice()) {
      if (!first) sb.append(", ");
      sb.append("price:");
      sb.append(this.price);
      first = false;
    }
    if (isSetExchange()) {
      if (!first) sb.append(", ");
      sb.append("exchange:");
      sb.append(this.exchange);
      first = false;
    }
    if (isSetCategory()) {
      if (!first) sb.append(", ");
      sb.append("category:");
      sb.append(this.category);
      first = false;
    }
    if (isSetStatus()) {
      if (!first) sb.append(", ");
      sb.append("status:");
      sb.append(this.status);
      first = false;
    }
    if (isSetUrl()) {
      if (!first) sb.append(", ");
      sb.append("url:");
      if (this.url == null) {
        sb.append("null");
      } else {
        sb.append(this.url);
      }
      first = false;
    }
    if (isSetPic()) {
      if (!first) sb.append(", ");
      sb.append("pic:");
      if (this.pic == null) {
        sb.append("null");
      } else {
        sb.append(this.pic);
      }
      first = false;
    }
    if (isSetMobileImage()) {
      if (!first) sb.append(", ");
      sb.append("mobileImage:");
      if (this.mobileImage == null) {
        sb.append("null");
      } else {
        sb.append(this.mobileImage);
      }
      first = false;
    }
    if (isSetOrigPrice()) {
      if (!first) sb.append(", ");
      sb.append("origPrice:");
      sb.append(this.origPrice);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class SimpleGiftInfoStandardSchemeFactory implements SchemeFactory {
    public SimpleGiftInfoStandardScheme getScheme() {
      return new SimpleGiftInfoStandardScheme();
    }
  }

  private static class SimpleGiftInfoStandardScheme extends StandardScheme<SimpleGiftInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, SimpleGiftInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.id = iprot.readI32();
              struct.setIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.name = iprot.readString();
              struct.setNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // PRICE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.price = iprot.readI32();
              struct.setPriceIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // EXCHANGE
            if (schemeField.type == org.apache.thrift.protocol.TType.DOUBLE) {
              struct.exchange = iprot.readDouble();
              struct.setExchangeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // CATEGORY
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.category = iprot.readI32();
              struct.setCategoryIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.status = iprot.readI32();
              struct.setStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // URL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.url = iprot.readString();
              struct.setUrlIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // PIC
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.pic = iprot.readString();
              struct.setPicIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // MOBILE_IMAGE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.mobileImage = iprot.readString();
              struct.setMobileImageIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // ORIG_PRICE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.origPrice = iprot.readI32();
              struct.setOrigPriceIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, SimpleGiftInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetId()) {
        oprot.writeFieldBegin(ID_FIELD_DESC);
        oprot.writeI32(struct.id);
        oprot.writeFieldEnd();
      }
      if (struct.name != null) {
        if (struct.isSetName()) {
          oprot.writeFieldBegin(NAME_FIELD_DESC);
          oprot.writeString(struct.name);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetPrice()) {
        oprot.writeFieldBegin(PRICE_FIELD_DESC);
        oprot.writeI32(struct.price);
        oprot.writeFieldEnd();
      }
      if (struct.isSetExchange()) {
        oprot.writeFieldBegin(EXCHANGE_FIELD_DESC);
        oprot.writeDouble(struct.exchange);
        oprot.writeFieldEnd();
      }
      if (struct.isSetCategory()) {
        oprot.writeFieldBegin(CATEGORY_FIELD_DESC);
        oprot.writeI32(struct.category);
        oprot.writeFieldEnd();
      }
      if (struct.isSetStatus()) {
        oprot.writeFieldBegin(STATUS_FIELD_DESC);
        oprot.writeI32(struct.status);
        oprot.writeFieldEnd();
      }
      if (struct.url != null) {
        if (struct.isSetUrl()) {
          oprot.writeFieldBegin(URL_FIELD_DESC);
          oprot.writeString(struct.url);
          oprot.writeFieldEnd();
        }
      }
      if (struct.pic != null) {
        if (struct.isSetPic()) {
          oprot.writeFieldBegin(PIC_FIELD_DESC);
          oprot.writeString(struct.pic);
          oprot.writeFieldEnd();
        }
      }
      if (struct.mobileImage != null) {
        if (struct.isSetMobileImage()) {
          oprot.writeFieldBegin(MOBILE_IMAGE_FIELD_DESC);
          oprot.writeString(struct.mobileImage);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetOrigPrice()) {
        oprot.writeFieldBegin(ORIG_PRICE_FIELD_DESC);
        oprot.writeI32(struct.origPrice);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class SimpleGiftInfoTupleSchemeFactory implements SchemeFactory {
    public SimpleGiftInfoTupleScheme getScheme() {
      return new SimpleGiftInfoTupleScheme();
    }
  }

  private static class SimpleGiftInfoTupleScheme extends TupleScheme<SimpleGiftInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, SimpleGiftInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetId()) {
        optionals.set(0);
      }
      if (struct.isSetName()) {
        optionals.set(1);
      }
      if (struct.isSetPrice()) {
        optionals.set(2);
      }
      if (struct.isSetExchange()) {
        optionals.set(3);
      }
      if (struct.isSetCategory()) {
        optionals.set(4);
      }
      if (struct.isSetStatus()) {
        optionals.set(5);
      }
      if (struct.isSetUrl()) {
        optionals.set(6);
      }
      if (struct.isSetPic()) {
        optionals.set(7);
      }
      if (struct.isSetMobileImage()) {
        optionals.set(8);
      }
      if (struct.isSetOrigPrice()) {
        optionals.set(9);
      }
      oprot.writeBitSet(optionals, 10);
      if (struct.isSetId()) {
        oprot.writeI32(struct.id);
      }
      if (struct.isSetName()) {
        oprot.writeString(struct.name);
      }
      if (struct.isSetPrice()) {
        oprot.writeI32(struct.price);
      }
      if (struct.isSetExchange()) {
        oprot.writeDouble(struct.exchange);
      }
      if (struct.isSetCategory()) {
        oprot.writeI32(struct.category);
      }
      if (struct.isSetStatus()) {
        oprot.writeI32(struct.status);
      }
      if (struct.isSetUrl()) {
        oprot.writeString(struct.url);
      }
      if (struct.isSetPic()) {
        oprot.writeString(struct.pic);
      }
      if (struct.isSetMobileImage()) {
        oprot.writeString(struct.mobileImage);
      }
      if (struct.isSetOrigPrice()) {
        oprot.writeI32(struct.origPrice);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, SimpleGiftInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(10);
      if (incoming.get(0)) {
        struct.id = iprot.readI32();
        struct.setIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.name = iprot.readString();
        struct.setNameIsSet(true);
      }
      if (incoming.get(2)) {
        struct.price = iprot.readI32();
        struct.setPriceIsSet(true);
      }
      if (incoming.get(3)) {
        struct.exchange = iprot.readDouble();
        struct.setExchangeIsSet(true);
      }
      if (incoming.get(4)) {
        struct.category = iprot.readI32();
        struct.setCategoryIsSet(true);
      }
      if (incoming.get(5)) {
        struct.status = iprot.readI32();
        struct.setStatusIsSet(true);
      }
      if (incoming.get(6)) {
        struct.url = iprot.readString();
        struct.setUrlIsSet(true);
      }
      if (incoming.get(7)) {
        struct.pic = iprot.readString();
        struct.setPicIsSet(true);
      }
      if (incoming.get(8)) {
        struct.mobileImage = iprot.readString();
        struct.setMobileImageIsSet(true);
      }
      if (incoming.get(9)) {
        struct.origPrice = iprot.readI32();
        struct.setOrigPriceIsSet(true);
      }
    }
  }

}

