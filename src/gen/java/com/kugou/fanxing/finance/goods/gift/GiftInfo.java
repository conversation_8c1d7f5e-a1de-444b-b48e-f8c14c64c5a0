/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.finance.goods.gift;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
/**
 * 礼物信息
 */
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2020-08-19")
public class GiftInfo implements org.apache.thrift.TBase<GiftInfo, GiftInfo._Fields>, java.io.Serializable, Cloneable, Comparable<GiftInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GiftInfo");

  private static final org.apache.thrift.protocol.TField ID_FIELD_DESC = new org.apache.thrift.protocol.TField("id", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("name", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField URL_FIELD_DESC = new org.apache.thrift.protocol.TField("url", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField PIC_FIELD_DESC = new org.apache.thrift.protocol.TField("pic", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField PRICE_FIELD_DESC = new org.apache.thrift.protocol.TField("price", org.apache.thrift.protocol.TType.I32, (short)5);
  private static final org.apache.thrift.protocol.TField EXCHANGE_FIELD_DESC = new org.apache.thrift.protocol.TField("exchange", org.apache.thrift.protocol.TType.DOUBLE, (short)6);
  private static final org.apache.thrift.protocol.TField CATEGORY_FIELD_DESC = new org.apache.thrift.protocol.TField("category", org.apache.thrift.protocol.TType.I32, (short)7);
  private static final org.apache.thrift.protocol.TField MIX_FIELD_DESC = new org.apache.thrift.protocol.TField("mix", org.apache.thrift.protocol.TType.I32, (short)8);
  private static final org.apache.thrift.protocol.TField FLY_FIELD_DESC = new org.apache.thrift.protocol.TField("fly", org.apache.thrift.protocol.TType.I32, (short)9);
  private static final org.apache.thrift.protocol.TField CLASS_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("className", org.apache.thrift.protocol.TType.STRING, (short)10);
  private static final org.apache.thrift.protocol.TField SORT_INDEX_FIELD_DESC = new org.apache.thrift.protocol.TField("sortIndex", org.apache.thrift.protocol.TType.I32, (short)11);
  private static final org.apache.thrift.protocol.TField STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("status", org.apache.thrift.protocol.TType.I32, (short)12);
  private static final org.apache.thrift.protocol.TField IMAGE_FIELD_DESC = new org.apache.thrift.protocol.TField("image", org.apache.thrift.protocol.TType.STRING, (short)13);
  private static final org.apache.thrift.protocol.TField IS_NEW_FIELD_DESC = new org.apache.thrift.protocol.TField("isNew", org.apache.thrift.protocol.TType.I32, (short)14);
  private static final org.apache.thrift.protocol.TField IMAGE_TRANS_FIELD_DESC = new org.apache.thrift.protocol.TField("imageTrans", org.apache.thrift.protocol.TType.STRING, (short)15);
  private static final org.apache.thrift.protocol.TField IMAGE_GRADE_FIELD_DESC = new org.apache.thrift.protocol.TField("imageGrade", org.apache.thrift.protocol.TType.STRING, (short)16);
  private static final org.apache.thrift.protocol.TField EXPIRE_FIELD_DESC = new org.apache.thrift.protocol.TField("expire", org.apache.thrift.protocol.TType.I32, (short)17);
  private static final org.apache.thrift.protocol.TField RICH_LEVEL_LIMIT_FIELD_DESC = new org.apache.thrift.protocol.TField("richLevelLimit", org.apache.thrift.protocol.TType.I32, (short)18);
  private static final org.apache.thrift.protocol.TField TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("type", org.apache.thrift.protocol.TType.I32, (short)19);
  private static final org.apache.thrift.protocol.TField ADT_EFFECT_FIELD_DESC = new org.apache.thrift.protocol.TField("adtEffect", org.apache.thrift.protocol.TType.STRING, (short)20);
  private static final org.apache.thrift.protocol.TField GUARD_LEVEL_LIMIT_FIELD_DESC = new org.apache.thrift.protocol.TField("guardLevelLimit", org.apache.thrift.protocol.TType.I32, (short)21);
  private static final org.apache.thrift.protocol.TField VIP_LIMIT_FIELD_DESC = new org.apache.thrift.protocol.TField("vipLimit", org.apache.thrift.protocol.TType.I32, (short)22);
  private static final org.apache.thrift.protocol.TField IS_PILE_FIELD_DESC = new org.apache.thrift.protocol.TField("isPile", org.apache.thrift.protocol.TType.I32, (short)23);
  private static final org.apache.thrift.protocol.TField CAN_NOT_SEND_FIELD_DESC = new org.apache.thrift.protocol.TField("canNotSend", org.apache.thrift.protocol.TType.I32, (short)24);
  private static final org.apache.thrift.protocol.TField EXTRA_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("extraType", org.apache.thrift.protocol.TType.I32, (short)25);
  private static final org.apache.thrift.protocol.TField EXTRA_FIELD_DESC = new org.apache.thrift.protocol.TField("extra", org.apache.thrift.protocol.TType.STRING, (short)26);
  private static final org.apache.thrift.protocol.TField USER_ID_LIMIT_FIELD_DESC = new org.apache.thrift.protocol.TField("userIdLimit", org.apache.thrift.protocol.TType.I64, (short)27);
  private static final org.apache.thrift.protocol.TField USER_LIMIT_NICK_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("userLimitNickName", org.apache.thrift.protocol.TType.STRING, (short)28);
  private static final org.apache.thrift.protocol.TField STAR_FANS_LIMIT_FIELD_DESC = new org.apache.thrift.protocol.TField("starFansLimit", org.apache.thrift.protocol.TType.I32, (short)29);
  private static final org.apache.thrift.protocol.TField GIFT_URL_FIELD_DESC = new org.apache.thrift.protocol.TField("giftUrl", org.apache.thrift.protocol.TType.STRING, (short)30);
  private static final org.apache.thrift.protocol.TField SPECIAL_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("specialType", org.apache.thrift.protocol.TType.I32, (short)31);
  private static final org.apache.thrift.protocol.TField TOP_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("topCount", org.apache.thrift.protocol.TType.I32, (short)32);
  private static final org.apache.thrift.protocol.TField WEEK_FIELD_DESC = new org.apache.thrift.protocol.TField("week", org.apache.thrift.protocol.TType.I32, (short)33);
  private static final org.apache.thrift.protocol.TField IS_SUPER_FIELD_DESC = new org.apache.thrift.protocol.TField("isSuper", org.apache.thrift.protocol.TType.I32, (short)34);
  private static final org.apache.thrift.protocol.TField HAPPY_OBJ_FIELD_DESC = new org.apache.thrift.protocol.TField("happyObj", org.apache.thrift.protocol.TType.I32, (short)35);
  private static final org.apache.thrift.protocol.TField HAPPY_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("happyType", org.apache.thrift.protocol.TType.I32, (short)36);
  private static final org.apache.thrift.protocol.TField MOBILE_IMAGE_FIELD_DESC = new org.apache.thrift.protocol.TField("mobileImage", org.apache.thrift.protocol.TType.STRING, (short)37);
  private static final org.apache.thrift.protocol.TField IS_PK_FIELD_DESC = new org.apache.thrift.protocol.TField("isPk", org.apache.thrift.protocol.TType.I32, (short)38);
  private static final org.apache.thrift.protocol.TField IS_FULL_SHOW_FIELD_DESC = new org.apache.thrift.protocol.TField("isFullShow", org.apache.thrift.protocol.TType.I32, (short)39);
  private static final org.apache.thrift.protocol.TField DURATION_FIELD_DESC = new org.apache.thrift.protocol.TField("duration", org.apache.thrift.protocol.TType.I32, (short)40);
  private static final org.apache.thrift.protocol.TField L_GUARD_LEVEL_LIMIT_FIELD_DESC = new org.apache.thrift.protocol.TField("lGuardLevelLimit", org.apache.thrift.protocol.TType.I32, (short)41);
  private static final org.apache.thrift.protocol.TField RATE_LIMIT_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("rateLimitType", org.apache.thrift.protocol.TType.I32, (short)42);
  private static final org.apache.thrift.protocol.TField RATE_LIMIT_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("rateLimitNum", org.apache.thrift.protocol.TType.I32, (short)43);
  private static final org.apache.thrift.protocol.TField RATE_LIMIT_EXPIRY_FIELD_DESC = new org.apache.thrift.protocol.TField("rateLimitExpiry", org.apache.thrift.protocol.TType.I32, (short)44);
  private static final org.apache.thrift.protocol.TField IS_SHOW_FIELD_DESC = new org.apache.thrift.protocol.TField("isShow", org.apache.thrift.protocol.TType.I32, (short)45);
  private static final org.apache.thrift.protocol.TField GUARD_FIELD_DESC = new org.apache.thrift.protocol.TField("guard", org.apache.thrift.protocol.TType.I32, (short)46);
  private static final org.apache.thrift.protocol.TField VIDEO_URL_FIELD_DESC = new org.apache.thrift.protocol.TField("videoUrl", org.apache.thrift.protocol.TType.STRING, (short)47);
  private static final org.apache.thrift.protocol.TField IS_ROLL_GIFT_FIELD_DESC = new org.apache.thrift.protocol.TField("isRollGift", org.apache.thrift.protocol.TType.I32, (short)48);
  private static final org.apache.thrift.protocol.TField ROLL_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("rollNum", org.apache.thrift.protocol.TType.I64, (short)49);
  private static final org.apache.thrift.protocol.TField MULTI_VIDEO_URLS_FIELD_DESC = new org.apache.thrift.protocol.TField("multiVideoUrls", org.apache.thrift.protocol.TType.STRING, (short)50);
  private static final org.apache.thrift.protocol.TField GIFT_TIPS_FIELD_DESC = new org.apache.thrift.protocol.TField("giftTips", org.apache.thrift.protocol.TType.STRING, (short)51);
  private static final org.apache.thrift.protocol.TField STAR_VIP_LEVEL_FIELD_DESC = new org.apache.thrift.protocol.TField("starVipLevel", org.apache.thrift.protocol.TType.I32, (short)52);
  private static final org.apache.thrift.protocol.TField VERTICAL_VIDEO_URL_FIELD_DESC = new org.apache.thrift.protocol.TField("verticalVideoUrl", org.apache.thrift.protocol.TType.STRING, (short)53);
  private static final org.apache.thrift.protocol.TField EXT_RESOURCE_FIELD_DESC = new org.apache.thrift.protocol.TField("extResource", org.apache.thrift.protocol.TType.STRING, (short)54);
  private static final org.apache.thrift.protocol.TField ORIG_PRICE_FIELD_DESC = new org.apache.thrift.protocol.TField("origPrice", org.apache.thrift.protocol.TType.I32, (short)55);
  private static final org.apache.thrift.protocol.TField NEW_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("newTime", org.apache.thrift.protocol.TType.I32, (short)56);
  private static final org.apache.thrift.protocol.TField IS_GLOBAL_FIELD_DESC = new org.apache.thrift.protocol.TField("isGlobal", org.apache.thrift.protocol.TType.I32, (short)57);
  private static final org.apache.thrift.protocol.TField IS_GLOBAL_NOTICE_FIELD_DESC = new org.apache.thrift.protocol.TField("isGlobalNotice", org.apache.thrift.protocol.TType.I32, (short)58);
  private static final org.apache.thrift.protocol.TField EXT_ATTR_FIELD_DESC = new org.apache.thrift.protocol.TField("extAttr", org.apache.thrift.protocol.TType.STRING, (short)59);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new GiftInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new GiftInfoTupleSchemeFactory());
  }

  /**
   * id
   */
  public int id; // optional
  /**
   * name
   */
  public String name; // optional
  /**
   * url
   */
  public String url; // optional
  /**
   * pic
   */
  public String pic; // optional
  /**
   * price
   */
  public int price; // optional
  /**
   * exchange
   */
  public double exchange; // optional
  /**
   * category
   */
  public int category; // optional
  /**
   * 可组合?(1:可以;0:不可以)
   */
  public int mix; // optional
  /**
   * 可飞屏?(1:可以;0:不可以)
   */
  public int fly; // optional
  /**
   * 对应类 *
   */
  public String className; // optional
  /**
   * sortIndex
   */
  public int sortIndex; // optional
  /**
   * status
   */
  public int status; // optional
  /**
   * image
   */
  public String image; // optional
  /**
   * isNew 新上架?(1:是;0:否)
   */
  public int isNew; // optional
  /**
   * 缩略图(透明) *
   */
  public String imageTrans; // optional
  /**
   * 缩略图(灰底) *
   */
  public String imageGrade; // optional
  /**
   * expire
   */
  public int expire; // optional
  /**
   * richLevelLimit
   */
  public int richLevelLimit; // optional
  /**
   * type
   */
  public int type; // optional
  /**
   * 安卓礼物效果图压缩包路径 *
   */
  public String adtEffect; // optional
  /**
   * guardLevelLimit
   */
  public int guardLevelLimit; // optional
  /**
   * vipLimit
   */
  public int vipLimit; // optional
  /**
   * isPile
   */
  public int isPile; // optional
  /**
   * canNotSend
   */
  public int canNotSend; // optional
  /**
   * extraType
   */
  public int extraType; // optional
  /**
   * extra
   */
  public String extra; // optional
  /**
   * userIdLimit
   */
  public long userIdLimit; // optional
  /**
   * userLimitNickName *
   */
  public String userLimitNickName; // optional
  /**
   * 星粉等级限制，0为不限制 *
   */
  public int starFansLimit; // optional
  /**
   * 礼物图url *
   */
  public String giftUrl; // optional
  /**
   * 特殊类型  0默认 1星星 2幸运礼物 3普天同庆 4未知(前后端都查不到) 5数字专辑 *
   */
  public int specialType; // optional
  /**
   * 是否计入排行榜 *
   */
  public int topCount; // optional
  /**
   * 是否周星 *
   */
  public int week; // optional
  /**
   * 是否超星 *
   */
  public int isSuper; // optional
  /**
   * 财神类礼物对象(1:观众,2:管理) *
   */
  public int happyObj; // optional
  /**
   * 财神类礼物类型(礼物类型(1:抢星币,2:平均分)) *
   */
  public int happyType; // optional
  /**
   * 手机端礼物图片 *
   */
  public String mobileImage; // optional
  /**
   * 是否是PK礼物（1是，0否） *
   */
  public int isPk; // optional
  /**
   * 是否可一起展示（1是，0否）*
   */
  public int isFullShow; // optional
  public int duration; // optional
  public int lGuardLevelLimit; // optional
  public int rateLimitType; // optional
  public int rateLimitNum; // optional
  public int rateLimitExpiry; // optional
  public int isShow; // optional
  public int guard; // optional
  public String videoUrl; // optional
  public int isRollGift; // optional
  public long rollNum; // optional
  public String multiVideoUrls; // optional
  public String giftTips; // optional
  /**
   * 星钻等级
   */
  public int starVipLevel; // optional
  /**
   * 竖屏礼物资源
   */
  public String verticalVideoUrl; // optional
  /**
   * 额外礼物资源,以后其它新增的资源,能放这里就放这里,透传给前端,不用再发版
   */
  public String extResource; // optional
  /**
   * 原价
   */
  public int origPrice; // optional
  /**
   * 上新时间
   */
  public int newTime; // optional
  /**
   * 全站礼物(0:不是,1:是) *
   */
  public int isGlobal; // optional
  /**
   * 发全站公告(0:不是,1:是) *
   */
  public int isGlobalNotice; // optional
  /**
   * 礼物扩展属性,以后但凡新增礼物属性，都放在这个扩展字段
   */
  public String extAttr; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * id
     */
    ID((short)1, "id"),
    /**
     * name
     */
    NAME((short)2, "name"),
    /**
     * url
     */
    URL((short)3, "url"),
    /**
     * pic
     */
    PIC((short)4, "pic"),
    /**
     * price
     */
    PRICE((short)5, "price"),
    /**
     * exchange
     */
    EXCHANGE((short)6, "exchange"),
    /**
     * category
     */
    CATEGORY((short)7, "category"),
    /**
     * 可组合?(1:可以;0:不可以)
     */
    MIX((short)8, "mix"),
    /**
     * 可飞屏?(1:可以;0:不可以)
     */
    FLY((short)9, "fly"),
    /**
     * 对应类 *
     */
    CLASS_NAME((short)10, "className"),
    /**
     * sortIndex
     */
    SORT_INDEX((short)11, "sortIndex"),
    /**
     * status
     */
    STATUS((short)12, "status"),
    /**
     * image
     */
    IMAGE((short)13, "image"),
    /**
     * isNew 新上架?(1:是;0:否)
     */
    IS_NEW((short)14, "isNew"),
    /**
     * 缩略图(透明) *
     */
    IMAGE_TRANS((short)15, "imageTrans"),
    /**
     * 缩略图(灰底) *
     */
    IMAGE_GRADE((short)16, "imageGrade"),
    /**
     * expire
     */
    EXPIRE((short)17, "expire"),
    /**
     * richLevelLimit
     */
    RICH_LEVEL_LIMIT((short)18, "richLevelLimit"),
    /**
     * type
     */
    TYPE((short)19, "type"),
    /**
     * 安卓礼物效果图压缩包路径 *
     */
    ADT_EFFECT((short)20, "adtEffect"),
    /**
     * guardLevelLimit
     */
    GUARD_LEVEL_LIMIT((short)21, "guardLevelLimit"),
    /**
     * vipLimit
     */
    VIP_LIMIT((short)22, "vipLimit"),
    /**
     * isPile
     */
    IS_PILE((short)23, "isPile"),
    /**
     * canNotSend
     */
    CAN_NOT_SEND((short)24, "canNotSend"),
    /**
     * extraType
     */
    EXTRA_TYPE((short)25, "extraType"),
    /**
     * extra
     */
    EXTRA((short)26, "extra"),
    /**
     * userIdLimit
     */
    USER_ID_LIMIT((short)27, "userIdLimit"),
    /**
     * userLimitNickName *
     */
    USER_LIMIT_NICK_NAME((short)28, "userLimitNickName"),
    /**
     * 星粉等级限制，0为不限制 *
     */
    STAR_FANS_LIMIT((short)29, "starFansLimit"),
    /**
     * 礼物图url *
     */
    GIFT_URL((short)30, "giftUrl"),
    /**
     * 特殊类型  0默认 1星星 2幸运礼物 3普天同庆 4未知(前后端都查不到) 5数字专辑 *
     */
    SPECIAL_TYPE((short)31, "specialType"),
    /**
     * 是否计入排行榜 *
     */
    TOP_COUNT((short)32, "topCount"),
    /**
     * 是否周星 *
     */
    WEEK((short)33, "week"),
    /**
     * 是否超星 *
     */
    IS_SUPER((short)34, "isSuper"),
    /**
     * 财神类礼物对象(1:观众,2:管理) *
     */
    HAPPY_OBJ((short)35, "happyObj"),
    /**
     * 财神类礼物类型(礼物类型(1:抢星币,2:平均分)) *
     */
    HAPPY_TYPE((short)36, "happyType"),
    /**
     * 手机端礼物图片 *
     */
    MOBILE_IMAGE((short)37, "mobileImage"),
    /**
     * 是否是PK礼物（1是，0否） *
     */
    IS_PK((short)38, "isPk"),
    /**
     * 是否可一起展示（1是，0否）*
     */
    IS_FULL_SHOW((short)39, "isFullShow"),
    DURATION((short)40, "duration"),
    L_GUARD_LEVEL_LIMIT((short)41, "lGuardLevelLimit"),
    RATE_LIMIT_TYPE((short)42, "rateLimitType"),
    RATE_LIMIT_NUM((short)43, "rateLimitNum"),
    RATE_LIMIT_EXPIRY((short)44, "rateLimitExpiry"),
    IS_SHOW((short)45, "isShow"),
    GUARD((short)46, "guard"),
    VIDEO_URL((short)47, "videoUrl"),
    IS_ROLL_GIFT((short)48, "isRollGift"),
    ROLL_NUM((short)49, "rollNum"),
    MULTI_VIDEO_URLS((short)50, "multiVideoUrls"),
    GIFT_TIPS((short)51, "giftTips"),
    /**
     * 星钻等级
     */
    STAR_VIP_LEVEL((short)52, "starVipLevel"),
    /**
     * 竖屏礼物资源
     */
    VERTICAL_VIDEO_URL((short)53, "verticalVideoUrl"),
    /**
     * 额外礼物资源,以后其它新增的资源,能放这里就放这里,透传给前端,不用再发版
     */
    EXT_RESOURCE((short)54, "extResource"),
    /**
     * 原价
     */
    ORIG_PRICE((short)55, "origPrice"),
    /**
     * 上新时间
     */
    NEW_TIME((short)56, "newTime"),
    /**
     * 全站礼物(0:不是,1:是) *
     */
    IS_GLOBAL((short)57, "isGlobal"),
    /**
     * 发全站公告(0:不是,1:是) *
     */
    IS_GLOBAL_NOTICE((short)58, "isGlobalNotice"),
    /**
     * 礼物扩展属性,以后但凡新增礼物属性，都放在这个扩展字段
     */
    EXT_ATTR((short)59, "extAttr");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ID
          return ID;
        case 2: // NAME
          return NAME;
        case 3: // URL
          return URL;
        case 4: // PIC
          return PIC;
        case 5: // PRICE
          return PRICE;
        case 6: // EXCHANGE
          return EXCHANGE;
        case 7: // CATEGORY
          return CATEGORY;
        case 8: // MIX
          return MIX;
        case 9: // FLY
          return FLY;
        case 10: // CLASS_NAME
          return CLASS_NAME;
        case 11: // SORT_INDEX
          return SORT_INDEX;
        case 12: // STATUS
          return STATUS;
        case 13: // IMAGE
          return IMAGE;
        case 14: // IS_NEW
          return IS_NEW;
        case 15: // IMAGE_TRANS
          return IMAGE_TRANS;
        case 16: // IMAGE_GRADE
          return IMAGE_GRADE;
        case 17: // EXPIRE
          return EXPIRE;
        case 18: // RICH_LEVEL_LIMIT
          return RICH_LEVEL_LIMIT;
        case 19: // TYPE
          return TYPE;
        case 20: // ADT_EFFECT
          return ADT_EFFECT;
        case 21: // GUARD_LEVEL_LIMIT
          return GUARD_LEVEL_LIMIT;
        case 22: // VIP_LIMIT
          return VIP_LIMIT;
        case 23: // IS_PILE
          return IS_PILE;
        case 24: // CAN_NOT_SEND
          return CAN_NOT_SEND;
        case 25: // EXTRA_TYPE
          return EXTRA_TYPE;
        case 26: // EXTRA
          return EXTRA;
        case 27: // USER_ID_LIMIT
          return USER_ID_LIMIT;
        case 28: // USER_LIMIT_NICK_NAME
          return USER_LIMIT_NICK_NAME;
        case 29: // STAR_FANS_LIMIT
          return STAR_FANS_LIMIT;
        case 30: // GIFT_URL
          return GIFT_URL;
        case 31: // SPECIAL_TYPE
          return SPECIAL_TYPE;
        case 32: // TOP_COUNT
          return TOP_COUNT;
        case 33: // WEEK
          return WEEK;
        case 34: // IS_SUPER
          return IS_SUPER;
        case 35: // HAPPY_OBJ
          return HAPPY_OBJ;
        case 36: // HAPPY_TYPE
          return HAPPY_TYPE;
        case 37: // MOBILE_IMAGE
          return MOBILE_IMAGE;
        case 38: // IS_PK
          return IS_PK;
        case 39: // IS_FULL_SHOW
          return IS_FULL_SHOW;
        case 40: // DURATION
          return DURATION;
        case 41: // L_GUARD_LEVEL_LIMIT
          return L_GUARD_LEVEL_LIMIT;
        case 42: // RATE_LIMIT_TYPE
          return RATE_LIMIT_TYPE;
        case 43: // RATE_LIMIT_NUM
          return RATE_LIMIT_NUM;
        case 44: // RATE_LIMIT_EXPIRY
          return RATE_LIMIT_EXPIRY;
        case 45: // IS_SHOW
          return IS_SHOW;
        case 46: // GUARD
          return GUARD;
        case 47: // VIDEO_URL
          return VIDEO_URL;
        case 48: // IS_ROLL_GIFT
          return IS_ROLL_GIFT;
        case 49: // ROLL_NUM
          return ROLL_NUM;
        case 50: // MULTI_VIDEO_URLS
          return MULTI_VIDEO_URLS;
        case 51: // GIFT_TIPS
          return GIFT_TIPS;
        case 52: // STAR_VIP_LEVEL
          return STAR_VIP_LEVEL;
        case 53: // VERTICAL_VIDEO_URL
          return VERTICAL_VIDEO_URL;
        case 54: // EXT_RESOURCE
          return EXT_RESOURCE;
        case 55: // ORIG_PRICE
          return ORIG_PRICE;
        case 56: // NEW_TIME
          return NEW_TIME;
        case 57: // IS_GLOBAL
          return IS_GLOBAL;
        case 58: // IS_GLOBAL_NOTICE
          return IS_GLOBAL_NOTICE;
        case 59: // EXT_ATTR
          return EXT_ATTR;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ID_ISSET_ID = 0;
  private static final int __PRICE_ISSET_ID = 1;
  private static final int __EXCHANGE_ISSET_ID = 2;
  private static final int __CATEGORY_ISSET_ID = 3;
  private static final int __MIX_ISSET_ID = 4;
  private static final int __FLY_ISSET_ID = 5;
  private static final int __SORTINDEX_ISSET_ID = 6;
  private static final int __STATUS_ISSET_ID = 7;
  private static final int __ISNEW_ISSET_ID = 8;
  private static final int __EXPIRE_ISSET_ID = 9;
  private static final int __RICHLEVELLIMIT_ISSET_ID = 10;
  private static final int __TYPE_ISSET_ID = 11;
  private static final int __GUARDLEVELLIMIT_ISSET_ID = 12;
  private static final int __VIPLIMIT_ISSET_ID = 13;
  private static final int __ISPILE_ISSET_ID = 14;
  private static final int __CANNOTSEND_ISSET_ID = 15;
  private static final int __EXTRATYPE_ISSET_ID = 16;
  private static final int __USERIDLIMIT_ISSET_ID = 17;
  private static final int __STARFANSLIMIT_ISSET_ID = 18;
  private static final int __SPECIALTYPE_ISSET_ID = 19;
  private static final int __TOPCOUNT_ISSET_ID = 20;
  private static final int __WEEK_ISSET_ID = 21;
  private static final int __ISSUPER_ISSET_ID = 22;
  private static final int __HAPPYOBJ_ISSET_ID = 23;
  private static final int __HAPPYTYPE_ISSET_ID = 24;
  private static final int __ISPK_ISSET_ID = 25;
  private static final int __ISFULLSHOW_ISSET_ID = 26;
  private static final int __DURATION_ISSET_ID = 27;
  private static final int __LGUARDLEVELLIMIT_ISSET_ID = 28;
  private static final int __RATELIMITTYPE_ISSET_ID = 29;
  private static final int __RATELIMITNUM_ISSET_ID = 30;
  private static final int __RATELIMITEXPIRY_ISSET_ID = 31;
  private static final int __ISSHOW_ISSET_ID = 32;
  private static final int __GUARD_ISSET_ID = 33;
  private static final int __ISROLLGIFT_ISSET_ID = 34;
  private static final int __ROLLNUM_ISSET_ID = 35;
  private static final int __STARVIPLEVEL_ISSET_ID = 36;
  private static final int __ORIGPRICE_ISSET_ID = 37;
  private static final int __NEWTIME_ISSET_ID = 38;
  private static final int __ISGLOBAL_ISSET_ID = 39;
  private static final int __ISGLOBALNOTICE_ISSET_ID = 40;
  private long __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.ID,_Fields.NAME,_Fields.URL,_Fields.PIC,_Fields.PRICE,_Fields.EXCHANGE,_Fields.CATEGORY,_Fields.MIX,_Fields.FLY,_Fields.CLASS_NAME,_Fields.SORT_INDEX,_Fields.STATUS,_Fields.IMAGE,_Fields.IS_NEW,_Fields.IMAGE_TRANS,_Fields.IMAGE_GRADE,_Fields.EXPIRE,_Fields.RICH_LEVEL_LIMIT,_Fields.TYPE,_Fields.ADT_EFFECT,_Fields.GUARD_LEVEL_LIMIT,_Fields.VIP_LIMIT,_Fields.IS_PILE,_Fields.CAN_NOT_SEND,_Fields.EXTRA_TYPE,_Fields.EXTRA,_Fields.USER_ID_LIMIT,_Fields.USER_LIMIT_NICK_NAME,_Fields.STAR_FANS_LIMIT,_Fields.GIFT_URL,_Fields.SPECIAL_TYPE,_Fields.TOP_COUNT,_Fields.WEEK,_Fields.IS_SUPER,_Fields.HAPPY_OBJ,_Fields.HAPPY_TYPE,_Fields.MOBILE_IMAGE,_Fields.IS_PK,_Fields.IS_FULL_SHOW,_Fields.DURATION,_Fields.L_GUARD_LEVEL_LIMIT,_Fields.RATE_LIMIT_TYPE,_Fields.RATE_LIMIT_NUM,_Fields.RATE_LIMIT_EXPIRY,_Fields.IS_SHOW,_Fields.GUARD,_Fields.VIDEO_URL,_Fields.IS_ROLL_GIFT,_Fields.ROLL_NUM,_Fields.MULTI_VIDEO_URLS,_Fields.GIFT_TIPS,_Fields.STAR_VIP_LEVEL,_Fields.VERTICAL_VIDEO_URL,_Fields.EXT_RESOURCE,_Fields.ORIG_PRICE,_Fields.NEW_TIME,_Fields.IS_GLOBAL,_Fields.IS_GLOBAL_NOTICE,_Fields.EXT_ATTR};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ID, new org.apache.thrift.meta_data.FieldMetaData("id", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.NAME, new org.apache.thrift.meta_data.FieldMetaData("name", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.URL, new org.apache.thrift.meta_data.FieldMetaData("url", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PIC, new org.apache.thrift.meta_data.FieldMetaData("pic", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PRICE, new org.apache.thrift.meta_data.FieldMetaData("price", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.EXCHANGE, new org.apache.thrift.meta_data.FieldMetaData("exchange", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.DOUBLE)));
    tmpMap.put(_Fields.CATEGORY, new org.apache.thrift.meta_data.FieldMetaData("category", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.MIX, new org.apache.thrift.meta_data.FieldMetaData("mix", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.FLY, new org.apache.thrift.meta_data.FieldMetaData("fly", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.CLASS_NAME, new org.apache.thrift.meta_data.FieldMetaData("className", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SORT_INDEX, new org.apache.thrift.meta_data.FieldMetaData("sortIndex", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.STATUS, new org.apache.thrift.meta_data.FieldMetaData("status", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.IMAGE, new org.apache.thrift.meta_data.FieldMetaData("image", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.IS_NEW, new org.apache.thrift.meta_data.FieldMetaData("isNew", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.IMAGE_TRANS, new org.apache.thrift.meta_data.FieldMetaData("imageTrans", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.IMAGE_GRADE, new org.apache.thrift.meta_data.FieldMetaData("imageGrade", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXPIRE, new org.apache.thrift.meta_data.FieldMetaData("expire", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.RICH_LEVEL_LIMIT, new org.apache.thrift.meta_data.FieldMetaData("richLevelLimit", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TYPE, new org.apache.thrift.meta_data.FieldMetaData("type", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ADT_EFFECT, new org.apache.thrift.meta_data.FieldMetaData("adtEffect", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.GUARD_LEVEL_LIMIT, new org.apache.thrift.meta_data.FieldMetaData("guardLevelLimit", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.VIP_LIMIT, new org.apache.thrift.meta_data.FieldMetaData("vipLimit", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.IS_PILE, new org.apache.thrift.meta_data.FieldMetaData("isPile", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.CAN_NOT_SEND, new org.apache.thrift.meta_data.FieldMetaData("canNotSend", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.EXTRA_TYPE, new org.apache.thrift.meta_data.FieldMetaData("extraType", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.EXTRA, new org.apache.thrift.meta_data.FieldMetaData("extra", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.USER_ID_LIMIT, new org.apache.thrift.meta_data.FieldMetaData("userIdLimit", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.USER_LIMIT_NICK_NAME, new org.apache.thrift.meta_data.FieldMetaData("userLimitNickName", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.STAR_FANS_LIMIT, new org.apache.thrift.meta_data.FieldMetaData("starFansLimit", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.GIFT_URL, new org.apache.thrift.meta_data.FieldMetaData("giftUrl", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SPECIAL_TYPE, new org.apache.thrift.meta_data.FieldMetaData("specialType", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TOP_COUNT, new org.apache.thrift.meta_data.FieldMetaData("topCount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.WEEK, new org.apache.thrift.meta_data.FieldMetaData("week", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.IS_SUPER, new org.apache.thrift.meta_data.FieldMetaData("isSuper", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.HAPPY_OBJ, new org.apache.thrift.meta_data.FieldMetaData("happyObj", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.HAPPY_TYPE, new org.apache.thrift.meta_data.FieldMetaData("happyType", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.MOBILE_IMAGE, new org.apache.thrift.meta_data.FieldMetaData("mobileImage", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.IS_PK, new org.apache.thrift.meta_data.FieldMetaData("isPk", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.IS_FULL_SHOW, new org.apache.thrift.meta_data.FieldMetaData("isFullShow", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.DURATION, new org.apache.thrift.meta_data.FieldMetaData("duration", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.L_GUARD_LEVEL_LIMIT, new org.apache.thrift.meta_data.FieldMetaData("lGuardLevelLimit", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.RATE_LIMIT_TYPE, new org.apache.thrift.meta_data.FieldMetaData("rateLimitType", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.RATE_LIMIT_NUM, new org.apache.thrift.meta_data.FieldMetaData("rateLimitNum", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.RATE_LIMIT_EXPIRY, new org.apache.thrift.meta_data.FieldMetaData("rateLimitExpiry", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.IS_SHOW, new org.apache.thrift.meta_data.FieldMetaData("isShow", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.GUARD, new org.apache.thrift.meta_data.FieldMetaData("guard", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.VIDEO_URL, new org.apache.thrift.meta_data.FieldMetaData("videoUrl", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.IS_ROLL_GIFT, new org.apache.thrift.meta_data.FieldMetaData("isRollGift", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ROLL_NUM, new org.apache.thrift.meta_data.FieldMetaData("rollNum", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.MULTI_VIDEO_URLS, new org.apache.thrift.meta_data.FieldMetaData("multiVideoUrls", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.GIFT_TIPS, new org.apache.thrift.meta_data.FieldMetaData("giftTips", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.STAR_VIP_LEVEL, new org.apache.thrift.meta_data.FieldMetaData("starVipLevel", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.VERTICAL_VIDEO_URL, new org.apache.thrift.meta_data.FieldMetaData("verticalVideoUrl", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT_RESOURCE, new org.apache.thrift.meta_data.FieldMetaData("extResource", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ORIG_PRICE, new org.apache.thrift.meta_data.FieldMetaData("origPrice", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.NEW_TIME, new org.apache.thrift.meta_data.FieldMetaData("newTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.IS_GLOBAL, new org.apache.thrift.meta_data.FieldMetaData("isGlobal", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.IS_GLOBAL_NOTICE, new org.apache.thrift.meta_data.FieldMetaData("isGlobalNotice", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.EXT_ATTR, new org.apache.thrift.meta_data.FieldMetaData("extAttr", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GiftInfo.class, metaDataMap);
  }

  public GiftInfo() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GiftInfo(GiftInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.id = other.id;
    if (other.isSetName()) {
      this.name = other.name;
    }
    if (other.isSetUrl()) {
      this.url = other.url;
    }
    if (other.isSetPic()) {
      this.pic = other.pic;
    }
    this.price = other.price;
    this.exchange = other.exchange;
    this.category = other.category;
    this.mix = other.mix;
    this.fly = other.fly;
    if (other.isSetClassName()) {
      this.className = other.className;
    }
    this.sortIndex = other.sortIndex;
    this.status = other.status;
    if (other.isSetImage()) {
      this.image = other.image;
    }
    this.isNew = other.isNew;
    if (other.isSetImageTrans()) {
      this.imageTrans = other.imageTrans;
    }
    if (other.isSetImageGrade()) {
      this.imageGrade = other.imageGrade;
    }
    this.expire = other.expire;
    this.richLevelLimit = other.richLevelLimit;
    this.type = other.type;
    if (other.isSetAdtEffect()) {
      this.adtEffect = other.adtEffect;
    }
    this.guardLevelLimit = other.guardLevelLimit;
    this.vipLimit = other.vipLimit;
    this.isPile = other.isPile;
    this.canNotSend = other.canNotSend;
    this.extraType = other.extraType;
    if (other.isSetExtra()) {
      this.extra = other.extra;
    }
    this.userIdLimit = other.userIdLimit;
    if (other.isSetUserLimitNickName()) {
      this.userLimitNickName = other.userLimitNickName;
    }
    this.starFansLimit = other.starFansLimit;
    if (other.isSetGiftUrl()) {
      this.giftUrl = other.giftUrl;
    }
    this.specialType = other.specialType;
    this.topCount = other.topCount;
    this.week = other.week;
    this.isSuper = other.isSuper;
    this.happyObj = other.happyObj;
    this.happyType = other.happyType;
    if (other.isSetMobileImage()) {
      this.mobileImage = other.mobileImage;
    }
    this.isPk = other.isPk;
    this.isFullShow = other.isFullShow;
    this.duration = other.duration;
    this.lGuardLevelLimit = other.lGuardLevelLimit;
    this.rateLimitType = other.rateLimitType;
    this.rateLimitNum = other.rateLimitNum;
    this.rateLimitExpiry = other.rateLimitExpiry;
    this.isShow = other.isShow;
    this.guard = other.guard;
    if (other.isSetVideoUrl()) {
      this.videoUrl = other.videoUrl;
    }
    this.isRollGift = other.isRollGift;
    this.rollNum = other.rollNum;
    if (other.isSetMultiVideoUrls()) {
      this.multiVideoUrls = other.multiVideoUrls;
    }
    if (other.isSetGiftTips()) {
      this.giftTips = other.giftTips;
    }
    this.starVipLevel = other.starVipLevel;
    if (other.isSetVerticalVideoUrl()) {
      this.verticalVideoUrl = other.verticalVideoUrl;
    }
    if (other.isSetExtResource()) {
      this.extResource = other.extResource;
    }
    this.origPrice = other.origPrice;
    this.newTime = other.newTime;
    this.isGlobal = other.isGlobal;
    this.isGlobalNotice = other.isGlobalNotice;
    if (other.isSetExtAttr()) {
      this.extAttr = other.extAttr;
    }
  }

  public GiftInfo deepCopy() {
    return new GiftInfo(this);
  }

  @Override
  public void clear() {
    setIdIsSet(false);
    this.id = 0;
    this.name = null;
    this.url = null;
    this.pic = null;
    setPriceIsSet(false);
    this.price = 0;
    setExchangeIsSet(false);
    this.exchange = 0.0;
    setCategoryIsSet(false);
    this.category = 0;
    setMixIsSet(false);
    this.mix = 0;
    setFlyIsSet(false);
    this.fly = 0;
    this.className = null;
    setSortIndexIsSet(false);
    this.sortIndex = 0;
    setStatusIsSet(false);
    this.status = 0;
    this.image = null;
    setIsNewIsSet(false);
    this.isNew = 0;
    this.imageTrans = null;
    this.imageGrade = null;
    setExpireIsSet(false);
    this.expire = 0;
    setRichLevelLimitIsSet(false);
    this.richLevelLimit = 0;
    setTypeIsSet(false);
    this.type = 0;
    this.adtEffect = null;
    setGuardLevelLimitIsSet(false);
    this.guardLevelLimit = 0;
    setVipLimitIsSet(false);
    this.vipLimit = 0;
    setIsPileIsSet(false);
    this.isPile = 0;
    setCanNotSendIsSet(false);
    this.canNotSend = 0;
    setExtraTypeIsSet(false);
    this.extraType = 0;
    this.extra = null;
    setUserIdLimitIsSet(false);
    this.userIdLimit = 0;
    this.userLimitNickName = null;
    setStarFansLimitIsSet(false);
    this.starFansLimit = 0;
    this.giftUrl = null;
    setSpecialTypeIsSet(false);
    this.specialType = 0;
    setTopCountIsSet(false);
    this.topCount = 0;
    setWeekIsSet(false);
    this.week = 0;
    setIsSuperIsSet(false);
    this.isSuper = 0;
    setHappyObjIsSet(false);
    this.happyObj = 0;
    setHappyTypeIsSet(false);
    this.happyType = 0;
    this.mobileImage = null;
    setIsPkIsSet(false);
    this.isPk = 0;
    setIsFullShowIsSet(false);
    this.isFullShow = 0;
    setDurationIsSet(false);
    this.duration = 0;
    setLGuardLevelLimitIsSet(false);
    this.lGuardLevelLimit = 0;
    setRateLimitTypeIsSet(false);
    this.rateLimitType = 0;
    setRateLimitNumIsSet(false);
    this.rateLimitNum = 0;
    setRateLimitExpiryIsSet(false);
    this.rateLimitExpiry = 0;
    setIsShowIsSet(false);
    this.isShow = 0;
    setGuardIsSet(false);
    this.guard = 0;
    this.videoUrl = null;
    setIsRollGiftIsSet(false);
    this.isRollGift = 0;
    setRollNumIsSet(false);
    this.rollNum = 0;
    this.multiVideoUrls = null;
    this.giftTips = null;
    setStarVipLevelIsSet(false);
    this.starVipLevel = 0;
    this.verticalVideoUrl = null;
    this.extResource = null;
    setOrigPriceIsSet(false);
    this.origPrice = 0;
    setNewTimeIsSet(false);
    this.newTime = 0;
    setIsGlobalIsSet(false);
    this.isGlobal = 0;
    setIsGlobalNoticeIsSet(false);
    this.isGlobalNotice = 0;
    this.extAttr = null;
  }

  /**
   * id
   */
  public int getId() {
    return this.id;
  }

  /**
   * id
   */
  public GiftInfo setId(int id) {
    this.id = id;
    setIdIsSet(true);
    return this;
  }

  public void unsetId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ID_ISSET_ID);
  }

  /** Returns true if field id is set (has been assigned a value) and false otherwise */
  public boolean isSetId() {
    return EncodingUtils.testBit(__isset_bitfield, __ID_ISSET_ID);
  }

  public void setIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ID_ISSET_ID, value);
  }

  /**
   * name
   */
  public String getName() {
    return this.name;
  }

  /**
   * name
   */
  public GiftInfo setName(String name) {
    this.name = name;
    return this;
  }

  public void unsetName() {
    this.name = null;
  }

  /** Returns true if field name is set (has been assigned a value) and false otherwise */
  public boolean isSetName() {
    return this.name != null;
  }

  public void setNameIsSet(boolean value) {
    if (!value) {
      this.name = null;
    }
  }

  /**
   * url
   */
  public String getUrl() {
    return this.url;
  }

  /**
   * url
   */
  public GiftInfo setUrl(String url) {
    this.url = url;
    return this;
  }

  public void unsetUrl() {
    this.url = null;
  }

  /** Returns true if field url is set (has been assigned a value) and false otherwise */
  public boolean isSetUrl() {
    return this.url != null;
  }

  public void setUrlIsSet(boolean value) {
    if (!value) {
      this.url = null;
    }
  }

  /**
   * pic
   */
  public String getPic() {
    return this.pic;
  }

  /**
   * pic
   */
  public GiftInfo setPic(String pic) {
    this.pic = pic;
    return this;
  }

  public void unsetPic() {
    this.pic = null;
  }

  /** Returns true if field pic is set (has been assigned a value) and false otherwise */
  public boolean isSetPic() {
    return this.pic != null;
  }

  public void setPicIsSet(boolean value) {
    if (!value) {
      this.pic = null;
    }
  }

  /**
   * price
   */
  public int getPrice() {
    return this.price;
  }

  /**
   * price
   */
  public GiftInfo setPrice(int price) {
    this.price = price;
    setPriceIsSet(true);
    return this;
  }

  public void unsetPrice() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PRICE_ISSET_ID);
  }

  /** Returns true if field price is set (has been assigned a value) and false otherwise */
  public boolean isSetPrice() {
    return EncodingUtils.testBit(__isset_bitfield, __PRICE_ISSET_ID);
  }

  public void setPriceIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PRICE_ISSET_ID, value);
  }

  /**
   * exchange
   */
  public double getExchange() {
    return this.exchange;
  }

  /**
   * exchange
   */
  public GiftInfo setExchange(double exchange) {
    this.exchange = exchange;
    setExchangeIsSet(true);
    return this;
  }

  public void unsetExchange() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __EXCHANGE_ISSET_ID);
  }

  /** Returns true if field exchange is set (has been assigned a value) and false otherwise */
  public boolean isSetExchange() {
    return EncodingUtils.testBit(__isset_bitfield, __EXCHANGE_ISSET_ID);
  }

  public void setExchangeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __EXCHANGE_ISSET_ID, value);
  }

  /**
   * category
   */
  public int getCategory() {
    return this.category;
  }

  /**
   * category
   */
  public GiftInfo setCategory(int category) {
    this.category = category;
    setCategoryIsSet(true);
    return this;
  }

  public void unsetCategory() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CATEGORY_ISSET_ID);
  }

  /** Returns true if field category is set (has been assigned a value) and false otherwise */
  public boolean isSetCategory() {
    return EncodingUtils.testBit(__isset_bitfield, __CATEGORY_ISSET_ID);
  }

  public void setCategoryIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CATEGORY_ISSET_ID, value);
  }

  /**
   * 可组合?(1:可以;0:不可以)
   */
  public int getMix() {
    return this.mix;
  }

  /**
   * 可组合?(1:可以;0:不可以)
   */
  public GiftInfo setMix(int mix) {
    this.mix = mix;
    setMixIsSet(true);
    return this;
  }

  public void unsetMix() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __MIX_ISSET_ID);
  }

  /** Returns true if field mix is set (has been assigned a value) and false otherwise */
  public boolean isSetMix() {
    return EncodingUtils.testBit(__isset_bitfield, __MIX_ISSET_ID);
  }

  public void setMixIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __MIX_ISSET_ID, value);
  }

  /**
   * 可飞屏?(1:可以;0:不可以)
   */
  public int getFly() {
    return this.fly;
  }

  /**
   * 可飞屏?(1:可以;0:不可以)
   */
  public GiftInfo setFly(int fly) {
    this.fly = fly;
    setFlyIsSet(true);
    return this;
  }

  public void unsetFly() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __FLY_ISSET_ID);
  }

  /** Returns true if field fly is set (has been assigned a value) and false otherwise */
  public boolean isSetFly() {
    return EncodingUtils.testBit(__isset_bitfield, __FLY_ISSET_ID);
  }

  public void setFlyIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __FLY_ISSET_ID, value);
  }

  /**
   * 对应类 *
   */
  public String getClassName() {
    return this.className;
  }

  /**
   * 对应类 *
   */
  public GiftInfo setClassName(String className) {
    this.className = className;
    return this;
  }

  public void unsetClassName() {
    this.className = null;
  }

  /** Returns true if field className is set (has been assigned a value) and false otherwise */
  public boolean isSetClassName() {
    return this.className != null;
  }

  public void setClassNameIsSet(boolean value) {
    if (!value) {
      this.className = null;
    }
  }

  /**
   * sortIndex
   */
  public int getSortIndex() {
    return this.sortIndex;
  }

  /**
   * sortIndex
   */
  public GiftInfo setSortIndex(int sortIndex) {
    this.sortIndex = sortIndex;
    setSortIndexIsSet(true);
    return this;
  }

  public void unsetSortIndex() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SORTINDEX_ISSET_ID);
  }

  /** Returns true if field sortIndex is set (has been assigned a value) and false otherwise */
  public boolean isSetSortIndex() {
    return EncodingUtils.testBit(__isset_bitfield, __SORTINDEX_ISSET_ID);
  }

  public void setSortIndexIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SORTINDEX_ISSET_ID, value);
  }

  /**
   * status
   */
  public int getStatus() {
    return this.status;
  }

  /**
   * status
   */
  public GiftInfo setStatus(int status) {
    this.status = status;
    setStatusIsSet(true);
    return this;
  }

  public void unsetStatus() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  /** Returns true if field status is set (has been assigned a value) and false otherwise */
  public boolean isSetStatus() {
    return EncodingUtils.testBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  public void setStatusIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STATUS_ISSET_ID, value);
  }

  /**
   * image
   */
  public String getImage() {
    return this.image;
  }

  /**
   * image
   */
  public GiftInfo setImage(String image) {
    this.image = image;
    return this;
  }

  public void unsetImage() {
    this.image = null;
  }

  /** Returns true if field image is set (has been assigned a value) and false otherwise */
  public boolean isSetImage() {
    return this.image != null;
  }

  public void setImageIsSet(boolean value) {
    if (!value) {
      this.image = null;
    }
  }

  /**
   * isNew 新上架?(1:是;0:否)
   */
  public int getIsNew() {
    return this.isNew;
  }

  /**
   * isNew 新上架?(1:是;0:否)
   */
  public GiftInfo setIsNew(int isNew) {
    this.isNew = isNew;
    setIsNewIsSet(true);
    return this;
  }

  public void unsetIsNew() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ISNEW_ISSET_ID);
  }

  /** Returns true if field isNew is set (has been assigned a value) and false otherwise */
  public boolean isSetIsNew() {
    return EncodingUtils.testBit(__isset_bitfield, __ISNEW_ISSET_ID);
  }

  public void setIsNewIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ISNEW_ISSET_ID, value);
  }

  /**
   * 缩略图(透明) *
   */
  public String getImageTrans() {
    return this.imageTrans;
  }

  /**
   * 缩略图(透明) *
   */
  public GiftInfo setImageTrans(String imageTrans) {
    this.imageTrans = imageTrans;
    return this;
  }

  public void unsetImageTrans() {
    this.imageTrans = null;
  }

  /** Returns true if field imageTrans is set (has been assigned a value) and false otherwise */
  public boolean isSetImageTrans() {
    return this.imageTrans != null;
  }

  public void setImageTransIsSet(boolean value) {
    if (!value) {
      this.imageTrans = null;
    }
  }

  /**
   * 缩略图(灰底) *
   */
  public String getImageGrade() {
    return this.imageGrade;
  }

  /**
   * 缩略图(灰底) *
   */
  public GiftInfo setImageGrade(String imageGrade) {
    this.imageGrade = imageGrade;
    return this;
  }

  public void unsetImageGrade() {
    this.imageGrade = null;
  }

  /** Returns true if field imageGrade is set (has been assigned a value) and false otherwise */
  public boolean isSetImageGrade() {
    return this.imageGrade != null;
  }

  public void setImageGradeIsSet(boolean value) {
    if (!value) {
      this.imageGrade = null;
    }
  }

  /**
   * expire
   */
  public int getExpire() {
    return this.expire;
  }

  /**
   * expire
   */
  public GiftInfo setExpire(int expire) {
    this.expire = expire;
    setExpireIsSet(true);
    return this;
  }

  public void unsetExpire() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __EXPIRE_ISSET_ID);
  }

  /** Returns true if field expire is set (has been assigned a value) and false otherwise */
  public boolean isSetExpire() {
    return EncodingUtils.testBit(__isset_bitfield, __EXPIRE_ISSET_ID);
  }

  public void setExpireIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __EXPIRE_ISSET_ID, value);
  }

  /**
   * richLevelLimit
   */
  public int getRichLevelLimit() {
    return this.richLevelLimit;
  }

  /**
   * richLevelLimit
   */
  public GiftInfo setRichLevelLimit(int richLevelLimit) {
    this.richLevelLimit = richLevelLimit;
    setRichLevelLimitIsSet(true);
    return this;
  }

  public void unsetRichLevelLimit() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RICHLEVELLIMIT_ISSET_ID);
  }

  /** Returns true if field richLevelLimit is set (has been assigned a value) and false otherwise */
  public boolean isSetRichLevelLimit() {
    return EncodingUtils.testBit(__isset_bitfield, __RICHLEVELLIMIT_ISSET_ID);
  }

  public void setRichLevelLimitIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RICHLEVELLIMIT_ISSET_ID, value);
  }

  /**
   * type
   */
  public int getType() {
    return this.type;
  }

  /**
   * type
   */
  public GiftInfo setType(int type) {
    this.type = type;
    setTypeIsSet(true);
    return this;
  }

  public void unsetType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TYPE_ISSET_ID);
  }

  /** Returns true if field type is set (has been assigned a value) and false otherwise */
  public boolean isSetType() {
    return EncodingUtils.testBit(__isset_bitfield, __TYPE_ISSET_ID);
  }

  public void setTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TYPE_ISSET_ID, value);
  }

  /**
   * 安卓礼物效果图压缩包路径 *
   */
  public String getAdtEffect() {
    return this.adtEffect;
  }

  /**
   * 安卓礼物效果图压缩包路径 *
   */
  public GiftInfo setAdtEffect(String adtEffect) {
    this.adtEffect = adtEffect;
    return this;
  }

  public void unsetAdtEffect() {
    this.adtEffect = null;
  }

  /** Returns true if field adtEffect is set (has been assigned a value) and false otherwise */
  public boolean isSetAdtEffect() {
    return this.adtEffect != null;
  }

  public void setAdtEffectIsSet(boolean value) {
    if (!value) {
      this.adtEffect = null;
    }
  }

  /**
   * guardLevelLimit
   */
  public int getGuardLevelLimit() {
    return this.guardLevelLimit;
  }

  /**
   * guardLevelLimit
   */
  public GiftInfo setGuardLevelLimit(int guardLevelLimit) {
    this.guardLevelLimit = guardLevelLimit;
    setGuardLevelLimitIsSet(true);
    return this;
  }

  public void unsetGuardLevelLimit() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __GUARDLEVELLIMIT_ISSET_ID);
  }

  /** Returns true if field guardLevelLimit is set (has been assigned a value) and false otherwise */
  public boolean isSetGuardLevelLimit() {
    return EncodingUtils.testBit(__isset_bitfield, __GUARDLEVELLIMIT_ISSET_ID);
  }

  public void setGuardLevelLimitIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __GUARDLEVELLIMIT_ISSET_ID, value);
  }

  /**
   * vipLimit
   */
  public int getVipLimit() {
    return this.vipLimit;
  }

  /**
   * vipLimit
   */
  public GiftInfo setVipLimit(int vipLimit) {
    this.vipLimit = vipLimit;
    setVipLimitIsSet(true);
    return this;
  }

  public void unsetVipLimit() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __VIPLIMIT_ISSET_ID);
  }

  /** Returns true if field vipLimit is set (has been assigned a value) and false otherwise */
  public boolean isSetVipLimit() {
    return EncodingUtils.testBit(__isset_bitfield, __VIPLIMIT_ISSET_ID);
  }

  public void setVipLimitIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __VIPLIMIT_ISSET_ID, value);
  }

  /**
   * isPile
   */
  public int getIsPile() {
    return this.isPile;
  }

  /**
   * isPile
   */
  public GiftInfo setIsPile(int isPile) {
    this.isPile = isPile;
    setIsPileIsSet(true);
    return this;
  }

  public void unsetIsPile() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ISPILE_ISSET_ID);
  }

  /** Returns true if field isPile is set (has been assigned a value) and false otherwise */
  public boolean isSetIsPile() {
    return EncodingUtils.testBit(__isset_bitfield, __ISPILE_ISSET_ID);
  }

  public void setIsPileIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ISPILE_ISSET_ID, value);
  }

  /**
   * canNotSend
   */
  public int getCanNotSend() {
    return this.canNotSend;
  }

  /**
   * canNotSend
   */
  public GiftInfo setCanNotSend(int canNotSend) {
    this.canNotSend = canNotSend;
    setCanNotSendIsSet(true);
    return this;
  }

  public void unsetCanNotSend() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CANNOTSEND_ISSET_ID);
  }

  /** Returns true if field canNotSend is set (has been assigned a value) and false otherwise */
  public boolean isSetCanNotSend() {
    return EncodingUtils.testBit(__isset_bitfield, __CANNOTSEND_ISSET_ID);
  }

  public void setCanNotSendIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CANNOTSEND_ISSET_ID, value);
  }

  /**
   * extraType
   */
  public int getExtraType() {
    return this.extraType;
  }

  /**
   * extraType
   */
  public GiftInfo setExtraType(int extraType) {
    this.extraType = extraType;
    setExtraTypeIsSet(true);
    return this;
  }

  public void unsetExtraType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __EXTRATYPE_ISSET_ID);
  }

  /** Returns true if field extraType is set (has been assigned a value) and false otherwise */
  public boolean isSetExtraType() {
    return EncodingUtils.testBit(__isset_bitfield, __EXTRATYPE_ISSET_ID);
  }

  public void setExtraTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __EXTRATYPE_ISSET_ID, value);
  }

  /**
   * extra
   */
  public String getExtra() {
    return this.extra;
  }

  /**
   * extra
   */
  public GiftInfo setExtra(String extra) {
    this.extra = extra;
    return this;
  }

  public void unsetExtra() {
    this.extra = null;
  }

  /** Returns true if field extra is set (has been assigned a value) and false otherwise */
  public boolean isSetExtra() {
    return this.extra != null;
  }

  public void setExtraIsSet(boolean value) {
    if (!value) {
      this.extra = null;
    }
  }

  /**
   * userIdLimit
   */
  public long getUserIdLimit() {
    return this.userIdLimit;
  }

  /**
   * userIdLimit
   */
  public GiftInfo setUserIdLimit(long userIdLimit) {
    this.userIdLimit = userIdLimit;
    setUserIdLimitIsSet(true);
    return this;
  }

  public void unsetUserIdLimit() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __USERIDLIMIT_ISSET_ID);
  }

  /** Returns true if field userIdLimit is set (has been assigned a value) and false otherwise */
  public boolean isSetUserIdLimit() {
    return EncodingUtils.testBit(__isset_bitfield, __USERIDLIMIT_ISSET_ID);
  }

  public void setUserIdLimitIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __USERIDLIMIT_ISSET_ID, value);
  }

  /**
   * userLimitNickName *
   */
  public String getUserLimitNickName() {
    return this.userLimitNickName;
  }

  /**
   * userLimitNickName *
   */
  public GiftInfo setUserLimitNickName(String userLimitNickName) {
    this.userLimitNickName = userLimitNickName;
    return this;
  }

  public void unsetUserLimitNickName() {
    this.userLimitNickName = null;
  }

  /** Returns true if field userLimitNickName is set (has been assigned a value) and false otherwise */
  public boolean isSetUserLimitNickName() {
    return this.userLimitNickName != null;
  }

  public void setUserLimitNickNameIsSet(boolean value) {
    if (!value) {
      this.userLimitNickName = null;
    }
  }

  /**
   * 星粉等级限制，0为不限制 *
   */
  public int getStarFansLimit() {
    return this.starFansLimit;
  }

  /**
   * 星粉等级限制，0为不限制 *
   */
  public GiftInfo setStarFansLimit(int starFansLimit) {
    this.starFansLimit = starFansLimit;
    setStarFansLimitIsSet(true);
    return this;
  }

  public void unsetStarFansLimit() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STARFANSLIMIT_ISSET_ID);
  }

  /** Returns true if field starFansLimit is set (has been assigned a value) and false otherwise */
  public boolean isSetStarFansLimit() {
    return EncodingUtils.testBit(__isset_bitfield, __STARFANSLIMIT_ISSET_ID);
  }

  public void setStarFansLimitIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STARFANSLIMIT_ISSET_ID, value);
  }

  /**
   * 礼物图url *
   */
  public String getGiftUrl() {
    return this.giftUrl;
  }

  /**
   * 礼物图url *
   */
  public GiftInfo setGiftUrl(String giftUrl) {
    this.giftUrl = giftUrl;
    return this;
  }

  public void unsetGiftUrl() {
    this.giftUrl = null;
  }

  /** Returns true if field giftUrl is set (has been assigned a value) and false otherwise */
  public boolean isSetGiftUrl() {
    return this.giftUrl != null;
  }

  public void setGiftUrlIsSet(boolean value) {
    if (!value) {
      this.giftUrl = null;
    }
  }

  /**
   * 特殊类型  0默认 1星星 2幸运礼物 3普天同庆 4未知(前后端都查不到) 5数字专辑 *
   */
  public int getSpecialType() {
    return this.specialType;
  }

  /**
   * 特殊类型  0默认 1星星 2幸运礼物 3普天同庆 4未知(前后端都查不到) 5数字专辑 *
   */
  public GiftInfo setSpecialType(int specialType) {
    this.specialType = specialType;
    setSpecialTypeIsSet(true);
    return this;
  }

  public void unsetSpecialType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SPECIALTYPE_ISSET_ID);
  }

  /** Returns true if field specialType is set (has been assigned a value) and false otherwise */
  public boolean isSetSpecialType() {
    return EncodingUtils.testBit(__isset_bitfield, __SPECIALTYPE_ISSET_ID);
  }

  public void setSpecialTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SPECIALTYPE_ISSET_ID, value);
  }

  /**
   * 是否计入排行榜 *
   */
  public int getTopCount() {
    return this.topCount;
  }

  /**
   * 是否计入排行榜 *
   */
  public GiftInfo setTopCount(int topCount) {
    this.topCount = topCount;
    setTopCountIsSet(true);
    return this;
  }

  public void unsetTopCount() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TOPCOUNT_ISSET_ID);
  }

  /** Returns true if field topCount is set (has been assigned a value) and false otherwise */
  public boolean isSetTopCount() {
    return EncodingUtils.testBit(__isset_bitfield, __TOPCOUNT_ISSET_ID);
  }

  public void setTopCountIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TOPCOUNT_ISSET_ID, value);
  }

  /**
   * 是否周星 *
   */
  public int getWeek() {
    return this.week;
  }

  /**
   * 是否周星 *
   */
  public GiftInfo setWeek(int week) {
    this.week = week;
    setWeekIsSet(true);
    return this;
  }

  public void unsetWeek() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __WEEK_ISSET_ID);
  }

  /** Returns true if field week is set (has been assigned a value) and false otherwise */
  public boolean isSetWeek() {
    return EncodingUtils.testBit(__isset_bitfield, __WEEK_ISSET_ID);
  }

  public void setWeekIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __WEEK_ISSET_ID, value);
  }

  /**
   * 是否超星 *
   */
  public int getIsSuper() {
    return this.isSuper;
  }

  /**
   * 是否超星 *
   */
  public GiftInfo setIsSuper(int isSuper) {
    this.isSuper = isSuper;
    setIsSuperIsSet(true);
    return this;
  }

  public void unsetIsSuper() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ISSUPER_ISSET_ID);
  }

  /** Returns true if field isSuper is set (has been assigned a value) and false otherwise */
  public boolean isSetIsSuper() {
    return EncodingUtils.testBit(__isset_bitfield, __ISSUPER_ISSET_ID);
  }

  public void setIsSuperIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ISSUPER_ISSET_ID, value);
  }

  /**
   * 财神类礼物对象(1:观众,2:管理) *
   */
  public int getHappyObj() {
    return this.happyObj;
  }

  /**
   * 财神类礼物对象(1:观众,2:管理) *
   */
  public GiftInfo setHappyObj(int happyObj) {
    this.happyObj = happyObj;
    setHappyObjIsSet(true);
    return this;
  }

  public void unsetHappyObj() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __HAPPYOBJ_ISSET_ID);
  }

  /** Returns true if field happyObj is set (has been assigned a value) and false otherwise */
  public boolean isSetHappyObj() {
    return EncodingUtils.testBit(__isset_bitfield, __HAPPYOBJ_ISSET_ID);
  }

  public void setHappyObjIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __HAPPYOBJ_ISSET_ID, value);
  }

  /**
   * 财神类礼物类型(礼物类型(1:抢星币,2:平均分)) *
   */
  public int getHappyType() {
    return this.happyType;
  }

  /**
   * 财神类礼物类型(礼物类型(1:抢星币,2:平均分)) *
   */
  public GiftInfo setHappyType(int happyType) {
    this.happyType = happyType;
    setHappyTypeIsSet(true);
    return this;
  }

  public void unsetHappyType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __HAPPYTYPE_ISSET_ID);
  }

  /** Returns true if field happyType is set (has been assigned a value) and false otherwise */
  public boolean isSetHappyType() {
    return EncodingUtils.testBit(__isset_bitfield, __HAPPYTYPE_ISSET_ID);
  }

  public void setHappyTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __HAPPYTYPE_ISSET_ID, value);
  }

  /**
   * 手机端礼物图片 *
   */
  public String getMobileImage() {
    return this.mobileImage;
  }

  /**
   * 手机端礼物图片 *
   */
  public GiftInfo setMobileImage(String mobileImage) {
    this.mobileImage = mobileImage;
    return this;
  }

  public void unsetMobileImage() {
    this.mobileImage = null;
  }

  /** Returns true if field mobileImage is set (has been assigned a value) and false otherwise */
  public boolean isSetMobileImage() {
    return this.mobileImage != null;
  }

  public void setMobileImageIsSet(boolean value) {
    if (!value) {
      this.mobileImage = null;
    }
  }

  /**
   * 是否是PK礼物（1是，0否） *
   */
  public int getIsPk() {
    return this.isPk;
  }

  /**
   * 是否是PK礼物（1是，0否） *
   */
  public GiftInfo setIsPk(int isPk) {
    this.isPk = isPk;
    setIsPkIsSet(true);
    return this;
  }

  public void unsetIsPk() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ISPK_ISSET_ID);
  }

  /** Returns true if field isPk is set (has been assigned a value) and false otherwise */
  public boolean isSetIsPk() {
    return EncodingUtils.testBit(__isset_bitfield, __ISPK_ISSET_ID);
  }

  public void setIsPkIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ISPK_ISSET_ID, value);
  }

  /**
   * 是否可一起展示（1是，0否）*
   */
  public int getIsFullShow() {
    return this.isFullShow;
  }

  /**
   * 是否可一起展示（1是，0否）*
   */
  public GiftInfo setIsFullShow(int isFullShow) {
    this.isFullShow = isFullShow;
    setIsFullShowIsSet(true);
    return this;
  }

  public void unsetIsFullShow() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ISFULLSHOW_ISSET_ID);
  }

  /** Returns true if field isFullShow is set (has been assigned a value) and false otherwise */
  public boolean isSetIsFullShow() {
    return EncodingUtils.testBit(__isset_bitfield, __ISFULLSHOW_ISSET_ID);
  }

  public void setIsFullShowIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ISFULLSHOW_ISSET_ID, value);
  }

  public int getDuration() {
    return this.duration;
  }

  public GiftInfo setDuration(int duration) {
    this.duration = duration;
    setDurationIsSet(true);
    return this;
  }

  public void unsetDuration() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __DURATION_ISSET_ID);
  }

  /** Returns true if field duration is set (has been assigned a value) and false otherwise */
  public boolean isSetDuration() {
    return EncodingUtils.testBit(__isset_bitfield, __DURATION_ISSET_ID);
  }

  public void setDurationIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __DURATION_ISSET_ID, value);
  }

  public int getLGuardLevelLimit() {
    return this.lGuardLevelLimit;
  }

  public GiftInfo setLGuardLevelLimit(int lGuardLevelLimit) {
    this.lGuardLevelLimit = lGuardLevelLimit;
    setLGuardLevelLimitIsSet(true);
    return this;
  }

  public void unsetLGuardLevelLimit() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __LGUARDLEVELLIMIT_ISSET_ID);
  }

  /** Returns true if field lGuardLevelLimit is set (has been assigned a value) and false otherwise */
  public boolean isSetLGuardLevelLimit() {
    return EncodingUtils.testBit(__isset_bitfield, __LGUARDLEVELLIMIT_ISSET_ID);
  }

  public void setLGuardLevelLimitIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __LGUARDLEVELLIMIT_ISSET_ID, value);
  }

  public int getRateLimitType() {
    return this.rateLimitType;
  }

  public GiftInfo setRateLimitType(int rateLimitType) {
    this.rateLimitType = rateLimitType;
    setRateLimitTypeIsSet(true);
    return this;
  }

  public void unsetRateLimitType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RATELIMITTYPE_ISSET_ID);
  }

  /** Returns true if field rateLimitType is set (has been assigned a value) and false otherwise */
  public boolean isSetRateLimitType() {
    return EncodingUtils.testBit(__isset_bitfield, __RATELIMITTYPE_ISSET_ID);
  }

  public void setRateLimitTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RATELIMITTYPE_ISSET_ID, value);
  }

  public int getRateLimitNum() {
    return this.rateLimitNum;
  }

  public GiftInfo setRateLimitNum(int rateLimitNum) {
    this.rateLimitNum = rateLimitNum;
    setRateLimitNumIsSet(true);
    return this;
  }

  public void unsetRateLimitNum() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RATELIMITNUM_ISSET_ID);
  }

  /** Returns true if field rateLimitNum is set (has been assigned a value) and false otherwise */
  public boolean isSetRateLimitNum() {
    return EncodingUtils.testBit(__isset_bitfield, __RATELIMITNUM_ISSET_ID);
  }

  public void setRateLimitNumIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RATELIMITNUM_ISSET_ID, value);
  }

  public int getRateLimitExpiry() {
    return this.rateLimitExpiry;
  }

  public GiftInfo setRateLimitExpiry(int rateLimitExpiry) {
    this.rateLimitExpiry = rateLimitExpiry;
    setRateLimitExpiryIsSet(true);
    return this;
  }

  public void unsetRateLimitExpiry() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RATELIMITEXPIRY_ISSET_ID);
  }

  /** Returns true if field rateLimitExpiry is set (has been assigned a value) and false otherwise */
  public boolean isSetRateLimitExpiry() {
    return EncodingUtils.testBit(__isset_bitfield, __RATELIMITEXPIRY_ISSET_ID);
  }

  public void setRateLimitExpiryIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RATELIMITEXPIRY_ISSET_ID, value);
  }

  public int getIsShow() {
    return this.isShow;
  }

  public GiftInfo setIsShow(int isShow) {
    this.isShow = isShow;
    setIsShowIsSet(true);
    return this;
  }

  public void unsetIsShow() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ISSHOW_ISSET_ID);
  }

  /** Returns true if field isShow is set (has been assigned a value) and false otherwise */
  public boolean isSetIsShow() {
    return EncodingUtils.testBit(__isset_bitfield, __ISSHOW_ISSET_ID);
  }

  public void setIsShowIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ISSHOW_ISSET_ID, value);
  }

  public int getGuard() {
    return this.guard;
  }

  public GiftInfo setGuard(int guard) {
    this.guard = guard;
    setGuardIsSet(true);
    return this;
  }

  public void unsetGuard() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __GUARD_ISSET_ID);
  }

  /** Returns true if field guard is set (has been assigned a value) and false otherwise */
  public boolean isSetGuard() {
    return EncodingUtils.testBit(__isset_bitfield, __GUARD_ISSET_ID);
  }

  public void setGuardIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __GUARD_ISSET_ID, value);
  }

  public String getVideoUrl() {
    return this.videoUrl;
  }

  public GiftInfo setVideoUrl(String videoUrl) {
    this.videoUrl = videoUrl;
    return this;
  }

  public void unsetVideoUrl() {
    this.videoUrl = null;
  }

  /** Returns true if field videoUrl is set (has been assigned a value) and false otherwise */
  public boolean isSetVideoUrl() {
    return this.videoUrl != null;
  }

  public void setVideoUrlIsSet(boolean value) {
    if (!value) {
      this.videoUrl = null;
    }
  }

  public int getIsRollGift() {
    return this.isRollGift;
  }

  public GiftInfo setIsRollGift(int isRollGift) {
    this.isRollGift = isRollGift;
    setIsRollGiftIsSet(true);
    return this;
  }

  public void unsetIsRollGift() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ISROLLGIFT_ISSET_ID);
  }

  /** Returns true if field isRollGift is set (has been assigned a value) and false otherwise */
  public boolean isSetIsRollGift() {
    return EncodingUtils.testBit(__isset_bitfield, __ISROLLGIFT_ISSET_ID);
  }

  public void setIsRollGiftIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ISROLLGIFT_ISSET_ID, value);
  }

  public long getRollNum() {
    return this.rollNum;
  }

  public GiftInfo setRollNum(long rollNum) {
    this.rollNum = rollNum;
    setRollNumIsSet(true);
    return this;
  }

  public void unsetRollNum() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ROLLNUM_ISSET_ID);
  }

  /** Returns true if field rollNum is set (has been assigned a value) and false otherwise */
  public boolean isSetRollNum() {
    return EncodingUtils.testBit(__isset_bitfield, __ROLLNUM_ISSET_ID);
  }

  public void setRollNumIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ROLLNUM_ISSET_ID, value);
  }

  public String getMultiVideoUrls() {
    return this.multiVideoUrls;
  }

  public GiftInfo setMultiVideoUrls(String multiVideoUrls) {
    this.multiVideoUrls = multiVideoUrls;
    return this;
  }

  public void unsetMultiVideoUrls() {
    this.multiVideoUrls = null;
  }

  /** Returns true if field multiVideoUrls is set (has been assigned a value) and false otherwise */
  public boolean isSetMultiVideoUrls() {
    return this.multiVideoUrls != null;
  }

  public void setMultiVideoUrlsIsSet(boolean value) {
    if (!value) {
      this.multiVideoUrls = null;
    }
  }

  public String getGiftTips() {
    return this.giftTips;
  }

  public GiftInfo setGiftTips(String giftTips) {
    this.giftTips = giftTips;
    return this;
  }

  public void unsetGiftTips() {
    this.giftTips = null;
  }

  /** Returns true if field giftTips is set (has been assigned a value) and false otherwise */
  public boolean isSetGiftTips() {
    return this.giftTips != null;
  }

  public void setGiftTipsIsSet(boolean value) {
    if (!value) {
      this.giftTips = null;
    }
  }

  /**
   * 星钻等级
   */
  public int getStarVipLevel() {
    return this.starVipLevel;
  }

  /**
   * 星钻等级
   */
  public GiftInfo setStarVipLevel(int starVipLevel) {
    this.starVipLevel = starVipLevel;
    setStarVipLevelIsSet(true);
    return this;
  }

  public void unsetStarVipLevel() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STARVIPLEVEL_ISSET_ID);
  }

  /** Returns true if field starVipLevel is set (has been assigned a value) and false otherwise */
  public boolean isSetStarVipLevel() {
    return EncodingUtils.testBit(__isset_bitfield, __STARVIPLEVEL_ISSET_ID);
  }

  public void setStarVipLevelIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STARVIPLEVEL_ISSET_ID, value);
  }

  /**
   * 竖屏礼物资源
   */
  public String getVerticalVideoUrl() {
    return this.verticalVideoUrl;
  }

  /**
   * 竖屏礼物资源
   */
  public GiftInfo setVerticalVideoUrl(String verticalVideoUrl) {
    this.verticalVideoUrl = verticalVideoUrl;
    return this;
  }

  public void unsetVerticalVideoUrl() {
    this.verticalVideoUrl = null;
  }

  /** Returns true if field verticalVideoUrl is set (has been assigned a value) and false otherwise */
  public boolean isSetVerticalVideoUrl() {
    return this.verticalVideoUrl != null;
  }

  public void setVerticalVideoUrlIsSet(boolean value) {
    if (!value) {
      this.verticalVideoUrl = null;
    }
  }

  /**
   * 额外礼物资源,以后其它新增的资源,能放这里就放这里,透传给前端,不用再发版
   */
  public String getExtResource() {
    return this.extResource;
  }

  /**
   * 额外礼物资源,以后其它新增的资源,能放这里就放这里,透传给前端,不用再发版
   */
  public GiftInfo setExtResource(String extResource) {
    this.extResource = extResource;
    return this;
  }

  public void unsetExtResource() {
    this.extResource = null;
  }

  /** Returns true if field extResource is set (has been assigned a value) and false otherwise */
  public boolean isSetExtResource() {
    return this.extResource != null;
  }

  public void setExtResourceIsSet(boolean value) {
    if (!value) {
      this.extResource = null;
    }
  }

  /**
   * 原价
   */
  public int getOrigPrice() {
    return this.origPrice;
  }

  /**
   * 原价
   */
  public GiftInfo setOrigPrice(int origPrice) {
    this.origPrice = origPrice;
    setOrigPriceIsSet(true);
    return this;
  }

  public void unsetOrigPrice() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ORIGPRICE_ISSET_ID);
  }

  /** Returns true if field origPrice is set (has been assigned a value) and false otherwise */
  public boolean isSetOrigPrice() {
    return EncodingUtils.testBit(__isset_bitfield, __ORIGPRICE_ISSET_ID);
  }

  public void setOrigPriceIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ORIGPRICE_ISSET_ID, value);
  }

  /**
   * 上新时间
   */
  public int getNewTime() {
    return this.newTime;
  }

  /**
   * 上新时间
   */
  public GiftInfo setNewTime(int newTime) {
    this.newTime = newTime;
    setNewTimeIsSet(true);
    return this;
  }

  public void unsetNewTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __NEWTIME_ISSET_ID);
  }

  /** Returns true if field newTime is set (has been assigned a value) and false otherwise */
  public boolean isSetNewTime() {
    return EncodingUtils.testBit(__isset_bitfield, __NEWTIME_ISSET_ID);
  }

  public void setNewTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __NEWTIME_ISSET_ID, value);
  }

  /**
   * 全站礼物(0:不是,1:是) *
   */
  public int getIsGlobal() {
    return this.isGlobal;
  }

  /**
   * 全站礼物(0:不是,1:是) *
   */
  public GiftInfo setIsGlobal(int isGlobal) {
    this.isGlobal = isGlobal;
    setIsGlobalIsSet(true);
    return this;
  }

  public void unsetIsGlobal() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ISGLOBAL_ISSET_ID);
  }

  /** Returns true if field isGlobal is set (has been assigned a value) and false otherwise */
  public boolean isSetIsGlobal() {
    return EncodingUtils.testBit(__isset_bitfield, __ISGLOBAL_ISSET_ID);
  }

  public void setIsGlobalIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ISGLOBAL_ISSET_ID, value);
  }

  /**
   * 发全站公告(0:不是,1:是) *
   */
  public int getIsGlobalNotice() {
    return this.isGlobalNotice;
  }

  /**
   * 发全站公告(0:不是,1:是) *
   */
  public GiftInfo setIsGlobalNotice(int isGlobalNotice) {
    this.isGlobalNotice = isGlobalNotice;
    setIsGlobalNoticeIsSet(true);
    return this;
  }

  public void unsetIsGlobalNotice() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ISGLOBALNOTICE_ISSET_ID);
  }

  /** Returns true if field isGlobalNotice is set (has been assigned a value) and false otherwise */
  public boolean isSetIsGlobalNotice() {
    return EncodingUtils.testBit(__isset_bitfield, __ISGLOBALNOTICE_ISSET_ID);
  }

  public void setIsGlobalNoticeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ISGLOBALNOTICE_ISSET_ID, value);
  }

  /**
   * 礼物扩展属性,以后但凡新增礼物属性，都放在这个扩展字段
   */
  public String getExtAttr() {
    return this.extAttr;
  }

  /**
   * 礼物扩展属性,以后但凡新增礼物属性，都放在这个扩展字段
   */
  public GiftInfo setExtAttr(String extAttr) {
    this.extAttr = extAttr;
    return this;
  }

  public void unsetExtAttr() {
    this.extAttr = null;
  }

  /** Returns true if field extAttr is set (has been assigned a value) and false otherwise */
  public boolean isSetExtAttr() {
    return this.extAttr != null;
  }

  public void setExtAttrIsSet(boolean value) {
    if (!value) {
      this.extAttr = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ID:
      if (value == null) {
        unsetId();
      } else {
        setId((Integer)value);
      }
      break;

    case NAME:
      if (value == null) {
        unsetName();
      } else {
        setName((String)value);
      }
      break;

    case URL:
      if (value == null) {
        unsetUrl();
      } else {
        setUrl((String)value);
      }
      break;

    case PIC:
      if (value == null) {
        unsetPic();
      } else {
        setPic((String)value);
      }
      break;

    case PRICE:
      if (value == null) {
        unsetPrice();
      } else {
        setPrice((Integer)value);
      }
      break;

    case EXCHANGE:
      if (value == null) {
        unsetExchange();
      } else {
        setExchange((Double)value);
      }
      break;

    case CATEGORY:
      if (value == null) {
        unsetCategory();
      } else {
        setCategory((Integer)value);
      }
      break;

    case MIX:
      if (value == null) {
        unsetMix();
      } else {
        setMix((Integer)value);
      }
      break;

    case FLY:
      if (value == null) {
        unsetFly();
      } else {
        setFly((Integer)value);
      }
      break;

    case CLASS_NAME:
      if (value == null) {
        unsetClassName();
      } else {
        setClassName((String)value);
      }
      break;

    case SORT_INDEX:
      if (value == null) {
        unsetSortIndex();
      } else {
        setSortIndex((Integer)value);
      }
      break;

    case STATUS:
      if (value == null) {
        unsetStatus();
      } else {
        setStatus((Integer)value);
      }
      break;

    case IMAGE:
      if (value == null) {
        unsetImage();
      } else {
        setImage((String)value);
      }
      break;

    case IS_NEW:
      if (value == null) {
        unsetIsNew();
      } else {
        setIsNew((Integer)value);
      }
      break;

    case IMAGE_TRANS:
      if (value == null) {
        unsetImageTrans();
      } else {
        setImageTrans((String)value);
      }
      break;

    case IMAGE_GRADE:
      if (value == null) {
        unsetImageGrade();
      } else {
        setImageGrade((String)value);
      }
      break;

    case EXPIRE:
      if (value == null) {
        unsetExpire();
      } else {
        setExpire((Integer)value);
      }
      break;

    case RICH_LEVEL_LIMIT:
      if (value == null) {
        unsetRichLevelLimit();
      } else {
        setRichLevelLimit((Integer)value);
      }
      break;

    case TYPE:
      if (value == null) {
        unsetType();
      } else {
        setType((Integer)value);
      }
      break;

    case ADT_EFFECT:
      if (value == null) {
        unsetAdtEffect();
      } else {
        setAdtEffect((String)value);
      }
      break;

    case GUARD_LEVEL_LIMIT:
      if (value == null) {
        unsetGuardLevelLimit();
      } else {
        setGuardLevelLimit((Integer)value);
      }
      break;

    case VIP_LIMIT:
      if (value == null) {
        unsetVipLimit();
      } else {
        setVipLimit((Integer)value);
      }
      break;

    case IS_PILE:
      if (value == null) {
        unsetIsPile();
      } else {
        setIsPile((Integer)value);
      }
      break;

    case CAN_NOT_SEND:
      if (value == null) {
        unsetCanNotSend();
      } else {
        setCanNotSend((Integer)value);
      }
      break;

    case EXTRA_TYPE:
      if (value == null) {
        unsetExtraType();
      } else {
        setExtraType((Integer)value);
      }
      break;

    case EXTRA:
      if (value == null) {
        unsetExtra();
      } else {
        setExtra((String)value);
      }
      break;

    case USER_ID_LIMIT:
      if (value == null) {
        unsetUserIdLimit();
      } else {
        setUserIdLimit((Long)value);
      }
      break;

    case USER_LIMIT_NICK_NAME:
      if (value == null) {
        unsetUserLimitNickName();
      } else {
        setUserLimitNickName((String)value);
      }
      break;

    case STAR_FANS_LIMIT:
      if (value == null) {
        unsetStarFansLimit();
      } else {
        setStarFansLimit((Integer)value);
      }
      break;

    case GIFT_URL:
      if (value == null) {
        unsetGiftUrl();
      } else {
        setGiftUrl((String)value);
      }
      break;

    case SPECIAL_TYPE:
      if (value == null) {
        unsetSpecialType();
      } else {
        setSpecialType((Integer)value);
      }
      break;

    case TOP_COUNT:
      if (value == null) {
        unsetTopCount();
      } else {
        setTopCount((Integer)value);
      }
      break;

    case WEEK:
      if (value == null) {
        unsetWeek();
      } else {
        setWeek((Integer)value);
      }
      break;

    case IS_SUPER:
      if (value == null) {
        unsetIsSuper();
      } else {
        setIsSuper((Integer)value);
      }
      break;

    case HAPPY_OBJ:
      if (value == null) {
        unsetHappyObj();
      } else {
        setHappyObj((Integer)value);
      }
      break;

    case HAPPY_TYPE:
      if (value == null) {
        unsetHappyType();
      } else {
        setHappyType((Integer)value);
      }
      break;

    case MOBILE_IMAGE:
      if (value == null) {
        unsetMobileImage();
      } else {
        setMobileImage((String)value);
      }
      break;

    case IS_PK:
      if (value == null) {
        unsetIsPk();
      } else {
        setIsPk((Integer)value);
      }
      break;

    case IS_FULL_SHOW:
      if (value == null) {
        unsetIsFullShow();
      } else {
        setIsFullShow((Integer)value);
      }
      break;

    case DURATION:
      if (value == null) {
        unsetDuration();
      } else {
        setDuration((Integer)value);
      }
      break;

    case L_GUARD_LEVEL_LIMIT:
      if (value == null) {
        unsetLGuardLevelLimit();
      } else {
        setLGuardLevelLimit((Integer)value);
      }
      break;

    case RATE_LIMIT_TYPE:
      if (value == null) {
        unsetRateLimitType();
      } else {
        setRateLimitType((Integer)value);
      }
      break;

    case RATE_LIMIT_NUM:
      if (value == null) {
        unsetRateLimitNum();
      } else {
        setRateLimitNum((Integer)value);
      }
      break;

    case RATE_LIMIT_EXPIRY:
      if (value == null) {
        unsetRateLimitExpiry();
      } else {
        setRateLimitExpiry((Integer)value);
      }
      break;

    case IS_SHOW:
      if (value == null) {
        unsetIsShow();
      } else {
        setIsShow((Integer)value);
      }
      break;

    case GUARD:
      if (value == null) {
        unsetGuard();
      } else {
        setGuard((Integer)value);
      }
      break;

    case VIDEO_URL:
      if (value == null) {
        unsetVideoUrl();
      } else {
        setVideoUrl((String)value);
      }
      break;

    case IS_ROLL_GIFT:
      if (value == null) {
        unsetIsRollGift();
      } else {
        setIsRollGift((Integer)value);
      }
      break;

    case ROLL_NUM:
      if (value == null) {
        unsetRollNum();
      } else {
        setRollNum((Long)value);
      }
      break;

    case MULTI_VIDEO_URLS:
      if (value == null) {
        unsetMultiVideoUrls();
      } else {
        setMultiVideoUrls((String)value);
      }
      break;

    case GIFT_TIPS:
      if (value == null) {
        unsetGiftTips();
      } else {
        setGiftTips((String)value);
      }
      break;

    case STAR_VIP_LEVEL:
      if (value == null) {
        unsetStarVipLevel();
      } else {
        setStarVipLevel((Integer)value);
      }
      break;

    case VERTICAL_VIDEO_URL:
      if (value == null) {
        unsetVerticalVideoUrl();
      } else {
        setVerticalVideoUrl((String)value);
      }
      break;

    case EXT_RESOURCE:
      if (value == null) {
        unsetExtResource();
      } else {
        setExtResource((String)value);
      }
      break;

    case ORIG_PRICE:
      if (value == null) {
        unsetOrigPrice();
      } else {
        setOrigPrice((Integer)value);
      }
      break;

    case NEW_TIME:
      if (value == null) {
        unsetNewTime();
      } else {
        setNewTime((Integer)value);
      }
      break;

    case IS_GLOBAL:
      if (value == null) {
        unsetIsGlobal();
      } else {
        setIsGlobal((Integer)value);
      }
      break;

    case IS_GLOBAL_NOTICE:
      if (value == null) {
        unsetIsGlobalNotice();
      } else {
        setIsGlobalNotice((Integer)value);
      }
      break;

    case EXT_ATTR:
      if (value == null) {
        unsetExtAttr();
      } else {
        setExtAttr((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ID:
      return getId();

    case NAME:
      return getName();

    case URL:
      return getUrl();

    case PIC:
      return getPic();

    case PRICE:
      return getPrice();

    case EXCHANGE:
      return getExchange();

    case CATEGORY:
      return getCategory();

    case MIX:
      return getMix();

    case FLY:
      return getFly();

    case CLASS_NAME:
      return getClassName();

    case SORT_INDEX:
      return getSortIndex();

    case STATUS:
      return getStatus();

    case IMAGE:
      return getImage();

    case IS_NEW:
      return getIsNew();

    case IMAGE_TRANS:
      return getImageTrans();

    case IMAGE_GRADE:
      return getImageGrade();

    case EXPIRE:
      return getExpire();

    case RICH_LEVEL_LIMIT:
      return getRichLevelLimit();

    case TYPE:
      return getType();

    case ADT_EFFECT:
      return getAdtEffect();

    case GUARD_LEVEL_LIMIT:
      return getGuardLevelLimit();

    case VIP_LIMIT:
      return getVipLimit();

    case IS_PILE:
      return getIsPile();

    case CAN_NOT_SEND:
      return getCanNotSend();

    case EXTRA_TYPE:
      return getExtraType();

    case EXTRA:
      return getExtra();

    case USER_ID_LIMIT:
      return getUserIdLimit();

    case USER_LIMIT_NICK_NAME:
      return getUserLimitNickName();

    case STAR_FANS_LIMIT:
      return getStarFansLimit();

    case GIFT_URL:
      return getGiftUrl();

    case SPECIAL_TYPE:
      return getSpecialType();

    case TOP_COUNT:
      return getTopCount();

    case WEEK:
      return getWeek();

    case IS_SUPER:
      return getIsSuper();

    case HAPPY_OBJ:
      return getHappyObj();

    case HAPPY_TYPE:
      return getHappyType();

    case MOBILE_IMAGE:
      return getMobileImage();

    case IS_PK:
      return getIsPk();

    case IS_FULL_SHOW:
      return getIsFullShow();

    case DURATION:
      return getDuration();

    case L_GUARD_LEVEL_LIMIT:
      return getLGuardLevelLimit();

    case RATE_LIMIT_TYPE:
      return getRateLimitType();

    case RATE_LIMIT_NUM:
      return getRateLimitNum();

    case RATE_LIMIT_EXPIRY:
      return getRateLimitExpiry();

    case IS_SHOW:
      return getIsShow();

    case GUARD:
      return getGuard();

    case VIDEO_URL:
      return getVideoUrl();

    case IS_ROLL_GIFT:
      return getIsRollGift();

    case ROLL_NUM:
      return getRollNum();

    case MULTI_VIDEO_URLS:
      return getMultiVideoUrls();

    case GIFT_TIPS:
      return getGiftTips();

    case STAR_VIP_LEVEL:
      return getStarVipLevel();

    case VERTICAL_VIDEO_URL:
      return getVerticalVideoUrl();

    case EXT_RESOURCE:
      return getExtResource();

    case ORIG_PRICE:
      return getOrigPrice();

    case NEW_TIME:
      return getNewTime();

    case IS_GLOBAL:
      return getIsGlobal();

    case IS_GLOBAL_NOTICE:
      return getIsGlobalNotice();

    case EXT_ATTR:
      return getExtAttr();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ID:
      return isSetId();
    case NAME:
      return isSetName();
    case URL:
      return isSetUrl();
    case PIC:
      return isSetPic();
    case PRICE:
      return isSetPrice();
    case EXCHANGE:
      return isSetExchange();
    case CATEGORY:
      return isSetCategory();
    case MIX:
      return isSetMix();
    case FLY:
      return isSetFly();
    case CLASS_NAME:
      return isSetClassName();
    case SORT_INDEX:
      return isSetSortIndex();
    case STATUS:
      return isSetStatus();
    case IMAGE:
      return isSetImage();
    case IS_NEW:
      return isSetIsNew();
    case IMAGE_TRANS:
      return isSetImageTrans();
    case IMAGE_GRADE:
      return isSetImageGrade();
    case EXPIRE:
      return isSetExpire();
    case RICH_LEVEL_LIMIT:
      return isSetRichLevelLimit();
    case TYPE:
      return isSetType();
    case ADT_EFFECT:
      return isSetAdtEffect();
    case GUARD_LEVEL_LIMIT:
      return isSetGuardLevelLimit();
    case VIP_LIMIT:
      return isSetVipLimit();
    case IS_PILE:
      return isSetIsPile();
    case CAN_NOT_SEND:
      return isSetCanNotSend();
    case EXTRA_TYPE:
      return isSetExtraType();
    case EXTRA:
      return isSetExtra();
    case USER_ID_LIMIT:
      return isSetUserIdLimit();
    case USER_LIMIT_NICK_NAME:
      return isSetUserLimitNickName();
    case STAR_FANS_LIMIT:
      return isSetStarFansLimit();
    case GIFT_URL:
      return isSetGiftUrl();
    case SPECIAL_TYPE:
      return isSetSpecialType();
    case TOP_COUNT:
      return isSetTopCount();
    case WEEK:
      return isSetWeek();
    case IS_SUPER:
      return isSetIsSuper();
    case HAPPY_OBJ:
      return isSetHappyObj();
    case HAPPY_TYPE:
      return isSetHappyType();
    case MOBILE_IMAGE:
      return isSetMobileImage();
    case IS_PK:
      return isSetIsPk();
    case IS_FULL_SHOW:
      return isSetIsFullShow();
    case DURATION:
      return isSetDuration();
    case L_GUARD_LEVEL_LIMIT:
      return isSetLGuardLevelLimit();
    case RATE_LIMIT_TYPE:
      return isSetRateLimitType();
    case RATE_LIMIT_NUM:
      return isSetRateLimitNum();
    case RATE_LIMIT_EXPIRY:
      return isSetRateLimitExpiry();
    case IS_SHOW:
      return isSetIsShow();
    case GUARD:
      return isSetGuard();
    case VIDEO_URL:
      return isSetVideoUrl();
    case IS_ROLL_GIFT:
      return isSetIsRollGift();
    case ROLL_NUM:
      return isSetRollNum();
    case MULTI_VIDEO_URLS:
      return isSetMultiVideoUrls();
    case GIFT_TIPS:
      return isSetGiftTips();
    case STAR_VIP_LEVEL:
      return isSetStarVipLevel();
    case VERTICAL_VIDEO_URL:
      return isSetVerticalVideoUrl();
    case EXT_RESOURCE:
      return isSetExtResource();
    case ORIG_PRICE:
      return isSetOrigPrice();
    case NEW_TIME:
      return isSetNewTime();
    case IS_GLOBAL:
      return isSetIsGlobal();
    case IS_GLOBAL_NOTICE:
      return isSetIsGlobalNotice();
    case EXT_ATTR:
      return isSetExtAttr();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof GiftInfo)
      return this.equals((GiftInfo)that);
    return false;
  }

  public boolean equals(GiftInfo that) {
    if (that == null)
      return false;

    boolean this_present_id = true && this.isSetId();
    boolean that_present_id = true && that.isSetId();
    if (this_present_id || that_present_id) {
      if (!(this_present_id && that_present_id))
        return false;
      if (this.id != that.id)
        return false;
    }

    boolean this_present_name = true && this.isSetName();
    boolean that_present_name = true && that.isSetName();
    if (this_present_name || that_present_name) {
      if (!(this_present_name && that_present_name))
        return false;
      if (!this.name.equals(that.name))
        return false;
    }

    boolean this_present_url = true && this.isSetUrl();
    boolean that_present_url = true && that.isSetUrl();
    if (this_present_url || that_present_url) {
      if (!(this_present_url && that_present_url))
        return false;
      if (!this.url.equals(that.url))
        return false;
    }

    boolean this_present_pic = true && this.isSetPic();
    boolean that_present_pic = true && that.isSetPic();
    if (this_present_pic || that_present_pic) {
      if (!(this_present_pic && that_present_pic))
        return false;
      if (!this.pic.equals(that.pic))
        return false;
    }

    boolean this_present_price = true && this.isSetPrice();
    boolean that_present_price = true && that.isSetPrice();
    if (this_present_price || that_present_price) {
      if (!(this_present_price && that_present_price))
        return false;
      if (this.price != that.price)
        return false;
    }

    boolean this_present_exchange = true && this.isSetExchange();
    boolean that_present_exchange = true && that.isSetExchange();
    if (this_present_exchange || that_present_exchange) {
      if (!(this_present_exchange && that_present_exchange))
        return false;
      if (this.exchange != that.exchange)
        return false;
    }

    boolean this_present_category = true && this.isSetCategory();
    boolean that_present_category = true && that.isSetCategory();
    if (this_present_category || that_present_category) {
      if (!(this_present_category && that_present_category))
        return false;
      if (this.category != that.category)
        return false;
    }

    boolean this_present_mix = true && this.isSetMix();
    boolean that_present_mix = true && that.isSetMix();
    if (this_present_mix || that_present_mix) {
      if (!(this_present_mix && that_present_mix))
        return false;
      if (this.mix != that.mix)
        return false;
    }

    boolean this_present_fly = true && this.isSetFly();
    boolean that_present_fly = true && that.isSetFly();
    if (this_present_fly || that_present_fly) {
      if (!(this_present_fly && that_present_fly))
        return false;
      if (this.fly != that.fly)
        return false;
    }

    boolean this_present_className = true && this.isSetClassName();
    boolean that_present_className = true && that.isSetClassName();
    if (this_present_className || that_present_className) {
      if (!(this_present_className && that_present_className))
        return false;
      if (!this.className.equals(that.className))
        return false;
    }

    boolean this_present_sortIndex = true && this.isSetSortIndex();
    boolean that_present_sortIndex = true && that.isSetSortIndex();
    if (this_present_sortIndex || that_present_sortIndex) {
      if (!(this_present_sortIndex && that_present_sortIndex))
        return false;
      if (this.sortIndex != that.sortIndex)
        return false;
    }

    boolean this_present_status = true && this.isSetStatus();
    boolean that_present_status = true && that.isSetStatus();
    if (this_present_status || that_present_status) {
      if (!(this_present_status && that_present_status))
        return false;
      if (this.status != that.status)
        return false;
    }

    boolean this_present_image = true && this.isSetImage();
    boolean that_present_image = true && that.isSetImage();
    if (this_present_image || that_present_image) {
      if (!(this_present_image && that_present_image))
        return false;
      if (!this.image.equals(that.image))
        return false;
    }

    boolean this_present_isNew = true && this.isSetIsNew();
    boolean that_present_isNew = true && that.isSetIsNew();
    if (this_present_isNew || that_present_isNew) {
      if (!(this_present_isNew && that_present_isNew))
        return false;
      if (this.isNew != that.isNew)
        return false;
    }

    boolean this_present_imageTrans = true && this.isSetImageTrans();
    boolean that_present_imageTrans = true && that.isSetImageTrans();
    if (this_present_imageTrans || that_present_imageTrans) {
      if (!(this_present_imageTrans && that_present_imageTrans))
        return false;
      if (!this.imageTrans.equals(that.imageTrans))
        return false;
    }

    boolean this_present_imageGrade = true && this.isSetImageGrade();
    boolean that_present_imageGrade = true && that.isSetImageGrade();
    if (this_present_imageGrade || that_present_imageGrade) {
      if (!(this_present_imageGrade && that_present_imageGrade))
        return false;
      if (!this.imageGrade.equals(that.imageGrade))
        return false;
    }

    boolean this_present_expire = true && this.isSetExpire();
    boolean that_present_expire = true && that.isSetExpire();
    if (this_present_expire || that_present_expire) {
      if (!(this_present_expire && that_present_expire))
        return false;
      if (this.expire != that.expire)
        return false;
    }

    boolean this_present_richLevelLimit = true && this.isSetRichLevelLimit();
    boolean that_present_richLevelLimit = true && that.isSetRichLevelLimit();
    if (this_present_richLevelLimit || that_present_richLevelLimit) {
      if (!(this_present_richLevelLimit && that_present_richLevelLimit))
        return false;
      if (this.richLevelLimit != that.richLevelLimit)
        return false;
    }

    boolean this_present_type = true && this.isSetType();
    boolean that_present_type = true && that.isSetType();
    if (this_present_type || that_present_type) {
      if (!(this_present_type && that_present_type))
        return false;
      if (this.type != that.type)
        return false;
    }

    boolean this_present_adtEffect = true && this.isSetAdtEffect();
    boolean that_present_adtEffect = true && that.isSetAdtEffect();
    if (this_present_adtEffect || that_present_adtEffect) {
      if (!(this_present_adtEffect && that_present_adtEffect))
        return false;
      if (!this.adtEffect.equals(that.adtEffect))
        return false;
    }

    boolean this_present_guardLevelLimit = true && this.isSetGuardLevelLimit();
    boolean that_present_guardLevelLimit = true && that.isSetGuardLevelLimit();
    if (this_present_guardLevelLimit || that_present_guardLevelLimit) {
      if (!(this_present_guardLevelLimit && that_present_guardLevelLimit))
        return false;
      if (this.guardLevelLimit != that.guardLevelLimit)
        return false;
    }

    boolean this_present_vipLimit = true && this.isSetVipLimit();
    boolean that_present_vipLimit = true && that.isSetVipLimit();
    if (this_present_vipLimit || that_present_vipLimit) {
      if (!(this_present_vipLimit && that_present_vipLimit))
        return false;
      if (this.vipLimit != that.vipLimit)
        return false;
    }

    boolean this_present_isPile = true && this.isSetIsPile();
    boolean that_present_isPile = true && that.isSetIsPile();
    if (this_present_isPile || that_present_isPile) {
      if (!(this_present_isPile && that_present_isPile))
        return false;
      if (this.isPile != that.isPile)
        return false;
    }

    boolean this_present_canNotSend = true && this.isSetCanNotSend();
    boolean that_present_canNotSend = true && that.isSetCanNotSend();
    if (this_present_canNotSend || that_present_canNotSend) {
      if (!(this_present_canNotSend && that_present_canNotSend))
        return false;
      if (this.canNotSend != that.canNotSend)
        return false;
    }

    boolean this_present_extraType = true && this.isSetExtraType();
    boolean that_present_extraType = true && that.isSetExtraType();
    if (this_present_extraType || that_present_extraType) {
      if (!(this_present_extraType && that_present_extraType))
        return false;
      if (this.extraType != that.extraType)
        return false;
    }

    boolean this_present_extra = true && this.isSetExtra();
    boolean that_present_extra = true && that.isSetExtra();
    if (this_present_extra || that_present_extra) {
      if (!(this_present_extra && that_present_extra))
        return false;
      if (!this.extra.equals(that.extra))
        return false;
    }

    boolean this_present_userIdLimit = true && this.isSetUserIdLimit();
    boolean that_present_userIdLimit = true && that.isSetUserIdLimit();
    if (this_present_userIdLimit || that_present_userIdLimit) {
      if (!(this_present_userIdLimit && that_present_userIdLimit))
        return false;
      if (this.userIdLimit != that.userIdLimit)
        return false;
    }

    boolean this_present_userLimitNickName = true && this.isSetUserLimitNickName();
    boolean that_present_userLimitNickName = true && that.isSetUserLimitNickName();
    if (this_present_userLimitNickName || that_present_userLimitNickName) {
      if (!(this_present_userLimitNickName && that_present_userLimitNickName))
        return false;
      if (!this.userLimitNickName.equals(that.userLimitNickName))
        return false;
    }

    boolean this_present_starFansLimit = true && this.isSetStarFansLimit();
    boolean that_present_starFansLimit = true && that.isSetStarFansLimit();
    if (this_present_starFansLimit || that_present_starFansLimit) {
      if (!(this_present_starFansLimit && that_present_starFansLimit))
        return false;
      if (this.starFansLimit != that.starFansLimit)
        return false;
    }

    boolean this_present_giftUrl = true && this.isSetGiftUrl();
    boolean that_present_giftUrl = true && that.isSetGiftUrl();
    if (this_present_giftUrl || that_present_giftUrl) {
      if (!(this_present_giftUrl && that_present_giftUrl))
        return false;
      if (!this.giftUrl.equals(that.giftUrl))
        return false;
    }

    boolean this_present_specialType = true && this.isSetSpecialType();
    boolean that_present_specialType = true && that.isSetSpecialType();
    if (this_present_specialType || that_present_specialType) {
      if (!(this_present_specialType && that_present_specialType))
        return false;
      if (this.specialType != that.specialType)
        return false;
    }

    boolean this_present_topCount = true && this.isSetTopCount();
    boolean that_present_topCount = true && that.isSetTopCount();
    if (this_present_topCount || that_present_topCount) {
      if (!(this_present_topCount && that_present_topCount))
        return false;
      if (this.topCount != that.topCount)
        return false;
    }

    boolean this_present_week = true && this.isSetWeek();
    boolean that_present_week = true && that.isSetWeek();
    if (this_present_week || that_present_week) {
      if (!(this_present_week && that_present_week))
        return false;
      if (this.week != that.week)
        return false;
    }

    boolean this_present_isSuper = true && this.isSetIsSuper();
    boolean that_present_isSuper = true && that.isSetIsSuper();
    if (this_present_isSuper || that_present_isSuper) {
      if (!(this_present_isSuper && that_present_isSuper))
        return false;
      if (this.isSuper != that.isSuper)
        return false;
    }

    boolean this_present_happyObj = true && this.isSetHappyObj();
    boolean that_present_happyObj = true && that.isSetHappyObj();
    if (this_present_happyObj || that_present_happyObj) {
      if (!(this_present_happyObj && that_present_happyObj))
        return false;
      if (this.happyObj != that.happyObj)
        return false;
    }

    boolean this_present_happyType = true && this.isSetHappyType();
    boolean that_present_happyType = true && that.isSetHappyType();
    if (this_present_happyType || that_present_happyType) {
      if (!(this_present_happyType && that_present_happyType))
        return false;
      if (this.happyType != that.happyType)
        return false;
    }

    boolean this_present_mobileImage = true && this.isSetMobileImage();
    boolean that_present_mobileImage = true && that.isSetMobileImage();
    if (this_present_mobileImage || that_present_mobileImage) {
      if (!(this_present_mobileImage && that_present_mobileImage))
        return false;
      if (!this.mobileImage.equals(that.mobileImage))
        return false;
    }

    boolean this_present_isPk = true && this.isSetIsPk();
    boolean that_present_isPk = true && that.isSetIsPk();
    if (this_present_isPk || that_present_isPk) {
      if (!(this_present_isPk && that_present_isPk))
        return false;
      if (this.isPk != that.isPk)
        return false;
    }

    boolean this_present_isFullShow = true && this.isSetIsFullShow();
    boolean that_present_isFullShow = true && that.isSetIsFullShow();
    if (this_present_isFullShow || that_present_isFullShow) {
      if (!(this_present_isFullShow && that_present_isFullShow))
        return false;
      if (this.isFullShow != that.isFullShow)
        return false;
    }

    boolean this_present_duration = true && this.isSetDuration();
    boolean that_present_duration = true && that.isSetDuration();
    if (this_present_duration || that_present_duration) {
      if (!(this_present_duration && that_present_duration))
        return false;
      if (this.duration != that.duration)
        return false;
    }

    boolean this_present_lGuardLevelLimit = true && this.isSetLGuardLevelLimit();
    boolean that_present_lGuardLevelLimit = true && that.isSetLGuardLevelLimit();
    if (this_present_lGuardLevelLimit || that_present_lGuardLevelLimit) {
      if (!(this_present_lGuardLevelLimit && that_present_lGuardLevelLimit))
        return false;
      if (this.lGuardLevelLimit != that.lGuardLevelLimit)
        return false;
    }

    boolean this_present_rateLimitType = true && this.isSetRateLimitType();
    boolean that_present_rateLimitType = true && that.isSetRateLimitType();
    if (this_present_rateLimitType || that_present_rateLimitType) {
      if (!(this_present_rateLimitType && that_present_rateLimitType))
        return false;
      if (this.rateLimitType != that.rateLimitType)
        return false;
    }

    boolean this_present_rateLimitNum = true && this.isSetRateLimitNum();
    boolean that_present_rateLimitNum = true && that.isSetRateLimitNum();
    if (this_present_rateLimitNum || that_present_rateLimitNum) {
      if (!(this_present_rateLimitNum && that_present_rateLimitNum))
        return false;
      if (this.rateLimitNum != that.rateLimitNum)
        return false;
    }

    boolean this_present_rateLimitExpiry = true && this.isSetRateLimitExpiry();
    boolean that_present_rateLimitExpiry = true && that.isSetRateLimitExpiry();
    if (this_present_rateLimitExpiry || that_present_rateLimitExpiry) {
      if (!(this_present_rateLimitExpiry && that_present_rateLimitExpiry))
        return false;
      if (this.rateLimitExpiry != that.rateLimitExpiry)
        return false;
    }

    boolean this_present_isShow = true && this.isSetIsShow();
    boolean that_present_isShow = true && that.isSetIsShow();
    if (this_present_isShow || that_present_isShow) {
      if (!(this_present_isShow && that_present_isShow))
        return false;
      if (this.isShow != that.isShow)
        return false;
    }

    boolean this_present_guard = true && this.isSetGuard();
    boolean that_present_guard = true && that.isSetGuard();
    if (this_present_guard || that_present_guard) {
      if (!(this_present_guard && that_present_guard))
        return false;
      if (this.guard != that.guard)
        return false;
    }

    boolean this_present_videoUrl = true && this.isSetVideoUrl();
    boolean that_present_videoUrl = true && that.isSetVideoUrl();
    if (this_present_videoUrl || that_present_videoUrl) {
      if (!(this_present_videoUrl && that_present_videoUrl))
        return false;
      if (!this.videoUrl.equals(that.videoUrl))
        return false;
    }

    boolean this_present_isRollGift = true && this.isSetIsRollGift();
    boolean that_present_isRollGift = true && that.isSetIsRollGift();
    if (this_present_isRollGift || that_present_isRollGift) {
      if (!(this_present_isRollGift && that_present_isRollGift))
        return false;
      if (this.isRollGift != that.isRollGift)
        return false;
    }

    boolean this_present_rollNum = true && this.isSetRollNum();
    boolean that_present_rollNum = true && that.isSetRollNum();
    if (this_present_rollNum || that_present_rollNum) {
      if (!(this_present_rollNum && that_present_rollNum))
        return false;
      if (this.rollNum != that.rollNum)
        return false;
    }

    boolean this_present_multiVideoUrls = true && this.isSetMultiVideoUrls();
    boolean that_present_multiVideoUrls = true && that.isSetMultiVideoUrls();
    if (this_present_multiVideoUrls || that_present_multiVideoUrls) {
      if (!(this_present_multiVideoUrls && that_present_multiVideoUrls))
        return false;
      if (!this.multiVideoUrls.equals(that.multiVideoUrls))
        return false;
    }

    boolean this_present_giftTips = true && this.isSetGiftTips();
    boolean that_present_giftTips = true && that.isSetGiftTips();
    if (this_present_giftTips || that_present_giftTips) {
      if (!(this_present_giftTips && that_present_giftTips))
        return false;
      if (!this.giftTips.equals(that.giftTips))
        return false;
    }

    boolean this_present_starVipLevel = true && this.isSetStarVipLevel();
    boolean that_present_starVipLevel = true && that.isSetStarVipLevel();
    if (this_present_starVipLevel || that_present_starVipLevel) {
      if (!(this_present_starVipLevel && that_present_starVipLevel))
        return false;
      if (this.starVipLevel != that.starVipLevel)
        return false;
    }

    boolean this_present_verticalVideoUrl = true && this.isSetVerticalVideoUrl();
    boolean that_present_verticalVideoUrl = true && that.isSetVerticalVideoUrl();
    if (this_present_verticalVideoUrl || that_present_verticalVideoUrl) {
      if (!(this_present_verticalVideoUrl && that_present_verticalVideoUrl))
        return false;
      if (!this.verticalVideoUrl.equals(that.verticalVideoUrl))
        return false;
    }

    boolean this_present_extResource = true && this.isSetExtResource();
    boolean that_present_extResource = true && that.isSetExtResource();
    if (this_present_extResource || that_present_extResource) {
      if (!(this_present_extResource && that_present_extResource))
        return false;
      if (!this.extResource.equals(that.extResource))
        return false;
    }

    boolean this_present_origPrice = true && this.isSetOrigPrice();
    boolean that_present_origPrice = true && that.isSetOrigPrice();
    if (this_present_origPrice || that_present_origPrice) {
      if (!(this_present_origPrice && that_present_origPrice))
        return false;
      if (this.origPrice != that.origPrice)
        return false;
    }

    boolean this_present_newTime = true && this.isSetNewTime();
    boolean that_present_newTime = true && that.isSetNewTime();
    if (this_present_newTime || that_present_newTime) {
      if (!(this_present_newTime && that_present_newTime))
        return false;
      if (this.newTime != that.newTime)
        return false;
    }

    boolean this_present_isGlobal = true && this.isSetIsGlobal();
    boolean that_present_isGlobal = true && that.isSetIsGlobal();
    if (this_present_isGlobal || that_present_isGlobal) {
      if (!(this_present_isGlobal && that_present_isGlobal))
        return false;
      if (this.isGlobal != that.isGlobal)
        return false;
    }

    boolean this_present_isGlobalNotice = true && this.isSetIsGlobalNotice();
    boolean that_present_isGlobalNotice = true && that.isSetIsGlobalNotice();
    if (this_present_isGlobalNotice || that_present_isGlobalNotice) {
      if (!(this_present_isGlobalNotice && that_present_isGlobalNotice))
        return false;
      if (this.isGlobalNotice != that.isGlobalNotice)
        return false;
    }

    boolean this_present_extAttr = true && this.isSetExtAttr();
    boolean that_present_extAttr = true && that.isSetExtAttr();
    if (this_present_extAttr || that_present_extAttr) {
      if (!(this_present_extAttr && that_present_extAttr))
        return false;
      if (!this.extAttr.equals(that.extAttr))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_id = true && (isSetId());
    list.add(present_id);
    if (present_id)
      list.add(id);

    boolean present_name = true && (isSetName());
    list.add(present_name);
    if (present_name)
      list.add(name);

    boolean present_url = true && (isSetUrl());
    list.add(present_url);
    if (present_url)
      list.add(url);

    boolean present_pic = true && (isSetPic());
    list.add(present_pic);
    if (present_pic)
      list.add(pic);

    boolean present_price = true && (isSetPrice());
    list.add(present_price);
    if (present_price)
      list.add(price);

    boolean present_exchange = true && (isSetExchange());
    list.add(present_exchange);
    if (present_exchange)
      list.add(exchange);

    boolean present_category = true && (isSetCategory());
    list.add(present_category);
    if (present_category)
      list.add(category);

    boolean present_mix = true && (isSetMix());
    list.add(present_mix);
    if (present_mix)
      list.add(mix);

    boolean present_fly = true && (isSetFly());
    list.add(present_fly);
    if (present_fly)
      list.add(fly);

    boolean present_className = true && (isSetClassName());
    list.add(present_className);
    if (present_className)
      list.add(className);

    boolean present_sortIndex = true && (isSetSortIndex());
    list.add(present_sortIndex);
    if (present_sortIndex)
      list.add(sortIndex);

    boolean present_status = true && (isSetStatus());
    list.add(present_status);
    if (present_status)
      list.add(status);

    boolean present_image = true && (isSetImage());
    list.add(present_image);
    if (present_image)
      list.add(image);

    boolean present_isNew = true && (isSetIsNew());
    list.add(present_isNew);
    if (present_isNew)
      list.add(isNew);

    boolean present_imageTrans = true && (isSetImageTrans());
    list.add(present_imageTrans);
    if (present_imageTrans)
      list.add(imageTrans);

    boolean present_imageGrade = true && (isSetImageGrade());
    list.add(present_imageGrade);
    if (present_imageGrade)
      list.add(imageGrade);

    boolean present_expire = true && (isSetExpire());
    list.add(present_expire);
    if (present_expire)
      list.add(expire);

    boolean present_richLevelLimit = true && (isSetRichLevelLimit());
    list.add(present_richLevelLimit);
    if (present_richLevelLimit)
      list.add(richLevelLimit);

    boolean present_type = true && (isSetType());
    list.add(present_type);
    if (present_type)
      list.add(type);

    boolean present_adtEffect = true && (isSetAdtEffect());
    list.add(present_adtEffect);
    if (present_adtEffect)
      list.add(adtEffect);

    boolean present_guardLevelLimit = true && (isSetGuardLevelLimit());
    list.add(present_guardLevelLimit);
    if (present_guardLevelLimit)
      list.add(guardLevelLimit);

    boolean present_vipLimit = true && (isSetVipLimit());
    list.add(present_vipLimit);
    if (present_vipLimit)
      list.add(vipLimit);

    boolean present_isPile = true && (isSetIsPile());
    list.add(present_isPile);
    if (present_isPile)
      list.add(isPile);

    boolean present_canNotSend = true && (isSetCanNotSend());
    list.add(present_canNotSend);
    if (present_canNotSend)
      list.add(canNotSend);

    boolean present_extraType = true && (isSetExtraType());
    list.add(present_extraType);
    if (present_extraType)
      list.add(extraType);

    boolean present_extra = true && (isSetExtra());
    list.add(present_extra);
    if (present_extra)
      list.add(extra);

    boolean present_userIdLimit = true && (isSetUserIdLimit());
    list.add(present_userIdLimit);
    if (present_userIdLimit)
      list.add(userIdLimit);

    boolean present_userLimitNickName = true && (isSetUserLimitNickName());
    list.add(present_userLimitNickName);
    if (present_userLimitNickName)
      list.add(userLimitNickName);

    boolean present_starFansLimit = true && (isSetStarFansLimit());
    list.add(present_starFansLimit);
    if (present_starFansLimit)
      list.add(starFansLimit);

    boolean present_giftUrl = true && (isSetGiftUrl());
    list.add(present_giftUrl);
    if (present_giftUrl)
      list.add(giftUrl);

    boolean present_specialType = true && (isSetSpecialType());
    list.add(present_specialType);
    if (present_specialType)
      list.add(specialType);

    boolean present_topCount = true && (isSetTopCount());
    list.add(present_topCount);
    if (present_topCount)
      list.add(topCount);

    boolean present_week = true && (isSetWeek());
    list.add(present_week);
    if (present_week)
      list.add(week);

    boolean present_isSuper = true && (isSetIsSuper());
    list.add(present_isSuper);
    if (present_isSuper)
      list.add(isSuper);

    boolean present_happyObj = true && (isSetHappyObj());
    list.add(present_happyObj);
    if (present_happyObj)
      list.add(happyObj);

    boolean present_happyType = true && (isSetHappyType());
    list.add(present_happyType);
    if (present_happyType)
      list.add(happyType);

    boolean present_mobileImage = true && (isSetMobileImage());
    list.add(present_mobileImage);
    if (present_mobileImage)
      list.add(mobileImage);

    boolean present_isPk = true && (isSetIsPk());
    list.add(present_isPk);
    if (present_isPk)
      list.add(isPk);

    boolean present_isFullShow = true && (isSetIsFullShow());
    list.add(present_isFullShow);
    if (present_isFullShow)
      list.add(isFullShow);

    boolean present_duration = true && (isSetDuration());
    list.add(present_duration);
    if (present_duration)
      list.add(duration);

    boolean present_lGuardLevelLimit = true && (isSetLGuardLevelLimit());
    list.add(present_lGuardLevelLimit);
    if (present_lGuardLevelLimit)
      list.add(lGuardLevelLimit);

    boolean present_rateLimitType = true && (isSetRateLimitType());
    list.add(present_rateLimitType);
    if (present_rateLimitType)
      list.add(rateLimitType);

    boolean present_rateLimitNum = true && (isSetRateLimitNum());
    list.add(present_rateLimitNum);
    if (present_rateLimitNum)
      list.add(rateLimitNum);

    boolean present_rateLimitExpiry = true && (isSetRateLimitExpiry());
    list.add(present_rateLimitExpiry);
    if (present_rateLimitExpiry)
      list.add(rateLimitExpiry);

    boolean present_isShow = true && (isSetIsShow());
    list.add(present_isShow);
    if (present_isShow)
      list.add(isShow);

    boolean present_guard = true && (isSetGuard());
    list.add(present_guard);
    if (present_guard)
      list.add(guard);

    boolean present_videoUrl = true && (isSetVideoUrl());
    list.add(present_videoUrl);
    if (present_videoUrl)
      list.add(videoUrl);

    boolean present_isRollGift = true && (isSetIsRollGift());
    list.add(present_isRollGift);
    if (present_isRollGift)
      list.add(isRollGift);

    boolean present_rollNum = true && (isSetRollNum());
    list.add(present_rollNum);
    if (present_rollNum)
      list.add(rollNum);

    boolean present_multiVideoUrls = true && (isSetMultiVideoUrls());
    list.add(present_multiVideoUrls);
    if (present_multiVideoUrls)
      list.add(multiVideoUrls);

    boolean present_giftTips = true && (isSetGiftTips());
    list.add(present_giftTips);
    if (present_giftTips)
      list.add(giftTips);

    boolean present_starVipLevel = true && (isSetStarVipLevel());
    list.add(present_starVipLevel);
    if (present_starVipLevel)
      list.add(starVipLevel);

    boolean present_verticalVideoUrl = true && (isSetVerticalVideoUrl());
    list.add(present_verticalVideoUrl);
    if (present_verticalVideoUrl)
      list.add(verticalVideoUrl);

    boolean present_extResource = true && (isSetExtResource());
    list.add(present_extResource);
    if (present_extResource)
      list.add(extResource);

    boolean present_origPrice = true && (isSetOrigPrice());
    list.add(present_origPrice);
    if (present_origPrice)
      list.add(origPrice);

    boolean present_newTime = true && (isSetNewTime());
    list.add(present_newTime);
    if (present_newTime)
      list.add(newTime);

    boolean present_isGlobal = true && (isSetIsGlobal());
    list.add(present_isGlobal);
    if (present_isGlobal)
      list.add(isGlobal);

    boolean present_isGlobalNotice = true && (isSetIsGlobalNotice());
    list.add(present_isGlobalNotice);
    if (present_isGlobalNotice)
      list.add(isGlobalNotice);

    boolean present_extAttr = true && (isSetExtAttr());
    list.add(present_extAttr);
    if (present_extAttr)
      list.add(extAttr);

    return list.hashCode();
  }

  @Override
  public int compareTo(GiftInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetId()).compareTo(other.isSetId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.id, other.id);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetName()).compareTo(other.isSetName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.name, other.name);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUrl()).compareTo(other.isSetUrl());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUrl()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.url, other.url);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPic()).compareTo(other.isSetPic());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPic()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pic, other.pic);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPrice()).compareTo(other.isSetPrice());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPrice()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.price, other.price);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExchange()).compareTo(other.isSetExchange());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExchange()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.exchange, other.exchange);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCategory()).compareTo(other.isSetCategory());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCategory()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.category, other.category);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMix()).compareTo(other.isSetMix());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMix()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.mix, other.mix);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetFly()).compareTo(other.isSetFly());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFly()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.fly, other.fly);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetClassName()).compareTo(other.isSetClassName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetClassName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.className, other.className);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSortIndex()).compareTo(other.isSetSortIndex());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSortIndex()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sortIndex, other.sortIndex);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetStatus()).compareTo(other.isSetStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.status, other.status);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetImage()).compareTo(other.isSetImage());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetImage()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.image, other.image);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIsNew()).compareTo(other.isSetIsNew());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsNew()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isNew, other.isNew);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetImageTrans()).compareTo(other.isSetImageTrans());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetImageTrans()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.imageTrans, other.imageTrans);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetImageGrade()).compareTo(other.isSetImageGrade());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetImageGrade()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.imageGrade, other.imageGrade);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExpire()).compareTo(other.isSetExpire());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExpire()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.expire, other.expire);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRichLevelLimit()).compareTo(other.isSetRichLevelLimit());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRichLevelLimit()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.richLevelLimit, other.richLevelLimit);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetType()).compareTo(other.isSetType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.type, other.type);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAdtEffect()).compareTo(other.isSetAdtEffect());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAdtEffect()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.adtEffect, other.adtEffect);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGuardLevelLimit()).compareTo(other.isSetGuardLevelLimit());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGuardLevelLimit()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.guardLevelLimit, other.guardLevelLimit);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetVipLimit()).compareTo(other.isSetVipLimit());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetVipLimit()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.vipLimit, other.vipLimit);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIsPile()).compareTo(other.isSetIsPile());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsPile()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isPile, other.isPile);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCanNotSend()).compareTo(other.isSetCanNotSend());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCanNotSend()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.canNotSend, other.canNotSend);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtraType()).compareTo(other.isSetExtraType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtraType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extraType, other.extraType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtra()).compareTo(other.isSetExtra());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtra()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extra, other.extra);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUserIdLimit()).compareTo(other.isSetUserIdLimit());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUserIdLimit()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.userIdLimit, other.userIdLimit);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUserLimitNickName()).compareTo(other.isSetUserLimitNickName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUserLimitNickName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.userLimitNickName, other.userLimitNickName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetStarFansLimit()).compareTo(other.isSetStarFansLimit());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStarFansLimit()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.starFansLimit, other.starFansLimit);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGiftUrl()).compareTo(other.isSetGiftUrl());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGiftUrl()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.giftUrl, other.giftUrl);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSpecialType()).compareTo(other.isSetSpecialType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSpecialType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.specialType, other.specialType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTopCount()).compareTo(other.isSetTopCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTopCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.topCount, other.topCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetWeek()).compareTo(other.isSetWeek());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetWeek()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.week, other.week);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIsSuper()).compareTo(other.isSetIsSuper());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsSuper()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isSuper, other.isSuper);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetHappyObj()).compareTo(other.isSetHappyObj());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHappyObj()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.happyObj, other.happyObj);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetHappyType()).compareTo(other.isSetHappyType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHappyType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.happyType, other.happyType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMobileImage()).compareTo(other.isSetMobileImage());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMobileImage()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.mobileImage, other.mobileImage);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIsPk()).compareTo(other.isSetIsPk());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsPk()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isPk, other.isPk);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIsFullShow()).compareTo(other.isSetIsFullShow());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsFullShow()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isFullShow, other.isFullShow);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDuration()).compareTo(other.isSetDuration());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDuration()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.duration, other.duration);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetLGuardLevelLimit()).compareTo(other.isSetLGuardLevelLimit());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLGuardLevelLimit()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.lGuardLevelLimit, other.lGuardLevelLimit);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRateLimitType()).compareTo(other.isSetRateLimitType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRateLimitType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rateLimitType, other.rateLimitType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRateLimitNum()).compareTo(other.isSetRateLimitNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRateLimitNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rateLimitNum, other.rateLimitNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRateLimitExpiry()).compareTo(other.isSetRateLimitExpiry());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRateLimitExpiry()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rateLimitExpiry, other.rateLimitExpiry);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIsShow()).compareTo(other.isSetIsShow());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsShow()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isShow, other.isShow);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGuard()).compareTo(other.isSetGuard());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGuard()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.guard, other.guard);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetVideoUrl()).compareTo(other.isSetVideoUrl());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetVideoUrl()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.videoUrl, other.videoUrl);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIsRollGift()).compareTo(other.isSetIsRollGift());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsRollGift()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isRollGift, other.isRollGift);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRollNum()).compareTo(other.isSetRollNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRollNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rollNum, other.rollNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMultiVideoUrls()).compareTo(other.isSetMultiVideoUrls());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMultiVideoUrls()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.multiVideoUrls, other.multiVideoUrls);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGiftTips()).compareTo(other.isSetGiftTips());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGiftTips()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.giftTips, other.giftTips);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetStarVipLevel()).compareTo(other.isSetStarVipLevel());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStarVipLevel()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.starVipLevel, other.starVipLevel);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetVerticalVideoUrl()).compareTo(other.isSetVerticalVideoUrl());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetVerticalVideoUrl()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.verticalVideoUrl, other.verticalVideoUrl);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtResource()).compareTo(other.isSetExtResource());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtResource()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extResource, other.extResource);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetOrigPrice()).compareTo(other.isSetOrigPrice());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOrigPrice()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.origPrice, other.origPrice);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetNewTime()).compareTo(other.isSetNewTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetNewTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.newTime, other.newTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIsGlobal()).compareTo(other.isSetIsGlobal());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsGlobal()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isGlobal, other.isGlobal);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIsGlobalNotice()).compareTo(other.isSetIsGlobalNotice());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsGlobalNotice()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isGlobalNotice, other.isGlobalNotice);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtAttr()).compareTo(other.isSetExtAttr());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtAttr()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extAttr, other.extAttr);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("GiftInfo(");
    boolean first = true;

    if (isSetId()) {
      sb.append("id:");
      sb.append(this.id);
      first = false;
    }
    if (isSetName()) {
      if (!first) sb.append(", ");
      sb.append("name:");
      if (this.name == null) {
        sb.append("null");
      } else {
        sb.append(this.name);
      }
      first = false;
    }
    if (isSetUrl()) {
      if (!first) sb.append(", ");
      sb.append("url:");
      if (this.url == null) {
        sb.append("null");
      } else {
        sb.append(this.url);
      }
      first = false;
    }
    if (isSetPic()) {
      if (!first) sb.append(", ");
      sb.append("pic:");
      if (this.pic == null) {
        sb.append("null");
      } else {
        sb.append(this.pic);
      }
      first = false;
    }
    if (isSetPrice()) {
      if (!first) sb.append(", ");
      sb.append("price:");
      sb.append(this.price);
      first = false;
    }
    if (isSetExchange()) {
      if (!first) sb.append(", ");
      sb.append("exchange:");
      sb.append(this.exchange);
      first = false;
    }
    if (isSetCategory()) {
      if (!first) sb.append(", ");
      sb.append("category:");
      sb.append(this.category);
      first = false;
    }
    if (isSetMix()) {
      if (!first) sb.append(", ");
      sb.append("mix:");
      sb.append(this.mix);
      first = false;
    }
    if (isSetFly()) {
      if (!first) sb.append(", ");
      sb.append("fly:");
      sb.append(this.fly);
      first = false;
    }
    if (isSetClassName()) {
      if (!first) sb.append(", ");
      sb.append("className:");
      if (this.className == null) {
        sb.append("null");
      } else {
        sb.append(this.className);
      }
      first = false;
    }
    if (isSetSortIndex()) {
      if (!first) sb.append(", ");
      sb.append("sortIndex:");
      sb.append(this.sortIndex);
      first = false;
    }
    if (isSetStatus()) {
      if (!first) sb.append(", ");
      sb.append("status:");
      sb.append(this.status);
      first = false;
    }
    if (isSetImage()) {
      if (!first) sb.append(", ");
      sb.append("image:");
      if (this.image == null) {
        sb.append("null");
      } else {
        sb.append(this.image);
      }
      first = false;
    }
    if (isSetIsNew()) {
      if (!first) sb.append(", ");
      sb.append("isNew:");
      sb.append(this.isNew);
      first = false;
    }
    if (isSetImageTrans()) {
      if (!first) sb.append(", ");
      sb.append("imageTrans:");
      if (this.imageTrans == null) {
        sb.append("null");
      } else {
        sb.append(this.imageTrans);
      }
      first = false;
    }
    if (isSetImageGrade()) {
      if (!first) sb.append(", ");
      sb.append("imageGrade:");
      if (this.imageGrade == null) {
        sb.append("null");
      } else {
        sb.append(this.imageGrade);
      }
      first = false;
    }
    if (isSetExpire()) {
      if (!first) sb.append(", ");
      sb.append("expire:");
      sb.append(this.expire);
      first = false;
    }
    if (isSetRichLevelLimit()) {
      if (!first) sb.append(", ");
      sb.append("richLevelLimit:");
      sb.append(this.richLevelLimit);
      first = false;
    }
    if (isSetType()) {
      if (!first) sb.append(", ");
      sb.append("type:");
      sb.append(this.type);
      first = false;
    }
    if (isSetAdtEffect()) {
      if (!first) sb.append(", ");
      sb.append("adtEffect:");
      if (this.adtEffect == null) {
        sb.append("null");
      } else {
        sb.append(this.adtEffect);
      }
      first = false;
    }
    if (isSetGuardLevelLimit()) {
      if (!first) sb.append(", ");
      sb.append("guardLevelLimit:");
      sb.append(this.guardLevelLimit);
      first = false;
    }
    if (isSetVipLimit()) {
      if (!first) sb.append(", ");
      sb.append("vipLimit:");
      sb.append(this.vipLimit);
      first = false;
    }
    if (isSetIsPile()) {
      if (!first) sb.append(", ");
      sb.append("isPile:");
      sb.append(this.isPile);
      first = false;
    }
    if (isSetCanNotSend()) {
      if (!first) sb.append(", ");
      sb.append("canNotSend:");
      sb.append(this.canNotSend);
      first = false;
    }
    if (isSetExtraType()) {
      if (!first) sb.append(", ");
      sb.append("extraType:");
      sb.append(this.extraType);
      first = false;
    }
    if (isSetExtra()) {
      if (!first) sb.append(", ");
      sb.append("extra:");
      if (this.extra == null) {
        sb.append("null");
      } else {
        sb.append(this.extra);
      }
      first = false;
    }
    if (isSetUserIdLimit()) {
      if (!first) sb.append(", ");
      sb.append("userIdLimit:");
      sb.append(this.userIdLimit);
      first = false;
    }
    if (isSetUserLimitNickName()) {
      if (!first) sb.append(", ");
      sb.append("userLimitNickName:");
      if (this.userLimitNickName == null) {
        sb.append("null");
      } else {
        sb.append(this.userLimitNickName);
      }
      first = false;
    }
    if (isSetStarFansLimit()) {
      if (!first) sb.append(", ");
      sb.append("starFansLimit:");
      sb.append(this.starFansLimit);
      first = false;
    }
    if (isSetGiftUrl()) {
      if (!first) sb.append(", ");
      sb.append("giftUrl:");
      if (this.giftUrl == null) {
        sb.append("null");
      } else {
        sb.append(this.giftUrl);
      }
      first = false;
    }
    if (isSetSpecialType()) {
      if (!first) sb.append(", ");
      sb.append("specialType:");
      sb.append(this.specialType);
      first = false;
    }
    if (isSetTopCount()) {
      if (!first) sb.append(", ");
      sb.append("topCount:");
      sb.append(this.topCount);
      first = false;
    }
    if (isSetWeek()) {
      if (!first) sb.append(", ");
      sb.append("week:");
      sb.append(this.week);
      first = false;
    }
    if (isSetIsSuper()) {
      if (!first) sb.append(", ");
      sb.append("isSuper:");
      sb.append(this.isSuper);
      first = false;
    }
    if (isSetHappyObj()) {
      if (!first) sb.append(", ");
      sb.append("happyObj:");
      sb.append(this.happyObj);
      first = false;
    }
    if (isSetHappyType()) {
      if (!first) sb.append(", ");
      sb.append("happyType:");
      sb.append(this.happyType);
      first = false;
    }
    if (isSetMobileImage()) {
      if (!first) sb.append(", ");
      sb.append("mobileImage:");
      if (this.mobileImage == null) {
        sb.append("null");
      } else {
        sb.append(this.mobileImage);
      }
      first = false;
    }
    if (isSetIsPk()) {
      if (!first) sb.append(", ");
      sb.append("isPk:");
      sb.append(this.isPk);
      first = false;
    }
    if (isSetIsFullShow()) {
      if (!first) sb.append(", ");
      sb.append("isFullShow:");
      sb.append(this.isFullShow);
      first = false;
    }
    if (isSetDuration()) {
      if (!first) sb.append(", ");
      sb.append("duration:");
      sb.append(this.duration);
      first = false;
    }
    if (isSetLGuardLevelLimit()) {
      if (!first) sb.append(", ");
      sb.append("lGuardLevelLimit:");
      sb.append(this.lGuardLevelLimit);
      first = false;
    }
    if (isSetRateLimitType()) {
      if (!first) sb.append(", ");
      sb.append("rateLimitType:");
      sb.append(this.rateLimitType);
      first = false;
    }
    if (isSetRateLimitNum()) {
      if (!first) sb.append(", ");
      sb.append("rateLimitNum:");
      sb.append(this.rateLimitNum);
      first = false;
    }
    if (isSetRateLimitExpiry()) {
      if (!first) sb.append(", ");
      sb.append("rateLimitExpiry:");
      sb.append(this.rateLimitExpiry);
      first = false;
    }
    if (isSetIsShow()) {
      if (!first) sb.append(", ");
      sb.append("isShow:");
      sb.append(this.isShow);
      first = false;
    }
    if (isSetGuard()) {
      if (!first) sb.append(", ");
      sb.append("guard:");
      sb.append(this.guard);
      first = false;
    }
    if (isSetVideoUrl()) {
      if (!first) sb.append(", ");
      sb.append("videoUrl:");
      if (this.videoUrl == null) {
        sb.append("null");
      } else {
        sb.append(this.videoUrl);
      }
      first = false;
    }
    if (isSetIsRollGift()) {
      if (!first) sb.append(", ");
      sb.append("isRollGift:");
      sb.append(this.isRollGift);
      first = false;
    }
    if (isSetRollNum()) {
      if (!first) sb.append(", ");
      sb.append("rollNum:");
      sb.append(this.rollNum);
      first = false;
    }
    if (isSetMultiVideoUrls()) {
      if (!first) sb.append(", ");
      sb.append("multiVideoUrls:");
      if (this.multiVideoUrls == null) {
        sb.append("null");
      } else {
        sb.append(this.multiVideoUrls);
      }
      first = false;
    }
    if (isSetGiftTips()) {
      if (!first) sb.append(", ");
      sb.append("giftTips:");
      if (this.giftTips == null) {
        sb.append("null");
      } else {
        sb.append(this.giftTips);
      }
      first = false;
    }
    if (isSetStarVipLevel()) {
      if (!first) sb.append(", ");
      sb.append("starVipLevel:");
      sb.append(this.starVipLevel);
      first = false;
    }
    if (isSetVerticalVideoUrl()) {
      if (!first) sb.append(", ");
      sb.append("verticalVideoUrl:");
      if (this.verticalVideoUrl == null) {
        sb.append("null");
      } else {
        sb.append(this.verticalVideoUrl);
      }
      first = false;
    }
    if (isSetExtResource()) {
      if (!first) sb.append(", ");
      sb.append("extResource:");
      if (this.extResource == null) {
        sb.append("null");
      } else {
        sb.append(this.extResource);
      }
      first = false;
    }
    if (isSetOrigPrice()) {
      if (!first) sb.append(", ");
      sb.append("origPrice:");
      sb.append(this.origPrice);
      first = false;
    }
    if (isSetNewTime()) {
      if (!first) sb.append(", ");
      sb.append("newTime:");
      sb.append(this.newTime);
      first = false;
    }
    if (isSetIsGlobal()) {
      if (!first) sb.append(", ");
      sb.append("isGlobal:");
      sb.append(this.isGlobal);
      first = false;
    }
    if (isSetIsGlobalNotice()) {
      if (!first) sb.append(", ");
      sb.append("isGlobalNotice:");
      sb.append(this.isGlobalNotice);
      first = false;
    }
    if (isSetExtAttr()) {
      if (!first) sb.append(", ");
      sb.append("extAttr:");
      if (this.extAttr == null) {
        sb.append("null");
      } else {
        sb.append(this.extAttr);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GiftInfoStandardSchemeFactory implements SchemeFactory {
    public GiftInfoStandardScheme getScheme() {
      return new GiftInfoStandardScheme();
    }
  }

  private static class GiftInfoStandardScheme extends StandardScheme<GiftInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GiftInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.id = iprot.readI32();
              struct.setIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.name = iprot.readString();
              struct.setNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // URL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.url = iprot.readString();
              struct.setUrlIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // PIC
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.pic = iprot.readString();
              struct.setPicIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // PRICE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.price = iprot.readI32();
              struct.setPriceIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // EXCHANGE
            if (schemeField.type == org.apache.thrift.protocol.TType.DOUBLE) {
              struct.exchange = iprot.readDouble();
              struct.setExchangeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // CATEGORY
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.category = iprot.readI32();
              struct.setCategoryIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // MIX
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.mix = iprot.readI32();
              struct.setMixIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // FLY
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.fly = iprot.readI32();
              struct.setFlyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // CLASS_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.className = iprot.readString();
              struct.setClassNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // SORT_INDEX
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.sortIndex = iprot.readI32();
              struct.setSortIndexIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.status = iprot.readI32();
              struct.setStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // IMAGE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.image = iprot.readString();
              struct.setImageIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 14: // IS_NEW
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.isNew = iprot.readI32();
              struct.setIsNewIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // IMAGE_TRANS
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.imageTrans = iprot.readString();
              struct.setImageTransIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 16: // IMAGE_GRADE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.imageGrade = iprot.readString();
              struct.setImageGradeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 17: // EXPIRE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.expire = iprot.readI32();
              struct.setExpireIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 18: // RICH_LEVEL_LIMIT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.richLevelLimit = iprot.readI32();
              struct.setRichLevelLimitIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 19: // TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.type = iprot.readI32();
              struct.setTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 20: // ADT_EFFECT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.adtEffect = iprot.readString();
              struct.setAdtEffectIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 21: // GUARD_LEVEL_LIMIT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.guardLevelLimit = iprot.readI32();
              struct.setGuardLevelLimitIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 22: // VIP_LIMIT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.vipLimit = iprot.readI32();
              struct.setVipLimitIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 23: // IS_PILE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.isPile = iprot.readI32();
              struct.setIsPileIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 24: // CAN_NOT_SEND
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.canNotSend = iprot.readI32();
              struct.setCanNotSendIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 25: // EXTRA_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.extraType = iprot.readI32();
              struct.setExtraTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 26: // EXTRA
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.extra = iprot.readString();
              struct.setExtraIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 27: // USER_ID_LIMIT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.userIdLimit = iprot.readI64();
              struct.setUserIdLimitIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 28: // USER_LIMIT_NICK_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.userLimitNickName = iprot.readString();
              struct.setUserLimitNickNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 29: // STAR_FANS_LIMIT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.starFansLimit = iprot.readI32();
              struct.setStarFansLimitIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 30: // GIFT_URL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.giftUrl = iprot.readString();
              struct.setGiftUrlIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 31: // SPECIAL_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.specialType = iprot.readI32();
              struct.setSpecialTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 32: // TOP_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.topCount = iprot.readI32();
              struct.setTopCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 33: // WEEK
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.week = iprot.readI32();
              struct.setWeekIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 34: // IS_SUPER
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.isSuper = iprot.readI32();
              struct.setIsSuperIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 35: // HAPPY_OBJ
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.happyObj = iprot.readI32();
              struct.setHappyObjIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 36: // HAPPY_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.happyType = iprot.readI32();
              struct.setHappyTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 37: // MOBILE_IMAGE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.mobileImage = iprot.readString();
              struct.setMobileImageIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 38: // IS_PK
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.isPk = iprot.readI32();
              struct.setIsPkIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 39: // IS_FULL_SHOW
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.isFullShow = iprot.readI32();
              struct.setIsFullShowIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 40: // DURATION
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.duration = iprot.readI32();
              struct.setDurationIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 41: // L_GUARD_LEVEL_LIMIT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.lGuardLevelLimit = iprot.readI32();
              struct.setLGuardLevelLimitIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 42: // RATE_LIMIT_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.rateLimitType = iprot.readI32();
              struct.setRateLimitTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 43: // RATE_LIMIT_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.rateLimitNum = iprot.readI32();
              struct.setRateLimitNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 44: // RATE_LIMIT_EXPIRY
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.rateLimitExpiry = iprot.readI32();
              struct.setRateLimitExpiryIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 45: // IS_SHOW
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.isShow = iprot.readI32();
              struct.setIsShowIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 46: // GUARD
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.guard = iprot.readI32();
              struct.setGuardIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 47: // VIDEO_URL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.videoUrl = iprot.readString();
              struct.setVideoUrlIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 48: // IS_ROLL_GIFT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.isRollGift = iprot.readI32();
              struct.setIsRollGiftIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 49: // ROLL_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rollNum = iprot.readI64();
              struct.setRollNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 50: // MULTI_VIDEO_URLS
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.multiVideoUrls = iprot.readString();
              struct.setMultiVideoUrlsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 51: // GIFT_TIPS
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.giftTips = iprot.readString();
              struct.setGiftTipsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 52: // STAR_VIP_LEVEL
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.starVipLevel = iprot.readI32();
              struct.setStarVipLevelIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 53: // VERTICAL_VIDEO_URL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.verticalVideoUrl = iprot.readString();
              struct.setVerticalVideoUrlIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 54: // EXT_RESOURCE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.extResource = iprot.readString();
              struct.setExtResourceIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 55: // ORIG_PRICE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.origPrice = iprot.readI32();
              struct.setOrigPriceIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 56: // NEW_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.newTime = iprot.readI32();
              struct.setNewTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 57: // IS_GLOBAL
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.isGlobal = iprot.readI32();
              struct.setIsGlobalIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 58: // IS_GLOBAL_NOTICE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.isGlobalNotice = iprot.readI32();
              struct.setIsGlobalNoticeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 59: // EXT_ATTR
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.extAttr = iprot.readString();
              struct.setExtAttrIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GiftInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetId()) {
        oprot.writeFieldBegin(ID_FIELD_DESC);
        oprot.writeI32(struct.id);
        oprot.writeFieldEnd();
      }
      if (struct.name != null) {
        if (struct.isSetName()) {
          oprot.writeFieldBegin(NAME_FIELD_DESC);
          oprot.writeString(struct.name);
          oprot.writeFieldEnd();
        }
      }
      if (struct.url != null) {
        if (struct.isSetUrl()) {
          oprot.writeFieldBegin(URL_FIELD_DESC);
          oprot.writeString(struct.url);
          oprot.writeFieldEnd();
        }
      }
      if (struct.pic != null) {
        if (struct.isSetPic()) {
          oprot.writeFieldBegin(PIC_FIELD_DESC);
          oprot.writeString(struct.pic);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetPrice()) {
        oprot.writeFieldBegin(PRICE_FIELD_DESC);
        oprot.writeI32(struct.price);
        oprot.writeFieldEnd();
      }
      if (struct.isSetExchange()) {
        oprot.writeFieldBegin(EXCHANGE_FIELD_DESC);
        oprot.writeDouble(struct.exchange);
        oprot.writeFieldEnd();
      }
      if (struct.isSetCategory()) {
        oprot.writeFieldBegin(CATEGORY_FIELD_DESC);
        oprot.writeI32(struct.category);
        oprot.writeFieldEnd();
      }
      if (struct.isSetMix()) {
        oprot.writeFieldBegin(MIX_FIELD_DESC);
        oprot.writeI32(struct.mix);
        oprot.writeFieldEnd();
      }
      if (struct.isSetFly()) {
        oprot.writeFieldBegin(FLY_FIELD_DESC);
        oprot.writeI32(struct.fly);
        oprot.writeFieldEnd();
      }
      if (struct.className != null) {
        if (struct.isSetClassName()) {
          oprot.writeFieldBegin(CLASS_NAME_FIELD_DESC);
          oprot.writeString(struct.className);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetSortIndex()) {
        oprot.writeFieldBegin(SORT_INDEX_FIELD_DESC);
        oprot.writeI32(struct.sortIndex);
        oprot.writeFieldEnd();
      }
      if (struct.isSetStatus()) {
        oprot.writeFieldBegin(STATUS_FIELD_DESC);
        oprot.writeI32(struct.status);
        oprot.writeFieldEnd();
      }
      if (struct.image != null) {
        if (struct.isSetImage()) {
          oprot.writeFieldBegin(IMAGE_FIELD_DESC);
          oprot.writeString(struct.image);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetIsNew()) {
        oprot.writeFieldBegin(IS_NEW_FIELD_DESC);
        oprot.writeI32(struct.isNew);
        oprot.writeFieldEnd();
      }
      if (struct.imageTrans != null) {
        if (struct.isSetImageTrans()) {
          oprot.writeFieldBegin(IMAGE_TRANS_FIELD_DESC);
          oprot.writeString(struct.imageTrans);
          oprot.writeFieldEnd();
        }
      }
      if (struct.imageGrade != null) {
        if (struct.isSetImageGrade()) {
          oprot.writeFieldBegin(IMAGE_GRADE_FIELD_DESC);
          oprot.writeString(struct.imageGrade);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetExpire()) {
        oprot.writeFieldBegin(EXPIRE_FIELD_DESC);
        oprot.writeI32(struct.expire);
        oprot.writeFieldEnd();
      }
      if (struct.isSetRichLevelLimit()) {
        oprot.writeFieldBegin(RICH_LEVEL_LIMIT_FIELD_DESC);
        oprot.writeI32(struct.richLevelLimit);
        oprot.writeFieldEnd();
      }
      if (struct.isSetType()) {
        oprot.writeFieldBegin(TYPE_FIELD_DESC);
        oprot.writeI32(struct.type);
        oprot.writeFieldEnd();
      }
      if (struct.adtEffect != null) {
        if (struct.isSetAdtEffect()) {
          oprot.writeFieldBegin(ADT_EFFECT_FIELD_DESC);
          oprot.writeString(struct.adtEffect);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetGuardLevelLimit()) {
        oprot.writeFieldBegin(GUARD_LEVEL_LIMIT_FIELD_DESC);
        oprot.writeI32(struct.guardLevelLimit);
        oprot.writeFieldEnd();
      }
      if (struct.isSetVipLimit()) {
        oprot.writeFieldBegin(VIP_LIMIT_FIELD_DESC);
        oprot.writeI32(struct.vipLimit);
        oprot.writeFieldEnd();
      }
      if (struct.isSetIsPile()) {
        oprot.writeFieldBegin(IS_PILE_FIELD_DESC);
        oprot.writeI32(struct.isPile);
        oprot.writeFieldEnd();
      }
      if (struct.isSetCanNotSend()) {
        oprot.writeFieldBegin(CAN_NOT_SEND_FIELD_DESC);
        oprot.writeI32(struct.canNotSend);
        oprot.writeFieldEnd();
      }
      if (struct.isSetExtraType()) {
        oprot.writeFieldBegin(EXTRA_TYPE_FIELD_DESC);
        oprot.writeI32(struct.extraType);
        oprot.writeFieldEnd();
      }
      if (struct.extra != null) {
        if (struct.isSetExtra()) {
          oprot.writeFieldBegin(EXTRA_FIELD_DESC);
          oprot.writeString(struct.extra);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetUserIdLimit()) {
        oprot.writeFieldBegin(USER_ID_LIMIT_FIELD_DESC);
        oprot.writeI64(struct.userIdLimit);
        oprot.writeFieldEnd();
      }
      if (struct.userLimitNickName != null) {
        if (struct.isSetUserLimitNickName()) {
          oprot.writeFieldBegin(USER_LIMIT_NICK_NAME_FIELD_DESC);
          oprot.writeString(struct.userLimitNickName);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetStarFansLimit()) {
        oprot.writeFieldBegin(STAR_FANS_LIMIT_FIELD_DESC);
        oprot.writeI32(struct.starFansLimit);
        oprot.writeFieldEnd();
      }
      if (struct.giftUrl != null) {
        if (struct.isSetGiftUrl()) {
          oprot.writeFieldBegin(GIFT_URL_FIELD_DESC);
          oprot.writeString(struct.giftUrl);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetSpecialType()) {
        oprot.writeFieldBegin(SPECIAL_TYPE_FIELD_DESC);
        oprot.writeI32(struct.specialType);
        oprot.writeFieldEnd();
      }
      if (struct.isSetTopCount()) {
        oprot.writeFieldBegin(TOP_COUNT_FIELD_DESC);
        oprot.writeI32(struct.topCount);
        oprot.writeFieldEnd();
      }
      if (struct.isSetWeek()) {
        oprot.writeFieldBegin(WEEK_FIELD_DESC);
        oprot.writeI32(struct.week);
        oprot.writeFieldEnd();
      }
      if (struct.isSetIsSuper()) {
        oprot.writeFieldBegin(IS_SUPER_FIELD_DESC);
        oprot.writeI32(struct.isSuper);
        oprot.writeFieldEnd();
      }
      if (struct.isSetHappyObj()) {
        oprot.writeFieldBegin(HAPPY_OBJ_FIELD_DESC);
        oprot.writeI32(struct.happyObj);
        oprot.writeFieldEnd();
      }
      if (struct.isSetHappyType()) {
        oprot.writeFieldBegin(HAPPY_TYPE_FIELD_DESC);
        oprot.writeI32(struct.happyType);
        oprot.writeFieldEnd();
      }
      if (struct.mobileImage != null) {
        if (struct.isSetMobileImage()) {
          oprot.writeFieldBegin(MOBILE_IMAGE_FIELD_DESC);
          oprot.writeString(struct.mobileImage);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetIsPk()) {
        oprot.writeFieldBegin(IS_PK_FIELD_DESC);
        oprot.writeI32(struct.isPk);
        oprot.writeFieldEnd();
      }
      if (struct.isSetIsFullShow()) {
        oprot.writeFieldBegin(IS_FULL_SHOW_FIELD_DESC);
        oprot.writeI32(struct.isFullShow);
        oprot.writeFieldEnd();
      }
      if (struct.isSetDuration()) {
        oprot.writeFieldBegin(DURATION_FIELD_DESC);
        oprot.writeI32(struct.duration);
        oprot.writeFieldEnd();
      }
      if (struct.isSetLGuardLevelLimit()) {
        oprot.writeFieldBegin(L_GUARD_LEVEL_LIMIT_FIELD_DESC);
        oprot.writeI32(struct.lGuardLevelLimit);
        oprot.writeFieldEnd();
      }
      if (struct.isSetRateLimitType()) {
        oprot.writeFieldBegin(RATE_LIMIT_TYPE_FIELD_DESC);
        oprot.writeI32(struct.rateLimitType);
        oprot.writeFieldEnd();
      }
      if (struct.isSetRateLimitNum()) {
        oprot.writeFieldBegin(RATE_LIMIT_NUM_FIELD_DESC);
        oprot.writeI32(struct.rateLimitNum);
        oprot.writeFieldEnd();
      }
      if (struct.isSetRateLimitExpiry()) {
        oprot.writeFieldBegin(RATE_LIMIT_EXPIRY_FIELD_DESC);
        oprot.writeI32(struct.rateLimitExpiry);
        oprot.writeFieldEnd();
      }
      if (struct.isSetIsShow()) {
        oprot.writeFieldBegin(IS_SHOW_FIELD_DESC);
        oprot.writeI32(struct.isShow);
        oprot.writeFieldEnd();
      }
      if (struct.isSetGuard()) {
        oprot.writeFieldBegin(GUARD_FIELD_DESC);
        oprot.writeI32(struct.guard);
        oprot.writeFieldEnd();
      }
      if (struct.videoUrl != null) {
        if (struct.isSetVideoUrl()) {
          oprot.writeFieldBegin(VIDEO_URL_FIELD_DESC);
          oprot.writeString(struct.videoUrl);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetIsRollGift()) {
        oprot.writeFieldBegin(IS_ROLL_GIFT_FIELD_DESC);
        oprot.writeI32(struct.isRollGift);
        oprot.writeFieldEnd();
      }
      if (struct.isSetRollNum()) {
        oprot.writeFieldBegin(ROLL_NUM_FIELD_DESC);
        oprot.writeI64(struct.rollNum);
        oprot.writeFieldEnd();
      }
      if (struct.multiVideoUrls != null) {
        if (struct.isSetMultiVideoUrls()) {
          oprot.writeFieldBegin(MULTI_VIDEO_URLS_FIELD_DESC);
          oprot.writeString(struct.multiVideoUrls);
          oprot.writeFieldEnd();
        }
      }
      if (struct.giftTips != null) {
        if (struct.isSetGiftTips()) {
          oprot.writeFieldBegin(GIFT_TIPS_FIELD_DESC);
          oprot.writeString(struct.giftTips);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetStarVipLevel()) {
        oprot.writeFieldBegin(STAR_VIP_LEVEL_FIELD_DESC);
        oprot.writeI32(struct.starVipLevel);
        oprot.writeFieldEnd();
      }
      if (struct.verticalVideoUrl != null) {
        if (struct.isSetVerticalVideoUrl()) {
          oprot.writeFieldBegin(VERTICAL_VIDEO_URL_FIELD_DESC);
          oprot.writeString(struct.verticalVideoUrl);
          oprot.writeFieldEnd();
        }
      }
      if (struct.extResource != null) {
        if (struct.isSetExtResource()) {
          oprot.writeFieldBegin(EXT_RESOURCE_FIELD_DESC);
          oprot.writeString(struct.extResource);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetOrigPrice()) {
        oprot.writeFieldBegin(ORIG_PRICE_FIELD_DESC);
        oprot.writeI32(struct.origPrice);
        oprot.writeFieldEnd();
      }
      if (struct.isSetNewTime()) {
        oprot.writeFieldBegin(NEW_TIME_FIELD_DESC);
        oprot.writeI32(struct.newTime);
        oprot.writeFieldEnd();
      }
      if (struct.isSetIsGlobal()) {
        oprot.writeFieldBegin(IS_GLOBAL_FIELD_DESC);
        oprot.writeI32(struct.isGlobal);
        oprot.writeFieldEnd();
      }
      if (struct.isSetIsGlobalNotice()) {
        oprot.writeFieldBegin(IS_GLOBAL_NOTICE_FIELD_DESC);
        oprot.writeI32(struct.isGlobalNotice);
        oprot.writeFieldEnd();
      }
      if (struct.extAttr != null) {
        if (struct.isSetExtAttr()) {
          oprot.writeFieldBegin(EXT_ATTR_FIELD_DESC);
          oprot.writeString(struct.extAttr);
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GiftInfoTupleSchemeFactory implements SchemeFactory {
    public GiftInfoTupleScheme getScheme() {
      return new GiftInfoTupleScheme();
    }
  }

  private static class GiftInfoTupleScheme extends TupleScheme<GiftInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GiftInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetId()) {
        optionals.set(0);
      }
      if (struct.isSetName()) {
        optionals.set(1);
      }
      if (struct.isSetUrl()) {
        optionals.set(2);
      }
      if (struct.isSetPic()) {
        optionals.set(3);
      }
      if (struct.isSetPrice()) {
        optionals.set(4);
      }
      if (struct.isSetExchange()) {
        optionals.set(5);
      }
      if (struct.isSetCategory()) {
        optionals.set(6);
      }
      if (struct.isSetMix()) {
        optionals.set(7);
      }
      if (struct.isSetFly()) {
        optionals.set(8);
      }
      if (struct.isSetClassName()) {
        optionals.set(9);
      }
      if (struct.isSetSortIndex()) {
        optionals.set(10);
      }
      if (struct.isSetStatus()) {
        optionals.set(11);
      }
      if (struct.isSetImage()) {
        optionals.set(12);
      }
      if (struct.isSetIsNew()) {
        optionals.set(13);
      }
      if (struct.isSetImageTrans()) {
        optionals.set(14);
      }
      if (struct.isSetImageGrade()) {
        optionals.set(15);
      }
      if (struct.isSetExpire()) {
        optionals.set(16);
      }
      if (struct.isSetRichLevelLimit()) {
        optionals.set(17);
      }
      if (struct.isSetType()) {
        optionals.set(18);
      }
      if (struct.isSetAdtEffect()) {
        optionals.set(19);
      }
      if (struct.isSetGuardLevelLimit()) {
        optionals.set(20);
      }
      if (struct.isSetVipLimit()) {
        optionals.set(21);
      }
      if (struct.isSetIsPile()) {
        optionals.set(22);
      }
      if (struct.isSetCanNotSend()) {
        optionals.set(23);
      }
      if (struct.isSetExtraType()) {
        optionals.set(24);
      }
      if (struct.isSetExtra()) {
        optionals.set(25);
      }
      if (struct.isSetUserIdLimit()) {
        optionals.set(26);
      }
      if (struct.isSetUserLimitNickName()) {
        optionals.set(27);
      }
      if (struct.isSetStarFansLimit()) {
        optionals.set(28);
      }
      if (struct.isSetGiftUrl()) {
        optionals.set(29);
      }
      if (struct.isSetSpecialType()) {
        optionals.set(30);
      }
      if (struct.isSetTopCount()) {
        optionals.set(31);
      }
      if (struct.isSetWeek()) {
        optionals.set(32);
      }
      if (struct.isSetIsSuper()) {
        optionals.set(33);
      }
      if (struct.isSetHappyObj()) {
        optionals.set(34);
      }
      if (struct.isSetHappyType()) {
        optionals.set(35);
      }
      if (struct.isSetMobileImage()) {
        optionals.set(36);
      }
      if (struct.isSetIsPk()) {
        optionals.set(37);
      }
      if (struct.isSetIsFullShow()) {
        optionals.set(38);
      }
      if (struct.isSetDuration()) {
        optionals.set(39);
      }
      if (struct.isSetLGuardLevelLimit()) {
        optionals.set(40);
      }
      if (struct.isSetRateLimitType()) {
        optionals.set(41);
      }
      if (struct.isSetRateLimitNum()) {
        optionals.set(42);
      }
      if (struct.isSetRateLimitExpiry()) {
        optionals.set(43);
      }
      if (struct.isSetIsShow()) {
        optionals.set(44);
      }
      if (struct.isSetGuard()) {
        optionals.set(45);
      }
      if (struct.isSetVideoUrl()) {
        optionals.set(46);
      }
      if (struct.isSetIsRollGift()) {
        optionals.set(47);
      }
      if (struct.isSetRollNum()) {
        optionals.set(48);
      }
      if (struct.isSetMultiVideoUrls()) {
        optionals.set(49);
      }
      if (struct.isSetGiftTips()) {
        optionals.set(50);
      }
      if (struct.isSetStarVipLevel()) {
        optionals.set(51);
      }
      if (struct.isSetVerticalVideoUrl()) {
        optionals.set(52);
      }
      if (struct.isSetExtResource()) {
        optionals.set(53);
      }
      if (struct.isSetOrigPrice()) {
        optionals.set(54);
      }
      if (struct.isSetNewTime()) {
        optionals.set(55);
      }
      if (struct.isSetIsGlobal()) {
        optionals.set(56);
      }
      if (struct.isSetIsGlobalNotice()) {
        optionals.set(57);
      }
      if (struct.isSetExtAttr()) {
        optionals.set(58);
      }
      oprot.writeBitSet(optionals, 59);
      if (struct.isSetId()) {
        oprot.writeI32(struct.id);
      }
      if (struct.isSetName()) {
        oprot.writeString(struct.name);
      }
      if (struct.isSetUrl()) {
        oprot.writeString(struct.url);
      }
      if (struct.isSetPic()) {
        oprot.writeString(struct.pic);
      }
      if (struct.isSetPrice()) {
        oprot.writeI32(struct.price);
      }
      if (struct.isSetExchange()) {
        oprot.writeDouble(struct.exchange);
      }
      if (struct.isSetCategory()) {
        oprot.writeI32(struct.category);
      }
      if (struct.isSetMix()) {
        oprot.writeI32(struct.mix);
      }
      if (struct.isSetFly()) {
        oprot.writeI32(struct.fly);
      }
      if (struct.isSetClassName()) {
        oprot.writeString(struct.className);
      }
      if (struct.isSetSortIndex()) {
        oprot.writeI32(struct.sortIndex);
      }
      if (struct.isSetStatus()) {
        oprot.writeI32(struct.status);
      }
      if (struct.isSetImage()) {
        oprot.writeString(struct.image);
      }
      if (struct.isSetIsNew()) {
        oprot.writeI32(struct.isNew);
      }
      if (struct.isSetImageTrans()) {
        oprot.writeString(struct.imageTrans);
      }
      if (struct.isSetImageGrade()) {
        oprot.writeString(struct.imageGrade);
      }
      if (struct.isSetExpire()) {
        oprot.writeI32(struct.expire);
      }
      if (struct.isSetRichLevelLimit()) {
        oprot.writeI32(struct.richLevelLimit);
      }
      if (struct.isSetType()) {
        oprot.writeI32(struct.type);
      }
      if (struct.isSetAdtEffect()) {
        oprot.writeString(struct.adtEffect);
      }
      if (struct.isSetGuardLevelLimit()) {
        oprot.writeI32(struct.guardLevelLimit);
      }
      if (struct.isSetVipLimit()) {
        oprot.writeI32(struct.vipLimit);
      }
      if (struct.isSetIsPile()) {
        oprot.writeI32(struct.isPile);
      }
      if (struct.isSetCanNotSend()) {
        oprot.writeI32(struct.canNotSend);
      }
      if (struct.isSetExtraType()) {
        oprot.writeI32(struct.extraType);
      }
      if (struct.isSetExtra()) {
        oprot.writeString(struct.extra);
      }
      if (struct.isSetUserIdLimit()) {
        oprot.writeI64(struct.userIdLimit);
      }
      if (struct.isSetUserLimitNickName()) {
        oprot.writeString(struct.userLimitNickName);
      }
      if (struct.isSetStarFansLimit()) {
        oprot.writeI32(struct.starFansLimit);
      }
      if (struct.isSetGiftUrl()) {
        oprot.writeString(struct.giftUrl);
      }
      if (struct.isSetSpecialType()) {
        oprot.writeI32(struct.specialType);
      }
      if (struct.isSetTopCount()) {
        oprot.writeI32(struct.topCount);
      }
      if (struct.isSetWeek()) {
        oprot.writeI32(struct.week);
      }
      if (struct.isSetIsSuper()) {
        oprot.writeI32(struct.isSuper);
      }
      if (struct.isSetHappyObj()) {
        oprot.writeI32(struct.happyObj);
      }
      if (struct.isSetHappyType()) {
        oprot.writeI32(struct.happyType);
      }
      if (struct.isSetMobileImage()) {
        oprot.writeString(struct.mobileImage);
      }
      if (struct.isSetIsPk()) {
        oprot.writeI32(struct.isPk);
      }
      if (struct.isSetIsFullShow()) {
        oprot.writeI32(struct.isFullShow);
      }
      if (struct.isSetDuration()) {
        oprot.writeI32(struct.duration);
      }
      if (struct.isSetLGuardLevelLimit()) {
        oprot.writeI32(struct.lGuardLevelLimit);
      }
      if (struct.isSetRateLimitType()) {
        oprot.writeI32(struct.rateLimitType);
      }
      if (struct.isSetRateLimitNum()) {
        oprot.writeI32(struct.rateLimitNum);
      }
      if (struct.isSetRateLimitExpiry()) {
        oprot.writeI32(struct.rateLimitExpiry);
      }
      if (struct.isSetIsShow()) {
        oprot.writeI32(struct.isShow);
      }
      if (struct.isSetGuard()) {
        oprot.writeI32(struct.guard);
      }
      if (struct.isSetVideoUrl()) {
        oprot.writeString(struct.videoUrl);
      }
      if (struct.isSetIsRollGift()) {
        oprot.writeI32(struct.isRollGift);
      }
      if (struct.isSetRollNum()) {
        oprot.writeI64(struct.rollNum);
      }
      if (struct.isSetMultiVideoUrls()) {
        oprot.writeString(struct.multiVideoUrls);
      }
      if (struct.isSetGiftTips()) {
        oprot.writeString(struct.giftTips);
      }
      if (struct.isSetStarVipLevel()) {
        oprot.writeI32(struct.starVipLevel);
      }
      if (struct.isSetVerticalVideoUrl()) {
        oprot.writeString(struct.verticalVideoUrl);
      }
      if (struct.isSetExtResource()) {
        oprot.writeString(struct.extResource);
      }
      if (struct.isSetOrigPrice()) {
        oprot.writeI32(struct.origPrice);
      }
      if (struct.isSetNewTime()) {
        oprot.writeI32(struct.newTime);
      }
      if (struct.isSetIsGlobal()) {
        oprot.writeI32(struct.isGlobal);
      }
      if (struct.isSetIsGlobalNotice()) {
        oprot.writeI32(struct.isGlobalNotice);
      }
      if (struct.isSetExtAttr()) {
        oprot.writeString(struct.extAttr);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GiftInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(59);
      if (incoming.get(0)) {
        struct.id = iprot.readI32();
        struct.setIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.name = iprot.readString();
        struct.setNameIsSet(true);
      }
      if (incoming.get(2)) {
        struct.url = iprot.readString();
        struct.setUrlIsSet(true);
      }
      if (incoming.get(3)) {
        struct.pic = iprot.readString();
        struct.setPicIsSet(true);
      }
      if (incoming.get(4)) {
        struct.price = iprot.readI32();
        struct.setPriceIsSet(true);
      }
      if (incoming.get(5)) {
        struct.exchange = iprot.readDouble();
        struct.setExchangeIsSet(true);
      }
      if (incoming.get(6)) {
        struct.category = iprot.readI32();
        struct.setCategoryIsSet(true);
      }
      if (incoming.get(7)) {
        struct.mix = iprot.readI32();
        struct.setMixIsSet(true);
      }
      if (incoming.get(8)) {
        struct.fly = iprot.readI32();
        struct.setFlyIsSet(true);
      }
      if (incoming.get(9)) {
        struct.className = iprot.readString();
        struct.setClassNameIsSet(true);
      }
      if (incoming.get(10)) {
        struct.sortIndex = iprot.readI32();
        struct.setSortIndexIsSet(true);
      }
      if (incoming.get(11)) {
        struct.status = iprot.readI32();
        struct.setStatusIsSet(true);
      }
      if (incoming.get(12)) {
        struct.image = iprot.readString();
        struct.setImageIsSet(true);
      }
      if (incoming.get(13)) {
        struct.isNew = iprot.readI32();
        struct.setIsNewIsSet(true);
      }
      if (incoming.get(14)) {
        struct.imageTrans = iprot.readString();
        struct.setImageTransIsSet(true);
      }
      if (incoming.get(15)) {
        struct.imageGrade = iprot.readString();
        struct.setImageGradeIsSet(true);
      }
      if (incoming.get(16)) {
        struct.expire = iprot.readI32();
        struct.setExpireIsSet(true);
      }
      if (incoming.get(17)) {
        struct.richLevelLimit = iprot.readI32();
        struct.setRichLevelLimitIsSet(true);
      }
      if (incoming.get(18)) {
        struct.type = iprot.readI32();
        struct.setTypeIsSet(true);
      }
      if (incoming.get(19)) {
        struct.adtEffect = iprot.readString();
        struct.setAdtEffectIsSet(true);
      }
      if (incoming.get(20)) {
        struct.guardLevelLimit = iprot.readI32();
        struct.setGuardLevelLimitIsSet(true);
      }
      if (incoming.get(21)) {
        struct.vipLimit = iprot.readI32();
        struct.setVipLimitIsSet(true);
      }
      if (incoming.get(22)) {
        struct.isPile = iprot.readI32();
        struct.setIsPileIsSet(true);
      }
      if (incoming.get(23)) {
        struct.canNotSend = iprot.readI32();
        struct.setCanNotSendIsSet(true);
      }
      if (incoming.get(24)) {
        struct.extraType = iprot.readI32();
        struct.setExtraTypeIsSet(true);
      }
      if (incoming.get(25)) {
        struct.extra = iprot.readString();
        struct.setExtraIsSet(true);
      }
      if (incoming.get(26)) {
        struct.userIdLimit = iprot.readI64();
        struct.setUserIdLimitIsSet(true);
      }
      if (incoming.get(27)) {
        struct.userLimitNickName = iprot.readString();
        struct.setUserLimitNickNameIsSet(true);
      }
      if (incoming.get(28)) {
        struct.starFansLimit = iprot.readI32();
        struct.setStarFansLimitIsSet(true);
      }
      if (incoming.get(29)) {
        struct.giftUrl = iprot.readString();
        struct.setGiftUrlIsSet(true);
      }
      if (incoming.get(30)) {
        struct.specialType = iprot.readI32();
        struct.setSpecialTypeIsSet(true);
      }
      if (incoming.get(31)) {
        struct.topCount = iprot.readI32();
        struct.setTopCountIsSet(true);
      }
      if (incoming.get(32)) {
        struct.week = iprot.readI32();
        struct.setWeekIsSet(true);
      }
      if (incoming.get(33)) {
        struct.isSuper = iprot.readI32();
        struct.setIsSuperIsSet(true);
      }
      if (incoming.get(34)) {
        struct.happyObj = iprot.readI32();
        struct.setHappyObjIsSet(true);
      }
      if (incoming.get(35)) {
        struct.happyType = iprot.readI32();
        struct.setHappyTypeIsSet(true);
      }
      if (incoming.get(36)) {
        struct.mobileImage = iprot.readString();
        struct.setMobileImageIsSet(true);
      }
      if (incoming.get(37)) {
        struct.isPk = iprot.readI32();
        struct.setIsPkIsSet(true);
      }
      if (incoming.get(38)) {
        struct.isFullShow = iprot.readI32();
        struct.setIsFullShowIsSet(true);
      }
      if (incoming.get(39)) {
        struct.duration = iprot.readI32();
        struct.setDurationIsSet(true);
      }
      if (incoming.get(40)) {
        struct.lGuardLevelLimit = iprot.readI32();
        struct.setLGuardLevelLimitIsSet(true);
      }
      if (incoming.get(41)) {
        struct.rateLimitType = iprot.readI32();
        struct.setRateLimitTypeIsSet(true);
      }
      if (incoming.get(42)) {
        struct.rateLimitNum = iprot.readI32();
        struct.setRateLimitNumIsSet(true);
      }
      if (incoming.get(43)) {
        struct.rateLimitExpiry = iprot.readI32();
        struct.setRateLimitExpiryIsSet(true);
      }
      if (incoming.get(44)) {
        struct.isShow = iprot.readI32();
        struct.setIsShowIsSet(true);
      }
      if (incoming.get(45)) {
        struct.guard = iprot.readI32();
        struct.setGuardIsSet(true);
      }
      if (incoming.get(46)) {
        struct.videoUrl = iprot.readString();
        struct.setVideoUrlIsSet(true);
      }
      if (incoming.get(47)) {
        struct.isRollGift = iprot.readI32();
        struct.setIsRollGiftIsSet(true);
      }
      if (incoming.get(48)) {
        struct.rollNum = iprot.readI64();
        struct.setRollNumIsSet(true);
      }
      if (incoming.get(49)) {
        struct.multiVideoUrls = iprot.readString();
        struct.setMultiVideoUrlsIsSet(true);
      }
      if (incoming.get(50)) {
        struct.giftTips = iprot.readString();
        struct.setGiftTipsIsSet(true);
      }
      if (incoming.get(51)) {
        struct.starVipLevel = iprot.readI32();
        struct.setStarVipLevelIsSet(true);
      }
      if (incoming.get(52)) {
        struct.verticalVideoUrl = iprot.readString();
        struct.setVerticalVideoUrlIsSet(true);
      }
      if (incoming.get(53)) {
        struct.extResource = iprot.readString();
        struct.setExtResourceIsSet(true);
      }
      if (incoming.get(54)) {
        struct.origPrice = iprot.readI32();
        struct.setOrigPriceIsSet(true);
      }
      if (incoming.get(55)) {
        struct.newTime = iprot.readI32();
        struct.setNewTimeIsSet(true);
      }
      if (incoming.get(56)) {
        struct.isGlobal = iprot.readI32();
        struct.setIsGlobalIsSet(true);
      }
      if (incoming.get(57)) {
        struct.isGlobalNotice = iprot.readI32();
        struct.setIsGlobalNoticeIsSet(true);
      }
      if (incoming.get(58)) {
        struct.extAttr = iprot.readString();
        struct.setExtAttrIsSet(true);
      }
    }
  }

}

