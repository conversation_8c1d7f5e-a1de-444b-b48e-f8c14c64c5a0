/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.platform.asset.readservice.thrift;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-08-17")
public class OrderVO implements org.apache.thrift.TBase<OrderVO, OrderVO._Fields>, java.io.Serializable, Cloneable, Comparable<OrderVO> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("OrderVO");

  private static final org.apache.thrift.protocol.TField ORDER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("orderId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField BUSINESS_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("businessId", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField ASSET_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("assetId", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("num", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField ORDER_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("orderType", org.apache.thrift.protocol.TType.I32, (short)6);
  private static final org.apache.thrift.protocol.TField CREATE_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("createTime", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("status", org.apache.thrift.protocol.TType.I32, (short)8);
  private static final org.apache.thrift.protocol.TField REF_ORDER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("refOrderId", org.apache.thrift.protocol.TType.STRING, (short)9);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new OrderVOStandardSchemeFactory());
    schemes.put(TupleScheme.class, new OrderVOTupleSchemeFactory());
  }

  public String orderId; // required
  public int businessId; // required
  /**
   * kugouId
   * 
   */
  public long kugouId; // required
  /**
   * 礼物ID
   * 
   */
  public long assetId; // required
  /**
   * 操作数量
   * 
   */
  public String num; // required
  /**
   * *    1,"发放"
   *      2,"扣减"
   *      3,"冻结"
   *      4, "冻结确认
   *      5,"冻结取消"
   *      6,"退款"
   * *
   */
  public int orderType; // required
  /**
   * 创建时间
   * 
   */
  public long createTime; // required
  /**
   *      1, "订单成功"
   *      2, "订单已退款"
   *      3, "订单已冻结"
   *      4, "订单已确认"
   *      5, "订单已取消"
   * *
   */
  public int status; // required
  /**
   * 退款原订单号
   * 
   */
  public String refOrderId; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ORDER_ID((short)1, "orderId"),
    BUSINESS_ID((short)2, "businessId"),
    /**
     * kugouId
     * 
     */
    KUGOU_ID((short)3, "kugouId"),
    /**
     * 礼物ID
     * 
     */
    ASSET_ID((short)4, "assetId"),
    /**
     * 操作数量
     * 
     */
    NUM((short)5, "num"),
    /**
     * *    1,"发放"
     *      2,"扣减"
     *      3,"冻结"
     *      4, "冻结确认
     *      5,"冻结取消"
     *      6,"退款"
     * *
     */
    ORDER_TYPE((short)6, "orderType"),
    /**
     * 创建时间
     * 
     */
    CREATE_TIME((short)7, "createTime"),
    /**
     *      1, "订单成功"
     *      2, "订单已退款"
     *      3, "订单已冻结"
     *      4, "订单已确认"
     *      5, "订单已取消"
     * *
     */
    STATUS((short)8, "status"),
    /**
     * 退款原订单号
     * 
     */
    REF_ORDER_ID((short)9, "refOrderId");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ORDER_ID
          return ORDER_ID;
        case 2: // BUSINESS_ID
          return BUSINESS_ID;
        case 3: // KUGOU_ID
          return KUGOU_ID;
        case 4: // ASSET_ID
          return ASSET_ID;
        case 5: // NUM
          return NUM;
        case 6: // ORDER_TYPE
          return ORDER_TYPE;
        case 7: // CREATE_TIME
          return CREATE_TIME;
        case 8: // STATUS
          return STATUS;
        case 9: // REF_ORDER_ID
          return REF_ORDER_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __BUSINESSID_ISSET_ID = 0;
  private static final int __KUGOUID_ISSET_ID = 1;
  private static final int __ASSETID_ISSET_ID = 2;
  private static final int __ORDERTYPE_ISSET_ID = 3;
  private static final int __CREATETIME_ISSET_ID = 4;
  private static final int __STATUS_ISSET_ID = 5;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.REF_ORDER_ID};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ORDER_ID, new org.apache.thrift.meta_data.FieldMetaData("orderId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BUSINESS_ID, new org.apache.thrift.meta_data.FieldMetaData("businessId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ASSET_ID, new org.apache.thrift.meta_data.FieldMetaData("assetId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.NUM, new org.apache.thrift.meta_data.FieldMetaData("num", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ORDER_TYPE, new org.apache.thrift.meta_data.FieldMetaData("orderType", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.CREATE_TIME, new org.apache.thrift.meta_data.FieldMetaData("createTime", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.STATUS, new org.apache.thrift.meta_data.FieldMetaData("status", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.REF_ORDER_ID, new org.apache.thrift.meta_data.FieldMetaData("refOrderId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(OrderVO.class, metaDataMap);
  }

  public OrderVO() {
  }

  public OrderVO(
    String orderId,
    int businessId,
    long kugouId,
    long assetId,
    String num,
    int orderType,
    long createTime,
    int status)
  {
    this();
    this.orderId = orderId;
    this.businessId = businessId;
    setBusinessIdIsSet(true);
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    this.assetId = assetId;
    setAssetIdIsSet(true);
    this.num = num;
    this.orderType = orderType;
    setOrderTypeIsSet(true);
    this.createTime = createTime;
    setCreateTimeIsSet(true);
    this.status = status;
    setStatusIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public OrderVO(OrderVO other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetOrderId()) {
      this.orderId = other.orderId;
    }
    this.businessId = other.businessId;
    this.kugouId = other.kugouId;
    this.assetId = other.assetId;
    if (other.isSetNum()) {
      this.num = other.num;
    }
    this.orderType = other.orderType;
    this.createTime = other.createTime;
    this.status = other.status;
    if (other.isSetRefOrderId()) {
      this.refOrderId = other.refOrderId;
    }
  }

  public OrderVO deepCopy() {
    return new OrderVO(this);
  }

  @Override
  public void clear() {
    this.orderId = null;
    setBusinessIdIsSet(false);
    this.businessId = 0;
    setKugouIdIsSet(false);
    this.kugouId = 0;
    setAssetIdIsSet(false);
    this.assetId = 0;
    this.num = null;
    setOrderTypeIsSet(false);
    this.orderType = 0;
    setCreateTimeIsSet(false);
    this.createTime = 0;
    setStatusIsSet(false);
    this.status = 0;
    this.refOrderId = null;
  }

  public String getOrderId() {
    return this.orderId;
  }

  public OrderVO setOrderId(String orderId) {
    this.orderId = orderId;
    return this;
  }

  public void unsetOrderId() {
    this.orderId = null;
  }

  /** Returns true if field orderId is set (has been assigned a value) and false otherwise */
  public boolean isSetOrderId() {
    return this.orderId != null;
  }

  public void setOrderIdIsSet(boolean value) {
    if (!value) {
      this.orderId = null;
    }
  }

  public int getBusinessId() {
    return this.businessId;
  }

  public OrderVO setBusinessId(int businessId) {
    this.businessId = businessId;
    setBusinessIdIsSet(true);
    return this;
  }

  public void unsetBusinessId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __BUSINESSID_ISSET_ID);
  }

  /** Returns true if field businessId is set (has been assigned a value) and false otherwise */
  public boolean isSetBusinessId() {
    return EncodingUtils.testBit(__isset_bitfield, __BUSINESSID_ISSET_ID);
  }

  public void setBusinessIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __BUSINESSID_ISSET_ID, value);
  }

  /**
   * kugouId
   * 
   */
  public long getKugouId() {
    return this.kugouId;
  }

  /**
   * kugouId
   * 
   */
  public OrderVO setKugouId(long kugouId) {
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    return this;
  }

  public void unsetKugouId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
  public boolean isSetKugouId() {
    return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  public void setKugouIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
  }

  /**
   * 礼物ID
   * 
   */
  public long getAssetId() {
    return this.assetId;
  }

  /**
   * 礼物ID
   * 
   */
  public OrderVO setAssetId(long assetId) {
    this.assetId = assetId;
    setAssetIdIsSet(true);
    return this;
  }

  public void unsetAssetId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ASSETID_ISSET_ID);
  }

  /** Returns true if field assetId is set (has been assigned a value) and false otherwise */
  public boolean isSetAssetId() {
    return EncodingUtils.testBit(__isset_bitfield, __ASSETID_ISSET_ID);
  }

  public void setAssetIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ASSETID_ISSET_ID, value);
  }

  /**
   * 操作数量
   * 
   */
  public String getNum() {
    return this.num;
  }

  /**
   * 操作数量
   * 
   */
  public OrderVO setNum(String num) {
    this.num = num;
    return this;
  }

  public void unsetNum() {
    this.num = null;
  }

  /** Returns true if field num is set (has been assigned a value) and false otherwise */
  public boolean isSetNum() {
    return this.num != null;
  }

  public void setNumIsSet(boolean value) {
    if (!value) {
      this.num = null;
    }
  }

  /**
   * *    1,"发放"
   *      2,"扣减"
   *      3,"冻结"
   *      4, "冻结确认
   *      5,"冻结取消"
   *      6,"退款"
   * *
   */
  public int getOrderType() {
    return this.orderType;
  }

  /**
   * *    1,"发放"
   *      2,"扣减"
   *      3,"冻结"
   *      4, "冻结确认
   *      5,"冻结取消"
   *      6,"退款"
   * *
   */
  public OrderVO setOrderType(int orderType) {
    this.orderType = orderType;
    setOrderTypeIsSet(true);
    return this;
  }

  public void unsetOrderType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ORDERTYPE_ISSET_ID);
  }

  /** Returns true if field orderType is set (has been assigned a value) and false otherwise */
  public boolean isSetOrderType() {
    return EncodingUtils.testBit(__isset_bitfield, __ORDERTYPE_ISSET_ID);
  }

  public void setOrderTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ORDERTYPE_ISSET_ID, value);
  }

  /**
   * 创建时间
   * 
   */
  public long getCreateTime() {
    return this.createTime;
  }

  /**
   * 创建时间
   * 
   */
  public OrderVO setCreateTime(long createTime) {
    this.createTime = createTime;
    setCreateTimeIsSet(true);
    return this;
  }

  public void unsetCreateTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CREATETIME_ISSET_ID);
  }

  /** Returns true if field createTime is set (has been assigned a value) and false otherwise */
  public boolean isSetCreateTime() {
    return EncodingUtils.testBit(__isset_bitfield, __CREATETIME_ISSET_ID);
  }

  public void setCreateTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CREATETIME_ISSET_ID, value);
  }

  /**
   *      1, "订单成功"
   *      2, "订单已退款"
   *      3, "订单已冻结"
   *      4, "订单已确认"
   *      5, "订单已取消"
   * *
   */
  public int getStatus() {
    return this.status;
  }

  /**
   *      1, "订单成功"
   *      2, "订单已退款"
   *      3, "订单已冻结"
   *      4, "订单已确认"
   *      5, "订单已取消"
   * *
   */
  public OrderVO setStatus(int status) {
    this.status = status;
    setStatusIsSet(true);
    return this;
  }

  public void unsetStatus() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  /** Returns true if field status is set (has been assigned a value) and false otherwise */
  public boolean isSetStatus() {
    return EncodingUtils.testBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  public void setStatusIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STATUS_ISSET_ID, value);
  }

  /**
   * 退款原订单号
   * 
   */
  public String getRefOrderId() {
    return this.refOrderId;
  }

  /**
   * 退款原订单号
   * 
   */
  public OrderVO setRefOrderId(String refOrderId) {
    this.refOrderId = refOrderId;
    return this;
  }

  public void unsetRefOrderId() {
    this.refOrderId = null;
  }

  /** Returns true if field refOrderId is set (has been assigned a value) and false otherwise */
  public boolean isSetRefOrderId() {
    return this.refOrderId != null;
  }

  public void setRefOrderIdIsSet(boolean value) {
    if (!value) {
      this.refOrderId = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ORDER_ID:
      if (value == null) {
        unsetOrderId();
      } else {
        setOrderId((String)value);
      }
      break;

    case BUSINESS_ID:
      if (value == null) {
        unsetBusinessId();
      } else {
        setBusinessId((Integer)value);
      }
      break;

    case KUGOU_ID:
      if (value == null) {
        unsetKugouId();
      } else {
        setKugouId((Long)value);
      }
      break;

    case ASSET_ID:
      if (value == null) {
        unsetAssetId();
      } else {
        setAssetId((Long)value);
      }
      break;

    case NUM:
      if (value == null) {
        unsetNum();
      } else {
        setNum((String)value);
      }
      break;

    case ORDER_TYPE:
      if (value == null) {
        unsetOrderType();
      } else {
        setOrderType((Integer)value);
      }
      break;

    case CREATE_TIME:
      if (value == null) {
        unsetCreateTime();
      } else {
        setCreateTime((Long)value);
      }
      break;

    case STATUS:
      if (value == null) {
        unsetStatus();
      } else {
        setStatus((Integer)value);
      }
      break;

    case REF_ORDER_ID:
      if (value == null) {
        unsetRefOrderId();
      } else {
        setRefOrderId((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ORDER_ID:
      return getOrderId();

    case BUSINESS_ID:
      return getBusinessId();

    case KUGOU_ID:
      return getKugouId();

    case ASSET_ID:
      return getAssetId();

    case NUM:
      return getNum();

    case ORDER_TYPE:
      return getOrderType();

    case CREATE_TIME:
      return getCreateTime();

    case STATUS:
      return getStatus();

    case REF_ORDER_ID:
      return getRefOrderId();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ORDER_ID:
      return isSetOrderId();
    case BUSINESS_ID:
      return isSetBusinessId();
    case KUGOU_ID:
      return isSetKugouId();
    case ASSET_ID:
      return isSetAssetId();
    case NUM:
      return isSetNum();
    case ORDER_TYPE:
      return isSetOrderType();
    case CREATE_TIME:
      return isSetCreateTime();
    case STATUS:
      return isSetStatus();
    case REF_ORDER_ID:
      return isSetRefOrderId();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof OrderVO)
      return this.equals((OrderVO)that);
    return false;
  }

  public boolean equals(OrderVO that) {
    if (that == null)
      return false;

    boolean this_present_orderId = true && this.isSetOrderId();
    boolean that_present_orderId = true && that.isSetOrderId();
    if (this_present_orderId || that_present_orderId) {
      if (!(this_present_orderId && that_present_orderId))
        return false;
      if (!this.orderId.equals(that.orderId))
        return false;
    }

    boolean this_present_businessId = true;
    boolean that_present_businessId = true;
    if (this_present_businessId || that_present_businessId) {
      if (!(this_present_businessId && that_present_businessId))
        return false;
      if (this.businessId != that.businessId)
        return false;
    }

    boolean this_present_kugouId = true;
    boolean that_present_kugouId = true;
    if (this_present_kugouId || that_present_kugouId) {
      if (!(this_present_kugouId && that_present_kugouId))
        return false;
      if (this.kugouId != that.kugouId)
        return false;
    }

    boolean this_present_assetId = true;
    boolean that_present_assetId = true;
    if (this_present_assetId || that_present_assetId) {
      if (!(this_present_assetId && that_present_assetId))
        return false;
      if (this.assetId != that.assetId)
        return false;
    }

    boolean this_present_num = true && this.isSetNum();
    boolean that_present_num = true && that.isSetNum();
    if (this_present_num || that_present_num) {
      if (!(this_present_num && that_present_num))
        return false;
      if (!this.num.equals(that.num))
        return false;
    }

    boolean this_present_orderType = true;
    boolean that_present_orderType = true;
    if (this_present_orderType || that_present_orderType) {
      if (!(this_present_orderType && that_present_orderType))
        return false;
      if (this.orderType != that.orderType)
        return false;
    }

    boolean this_present_createTime = true;
    boolean that_present_createTime = true;
    if (this_present_createTime || that_present_createTime) {
      if (!(this_present_createTime && that_present_createTime))
        return false;
      if (this.createTime != that.createTime)
        return false;
    }

    boolean this_present_status = true;
    boolean that_present_status = true;
    if (this_present_status || that_present_status) {
      if (!(this_present_status && that_present_status))
        return false;
      if (this.status != that.status)
        return false;
    }

    boolean this_present_refOrderId = true && this.isSetRefOrderId();
    boolean that_present_refOrderId = true && that.isSetRefOrderId();
    if (this_present_refOrderId || that_present_refOrderId) {
      if (!(this_present_refOrderId && that_present_refOrderId))
        return false;
      if (!this.refOrderId.equals(that.refOrderId))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_orderId = true && (isSetOrderId());
    list.add(present_orderId);
    if (present_orderId)
      list.add(orderId);

    boolean present_businessId = true;
    list.add(present_businessId);
    if (present_businessId)
      list.add(businessId);

    boolean present_kugouId = true;
    list.add(present_kugouId);
    if (present_kugouId)
      list.add(kugouId);

    boolean present_assetId = true;
    list.add(present_assetId);
    if (present_assetId)
      list.add(assetId);

    boolean present_num = true && (isSetNum());
    list.add(present_num);
    if (present_num)
      list.add(num);

    boolean present_orderType = true;
    list.add(present_orderType);
    if (present_orderType)
      list.add(orderType);

    boolean present_createTime = true;
    list.add(present_createTime);
    if (present_createTime)
      list.add(createTime);

    boolean present_status = true;
    list.add(present_status);
    if (present_status)
      list.add(status);

    boolean present_refOrderId = true && (isSetRefOrderId());
    list.add(present_refOrderId);
    if (present_refOrderId)
      list.add(refOrderId);

    return list.hashCode();
  }

  @Override
  public int compareTo(OrderVO other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetOrderId()).compareTo(other.isSetOrderId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOrderId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderId, other.orderId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBusinessId()).compareTo(other.isSetBusinessId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBusinessId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.businessId, other.businessId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKugouId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAssetId()).compareTo(other.isSetAssetId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAssetId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.assetId, other.assetId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetNum()).compareTo(other.isSetNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.num, other.num);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetOrderType()).compareTo(other.isSetOrderType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOrderType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderType, other.orderType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCreateTime()).compareTo(other.isSetCreateTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCreateTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.createTime, other.createTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetStatus()).compareTo(other.isSetStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.status, other.status);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRefOrderId()).compareTo(other.isSetRefOrderId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRefOrderId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.refOrderId, other.refOrderId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("OrderVO(");
    boolean first = true;

    sb.append("orderId:");
    if (this.orderId == null) {
      sb.append("null");
    } else {
      sb.append(this.orderId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("businessId:");
    sb.append(this.businessId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("kugouId:");
    sb.append(this.kugouId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("assetId:");
    sb.append(this.assetId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("num:");
    if (this.num == null) {
      sb.append("null");
    } else {
      sb.append(this.num);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("orderType:");
    sb.append(this.orderType);
    first = false;
    if (!first) sb.append(", ");
    sb.append("createTime:");
    sb.append(this.createTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("status:");
    sb.append(this.status);
    first = false;
    if (isSetRefOrderId()) {
      if (!first) sb.append(", ");
      sb.append("refOrderId:");
      if (this.refOrderId == null) {
        sb.append("null");
      } else {
        sb.append(this.refOrderId);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (orderId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderId' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'businessId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'assetId' because it's a primitive and you chose the non-beans generator.
    if (num == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'num' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'orderType' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'createTime' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'status' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class OrderVOStandardSchemeFactory implements SchemeFactory {
    public OrderVOStandardScheme getScheme() {
      return new OrderVOStandardScheme();
    }
  }

  private static class OrderVOStandardScheme extends StandardScheme<OrderVO> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, OrderVO struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ORDER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.orderId = iprot.readString();
              struct.setOrderIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // BUSINESS_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.businessId = iprot.readI32();
              struct.setBusinessIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // KUGOU_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.kugouId = iprot.readI64();
              struct.setKugouIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // ASSET_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.assetId = iprot.readI64();
              struct.setAssetIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.num = iprot.readString();
              struct.setNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // ORDER_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.orderType = iprot.readI32();
              struct.setOrderTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // CREATE_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.createTime = iprot.readI64();
              struct.setCreateTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.status = iprot.readI32();
              struct.setStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // REF_ORDER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.refOrderId = iprot.readString();
              struct.setRefOrderIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetBusinessId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'businessId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetKugouId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetAssetId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'assetId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetOrderType()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderType' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetCreateTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'createTime' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetStatus()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'status' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, OrderVO struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.orderId != null) {
        oprot.writeFieldBegin(ORDER_ID_FIELD_DESC);
        oprot.writeString(struct.orderId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(BUSINESS_ID_FIELD_DESC);
      oprot.writeI32(struct.businessId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
      oprot.writeI64(struct.kugouId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ASSET_ID_FIELD_DESC);
      oprot.writeI64(struct.assetId);
      oprot.writeFieldEnd();
      if (struct.num != null) {
        oprot.writeFieldBegin(NUM_FIELD_DESC);
        oprot.writeString(struct.num);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(ORDER_TYPE_FIELD_DESC);
      oprot.writeI32(struct.orderType);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(CREATE_TIME_FIELD_DESC);
      oprot.writeI64(struct.createTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(STATUS_FIELD_DESC);
      oprot.writeI32(struct.status);
      oprot.writeFieldEnd();
      if (struct.refOrderId != null) {
        if (struct.isSetRefOrderId()) {
          oprot.writeFieldBegin(REF_ORDER_ID_FIELD_DESC);
          oprot.writeString(struct.refOrderId);
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class OrderVOTupleSchemeFactory implements SchemeFactory {
    public OrderVOTupleScheme getScheme() {
      return new OrderVOTupleScheme();
    }
  }

  private static class OrderVOTupleScheme extends TupleScheme<OrderVO> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, OrderVO struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeString(struct.orderId);
      oprot.writeI32(struct.businessId);
      oprot.writeI64(struct.kugouId);
      oprot.writeI64(struct.assetId);
      oprot.writeString(struct.num);
      oprot.writeI32(struct.orderType);
      oprot.writeI64(struct.createTime);
      oprot.writeI32(struct.status);
      BitSet optionals = new BitSet();
      if (struct.isSetRefOrderId()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetRefOrderId()) {
        oprot.writeString(struct.refOrderId);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, OrderVO struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.orderId = iprot.readString();
      struct.setOrderIdIsSet(true);
      struct.businessId = iprot.readI32();
      struct.setBusinessIdIsSet(true);
      struct.kugouId = iprot.readI64();
      struct.setKugouIdIsSet(true);
      struct.assetId = iprot.readI64();
      struct.setAssetIdIsSet(true);
      struct.num = iprot.readString();
      struct.setNumIsSet(true);
      struct.orderType = iprot.readI32();
      struct.setOrderTypeIsSet(true);
      struct.createTime = iprot.readI64();
      struct.setCreateTimeIsSet(true);
      struct.status = iprot.readI32();
      struct.setStatusIsSet(true);
      BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        struct.refOrderId = iprot.readString();
        struct.setRefOrderIdIsSet(true);
      }
    }
  }

}

