/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.platform.asset.service.thrift;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-08-17")
public class AssetService {

  /**
   * 用户资产协议
   */
  public interface Iface {

    /**
     * 添加
     * 
     * 
     * @param request
     * @param commonParameter
     */
    public IncrementResponse increment(IncrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException;

    /**
     * 扣减
     * 
     * 
     * @param request
     * @param commonParameter
     */
    public DecrementResponse decrement(DecrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException;

    /**
     * 冻结
     * 
     * 
     * @param request
     * @param commonParameter
     */
    public FreezeResponse freeze(FreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException;

    /**
     * 冻结确认
     * 
     * 
     * @param request
     * @param commonParameter
     */
    public UnFreezeResponse freezeConfirm(UnFreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException;

    /**
     * 冻结取消
     * 
     * 
     * @param request
     * @param commonParameter
     */
    public UnFreezeResponse freezeCancel(UnFreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException;

    /**
     * 退款
     * 
     * 
     * @param request
     * @param commonParameter
     */
    public RefundResponse refund(RefundRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException;

    /**
     * 扣减余额接口
     * 
     * 
     * @param request
     * @param cientIp
     */
    public DecrementResponse decrementBySystem(DecrementRequest request, String cientIp) throws org.apache.thrift.TException;

    /**
     * 充值
     * 
     * 
     * @param request
     * @param commonParameter
     */
    public IncrementResponse recharge(IncrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException;

  }

  public interface AsyncIface {

    public void increment(IncrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void decrement(DecrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void freeze(FreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void freezeConfirm(UnFreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void freezeCancel(UnFreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void refund(RefundRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void decrementBySystem(DecrementRequest request, String cientIp, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void recharge(IncrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

  }

  public static class Client extends org.apache.thrift.TServiceClient implements Iface {
    public static class Factory implements org.apache.thrift.TServiceClientFactory<Client> {
      public Factory() {}
      public Client getClient(org.apache.thrift.protocol.TProtocol prot) {
        return new Client(prot);
      }
      public Client getClient(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
        return new Client(iprot, oprot);
      }
    }

    public Client(org.apache.thrift.protocol.TProtocol prot)
    {
      super(prot, prot);
    }

    public Client(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
      super(iprot, oprot);
    }

    public IncrementResponse increment(IncrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException
    {
      send_increment(request, commonParameter);
      return recv_increment();
    }

    public void send_increment(IncrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException
    {
      increment_args args = new increment_args();
      args.setRequest(request);
      args.setCommonParameter(commonParameter);
      sendBase("increment", args);
    }

    public IncrementResponse recv_increment() throws org.apache.thrift.TException
    {
      increment_result result = new increment_result();
      receiveBase(result, "increment");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "increment failed: unknown result");
    }

    public DecrementResponse decrement(DecrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException
    {
      send_decrement(request, commonParameter);
      return recv_decrement();
    }

    public void send_decrement(DecrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException
    {
      decrement_args args = new decrement_args();
      args.setRequest(request);
      args.setCommonParameter(commonParameter);
      sendBase("decrement", args);
    }

    public DecrementResponse recv_decrement() throws org.apache.thrift.TException
    {
      decrement_result result = new decrement_result();
      receiveBase(result, "decrement");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "decrement failed: unknown result");
    }

    public FreezeResponse freeze(FreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException
    {
      send_freeze(request, commonParameter);
      return recv_freeze();
    }

    public void send_freeze(FreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException
    {
      freeze_args args = new freeze_args();
      args.setRequest(request);
      args.setCommonParameter(commonParameter);
      sendBase("freeze", args);
    }

    public FreezeResponse recv_freeze() throws org.apache.thrift.TException
    {
      freeze_result result = new freeze_result();
      receiveBase(result, "freeze");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "freeze failed: unknown result");
    }

    public UnFreezeResponse freezeConfirm(UnFreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException
    {
      send_freezeConfirm(request, commonParameter);
      return recv_freezeConfirm();
    }

    public void send_freezeConfirm(UnFreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException
    {
      freezeConfirm_args args = new freezeConfirm_args();
      args.setRequest(request);
      args.setCommonParameter(commonParameter);
      sendBase("freezeConfirm", args);
    }

    public UnFreezeResponse recv_freezeConfirm() throws org.apache.thrift.TException
    {
      freezeConfirm_result result = new freezeConfirm_result();
      receiveBase(result, "freezeConfirm");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "freezeConfirm failed: unknown result");
    }

    public UnFreezeResponse freezeCancel(UnFreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException
    {
      send_freezeCancel(request, commonParameter);
      return recv_freezeCancel();
    }

    public void send_freezeCancel(UnFreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException
    {
      freezeCancel_args args = new freezeCancel_args();
      args.setRequest(request);
      args.setCommonParameter(commonParameter);
      sendBase("freezeCancel", args);
    }

    public UnFreezeResponse recv_freezeCancel() throws org.apache.thrift.TException
    {
      freezeCancel_result result = new freezeCancel_result();
      receiveBase(result, "freezeCancel");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "freezeCancel failed: unknown result");
    }

    public RefundResponse refund(RefundRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException
    {
      send_refund(request, commonParameter);
      return recv_refund();
    }

    public void send_refund(RefundRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException
    {
      refund_args args = new refund_args();
      args.setRequest(request);
      args.setCommonParameter(commonParameter);
      sendBase("refund", args);
    }

    public RefundResponse recv_refund() throws org.apache.thrift.TException
    {
      refund_result result = new refund_result();
      receiveBase(result, "refund");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "refund failed: unknown result");
    }

    public DecrementResponse decrementBySystem(DecrementRequest request, String cientIp) throws org.apache.thrift.TException
    {
      send_decrementBySystem(request, cientIp);
      return recv_decrementBySystem();
    }

    public void send_decrementBySystem(DecrementRequest request, String cientIp) throws org.apache.thrift.TException
    {
      decrementBySystem_args args = new decrementBySystem_args();
      args.setRequest(request);
      args.setCientIp(cientIp);
      sendBase("decrementBySystem", args);
    }

    public DecrementResponse recv_decrementBySystem() throws org.apache.thrift.TException
    {
      decrementBySystem_result result = new decrementBySystem_result();
      receiveBase(result, "decrementBySystem");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "decrementBySystem failed: unknown result");
    }

    public IncrementResponse recharge(IncrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException
    {
      send_recharge(request, commonParameter);
      return recv_recharge();
    }

    public void send_recharge(IncrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) throws org.apache.thrift.TException
    {
      recharge_args args = new recharge_args();
      args.setRequest(request);
      args.setCommonParameter(commonParameter);
      sendBase("recharge", args);
    }

    public IncrementResponse recv_recharge() throws org.apache.thrift.TException
    {
      recharge_result result = new recharge_result();
      receiveBase(result, "recharge");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "recharge failed: unknown result");
    }

  }
  public static class AsyncClient extends org.apache.thrift.async.TAsyncClient implements AsyncIface {
    public static class Factory implements org.apache.thrift.async.TAsyncClientFactory<AsyncClient> {
      private org.apache.thrift.async.TAsyncClientManager clientManager;
      private org.apache.thrift.protocol.TProtocolFactory protocolFactory;
      public Factory(org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.protocol.TProtocolFactory protocolFactory) {
        this.clientManager = clientManager;
        this.protocolFactory = protocolFactory;
      }
      public AsyncClient getAsyncClient(org.apache.thrift.transport.TNonblockingTransport transport) {
        return new AsyncClient(protocolFactory, clientManager, transport);
      }
    }

    public AsyncClient(org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.transport.TNonblockingTransport transport) {
      super(protocolFactory, clientManager, transport);
    }

    public void increment(IncrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      increment_call method_call = new increment_call(request, commonParameter, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class increment_call extends org.apache.thrift.async.TAsyncMethodCall {
      private IncrementRequest request;
      private com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter;
      public increment_call(IncrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.request = request;
        this.commonParameter = commonParameter;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("increment", org.apache.thrift.protocol.TMessageType.CALL, 0));
        increment_args args = new increment_args();
        args.setRequest(request);
        args.setCommonParameter(commonParameter);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public IncrementResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_increment();
      }
    }

    public void decrement(DecrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      decrement_call method_call = new decrement_call(request, commonParameter, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class decrement_call extends org.apache.thrift.async.TAsyncMethodCall {
      private DecrementRequest request;
      private com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter;
      public decrement_call(DecrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.request = request;
        this.commonParameter = commonParameter;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("decrement", org.apache.thrift.protocol.TMessageType.CALL, 0));
        decrement_args args = new decrement_args();
        args.setRequest(request);
        args.setCommonParameter(commonParameter);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public DecrementResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_decrement();
      }
    }

    public void freeze(FreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      freeze_call method_call = new freeze_call(request, commonParameter, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class freeze_call extends org.apache.thrift.async.TAsyncMethodCall {
      private FreezeRequest request;
      private com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter;
      public freeze_call(FreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.request = request;
        this.commonParameter = commonParameter;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("freeze", org.apache.thrift.protocol.TMessageType.CALL, 0));
        freeze_args args = new freeze_args();
        args.setRequest(request);
        args.setCommonParameter(commonParameter);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public FreezeResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_freeze();
      }
    }

    public void freezeConfirm(UnFreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      freezeConfirm_call method_call = new freezeConfirm_call(request, commonParameter, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class freezeConfirm_call extends org.apache.thrift.async.TAsyncMethodCall {
      private UnFreezeRequest request;
      private com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter;
      public freezeConfirm_call(UnFreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.request = request;
        this.commonParameter = commonParameter;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("freezeConfirm", org.apache.thrift.protocol.TMessageType.CALL, 0));
        freezeConfirm_args args = new freezeConfirm_args();
        args.setRequest(request);
        args.setCommonParameter(commonParameter);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public UnFreezeResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_freezeConfirm();
      }
    }

    public void freezeCancel(UnFreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      freezeCancel_call method_call = new freezeCancel_call(request, commonParameter, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class freezeCancel_call extends org.apache.thrift.async.TAsyncMethodCall {
      private UnFreezeRequest request;
      private com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter;
      public freezeCancel_call(UnFreezeRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.request = request;
        this.commonParameter = commonParameter;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("freezeCancel", org.apache.thrift.protocol.TMessageType.CALL, 0));
        freezeCancel_args args = new freezeCancel_args();
        args.setRequest(request);
        args.setCommonParameter(commonParameter);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public UnFreezeResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_freezeCancel();
      }
    }

    public void refund(RefundRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      refund_call method_call = new refund_call(request, commonParameter, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class refund_call extends org.apache.thrift.async.TAsyncMethodCall {
      private RefundRequest request;
      private com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter;
      public refund_call(RefundRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.request = request;
        this.commonParameter = commonParameter;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("refund", org.apache.thrift.protocol.TMessageType.CALL, 0));
        refund_args args = new refund_args();
        args.setRequest(request);
        args.setCommonParameter(commonParameter);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public RefundResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_refund();
      }
    }

    public void decrementBySystem(DecrementRequest request, String cientIp, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      decrementBySystem_call method_call = new decrementBySystem_call(request, cientIp, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class decrementBySystem_call extends org.apache.thrift.async.TAsyncMethodCall {
      private DecrementRequest request;
      private String cientIp;
      public decrementBySystem_call(DecrementRequest request, String cientIp, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.request = request;
        this.cientIp = cientIp;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("decrementBySystem", org.apache.thrift.protocol.TMessageType.CALL, 0));
        decrementBySystem_args args = new decrementBySystem_args();
        args.setRequest(request);
        args.setCientIp(cientIp);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public DecrementResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_decrementBySystem();
      }
    }

    public void recharge(IncrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      recharge_call method_call = new recharge_call(request, commonParameter, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class recharge_call extends org.apache.thrift.async.TAsyncMethodCall {
      private IncrementRequest request;
      private com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter;
      public recharge_call(IncrementRequest request, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.request = request;
        this.commonParameter = commonParameter;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("recharge", org.apache.thrift.protocol.TMessageType.CALL, 0));
        recharge_args args = new recharge_args();
        args.setRequest(request);
        args.setCommonParameter(commonParameter);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public IncrementResponse getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_recharge();
      }
    }

  }

  public static class Processor<I extends Iface> extends org.apache.thrift.TBaseProcessor<I> implements org.apache.thrift.TProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());
    public Processor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>>()));
    }

    protected Processor(I iface, Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends Iface> Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> getProcessMap(Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      processMap.put("increment", new increment());
      processMap.put("decrement", new decrement());
      processMap.put("freeze", new freeze());
      processMap.put("freezeConfirm", new freezeConfirm());
      processMap.put("freezeCancel", new freezeCancel());
      processMap.put("refund", new refund());
      processMap.put("decrementBySystem", new decrementBySystem());
      processMap.put("recharge", new recharge());
      return processMap;
    }

    public static class increment<I extends Iface> extends org.apache.thrift.ProcessFunction<I, increment_args> {
      public increment() {
        super("increment");
      }

      public increment_args getEmptyArgsInstance() {
        return new increment_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public increment_result getResult(I iface, increment_args args) throws org.apache.thrift.TException {
        increment_result result = new increment_result();
        result.success = iface.increment(args.request, args.commonParameter);
        return result;
      }
    }

    public static class decrement<I extends Iface> extends org.apache.thrift.ProcessFunction<I, decrement_args> {
      public decrement() {
        super("decrement");
      }

      public decrement_args getEmptyArgsInstance() {
        return new decrement_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public decrement_result getResult(I iface, decrement_args args) throws org.apache.thrift.TException {
        decrement_result result = new decrement_result();
        result.success = iface.decrement(args.request, args.commonParameter);
        return result;
      }
    }

    public static class freeze<I extends Iface> extends org.apache.thrift.ProcessFunction<I, freeze_args> {
      public freeze() {
        super("freeze");
      }

      public freeze_args getEmptyArgsInstance() {
        return new freeze_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public freeze_result getResult(I iface, freeze_args args) throws org.apache.thrift.TException {
        freeze_result result = new freeze_result();
        result.success = iface.freeze(args.request, args.commonParameter);
        return result;
      }
    }

    public static class freezeConfirm<I extends Iface> extends org.apache.thrift.ProcessFunction<I, freezeConfirm_args> {
      public freezeConfirm() {
        super("freezeConfirm");
      }

      public freezeConfirm_args getEmptyArgsInstance() {
        return new freezeConfirm_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public freezeConfirm_result getResult(I iface, freezeConfirm_args args) throws org.apache.thrift.TException {
        freezeConfirm_result result = new freezeConfirm_result();
        result.success = iface.freezeConfirm(args.request, args.commonParameter);
        return result;
      }
    }

    public static class freezeCancel<I extends Iface> extends org.apache.thrift.ProcessFunction<I, freezeCancel_args> {
      public freezeCancel() {
        super("freezeCancel");
      }

      public freezeCancel_args getEmptyArgsInstance() {
        return new freezeCancel_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public freezeCancel_result getResult(I iface, freezeCancel_args args) throws org.apache.thrift.TException {
        freezeCancel_result result = new freezeCancel_result();
        result.success = iface.freezeCancel(args.request, args.commonParameter);
        return result;
      }
    }

    public static class refund<I extends Iface> extends org.apache.thrift.ProcessFunction<I, refund_args> {
      public refund() {
        super("refund");
      }

      public refund_args getEmptyArgsInstance() {
        return new refund_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public refund_result getResult(I iface, refund_args args) throws org.apache.thrift.TException {
        refund_result result = new refund_result();
        result.success = iface.refund(args.request, args.commonParameter);
        return result;
      }
    }

    public static class decrementBySystem<I extends Iface> extends org.apache.thrift.ProcessFunction<I, decrementBySystem_args> {
      public decrementBySystem() {
        super("decrementBySystem");
      }

      public decrementBySystem_args getEmptyArgsInstance() {
        return new decrementBySystem_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public decrementBySystem_result getResult(I iface, decrementBySystem_args args) throws org.apache.thrift.TException {
        decrementBySystem_result result = new decrementBySystem_result();
        result.success = iface.decrementBySystem(args.request, args.cientIp);
        return result;
      }
    }

    public static class recharge<I extends Iface> extends org.apache.thrift.ProcessFunction<I, recharge_args> {
      public recharge() {
        super("recharge");
      }

      public recharge_args getEmptyArgsInstance() {
        return new recharge_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public recharge_result getResult(I iface, recharge_args args) throws org.apache.thrift.TException {
        recharge_result result = new recharge_result();
        result.success = iface.recharge(args.request, args.commonParameter);
        return result;
      }
    }

  }

  public static class AsyncProcessor<I extends AsyncIface> extends org.apache.thrift.TBaseAsyncProcessor<I> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());
    public AsyncProcessor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.AsyncProcessFunction<I, ? extends org.apache.thrift.TBase, ?>>()));
    }

    protected AsyncProcessor(I iface, Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends AsyncIface> Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase,?>> getProcessMap(Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      processMap.put("increment", new increment());
      processMap.put("decrement", new decrement());
      processMap.put("freeze", new freeze());
      processMap.put("freezeConfirm", new freezeConfirm());
      processMap.put("freezeCancel", new freezeCancel());
      processMap.put("refund", new refund());
      processMap.put("decrementBySystem", new decrementBySystem());
      processMap.put("recharge", new recharge());
      return processMap;
    }

    public static class increment<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, increment_args, IncrementResponse> {
      public increment() {
        super("increment");
      }

      public increment_args getEmptyArgsInstance() {
        return new increment_args();
      }

      public AsyncMethodCallback<IncrementResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<IncrementResponse>() { 
          public void onComplete(IncrementResponse o) {
            increment_result result = new increment_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            increment_result result = new increment_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, increment_args args, org.apache.thrift.async.AsyncMethodCallback<IncrementResponse> resultHandler) throws TException {
        iface.increment(args.request, args.commonParameter,resultHandler);
      }
    }

    public static class decrement<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, decrement_args, DecrementResponse> {
      public decrement() {
        super("decrement");
      }

      public decrement_args getEmptyArgsInstance() {
        return new decrement_args();
      }

      public AsyncMethodCallback<DecrementResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<DecrementResponse>() { 
          public void onComplete(DecrementResponse o) {
            decrement_result result = new decrement_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            decrement_result result = new decrement_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, decrement_args args, org.apache.thrift.async.AsyncMethodCallback<DecrementResponse> resultHandler) throws TException {
        iface.decrement(args.request, args.commonParameter,resultHandler);
      }
    }

    public static class freeze<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, freeze_args, FreezeResponse> {
      public freeze() {
        super("freeze");
      }

      public freeze_args getEmptyArgsInstance() {
        return new freeze_args();
      }

      public AsyncMethodCallback<FreezeResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<FreezeResponse>() { 
          public void onComplete(FreezeResponse o) {
            freeze_result result = new freeze_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            freeze_result result = new freeze_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, freeze_args args, org.apache.thrift.async.AsyncMethodCallback<FreezeResponse> resultHandler) throws TException {
        iface.freeze(args.request, args.commonParameter,resultHandler);
      }
    }

    public static class freezeConfirm<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, freezeConfirm_args, UnFreezeResponse> {
      public freezeConfirm() {
        super("freezeConfirm");
      }

      public freezeConfirm_args getEmptyArgsInstance() {
        return new freezeConfirm_args();
      }

      public AsyncMethodCallback<UnFreezeResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<UnFreezeResponse>() { 
          public void onComplete(UnFreezeResponse o) {
            freezeConfirm_result result = new freezeConfirm_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            freezeConfirm_result result = new freezeConfirm_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, freezeConfirm_args args, org.apache.thrift.async.AsyncMethodCallback<UnFreezeResponse> resultHandler) throws TException {
        iface.freezeConfirm(args.request, args.commonParameter,resultHandler);
      }
    }

    public static class freezeCancel<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, freezeCancel_args, UnFreezeResponse> {
      public freezeCancel() {
        super("freezeCancel");
      }

      public freezeCancel_args getEmptyArgsInstance() {
        return new freezeCancel_args();
      }

      public AsyncMethodCallback<UnFreezeResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<UnFreezeResponse>() { 
          public void onComplete(UnFreezeResponse o) {
            freezeCancel_result result = new freezeCancel_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            freezeCancel_result result = new freezeCancel_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, freezeCancel_args args, org.apache.thrift.async.AsyncMethodCallback<UnFreezeResponse> resultHandler) throws TException {
        iface.freezeCancel(args.request, args.commonParameter,resultHandler);
      }
    }

    public static class refund<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, refund_args, RefundResponse> {
      public refund() {
        super("refund");
      }

      public refund_args getEmptyArgsInstance() {
        return new refund_args();
      }

      public AsyncMethodCallback<RefundResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<RefundResponse>() { 
          public void onComplete(RefundResponse o) {
            refund_result result = new refund_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            refund_result result = new refund_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, refund_args args, org.apache.thrift.async.AsyncMethodCallback<RefundResponse> resultHandler) throws TException {
        iface.refund(args.request, args.commonParameter,resultHandler);
      }
    }

    public static class decrementBySystem<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, decrementBySystem_args, DecrementResponse> {
      public decrementBySystem() {
        super("decrementBySystem");
      }

      public decrementBySystem_args getEmptyArgsInstance() {
        return new decrementBySystem_args();
      }

      public AsyncMethodCallback<DecrementResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<DecrementResponse>() { 
          public void onComplete(DecrementResponse o) {
            decrementBySystem_result result = new decrementBySystem_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            decrementBySystem_result result = new decrementBySystem_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, decrementBySystem_args args, org.apache.thrift.async.AsyncMethodCallback<DecrementResponse> resultHandler) throws TException {
        iface.decrementBySystem(args.request, args.cientIp,resultHandler);
      }
    }

    public static class recharge<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, recharge_args, IncrementResponse> {
      public recharge() {
        super("recharge");
      }

      public recharge_args getEmptyArgsInstance() {
        return new recharge_args();
      }

      public AsyncMethodCallback<IncrementResponse> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<IncrementResponse>() { 
          public void onComplete(IncrementResponse o) {
            recharge_result result = new recharge_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            recharge_result result = new recharge_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, recharge_args args, org.apache.thrift.async.AsyncMethodCallback<IncrementResponse> resultHandler) throws TException {
        iface.recharge(args.request, args.commonParameter,resultHandler);
      }
    }

  }

  public static class increment_args implements org.apache.thrift.TBase<increment_args, increment_args._Fields>, java.io.Serializable, Cloneable, Comparable<increment_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("increment_args");

    private static final org.apache.thrift.protocol.TField REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("request", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField COMMON_PARAMETER_FIELD_DESC = new org.apache.thrift.protocol.TField("commonParameter", org.apache.thrift.protocol.TType.STRUCT, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new increment_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new increment_argsTupleSchemeFactory());
    }

    public IncrementRequest request; // required
    public com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      REQUEST((short)1, "request"),
      COMMON_PARAMETER((short)2, "commonParameter");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // REQUEST
            return REQUEST;
          case 2: // COMMON_PARAMETER
            return COMMON_PARAMETER;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.REQUEST, new org.apache.thrift.meta_data.FieldMetaData("request", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, IncrementRequest.class)));
      tmpMap.put(_Fields.COMMON_PARAMETER, new org.apache.thrift.meta_data.FieldMetaData("commonParameter", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(increment_args.class, metaDataMap);
    }

    public increment_args() {
    }

    public increment_args(
      IncrementRequest request,
      com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter)
    {
      this();
      this.request = request;
      this.commonParameter = commonParameter;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public increment_args(increment_args other) {
      if (other.isSetRequest()) {
        this.request = new IncrementRequest(other.request);
      }
      if (other.isSetCommonParameter()) {
        this.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter(other.commonParameter);
      }
    }

    public increment_args deepCopy() {
      return new increment_args(this);
    }

    @Override
    public void clear() {
      this.request = null;
      this.commonParameter = null;
    }

    public IncrementRequest getRequest() {
      return this.request;
    }

    public increment_args setRequest(IncrementRequest request) {
      this.request = request;
      return this;
    }

    public void unsetRequest() {
      this.request = null;
    }

    /** Returns true if field request is set (has been assigned a value) and false otherwise */
    public boolean isSetRequest() {
      return this.request != null;
    }

    public void setRequestIsSet(boolean value) {
      if (!value) {
        this.request = null;
      }
    }

    public com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter getCommonParameter() {
      return this.commonParameter;
    }

    public increment_args setCommonParameter(com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) {
      this.commonParameter = commonParameter;
      return this;
    }

    public void unsetCommonParameter() {
      this.commonParameter = null;
    }

    /** Returns true if field commonParameter is set (has been assigned a value) and false otherwise */
    public boolean isSetCommonParameter() {
      return this.commonParameter != null;
    }

    public void setCommonParameterIsSet(boolean value) {
      if (!value) {
        this.commonParameter = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case REQUEST:
        if (value == null) {
          unsetRequest();
        } else {
          setRequest((IncrementRequest)value);
        }
        break;

      case COMMON_PARAMETER:
        if (value == null) {
          unsetCommonParameter();
        } else {
          setCommonParameter((com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case REQUEST:
        return getRequest();

      case COMMON_PARAMETER:
        return getCommonParameter();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case REQUEST:
        return isSetRequest();
      case COMMON_PARAMETER:
        return isSetCommonParameter();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof increment_args)
        return this.equals((increment_args)that);
      return false;
    }

    public boolean equals(increment_args that) {
      if (that == null)
        return false;

      boolean this_present_request = true && this.isSetRequest();
      boolean that_present_request = true && that.isSetRequest();
      if (this_present_request || that_present_request) {
        if (!(this_present_request && that_present_request))
          return false;
        if (!this.request.equals(that.request))
          return false;
      }

      boolean this_present_commonParameter = true && this.isSetCommonParameter();
      boolean that_present_commonParameter = true && that.isSetCommonParameter();
      if (this_present_commonParameter || that_present_commonParameter) {
        if (!(this_present_commonParameter && that_present_commonParameter))
          return false;
        if (!this.commonParameter.equals(that.commonParameter))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_request = true && (isSetRequest());
      list.add(present_request);
      if (present_request)
        list.add(request);

      boolean present_commonParameter = true && (isSetCommonParameter());
      list.add(present_commonParameter);
      if (present_commonParameter)
        list.add(commonParameter);

      return list.hashCode();
    }

    @Override
    public int compareTo(increment_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.request, other.request);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetCommonParameter()).compareTo(other.isSetCommonParameter());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetCommonParameter()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.commonParameter, other.commonParameter);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("increment_args(");
      boolean first = true;

      sb.append("request:");
      if (this.request == null) {
        sb.append("null");
      } else {
        sb.append(this.request);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("commonParameter:");
      if (this.commonParameter == null) {
        sb.append("null");
      } else {
        sb.append(this.commonParameter);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (request == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'request' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (request != null) {
        request.validate();
      }
      if (commonParameter != null) {
        commonParameter.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class increment_argsStandardSchemeFactory implements SchemeFactory {
      public increment_argsStandardScheme getScheme() {
        return new increment_argsStandardScheme();
      }
    }

    private static class increment_argsStandardScheme extends StandardScheme<increment_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, increment_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.request = new IncrementRequest();
                struct.request.read(iprot);
                struct.setRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // COMMON_PARAMETER
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter();
                struct.commonParameter.read(iprot);
                struct.setCommonParameterIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, increment_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.request != null) {
          oprot.writeFieldBegin(REQUEST_FIELD_DESC);
          struct.request.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.commonParameter != null) {
          oprot.writeFieldBegin(COMMON_PARAMETER_FIELD_DESC);
          struct.commonParameter.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class increment_argsTupleSchemeFactory implements SchemeFactory {
      public increment_argsTupleScheme getScheme() {
        return new increment_argsTupleScheme();
      }
    }

    private static class increment_argsTupleScheme extends TupleScheme<increment_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, increment_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.request.write(oprot);
        BitSet optionals = new BitSet();
        if (struct.isSetCommonParameter()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetCommonParameter()) {
          struct.commonParameter.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, increment_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.request = new IncrementRequest();
        struct.request.read(iprot);
        struct.setRequestIsSet(true);
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter();
          struct.commonParameter.read(iprot);
          struct.setCommonParameterIsSet(true);
        }
      }
    }

  }

  public static class increment_result implements org.apache.thrift.TBase<increment_result, increment_result._Fields>, java.io.Serializable, Cloneable, Comparable<increment_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("increment_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new increment_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new increment_resultTupleSchemeFactory());
    }

    public IncrementResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, IncrementResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(increment_result.class, metaDataMap);
    }

    public increment_result() {
    }

    public increment_result(
      IncrementResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public increment_result(increment_result other) {
      if (other.isSetSuccess()) {
        this.success = new IncrementResponse(other.success);
      }
    }

    public increment_result deepCopy() {
      return new increment_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public IncrementResponse getSuccess() {
      return this.success;
    }

    public increment_result setSuccess(IncrementResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((IncrementResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof increment_result)
        return this.equals((increment_result)that);
      return false;
    }

    public boolean equals(increment_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(increment_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("increment_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class increment_resultStandardSchemeFactory implements SchemeFactory {
      public increment_resultStandardScheme getScheme() {
        return new increment_resultStandardScheme();
      }
    }

    private static class increment_resultStandardScheme extends StandardScheme<increment_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, increment_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new IncrementResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, increment_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class increment_resultTupleSchemeFactory implements SchemeFactory {
      public increment_resultTupleScheme getScheme() {
        return new increment_resultTupleScheme();
      }
    }

    private static class increment_resultTupleScheme extends TupleScheme<increment_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, increment_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, increment_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new IncrementResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class decrement_args implements org.apache.thrift.TBase<decrement_args, decrement_args._Fields>, java.io.Serializable, Cloneable, Comparable<decrement_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("decrement_args");

    private static final org.apache.thrift.protocol.TField REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("request", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField COMMON_PARAMETER_FIELD_DESC = new org.apache.thrift.protocol.TField("commonParameter", org.apache.thrift.protocol.TType.STRUCT, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new decrement_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new decrement_argsTupleSchemeFactory());
    }

    public DecrementRequest request; // required
    public com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      REQUEST((short)1, "request"),
      COMMON_PARAMETER((short)2, "commonParameter");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // REQUEST
            return REQUEST;
          case 2: // COMMON_PARAMETER
            return COMMON_PARAMETER;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.REQUEST, new org.apache.thrift.meta_data.FieldMetaData("request", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, DecrementRequest.class)));
      tmpMap.put(_Fields.COMMON_PARAMETER, new org.apache.thrift.meta_data.FieldMetaData("commonParameter", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(decrement_args.class, metaDataMap);
    }

    public decrement_args() {
    }

    public decrement_args(
      DecrementRequest request,
      com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter)
    {
      this();
      this.request = request;
      this.commonParameter = commonParameter;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public decrement_args(decrement_args other) {
      if (other.isSetRequest()) {
        this.request = new DecrementRequest(other.request);
      }
      if (other.isSetCommonParameter()) {
        this.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter(other.commonParameter);
      }
    }

    public decrement_args deepCopy() {
      return new decrement_args(this);
    }

    @Override
    public void clear() {
      this.request = null;
      this.commonParameter = null;
    }

    public DecrementRequest getRequest() {
      return this.request;
    }

    public decrement_args setRequest(DecrementRequest request) {
      this.request = request;
      return this;
    }

    public void unsetRequest() {
      this.request = null;
    }

    /** Returns true if field request is set (has been assigned a value) and false otherwise */
    public boolean isSetRequest() {
      return this.request != null;
    }

    public void setRequestIsSet(boolean value) {
      if (!value) {
        this.request = null;
      }
    }

    public com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter getCommonParameter() {
      return this.commonParameter;
    }

    public decrement_args setCommonParameter(com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) {
      this.commonParameter = commonParameter;
      return this;
    }

    public void unsetCommonParameter() {
      this.commonParameter = null;
    }

    /** Returns true if field commonParameter is set (has been assigned a value) and false otherwise */
    public boolean isSetCommonParameter() {
      return this.commonParameter != null;
    }

    public void setCommonParameterIsSet(boolean value) {
      if (!value) {
        this.commonParameter = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case REQUEST:
        if (value == null) {
          unsetRequest();
        } else {
          setRequest((DecrementRequest)value);
        }
        break;

      case COMMON_PARAMETER:
        if (value == null) {
          unsetCommonParameter();
        } else {
          setCommonParameter((com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case REQUEST:
        return getRequest();

      case COMMON_PARAMETER:
        return getCommonParameter();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case REQUEST:
        return isSetRequest();
      case COMMON_PARAMETER:
        return isSetCommonParameter();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof decrement_args)
        return this.equals((decrement_args)that);
      return false;
    }

    public boolean equals(decrement_args that) {
      if (that == null)
        return false;

      boolean this_present_request = true && this.isSetRequest();
      boolean that_present_request = true && that.isSetRequest();
      if (this_present_request || that_present_request) {
        if (!(this_present_request && that_present_request))
          return false;
        if (!this.request.equals(that.request))
          return false;
      }

      boolean this_present_commonParameter = true && this.isSetCommonParameter();
      boolean that_present_commonParameter = true && that.isSetCommonParameter();
      if (this_present_commonParameter || that_present_commonParameter) {
        if (!(this_present_commonParameter && that_present_commonParameter))
          return false;
        if (!this.commonParameter.equals(that.commonParameter))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_request = true && (isSetRequest());
      list.add(present_request);
      if (present_request)
        list.add(request);

      boolean present_commonParameter = true && (isSetCommonParameter());
      list.add(present_commonParameter);
      if (present_commonParameter)
        list.add(commonParameter);

      return list.hashCode();
    }

    @Override
    public int compareTo(decrement_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.request, other.request);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetCommonParameter()).compareTo(other.isSetCommonParameter());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetCommonParameter()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.commonParameter, other.commonParameter);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("decrement_args(");
      boolean first = true;

      sb.append("request:");
      if (this.request == null) {
        sb.append("null");
      } else {
        sb.append(this.request);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("commonParameter:");
      if (this.commonParameter == null) {
        sb.append("null");
      } else {
        sb.append(this.commonParameter);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (request == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'request' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (request != null) {
        request.validate();
      }
      if (commonParameter != null) {
        commonParameter.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class decrement_argsStandardSchemeFactory implements SchemeFactory {
      public decrement_argsStandardScheme getScheme() {
        return new decrement_argsStandardScheme();
      }
    }

    private static class decrement_argsStandardScheme extends StandardScheme<decrement_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, decrement_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.request = new DecrementRequest();
                struct.request.read(iprot);
                struct.setRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // COMMON_PARAMETER
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter();
                struct.commonParameter.read(iprot);
                struct.setCommonParameterIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, decrement_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.request != null) {
          oprot.writeFieldBegin(REQUEST_FIELD_DESC);
          struct.request.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.commonParameter != null) {
          oprot.writeFieldBegin(COMMON_PARAMETER_FIELD_DESC);
          struct.commonParameter.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class decrement_argsTupleSchemeFactory implements SchemeFactory {
      public decrement_argsTupleScheme getScheme() {
        return new decrement_argsTupleScheme();
      }
    }

    private static class decrement_argsTupleScheme extends TupleScheme<decrement_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, decrement_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.request.write(oprot);
        BitSet optionals = new BitSet();
        if (struct.isSetCommonParameter()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetCommonParameter()) {
          struct.commonParameter.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, decrement_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.request = new DecrementRequest();
        struct.request.read(iprot);
        struct.setRequestIsSet(true);
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter();
          struct.commonParameter.read(iprot);
          struct.setCommonParameterIsSet(true);
        }
      }
    }

  }

  public static class decrement_result implements org.apache.thrift.TBase<decrement_result, decrement_result._Fields>, java.io.Serializable, Cloneable, Comparable<decrement_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("decrement_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new decrement_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new decrement_resultTupleSchemeFactory());
    }

    public DecrementResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, DecrementResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(decrement_result.class, metaDataMap);
    }

    public decrement_result() {
    }

    public decrement_result(
      DecrementResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public decrement_result(decrement_result other) {
      if (other.isSetSuccess()) {
        this.success = new DecrementResponse(other.success);
      }
    }

    public decrement_result deepCopy() {
      return new decrement_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public DecrementResponse getSuccess() {
      return this.success;
    }

    public decrement_result setSuccess(DecrementResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((DecrementResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof decrement_result)
        return this.equals((decrement_result)that);
      return false;
    }

    public boolean equals(decrement_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(decrement_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("decrement_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class decrement_resultStandardSchemeFactory implements SchemeFactory {
      public decrement_resultStandardScheme getScheme() {
        return new decrement_resultStandardScheme();
      }
    }

    private static class decrement_resultStandardScheme extends StandardScheme<decrement_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, decrement_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new DecrementResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, decrement_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class decrement_resultTupleSchemeFactory implements SchemeFactory {
      public decrement_resultTupleScheme getScheme() {
        return new decrement_resultTupleScheme();
      }
    }

    private static class decrement_resultTupleScheme extends TupleScheme<decrement_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, decrement_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, decrement_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new DecrementResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class freeze_args implements org.apache.thrift.TBase<freeze_args, freeze_args._Fields>, java.io.Serializable, Cloneable, Comparable<freeze_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("freeze_args");

    private static final org.apache.thrift.protocol.TField REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("request", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField COMMON_PARAMETER_FIELD_DESC = new org.apache.thrift.protocol.TField("commonParameter", org.apache.thrift.protocol.TType.STRUCT, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new freeze_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new freeze_argsTupleSchemeFactory());
    }

    public FreezeRequest request; // required
    public com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      REQUEST((short)1, "request"),
      COMMON_PARAMETER((short)2, "commonParameter");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // REQUEST
            return REQUEST;
          case 2: // COMMON_PARAMETER
            return COMMON_PARAMETER;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.REQUEST, new org.apache.thrift.meta_data.FieldMetaData("request", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, FreezeRequest.class)));
      tmpMap.put(_Fields.COMMON_PARAMETER, new org.apache.thrift.meta_data.FieldMetaData("commonParameter", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(freeze_args.class, metaDataMap);
    }

    public freeze_args() {
    }

    public freeze_args(
      FreezeRequest request,
      com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter)
    {
      this();
      this.request = request;
      this.commonParameter = commonParameter;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public freeze_args(freeze_args other) {
      if (other.isSetRequest()) {
        this.request = new FreezeRequest(other.request);
      }
      if (other.isSetCommonParameter()) {
        this.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter(other.commonParameter);
      }
    }

    public freeze_args deepCopy() {
      return new freeze_args(this);
    }

    @Override
    public void clear() {
      this.request = null;
      this.commonParameter = null;
    }

    public FreezeRequest getRequest() {
      return this.request;
    }

    public freeze_args setRequest(FreezeRequest request) {
      this.request = request;
      return this;
    }

    public void unsetRequest() {
      this.request = null;
    }

    /** Returns true if field request is set (has been assigned a value) and false otherwise */
    public boolean isSetRequest() {
      return this.request != null;
    }

    public void setRequestIsSet(boolean value) {
      if (!value) {
        this.request = null;
      }
    }

    public com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter getCommonParameter() {
      return this.commonParameter;
    }

    public freeze_args setCommonParameter(com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) {
      this.commonParameter = commonParameter;
      return this;
    }

    public void unsetCommonParameter() {
      this.commonParameter = null;
    }

    /** Returns true if field commonParameter is set (has been assigned a value) and false otherwise */
    public boolean isSetCommonParameter() {
      return this.commonParameter != null;
    }

    public void setCommonParameterIsSet(boolean value) {
      if (!value) {
        this.commonParameter = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case REQUEST:
        if (value == null) {
          unsetRequest();
        } else {
          setRequest((FreezeRequest)value);
        }
        break;

      case COMMON_PARAMETER:
        if (value == null) {
          unsetCommonParameter();
        } else {
          setCommonParameter((com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case REQUEST:
        return getRequest();

      case COMMON_PARAMETER:
        return getCommonParameter();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case REQUEST:
        return isSetRequest();
      case COMMON_PARAMETER:
        return isSetCommonParameter();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof freeze_args)
        return this.equals((freeze_args)that);
      return false;
    }

    public boolean equals(freeze_args that) {
      if (that == null)
        return false;

      boolean this_present_request = true && this.isSetRequest();
      boolean that_present_request = true && that.isSetRequest();
      if (this_present_request || that_present_request) {
        if (!(this_present_request && that_present_request))
          return false;
        if (!this.request.equals(that.request))
          return false;
      }

      boolean this_present_commonParameter = true && this.isSetCommonParameter();
      boolean that_present_commonParameter = true && that.isSetCommonParameter();
      if (this_present_commonParameter || that_present_commonParameter) {
        if (!(this_present_commonParameter && that_present_commonParameter))
          return false;
        if (!this.commonParameter.equals(that.commonParameter))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_request = true && (isSetRequest());
      list.add(present_request);
      if (present_request)
        list.add(request);

      boolean present_commonParameter = true && (isSetCommonParameter());
      list.add(present_commonParameter);
      if (present_commonParameter)
        list.add(commonParameter);

      return list.hashCode();
    }

    @Override
    public int compareTo(freeze_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.request, other.request);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetCommonParameter()).compareTo(other.isSetCommonParameter());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetCommonParameter()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.commonParameter, other.commonParameter);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("freeze_args(");
      boolean first = true;

      sb.append("request:");
      if (this.request == null) {
        sb.append("null");
      } else {
        sb.append(this.request);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("commonParameter:");
      if (this.commonParameter == null) {
        sb.append("null");
      } else {
        sb.append(this.commonParameter);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (request == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'request' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (request != null) {
        request.validate();
      }
      if (commonParameter != null) {
        commonParameter.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class freeze_argsStandardSchemeFactory implements SchemeFactory {
      public freeze_argsStandardScheme getScheme() {
        return new freeze_argsStandardScheme();
      }
    }

    private static class freeze_argsStandardScheme extends StandardScheme<freeze_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, freeze_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.request = new FreezeRequest();
                struct.request.read(iprot);
                struct.setRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // COMMON_PARAMETER
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter();
                struct.commonParameter.read(iprot);
                struct.setCommonParameterIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, freeze_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.request != null) {
          oprot.writeFieldBegin(REQUEST_FIELD_DESC);
          struct.request.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.commonParameter != null) {
          oprot.writeFieldBegin(COMMON_PARAMETER_FIELD_DESC);
          struct.commonParameter.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class freeze_argsTupleSchemeFactory implements SchemeFactory {
      public freeze_argsTupleScheme getScheme() {
        return new freeze_argsTupleScheme();
      }
    }

    private static class freeze_argsTupleScheme extends TupleScheme<freeze_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, freeze_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.request.write(oprot);
        BitSet optionals = new BitSet();
        if (struct.isSetCommonParameter()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetCommonParameter()) {
          struct.commonParameter.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, freeze_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.request = new FreezeRequest();
        struct.request.read(iprot);
        struct.setRequestIsSet(true);
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter();
          struct.commonParameter.read(iprot);
          struct.setCommonParameterIsSet(true);
        }
      }
    }

  }

  public static class freeze_result implements org.apache.thrift.TBase<freeze_result, freeze_result._Fields>, java.io.Serializable, Cloneable, Comparable<freeze_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("freeze_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new freeze_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new freeze_resultTupleSchemeFactory());
    }

    public FreezeResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, FreezeResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(freeze_result.class, metaDataMap);
    }

    public freeze_result() {
    }

    public freeze_result(
      FreezeResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public freeze_result(freeze_result other) {
      if (other.isSetSuccess()) {
        this.success = new FreezeResponse(other.success);
      }
    }

    public freeze_result deepCopy() {
      return new freeze_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public FreezeResponse getSuccess() {
      return this.success;
    }

    public freeze_result setSuccess(FreezeResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((FreezeResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof freeze_result)
        return this.equals((freeze_result)that);
      return false;
    }

    public boolean equals(freeze_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(freeze_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("freeze_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class freeze_resultStandardSchemeFactory implements SchemeFactory {
      public freeze_resultStandardScheme getScheme() {
        return new freeze_resultStandardScheme();
      }
    }

    private static class freeze_resultStandardScheme extends StandardScheme<freeze_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, freeze_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new FreezeResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, freeze_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class freeze_resultTupleSchemeFactory implements SchemeFactory {
      public freeze_resultTupleScheme getScheme() {
        return new freeze_resultTupleScheme();
      }
    }

    private static class freeze_resultTupleScheme extends TupleScheme<freeze_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, freeze_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, freeze_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new FreezeResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class freezeConfirm_args implements org.apache.thrift.TBase<freezeConfirm_args, freezeConfirm_args._Fields>, java.io.Serializable, Cloneable, Comparable<freezeConfirm_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("freezeConfirm_args");

    private static final org.apache.thrift.protocol.TField REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("request", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField COMMON_PARAMETER_FIELD_DESC = new org.apache.thrift.protocol.TField("commonParameter", org.apache.thrift.protocol.TType.STRUCT, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new freezeConfirm_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new freezeConfirm_argsTupleSchemeFactory());
    }

    public UnFreezeRequest request; // required
    public com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      REQUEST((short)1, "request"),
      COMMON_PARAMETER((short)2, "commonParameter");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // REQUEST
            return REQUEST;
          case 2: // COMMON_PARAMETER
            return COMMON_PARAMETER;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.REQUEST, new org.apache.thrift.meta_data.FieldMetaData("request", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, UnFreezeRequest.class)));
      tmpMap.put(_Fields.COMMON_PARAMETER, new org.apache.thrift.meta_data.FieldMetaData("commonParameter", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(freezeConfirm_args.class, metaDataMap);
    }

    public freezeConfirm_args() {
    }

    public freezeConfirm_args(
      UnFreezeRequest request,
      com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter)
    {
      this();
      this.request = request;
      this.commonParameter = commonParameter;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public freezeConfirm_args(freezeConfirm_args other) {
      if (other.isSetRequest()) {
        this.request = new UnFreezeRequest(other.request);
      }
      if (other.isSetCommonParameter()) {
        this.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter(other.commonParameter);
      }
    }

    public freezeConfirm_args deepCopy() {
      return new freezeConfirm_args(this);
    }

    @Override
    public void clear() {
      this.request = null;
      this.commonParameter = null;
    }

    public UnFreezeRequest getRequest() {
      return this.request;
    }

    public freezeConfirm_args setRequest(UnFreezeRequest request) {
      this.request = request;
      return this;
    }

    public void unsetRequest() {
      this.request = null;
    }

    /** Returns true if field request is set (has been assigned a value) and false otherwise */
    public boolean isSetRequest() {
      return this.request != null;
    }

    public void setRequestIsSet(boolean value) {
      if (!value) {
        this.request = null;
      }
    }

    public com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter getCommonParameter() {
      return this.commonParameter;
    }

    public freezeConfirm_args setCommonParameter(com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) {
      this.commonParameter = commonParameter;
      return this;
    }

    public void unsetCommonParameter() {
      this.commonParameter = null;
    }

    /** Returns true if field commonParameter is set (has been assigned a value) and false otherwise */
    public boolean isSetCommonParameter() {
      return this.commonParameter != null;
    }

    public void setCommonParameterIsSet(boolean value) {
      if (!value) {
        this.commonParameter = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case REQUEST:
        if (value == null) {
          unsetRequest();
        } else {
          setRequest((UnFreezeRequest)value);
        }
        break;

      case COMMON_PARAMETER:
        if (value == null) {
          unsetCommonParameter();
        } else {
          setCommonParameter((com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case REQUEST:
        return getRequest();

      case COMMON_PARAMETER:
        return getCommonParameter();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case REQUEST:
        return isSetRequest();
      case COMMON_PARAMETER:
        return isSetCommonParameter();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof freezeConfirm_args)
        return this.equals((freezeConfirm_args)that);
      return false;
    }

    public boolean equals(freezeConfirm_args that) {
      if (that == null)
        return false;

      boolean this_present_request = true && this.isSetRequest();
      boolean that_present_request = true && that.isSetRequest();
      if (this_present_request || that_present_request) {
        if (!(this_present_request && that_present_request))
          return false;
        if (!this.request.equals(that.request))
          return false;
      }

      boolean this_present_commonParameter = true && this.isSetCommonParameter();
      boolean that_present_commonParameter = true && that.isSetCommonParameter();
      if (this_present_commonParameter || that_present_commonParameter) {
        if (!(this_present_commonParameter && that_present_commonParameter))
          return false;
        if (!this.commonParameter.equals(that.commonParameter))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_request = true && (isSetRequest());
      list.add(present_request);
      if (present_request)
        list.add(request);

      boolean present_commonParameter = true && (isSetCommonParameter());
      list.add(present_commonParameter);
      if (present_commonParameter)
        list.add(commonParameter);

      return list.hashCode();
    }

    @Override
    public int compareTo(freezeConfirm_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.request, other.request);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetCommonParameter()).compareTo(other.isSetCommonParameter());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetCommonParameter()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.commonParameter, other.commonParameter);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("freezeConfirm_args(");
      boolean first = true;

      sb.append("request:");
      if (this.request == null) {
        sb.append("null");
      } else {
        sb.append(this.request);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("commonParameter:");
      if (this.commonParameter == null) {
        sb.append("null");
      } else {
        sb.append(this.commonParameter);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (request == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'request' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (request != null) {
        request.validate();
      }
      if (commonParameter != null) {
        commonParameter.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class freezeConfirm_argsStandardSchemeFactory implements SchemeFactory {
      public freezeConfirm_argsStandardScheme getScheme() {
        return new freezeConfirm_argsStandardScheme();
      }
    }

    private static class freezeConfirm_argsStandardScheme extends StandardScheme<freezeConfirm_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, freezeConfirm_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.request = new UnFreezeRequest();
                struct.request.read(iprot);
                struct.setRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // COMMON_PARAMETER
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter();
                struct.commonParameter.read(iprot);
                struct.setCommonParameterIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, freezeConfirm_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.request != null) {
          oprot.writeFieldBegin(REQUEST_FIELD_DESC);
          struct.request.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.commonParameter != null) {
          oprot.writeFieldBegin(COMMON_PARAMETER_FIELD_DESC);
          struct.commonParameter.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class freezeConfirm_argsTupleSchemeFactory implements SchemeFactory {
      public freezeConfirm_argsTupleScheme getScheme() {
        return new freezeConfirm_argsTupleScheme();
      }
    }

    private static class freezeConfirm_argsTupleScheme extends TupleScheme<freezeConfirm_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, freezeConfirm_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.request.write(oprot);
        BitSet optionals = new BitSet();
        if (struct.isSetCommonParameter()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetCommonParameter()) {
          struct.commonParameter.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, freezeConfirm_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.request = new UnFreezeRequest();
        struct.request.read(iprot);
        struct.setRequestIsSet(true);
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter();
          struct.commonParameter.read(iprot);
          struct.setCommonParameterIsSet(true);
        }
      }
    }

  }

  public static class freezeConfirm_result implements org.apache.thrift.TBase<freezeConfirm_result, freezeConfirm_result._Fields>, java.io.Serializable, Cloneable, Comparable<freezeConfirm_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("freezeConfirm_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new freezeConfirm_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new freezeConfirm_resultTupleSchemeFactory());
    }

    public UnFreezeResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, UnFreezeResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(freezeConfirm_result.class, metaDataMap);
    }

    public freezeConfirm_result() {
    }

    public freezeConfirm_result(
      UnFreezeResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public freezeConfirm_result(freezeConfirm_result other) {
      if (other.isSetSuccess()) {
        this.success = new UnFreezeResponse(other.success);
      }
    }

    public freezeConfirm_result deepCopy() {
      return new freezeConfirm_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public UnFreezeResponse getSuccess() {
      return this.success;
    }

    public freezeConfirm_result setSuccess(UnFreezeResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((UnFreezeResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof freezeConfirm_result)
        return this.equals((freezeConfirm_result)that);
      return false;
    }

    public boolean equals(freezeConfirm_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(freezeConfirm_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("freezeConfirm_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class freezeConfirm_resultStandardSchemeFactory implements SchemeFactory {
      public freezeConfirm_resultStandardScheme getScheme() {
        return new freezeConfirm_resultStandardScheme();
      }
    }

    private static class freezeConfirm_resultStandardScheme extends StandardScheme<freezeConfirm_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, freezeConfirm_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new UnFreezeResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, freezeConfirm_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class freezeConfirm_resultTupleSchemeFactory implements SchemeFactory {
      public freezeConfirm_resultTupleScheme getScheme() {
        return new freezeConfirm_resultTupleScheme();
      }
    }

    private static class freezeConfirm_resultTupleScheme extends TupleScheme<freezeConfirm_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, freezeConfirm_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, freezeConfirm_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new UnFreezeResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class freezeCancel_args implements org.apache.thrift.TBase<freezeCancel_args, freezeCancel_args._Fields>, java.io.Serializable, Cloneable, Comparable<freezeCancel_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("freezeCancel_args");

    private static final org.apache.thrift.protocol.TField REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("request", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField COMMON_PARAMETER_FIELD_DESC = new org.apache.thrift.protocol.TField("commonParameter", org.apache.thrift.protocol.TType.STRUCT, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new freezeCancel_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new freezeCancel_argsTupleSchemeFactory());
    }

    public UnFreezeRequest request; // required
    public com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      REQUEST((short)1, "request"),
      COMMON_PARAMETER((short)2, "commonParameter");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // REQUEST
            return REQUEST;
          case 2: // COMMON_PARAMETER
            return COMMON_PARAMETER;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.REQUEST, new org.apache.thrift.meta_data.FieldMetaData("request", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, UnFreezeRequest.class)));
      tmpMap.put(_Fields.COMMON_PARAMETER, new org.apache.thrift.meta_data.FieldMetaData("commonParameter", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(freezeCancel_args.class, metaDataMap);
    }

    public freezeCancel_args() {
    }

    public freezeCancel_args(
      UnFreezeRequest request,
      com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter)
    {
      this();
      this.request = request;
      this.commonParameter = commonParameter;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public freezeCancel_args(freezeCancel_args other) {
      if (other.isSetRequest()) {
        this.request = new UnFreezeRequest(other.request);
      }
      if (other.isSetCommonParameter()) {
        this.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter(other.commonParameter);
      }
    }

    public freezeCancel_args deepCopy() {
      return new freezeCancel_args(this);
    }

    @Override
    public void clear() {
      this.request = null;
      this.commonParameter = null;
    }

    public UnFreezeRequest getRequest() {
      return this.request;
    }

    public freezeCancel_args setRequest(UnFreezeRequest request) {
      this.request = request;
      return this;
    }

    public void unsetRequest() {
      this.request = null;
    }

    /** Returns true if field request is set (has been assigned a value) and false otherwise */
    public boolean isSetRequest() {
      return this.request != null;
    }

    public void setRequestIsSet(boolean value) {
      if (!value) {
        this.request = null;
      }
    }

    public com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter getCommonParameter() {
      return this.commonParameter;
    }

    public freezeCancel_args setCommonParameter(com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) {
      this.commonParameter = commonParameter;
      return this;
    }

    public void unsetCommonParameter() {
      this.commonParameter = null;
    }

    /** Returns true if field commonParameter is set (has been assigned a value) and false otherwise */
    public boolean isSetCommonParameter() {
      return this.commonParameter != null;
    }

    public void setCommonParameterIsSet(boolean value) {
      if (!value) {
        this.commonParameter = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case REQUEST:
        if (value == null) {
          unsetRequest();
        } else {
          setRequest((UnFreezeRequest)value);
        }
        break;

      case COMMON_PARAMETER:
        if (value == null) {
          unsetCommonParameter();
        } else {
          setCommonParameter((com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case REQUEST:
        return getRequest();

      case COMMON_PARAMETER:
        return getCommonParameter();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case REQUEST:
        return isSetRequest();
      case COMMON_PARAMETER:
        return isSetCommonParameter();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof freezeCancel_args)
        return this.equals((freezeCancel_args)that);
      return false;
    }

    public boolean equals(freezeCancel_args that) {
      if (that == null)
        return false;

      boolean this_present_request = true && this.isSetRequest();
      boolean that_present_request = true && that.isSetRequest();
      if (this_present_request || that_present_request) {
        if (!(this_present_request && that_present_request))
          return false;
        if (!this.request.equals(that.request))
          return false;
      }

      boolean this_present_commonParameter = true && this.isSetCommonParameter();
      boolean that_present_commonParameter = true && that.isSetCommonParameter();
      if (this_present_commonParameter || that_present_commonParameter) {
        if (!(this_present_commonParameter && that_present_commonParameter))
          return false;
        if (!this.commonParameter.equals(that.commonParameter))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_request = true && (isSetRequest());
      list.add(present_request);
      if (present_request)
        list.add(request);

      boolean present_commonParameter = true && (isSetCommonParameter());
      list.add(present_commonParameter);
      if (present_commonParameter)
        list.add(commonParameter);

      return list.hashCode();
    }

    @Override
    public int compareTo(freezeCancel_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.request, other.request);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetCommonParameter()).compareTo(other.isSetCommonParameter());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetCommonParameter()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.commonParameter, other.commonParameter);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("freezeCancel_args(");
      boolean first = true;

      sb.append("request:");
      if (this.request == null) {
        sb.append("null");
      } else {
        sb.append(this.request);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("commonParameter:");
      if (this.commonParameter == null) {
        sb.append("null");
      } else {
        sb.append(this.commonParameter);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (request == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'request' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (request != null) {
        request.validate();
      }
      if (commonParameter != null) {
        commonParameter.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class freezeCancel_argsStandardSchemeFactory implements SchemeFactory {
      public freezeCancel_argsStandardScheme getScheme() {
        return new freezeCancel_argsStandardScheme();
      }
    }

    private static class freezeCancel_argsStandardScheme extends StandardScheme<freezeCancel_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, freezeCancel_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.request = new UnFreezeRequest();
                struct.request.read(iprot);
                struct.setRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // COMMON_PARAMETER
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter();
                struct.commonParameter.read(iprot);
                struct.setCommonParameterIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, freezeCancel_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.request != null) {
          oprot.writeFieldBegin(REQUEST_FIELD_DESC);
          struct.request.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.commonParameter != null) {
          oprot.writeFieldBegin(COMMON_PARAMETER_FIELD_DESC);
          struct.commonParameter.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class freezeCancel_argsTupleSchemeFactory implements SchemeFactory {
      public freezeCancel_argsTupleScheme getScheme() {
        return new freezeCancel_argsTupleScheme();
      }
    }

    private static class freezeCancel_argsTupleScheme extends TupleScheme<freezeCancel_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, freezeCancel_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.request.write(oprot);
        BitSet optionals = new BitSet();
        if (struct.isSetCommonParameter()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetCommonParameter()) {
          struct.commonParameter.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, freezeCancel_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.request = new UnFreezeRequest();
        struct.request.read(iprot);
        struct.setRequestIsSet(true);
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter();
          struct.commonParameter.read(iprot);
          struct.setCommonParameterIsSet(true);
        }
      }
    }

  }

  public static class freezeCancel_result implements org.apache.thrift.TBase<freezeCancel_result, freezeCancel_result._Fields>, java.io.Serializable, Cloneable, Comparable<freezeCancel_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("freezeCancel_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new freezeCancel_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new freezeCancel_resultTupleSchemeFactory());
    }

    public UnFreezeResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, UnFreezeResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(freezeCancel_result.class, metaDataMap);
    }

    public freezeCancel_result() {
    }

    public freezeCancel_result(
      UnFreezeResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public freezeCancel_result(freezeCancel_result other) {
      if (other.isSetSuccess()) {
        this.success = new UnFreezeResponse(other.success);
      }
    }

    public freezeCancel_result deepCopy() {
      return new freezeCancel_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public UnFreezeResponse getSuccess() {
      return this.success;
    }

    public freezeCancel_result setSuccess(UnFreezeResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((UnFreezeResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof freezeCancel_result)
        return this.equals((freezeCancel_result)that);
      return false;
    }

    public boolean equals(freezeCancel_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(freezeCancel_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("freezeCancel_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class freezeCancel_resultStandardSchemeFactory implements SchemeFactory {
      public freezeCancel_resultStandardScheme getScheme() {
        return new freezeCancel_resultStandardScheme();
      }
    }

    private static class freezeCancel_resultStandardScheme extends StandardScheme<freezeCancel_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, freezeCancel_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new UnFreezeResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, freezeCancel_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class freezeCancel_resultTupleSchemeFactory implements SchemeFactory {
      public freezeCancel_resultTupleScheme getScheme() {
        return new freezeCancel_resultTupleScheme();
      }
    }

    private static class freezeCancel_resultTupleScheme extends TupleScheme<freezeCancel_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, freezeCancel_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, freezeCancel_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new UnFreezeResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class refund_args implements org.apache.thrift.TBase<refund_args, refund_args._Fields>, java.io.Serializable, Cloneable, Comparable<refund_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("refund_args");

    private static final org.apache.thrift.protocol.TField REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("request", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField COMMON_PARAMETER_FIELD_DESC = new org.apache.thrift.protocol.TField("commonParameter", org.apache.thrift.protocol.TType.STRUCT, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new refund_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new refund_argsTupleSchemeFactory());
    }

    public RefundRequest request; // required
    public com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      REQUEST((short)1, "request"),
      COMMON_PARAMETER((short)2, "commonParameter");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // REQUEST
            return REQUEST;
          case 2: // COMMON_PARAMETER
            return COMMON_PARAMETER;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.REQUEST, new org.apache.thrift.meta_data.FieldMetaData("request", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, RefundRequest.class)));
      tmpMap.put(_Fields.COMMON_PARAMETER, new org.apache.thrift.meta_data.FieldMetaData("commonParameter", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(refund_args.class, metaDataMap);
    }

    public refund_args() {
    }

    public refund_args(
      RefundRequest request,
      com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter)
    {
      this();
      this.request = request;
      this.commonParameter = commonParameter;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public refund_args(refund_args other) {
      if (other.isSetRequest()) {
        this.request = new RefundRequest(other.request);
      }
      if (other.isSetCommonParameter()) {
        this.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter(other.commonParameter);
      }
    }

    public refund_args deepCopy() {
      return new refund_args(this);
    }

    @Override
    public void clear() {
      this.request = null;
      this.commonParameter = null;
    }

    public RefundRequest getRequest() {
      return this.request;
    }

    public refund_args setRequest(RefundRequest request) {
      this.request = request;
      return this;
    }

    public void unsetRequest() {
      this.request = null;
    }

    /** Returns true if field request is set (has been assigned a value) and false otherwise */
    public boolean isSetRequest() {
      return this.request != null;
    }

    public void setRequestIsSet(boolean value) {
      if (!value) {
        this.request = null;
      }
    }

    public com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter getCommonParameter() {
      return this.commonParameter;
    }

    public refund_args setCommonParameter(com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) {
      this.commonParameter = commonParameter;
      return this;
    }

    public void unsetCommonParameter() {
      this.commonParameter = null;
    }

    /** Returns true if field commonParameter is set (has been assigned a value) and false otherwise */
    public boolean isSetCommonParameter() {
      return this.commonParameter != null;
    }

    public void setCommonParameterIsSet(boolean value) {
      if (!value) {
        this.commonParameter = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case REQUEST:
        if (value == null) {
          unsetRequest();
        } else {
          setRequest((RefundRequest)value);
        }
        break;

      case COMMON_PARAMETER:
        if (value == null) {
          unsetCommonParameter();
        } else {
          setCommonParameter((com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case REQUEST:
        return getRequest();

      case COMMON_PARAMETER:
        return getCommonParameter();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case REQUEST:
        return isSetRequest();
      case COMMON_PARAMETER:
        return isSetCommonParameter();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof refund_args)
        return this.equals((refund_args)that);
      return false;
    }

    public boolean equals(refund_args that) {
      if (that == null)
        return false;

      boolean this_present_request = true && this.isSetRequest();
      boolean that_present_request = true && that.isSetRequest();
      if (this_present_request || that_present_request) {
        if (!(this_present_request && that_present_request))
          return false;
        if (!this.request.equals(that.request))
          return false;
      }

      boolean this_present_commonParameter = true && this.isSetCommonParameter();
      boolean that_present_commonParameter = true && that.isSetCommonParameter();
      if (this_present_commonParameter || that_present_commonParameter) {
        if (!(this_present_commonParameter && that_present_commonParameter))
          return false;
        if (!this.commonParameter.equals(that.commonParameter))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_request = true && (isSetRequest());
      list.add(present_request);
      if (present_request)
        list.add(request);

      boolean present_commonParameter = true && (isSetCommonParameter());
      list.add(present_commonParameter);
      if (present_commonParameter)
        list.add(commonParameter);

      return list.hashCode();
    }

    @Override
    public int compareTo(refund_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.request, other.request);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetCommonParameter()).compareTo(other.isSetCommonParameter());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetCommonParameter()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.commonParameter, other.commonParameter);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("refund_args(");
      boolean first = true;

      sb.append("request:");
      if (this.request == null) {
        sb.append("null");
      } else {
        sb.append(this.request);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("commonParameter:");
      if (this.commonParameter == null) {
        sb.append("null");
      } else {
        sb.append(this.commonParameter);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (request == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'request' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (request != null) {
        request.validate();
      }
      if (commonParameter != null) {
        commonParameter.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class refund_argsStandardSchemeFactory implements SchemeFactory {
      public refund_argsStandardScheme getScheme() {
        return new refund_argsStandardScheme();
      }
    }

    private static class refund_argsStandardScheme extends StandardScheme<refund_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, refund_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.request = new RefundRequest();
                struct.request.read(iprot);
                struct.setRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // COMMON_PARAMETER
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter();
                struct.commonParameter.read(iprot);
                struct.setCommonParameterIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, refund_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.request != null) {
          oprot.writeFieldBegin(REQUEST_FIELD_DESC);
          struct.request.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.commonParameter != null) {
          oprot.writeFieldBegin(COMMON_PARAMETER_FIELD_DESC);
          struct.commonParameter.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class refund_argsTupleSchemeFactory implements SchemeFactory {
      public refund_argsTupleScheme getScheme() {
        return new refund_argsTupleScheme();
      }
    }

    private static class refund_argsTupleScheme extends TupleScheme<refund_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, refund_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.request.write(oprot);
        BitSet optionals = new BitSet();
        if (struct.isSetCommonParameter()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetCommonParameter()) {
          struct.commonParameter.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, refund_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.request = new RefundRequest();
        struct.request.read(iprot);
        struct.setRequestIsSet(true);
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter();
          struct.commonParameter.read(iprot);
          struct.setCommonParameterIsSet(true);
        }
      }
    }

  }

  public static class refund_result implements org.apache.thrift.TBase<refund_result, refund_result._Fields>, java.io.Serializable, Cloneable, Comparable<refund_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("refund_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new refund_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new refund_resultTupleSchemeFactory());
    }

    public RefundResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, RefundResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(refund_result.class, metaDataMap);
    }

    public refund_result() {
    }

    public refund_result(
      RefundResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public refund_result(refund_result other) {
      if (other.isSetSuccess()) {
        this.success = new RefundResponse(other.success);
      }
    }

    public refund_result deepCopy() {
      return new refund_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public RefundResponse getSuccess() {
      return this.success;
    }

    public refund_result setSuccess(RefundResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((RefundResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof refund_result)
        return this.equals((refund_result)that);
      return false;
    }

    public boolean equals(refund_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(refund_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("refund_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class refund_resultStandardSchemeFactory implements SchemeFactory {
      public refund_resultStandardScheme getScheme() {
        return new refund_resultStandardScheme();
      }
    }

    private static class refund_resultStandardScheme extends StandardScheme<refund_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, refund_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new RefundResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, refund_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class refund_resultTupleSchemeFactory implements SchemeFactory {
      public refund_resultTupleScheme getScheme() {
        return new refund_resultTupleScheme();
      }
    }

    private static class refund_resultTupleScheme extends TupleScheme<refund_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, refund_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, refund_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new RefundResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class decrementBySystem_args implements org.apache.thrift.TBase<decrementBySystem_args, decrementBySystem_args._Fields>, java.io.Serializable, Cloneable, Comparable<decrementBySystem_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("decrementBySystem_args");

    private static final org.apache.thrift.protocol.TField REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("request", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField CIENT_IP_FIELD_DESC = new org.apache.thrift.protocol.TField("cientIp", org.apache.thrift.protocol.TType.STRING, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new decrementBySystem_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new decrementBySystem_argsTupleSchemeFactory());
    }

    public DecrementRequest request; // required
    public String cientIp; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      REQUEST((short)1, "request"),
      CIENT_IP((short)2, "cientIp");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // REQUEST
            return REQUEST;
          case 2: // CIENT_IP
            return CIENT_IP;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.REQUEST, new org.apache.thrift.meta_data.FieldMetaData("request", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, DecrementRequest.class)));
      tmpMap.put(_Fields.CIENT_IP, new org.apache.thrift.meta_data.FieldMetaData("cientIp", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(decrementBySystem_args.class, metaDataMap);
    }

    public decrementBySystem_args() {
    }

    public decrementBySystem_args(
      DecrementRequest request,
      String cientIp)
    {
      this();
      this.request = request;
      this.cientIp = cientIp;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public decrementBySystem_args(decrementBySystem_args other) {
      if (other.isSetRequest()) {
        this.request = new DecrementRequest(other.request);
      }
      if (other.isSetCientIp()) {
        this.cientIp = other.cientIp;
      }
    }

    public decrementBySystem_args deepCopy() {
      return new decrementBySystem_args(this);
    }

    @Override
    public void clear() {
      this.request = null;
      this.cientIp = null;
    }

    public DecrementRequest getRequest() {
      return this.request;
    }

    public decrementBySystem_args setRequest(DecrementRequest request) {
      this.request = request;
      return this;
    }

    public void unsetRequest() {
      this.request = null;
    }

    /** Returns true if field request is set (has been assigned a value) and false otherwise */
    public boolean isSetRequest() {
      return this.request != null;
    }

    public void setRequestIsSet(boolean value) {
      if (!value) {
        this.request = null;
      }
    }

    public String getCientIp() {
      return this.cientIp;
    }

    public decrementBySystem_args setCientIp(String cientIp) {
      this.cientIp = cientIp;
      return this;
    }

    public void unsetCientIp() {
      this.cientIp = null;
    }

    /** Returns true if field cientIp is set (has been assigned a value) and false otherwise */
    public boolean isSetCientIp() {
      return this.cientIp != null;
    }

    public void setCientIpIsSet(boolean value) {
      if (!value) {
        this.cientIp = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case REQUEST:
        if (value == null) {
          unsetRequest();
        } else {
          setRequest((DecrementRequest)value);
        }
        break;

      case CIENT_IP:
        if (value == null) {
          unsetCientIp();
        } else {
          setCientIp((String)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case REQUEST:
        return getRequest();

      case CIENT_IP:
        return getCientIp();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case REQUEST:
        return isSetRequest();
      case CIENT_IP:
        return isSetCientIp();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof decrementBySystem_args)
        return this.equals((decrementBySystem_args)that);
      return false;
    }

    public boolean equals(decrementBySystem_args that) {
      if (that == null)
        return false;

      boolean this_present_request = true && this.isSetRequest();
      boolean that_present_request = true && that.isSetRequest();
      if (this_present_request || that_present_request) {
        if (!(this_present_request && that_present_request))
          return false;
        if (!this.request.equals(that.request))
          return false;
      }

      boolean this_present_cientIp = true && this.isSetCientIp();
      boolean that_present_cientIp = true && that.isSetCientIp();
      if (this_present_cientIp || that_present_cientIp) {
        if (!(this_present_cientIp && that_present_cientIp))
          return false;
        if (!this.cientIp.equals(that.cientIp))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_request = true && (isSetRequest());
      list.add(present_request);
      if (present_request)
        list.add(request);

      boolean present_cientIp = true && (isSetCientIp());
      list.add(present_cientIp);
      if (present_cientIp)
        list.add(cientIp);

      return list.hashCode();
    }

    @Override
    public int compareTo(decrementBySystem_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.request, other.request);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetCientIp()).compareTo(other.isSetCientIp());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetCientIp()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.cientIp, other.cientIp);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("decrementBySystem_args(");
      boolean first = true;

      sb.append("request:");
      if (this.request == null) {
        sb.append("null");
      } else {
        sb.append(this.request);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("cientIp:");
      if (this.cientIp == null) {
        sb.append("null");
      } else {
        sb.append(this.cientIp);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (request == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'request' was not present! Struct: " + toString());
      }
      if (cientIp == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'cientIp' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (request != null) {
        request.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class decrementBySystem_argsStandardSchemeFactory implements SchemeFactory {
      public decrementBySystem_argsStandardScheme getScheme() {
        return new decrementBySystem_argsStandardScheme();
      }
    }

    private static class decrementBySystem_argsStandardScheme extends StandardScheme<decrementBySystem_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, decrementBySystem_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.request = new DecrementRequest();
                struct.request.read(iprot);
                struct.setRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // CIENT_IP
              if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                struct.cientIp = iprot.readString();
                struct.setCientIpIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, decrementBySystem_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.request != null) {
          oprot.writeFieldBegin(REQUEST_FIELD_DESC);
          struct.request.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.cientIp != null) {
          oprot.writeFieldBegin(CIENT_IP_FIELD_DESC);
          oprot.writeString(struct.cientIp);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class decrementBySystem_argsTupleSchemeFactory implements SchemeFactory {
      public decrementBySystem_argsTupleScheme getScheme() {
        return new decrementBySystem_argsTupleScheme();
      }
    }

    private static class decrementBySystem_argsTupleScheme extends TupleScheme<decrementBySystem_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, decrementBySystem_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.request.write(oprot);
        oprot.writeString(struct.cientIp);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, decrementBySystem_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.request = new DecrementRequest();
        struct.request.read(iprot);
        struct.setRequestIsSet(true);
        struct.cientIp = iprot.readString();
        struct.setCientIpIsSet(true);
      }
    }

  }

  public static class decrementBySystem_result implements org.apache.thrift.TBase<decrementBySystem_result, decrementBySystem_result._Fields>, java.io.Serializable, Cloneable, Comparable<decrementBySystem_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("decrementBySystem_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new decrementBySystem_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new decrementBySystem_resultTupleSchemeFactory());
    }

    public DecrementResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, DecrementResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(decrementBySystem_result.class, metaDataMap);
    }

    public decrementBySystem_result() {
    }

    public decrementBySystem_result(
      DecrementResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public decrementBySystem_result(decrementBySystem_result other) {
      if (other.isSetSuccess()) {
        this.success = new DecrementResponse(other.success);
      }
    }

    public decrementBySystem_result deepCopy() {
      return new decrementBySystem_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public DecrementResponse getSuccess() {
      return this.success;
    }

    public decrementBySystem_result setSuccess(DecrementResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((DecrementResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof decrementBySystem_result)
        return this.equals((decrementBySystem_result)that);
      return false;
    }

    public boolean equals(decrementBySystem_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(decrementBySystem_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("decrementBySystem_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class decrementBySystem_resultStandardSchemeFactory implements SchemeFactory {
      public decrementBySystem_resultStandardScheme getScheme() {
        return new decrementBySystem_resultStandardScheme();
      }
    }

    private static class decrementBySystem_resultStandardScheme extends StandardScheme<decrementBySystem_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, decrementBySystem_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new DecrementResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, decrementBySystem_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class decrementBySystem_resultTupleSchemeFactory implements SchemeFactory {
      public decrementBySystem_resultTupleScheme getScheme() {
        return new decrementBySystem_resultTupleScheme();
      }
    }

    private static class decrementBySystem_resultTupleScheme extends TupleScheme<decrementBySystem_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, decrementBySystem_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, decrementBySystem_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new DecrementResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class recharge_args implements org.apache.thrift.TBase<recharge_args, recharge_args._Fields>, java.io.Serializable, Cloneable, Comparable<recharge_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("recharge_args");

    private static final org.apache.thrift.protocol.TField REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("request", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField COMMON_PARAMETER_FIELD_DESC = new org.apache.thrift.protocol.TField("commonParameter", org.apache.thrift.protocol.TType.STRUCT, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new recharge_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new recharge_argsTupleSchemeFactory());
    }

    public IncrementRequest request; // required
    public com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      REQUEST((short)1, "request"),
      COMMON_PARAMETER((short)2, "commonParameter");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // REQUEST
            return REQUEST;
          case 2: // COMMON_PARAMETER
            return COMMON_PARAMETER;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.REQUEST, new org.apache.thrift.meta_data.FieldMetaData("request", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, IncrementRequest.class)));
      tmpMap.put(_Fields.COMMON_PARAMETER, new org.apache.thrift.meta_data.FieldMetaData("commonParameter", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(recharge_args.class, metaDataMap);
    }

    public recharge_args() {
    }

    public recharge_args(
      IncrementRequest request,
      com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter)
    {
      this();
      this.request = request;
      this.commonParameter = commonParameter;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public recharge_args(recharge_args other) {
      if (other.isSetRequest()) {
        this.request = new IncrementRequest(other.request);
      }
      if (other.isSetCommonParameter()) {
        this.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter(other.commonParameter);
      }
    }

    public recharge_args deepCopy() {
      return new recharge_args(this);
    }

    @Override
    public void clear() {
      this.request = null;
      this.commonParameter = null;
    }

    public IncrementRequest getRequest() {
      return this.request;
    }

    public recharge_args setRequest(IncrementRequest request) {
      this.request = request;
      return this;
    }

    public void unsetRequest() {
      this.request = null;
    }

    /** Returns true if field request is set (has been assigned a value) and false otherwise */
    public boolean isSetRequest() {
      return this.request != null;
    }

    public void setRequestIsSet(boolean value) {
      if (!value) {
        this.request = null;
      }
    }

    public com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter getCommonParameter() {
      return this.commonParameter;
    }

    public recharge_args setCommonParameter(com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter commonParameter) {
      this.commonParameter = commonParameter;
      return this;
    }

    public void unsetCommonParameter() {
      this.commonParameter = null;
    }

    /** Returns true if field commonParameter is set (has been assigned a value) and false otherwise */
    public boolean isSetCommonParameter() {
      return this.commonParameter != null;
    }

    public void setCommonParameterIsSet(boolean value) {
      if (!value) {
        this.commonParameter = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case REQUEST:
        if (value == null) {
          unsetRequest();
        } else {
          setRequest((IncrementRequest)value);
        }
        break;

      case COMMON_PARAMETER:
        if (value == null) {
          unsetCommonParameter();
        } else {
          setCommonParameter((com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case REQUEST:
        return getRequest();

      case COMMON_PARAMETER:
        return getCommonParameter();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case REQUEST:
        return isSetRequest();
      case COMMON_PARAMETER:
        return isSetCommonParameter();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof recharge_args)
        return this.equals((recharge_args)that);
      return false;
    }

    public boolean equals(recharge_args that) {
      if (that == null)
        return false;

      boolean this_present_request = true && this.isSetRequest();
      boolean that_present_request = true && that.isSetRequest();
      if (this_present_request || that_present_request) {
        if (!(this_present_request && that_present_request))
          return false;
        if (!this.request.equals(that.request))
          return false;
      }

      boolean this_present_commonParameter = true && this.isSetCommonParameter();
      boolean that_present_commonParameter = true && that.isSetCommonParameter();
      if (this_present_commonParameter || that_present_commonParameter) {
        if (!(this_present_commonParameter && that_present_commonParameter))
          return false;
        if (!this.commonParameter.equals(that.commonParameter))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_request = true && (isSetRequest());
      list.add(present_request);
      if (present_request)
        list.add(request);

      boolean present_commonParameter = true && (isSetCommonParameter());
      list.add(present_commonParameter);
      if (present_commonParameter)
        list.add(commonParameter);

      return list.hashCode();
    }

    @Override
    public int compareTo(recharge_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetRequest()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.request, other.request);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetCommonParameter()).compareTo(other.isSetCommonParameter());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetCommonParameter()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.commonParameter, other.commonParameter);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("recharge_args(");
      boolean first = true;

      sb.append("request:");
      if (this.request == null) {
        sb.append("null");
      } else {
        sb.append(this.request);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("commonParameter:");
      if (this.commonParameter == null) {
        sb.append("null");
      } else {
        sb.append(this.commonParameter);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (request == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'request' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (request != null) {
        request.validate();
      }
      if (commonParameter != null) {
        commonParameter.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class recharge_argsStandardSchemeFactory implements SchemeFactory {
      public recharge_argsStandardScheme getScheme() {
        return new recharge_argsStandardScheme();
      }
    }

    private static class recharge_argsStandardScheme extends StandardScheme<recharge_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, recharge_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // REQUEST
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.request = new IncrementRequest();
                struct.request.read(iprot);
                struct.setRequestIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // COMMON_PARAMETER
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter();
                struct.commonParameter.read(iprot);
                struct.setCommonParameterIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, recharge_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.request != null) {
          oprot.writeFieldBegin(REQUEST_FIELD_DESC);
          struct.request.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.commonParameter != null) {
          oprot.writeFieldBegin(COMMON_PARAMETER_FIELD_DESC);
          struct.commonParameter.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class recharge_argsTupleSchemeFactory implements SchemeFactory {
      public recharge_argsTupleScheme getScheme() {
        return new recharge_argsTupleScheme();
      }
    }

    private static class recharge_argsTupleScheme extends TupleScheme<recharge_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, recharge_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.request.write(oprot);
        BitSet optionals = new BitSet();
        if (struct.isSetCommonParameter()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetCommonParameter()) {
          struct.commonParameter.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, recharge_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.request = new IncrementRequest();
        struct.request.read(iprot);
        struct.setRequestIsSet(true);
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.commonParameter = new com.kugou.fanxing.thrift.common.simpleparameter.CommonParameter();
          struct.commonParameter.read(iprot);
          struct.setCommonParameterIsSet(true);
        }
      }
    }

  }

  public static class recharge_result implements org.apache.thrift.TBase<recharge_result, recharge_result._Fields>, java.io.Serializable, Cloneable, Comparable<recharge_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("recharge_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new recharge_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new recharge_resultTupleSchemeFactory());
    }

    public IncrementResponse success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, IncrementResponse.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(recharge_result.class, metaDataMap);
    }

    public recharge_result() {
    }

    public recharge_result(
      IncrementResponse success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public recharge_result(recharge_result other) {
      if (other.isSetSuccess()) {
        this.success = new IncrementResponse(other.success);
      }
    }

    public recharge_result deepCopy() {
      return new recharge_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public IncrementResponse getSuccess() {
      return this.success;
    }

    public recharge_result setSuccess(IncrementResponse success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((IncrementResponse)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof recharge_result)
        return this.equals((recharge_result)that);
      return false;
    }

    public boolean equals(recharge_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(recharge_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("recharge_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class recharge_resultStandardSchemeFactory implements SchemeFactory {
      public recharge_resultStandardScheme getScheme() {
        return new recharge_resultStandardScheme();
      }
    }

    private static class recharge_resultStandardScheme extends StandardScheme<recharge_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, recharge_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new IncrementResponse();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, recharge_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class recharge_resultTupleSchemeFactory implements SchemeFactory {
      public recharge_resultTupleScheme getScheme() {
        return new recharge_resultTupleScheme();
      }
    }

    private static class recharge_resultTupleScheme extends TupleScheme<recharge_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, recharge_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, recharge_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new IncrementResponse();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

}
