/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.coupon.thrift;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2020-03-10")
public class PutParamsVO implements org.apache.thrift.TBase<PutParamsVO, PutParamsVO._Fields>, java.io.Serializable, Cloneable, Comparable<PutParamsVO> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PutParamsVO");

  private static final org.apache.thrift.protocol.TField ORDER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("orderId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField COUPON_TYPE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("couponTypeId", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField CFROM_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("cfromId", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField P_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("pId", org.apache.thrift.protocol.TType.I32, (short)5);
  private static final org.apache.thrift.protocol.TField ACTION_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actionId", org.apache.thrift.protocol.TType.I32, (short)6);
  private static final org.apache.thrift.protocol.TField JSON_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("jsonData", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField REQ_TIMESTAMP_FIELD_DESC = new org.apache.thrift.protocol.TField("reqTimestamp", org.apache.thrift.protocol.TType.I32, (short)8);
  private static final org.apache.thrift.protocol.TField SIGN_FIELD_DESC = new org.apache.thrift.protocol.TField("sign", org.apache.thrift.protocol.TType.STRING, (short)9);
  private static final org.apache.thrift.protocol.TField CUSTOMIZE_COUPON_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("customizeCouponId", org.apache.thrift.protocol.TType.I64, (short)10);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new PutParamsVOStandardSchemeFactory());
    schemes.put(TupleScheme.class, new PutParamsVOTupleSchemeFactory());
  }

  public long orderId; // required
  public long kugouId; // required
  public long couponTypeId; // required
  public int cfromId; // required
  public int pId; // required
  public int actionId; // required
  public String jsonData; // required
  public int reqTimestamp; // required
  public String sign; // required
  public long customizeCouponId; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ORDER_ID((short)1, "orderId"),
    KUGOU_ID((short)2, "kugouId"),
    COUPON_TYPE_ID((short)3, "couponTypeId"),
    CFROM_ID((short)4, "cfromId"),
    P_ID((short)5, "pId"),
    ACTION_ID((short)6, "actionId"),
    JSON_DATA((short)7, "jsonData"),
    REQ_TIMESTAMP((short)8, "reqTimestamp"),
    SIGN((short)9, "sign"),
    CUSTOMIZE_COUPON_ID((short)10, "customizeCouponId");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ORDER_ID
          return ORDER_ID;
        case 2: // KUGOU_ID
          return KUGOU_ID;
        case 3: // COUPON_TYPE_ID
          return COUPON_TYPE_ID;
        case 4: // CFROM_ID
          return CFROM_ID;
        case 5: // P_ID
          return P_ID;
        case 6: // ACTION_ID
          return ACTION_ID;
        case 7: // JSON_DATA
          return JSON_DATA;
        case 8: // REQ_TIMESTAMP
          return REQ_TIMESTAMP;
        case 9: // SIGN
          return SIGN;
        case 10: // CUSTOMIZE_COUPON_ID
          return CUSTOMIZE_COUPON_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ORDERID_ISSET_ID = 0;
  private static final int __KUGOUID_ISSET_ID = 1;
  private static final int __COUPONTYPEID_ISSET_ID = 2;
  private static final int __CFROMID_ISSET_ID = 3;
  private static final int __PID_ISSET_ID = 4;
  private static final int __ACTIONID_ISSET_ID = 5;
  private static final int __REQTIMESTAMP_ISSET_ID = 6;
  private static final int __CUSTOMIZECOUPONID_ISSET_ID = 7;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.CUSTOMIZE_COUPON_ID};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ORDER_ID, new org.apache.thrift.meta_data.FieldMetaData("orderId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.COUPON_TYPE_ID, new org.apache.thrift.meta_data.FieldMetaData("couponTypeId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CFROM_ID, new org.apache.thrift.meta_data.FieldMetaData("cfromId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.P_ID, new org.apache.thrift.meta_data.FieldMetaData("pId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ACTION_ID, new org.apache.thrift.meta_data.FieldMetaData("actionId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.JSON_DATA, new org.apache.thrift.meta_data.FieldMetaData("jsonData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.REQ_TIMESTAMP, new org.apache.thrift.meta_data.FieldMetaData("reqTimestamp", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.SIGN, new org.apache.thrift.meta_data.FieldMetaData("sign", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CUSTOMIZE_COUPON_ID, new org.apache.thrift.meta_data.FieldMetaData("customizeCouponId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PutParamsVO.class, metaDataMap);
  }

  public PutParamsVO() {
  }

  public PutParamsVO(
    long orderId,
    long kugouId,
    long couponTypeId,
    int cfromId,
    int pId,
    int actionId,
    String jsonData,
    int reqTimestamp,
    String sign)
  {
    this();
    this.orderId = orderId;
    setOrderIdIsSet(true);
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    this.couponTypeId = couponTypeId;
    setCouponTypeIdIsSet(true);
    this.cfromId = cfromId;
    setCfromIdIsSet(true);
    this.pId = pId;
    setPIdIsSet(true);
    this.actionId = actionId;
    setActionIdIsSet(true);
    this.jsonData = jsonData;
    this.reqTimestamp = reqTimestamp;
    setReqTimestampIsSet(true);
    this.sign = sign;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PutParamsVO(PutParamsVO other) {
    __isset_bitfield = other.__isset_bitfield;
    this.orderId = other.orderId;
    this.kugouId = other.kugouId;
    this.couponTypeId = other.couponTypeId;
    this.cfromId = other.cfromId;
    this.pId = other.pId;
    this.actionId = other.actionId;
    if (other.isSetJsonData()) {
      this.jsonData = other.jsonData;
    }
    this.reqTimestamp = other.reqTimestamp;
    if (other.isSetSign()) {
      this.sign = other.sign;
    }
    this.customizeCouponId = other.customizeCouponId;
  }

  public PutParamsVO deepCopy() {
    return new PutParamsVO(this);
  }

  @Override
  public void clear() {
    setOrderIdIsSet(false);
    this.orderId = 0;
    setKugouIdIsSet(false);
    this.kugouId = 0;
    setCouponTypeIdIsSet(false);
    this.couponTypeId = 0;
    setCfromIdIsSet(false);
    this.cfromId = 0;
    setPIdIsSet(false);
    this.pId = 0;
    setActionIdIsSet(false);
    this.actionId = 0;
    this.jsonData = null;
    setReqTimestampIsSet(false);
    this.reqTimestamp = 0;
    this.sign = null;
    setCustomizeCouponIdIsSet(false);
    this.customizeCouponId = 0;
  }

  public long getOrderId() {
    return this.orderId;
  }

  public PutParamsVO setOrderId(long orderId) {
    this.orderId = orderId;
    setOrderIdIsSet(true);
    return this;
  }

  public void unsetOrderId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ORDERID_ISSET_ID);
  }

  /** Returns true if field orderId is set (has been assigned a value) and false otherwise */
  public boolean isSetOrderId() {
    return EncodingUtils.testBit(__isset_bitfield, __ORDERID_ISSET_ID);
  }

  public void setOrderIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ORDERID_ISSET_ID, value);
  }

  public long getKugouId() {
    return this.kugouId;
  }

  public PutParamsVO setKugouId(long kugouId) {
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    return this;
  }

  public void unsetKugouId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
  public boolean isSetKugouId() {
    return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  public void setKugouIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
  }

  public long getCouponTypeId() {
    return this.couponTypeId;
  }

  public PutParamsVO setCouponTypeId(long couponTypeId) {
    this.couponTypeId = couponTypeId;
    setCouponTypeIdIsSet(true);
    return this;
  }

  public void unsetCouponTypeId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COUPONTYPEID_ISSET_ID);
  }

  /** Returns true if field couponTypeId is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponTypeId() {
    return EncodingUtils.testBit(__isset_bitfield, __COUPONTYPEID_ISSET_ID);
  }

  public void setCouponTypeIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COUPONTYPEID_ISSET_ID, value);
  }

  public int getCfromId() {
    return this.cfromId;
  }

  public PutParamsVO setCfromId(int cfromId) {
    this.cfromId = cfromId;
    setCfromIdIsSet(true);
    return this;
  }

  public void unsetCfromId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CFROMID_ISSET_ID);
  }

  /** Returns true if field cfromId is set (has been assigned a value) and false otherwise */
  public boolean isSetCfromId() {
    return EncodingUtils.testBit(__isset_bitfield, __CFROMID_ISSET_ID);
  }

  public void setCfromIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CFROMID_ISSET_ID, value);
  }

  public int getPId() {
    return this.pId;
  }

  public PutParamsVO setPId(int pId) {
    this.pId = pId;
    setPIdIsSet(true);
    return this;
  }

  public void unsetPId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PID_ISSET_ID);
  }

  /** Returns true if field pId is set (has been assigned a value) and false otherwise */
  public boolean isSetPId() {
    return EncodingUtils.testBit(__isset_bitfield, __PID_ISSET_ID);
  }

  public void setPIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PID_ISSET_ID, value);
  }

  public int getActionId() {
    return this.actionId;
  }

  public PutParamsVO setActionId(int actionId) {
    this.actionId = actionId;
    setActionIdIsSet(true);
    return this;
  }

  public void unsetActionId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTIONID_ISSET_ID);
  }

  /** Returns true if field actionId is set (has been assigned a value) and false otherwise */
  public boolean isSetActionId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTIONID_ISSET_ID);
  }

  public void setActionIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTIONID_ISSET_ID, value);
  }

  public String getJsonData() {
    return this.jsonData;
  }

  public PutParamsVO setJsonData(String jsonData) {
    this.jsonData = jsonData;
    return this;
  }

  public void unsetJsonData() {
    this.jsonData = null;
  }

  /** Returns true if field jsonData is set (has been assigned a value) and false otherwise */
  public boolean isSetJsonData() {
    return this.jsonData != null;
  }

  public void setJsonDataIsSet(boolean value) {
    if (!value) {
      this.jsonData = null;
    }
  }

  public int getReqTimestamp() {
    return this.reqTimestamp;
  }

  public PutParamsVO setReqTimestamp(int reqTimestamp) {
    this.reqTimestamp = reqTimestamp;
    setReqTimestampIsSet(true);
    return this;
  }

  public void unsetReqTimestamp() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __REQTIMESTAMP_ISSET_ID);
  }

  /** Returns true if field reqTimestamp is set (has been assigned a value) and false otherwise */
  public boolean isSetReqTimestamp() {
    return EncodingUtils.testBit(__isset_bitfield, __REQTIMESTAMP_ISSET_ID);
  }

  public void setReqTimestampIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __REQTIMESTAMP_ISSET_ID, value);
  }

  public String getSign() {
    return this.sign;
  }

  public PutParamsVO setSign(String sign) {
    this.sign = sign;
    return this;
  }

  public void unsetSign() {
    this.sign = null;
  }

  /** Returns true if field sign is set (has been assigned a value) and false otherwise */
  public boolean isSetSign() {
    return this.sign != null;
  }

  public void setSignIsSet(boolean value) {
    if (!value) {
      this.sign = null;
    }
  }

  public long getCustomizeCouponId() {
    return this.customizeCouponId;
  }

  public PutParamsVO setCustomizeCouponId(long customizeCouponId) {
    this.customizeCouponId = customizeCouponId;
    setCustomizeCouponIdIsSet(true);
    return this;
  }

  public void unsetCustomizeCouponId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CUSTOMIZECOUPONID_ISSET_ID);
  }

  /** Returns true if field customizeCouponId is set (has been assigned a value) and false otherwise */
  public boolean isSetCustomizeCouponId() {
    return EncodingUtils.testBit(__isset_bitfield, __CUSTOMIZECOUPONID_ISSET_ID);
  }

  public void setCustomizeCouponIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CUSTOMIZECOUPONID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ORDER_ID:
      if (value == null) {
        unsetOrderId();
      } else {
        setOrderId((Long)value);
      }
      break;

    case KUGOU_ID:
      if (value == null) {
        unsetKugouId();
      } else {
        setKugouId((Long)value);
      }
      break;

    case COUPON_TYPE_ID:
      if (value == null) {
        unsetCouponTypeId();
      } else {
        setCouponTypeId((Long)value);
      }
      break;

    case CFROM_ID:
      if (value == null) {
        unsetCfromId();
      } else {
        setCfromId((Integer)value);
      }
      break;

    case P_ID:
      if (value == null) {
        unsetPId();
      } else {
        setPId((Integer)value);
      }
      break;

    case ACTION_ID:
      if (value == null) {
        unsetActionId();
      } else {
        setActionId((Integer)value);
      }
      break;

    case JSON_DATA:
      if (value == null) {
        unsetJsonData();
      } else {
        setJsonData((String)value);
      }
      break;

    case REQ_TIMESTAMP:
      if (value == null) {
        unsetReqTimestamp();
      } else {
        setReqTimestamp((Integer)value);
      }
      break;

    case SIGN:
      if (value == null) {
        unsetSign();
      } else {
        setSign((String)value);
      }
      break;

    case CUSTOMIZE_COUPON_ID:
      if (value == null) {
        unsetCustomizeCouponId();
      } else {
        setCustomizeCouponId((Long)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ORDER_ID:
      return getOrderId();

    case KUGOU_ID:
      return getKugouId();

    case COUPON_TYPE_ID:
      return getCouponTypeId();

    case CFROM_ID:
      return getCfromId();

    case P_ID:
      return getPId();

    case ACTION_ID:
      return getActionId();

    case JSON_DATA:
      return getJsonData();

    case REQ_TIMESTAMP:
      return getReqTimestamp();

    case SIGN:
      return getSign();

    case CUSTOMIZE_COUPON_ID:
      return getCustomizeCouponId();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ORDER_ID:
      return isSetOrderId();
    case KUGOU_ID:
      return isSetKugouId();
    case COUPON_TYPE_ID:
      return isSetCouponTypeId();
    case CFROM_ID:
      return isSetCfromId();
    case P_ID:
      return isSetPId();
    case ACTION_ID:
      return isSetActionId();
    case JSON_DATA:
      return isSetJsonData();
    case REQ_TIMESTAMP:
      return isSetReqTimestamp();
    case SIGN:
      return isSetSign();
    case CUSTOMIZE_COUPON_ID:
      return isSetCustomizeCouponId();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof PutParamsVO)
      return this.equals((PutParamsVO)that);
    return false;
  }

  public boolean equals(PutParamsVO that) {
    if (that == null)
      return false;

    boolean this_present_orderId = true;
    boolean that_present_orderId = true;
    if (this_present_orderId || that_present_orderId) {
      if (!(this_present_orderId && that_present_orderId))
        return false;
      if (this.orderId != that.orderId)
        return false;
    }

    boolean this_present_kugouId = true;
    boolean that_present_kugouId = true;
    if (this_present_kugouId || that_present_kugouId) {
      if (!(this_present_kugouId && that_present_kugouId))
        return false;
      if (this.kugouId != that.kugouId)
        return false;
    }

    boolean this_present_couponTypeId = true;
    boolean that_present_couponTypeId = true;
    if (this_present_couponTypeId || that_present_couponTypeId) {
      if (!(this_present_couponTypeId && that_present_couponTypeId))
        return false;
      if (this.couponTypeId != that.couponTypeId)
        return false;
    }

    boolean this_present_cfromId = true;
    boolean that_present_cfromId = true;
    if (this_present_cfromId || that_present_cfromId) {
      if (!(this_present_cfromId && that_present_cfromId))
        return false;
      if (this.cfromId != that.cfromId)
        return false;
    }

    boolean this_present_pId = true;
    boolean that_present_pId = true;
    if (this_present_pId || that_present_pId) {
      if (!(this_present_pId && that_present_pId))
        return false;
      if (this.pId != that.pId)
        return false;
    }

    boolean this_present_actionId = true;
    boolean that_present_actionId = true;
    if (this_present_actionId || that_present_actionId) {
      if (!(this_present_actionId && that_present_actionId))
        return false;
      if (this.actionId != that.actionId)
        return false;
    }

    boolean this_present_jsonData = true && this.isSetJsonData();
    boolean that_present_jsonData = true && that.isSetJsonData();
    if (this_present_jsonData || that_present_jsonData) {
      if (!(this_present_jsonData && that_present_jsonData))
        return false;
      if (!this.jsonData.equals(that.jsonData))
        return false;
    }

    boolean this_present_reqTimestamp = true;
    boolean that_present_reqTimestamp = true;
    if (this_present_reqTimestamp || that_present_reqTimestamp) {
      if (!(this_present_reqTimestamp && that_present_reqTimestamp))
        return false;
      if (this.reqTimestamp != that.reqTimestamp)
        return false;
    }

    boolean this_present_sign = true && this.isSetSign();
    boolean that_present_sign = true && that.isSetSign();
    if (this_present_sign || that_present_sign) {
      if (!(this_present_sign && that_present_sign))
        return false;
      if (!this.sign.equals(that.sign))
        return false;
    }

    boolean this_present_customizeCouponId = true && this.isSetCustomizeCouponId();
    boolean that_present_customizeCouponId = true && that.isSetCustomizeCouponId();
    if (this_present_customizeCouponId || that_present_customizeCouponId) {
      if (!(this_present_customizeCouponId && that_present_customizeCouponId))
        return false;
      if (this.customizeCouponId != that.customizeCouponId)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_orderId = true;
    list.add(present_orderId);
    if (present_orderId)
      list.add(orderId);

    boolean present_kugouId = true;
    list.add(present_kugouId);
    if (present_kugouId)
      list.add(kugouId);

    boolean present_couponTypeId = true;
    list.add(present_couponTypeId);
    if (present_couponTypeId)
      list.add(couponTypeId);

    boolean present_cfromId = true;
    list.add(present_cfromId);
    if (present_cfromId)
      list.add(cfromId);

    boolean present_pId = true;
    list.add(present_pId);
    if (present_pId)
      list.add(pId);

    boolean present_actionId = true;
    list.add(present_actionId);
    if (present_actionId)
      list.add(actionId);

    boolean present_jsonData = true && (isSetJsonData());
    list.add(present_jsonData);
    if (present_jsonData)
      list.add(jsonData);

    boolean present_reqTimestamp = true;
    list.add(present_reqTimestamp);
    if (present_reqTimestamp)
      list.add(reqTimestamp);

    boolean present_sign = true && (isSetSign());
    list.add(present_sign);
    if (present_sign)
      list.add(sign);

    boolean present_customizeCouponId = true && (isSetCustomizeCouponId());
    list.add(present_customizeCouponId);
    if (present_customizeCouponId)
      list.add(customizeCouponId);

    return list.hashCode();
  }

  @Override
  public int compareTo(PutParamsVO other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetOrderId()).compareTo(other.isSetOrderId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOrderId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.orderId, other.orderId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKugouId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponTypeId()).compareTo(other.isSetCouponTypeId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponTypeId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponTypeId, other.couponTypeId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCfromId()).compareTo(other.isSetCfromId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCfromId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.cfromId, other.cfromId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPId()).compareTo(other.isSetPId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pId, other.pId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetActionId()).compareTo(other.isSetActionId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActionId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actionId, other.actionId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetJsonData()).compareTo(other.isSetJsonData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetJsonData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.jsonData, other.jsonData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetReqTimestamp()).compareTo(other.isSetReqTimestamp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetReqTimestamp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.reqTimestamp, other.reqTimestamp);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSign()).compareTo(other.isSetSign());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSign()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sign, other.sign);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCustomizeCouponId()).compareTo(other.isSetCustomizeCouponId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCustomizeCouponId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.customizeCouponId, other.customizeCouponId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("PutParamsVO(");
    boolean first = true;

    sb.append("orderId:");
    sb.append(this.orderId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("kugouId:");
    sb.append(this.kugouId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponTypeId:");
    sb.append(this.couponTypeId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("cfromId:");
    sb.append(this.cfromId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("pId:");
    sb.append(this.pId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("actionId:");
    sb.append(this.actionId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("jsonData:");
    if (this.jsonData == null) {
      sb.append("null");
    } else {
      sb.append(this.jsonData);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("reqTimestamp:");
    sb.append(this.reqTimestamp);
    first = false;
    if (!first) sb.append(", ");
    sb.append("sign:");
    if (this.sign == null) {
      sb.append("null");
    } else {
      sb.append(this.sign);
    }
    first = false;
    if (isSetCustomizeCouponId()) {
      if (!first) sb.append(", ");
      sb.append("customizeCouponId:");
      sb.append(this.customizeCouponId);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'orderId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'couponTypeId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'cfromId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'pId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'actionId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'reqTimestamp' because it's a primitive and you chose the non-beans generator.
    if (sign == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'sign' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PutParamsVOStandardSchemeFactory implements SchemeFactory {
    public PutParamsVOStandardScheme getScheme() {
      return new PutParamsVOStandardScheme();
    }
  }

  private static class PutParamsVOStandardScheme extends StandardScheme<PutParamsVO> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PutParamsVO struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ORDER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.orderId = iprot.readI64();
              struct.setOrderIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // KUGOU_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.kugouId = iprot.readI64();
              struct.setKugouIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // COUPON_TYPE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.couponTypeId = iprot.readI64();
              struct.setCouponTypeIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // CFROM_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.cfromId = iprot.readI32();
              struct.setCfromIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // P_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.pId = iprot.readI32();
              struct.setPIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // ACTION_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.actionId = iprot.readI32();
              struct.setActionIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // JSON_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.jsonData = iprot.readString();
              struct.setJsonDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // REQ_TIMESTAMP
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.reqTimestamp = iprot.readI32();
              struct.setReqTimestampIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // SIGN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.sign = iprot.readString();
              struct.setSignIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // CUSTOMIZE_COUPON_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.customizeCouponId = iprot.readI64();
              struct.setCustomizeCouponIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetOrderId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'orderId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetKugouId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetCouponTypeId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'couponTypeId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetCfromId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'cfromId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetPId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'pId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetActionId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'actionId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetReqTimestamp()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'reqTimestamp' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PutParamsVO struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ORDER_ID_FIELD_DESC);
      oprot.writeI64(struct.orderId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
      oprot.writeI64(struct.kugouId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(COUPON_TYPE_ID_FIELD_DESC);
      oprot.writeI64(struct.couponTypeId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(CFROM_ID_FIELD_DESC);
      oprot.writeI32(struct.cfromId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(P_ID_FIELD_DESC);
      oprot.writeI32(struct.pId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ACTION_ID_FIELD_DESC);
      oprot.writeI32(struct.actionId);
      oprot.writeFieldEnd();
      if (struct.jsonData != null) {
        oprot.writeFieldBegin(JSON_DATA_FIELD_DESC);
        oprot.writeString(struct.jsonData);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(REQ_TIMESTAMP_FIELD_DESC);
      oprot.writeI32(struct.reqTimestamp);
      oprot.writeFieldEnd();
      if (struct.sign != null) {
        oprot.writeFieldBegin(SIGN_FIELD_DESC);
        oprot.writeString(struct.sign);
        oprot.writeFieldEnd();
      }
      if (struct.isSetCustomizeCouponId()) {
        oprot.writeFieldBegin(CUSTOMIZE_COUPON_ID_FIELD_DESC);
        oprot.writeI64(struct.customizeCouponId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PutParamsVOTupleSchemeFactory implements SchemeFactory {
    public PutParamsVOTupleScheme getScheme() {
      return new PutParamsVOTupleScheme();
    }
  }

  private static class PutParamsVOTupleScheme extends TupleScheme<PutParamsVO> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PutParamsVO struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeI64(struct.orderId);
      oprot.writeI64(struct.kugouId);
      oprot.writeI64(struct.couponTypeId);
      oprot.writeI32(struct.cfromId);
      oprot.writeI32(struct.pId);
      oprot.writeI32(struct.actionId);
      oprot.writeI32(struct.reqTimestamp);
      oprot.writeString(struct.sign);
      BitSet optionals = new BitSet();
      if (struct.isSetJsonData()) {
        optionals.set(0);
      }
      if (struct.isSetCustomizeCouponId()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetJsonData()) {
        oprot.writeString(struct.jsonData);
      }
      if (struct.isSetCustomizeCouponId()) {
        oprot.writeI64(struct.customizeCouponId);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PutParamsVO struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.orderId = iprot.readI64();
      struct.setOrderIdIsSet(true);
      struct.kugouId = iprot.readI64();
      struct.setKugouIdIsSet(true);
      struct.couponTypeId = iprot.readI64();
      struct.setCouponTypeIdIsSet(true);
      struct.cfromId = iprot.readI32();
      struct.setCfromIdIsSet(true);
      struct.pId = iprot.readI32();
      struct.setPIdIsSet(true);
      struct.actionId = iprot.readI32();
      struct.setActionIdIsSet(true);
      struct.reqTimestamp = iprot.readI32();
      struct.setReqTimestampIsSet(true);
      struct.sign = iprot.readString();
      struct.setSignIsSet(true);
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.jsonData = iprot.readString();
        struct.setJsonDataIsSet(true);
      }
      if (incoming.get(1)) {
        struct.customizeCouponId = iprot.readI64();
        struct.setCustomizeCouponIdIsSet(true);
      }
    }
  }

}

