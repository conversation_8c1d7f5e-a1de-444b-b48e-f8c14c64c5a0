/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.coupon.thrift;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2020-03-10")
public class CouponDetailVO implements org.apache.thrift.TBase<CouponDetailVO, CouponDetailVO._Fields>, java.io.Serializable, Cloneable, Comparable<CouponDetailVO> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CouponDetailVO");

  private static final org.apache.thrift.protocol.TField COUPON_IMG_FIELD_DESC = new org.apache.thrift.protocol.TField("couponImg", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField COUPON_COLOR_FIELD_DESC = new org.apache.thrift.protocol.TField("couponColor", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField COIN_FIELD_DESC = new org.apache.thrift.protocol.TField("coin", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField COUPON_DOC_FIELD_DESC = new org.apache.thrift.protocol.TField("couponDoc", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField EXPIRE_DATE_FIELD_DESC = new org.apache.thrift.protocol.TField("expireDate", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField COUPON_NO_FIELD_DESC = new org.apache.thrift.protocol.TField("couponNo", org.apache.thrift.protocol.TType.STRING, (short)6);
  private static final org.apache.thrift.protocol.TField COUPON_CONFIG_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("couponConfigId", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField COUPON_STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("couponStatus", org.apache.thrift.protocol.TType.I32, (short)8);
  private static final org.apache.thrift.protocol.TField COUPON_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("couponType", org.apache.thrift.protocol.TType.I32, (short)9);
  private static final org.apache.thrift.protocol.TField COUPON_GRANT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("couponGrantId", org.apache.thrift.protocol.TType.I64, (short)10);
  private static final org.apache.thrift.protocol.TField IS_HAS_REBAT_FIELD_DESC = new org.apache.thrift.protocol.TField("isHasRebat", org.apache.thrift.protocol.TType.I32, (short)11);
  private static final org.apache.thrift.protocol.TField SKIP_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("skipType", org.apache.thrift.protocol.TType.I32, (short)12);
  private static final org.apache.thrift.protocol.TField COUPON_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("couponName", org.apache.thrift.protocol.TType.STRING, (short)13);
  private static final org.apache.thrift.protocol.TField CATEGORY_FIELD_DESC = new org.apache.thrift.protocol.TField("category", org.apache.thrift.protocol.TType.I32, (short)14);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new CouponDetailVOStandardSchemeFactory());
    schemes.put(TupleScheme.class, new CouponDetailVOTupleSchemeFactory());
  }

  public String couponImg; // required
  public String couponColor; // required
  public int coin; // required
  public String couponDoc; // required
  public long expireDate; // required
  public String couponNo; // required
  public String couponConfigId; // required
  public int couponStatus; // required
  public int couponType; // required
  public long couponGrantId; // required
  public int isHasRebat; // required
  public int skipType; // required
  public String couponName; // required
  public int category; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    COUPON_IMG((short)1, "couponImg"),
    COUPON_COLOR((short)2, "couponColor"),
    COIN((short)3, "coin"),
    COUPON_DOC((short)4, "couponDoc"),
    EXPIRE_DATE((short)5, "expireDate"),
    COUPON_NO((short)6, "couponNo"),
    COUPON_CONFIG_ID((short)7, "couponConfigId"),
    COUPON_STATUS((short)8, "couponStatus"),
    COUPON_TYPE((short)9, "couponType"),
    COUPON_GRANT_ID((short)10, "couponGrantId"),
    IS_HAS_REBAT((short)11, "isHasRebat"),
    SKIP_TYPE((short)12, "skipType"),
    COUPON_NAME((short)13, "couponName"),
    CATEGORY((short)14, "category");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // COUPON_IMG
          return COUPON_IMG;
        case 2: // COUPON_COLOR
          return COUPON_COLOR;
        case 3: // COIN
          return COIN;
        case 4: // COUPON_DOC
          return COUPON_DOC;
        case 5: // EXPIRE_DATE
          return EXPIRE_DATE;
        case 6: // COUPON_NO
          return COUPON_NO;
        case 7: // COUPON_CONFIG_ID
          return COUPON_CONFIG_ID;
        case 8: // COUPON_STATUS
          return COUPON_STATUS;
        case 9: // COUPON_TYPE
          return COUPON_TYPE;
        case 10: // COUPON_GRANT_ID
          return COUPON_GRANT_ID;
        case 11: // IS_HAS_REBAT
          return IS_HAS_REBAT;
        case 12: // SKIP_TYPE
          return SKIP_TYPE;
        case 13: // COUPON_NAME
          return COUPON_NAME;
        case 14: // CATEGORY
          return CATEGORY;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __COIN_ISSET_ID = 0;
  private static final int __EXPIREDATE_ISSET_ID = 1;
  private static final int __COUPONSTATUS_ISSET_ID = 2;
  private static final int __COUPONTYPE_ISSET_ID = 3;
  private static final int __COUPONGRANTID_ISSET_ID = 4;
  private static final int __ISHASREBAT_ISSET_ID = 5;
  private static final int __SKIPTYPE_ISSET_ID = 6;
  private static final int __CATEGORY_ISSET_ID = 7;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.COUPON_IMG, new org.apache.thrift.meta_data.FieldMetaData("couponImg", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.COUPON_COLOR, new org.apache.thrift.meta_data.FieldMetaData("couponColor", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.COIN, new org.apache.thrift.meta_data.FieldMetaData("coin", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.COUPON_DOC, new org.apache.thrift.meta_data.FieldMetaData("couponDoc", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXPIRE_DATE, new org.apache.thrift.meta_data.FieldMetaData("expireDate", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.COUPON_NO, new org.apache.thrift.meta_data.FieldMetaData("couponNo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.COUPON_CONFIG_ID, new org.apache.thrift.meta_data.FieldMetaData("couponConfigId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.COUPON_STATUS, new org.apache.thrift.meta_data.FieldMetaData("couponStatus", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.COUPON_TYPE, new org.apache.thrift.meta_data.FieldMetaData("couponType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.COUPON_GRANT_ID, new org.apache.thrift.meta_data.FieldMetaData("couponGrantId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.IS_HAS_REBAT, new org.apache.thrift.meta_data.FieldMetaData("isHasRebat", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.SKIP_TYPE, new org.apache.thrift.meta_data.FieldMetaData("skipType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.COUPON_NAME, new org.apache.thrift.meta_data.FieldMetaData("couponName", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CATEGORY, new org.apache.thrift.meta_data.FieldMetaData("category", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CouponDetailVO.class, metaDataMap);
  }

  public CouponDetailVO() {
  }

  public CouponDetailVO(
    String couponImg,
    String couponColor,
    int coin,
    String couponDoc,
    long expireDate,
    String couponNo,
    String couponConfigId,
    int couponStatus,
    int couponType,
    long couponGrantId,
    int isHasRebat,
    int skipType,
    String couponName,
    int category)
  {
    this();
    this.couponImg = couponImg;
    this.couponColor = couponColor;
    this.coin = coin;
    setCoinIsSet(true);
    this.couponDoc = couponDoc;
    this.expireDate = expireDate;
    setExpireDateIsSet(true);
    this.couponNo = couponNo;
    this.couponConfigId = couponConfigId;
    this.couponStatus = couponStatus;
    setCouponStatusIsSet(true);
    this.couponType = couponType;
    setCouponTypeIsSet(true);
    this.couponGrantId = couponGrantId;
    setCouponGrantIdIsSet(true);
    this.isHasRebat = isHasRebat;
    setIsHasRebatIsSet(true);
    this.skipType = skipType;
    setSkipTypeIsSet(true);
    this.couponName = couponName;
    this.category = category;
    setCategoryIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CouponDetailVO(CouponDetailVO other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetCouponImg()) {
      this.couponImg = other.couponImg;
    }
    if (other.isSetCouponColor()) {
      this.couponColor = other.couponColor;
    }
    this.coin = other.coin;
    if (other.isSetCouponDoc()) {
      this.couponDoc = other.couponDoc;
    }
    this.expireDate = other.expireDate;
    if (other.isSetCouponNo()) {
      this.couponNo = other.couponNo;
    }
    if (other.isSetCouponConfigId()) {
      this.couponConfigId = other.couponConfigId;
    }
    this.couponStatus = other.couponStatus;
    this.couponType = other.couponType;
    this.couponGrantId = other.couponGrantId;
    this.isHasRebat = other.isHasRebat;
    this.skipType = other.skipType;
    if (other.isSetCouponName()) {
      this.couponName = other.couponName;
    }
    this.category = other.category;
  }

  public CouponDetailVO deepCopy() {
    return new CouponDetailVO(this);
  }

  @Override
  public void clear() {
    this.couponImg = null;
    this.couponColor = null;
    setCoinIsSet(false);
    this.coin = 0;
    this.couponDoc = null;
    setExpireDateIsSet(false);
    this.expireDate = 0;
    this.couponNo = null;
    this.couponConfigId = null;
    setCouponStatusIsSet(false);
    this.couponStatus = 0;
    setCouponTypeIsSet(false);
    this.couponType = 0;
    setCouponGrantIdIsSet(false);
    this.couponGrantId = 0;
    setIsHasRebatIsSet(false);
    this.isHasRebat = 0;
    setSkipTypeIsSet(false);
    this.skipType = 0;
    this.couponName = null;
    setCategoryIsSet(false);
    this.category = 0;
  }

  public String getCouponImg() {
    return this.couponImg;
  }

  public CouponDetailVO setCouponImg(String couponImg) {
    this.couponImg = couponImg;
    return this;
  }

  public void unsetCouponImg() {
    this.couponImg = null;
  }

  /** Returns true if field couponImg is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponImg() {
    return this.couponImg != null;
  }

  public void setCouponImgIsSet(boolean value) {
    if (!value) {
      this.couponImg = null;
    }
  }

  public String getCouponColor() {
    return this.couponColor;
  }

  public CouponDetailVO setCouponColor(String couponColor) {
    this.couponColor = couponColor;
    return this;
  }

  public void unsetCouponColor() {
    this.couponColor = null;
  }

  /** Returns true if field couponColor is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponColor() {
    return this.couponColor != null;
  }

  public void setCouponColorIsSet(boolean value) {
    if (!value) {
      this.couponColor = null;
    }
  }

  public int getCoin() {
    return this.coin;
  }

  public CouponDetailVO setCoin(int coin) {
    this.coin = coin;
    setCoinIsSet(true);
    return this;
  }

  public void unsetCoin() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COIN_ISSET_ID);
  }

  /** Returns true if field coin is set (has been assigned a value) and false otherwise */
  public boolean isSetCoin() {
    return EncodingUtils.testBit(__isset_bitfield, __COIN_ISSET_ID);
  }

  public void setCoinIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COIN_ISSET_ID, value);
  }

  public String getCouponDoc() {
    return this.couponDoc;
  }

  public CouponDetailVO setCouponDoc(String couponDoc) {
    this.couponDoc = couponDoc;
    return this;
  }

  public void unsetCouponDoc() {
    this.couponDoc = null;
  }

  /** Returns true if field couponDoc is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponDoc() {
    return this.couponDoc != null;
  }

  public void setCouponDocIsSet(boolean value) {
    if (!value) {
      this.couponDoc = null;
    }
  }

  public long getExpireDate() {
    return this.expireDate;
  }

  public CouponDetailVO setExpireDate(long expireDate) {
    this.expireDate = expireDate;
    setExpireDateIsSet(true);
    return this;
  }

  public void unsetExpireDate() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __EXPIREDATE_ISSET_ID);
  }

  /** Returns true if field expireDate is set (has been assigned a value) and false otherwise */
  public boolean isSetExpireDate() {
    return EncodingUtils.testBit(__isset_bitfield, __EXPIREDATE_ISSET_ID);
  }

  public void setExpireDateIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __EXPIREDATE_ISSET_ID, value);
  }

  public String getCouponNo() {
    return this.couponNo;
  }

  public CouponDetailVO setCouponNo(String couponNo) {
    this.couponNo = couponNo;
    return this;
  }

  public void unsetCouponNo() {
    this.couponNo = null;
  }

  /** Returns true if field couponNo is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponNo() {
    return this.couponNo != null;
  }

  public void setCouponNoIsSet(boolean value) {
    if (!value) {
      this.couponNo = null;
    }
  }

  public String getCouponConfigId() {
    return this.couponConfigId;
  }

  public CouponDetailVO setCouponConfigId(String couponConfigId) {
    this.couponConfigId = couponConfigId;
    return this;
  }

  public void unsetCouponConfigId() {
    this.couponConfigId = null;
  }

  /** Returns true if field couponConfigId is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponConfigId() {
    return this.couponConfigId != null;
  }

  public void setCouponConfigIdIsSet(boolean value) {
    if (!value) {
      this.couponConfigId = null;
    }
  }

  public int getCouponStatus() {
    return this.couponStatus;
  }

  public CouponDetailVO setCouponStatus(int couponStatus) {
    this.couponStatus = couponStatus;
    setCouponStatusIsSet(true);
    return this;
  }

  public void unsetCouponStatus() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COUPONSTATUS_ISSET_ID);
  }

  /** Returns true if field couponStatus is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponStatus() {
    return EncodingUtils.testBit(__isset_bitfield, __COUPONSTATUS_ISSET_ID);
  }

  public void setCouponStatusIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COUPONSTATUS_ISSET_ID, value);
  }

  public int getCouponType() {
    return this.couponType;
  }

  public CouponDetailVO setCouponType(int couponType) {
    this.couponType = couponType;
    setCouponTypeIsSet(true);
    return this;
  }

  public void unsetCouponType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COUPONTYPE_ISSET_ID);
  }

  /** Returns true if field couponType is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponType() {
    return EncodingUtils.testBit(__isset_bitfield, __COUPONTYPE_ISSET_ID);
  }

  public void setCouponTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COUPONTYPE_ISSET_ID, value);
  }

  public long getCouponGrantId() {
    return this.couponGrantId;
  }

  public CouponDetailVO setCouponGrantId(long couponGrantId) {
    this.couponGrantId = couponGrantId;
    setCouponGrantIdIsSet(true);
    return this;
  }

  public void unsetCouponGrantId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COUPONGRANTID_ISSET_ID);
  }

  /** Returns true if field couponGrantId is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponGrantId() {
    return EncodingUtils.testBit(__isset_bitfield, __COUPONGRANTID_ISSET_ID);
  }

  public void setCouponGrantIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COUPONGRANTID_ISSET_ID, value);
  }

  public int getIsHasRebat() {
    return this.isHasRebat;
  }

  public CouponDetailVO setIsHasRebat(int isHasRebat) {
    this.isHasRebat = isHasRebat;
    setIsHasRebatIsSet(true);
    return this;
  }

  public void unsetIsHasRebat() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ISHASREBAT_ISSET_ID);
  }

  /** Returns true if field isHasRebat is set (has been assigned a value) and false otherwise */
  public boolean isSetIsHasRebat() {
    return EncodingUtils.testBit(__isset_bitfield, __ISHASREBAT_ISSET_ID);
  }

  public void setIsHasRebatIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ISHASREBAT_ISSET_ID, value);
  }

  public int getSkipType() {
    return this.skipType;
  }

  public CouponDetailVO setSkipType(int skipType) {
    this.skipType = skipType;
    setSkipTypeIsSet(true);
    return this;
  }

  public void unsetSkipType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SKIPTYPE_ISSET_ID);
  }

  /** Returns true if field skipType is set (has been assigned a value) and false otherwise */
  public boolean isSetSkipType() {
    return EncodingUtils.testBit(__isset_bitfield, __SKIPTYPE_ISSET_ID);
  }

  public void setSkipTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SKIPTYPE_ISSET_ID, value);
  }

  public String getCouponName() {
    return this.couponName;
  }

  public CouponDetailVO setCouponName(String couponName) {
    this.couponName = couponName;
    return this;
  }

  public void unsetCouponName() {
    this.couponName = null;
  }

  /** Returns true if field couponName is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponName() {
    return this.couponName != null;
  }

  public void setCouponNameIsSet(boolean value) {
    if (!value) {
      this.couponName = null;
    }
  }

  public int getCategory() {
    return this.category;
  }

  public CouponDetailVO setCategory(int category) {
    this.category = category;
    setCategoryIsSet(true);
    return this;
  }

  public void unsetCategory() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CATEGORY_ISSET_ID);
  }

  /** Returns true if field category is set (has been assigned a value) and false otherwise */
  public boolean isSetCategory() {
    return EncodingUtils.testBit(__isset_bitfield, __CATEGORY_ISSET_ID);
  }

  public void setCategoryIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CATEGORY_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case COUPON_IMG:
      if (value == null) {
        unsetCouponImg();
      } else {
        setCouponImg((String)value);
      }
      break;

    case COUPON_COLOR:
      if (value == null) {
        unsetCouponColor();
      } else {
        setCouponColor((String)value);
      }
      break;

    case COIN:
      if (value == null) {
        unsetCoin();
      } else {
        setCoin((Integer)value);
      }
      break;

    case COUPON_DOC:
      if (value == null) {
        unsetCouponDoc();
      } else {
        setCouponDoc((String)value);
      }
      break;

    case EXPIRE_DATE:
      if (value == null) {
        unsetExpireDate();
      } else {
        setExpireDate((Long)value);
      }
      break;

    case COUPON_NO:
      if (value == null) {
        unsetCouponNo();
      } else {
        setCouponNo((String)value);
      }
      break;

    case COUPON_CONFIG_ID:
      if (value == null) {
        unsetCouponConfigId();
      } else {
        setCouponConfigId((String)value);
      }
      break;

    case COUPON_STATUS:
      if (value == null) {
        unsetCouponStatus();
      } else {
        setCouponStatus((Integer)value);
      }
      break;

    case COUPON_TYPE:
      if (value == null) {
        unsetCouponType();
      } else {
        setCouponType((Integer)value);
      }
      break;

    case COUPON_GRANT_ID:
      if (value == null) {
        unsetCouponGrantId();
      } else {
        setCouponGrantId((Long)value);
      }
      break;

    case IS_HAS_REBAT:
      if (value == null) {
        unsetIsHasRebat();
      } else {
        setIsHasRebat((Integer)value);
      }
      break;

    case SKIP_TYPE:
      if (value == null) {
        unsetSkipType();
      } else {
        setSkipType((Integer)value);
      }
      break;

    case COUPON_NAME:
      if (value == null) {
        unsetCouponName();
      } else {
        setCouponName((String)value);
      }
      break;

    case CATEGORY:
      if (value == null) {
        unsetCategory();
      } else {
        setCategory((Integer)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case COUPON_IMG:
      return getCouponImg();

    case COUPON_COLOR:
      return getCouponColor();

    case COIN:
      return getCoin();

    case COUPON_DOC:
      return getCouponDoc();

    case EXPIRE_DATE:
      return getExpireDate();

    case COUPON_NO:
      return getCouponNo();

    case COUPON_CONFIG_ID:
      return getCouponConfigId();

    case COUPON_STATUS:
      return getCouponStatus();

    case COUPON_TYPE:
      return getCouponType();

    case COUPON_GRANT_ID:
      return getCouponGrantId();

    case IS_HAS_REBAT:
      return getIsHasRebat();

    case SKIP_TYPE:
      return getSkipType();

    case COUPON_NAME:
      return getCouponName();

    case CATEGORY:
      return getCategory();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case COUPON_IMG:
      return isSetCouponImg();
    case COUPON_COLOR:
      return isSetCouponColor();
    case COIN:
      return isSetCoin();
    case COUPON_DOC:
      return isSetCouponDoc();
    case EXPIRE_DATE:
      return isSetExpireDate();
    case COUPON_NO:
      return isSetCouponNo();
    case COUPON_CONFIG_ID:
      return isSetCouponConfigId();
    case COUPON_STATUS:
      return isSetCouponStatus();
    case COUPON_TYPE:
      return isSetCouponType();
    case COUPON_GRANT_ID:
      return isSetCouponGrantId();
    case IS_HAS_REBAT:
      return isSetIsHasRebat();
    case SKIP_TYPE:
      return isSetSkipType();
    case COUPON_NAME:
      return isSetCouponName();
    case CATEGORY:
      return isSetCategory();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof CouponDetailVO)
      return this.equals((CouponDetailVO)that);
    return false;
  }

  public boolean equals(CouponDetailVO that) {
    if (that == null)
      return false;

    boolean this_present_couponImg = true && this.isSetCouponImg();
    boolean that_present_couponImg = true && that.isSetCouponImg();
    if (this_present_couponImg || that_present_couponImg) {
      if (!(this_present_couponImg && that_present_couponImg))
        return false;
      if (!this.couponImg.equals(that.couponImg))
        return false;
    }

    boolean this_present_couponColor = true && this.isSetCouponColor();
    boolean that_present_couponColor = true && that.isSetCouponColor();
    if (this_present_couponColor || that_present_couponColor) {
      if (!(this_present_couponColor && that_present_couponColor))
        return false;
      if (!this.couponColor.equals(that.couponColor))
        return false;
    }

    boolean this_present_coin = true;
    boolean that_present_coin = true;
    if (this_present_coin || that_present_coin) {
      if (!(this_present_coin && that_present_coin))
        return false;
      if (this.coin != that.coin)
        return false;
    }

    boolean this_present_couponDoc = true && this.isSetCouponDoc();
    boolean that_present_couponDoc = true && that.isSetCouponDoc();
    if (this_present_couponDoc || that_present_couponDoc) {
      if (!(this_present_couponDoc && that_present_couponDoc))
        return false;
      if (!this.couponDoc.equals(that.couponDoc))
        return false;
    }

    boolean this_present_expireDate = true;
    boolean that_present_expireDate = true;
    if (this_present_expireDate || that_present_expireDate) {
      if (!(this_present_expireDate && that_present_expireDate))
        return false;
      if (this.expireDate != that.expireDate)
        return false;
    }

    boolean this_present_couponNo = true && this.isSetCouponNo();
    boolean that_present_couponNo = true && that.isSetCouponNo();
    if (this_present_couponNo || that_present_couponNo) {
      if (!(this_present_couponNo && that_present_couponNo))
        return false;
      if (!this.couponNo.equals(that.couponNo))
        return false;
    }

    boolean this_present_couponConfigId = true && this.isSetCouponConfigId();
    boolean that_present_couponConfigId = true && that.isSetCouponConfigId();
    if (this_present_couponConfigId || that_present_couponConfigId) {
      if (!(this_present_couponConfigId && that_present_couponConfigId))
        return false;
      if (!this.couponConfigId.equals(that.couponConfigId))
        return false;
    }

    boolean this_present_couponStatus = true;
    boolean that_present_couponStatus = true;
    if (this_present_couponStatus || that_present_couponStatus) {
      if (!(this_present_couponStatus && that_present_couponStatus))
        return false;
      if (this.couponStatus != that.couponStatus)
        return false;
    }

    boolean this_present_couponType = true;
    boolean that_present_couponType = true;
    if (this_present_couponType || that_present_couponType) {
      if (!(this_present_couponType && that_present_couponType))
        return false;
      if (this.couponType != that.couponType)
        return false;
    }

    boolean this_present_couponGrantId = true;
    boolean that_present_couponGrantId = true;
    if (this_present_couponGrantId || that_present_couponGrantId) {
      if (!(this_present_couponGrantId && that_present_couponGrantId))
        return false;
      if (this.couponGrantId != that.couponGrantId)
        return false;
    }

    boolean this_present_isHasRebat = true;
    boolean that_present_isHasRebat = true;
    if (this_present_isHasRebat || that_present_isHasRebat) {
      if (!(this_present_isHasRebat && that_present_isHasRebat))
        return false;
      if (this.isHasRebat != that.isHasRebat)
        return false;
    }

    boolean this_present_skipType = true;
    boolean that_present_skipType = true;
    if (this_present_skipType || that_present_skipType) {
      if (!(this_present_skipType && that_present_skipType))
        return false;
      if (this.skipType != that.skipType)
        return false;
    }

    boolean this_present_couponName = true && this.isSetCouponName();
    boolean that_present_couponName = true && that.isSetCouponName();
    if (this_present_couponName || that_present_couponName) {
      if (!(this_present_couponName && that_present_couponName))
        return false;
      if (!this.couponName.equals(that.couponName))
        return false;
    }

    boolean this_present_category = true;
    boolean that_present_category = true;
    if (this_present_category || that_present_category) {
      if (!(this_present_category && that_present_category))
        return false;
      if (this.category != that.category)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_couponImg = true && (isSetCouponImg());
    list.add(present_couponImg);
    if (present_couponImg)
      list.add(couponImg);

    boolean present_couponColor = true && (isSetCouponColor());
    list.add(present_couponColor);
    if (present_couponColor)
      list.add(couponColor);

    boolean present_coin = true;
    list.add(present_coin);
    if (present_coin)
      list.add(coin);

    boolean present_couponDoc = true && (isSetCouponDoc());
    list.add(present_couponDoc);
    if (present_couponDoc)
      list.add(couponDoc);

    boolean present_expireDate = true;
    list.add(present_expireDate);
    if (present_expireDate)
      list.add(expireDate);

    boolean present_couponNo = true && (isSetCouponNo());
    list.add(present_couponNo);
    if (present_couponNo)
      list.add(couponNo);

    boolean present_couponConfigId = true && (isSetCouponConfigId());
    list.add(present_couponConfigId);
    if (present_couponConfigId)
      list.add(couponConfigId);

    boolean present_couponStatus = true;
    list.add(present_couponStatus);
    if (present_couponStatus)
      list.add(couponStatus);

    boolean present_couponType = true;
    list.add(present_couponType);
    if (present_couponType)
      list.add(couponType);

    boolean present_couponGrantId = true;
    list.add(present_couponGrantId);
    if (present_couponGrantId)
      list.add(couponGrantId);

    boolean present_isHasRebat = true;
    list.add(present_isHasRebat);
    if (present_isHasRebat)
      list.add(isHasRebat);

    boolean present_skipType = true;
    list.add(present_skipType);
    if (present_skipType)
      list.add(skipType);

    boolean present_couponName = true && (isSetCouponName());
    list.add(present_couponName);
    if (present_couponName)
      list.add(couponName);

    boolean present_category = true;
    list.add(present_category);
    if (present_category)
      list.add(category);

    return list.hashCode();
  }

  @Override
  public int compareTo(CouponDetailVO other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetCouponImg()).compareTo(other.isSetCouponImg());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponImg()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponImg, other.couponImg);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponColor()).compareTo(other.isSetCouponColor());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponColor()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponColor, other.couponColor);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCoin()).compareTo(other.isSetCoin());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCoin()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.coin, other.coin);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponDoc()).compareTo(other.isSetCouponDoc());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponDoc()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponDoc, other.couponDoc);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExpireDate()).compareTo(other.isSetExpireDate());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExpireDate()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.expireDate, other.expireDate);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponNo()).compareTo(other.isSetCouponNo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponNo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponNo, other.couponNo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponConfigId()).compareTo(other.isSetCouponConfigId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponConfigId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponConfigId, other.couponConfigId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponStatus()).compareTo(other.isSetCouponStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponStatus, other.couponStatus);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponType()).compareTo(other.isSetCouponType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponType, other.couponType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponGrantId()).compareTo(other.isSetCouponGrantId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponGrantId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponGrantId, other.couponGrantId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIsHasRebat()).compareTo(other.isSetIsHasRebat());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsHasRebat()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isHasRebat, other.isHasRebat);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSkipType()).compareTo(other.isSetSkipType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSkipType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.skipType, other.skipType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponName()).compareTo(other.isSetCouponName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponName, other.couponName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCategory()).compareTo(other.isSetCategory());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCategory()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.category, other.category);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("CouponDetailVO(");
    boolean first = true;

    sb.append("couponImg:");
    if (this.couponImg == null) {
      sb.append("null");
    } else {
      sb.append(this.couponImg);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponColor:");
    if (this.couponColor == null) {
      sb.append("null");
    } else {
      sb.append(this.couponColor);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("coin:");
    sb.append(this.coin);
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponDoc:");
    if (this.couponDoc == null) {
      sb.append("null");
    } else {
      sb.append(this.couponDoc);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("expireDate:");
    sb.append(this.expireDate);
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponNo:");
    if (this.couponNo == null) {
      sb.append("null");
    } else {
      sb.append(this.couponNo);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponConfigId:");
    if (this.couponConfigId == null) {
      sb.append("null");
    } else {
      sb.append(this.couponConfigId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponStatus:");
    sb.append(this.couponStatus);
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponType:");
    sb.append(this.couponType);
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponGrantId:");
    sb.append(this.couponGrantId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("isHasRebat:");
    sb.append(this.isHasRebat);
    first = false;
    if (!first) sb.append(", ");
    sb.append("skipType:");
    sb.append(this.skipType);
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponName:");
    if (this.couponName == null) {
      sb.append("null");
    } else {
      sb.append(this.couponName);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("category:");
    sb.append(this.category);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CouponDetailVOStandardSchemeFactory implements SchemeFactory {
    public CouponDetailVOStandardScheme getScheme() {
      return new CouponDetailVOStandardScheme();
    }
  }

  private static class CouponDetailVOStandardScheme extends StandardScheme<CouponDetailVO> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CouponDetailVO struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // COUPON_IMG
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.couponImg = iprot.readString();
              struct.setCouponImgIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // COUPON_COLOR
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.couponColor = iprot.readString();
              struct.setCouponColorIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // COIN
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.coin = iprot.readI32();
              struct.setCoinIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // COUPON_DOC
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.couponDoc = iprot.readString();
              struct.setCouponDocIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // EXPIRE_DATE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.expireDate = iprot.readI64();
              struct.setExpireDateIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // COUPON_NO
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.couponNo = iprot.readString();
              struct.setCouponNoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // COUPON_CONFIG_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.couponConfigId = iprot.readString();
              struct.setCouponConfigIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // COUPON_STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.couponStatus = iprot.readI32();
              struct.setCouponStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // COUPON_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.couponType = iprot.readI32();
              struct.setCouponTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // COUPON_GRANT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.couponGrantId = iprot.readI64();
              struct.setCouponGrantIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // IS_HAS_REBAT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.isHasRebat = iprot.readI32();
              struct.setIsHasRebatIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // SKIP_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.skipType = iprot.readI32();
              struct.setSkipTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // COUPON_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.couponName = iprot.readString();
              struct.setCouponNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 14: // CATEGORY
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.category = iprot.readI32();
              struct.setCategoryIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CouponDetailVO struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.couponImg != null) {
        oprot.writeFieldBegin(COUPON_IMG_FIELD_DESC);
        oprot.writeString(struct.couponImg);
        oprot.writeFieldEnd();
      }
      if (struct.couponColor != null) {
        oprot.writeFieldBegin(COUPON_COLOR_FIELD_DESC);
        oprot.writeString(struct.couponColor);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(COIN_FIELD_DESC);
      oprot.writeI32(struct.coin);
      oprot.writeFieldEnd();
      if (struct.couponDoc != null) {
        oprot.writeFieldBegin(COUPON_DOC_FIELD_DESC);
        oprot.writeString(struct.couponDoc);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(EXPIRE_DATE_FIELD_DESC);
      oprot.writeI64(struct.expireDate);
      oprot.writeFieldEnd();
      if (struct.couponNo != null) {
        oprot.writeFieldBegin(COUPON_NO_FIELD_DESC);
        oprot.writeString(struct.couponNo);
        oprot.writeFieldEnd();
      }
      if (struct.couponConfigId != null) {
        oprot.writeFieldBegin(COUPON_CONFIG_ID_FIELD_DESC);
        oprot.writeString(struct.couponConfigId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(COUPON_STATUS_FIELD_DESC);
      oprot.writeI32(struct.couponStatus);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(COUPON_TYPE_FIELD_DESC);
      oprot.writeI32(struct.couponType);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(COUPON_GRANT_ID_FIELD_DESC);
      oprot.writeI64(struct.couponGrantId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(IS_HAS_REBAT_FIELD_DESC);
      oprot.writeI32(struct.isHasRebat);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SKIP_TYPE_FIELD_DESC);
      oprot.writeI32(struct.skipType);
      oprot.writeFieldEnd();
      if (struct.couponName != null) {
        oprot.writeFieldBegin(COUPON_NAME_FIELD_DESC);
        oprot.writeString(struct.couponName);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(CATEGORY_FIELD_DESC);
      oprot.writeI32(struct.category);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CouponDetailVOTupleSchemeFactory implements SchemeFactory {
    public CouponDetailVOTupleScheme getScheme() {
      return new CouponDetailVOTupleScheme();
    }
  }

  private static class CouponDetailVOTupleScheme extends TupleScheme<CouponDetailVO> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CouponDetailVO struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetCouponImg()) {
        optionals.set(0);
      }
      if (struct.isSetCouponColor()) {
        optionals.set(1);
      }
      if (struct.isSetCoin()) {
        optionals.set(2);
      }
      if (struct.isSetCouponDoc()) {
        optionals.set(3);
      }
      if (struct.isSetExpireDate()) {
        optionals.set(4);
      }
      if (struct.isSetCouponNo()) {
        optionals.set(5);
      }
      if (struct.isSetCouponConfigId()) {
        optionals.set(6);
      }
      if (struct.isSetCouponStatus()) {
        optionals.set(7);
      }
      if (struct.isSetCouponType()) {
        optionals.set(8);
      }
      if (struct.isSetCouponGrantId()) {
        optionals.set(9);
      }
      if (struct.isSetIsHasRebat()) {
        optionals.set(10);
      }
      if (struct.isSetSkipType()) {
        optionals.set(11);
      }
      if (struct.isSetCouponName()) {
        optionals.set(12);
      }
      if (struct.isSetCategory()) {
        optionals.set(13);
      }
      oprot.writeBitSet(optionals, 14);
      if (struct.isSetCouponImg()) {
        oprot.writeString(struct.couponImg);
      }
      if (struct.isSetCouponColor()) {
        oprot.writeString(struct.couponColor);
      }
      if (struct.isSetCoin()) {
        oprot.writeI32(struct.coin);
      }
      if (struct.isSetCouponDoc()) {
        oprot.writeString(struct.couponDoc);
      }
      if (struct.isSetExpireDate()) {
        oprot.writeI64(struct.expireDate);
      }
      if (struct.isSetCouponNo()) {
        oprot.writeString(struct.couponNo);
      }
      if (struct.isSetCouponConfigId()) {
        oprot.writeString(struct.couponConfigId);
      }
      if (struct.isSetCouponStatus()) {
        oprot.writeI32(struct.couponStatus);
      }
      if (struct.isSetCouponType()) {
        oprot.writeI32(struct.couponType);
      }
      if (struct.isSetCouponGrantId()) {
        oprot.writeI64(struct.couponGrantId);
      }
      if (struct.isSetIsHasRebat()) {
        oprot.writeI32(struct.isHasRebat);
      }
      if (struct.isSetSkipType()) {
        oprot.writeI32(struct.skipType);
      }
      if (struct.isSetCouponName()) {
        oprot.writeString(struct.couponName);
      }
      if (struct.isSetCategory()) {
        oprot.writeI32(struct.category);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CouponDetailVO struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(14);
      if (incoming.get(0)) {
        struct.couponImg = iprot.readString();
        struct.setCouponImgIsSet(true);
      }
      if (incoming.get(1)) {
        struct.couponColor = iprot.readString();
        struct.setCouponColorIsSet(true);
      }
      if (incoming.get(2)) {
        struct.coin = iprot.readI32();
        struct.setCoinIsSet(true);
      }
      if (incoming.get(3)) {
        struct.couponDoc = iprot.readString();
        struct.setCouponDocIsSet(true);
      }
      if (incoming.get(4)) {
        struct.expireDate = iprot.readI64();
        struct.setExpireDateIsSet(true);
      }
      if (incoming.get(5)) {
        struct.couponNo = iprot.readString();
        struct.setCouponNoIsSet(true);
      }
      if (incoming.get(6)) {
        struct.couponConfigId = iprot.readString();
        struct.setCouponConfigIdIsSet(true);
      }
      if (incoming.get(7)) {
        struct.couponStatus = iprot.readI32();
        struct.setCouponStatusIsSet(true);
      }
      if (incoming.get(8)) {
        struct.couponType = iprot.readI32();
        struct.setCouponTypeIsSet(true);
      }
      if (incoming.get(9)) {
        struct.couponGrantId = iprot.readI64();
        struct.setCouponGrantIdIsSet(true);
      }
      if (incoming.get(10)) {
        struct.isHasRebat = iprot.readI32();
        struct.setIsHasRebatIsSet(true);
      }
      if (incoming.get(11)) {
        struct.skipType = iprot.readI32();
        struct.setSkipTypeIsSet(true);
      }
      if (incoming.get(12)) {
        struct.couponName = iprot.readString();
        struct.setCouponNameIsSet(true);
      }
      if (incoming.get(13)) {
        struct.category = iprot.readI32();
        struct.setCategoryIsSet(true);
      }
    }
  }

}

