/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.coupon.thrift;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2020-03-10")
public class CouponService {

  public interface Iface {

    public ReturnResult put(PutParamsVO params) throws org.apache.thrift.TException;

    public ReturnResult freeze(FreezeParamsVO params, ConsumeVO consumeVo) throws org.apache.thrift.TException;

    public ReturnResult freezeConfirm(UnFreezeParamsVO params) throws org.apache.thrift.TException;

    public ReturnResult freezeCancel(UnFreezeParamsVO params) throws org.apache.thrift.TException;

    public ReturnResult simpleFreeze(FreezeParamsVO params) throws org.apache.thrift.TException;

    public ReturnResult confirm(UnFreezeVO params) throws org.apache.thrift.TException;

    public ReturnResult cancel(UnFreezeVO params) throws org.apache.thrift.TException;

    public CouponListResult queryByCFrom(long kugouId, int cFromId, short couponStat, int actionId) throws org.apache.thrift.TException;

    public CouponCountResult countByCFrom(long kugouId, int cFromId, int actionId) throws org.apache.thrift.TException;

    public CouponQueryResult queryCoupon(long kugouId, long couponId, int actionId) throws org.apache.thrift.TException;

    public PersonalCouponNumResult getPersonalCouponNum(CouponListRequestParam param) throws org.apache.thrift.TException;

    public PersonalCouponListResult getPersonalCouponList(CouponListRequestParam param) throws org.apache.thrift.TException;

    public DrawCouponResult drawCoupon(DrawCouponParam param) throws org.apache.thrift.TException;

    public GetRebateCouponsResult getRebateCouponList(GetRebateCouponsParams params) throws org.apache.thrift.TException;

    public ActionIdConfigQueryResult queryActionIdConfig(ActionIdConfigRequest params) throws org.apache.thrift.TException;

  }

  public interface AsyncIface {

    public void put(PutParamsVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void freeze(FreezeParamsVO params, ConsumeVO consumeVo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void freezeConfirm(UnFreezeParamsVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void freezeCancel(UnFreezeParamsVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void simpleFreeze(FreezeParamsVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void confirm(UnFreezeVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void cancel(UnFreezeVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void queryByCFrom(long kugouId, int cFromId, short couponStat, int actionId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void countByCFrom(long kugouId, int cFromId, int actionId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void queryCoupon(long kugouId, long couponId, int actionId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getPersonalCouponNum(CouponListRequestParam param, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getPersonalCouponList(CouponListRequestParam param, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void drawCoupon(DrawCouponParam param, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void getRebateCouponList(GetRebateCouponsParams params, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

    public void queryActionIdConfig(ActionIdConfigRequest params, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

  }

  public static class Client extends org.apache.thrift.TServiceClient implements Iface {
    public static class Factory implements org.apache.thrift.TServiceClientFactory<Client> {
      public Factory() {}
      public Client getClient(org.apache.thrift.protocol.TProtocol prot) {
        return new Client(prot);
      }
      public Client getClient(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
        return new Client(iprot, oprot);
      }
    }

    public Client(org.apache.thrift.protocol.TProtocol prot)
    {
      super(prot, prot);
    }

    public Client(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
      super(iprot, oprot);
    }

    public ReturnResult put(PutParamsVO params) throws org.apache.thrift.TException
    {
      send_put(params);
      return recv_put();
    }

    public void send_put(PutParamsVO params) throws org.apache.thrift.TException
    {
      put_args args = new put_args();
      args.setParams(params);
      sendBase("put", args);
    }

    public ReturnResult recv_put() throws org.apache.thrift.TException
    {
      put_result result = new put_result();
      receiveBase(result, "put");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "put failed: unknown result");
    }

    public ReturnResult freeze(FreezeParamsVO params, ConsumeVO consumeVo) throws org.apache.thrift.TException
    {
      send_freeze(params, consumeVo);
      return recv_freeze();
    }

    public void send_freeze(FreezeParamsVO params, ConsumeVO consumeVo) throws org.apache.thrift.TException
    {
      freeze_args args = new freeze_args();
      args.setParams(params);
      args.setConsumeVo(consumeVo);
      sendBase("freeze", args);
    }

    public ReturnResult recv_freeze() throws org.apache.thrift.TException
    {
      freeze_result result = new freeze_result();
      receiveBase(result, "freeze");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "freeze failed: unknown result");
    }

    public ReturnResult freezeConfirm(UnFreezeParamsVO params) throws org.apache.thrift.TException
    {
      send_freezeConfirm(params);
      return recv_freezeConfirm();
    }

    public void send_freezeConfirm(UnFreezeParamsVO params) throws org.apache.thrift.TException
    {
      freezeConfirm_args args = new freezeConfirm_args();
      args.setParams(params);
      sendBase("freezeConfirm", args);
    }

    public ReturnResult recv_freezeConfirm() throws org.apache.thrift.TException
    {
      freezeConfirm_result result = new freezeConfirm_result();
      receiveBase(result, "freezeConfirm");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "freezeConfirm failed: unknown result");
    }

    public ReturnResult freezeCancel(UnFreezeParamsVO params) throws org.apache.thrift.TException
    {
      send_freezeCancel(params);
      return recv_freezeCancel();
    }

    public void send_freezeCancel(UnFreezeParamsVO params) throws org.apache.thrift.TException
    {
      freezeCancel_args args = new freezeCancel_args();
      args.setParams(params);
      sendBase("freezeCancel", args);
    }

    public ReturnResult recv_freezeCancel() throws org.apache.thrift.TException
    {
      freezeCancel_result result = new freezeCancel_result();
      receiveBase(result, "freezeCancel");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "freezeCancel failed: unknown result");
    }

    public ReturnResult simpleFreeze(FreezeParamsVO params) throws org.apache.thrift.TException
    {
      send_simpleFreeze(params);
      return recv_simpleFreeze();
    }

    public void send_simpleFreeze(FreezeParamsVO params) throws org.apache.thrift.TException
    {
      simpleFreeze_args args = new simpleFreeze_args();
      args.setParams(params);
      sendBase("simpleFreeze", args);
    }

    public ReturnResult recv_simpleFreeze() throws org.apache.thrift.TException
    {
      simpleFreeze_result result = new simpleFreeze_result();
      receiveBase(result, "simpleFreeze");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "simpleFreeze failed: unknown result");
    }

    public ReturnResult confirm(UnFreezeVO params) throws org.apache.thrift.TException
    {
      send_confirm(params);
      return recv_confirm();
    }

    public void send_confirm(UnFreezeVO params) throws org.apache.thrift.TException
    {
      confirm_args args = new confirm_args();
      args.setParams(params);
      sendBase("confirm", args);
    }

    public ReturnResult recv_confirm() throws org.apache.thrift.TException
    {
      confirm_result result = new confirm_result();
      receiveBase(result, "confirm");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "confirm failed: unknown result");
    }

    public ReturnResult cancel(UnFreezeVO params) throws org.apache.thrift.TException
    {
      send_cancel(params);
      return recv_cancel();
    }

    public void send_cancel(UnFreezeVO params) throws org.apache.thrift.TException
    {
      cancel_args args = new cancel_args();
      args.setParams(params);
      sendBase("cancel", args);
    }

    public ReturnResult recv_cancel() throws org.apache.thrift.TException
    {
      cancel_result result = new cancel_result();
      receiveBase(result, "cancel");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "cancel failed: unknown result");
    }

    public CouponListResult queryByCFrom(long kugouId, int cFromId, short couponStat, int actionId) throws org.apache.thrift.TException
    {
      send_queryByCFrom(kugouId, cFromId, couponStat, actionId);
      return recv_queryByCFrom();
    }

    public void send_queryByCFrom(long kugouId, int cFromId, short couponStat, int actionId) throws org.apache.thrift.TException
    {
      queryByCFrom_args args = new queryByCFrom_args();
      args.setKugouId(kugouId);
      args.setCFromId(cFromId);
      args.setCouponStat(couponStat);
      args.setActionId(actionId);
      sendBase("queryByCFrom", args);
    }

    public CouponListResult recv_queryByCFrom() throws org.apache.thrift.TException
    {
      queryByCFrom_result result = new queryByCFrom_result();
      receiveBase(result, "queryByCFrom");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "queryByCFrom failed: unknown result");
    }

    public CouponCountResult countByCFrom(long kugouId, int cFromId, int actionId) throws org.apache.thrift.TException
    {
      send_countByCFrom(kugouId, cFromId, actionId);
      return recv_countByCFrom();
    }

    public void send_countByCFrom(long kugouId, int cFromId, int actionId) throws org.apache.thrift.TException
    {
      countByCFrom_args args = new countByCFrom_args();
      args.setKugouId(kugouId);
      args.setCFromId(cFromId);
      args.setActionId(actionId);
      sendBase("countByCFrom", args);
    }

    public CouponCountResult recv_countByCFrom() throws org.apache.thrift.TException
    {
      countByCFrom_result result = new countByCFrom_result();
      receiveBase(result, "countByCFrom");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "countByCFrom failed: unknown result");
    }

    public CouponQueryResult queryCoupon(long kugouId, long couponId, int actionId) throws org.apache.thrift.TException
    {
      send_queryCoupon(kugouId, couponId, actionId);
      return recv_queryCoupon();
    }

    public void send_queryCoupon(long kugouId, long couponId, int actionId) throws org.apache.thrift.TException
    {
      queryCoupon_args args = new queryCoupon_args();
      args.setKugouId(kugouId);
      args.setCouponId(couponId);
      args.setActionId(actionId);
      sendBase("queryCoupon", args);
    }

    public CouponQueryResult recv_queryCoupon() throws org.apache.thrift.TException
    {
      queryCoupon_result result = new queryCoupon_result();
      receiveBase(result, "queryCoupon");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "queryCoupon failed: unknown result");
    }

    public PersonalCouponNumResult getPersonalCouponNum(CouponListRequestParam param) throws org.apache.thrift.TException
    {
      send_getPersonalCouponNum(param);
      return recv_getPersonalCouponNum();
    }

    public void send_getPersonalCouponNum(CouponListRequestParam param) throws org.apache.thrift.TException
    {
      getPersonalCouponNum_args args = new getPersonalCouponNum_args();
      args.setParam(param);
      sendBase("getPersonalCouponNum", args);
    }

    public PersonalCouponNumResult recv_getPersonalCouponNum() throws org.apache.thrift.TException
    {
      getPersonalCouponNum_result result = new getPersonalCouponNum_result();
      receiveBase(result, "getPersonalCouponNum");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getPersonalCouponNum failed: unknown result");
    }

    public PersonalCouponListResult getPersonalCouponList(CouponListRequestParam param) throws org.apache.thrift.TException
    {
      send_getPersonalCouponList(param);
      return recv_getPersonalCouponList();
    }

    public void send_getPersonalCouponList(CouponListRequestParam param) throws org.apache.thrift.TException
    {
      getPersonalCouponList_args args = new getPersonalCouponList_args();
      args.setParam(param);
      sendBase("getPersonalCouponList", args);
    }

    public PersonalCouponListResult recv_getPersonalCouponList() throws org.apache.thrift.TException
    {
      getPersonalCouponList_result result = new getPersonalCouponList_result();
      receiveBase(result, "getPersonalCouponList");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getPersonalCouponList failed: unknown result");
    }

    public DrawCouponResult drawCoupon(DrawCouponParam param) throws org.apache.thrift.TException
    {
      send_drawCoupon(param);
      return recv_drawCoupon();
    }

    public void send_drawCoupon(DrawCouponParam param) throws org.apache.thrift.TException
    {
      drawCoupon_args args = new drawCoupon_args();
      args.setParam(param);
      sendBase("drawCoupon", args);
    }

    public DrawCouponResult recv_drawCoupon() throws org.apache.thrift.TException
    {
      drawCoupon_result result = new drawCoupon_result();
      receiveBase(result, "drawCoupon");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "drawCoupon failed: unknown result");
    }

    public GetRebateCouponsResult getRebateCouponList(GetRebateCouponsParams params) throws org.apache.thrift.TException
    {
      send_getRebateCouponList(params);
      return recv_getRebateCouponList();
    }

    public void send_getRebateCouponList(GetRebateCouponsParams params) throws org.apache.thrift.TException
    {
      getRebateCouponList_args args = new getRebateCouponList_args();
      args.setParams(params);
      sendBase("getRebateCouponList", args);
    }

    public GetRebateCouponsResult recv_getRebateCouponList() throws org.apache.thrift.TException
    {
      getRebateCouponList_result result = new getRebateCouponList_result();
      receiveBase(result, "getRebateCouponList");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "getRebateCouponList failed: unknown result");
    }

    public ActionIdConfigQueryResult queryActionIdConfig(ActionIdConfigRequest params) throws org.apache.thrift.TException
    {
      send_queryActionIdConfig(params);
      return recv_queryActionIdConfig();
    }

    public void send_queryActionIdConfig(ActionIdConfigRequest params) throws org.apache.thrift.TException
    {
      queryActionIdConfig_args args = new queryActionIdConfig_args();
      args.setParams(params);
      sendBase("queryActionIdConfig", args);
    }

    public ActionIdConfigQueryResult recv_queryActionIdConfig() throws org.apache.thrift.TException
    {
      queryActionIdConfig_result result = new queryActionIdConfig_result();
      receiveBase(result, "queryActionIdConfig");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "queryActionIdConfig failed: unknown result");
    }

  }
  public static class AsyncClient extends org.apache.thrift.async.TAsyncClient implements AsyncIface {
    public static class Factory implements org.apache.thrift.async.TAsyncClientFactory<AsyncClient> {
      private org.apache.thrift.async.TAsyncClientManager clientManager;
      private org.apache.thrift.protocol.TProtocolFactory protocolFactory;
      public Factory(org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.protocol.TProtocolFactory protocolFactory) {
        this.clientManager = clientManager;
        this.protocolFactory = protocolFactory;
      }
      public AsyncClient getAsyncClient(org.apache.thrift.transport.TNonblockingTransport transport) {
        return new AsyncClient(protocolFactory, clientManager, transport);
      }
    }

    public AsyncClient(org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.transport.TNonblockingTransport transport) {
      super(protocolFactory, clientManager, transport);
    }

    public void put(PutParamsVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      put_call method_call = new put_call(params, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class put_call extends org.apache.thrift.async.TAsyncMethodCall {
      private PutParamsVO params;
      public put_call(PutParamsVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.params = params;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("put", org.apache.thrift.protocol.TMessageType.CALL, 0));
        put_args args = new put_args();
        args.setParams(params);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ReturnResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_put();
      }
    }

    public void freeze(FreezeParamsVO params, ConsumeVO consumeVo, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      freeze_call method_call = new freeze_call(params, consumeVo, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class freeze_call extends org.apache.thrift.async.TAsyncMethodCall {
      private FreezeParamsVO params;
      private ConsumeVO consumeVo;
      public freeze_call(FreezeParamsVO params, ConsumeVO consumeVo, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.params = params;
        this.consumeVo = consumeVo;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("freeze", org.apache.thrift.protocol.TMessageType.CALL, 0));
        freeze_args args = new freeze_args();
        args.setParams(params);
        args.setConsumeVo(consumeVo);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ReturnResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_freeze();
      }
    }

    public void freezeConfirm(UnFreezeParamsVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      freezeConfirm_call method_call = new freezeConfirm_call(params, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class freezeConfirm_call extends org.apache.thrift.async.TAsyncMethodCall {
      private UnFreezeParamsVO params;
      public freezeConfirm_call(UnFreezeParamsVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.params = params;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("freezeConfirm", org.apache.thrift.protocol.TMessageType.CALL, 0));
        freezeConfirm_args args = new freezeConfirm_args();
        args.setParams(params);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ReturnResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_freezeConfirm();
      }
    }

    public void freezeCancel(UnFreezeParamsVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      freezeCancel_call method_call = new freezeCancel_call(params, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class freezeCancel_call extends org.apache.thrift.async.TAsyncMethodCall {
      private UnFreezeParamsVO params;
      public freezeCancel_call(UnFreezeParamsVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.params = params;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("freezeCancel", org.apache.thrift.protocol.TMessageType.CALL, 0));
        freezeCancel_args args = new freezeCancel_args();
        args.setParams(params);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ReturnResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_freezeCancel();
      }
    }

    public void simpleFreeze(FreezeParamsVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      simpleFreeze_call method_call = new simpleFreeze_call(params, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class simpleFreeze_call extends org.apache.thrift.async.TAsyncMethodCall {
      private FreezeParamsVO params;
      public simpleFreeze_call(FreezeParamsVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.params = params;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("simpleFreeze", org.apache.thrift.protocol.TMessageType.CALL, 0));
        simpleFreeze_args args = new simpleFreeze_args();
        args.setParams(params);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ReturnResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_simpleFreeze();
      }
    }

    public void confirm(UnFreezeVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      confirm_call method_call = new confirm_call(params, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class confirm_call extends org.apache.thrift.async.TAsyncMethodCall {
      private UnFreezeVO params;
      public confirm_call(UnFreezeVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.params = params;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("confirm", org.apache.thrift.protocol.TMessageType.CALL, 0));
        confirm_args args = new confirm_args();
        args.setParams(params);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ReturnResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_confirm();
      }
    }

    public void cancel(UnFreezeVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      cancel_call method_call = new cancel_call(params, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class cancel_call extends org.apache.thrift.async.TAsyncMethodCall {
      private UnFreezeVO params;
      public cancel_call(UnFreezeVO params, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.params = params;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("cancel", org.apache.thrift.protocol.TMessageType.CALL, 0));
        cancel_args args = new cancel_args();
        args.setParams(params);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ReturnResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_cancel();
      }
    }

    public void queryByCFrom(long kugouId, int cFromId, short couponStat, int actionId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      queryByCFrom_call method_call = new queryByCFrom_call(kugouId, cFromId, couponStat, actionId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class queryByCFrom_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      private int cFromId;
      private short couponStat;
      private int actionId;
      public queryByCFrom_call(long kugouId, int cFromId, short couponStat, int actionId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
        this.cFromId = cFromId;
        this.couponStat = couponStat;
        this.actionId = actionId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("queryByCFrom", org.apache.thrift.protocol.TMessageType.CALL, 0));
        queryByCFrom_args args = new queryByCFrom_args();
        args.setKugouId(kugouId);
        args.setCFromId(cFromId);
        args.setCouponStat(couponStat);
        args.setActionId(actionId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public CouponListResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_queryByCFrom();
      }
    }

    public void countByCFrom(long kugouId, int cFromId, int actionId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      countByCFrom_call method_call = new countByCFrom_call(kugouId, cFromId, actionId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class countByCFrom_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      private int cFromId;
      private int actionId;
      public countByCFrom_call(long kugouId, int cFromId, int actionId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
        this.cFromId = cFromId;
        this.actionId = actionId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("countByCFrom", org.apache.thrift.protocol.TMessageType.CALL, 0));
        countByCFrom_args args = new countByCFrom_args();
        args.setKugouId(kugouId);
        args.setCFromId(cFromId);
        args.setActionId(actionId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public CouponCountResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_countByCFrom();
      }
    }

    public void queryCoupon(long kugouId, long couponId, int actionId, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      queryCoupon_call method_call = new queryCoupon_call(kugouId, couponId, actionId, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class queryCoupon_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long kugouId;
      private long couponId;
      private int actionId;
      public queryCoupon_call(long kugouId, long couponId, int actionId, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.kugouId = kugouId;
        this.couponId = couponId;
        this.actionId = actionId;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("queryCoupon", org.apache.thrift.protocol.TMessageType.CALL, 0));
        queryCoupon_args args = new queryCoupon_args();
        args.setKugouId(kugouId);
        args.setCouponId(couponId);
        args.setActionId(actionId);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public CouponQueryResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_queryCoupon();
      }
    }

    public void getPersonalCouponNum(CouponListRequestParam param, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getPersonalCouponNum_call method_call = new getPersonalCouponNum_call(param, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getPersonalCouponNum_call extends org.apache.thrift.async.TAsyncMethodCall {
      private CouponListRequestParam param;
      public getPersonalCouponNum_call(CouponListRequestParam param, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.param = param;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getPersonalCouponNum", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getPersonalCouponNum_args args = new getPersonalCouponNum_args();
        args.setParam(param);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public PersonalCouponNumResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getPersonalCouponNum();
      }
    }

    public void getPersonalCouponList(CouponListRequestParam param, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getPersonalCouponList_call method_call = new getPersonalCouponList_call(param, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getPersonalCouponList_call extends org.apache.thrift.async.TAsyncMethodCall {
      private CouponListRequestParam param;
      public getPersonalCouponList_call(CouponListRequestParam param, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.param = param;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getPersonalCouponList", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getPersonalCouponList_args args = new getPersonalCouponList_args();
        args.setParam(param);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public PersonalCouponListResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getPersonalCouponList();
      }
    }

    public void drawCoupon(DrawCouponParam param, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      drawCoupon_call method_call = new drawCoupon_call(param, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class drawCoupon_call extends org.apache.thrift.async.TAsyncMethodCall {
      private DrawCouponParam param;
      public drawCoupon_call(DrawCouponParam param, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.param = param;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("drawCoupon", org.apache.thrift.protocol.TMessageType.CALL, 0));
        drawCoupon_args args = new drawCoupon_args();
        args.setParam(param);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public DrawCouponResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_drawCoupon();
      }
    }

    public void getRebateCouponList(GetRebateCouponsParams params, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      getRebateCouponList_call method_call = new getRebateCouponList_call(params, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class getRebateCouponList_call extends org.apache.thrift.async.TAsyncMethodCall {
      private GetRebateCouponsParams params;
      public getRebateCouponList_call(GetRebateCouponsParams params, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.params = params;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("getRebateCouponList", org.apache.thrift.protocol.TMessageType.CALL, 0));
        getRebateCouponList_args args = new getRebateCouponList_args();
        args.setParams(params);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public GetRebateCouponsResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_getRebateCouponList();
      }
    }

    public void queryActionIdConfig(ActionIdConfigRequest params, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      queryActionIdConfig_call method_call = new queryActionIdConfig_call(params, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class queryActionIdConfig_call extends org.apache.thrift.async.TAsyncMethodCall {
      private ActionIdConfigRequest params;
      public queryActionIdConfig_call(ActionIdConfigRequest params, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.params = params;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("queryActionIdConfig", org.apache.thrift.protocol.TMessageType.CALL, 0));
        queryActionIdConfig_args args = new queryActionIdConfig_args();
        args.setParams(params);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public ActionIdConfigQueryResult getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_queryActionIdConfig();
      }
    }

  }

  public static class Processor<I extends Iface> extends org.apache.thrift.TBaseProcessor<I> implements org.apache.thrift.TProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());
    public Processor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>>()));
    }

    protected Processor(I iface, Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends Iface> Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> getProcessMap(Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      processMap.put("put", new put());
      processMap.put("freeze", new freeze());
      processMap.put("freezeConfirm", new freezeConfirm());
      processMap.put("freezeCancel", new freezeCancel());
      processMap.put("simpleFreeze", new simpleFreeze());
      processMap.put("confirm", new confirm());
      processMap.put("cancel", new cancel());
      processMap.put("queryByCFrom", new queryByCFrom());
      processMap.put("countByCFrom", new countByCFrom());
      processMap.put("queryCoupon", new queryCoupon());
      processMap.put("getPersonalCouponNum", new getPersonalCouponNum());
      processMap.put("getPersonalCouponList", new getPersonalCouponList());
      processMap.put("drawCoupon", new drawCoupon());
      processMap.put("getRebateCouponList", new getRebateCouponList());
      processMap.put("queryActionIdConfig", new queryActionIdConfig());
      return processMap;
    }

    public static class put<I extends Iface> extends org.apache.thrift.ProcessFunction<I, put_args> {
      public put() {
        super("put");
      }

      public put_args getEmptyArgsInstance() {
        return new put_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public put_result getResult(I iface, put_args args) throws org.apache.thrift.TException {
        put_result result = new put_result();
        result.success = iface.put(args.params);
        return result;
      }
    }

    public static class freeze<I extends Iface> extends org.apache.thrift.ProcessFunction<I, freeze_args> {
      public freeze() {
        super("freeze");
      }

      public freeze_args getEmptyArgsInstance() {
        return new freeze_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public freeze_result getResult(I iface, freeze_args args) throws org.apache.thrift.TException {
        freeze_result result = new freeze_result();
        result.success = iface.freeze(args.params, args.consumeVo);
        return result;
      }
    }

    public static class freezeConfirm<I extends Iface> extends org.apache.thrift.ProcessFunction<I, freezeConfirm_args> {
      public freezeConfirm() {
        super("freezeConfirm");
      }

      public freezeConfirm_args getEmptyArgsInstance() {
        return new freezeConfirm_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public freezeConfirm_result getResult(I iface, freezeConfirm_args args) throws org.apache.thrift.TException {
        freezeConfirm_result result = new freezeConfirm_result();
        result.success = iface.freezeConfirm(args.params);
        return result;
      }
    }

    public static class freezeCancel<I extends Iface> extends org.apache.thrift.ProcessFunction<I, freezeCancel_args> {
      public freezeCancel() {
        super("freezeCancel");
      }

      public freezeCancel_args getEmptyArgsInstance() {
        return new freezeCancel_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public freezeCancel_result getResult(I iface, freezeCancel_args args) throws org.apache.thrift.TException {
        freezeCancel_result result = new freezeCancel_result();
        result.success = iface.freezeCancel(args.params);
        return result;
      }
    }

    public static class simpleFreeze<I extends Iface> extends org.apache.thrift.ProcessFunction<I, simpleFreeze_args> {
      public simpleFreeze() {
        super("simpleFreeze");
      }

      public simpleFreeze_args getEmptyArgsInstance() {
        return new simpleFreeze_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public simpleFreeze_result getResult(I iface, simpleFreeze_args args) throws org.apache.thrift.TException {
        simpleFreeze_result result = new simpleFreeze_result();
        result.success = iface.simpleFreeze(args.params);
        return result;
      }
    }

    public static class confirm<I extends Iface> extends org.apache.thrift.ProcessFunction<I, confirm_args> {
      public confirm() {
        super("confirm");
      }

      public confirm_args getEmptyArgsInstance() {
        return new confirm_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public confirm_result getResult(I iface, confirm_args args) throws org.apache.thrift.TException {
        confirm_result result = new confirm_result();
        result.success = iface.confirm(args.params);
        return result;
      }
    }

    public static class cancel<I extends Iface> extends org.apache.thrift.ProcessFunction<I, cancel_args> {
      public cancel() {
        super("cancel");
      }

      public cancel_args getEmptyArgsInstance() {
        return new cancel_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public cancel_result getResult(I iface, cancel_args args) throws org.apache.thrift.TException {
        cancel_result result = new cancel_result();
        result.success = iface.cancel(args.params);
        return result;
      }
    }

    public static class queryByCFrom<I extends Iface> extends org.apache.thrift.ProcessFunction<I, queryByCFrom_args> {
      public queryByCFrom() {
        super("queryByCFrom");
      }

      public queryByCFrom_args getEmptyArgsInstance() {
        return new queryByCFrom_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public queryByCFrom_result getResult(I iface, queryByCFrom_args args) throws org.apache.thrift.TException {
        queryByCFrom_result result = new queryByCFrom_result();
        result.success = iface.queryByCFrom(args.kugouId, args.cFromId, args.couponStat, args.actionId);
        return result;
      }
    }

    public static class countByCFrom<I extends Iface> extends org.apache.thrift.ProcessFunction<I, countByCFrom_args> {
      public countByCFrom() {
        super("countByCFrom");
      }

      public countByCFrom_args getEmptyArgsInstance() {
        return new countByCFrom_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public countByCFrom_result getResult(I iface, countByCFrom_args args) throws org.apache.thrift.TException {
        countByCFrom_result result = new countByCFrom_result();
        result.success = iface.countByCFrom(args.kugouId, args.cFromId, args.actionId);
        return result;
      }
    }

    public static class queryCoupon<I extends Iface> extends org.apache.thrift.ProcessFunction<I, queryCoupon_args> {
      public queryCoupon() {
        super("queryCoupon");
      }

      public queryCoupon_args getEmptyArgsInstance() {
        return new queryCoupon_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public queryCoupon_result getResult(I iface, queryCoupon_args args) throws org.apache.thrift.TException {
        queryCoupon_result result = new queryCoupon_result();
        result.success = iface.queryCoupon(args.kugouId, args.couponId, args.actionId);
        return result;
      }
    }

    public static class getPersonalCouponNum<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getPersonalCouponNum_args> {
      public getPersonalCouponNum() {
        super("getPersonalCouponNum");
      }

      public getPersonalCouponNum_args getEmptyArgsInstance() {
        return new getPersonalCouponNum_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getPersonalCouponNum_result getResult(I iface, getPersonalCouponNum_args args) throws org.apache.thrift.TException {
        getPersonalCouponNum_result result = new getPersonalCouponNum_result();
        result.success = iface.getPersonalCouponNum(args.param);
        return result;
      }
    }

    public static class getPersonalCouponList<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getPersonalCouponList_args> {
      public getPersonalCouponList() {
        super("getPersonalCouponList");
      }

      public getPersonalCouponList_args getEmptyArgsInstance() {
        return new getPersonalCouponList_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getPersonalCouponList_result getResult(I iface, getPersonalCouponList_args args) throws org.apache.thrift.TException {
        getPersonalCouponList_result result = new getPersonalCouponList_result();
        result.success = iface.getPersonalCouponList(args.param);
        return result;
      }
    }

    public static class drawCoupon<I extends Iface> extends org.apache.thrift.ProcessFunction<I, drawCoupon_args> {
      public drawCoupon() {
        super("drawCoupon");
      }

      public drawCoupon_args getEmptyArgsInstance() {
        return new drawCoupon_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public drawCoupon_result getResult(I iface, drawCoupon_args args) throws org.apache.thrift.TException {
        drawCoupon_result result = new drawCoupon_result();
        result.success = iface.drawCoupon(args.param);
        return result;
      }
    }

    public static class getRebateCouponList<I extends Iface> extends org.apache.thrift.ProcessFunction<I, getRebateCouponList_args> {
      public getRebateCouponList() {
        super("getRebateCouponList");
      }

      public getRebateCouponList_args getEmptyArgsInstance() {
        return new getRebateCouponList_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public getRebateCouponList_result getResult(I iface, getRebateCouponList_args args) throws org.apache.thrift.TException {
        getRebateCouponList_result result = new getRebateCouponList_result();
        result.success = iface.getRebateCouponList(args.params);
        return result;
      }
    }

    public static class queryActionIdConfig<I extends Iface> extends org.apache.thrift.ProcessFunction<I, queryActionIdConfig_args> {
      public queryActionIdConfig() {
        super("queryActionIdConfig");
      }

      public queryActionIdConfig_args getEmptyArgsInstance() {
        return new queryActionIdConfig_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public queryActionIdConfig_result getResult(I iface, queryActionIdConfig_args args) throws org.apache.thrift.TException {
        queryActionIdConfig_result result = new queryActionIdConfig_result();
        result.success = iface.queryActionIdConfig(args.params);
        return result;
      }
    }

  }

  public static class AsyncProcessor<I extends AsyncIface> extends org.apache.thrift.TBaseAsyncProcessor<I> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());
    public AsyncProcessor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.AsyncProcessFunction<I, ? extends org.apache.thrift.TBase, ?>>()));
    }

    protected AsyncProcessor(I iface, Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends AsyncIface> Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase,?>> getProcessMap(Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      processMap.put("put", new put());
      processMap.put("freeze", new freeze());
      processMap.put("freezeConfirm", new freezeConfirm());
      processMap.put("freezeCancel", new freezeCancel());
      processMap.put("simpleFreeze", new simpleFreeze());
      processMap.put("confirm", new confirm());
      processMap.put("cancel", new cancel());
      processMap.put("queryByCFrom", new queryByCFrom());
      processMap.put("countByCFrom", new countByCFrom());
      processMap.put("queryCoupon", new queryCoupon());
      processMap.put("getPersonalCouponNum", new getPersonalCouponNum());
      processMap.put("getPersonalCouponList", new getPersonalCouponList());
      processMap.put("drawCoupon", new drawCoupon());
      processMap.put("getRebateCouponList", new getRebateCouponList());
      processMap.put("queryActionIdConfig", new queryActionIdConfig());
      return processMap;
    }

    public static class put<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, put_args, ReturnResult> {
      public put() {
        super("put");
      }

      public put_args getEmptyArgsInstance() {
        return new put_args();
      }

      public AsyncMethodCallback<ReturnResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ReturnResult>() { 
          public void onComplete(ReturnResult o) {
            put_result result = new put_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            put_result result = new put_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, put_args args, org.apache.thrift.async.AsyncMethodCallback<ReturnResult> resultHandler) throws TException {
        iface.put(args.params,resultHandler);
      }
    }

    public static class freeze<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, freeze_args, ReturnResult> {
      public freeze() {
        super("freeze");
      }

      public freeze_args getEmptyArgsInstance() {
        return new freeze_args();
      }

      public AsyncMethodCallback<ReturnResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ReturnResult>() { 
          public void onComplete(ReturnResult o) {
            freeze_result result = new freeze_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            freeze_result result = new freeze_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, freeze_args args, org.apache.thrift.async.AsyncMethodCallback<ReturnResult> resultHandler) throws TException {
        iface.freeze(args.params, args.consumeVo,resultHandler);
      }
    }

    public static class freezeConfirm<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, freezeConfirm_args, ReturnResult> {
      public freezeConfirm() {
        super("freezeConfirm");
      }

      public freezeConfirm_args getEmptyArgsInstance() {
        return new freezeConfirm_args();
      }

      public AsyncMethodCallback<ReturnResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ReturnResult>() { 
          public void onComplete(ReturnResult o) {
            freezeConfirm_result result = new freezeConfirm_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            freezeConfirm_result result = new freezeConfirm_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, freezeConfirm_args args, org.apache.thrift.async.AsyncMethodCallback<ReturnResult> resultHandler) throws TException {
        iface.freezeConfirm(args.params,resultHandler);
      }
    }

    public static class freezeCancel<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, freezeCancel_args, ReturnResult> {
      public freezeCancel() {
        super("freezeCancel");
      }

      public freezeCancel_args getEmptyArgsInstance() {
        return new freezeCancel_args();
      }

      public AsyncMethodCallback<ReturnResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ReturnResult>() { 
          public void onComplete(ReturnResult o) {
            freezeCancel_result result = new freezeCancel_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            freezeCancel_result result = new freezeCancel_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, freezeCancel_args args, org.apache.thrift.async.AsyncMethodCallback<ReturnResult> resultHandler) throws TException {
        iface.freezeCancel(args.params,resultHandler);
      }
    }

    public static class simpleFreeze<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, simpleFreeze_args, ReturnResult> {
      public simpleFreeze() {
        super("simpleFreeze");
      }

      public simpleFreeze_args getEmptyArgsInstance() {
        return new simpleFreeze_args();
      }

      public AsyncMethodCallback<ReturnResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ReturnResult>() { 
          public void onComplete(ReturnResult o) {
            simpleFreeze_result result = new simpleFreeze_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            simpleFreeze_result result = new simpleFreeze_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, simpleFreeze_args args, org.apache.thrift.async.AsyncMethodCallback<ReturnResult> resultHandler) throws TException {
        iface.simpleFreeze(args.params,resultHandler);
      }
    }

    public static class confirm<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, confirm_args, ReturnResult> {
      public confirm() {
        super("confirm");
      }

      public confirm_args getEmptyArgsInstance() {
        return new confirm_args();
      }

      public AsyncMethodCallback<ReturnResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ReturnResult>() { 
          public void onComplete(ReturnResult o) {
            confirm_result result = new confirm_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            confirm_result result = new confirm_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, confirm_args args, org.apache.thrift.async.AsyncMethodCallback<ReturnResult> resultHandler) throws TException {
        iface.confirm(args.params,resultHandler);
      }
    }

    public static class cancel<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, cancel_args, ReturnResult> {
      public cancel() {
        super("cancel");
      }

      public cancel_args getEmptyArgsInstance() {
        return new cancel_args();
      }

      public AsyncMethodCallback<ReturnResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ReturnResult>() { 
          public void onComplete(ReturnResult o) {
            cancel_result result = new cancel_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            cancel_result result = new cancel_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, cancel_args args, org.apache.thrift.async.AsyncMethodCallback<ReturnResult> resultHandler) throws TException {
        iface.cancel(args.params,resultHandler);
      }
    }

    public static class queryByCFrom<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, queryByCFrom_args, CouponListResult> {
      public queryByCFrom() {
        super("queryByCFrom");
      }

      public queryByCFrom_args getEmptyArgsInstance() {
        return new queryByCFrom_args();
      }

      public AsyncMethodCallback<CouponListResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<CouponListResult>() { 
          public void onComplete(CouponListResult o) {
            queryByCFrom_result result = new queryByCFrom_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            queryByCFrom_result result = new queryByCFrom_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, queryByCFrom_args args, org.apache.thrift.async.AsyncMethodCallback<CouponListResult> resultHandler) throws TException {
        iface.queryByCFrom(args.kugouId, args.cFromId, args.couponStat, args.actionId,resultHandler);
      }
    }

    public static class countByCFrom<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, countByCFrom_args, CouponCountResult> {
      public countByCFrom() {
        super("countByCFrom");
      }

      public countByCFrom_args getEmptyArgsInstance() {
        return new countByCFrom_args();
      }

      public AsyncMethodCallback<CouponCountResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<CouponCountResult>() { 
          public void onComplete(CouponCountResult o) {
            countByCFrom_result result = new countByCFrom_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            countByCFrom_result result = new countByCFrom_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, countByCFrom_args args, org.apache.thrift.async.AsyncMethodCallback<CouponCountResult> resultHandler) throws TException {
        iface.countByCFrom(args.kugouId, args.cFromId, args.actionId,resultHandler);
      }
    }

    public static class queryCoupon<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, queryCoupon_args, CouponQueryResult> {
      public queryCoupon() {
        super("queryCoupon");
      }

      public queryCoupon_args getEmptyArgsInstance() {
        return new queryCoupon_args();
      }

      public AsyncMethodCallback<CouponQueryResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<CouponQueryResult>() { 
          public void onComplete(CouponQueryResult o) {
            queryCoupon_result result = new queryCoupon_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            queryCoupon_result result = new queryCoupon_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, queryCoupon_args args, org.apache.thrift.async.AsyncMethodCallback<CouponQueryResult> resultHandler) throws TException {
        iface.queryCoupon(args.kugouId, args.couponId, args.actionId,resultHandler);
      }
    }

    public static class getPersonalCouponNum<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getPersonalCouponNum_args, PersonalCouponNumResult> {
      public getPersonalCouponNum() {
        super("getPersonalCouponNum");
      }

      public getPersonalCouponNum_args getEmptyArgsInstance() {
        return new getPersonalCouponNum_args();
      }

      public AsyncMethodCallback<PersonalCouponNumResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<PersonalCouponNumResult>() { 
          public void onComplete(PersonalCouponNumResult o) {
            getPersonalCouponNum_result result = new getPersonalCouponNum_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getPersonalCouponNum_result result = new getPersonalCouponNum_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getPersonalCouponNum_args args, org.apache.thrift.async.AsyncMethodCallback<PersonalCouponNumResult> resultHandler) throws TException {
        iface.getPersonalCouponNum(args.param,resultHandler);
      }
    }

    public static class getPersonalCouponList<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getPersonalCouponList_args, PersonalCouponListResult> {
      public getPersonalCouponList() {
        super("getPersonalCouponList");
      }

      public getPersonalCouponList_args getEmptyArgsInstance() {
        return new getPersonalCouponList_args();
      }

      public AsyncMethodCallback<PersonalCouponListResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<PersonalCouponListResult>() { 
          public void onComplete(PersonalCouponListResult o) {
            getPersonalCouponList_result result = new getPersonalCouponList_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getPersonalCouponList_result result = new getPersonalCouponList_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getPersonalCouponList_args args, org.apache.thrift.async.AsyncMethodCallback<PersonalCouponListResult> resultHandler) throws TException {
        iface.getPersonalCouponList(args.param,resultHandler);
      }
    }

    public static class drawCoupon<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, drawCoupon_args, DrawCouponResult> {
      public drawCoupon() {
        super("drawCoupon");
      }

      public drawCoupon_args getEmptyArgsInstance() {
        return new drawCoupon_args();
      }

      public AsyncMethodCallback<DrawCouponResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<DrawCouponResult>() { 
          public void onComplete(DrawCouponResult o) {
            drawCoupon_result result = new drawCoupon_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            drawCoupon_result result = new drawCoupon_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, drawCoupon_args args, org.apache.thrift.async.AsyncMethodCallback<DrawCouponResult> resultHandler) throws TException {
        iface.drawCoupon(args.param,resultHandler);
      }
    }

    public static class getRebateCouponList<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, getRebateCouponList_args, GetRebateCouponsResult> {
      public getRebateCouponList() {
        super("getRebateCouponList");
      }

      public getRebateCouponList_args getEmptyArgsInstance() {
        return new getRebateCouponList_args();
      }

      public AsyncMethodCallback<GetRebateCouponsResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<GetRebateCouponsResult>() { 
          public void onComplete(GetRebateCouponsResult o) {
            getRebateCouponList_result result = new getRebateCouponList_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            getRebateCouponList_result result = new getRebateCouponList_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, getRebateCouponList_args args, org.apache.thrift.async.AsyncMethodCallback<GetRebateCouponsResult> resultHandler) throws TException {
        iface.getRebateCouponList(args.params,resultHandler);
      }
    }

    public static class queryActionIdConfig<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, queryActionIdConfig_args, ActionIdConfigQueryResult> {
      public queryActionIdConfig() {
        super("queryActionIdConfig");
      }

      public queryActionIdConfig_args getEmptyArgsInstance() {
        return new queryActionIdConfig_args();
      }

      public AsyncMethodCallback<ActionIdConfigQueryResult> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<ActionIdConfigQueryResult>() { 
          public void onComplete(ActionIdConfigQueryResult o) {
            queryActionIdConfig_result result = new queryActionIdConfig_result();
            result.success = o;
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            queryActionIdConfig_result result = new queryActionIdConfig_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, queryActionIdConfig_args args, org.apache.thrift.async.AsyncMethodCallback<ActionIdConfigQueryResult> resultHandler) throws TException {
        iface.queryActionIdConfig(args.params,resultHandler);
      }
    }

  }

  public static class put_args implements org.apache.thrift.TBase<put_args, put_args._Fields>, java.io.Serializable, Cloneable, Comparable<put_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("put_args");

    private static final org.apache.thrift.protocol.TField PARAMS_FIELD_DESC = new org.apache.thrift.protocol.TField("params", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new put_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new put_argsTupleSchemeFactory());
    }

    public PutParamsVO params; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      PARAMS((short)1, "params");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // PARAMS
            return PARAMS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.PARAMS, new org.apache.thrift.meta_data.FieldMetaData("params", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PutParamsVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(put_args.class, metaDataMap);
    }

    public put_args() {
    }

    public put_args(
      PutParamsVO params)
    {
      this();
      this.params = params;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public put_args(put_args other) {
      if (other.isSetParams()) {
        this.params = new PutParamsVO(other.params);
      }
    }

    public put_args deepCopy() {
      return new put_args(this);
    }

    @Override
    public void clear() {
      this.params = null;
    }

    public PutParamsVO getParams() {
      return this.params;
    }

    public put_args setParams(PutParamsVO params) {
      this.params = params;
      return this;
    }

    public void unsetParams() {
      this.params = null;
    }

    /** Returns true if field params is set (has been assigned a value) and false otherwise */
    public boolean isSetParams() {
      return this.params != null;
    }

    public void setParamsIsSet(boolean value) {
      if (!value) {
        this.params = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case PARAMS:
        if (value == null) {
          unsetParams();
        } else {
          setParams((PutParamsVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case PARAMS:
        return getParams();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case PARAMS:
        return isSetParams();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof put_args)
        return this.equals((put_args)that);
      return false;
    }

    public boolean equals(put_args that) {
      if (that == null)
        return false;

      boolean this_present_params = true && this.isSetParams();
      boolean that_present_params = true && that.isSetParams();
      if (this_present_params || that_present_params) {
        if (!(this_present_params && that_present_params))
          return false;
        if (!this.params.equals(that.params))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_params = true && (isSetParams());
      list.add(present_params);
      if (present_params)
        list.add(params);

      return list.hashCode();
    }

    @Override
    public int compareTo(put_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetParams()).compareTo(other.isSetParams());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetParams()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.params, other.params);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("put_args(");
      boolean first = true;

      sb.append("params:");
      if (this.params == null) {
        sb.append("null");
      } else {
        sb.append(this.params);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (params == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'params' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (params != null) {
        params.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class put_argsStandardSchemeFactory implements SchemeFactory {
      public put_argsStandardScheme getScheme() {
        return new put_argsStandardScheme();
      }
    }

    private static class put_argsStandardScheme extends StandardScheme<put_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, put_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // PARAMS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.params = new PutParamsVO();
                struct.params.read(iprot);
                struct.setParamsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, put_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.params != null) {
          oprot.writeFieldBegin(PARAMS_FIELD_DESC);
          struct.params.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class put_argsTupleSchemeFactory implements SchemeFactory {
      public put_argsTupleScheme getScheme() {
        return new put_argsTupleScheme();
      }
    }

    private static class put_argsTupleScheme extends TupleScheme<put_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, put_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.params.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, put_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.params = new PutParamsVO();
        struct.params.read(iprot);
        struct.setParamsIsSet(true);
      }
    }

  }

  public static class put_result implements org.apache.thrift.TBase<put_result, put_result._Fields>, java.io.Serializable, Cloneable, Comparable<put_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("put_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new put_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new put_resultTupleSchemeFactory());
    }

    public ReturnResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ReturnResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(put_result.class, metaDataMap);
    }

    public put_result() {
    }

    public put_result(
      ReturnResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public put_result(put_result other) {
      if (other.isSetSuccess()) {
        this.success = new ReturnResult(other.success);
      }
    }

    public put_result deepCopy() {
      return new put_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ReturnResult getSuccess() {
      return this.success;
    }

    public put_result setSuccess(ReturnResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ReturnResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof put_result)
        return this.equals((put_result)that);
      return false;
    }

    public boolean equals(put_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(put_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("put_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class put_resultStandardSchemeFactory implements SchemeFactory {
      public put_resultStandardScheme getScheme() {
        return new put_resultStandardScheme();
      }
    }

    private static class put_resultStandardScheme extends StandardScheme<put_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, put_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ReturnResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, put_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class put_resultTupleSchemeFactory implements SchemeFactory {
      public put_resultTupleScheme getScheme() {
        return new put_resultTupleScheme();
      }
    }

    private static class put_resultTupleScheme extends TupleScheme<put_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, put_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, put_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ReturnResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class freeze_args implements org.apache.thrift.TBase<freeze_args, freeze_args._Fields>, java.io.Serializable, Cloneable, Comparable<freeze_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("freeze_args");

    private static final org.apache.thrift.protocol.TField PARAMS_FIELD_DESC = new org.apache.thrift.protocol.TField("params", org.apache.thrift.protocol.TType.STRUCT, (short)1);
    private static final org.apache.thrift.protocol.TField CONSUME_VO_FIELD_DESC = new org.apache.thrift.protocol.TField("consumeVo", org.apache.thrift.protocol.TType.STRUCT, (short)2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new freeze_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new freeze_argsTupleSchemeFactory());
    }

    public FreezeParamsVO params; // required
    public ConsumeVO consumeVo; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      PARAMS((short)1, "params"),
      CONSUME_VO((short)2, "consumeVo");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // PARAMS
            return PARAMS;
          case 2: // CONSUME_VO
            return CONSUME_VO;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.PARAMS, new org.apache.thrift.meta_data.FieldMetaData("params", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, FreezeParamsVO.class)));
      tmpMap.put(_Fields.CONSUME_VO, new org.apache.thrift.meta_data.FieldMetaData("consumeVo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ConsumeVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(freeze_args.class, metaDataMap);
    }

    public freeze_args() {
    }

    public freeze_args(
      FreezeParamsVO params,
      ConsumeVO consumeVo)
    {
      this();
      this.params = params;
      this.consumeVo = consumeVo;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public freeze_args(freeze_args other) {
      if (other.isSetParams()) {
        this.params = new FreezeParamsVO(other.params);
      }
      if (other.isSetConsumeVo()) {
        this.consumeVo = new ConsumeVO(other.consumeVo);
      }
    }

    public freeze_args deepCopy() {
      return new freeze_args(this);
    }

    @Override
    public void clear() {
      this.params = null;
      this.consumeVo = null;
    }

    public FreezeParamsVO getParams() {
      return this.params;
    }

    public freeze_args setParams(FreezeParamsVO params) {
      this.params = params;
      return this;
    }

    public void unsetParams() {
      this.params = null;
    }

    /** Returns true if field params is set (has been assigned a value) and false otherwise */
    public boolean isSetParams() {
      return this.params != null;
    }

    public void setParamsIsSet(boolean value) {
      if (!value) {
        this.params = null;
      }
    }

    public ConsumeVO getConsumeVo() {
      return this.consumeVo;
    }

    public freeze_args setConsumeVo(ConsumeVO consumeVo) {
      this.consumeVo = consumeVo;
      return this;
    }

    public void unsetConsumeVo() {
      this.consumeVo = null;
    }

    /** Returns true if field consumeVo is set (has been assigned a value) and false otherwise */
    public boolean isSetConsumeVo() {
      return this.consumeVo != null;
    }

    public void setConsumeVoIsSet(boolean value) {
      if (!value) {
        this.consumeVo = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case PARAMS:
        if (value == null) {
          unsetParams();
        } else {
          setParams((FreezeParamsVO)value);
        }
        break;

      case CONSUME_VO:
        if (value == null) {
          unsetConsumeVo();
        } else {
          setConsumeVo((ConsumeVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case PARAMS:
        return getParams();

      case CONSUME_VO:
        return getConsumeVo();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case PARAMS:
        return isSetParams();
      case CONSUME_VO:
        return isSetConsumeVo();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof freeze_args)
        return this.equals((freeze_args)that);
      return false;
    }

    public boolean equals(freeze_args that) {
      if (that == null)
        return false;

      boolean this_present_params = true && this.isSetParams();
      boolean that_present_params = true && that.isSetParams();
      if (this_present_params || that_present_params) {
        if (!(this_present_params && that_present_params))
          return false;
        if (!this.params.equals(that.params))
          return false;
      }

      boolean this_present_consumeVo = true && this.isSetConsumeVo();
      boolean that_present_consumeVo = true && that.isSetConsumeVo();
      if (this_present_consumeVo || that_present_consumeVo) {
        if (!(this_present_consumeVo && that_present_consumeVo))
          return false;
        if (!this.consumeVo.equals(that.consumeVo))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_params = true && (isSetParams());
      list.add(present_params);
      if (present_params)
        list.add(params);

      boolean present_consumeVo = true && (isSetConsumeVo());
      list.add(present_consumeVo);
      if (present_consumeVo)
        list.add(consumeVo);

      return list.hashCode();
    }

    @Override
    public int compareTo(freeze_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetParams()).compareTo(other.isSetParams());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetParams()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.params, other.params);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetConsumeVo()).compareTo(other.isSetConsumeVo());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetConsumeVo()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.consumeVo, other.consumeVo);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("freeze_args(");
      boolean first = true;

      sb.append("params:");
      if (this.params == null) {
        sb.append("null");
      } else {
        sb.append(this.params);
      }
      first = false;
      if (!first) sb.append(", ");
      sb.append("consumeVo:");
      if (this.consumeVo == null) {
        sb.append("null");
      } else {
        sb.append(this.consumeVo);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (params == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'params' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (params != null) {
        params.validate();
      }
      if (consumeVo != null) {
        consumeVo.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class freeze_argsStandardSchemeFactory implements SchemeFactory {
      public freeze_argsStandardScheme getScheme() {
        return new freeze_argsStandardScheme();
      }
    }

    private static class freeze_argsStandardScheme extends StandardScheme<freeze_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, freeze_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // PARAMS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.params = new FreezeParamsVO();
                struct.params.read(iprot);
                struct.setParamsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // CONSUME_VO
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.consumeVo = new ConsumeVO();
                struct.consumeVo.read(iprot);
                struct.setConsumeVoIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, freeze_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.params != null) {
          oprot.writeFieldBegin(PARAMS_FIELD_DESC);
          struct.params.write(oprot);
          oprot.writeFieldEnd();
        }
        if (struct.consumeVo != null) {
          oprot.writeFieldBegin(CONSUME_VO_FIELD_DESC);
          struct.consumeVo.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class freeze_argsTupleSchemeFactory implements SchemeFactory {
      public freeze_argsTupleScheme getScheme() {
        return new freeze_argsTupleScheme();
      }
    }

    private static class freeze_argsTupleScheme extends TupleScheme<freeze_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, freeze_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.params.write(oprot);
        BitSet optionals = new BitSet();
        if (struct.isSetConsumeVo()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetConsumeVo()) {
          struct.consumeVo.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, freeze_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.params = new FreezeParamsVO();
        struct.params.read(iprot);
        struct.setParamsIsSet(true);
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.consumeVo = new ConsumeVO();
          struct.consumeVo.read(iprot);
          struct.setConsumeVoIsSet(true);
        }
      }
    }

  }

  public static class freeze_result implements org.apache.thrift.TBase<freeze_result, freeze_result._Fields>, java.io.Serializable, Cloneable, Comparable<freeze_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("freeze_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new freeze_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new freeze_resultTupleSchemeFactory());
    }

    public ReturnResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ReturnResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(freeze_result.class, metaDataMap);
    }

    public freeze_result() {
    }

    public freeze_result(
      ReturnResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public freeze_result(freeze_result other) {
      if (other.isSetSuccess()) {
        this.success = new ReturnResult(other.success);
      }
    }

    public freeze_result deepCopy() {
      return new freeze_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ReturnResult getSuccess() {
      return this.success;
    }

    public freeze_result setSuccess(ReturnResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ReturnResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof freeze_result)
        return this.equals((freeze_result)that);
      return false;
    }

    public boolean equals(freeze_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(freeze_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("freeze_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class freeze_resultStandardSchemeFactory implements SchemeFactory {
      public freeze_resultStandardScheme getScheme() {
        return new freeze_resultStandardScheme();
      }
    }

    private static class freeze_resultStandardScheme extends StandardScheme<freeze_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, freeze_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ReturnResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, freeze_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class freeze_resultTupleSchemeFactory implements SchemeFactory {
      public freeze_resultTupleScheme getScheme() {
        return new freeze_resultTupleScheme();
      }
    }

    private static class freeze_resultTupleScheme extends TupleScheme<freeze_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, freeze_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, freeze_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ReturnResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class freezeConfirm_args implements org.apache.thrift.TBase<freezeConfirm_args, freezeConfirm_args._Fields>, java.io.Serializable, Cloneable, Comparable<freezeConfirm_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("freezeConfirm_args");

    private static final org.apache.thrift.protocol.TField PARAMS_FIELD_DESC = new org.apache.thrift.protocol.TField("params", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new freezeConfirm_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new freezeConfirm_argsTupleSchemeFactory());
    }

    public UnFreezeParamsVO params; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      PARAMS((short)1, "params");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // PARAMS
            return PARAMS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.PARAMS, new org.apache.thrift.meta_data.FieldMetaData("params", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, UnFreezeParamsVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(freezeConfirm_args.class, metaDataMap);
    }

    public freezeConfirm_args() {
    }

    public freezeConfirm_args(
      UnFreezeParamsVO params)
    {
      this();
      this.params = params;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public freezeConfirm_args(freezeConfirm_args other) {
      if (other.isSetParams()) {
        this.params = new UnFreezeParamsVO(other.params);
      }
    }

    public freezeConfirm_args deepCopy() {
      return new freezeConfirm_args(this);
    }

    @Override
    public void clear() {
      this.params = null;
    }

    public UnFreezeParamsVO getParams() {
      return this.params;
    }

    public freezeConfirm_args setParams(UnFreezeParamsVO params) {
      this.params = params;
      return this;
    }

    public void unsetParams() {
      this.params = null;
    }

    /** Returns true if field params is set (has been assigned a value) and false otherwise */
    public boolean isSetParams() {
      return this.params != null;
    }

    public void setParamsIsSet(boolean value) {
      if (!value) {
        this.params = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case PARAMS:
        if (value == null) {
          unsetParams();
        } else {
          setParams((UnFreezeParamsVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case PARAMS:
        return getParams();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case PARAMS:
        return isSetParams();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof freezeConfirm_args)
        return this.equals((freezeConfirm_args)that);
      return false;
    }

    public boolean equals(freezeConfirm_args that) {
      if (that == null)
        return false;

      boolean this_present_params = true && this.isSetParams();
      boolean that_present_params = true && that.isSetParams();
      if (this_present_params || that_present_params) {
        if (!(this_present_params && that_present_params))
          return false;
        if (!this.params.equals(that.params))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_params = true && (isSetParams());
      list.add(present_params);
      if (present_params)
        list.add(params);

      return list.hashCode();
    }

    @Override
    public int compareTo(freezeConfirm_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetParams()).compareTo(other.isSetParams());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetParams()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.params, other.params);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("freezeConfirm_args(");
      boolean first = true;

      sb.append("params:");
      if (this.params == null) {
        sb.append("null");
      } else {
        sb.append(this.params);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (params == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'params' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (params != null) {
        params.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class freezeConfirm_argsStandardSchemeFactory implements SchemeFactory {
      public freezeConfirm_argsStandardScheme getScheme() {
        return new freezeConfirm_argsStandardScheme();
      }
    }

    private static class freezeConfirm_argsStandardScheme extends StandardScheme<freezeConfirm_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, freezeConfirm_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // PARAMS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.params = new UnFreezeParamsVO();
                struct.params.read(iprot);
                struct.setParamsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, freezeConfirm_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.params != null) {
          oprot.writeFieldBegin(PARAMS_FIELD_DESC);
          struct.params.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class freezeConfirm_argsTupleSchemeFactory implements SchemeFactory {
      public freezeConfirm_argsTupleScheme getScheme() {
        return new freezeConfirm_argsTupleScheme();
      }
    }

    private static class freezeConfirm_argsTupleScheme extends TupleScheme<freezeConfirm_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, freezeConfirm_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.params.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, freezeConfirm_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.params = new UnFreezeParamsVO();
        struct.params.read(iprot);
        struct.setParamsIsSet(true);
      }
    }

  }

  public static class freezeConfirm_result implements org.apache.thrift.TBase<freezeConfirm_result, freezeConfirm_result._Fields>, java.io.Serializable, Cloneable, Comparable<freezeConfirm_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("freezeConfirm_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new freezeConfirm_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new freezeConfirm_resultTupleSchemeFactory());
    }

    public ReturnResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ReturnResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(freezeConfirm_result.class, metaDataMap);
    }

    public freezeConfirm_result() {
    }

    public freezeConfirm_result(
      ReturnResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public freezeConfirm_result(freezeConfirm_result other) {
      if (other.isSetSuccess()) {
        this.success = new ReturnResult(other.success);
      }
    }

    public freezeConfirm_result deepCopy() {
      return new freezeConfirm_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ReturnResult getSuccess() {
      return this.success;
    }

    public freezeConfirm_result setSuccess(ReturnResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ReturnResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof freezeConfirm_result)
        return this.equals((freezeConfirm_result)that);
      return false;
    }

    public boolean equals(freezeConfirm_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(freezeConfirm_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("freezeConfirm_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class freezeConfirm_resultStandardSchemeFactory implements SchemeFactory {
      public freezeConfirm_resultStandardScheme getScheme() {
        return new freezeConfirm_resultStandardScheme();
      }
    }

    private static class freezeConfirm_resultStandardScheme extends StandardScheme<freezeConfirm_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, freezeConfirm_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ReturnResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, freezeConfirm_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class freezeConfirm_resultTupleSchemeFactory implements SchemeFactory {
      public freezeConfirm_resultTupleScheme getScheme() {
        return new freezeConfirm_resultTupleScheme();
      }
    }

    private static class freezeConfirm_resultTupleScheme extends TupleScheme<freezeConfirm_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, freezeConfirm_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, freezeConfirm_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ReturnResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class freezeCancel_args implements org.apache.thrift.TBase<freezeCancel_args, freezeCancel_args._Fields>, java.io.Serializable, Cloneable, Comparable<freezeCancel_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("freezeCancel_args");

    private static final org.apache.thrift.protocol.TField PARAMS_FIELD_DESC = new org.apache.thrift.protocol.TField("params", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new freezeCancel_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new freezeCancel_argsTupleSchemeFactory());
    }

    public UnFreezeParamsVO params; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      PARAMS((short)1, "params");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // PARAMS
            return PARAMS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.PARAMS, new org.apache.thrift.meta_data.FieldMetaData("params", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, UnFreezeParamsVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(freezeCancel_args.class, metaDataMap);
    }

    public freezeCancel_args() {
    }

    public freezeCancel_args(
      UnFreezeParamsVO params)
    {
      this();
      this.params = params;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public freezeCancel_args(freezeCancel_args other) {
      if (other.isSetParams()) {
        this.params = new UnFreezeParamsVO(other.params);
      }
    }

    public freezeCancel_args deepCopy() {
      return new freezeCancel_args(this);
    }

    @Override
    public void clear() {
      this.params = null;
    }

    public UnFreezeParamsVO getParams() {
      return this.params;
    }

    public freezeCancel_args setParams(UnFreezeParamsVO params) {
      this.params = params;
      return this;
    }

    public void unsetParams() {
      this.params = null;
    }

    /** Returns true if field params is set (has been assigned a value) and false otherwise */
    public boolean isSetParams() {
      return this.params != null;
    }

    public void setParamsIsSet(boolean value) {
      if (!value) {
        this.params = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case PARAMS:
        if (value == null) {
          unsetParams();
        } else {
          setParams((UnFreezeParamsVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case PARAMS:
        return getParams();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case PARAMS:
        return isSetParams();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof freezeCancel_args)
        return this.equals((freezeCancel_args)that);
      return false;
    }

    public boolean equals(freezeCancel_args that) {
      if (that == null)
        return false;

      boolean this_present_params = true && this.isSetParams();
      boolean that_present_params = true && that.isSetParams();
      if (this_present_params || that_present_params) {
        if (!(this_present_params && that_present_params))
          return false;
        if (!this.params.equals(that.params))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_params = true && (isSetParams());
      list.add(present_params);
      if (present_params)
        list.add(params);

      return list.hashCode();
    }

    @Override
    public int compareTo(freezeCancel_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetParams()).compareTo(other.isSetParams());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetParams()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.params, other.params);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("freezeCancel_args(");
      boolean first = true;

      sb.append("params:");
      if (this.params == null) {
        sb.append("null");
      } else {
        sb.append(this.params);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (params == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'params' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (params != null) {
        params.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class freezeCancel_argsStandardSchemeFactory implements SchemeFactory {
      public freezeCancel_argsStandardScheme getScheme() {
        return new freezeCancel_argsStandardScheme();
      }
    }

    private static class freezeCancel_argsStandardScheme extends StandardScheme<freezeCancel_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, freezeCancel_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // PARAMS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.params = new UnFreezeParamsVO();
                struct.params.read(iprot);
                struct.setParamsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, freezeCancel_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.params != null) {
          oprot.writeFieldBegin(PARAMS_FIELD_DESC);
          struct.params.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class freezeCancel_argsTupleSchemeFactory implements SchemeFactory {
      public freezeCancel_argsTupleScheme getScheme() {
        return new freezeCancel_argsTupleScheme();
      }
    }

    private static class freezeCancel_argsTupleScheme extends TupleScheme<freezeCancel_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, freezeCancel_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.params.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, freezeCancel_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.params = new UnFreezeParamsVO();
        struct.params.read(iprot);
        struct.setParamsIsSet(true);
      }
    }

  }

  public static class freezeCancel_result implements org.apache.thrift.TBase<freezeCancel_result, freezeCancel_result._Fields>, java.io.Serializable, Cloneable, Comparable<freezeCancel_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("freezeCancel_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new freezeCancel_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new freezeCancel_resultTupleSchemeFactory());
    }

    public ReturnResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ReturnResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(freezeCancel_result.class, metaDataMap);
    }

    public freezeCancel_result() {
    }

    public freezeCancel_result(
      ReturnResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public freezeCancel_result(freezeCancel_result other) {
      if (other.isSetSuccess()) {
        this.success = new ReturnResult(other.success);
      }
    }

    public freezeCancel_result deepCopy() {
      return new freezeCancel_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ReturnResult getSuccess() {
      return this.success;
    }

    public freezeCancel_result setSuccess(ReturnResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ReturnResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof freezeCancel_result)
        return this.equals((freezeCancel_result)that);
      return false;
    }

    public boolean equals(freezeCancel_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(freezeCancel_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("freezeCancel_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class freezeCancel_resultStandardSchemeFactory implements SchemeFactory {
      public freezeCancel_resultStandardScheme getScheme() {
        return new freezeCancel_resultStandardScheme();
      }
    }

    private static class freezeCancel_resultStandardScheme extends StandardScheme<freezeCancel_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, freezeCancel_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ReturnResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, freezeCancel_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class freezeCancel_resultTupleSchemeFactory implements SchemeFactory {
      public freezeCancel_resultTupleScheme getScheme() {
        return new freezeCancel_resultTupleScheme();
      }
    }

    private static class freezeCancel_resultTupleScheme extends TupleScheme<freezeCancel_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, freezeCancel_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, freezeCancel_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ReturnResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class simpleFreeze_args implements org.apache.thrift.TBase<simpleFreeze_args, simpleFreeze_args._Fields>, java.io.Serializable, Cloneable, Comparable<simpleFreeze_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("simpleFreeze_args");

    private static final org.apache.thrift.protocol.TField PARAMS_FIELD_DESC = new org.apache.thrift.protocol.TField("params", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new simpleFreeze_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new simpleFreeze_argsTupleSchemeFactory());
    }

    public FreezeParamsVO params; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      PARAMS((short)1, "params");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // PARAMS
            return PARAMS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.PARAMS, new org.apache.thrift.meta_data.FieldMetaData("params", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, FreezeParamsVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(simpleFreeze_args.class, metaDataMap);
    }

    public simpleFreeze_args() {
    }

    public simpleFreeze_args(
      FreezeParamsVO params)
    {
      this();
      this.params = params;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public simpleFreeze_args(simpleFreeze_args other) {
      if (other.isSetParams()) {
        this.params = new FreezeParamsVO(other.params);
      }
    }

    public simpleFreeze_args deepCopy() {
      return new simpleFreeze_args(this);
    }

    @Override
    public void clear() {
      this.params = null;
    }

    public FreezeParamsVO getParams() {
      return this.params;
    }

    public simpleFreeze_args setParams(FreezeParamsVO params) {
      this.params = params;
      return this;
    }

    public void unsetParams() {
      this.params = null;
    }

    /** Returns true if field params is set (has been assigned a value) and false otherwise */
    public boolean isSetParams() {
      return this.params != null;
    }

    public void setParamsIsSet(boolean value) {
      if (!value) {
        this.params = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case PARAMS:
        if (value == null) {
          unsetParams();
        } else {
          setParams((FreezeParamsVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case PARAMS:
        return getParams();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case PARAMS:
        return isSetParams();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof simpleFreeze_args)
        return this.equals((simpleFreeze_args)that);
      return false;
    }

    public boolean equals(simpleFreeze_args that) {
      if (that == null)
        return false;

      boolean this_present_params = true && this.isSetParams();
      boolean that_present_params = true && that.isSetParams();
      if (this_present_params || that_present_params) {
        if (!(this_present_params && that_present_params))
          return false;
        if (!this.params.equals(that.params))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_params = true && (isSetParams());
      list.add(present_params);
      if (present_params)
        list.add(params);

      return list.hashCode();
    }

    @Override
    public int compareTo(simpleFreeze_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetParams()).compareTo(other.isSetParams());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetParams()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.params, other.params);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("simpleFreeze_args(");
      boolean first = true;

      sb.append("params:");
      if (this.params == null) {
        sb.append("null");
      } else {
        sb.append(this.params);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (params == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'params' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (params != null) {
        params.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class simpleFreeze_argsStandardSchemeFactory implements SchemeFactory {
      public simpleFreeze_argsStandardScheme getScheme() {
        return new simpleFreeze_argsStandardScheme();
      }
    }

    private static class simpleFreeze_argsStandardScheme extends StandardScheme<simpleFreeze_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, simpleFreeze_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // PARAMS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.params = new FreezeParamsVO();
                struct.params.read(iprot);
                struct.setParamsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, simpleFreeze_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.params != null) {
          oprot.writeFieldBegin(PARAMS_FIELD_DESC);
          struct.params.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class simpleFreeze_argsTupleSchemeFactory implements SchemeFactory {
      public simpleFreeze_argsTupleScheme getScheme() {
        return new simpleFreeze_argsTupleScheme();
      }
    }

    private static class simpleFreeze_argsTupleScheme extends TupleScheme<simpleFreeze_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, simpleFreeze_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.params.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, simpleFreeze_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.params = new FreezeParamsVO();
        struct.params.read(iprot);
        struct.setParamsIsSet(true);
      }
    }

  }

  public static class simpleFreeze_result implements org.apache.thrift.TBase<simpleFreeze_result, simpleFreeze_result._Fields>, java.io.Serializable, Cloneable, Comparable<simpleFreeze_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("simpleFreeze_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new simpleFreeze_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new simpleFreeze_resultTupleSchemeFactory());
    }

    public ReturnResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ReturnResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(simpleFreeze_result.class, metaDataMap);
    }

    public simpleFreeze_result() {
    }

    public simpleFreeze_result(
      ReturnResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public simpleFreeze_result(simpleFreeze_result other) {
      if (other.isSetSuccess()) {
        this.success = new ReturnResult(other.success);
      }
    }

    public simpleFreeze_result deepCopy() {
      return new simpleFreeze_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ReturnResult getSuccess() {
      return this.success;
    }

    public simpleFreeze_result setSuccess(ReturnResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ReturnResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof simpleFreeze_result)
        return this.equals((simpleFreeze_result)that);
      return false;
    }

    public boolean equals(simpleFreeze_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(simpleFreeze_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("simpleFreeze_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class simpleFreeze_resultStandardSchemeFactory implements SchemeFactory {
      public simpleFreeze_resultStandardScheme getScheme() {
        return new simpleFreeze_resultStandardScheme();
      }
    }

    private static class simpleFreeze_resultStandardScheme extends StandardScheme<simpleFreeze_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, simpleFreeze_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ReturnResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, simpleFreeze_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class simpleFreeze_resultTupleSchemeFactory implements SchemeFactory {
      public simpleFreeze_resultTupleScheme getScheme() {
        return new simpleFreeze_resultTupleScheme();
      }
    }

    private static class simpleFreeze_resultTupleScheme extends TupleScheme<simpleFreeze_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, simpleFreeze_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, simpleFreeze_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ReturnResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class confirm_args implements org.apache.thrift.TBase<confirm_args, confirm_args._Fields>, java.io.Serializable, Cloneable, Comparable<confirm_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("confirm_args");

    private static final org.apache.thrift.protocol.TField PARAMS_FIELD_DESC = new org.apache.thrift.protocol.TField("params", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new confirm_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new confirm_argsTupleSchemeFactory());
    }

    public UnFreezeVO params; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      PARAMS((short)1, "params");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // PARAMS
            return PARAMS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.PARAMS, new org.apache.thrift.meta_data.FieldMetaData("params", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, UnFreezeVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(confirm_args.class, metaDataMap);
    }

    public confirm_args() {
    }

    public confirm_args(
      UnFreezeVO params)
    {
      this();
      this.params = params;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public confirm_args(confirm_args other) {
      if (other.isSetParams()) {
        this.params = new UnFreezeVO(other.params);
      }
    }

    public confirm_args deepCopy() {
      return new confirm_args(this);
    }

    @Override
    public void clear() {
      this.params = null;
    }

    public UnFreezeVO getParams() {
      return this.params;
    }

    public confirm_args setParams(UnFreezeVO params) {
      this.params = params;
      return this;
    }

    public void unsetParams() {
      this.params = null;
    }

    /** Returns true if field params is set (has been assigned a value) and false otherwise */
    public boolean isSetParams() {
      return this.params != null;
    }

    public void setParamsIsSet(boolean value) {
      if (!value) {
        this.params = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case PARAMS:
        if (value == null) {
          unsetParams();
        } else {
          setParams((UnFreezeVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case PARAMS:
        return getParams();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case PARAMS:
        return isSetParams();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof confirm_args)
        return this.equals((confirm_args)that);
      return false;
    }

    public boolean equals(confirm_args that) {
      if (that == null)
        return false;

      boolean this_present_params = true && this.isSetParams();
      boolean that_present_params = true && that.isSetParams();
      if (this_present_params || that_present_params) {
        if (!(this_present_params && that_present_params))
          return false;
        if (!this.params.equals(that.params))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_params = true && (isSetParams());
      list.add(present_params);
      if (present_params)
        list.add(params);

      return list.hashCode();
    }

    @Override
    public int compareTo(confirm_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetParams()).compareTo(other.isSetParams());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetParams()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.params, other.params);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("confirm_args(");
      boolean first = true;

      sb.append("params:");
      if (this.params == null) {
        sb.append("null");
      } else {
        sb.append(this.params);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (params == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'params' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (params != null) {
        params.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class confirm_argsStandardSchemeFactory implements SchemeFactory {
      public confirm_argsStandardScheme getScheme() {
        return new confirm_argsStandardScheme();
      }
    }

    private static class confirm_argsStandardScheme extends StandardScheme<confirm_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, confirm_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // PARAMS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.params = new UnFreezeVO();
                struct.params.read(iprot);
                struct.setParamsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, confirm_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.params != null) {
          oprot.writeFieldBegin(PARAMS_FIELD_DESC);
          struct.params.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class confirm_argsTupleSchemeFactory implements SchemeFactory {
      public confirm_argsTupleScheme getScheme() {
        return new confirm_argsTupleScheme();
      }
    }

    private static class confirm_argsTupleScheme extends TupleScheme<confirm_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, confirm_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.params.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, confirm_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.params = new UnFreezeVO();
        struct.params.read(iprot);
        struct.setParamsIsSet(true);
      }
    }

  }

  public static class confirm_result implements org.apache.thrift.TBase<confirm_result, confirm_result._Fields>, java.io.Serializable, Cloneable, Comparable<confirm_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("confirm_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new confirm_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new confirm_resultTupleSchemeFactory());
    }

    public ReturnResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ReturnResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(confirm_result.class, metaDataMap);
    }

    public confirm_result() {
    }

    public confirm_result(
      ReturnResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public confirm_result(confirm_result other) {
      if (other.isSetSuccess()) {
        this.success = new ReturnResult(other.success);
      }
    }

    public confirm_result deepCopy() {
      return new confirm_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ReturnResult getSuccess() {
      return this.success;
    }

    public confirm_result setSuccess(ReturnResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ReturnResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof confirm_result)
        return this.equals((confirm_result)that);
      return false;
    }

    public boolean equals(confirm_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(confirm_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("confirm_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class confirm_resultStandardSchemeFactory implements SchemeFactory {
      public confirm_resultStandardScheme getScheme() {
        return new confirm_resultStandardScheme();
      }
    }

    private static class confirm_resultStandardScheme extends StandardScheme<confirm_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, confirm_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ReturnResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, confirm_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class confirm_resultTupleSchemeFactory implements SchemeFactory {
      public confirm_resultTupleScheme getScheme() {
        return new confirm_resultTupleScheme();
      }
    }

    private static class confirm_resultTupleScheme extends TupleScheme<confirm_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, confirm_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, confirm_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ReturnResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class cancel_args implements org.apache.thrift.TBase<cancel_args, cancel_args._Fields>, java.io.Serializable, Cloneable, Comparable<cancel_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("cancel_args");

    private static final org.apache.thrift.protocol.TField PARAMS_FIELD_DESC = new org.apache.thrift.protocol.TField("params", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new cancel_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new cancel_argsTupleSchemeFactory());
    }

    public UnFreezeVO params; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      PARAMS((short)1, "params");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // PARAMS
            return PARAMS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.PARAMS, new org.apache.thrift.meta_data.FieldMetaData("params", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, UnFreezeVO.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(cancel_args.class, metaDataMap);
    }

    public cancel_args() {
    }

    public cancel_args(
      UnFreezeVO params)
    {
      this();
      this.params = params;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public cancel_args(cancel_args other) {
      if (other.isSetParams()) {
        this.params = new UnFreezeVO(other.params);
      }
    }

    public cancel_args deepCopy() {
      return new cancel_args(this);
    }

    @Override
    public void clear() {
      this.params = null;
    }

    public UnFreezeVO getParams() {
      return this.params;
    }

    public cancel_args setParams(UnFreezeVO params) {
      this.params = params;
      return this;
    }

    public void unsetParams() {
      this.params = null;
    }

    /** Returns true if field params is set (has been assigned a value) and false otherwise */
    public boolean isSetParams() {
      return this.params != null;
    }

    public void setParamsIsSet(boolean value) {
      if (!value) {
        this.params = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case PARAMS:
        if (value == null) {
          unsetParams();
        } else {
          setParams((UnFreezeVO)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case PARAMS:
        return getParams();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case PARAMS:
        return isSetParams();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof cancel_args)
        return this.equals((cancel_args)that);
      return false;
    }

    public boolean equals(cancel_args that) {
      if (that == null)
        return false;

      boolean this_present_params = true && this.isSetParams();
      boolean that_present_params = true && that.isSetParams();
      if (this_present_params || that_present_params) {
        if (!(this_present_params && that_present_params))
          return false;
        if (!this.params.equals(that.params))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_params = true && (isSetParams());
      list.add(present_params);
      if (present_params)
        list.add(params);

      return list.hashCode();
    }

    @Override
    public int compareTo(cancel_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetParams()).compareTo(other.isSetParams());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetParams()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.params, other.params);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("cancel_args(");
      boolean first = true;

      sb.append("params:");
      if (this.params == null) {
        sb.append("null");
      } else {
        sb.append(this.params);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (params == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'params' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (params != null) {
        params.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class cancel_argsStandardSchemeFactory implements SchemeFactory {
      public cancel_argsStandardScheme getScheme() {
        return new cancel_argsStandardScheme();
      }
    }

    private static class cancel_argsStandardScheme extends StandardScheme<cancel_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, cancel_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // PARAMS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.params = new UnFreezeVO();
                struct.params.read(iprot);
                struct.setParamsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, cancel_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.params != null) {
          oprot.writeFieldBegin(PARAMS_FIELD_DESC);
          struct.params.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class cancel_argsTupleSchemeFactory implements SchemeFactory {
      public cancel_argsTupleScheme getScheme() {
        return new cancel_argsTupleScheme();
      }
    }

    private static class cancel_argsTupleScheme extends TupleScheme<cancel_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, cancel_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.params.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, cancel_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.params = new UnFreezeVO();
        struct.params.read(iprot);
        struct.setParamsIsSet(true);
      }
    }

  }

  public static class cancel_result implements org.apache.thrift.TBase<cancel_result, cancel_result._Fields>, java.io.Serializable, Cloneable, Comparable<cancel_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("cancel_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new cancel_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new cancel_resultTupleSchemeFactory());
    }

    public ReturnResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ReturnResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(cancel_result.class, metaDataMap);
    }

    public cancel_result() {
    }

    public cancel_result(
      ReturnResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public cancel_result(cancel_result other) {
      if (other.isSetSuccess()) {
        this.success = new ReturnResult(other.success);
      }
    }

    public cancel_result deepCopy() {
      return new cancel_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ReturnResult getSuccess() {
      return this.success;
    }

    public cancel_result setSuccess(ReturnResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ReturnResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof cancel_result)
        return this.equals((cancel_result)that);
      return false;
    }

    public boolean equals(cancel_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(cancel_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("cancel_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class cancel_resultStandardSchemeFactory implements SchemeFactory {
      public cancel_resultStandardScheme getScheme() {
        return new cancel_resultStandardScheme();
      }
    }

    private static class cancel_resultStandardScheme extends StandardScheme<cancel_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, cancel_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ReturnResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, cancel_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class cancel_resultTupleSchemeFactory implements SchemeFactory {
      public cancel_resultTupleScheme getScheme() {
        return new cancel_resultTupleScheme();
      }
    }

    private static class cancel_resultTupleScheme extends TupleScheme<cancel_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, cancel_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, cancel_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ReturnResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class queryByCFrom_args implements org.apache.thrift.TBase<queryByCFrom_args, queryByCFrom_args._Fields>, java.io.Serializable, Cloneable, Comparable<queryByCFrom_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("queryByCFrom_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);
    private static final org.apache.thrift.protocol.TField C_FROM_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("cFromId", org.apache.thrift.protocol.TType.I32, (short)2);
    private static final org.apache.thrift.protocol.TField COUPON_STAT_FIELD_DESC = new org.apache.thrift.protocol.TField("couponStat", org.apache.thrift.protocol.TType.I16, (short)3);
    private static final org.apache.thrift.protocol.TField ACTION_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actionId", org.apache.thrift.protocol.TType.I32, (short)4);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new queryByCFrom_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new queryByCFrom_argsTupleSchemeFactory());
    }

    public long kugouId; // required
    public int cFromId; // required
    public short couponStat; // required
    public int actionId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId"),
      C_FROM_ID((short)2, "cFromId"),
      COUPON_STAT((short)3, "couponStat"),
      ACTION_ID((short)4, "actionId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          case 2: // C_FROM_ID
            return C_FROM_ID;
          case 3: // COUPON_STAT
            return COUPON_STAT;
          case 4: // ACTION_ID
            return ACTION_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private static final int __CFROMID_ISSET_ID = 1;
    private static final int __COUPONSTAT_ISSET_ID = 2;
    private static final int __ACTIONID_ISSET_ID = 3;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.C_FROM_ID, new org.apache.thrift.meta_data.FieldMetaData("cFromId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      tmpMap.put(_Fields.COUPON_STAT, new org.apache.thrift.meta_data.FieldMetaData("couponStat", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I16)));
      tmpMap.put(_Fields.ACTION_ID, new org.apache.thrift.meta_data.FieldMetaData("actionId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(queryByCFrom_args.class, metaDataMap);
    }

    public queryByCFrom_args() {
      this.couponStat = (short)1;

    }

    public queryByCFrom_args(
      long kugouId,
      int cFromId,
      short couponStat,
      int actionId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      this.cFromId = cFromId;
      setCFromIdIsSet(true);
      this.couponStat = couponStat;
      setCouponStatIsSet(true);
      this.actionId = actionId;
      setActionIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public queryByCFrom_args(queryByCFrom_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
      this.cFromId = other.cFromId;
      this.couponStat = other.couponStat;
      this.actionId = other.actionId;
    }

    public queryByCFrom_args deepCopy() {
      return new queryByCFrom_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
      setCFromIdIsSet(false);
      this.cFromId = 0;
      this.couponStat = (short)1;

      setActionIdIsSet(false);
      this.actionId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public queryByCFrom_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public int getCFromId() {
      return this.cFromId;
    }

    public queryByCFrom_args setCFromId(int cFromId) {
      this.cFromId = cFromId;
      setCFromIdIsSet(true);
      return this;
    }

    public void unsetCFromId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CFROMID_ISSET_ID);
    }

    /** Returns true if field cFromId is set (has been assigned a value) and false otherwise */
    public boolean isSetCFromId() {
      return EncodingUtils.testBit(__isset_bitfield, __CFROMID_ISSET_ID);
    }

    public void setCFromIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CFROMID_ISSET_ID, value);
    }

    public short getCouponStat() {
      return this.couponStat;
    }

    public queryByCFrom_args setCouponStat(short couponStat) {
      this.couponStat = couponStat;
      setCouponStatIsSet(true);
      return this;
    }

    public void unsetCouponStat() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COUPONSTAT_ISSET_ID);
    }

    /** Returns true if field couponStat is set (has been assigned a value) and false otherwise */
    public boolean isSetCouponStat() {
      return EncodingUtils.testBit(__isset_bitfield, __COUPONSTAT_ISSET_ID);
    }

    public void setCouponStatIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COUPONSTAT_ISSET_ID, value);
    }

    public int getActionId() {
      return this.actionId;
    }

    public queryByCFrom_args setActionId(int actionId) {
      this.actionId = actionId;
      setActionIdIsSet(true);
      return this;
    }

    public void unsetActionId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTIONID_ISSET_ID);
    }

    /** Returns true if field actionId is set (has been assigned a value) and false otherwise */
    public boolean isSetActionId() {
      return EncodingUtils.testBit(__isset_bitfield, __ACTIONID_ISSET_ID);
    }

    public void setActionIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTIONID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      case C_FROM_ID:
        if (value == null) {
          unsetCFromId();
        } else {
          setCFromId((Integer)value);
        }
        break;

      case COUPON_STAT:
        if (value == null) {
          unsetCouponStat();
        } else {
          setCouponStat((Short)value);
        }
        break;

      case ACTION_ID:
        if (value == null) {
          unsetActionId();
        } else {
          setActionId((Integer)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      case C_FROM_ID:
        return getCFromId();

      case COUPON_STAT:
        return getCouponStat();

      case ACTION_ID:
        return getActionId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      case C_FROM_ID:
        return isSetCFromId();
      case COUPON_STAT:
        return isSetCouponStat();
      case ACTION_ID:
        return isSetActionId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof queryByCFrom_args)
        return this.equals((queryByCFrom_args)that);
      return false;
    }

    public boolean equals(queryByCFrom_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      boolean this_present_cFromId = true;
      boolean that_present_cFromId = true;
      if (this_present_cFromId || that_present_cFromId) {
        if (!(this_present_cFromId && that_present_cFromId))
          return false;
        if (this.cFromId != that.cFromId)
          return false;
      }

      boolean this_present_couponStat = true;
      boolean that_present_couponStat = true;
      if (this_present_couponStat || that_present_couponStat) {
        if (!(this_present_couponStat && that_present_couponStat))
          return false;
        if (this.couponStat != that.couponStat)
          return false;
      }

      boolean this_present_actionId = true;
      boolean that_present_actionId = true;
      if (this_present_actionId || that_present_actionId) {
        if (!(this_present_actionId && that_present_actionId))
          return false;
        if (this.actionId != that.actionId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      boolean present_cFromId = true;
      list.add(present_cFromId);
      if (present_cFromId)
        list.add(cFromId);

      boolean present_couponStat = true;
      list.add(present_couponStat);
      if (present_couponStat)
        list.add(couponStat);

      boolean present_actionId = true;
      list.add(present_actionId);
      if (present_actionId)
        list.add(actionId);

      return list.hashCode();
    }

    @Override
    public int compareTo(queryByCFrom_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetCFromId()).compareTo(other.isSetCFromId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetCFromId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.cFromId, other.cFromId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetCouponStat()).compareTo(other.isSetCouponStat());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetCouponStat()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponStat, other.couponStat);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetActionId()).compareTo(other.isSetActionId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetActionId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actionId, other.actionId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("queryByCFrom_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      if (!first) sb.append(", ");
      sb.append("cFromId:");
      sb.append(this.cFromId);
      first = false;
      if (!first) sb.append(", ");
      sb.append("couponStat:");
      sb.append(this.couponStat);
      first = false;
      if (!first) sb.append(", ");
      sb.append("actionId:");
      sb.append(this.actionId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
      // alas, we cannot check 'cFromId' because it's a primitive and you chose the non-beans generator.
      // alas, we cannot check 'couponStat' because it's a primitive and you chose the non-beans generator.
      // alas, we cannot check 'actionId' because it's a primitive and you chose the non-beans generator.
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class queryByCFrom_argsStandardSchemeFactory implements SchemeFactory {
      public queryByCFrom_argsStandardScheme getScheme() {
        return new queryByCFrom_argsStandardScheme();
      }
    }

    private static class queryByCFrom_argsStandardScheme extends StandardScheme<queryByCFrom_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, queryByCFrom_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // C_FROM_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.cFromId = iprot.readI32();
                struct.setCFromIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // COUPON_STAT
              if (schemeField.type == org.apache.thrift.protocol.TType.I16) {
                struct.couponStat = iprot.readI16();
                struct.setCouponStatIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 4: // ACTION_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.actionId = iprot.readI32();
                struct.setActionIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        if (!struct.isSetKugouId()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
        }
        if (!struct.isSetCFromId()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'cFromId' was not found in serialized data! Struct: " + toString());
        }
        if (!struct.isSetCouponStat()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'couponStat' was not found in serialized data! Struct: " + toString());
        }
        if (!struct.isSetActionId()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'actionId' was not found in serialized data! Struct: " + toString());
        }
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, queryByCFrom_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(C_FROM_ID_FIELD_DESC);
        oprot.writeI32(struct.cFromId);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(COUPON_STAT_FIELD_DESC);
        oprot.writeI16(struct.couponStat);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(ACTION_ID_FIELD_DESC);
        oprot.writeI32(struct.actionId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class queryByCFrom_argsTupleSchemeFactory implements SchemeFactory {
      public queryByCFrom_argsTupleScheme getScheme() {
        return new queryByCFrom_argsTupleScheme();
      }
    }

    private static class queryByCFrom_argsTupleScheme extends TupleScheme<queryByCFrom_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, queryByCFrom_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        oprot.writeI64(struct.kugouId);
        oprot.writeI32(struct.cFromId);
        oprot.writeI16(struct.couponStat);
        oprot.writeI32(struct.actionId);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, queryByCFrom_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.kugouId = iprot.readI64();
        struct.setKugouIdIsSet(true);
        struct.cFromId = iprot.readI32();
        struct.setCFromIdIsSet(true);
        struct.couponStat = iprot.readI16();
        struct.setCouponStatIsSet(true);
        struct.actionId = iprot.readI32();
        struct.setActionIdIsSet(true);
      }
    }

  }

  public static class queryByCFrom_result implements org.apache.thrift.TBase<queryByCFrom_result, queryByCFrom_result._Fields>, java.io.Serializable, Cloneable, Comparable<queryByCFrom_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("queryByCFrom_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new queryByCFrom_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new queryByCFrom_resultTupleSchemeFactory());
    }

    public CouponListResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, CouponListResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(queryByCFrom_result.class, metaDataMap);
    }

    public queryByCFrom_result() {
    }

    public queryByCFrom_result(
      CouponListResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public queryByCFrom_result(queryByCFrom_result other) {
      if (other.isSetSuccess()) {
        this.success = new CouponListResult(other.success);
      }
    }

    public queryByCFrom_result deepCopy() {
      return new queryByCFrom_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public CouponListResult getSuccess() {
      return this.success;
    }

    public queryByCFrom_result setSuccess(CouponListResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((CouponListResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof queryByCFrom_result)
        return this.equals((queryByCFrom_result)that);
      return false;
    }

    public boolean equals(queryByCFrom_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(queryByCFrom_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("queryByCFrom_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class queryByCFrom_resultStandardSchemeFactory implements SchemeFactory {
      public queryByCFrom_resultStandardScheme getScheme() {
        return new queryByCFrom_resultStandardScheme();
      }
    }

    private static class queryByCFrom_resultStandardScheme extends StandardScheme<queryByCFrom_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, queryByCFrom_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new CouponListResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, queryByCFrom_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class queryByCFrom_resultTupleSchemeFactory implements SchemeFactory {
      public queryByCFrom_resultTupleScheme getScheme() {
        return new queryByCFrom_resultTupleScheme();
      }
    }

    private static class queryByCFrom_resultTupleScheme extends TupleScheme<queryByCFrom_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, queryByCFrom_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, queryByCFrom_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new CouponListResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class countByCFrom_args implements org.apache.thrift.TBase<countByCFrom_args, countByCFrom_args._Fields>, java.io.Serializable, Cloneable, Comparable<countByCFrom_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("countByCFrom_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);
    private static final org.apache.thrift.protocol.TField C_FROM_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("cFromId", org.apache.thrift.protocol.TType.I32, (short)2);
    private static final org.apache.thrift.protocol.TField ACTION_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actionId", org.apache.thrift.protocol.TType.I32, (short)3);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new countByCFrom_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new countByCFrom_argsTupleSchemeFactory());
    }

    public long kugouId; // required
    public int cFromId; // required
    public int actionId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId"),
      C_FROM_ID((short)2, "cFromId"),
      ACTION_ID((short)3, "actionId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          case 2: // C_FROM_ID
            return C_FROM_ID;
          case 3: // ACTION_ID
            return ACTION_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private static final int __CFROMID_ISSET_ID = 1;
    private static final int __ACTIONID_ISSET_ID = 2;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.C_FROM_ID, new org.apache.thrift.meta_data.FieldMetaData("cFromId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      tmpMap.put(_Fields.ACTION_ID, new org.apache.thrift.meta_data.FieldMetaData("actionId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(countByCFrom_args.class, metaDataMap);
    }

    public countByCFrom_args() {
    }

    public countByCFrom_args(
      long kugouId,
      int cFromId,
      int actionId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      this.cFromId = cFromId;
      setCFromIdIsSet(true);
      this.actionId = actionId;
      setActionIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public countByCFrom_args(countByCFrom_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
      this.cFromId = other.cFromId;
      this.actionId = other.actionId;
    }

    public countByCFrom_args deepCopy() {
      return new countByCFrom_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
      setCFromIdIsSet(false);
      this.cFromId = 0;
      setActionIdIsSet(false);
      this.actionId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public countByCFrom_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public int getCFromId() {
      return this.cFromId;
    }

    public countByCFrom_args setCFromId(int cFromId) {
      this.cFromId = cFromId;
      setCFromIdIsSet(true);
      return this;
    }

    public void unsetCFromId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CFROMID_ISSET_ID);
    }

    /** Returns true if field cFromId is set (has been assigned a value) and false otherwise */
    public boolean isSetCFromId() {
      return EncodingUtils.testBit(__isset_bitfield, __CFROMID_ISSET_ID);
    }

    public void setCFromIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CFROMID_ISSET_ID, value);
    }

    public int getActionId() {
      return this.actionId;
    }

    public countByCFrom_args setActionId(int actionId) {
      this.actionId = actionId;
      setActionIdIsSet(true);
      return this;
    }

    public void unsetActionId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTIONID_ISSET_ID);
    }

    /** Returns true if field actionId is set (has been assigned a value) and false otherwise */
    public boolean isSetActionId() {
      return EncodingUtils.testBit(__isset_bitfield, __ACTIONID_ISSET_ID);
    }

    public void setActionIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTIONID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      case C_FROM_ID:
        if (value == null) {
          unsetCFromId();
        } else {
          setCFromId((Integer)value);
        }
        break;

      case ACTION_ID:
        if (value == null) {
          unsetActionId();
        } else {
          setActionId((Integer)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      case C_FROM_ID:
        return getCFromId();

      case ACTION_ID:
        return getActionId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      case C_FROM_ID:
        return isSetCFromId();
      case ACTION_ID:
        return isSetActionId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof countByCFrom_args)
        return this.equals((countByCFrom_args)that);
      return false;
    }

    public boolean equals(countByCFrom_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      boolean this_present_cFromId = true;
      boolean that_present_cFromId = true;
      if (this_present_cFromId || that_present_cFromId) {
        if (!(this_present_cFromId && that_present_cFromId))
          return false;
        if (this.cFromId != that.cFromId)
          return false;
      }

      boolean this_present_actionId = true;
      boolean that_present_actionId = true;
      if (this_present_actionId || that_present_actionId) {
        if (!(this_present_actionId && that_present_actionId))
          return false;
        if (this.actionId != that.actionId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      boolean present_cFromId = true;
      list.add(present_cFromId);
      if (present_cFromId)
        list.add(cFromId);

      boolean present_actionId = true;
      list.add(present_actionId);
      if (present_actionId)
        list.add(actionId);

      return list.hashCode();
    }

    @Override
    public int compareTo(countByCFrom_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetCFromId()).compareTo(other.isSetCFromId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetCFromId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.cFromId, other.cFromId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetActionId()).compareTo(other.isSetActionId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetActionId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actionId, other.actionId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("countByCFrom_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      if (!first) sb.append(", ");
      sb.append("cFromId:");
      sb.append(this.cFromId);
      first = false;
      if (!first) sb.append(", ");
      sb.append("actionId:");
      sb.append(this.actionId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
      // alas, we cannot check 'cFromId' because it's a primitive and you chose the non-beans generator.
      // alas, we cannot check 'actionId' because it's a primitive and you chose the non-beans generator.
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class countByCFrom_argsStandardSchemeFactory implements SchemeFactory {
      public countByCFrom_argsStandardScheme getScheme() {
        return new countByCFrom_argsStandardScheme();
      }
    }

    private static class countByCFrom_argsStandardScheme extends StandardScheme<countByCFrom_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, countByCFrom_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // C_FROM_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.cFromId = iprot.readI32();
                struct.setCFromIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // ACTION_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.actionId = iprot.readI32();
                struct.setActionIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        if (!struct.isSetKugouId()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
        }
        if (!struct.isSetCFromId()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'cFromId' was not found in serialized data! Struct: " + toString());
        }
        if (!struct.isSetActionId()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'actionId' was not found in serialized data! Struct: " + toString());
        }
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, countByCFrom_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(C_FROM_ID_FIELD_DESC);
        oprot.writeI32(struct.cFromId);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(ACTION_ID_FIELD_DESC);
        oprot.writeI32(struct.actionId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class countByCFrom_argsTupleSchemeFactory implements SchemeFactory {
      public countByCFrom_argsTupleScheme getScheme() {
        return new countByCFrom_argsTupleScheme();
      }
    }

    private static class countByCFrom_argsTupleScheme extends TupleScheme<countByCFrom_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, countByCFrom_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        oprot.writeI64(struct.kugouId);
        oprot.writeI32(struct.cFromId);
        oprot.writeI32(struct.actionId);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, countByCFrom_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.kugouId = iprot.readI64();
        struct.setKugouIdIsSet(true);
        struct.cFromId = iprot.readI32();
        struct.setCFromIdIsSet(true);
        struct.actionId = iprot.readI32();
        struct.setActionIdIsSet(true);
      }
    }

  }

  public static class countByCFrom_result implements org.apache.thrift.TBase<countByCFrom_result, countByCFrom_result._Fields>, java.io.Serializable, Cloneable, Comparable<countByCFrom_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("countByCFrom_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new countByCFrom_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new countByCFrom_resultTupleSchemeFactory());
    }

    public CouponCountResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, CouponCountResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(countByCFrom_result.class, metaDataMap);
    }

    public countByCFrom_result() {
    }

    public countByCFrom_result(
      CouponCountResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public countByCFrom_result(countByCFrom_result other) {
      if (other.isSetSuccess()) {
        this.success = new CouponCountResult(other.success);
      }
    }

    public countByCFrom_result deepCopy() {
      return new countByCFrom_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public CouponCountResult getSuccess() {
      return this.success;
    }

    public countByCFrom_result setSuccess(CouponCountResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((CouponCountResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof countByCFrom_result)
        return this.equals((countByCFrom_result)that);
      return false;
    }

    public boolean equals(countByCFrom_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(countByCFrom_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("countByCFrom_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class countByCFrom_resultStandardSchemeFactory implements SchemeFactory {
      public countByCFrom_resultStandardScheme getScheme() {
        return new countByCFrom_resultStandardScheme();
      }
    }

    private static class countByCFrom_resultStandardScheme extends StandardScheme<countByCFrom_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, countByCFrom_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new CouponCountResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, countByCFrom_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class countByCFrom_resultTupleSchemeFactory implements SchemeFactory {
      public countByCFrom_resultTupleScheme getScheme() {
        return new countByCFrom_resultTupleScheme();
      }
    }

    private static class countByCFrom_resultTupleScheme extends TupleScheme<countByCFrom_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, countByCFrom_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, countByCFrom_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new CouponCountResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class queryCoupon_args implements org.apache.thrift.TBase<queryCoupon_args, queryCoupon_args._Fields>, java.io.Serializable, Cloneable, Comparable<queryCoupon_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("queryCoupon_args");

    private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);
    private static final org.apache.thrift.protocol.TField COUPON_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("couponId", org.apache.thrift.protocol.TType.I64, (short)2);
    private static final org.apache.thrift.protocol.TField ACTION_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actionId", org.apache.thrift.protocol.TType.I32, (short)3);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new queryCoupon_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new queryCoupon_argsTupleSchemeFactory());
    }

    public long kugouId; // required
    public long couponId; // required
    public int actionId; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      KUGOU_ID((short)1, "kugouId"),
      COUPON_ID((short)2, "couponId"),
      ACTION_ID((short)3, "actionId");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // KUGOU_ID
            return KUGOU_ID;
          case 2: // COUPON_ID
            return COUPON_ID;
          case 3: // ACTION_ID
            return ACTION_ID;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __KUGOUID_ISSET_ID = 0;
    private static final int __COUPONID_ISSET_ID = 1;
    private static final int __ACTIONID_ISSET_ID = 2;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.COUPON_ID, new org.apache.thrift.meta_data.FieldMetaData("couponId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.ACTION_ID, new org.apache.thrift.meta_data.FieldMetaData("actionId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(queryCoupon_args.class, metaDataMap);
    }

    public queryCoupon_args() {
    }

    public queryCoupon_args(
      long kugouId,
      long couponId,
      int actionId)
    {
      this();
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      this.couponId = couponId;
      setCouponIdIsSet(true);
      this.actionId = actionId;
      setActionIdIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public queryCoupon_args(queryCoupon_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.kugouId = other.kugouId;
      this.couponId = other.couponId;
      this.actionId = other.actionId;
    }

    public queryCoupon_args deepCopy() {
      return new queryCoupon_args(this);
    }

    @Override
    public void clear() {
      setKugouIdIsSet(false);
      this.kugouId = 0;
      setCouponIdIsSet(false);
      this.couponId = 0;
      setActionIdIsSet(false);
      this.actionId = 0;
    }

    public long getKugouId() {
      return this.kugouId;
    }

    public queryCoupon_args setKugouId(long kugouId) {
      this.kugouId = kugouId;
      setKugouIdIsSet(true);
      return this;
    }

    public void unsetKugouId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
    public boolean isSetKugouId() {
      return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
    }

    public void setKugouIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
    }

    public long getCouponId() {
      return this.couponId;
    }

    public queryCoupon_args setCouponId(long couponId) {
      this.couponId = couponId;
      setCouponIdIsSet(true);
      return this;
    }

    public void unsetCouponId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COUPONID_ISSET_ID);
    }

    /** Returns true if field couponId is set (has been assigned a value) and false otherwise */
    public boolean isSetCouponId() {
      return EncodingUtils.testBit(__isset_bitfield, __COUPONID_ISSET_ID);
    }

    public void setCouponIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COUPONID_ISSET_ID, value);
    }

    public int getActionId() {
      return this.actionId;
    }

    public queryCoupon_args setActionId(int actionId) {
      this.actionId = actionId;
      setActionIdIsSet(true);
      return this;
    }

    public void unsetActionId() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTIONID_ISSET_ID);
    }

    /** Returns true if field actionId is set (has been assigned a value) and false otherwise */
    public boolean isSetActionId() {
      return EncodingUtils.testBit(__isset_bitfield, __ACTIONID_ISSET_ID);
    }

    public void setActionIdIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTIONID_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case KUGOU_ID:
        if (value == null) {
          unsetKugouId();
        } else {
          setKugouId((Long)value);
        }
        break;

      case COUPON_ID:
        if (value == null) {
          unsetCouponId();
        } else {
          setCouponId((Long)value);
        }
        break;

      case ACTION_ID:
        if (value == null) {
          unsetActionId();
        } else {
          setActionId((Integer)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case KUGOU_ID:
        return getKugouId();

      case COUPON_ID:
        return getCouponId();

      case ACTION_ID:
        return getActionId();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case KUGOU_ID:
        return isSetKugouId();
      case COUPON_ID:
        return isSetCouponId();
      case ACTION_ID:
        return isSetActionId();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof queryCoupon_args)
        return this.equals((queryCoupon_args)that);
      return false;
    }

    public boolean equals(queryCoupon_args that) {
      if (that == null)
        return false;

      boolean this_present_kugouId = true;
      boolean that_present_kugouId = true;
      if (this_present_kugouId || that_present_kugouId) {
        if (!(this_present_kugouId && that_present_kugouId))
          return false;
        if (this.kugouId != that.kugouId)
          return false;
      }

      boolean this_present_couponId = true;
      boolean that_present_couponId = true;
      if (this_present_couponId || that_present_couponId) {
        if (!(this_present_couponId && that_present_couponId))
          return false;
        if (this.couponId != that.couponId)
          return false;
      }

      boolean this_present_actionId = true;
      boolean that_present_actionId = true;
      if (this_present_actionId || that_present_actionId) {
        if (!(this_present_actionId && that_present_actionId))
          return false;
        if (this.actionId != that.actionId)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_kugouId = true;
      list.add(present_kugouId);
      if (present_kugouId)
        list.add(kugouId);

      boolean present_couponId = true;
      list.add(present_couponId);
      if (present_couponId)
        list.add(couponId);

      boolean present_actionId = true;
      list.add(present_actionId);
      if (present_actionId)
        list.add(actionId);

      return list.hashCode();
    }

    @Override
    public int compareTo(queryCoupon_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetKugouId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetCouponId()).compareTo(other.isSetCouponId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetCouponId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponId, other.couponId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetActionId()).compareTo(other.isSetActionId());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetActionId()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actionId, other.actionId);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("queryCoupon_args(");
      boolean first = true;

      sb.append("kugouId:");
      sb.append(this.kugouId);
      first = false;
      if (!first) sb.append(", ");
      sb.append("couponId:");
      sb.append(this.couponId);
      first = false;
      if (!first) sb.append(", ");
      sb.append("actionId:");
      sb.append(this.actionId);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
      // alas, we cannot check 'couponId' because it's a primitive and you chose the non-beans generator.
      // alas, we cannot check 'actionId' because it's a primitive and you chose the non-beans generator.
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class queryCoupon_argsStandardSchemeFactory implements SchemeFactory {
      public queryCoupon_argsStandardScheme getScheme() {
        return new queryCoupon_argsStandardScheme();
      }
    }

    private static class queryCoupon_argsStandardScheme extends StandardScheme<queryCoupon_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, queryCoupon_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // KUGOU_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.kugouId = iprot.readI64();
                struct.setKugouIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // COUPON_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.couponId = iprot.readI64();
                struct.setCouponIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // ACTION_ID
              if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
                struct.actionId = iprot.readI32();
                struct.setActionIdIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        if (!struct.isSetKugouId()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
        }
        if (!struct.isSetCouponId()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'couponId' was not found in serialized data! Struct: " + toString());
        }
        if (!struct.isSetActionId()) {
          throw new org.apache.thrift.protocol.TProtocolException("Required field 'actionId' was not found in serialized data! Struct: " + toString());
        }
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, queryCoupon_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
        oprot.writeI64(struct.kugouId);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(COUPON_ID_FIELD_DESC);
        oprot.writeI64(struct.couponId);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(ACTION_ID_FIELD_DESC);
        oprot.writeI32(struct.actionId);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class queryCoupon_argsTupleSchemeFactory implements SchemeFactory {
      public queryCoupon_argsTupleScheme getScheme() {
        return new queryCoupon_argsTupleScheme();
      }
    }

    private static class queryCoupon_argsTupleScheme extends TupleScheme<queryCoupon_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, queryCoupon_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        oprot.writeI64(struct.kugouId);
        oprot.writeI64(struct.couponId);
        oprot.writeI32(struct.actionId);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, queryCoupon_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.kugouId = iprot.readI64();
        struct.setKugouIdIsSet(true);
        struct.couponId = iprot.readI64();
        struct.setCouponIdIsSet(true);
        struct.actionId = iprot.readI32();
        struct.setActionIdIsSet(true);
      }
    }

  }

  public static class queryCoupon_result implements org.apache.thrift.TBase<queryCoupon_result, queryCoupon_result._Fields>, java.io.Serializable, Cloneable, Comparable<queryCoupon_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("queryCoupon_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new queryCoupon_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new queryCoupon_resultTupleSchemeFactory());
    }

    public CouponQueryResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, CouponQueryResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(queryCoupon_result.class, metaDataMap);
    }

    public queryCoupon_result() {
    }

    public queryCoupon_result(
      CouponQueryResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public queryCoupon_result(queryCoupon_result other) {
      if (other.isSetSuccess()) {
        this.success = new CouponQueryResult(other.success);
      }
    }

    public queryCoupon_result deepCopy() {
      return new queryCoupon_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public CouponQueryResult getSuccess() {
      return this.success;
    }

    public queryCoupon_result setSuccess(CouponQueryResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((CouponQueryResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof queryCoupon_result)
        return this.equals((queryCoupon_result)that);
      return false;
    }

    public boolean equals(queryCoupon_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(queryCoupon_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("queryCoupon_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class queryCoupon_resultStandardSchemeFactory implements SchemeFactory {
      public queryCoupon_resultStandardScheme getScheme() {
        return new queryCoupon_resultStandardScheme();
      }
    }

    private static class queryCoupon_resultStandardScheme extends StandardScheme<queryCoupon_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, queryCoupon_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new CouponQueryResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, queryCoupon_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class queryCoupon_resultTupleSchemeFactory implements SchemeFactory {
      public queryCoupon_resultTupleScheme getScheme() {
        return new queryCoupon_resultTupleScheme();
      }
    }

    private static class queryCoupon_resultTupleScheme extends TupleScheme<queryCoupon_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, queryCoupon_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, queryCoupon_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new CouponQueryResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getPersonalCouponNum_args implements org.apache.thrift.TBase<getPersonalCouponNum_args, getPersonalCouponNum_args._Fields>, java.io.Serializable, Cloneable, Comparable<getPersonalCouponNum_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getPersonalCouponNum_args");

    private static final org.apache.thrift.protocol.TField PARAM_FIELD_DESC = new org.apache.thrift.protocol.TField("param", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getPersonalCouponNum_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getPersonalCouponNum_argsTupleSchemeFactory());
    }

    public CouponListRequestParam param; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      PARAM((short)1, "param");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // PARAM
            return PARAM;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.PARAM, new org.apache.thrift.meta_data.FieldMetaData("param", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, CouponListRequestParam.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getPersonalCouponNum_args.class, metaDataMap);
    }

    public getPersonalCouponNum_args() {
    }

    public getPersonalCouponNum_args(
      CouponListRequestParam param)
    {
      this();
      this.param = param;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getPersonalCouponNum_args(getPersonalCouponNum_args other) {
      if (other.isSetParam()) {
        this.param = new CouponListRequestParam(other.param);
      }
    }

    public getPersonalCouponNum_args deepCopy() {
      return new getPersonalCouponNum_args(this);
    }

    @Override
    public void clear() {
      this.param = null;
    }

    public CouponListRequestParam getParam() {
      return this.param;
    }

    public getPersonalCouponNum_args setParam(CouponListRequestParam param) {
      this.param = param;
      return this;
    }

    public void unsetParam() {
      this.param = null;
    }

    /** Returns true if field param is set (has been assigned a value) and false otherwise */
    public boolean isSetParam() {
      return this.param != null;
    }

    public void setParamIsSet(boolean value) {
      if (!value) {
        this.param = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case PARAM:
        if (value == null) {
          unsetParam();
        } else {
          setParam((CouponListRequestParam)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case PARAM:
        return getParam();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case PARAM:
        return isSetParam();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getPersonalCouponNum_args)
        return this.equals((getPersonalCouponNum_args)that);
      return false;
    }

    public boolean equals(getPersonalCouponNum_args that) {
      if (that == null)
        return false;

      boolean this_present_param = true && this.isSetParam();
      boolean that_present_param = true && that.isSetParam();
      if (this_present_param || that_present_param) {
        if (!(this_present_param && that_present_param))
          return false;
        if (!this.param.equals(that.param))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_param = true && (isSetParam());
      list.add(present_param);
      if (present_param)
        list.add(param);

      return list.hashCode();
    }

    @Override
    public int compareTo(getPersonalCouponNum_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetParam()).compareTo(other.isSetParam());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetParam()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.param, other.param);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getPersonalCouponNum_args(");
      boolean first = true;

      sb.append("param:");
      if (this.param == null) {
        sb.append("null");
      } else {
        sb.append(this.param);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (param == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'param' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (param != null) {
        param.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getPersonalCouponNum_argsStandardSchemeFactory implements SchemeFactory {
      public getPersonalCouponNum_argsStandardScheme getScheme() {
        return new getPersonalCouponNum_argsStandardScheme();
      }
    }

    private static class getPersonalCouponNum_argsStandardScheme extends StandardScheme<getPersonalCouponNum_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getPersonalCouponNum_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // PARAM
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.param = new CouponListRequestParam();
                struct.param.read(iprot);
                struct.setParamIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getPersonalCouponNum_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.param != null) {
          oprot.writeFieldBegin(PARAM_FIELD_DESC);
          struct.param.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getPersonalCouponNum_argsTupleSchemeFactory implements SchemeFactory {
      public getPersonalCouponNum_argsTupleScheme getScheme() {
        return new getPersonalCouponNum_argsTupleScheme();
      }
    }

    private static class getPersonalCouponNum_argsTupleScheme extends TupleScheme<getPersonalCouponNum_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getPersonalCouponNum_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.param.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getPersonalCouponNum_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.param = new CouponListRequestParam();
        struct.param.read(iprot);
        struct.setParamIsSet(true);
      }
    }

  }

  public static class getPersonalCouponNum_result implements org.apache.thrift.TBase<getPersonalCouponNum_result, getPersonalCouponNum_result._Fields>, java.io.Serializable, Cloneable, Comparable<getPersonalCouponNum_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getPersonalCouponNum_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getPersonalCouponNum_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getPersonalCouponNum_resultTupleSchemeFactory());
    }

    public PersonalCouponNumResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PersonalCouponNumResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getPersonalCouponNum_result.class, metaDataMap);
    }

    public getPersonalCouponNum_result() {
    }

    public getPersonalCouponNum_result(
      PersonalCouponNumResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getPersonalCouponNum_result(getPersonalCouponNum_result other) {
      if (other.isSetSuccess()) {
        this.success = new PersonalCouponNumResult(other.success);
      }
    }

    public getPersonalCouponNum_result deepCopy() {
      return new getPersonalCouponNum_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public PersonalCouponNumResult getSuccess() {
      return this.success;
    }

    public getPersonalCouponNum_result setSuccess(PersonalCouponNumResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((PersonalCouponNumResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getPersonalCouponNum_result)
        return this.equals((getPersonalCouponNum_result)that);
      return false;
    }

    public boolean equals(getPersonalCouponNum_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getPersonalCouponNum_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getPersonalCouponNum_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getPersonalCouponNum_resultStandardSchemeFactory implements SchemeFactory {
      public getPersonalCouponNum_resultStandardScheme getScheme() {
        return new getPersonalCouponNum_resultStandardScheme();
      }
    }

    private static class getPersonalCouponNum_resultStandardScheme extends StandardScheme<getPersonalCouponNum_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getPersonalCouponNum_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new PersonalCouponNumResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getPersonalCouponNum_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getPersonalCouponNum_resultTupleSchemeFactory implements SchemeFactory {
      public getPersonalCouponNum_resultTupleScheme getScheme() {
        return new getPersonalCouponNum_resultTupleScheme();
      }
    }

    private static class getPersonalCouponNum_resultTupleScheme extends TupleScheme<getPersonalCouponNum_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getPersonalCouponNum_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getPersonalCouponNum_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new PersonalCouponNumResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getPersonalCouponList_args implements org.apache.thrift.TBase<getPersonalCouponList_args, getPersonalCouponList_args._Fields>, java.io.Serializable, Cloneable, Comparable<getPersonalCouponList_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getPersonalCouponList_args");

    private static final org.apache.thrift.protocol.TField PARAM_FIELD_DESC = new org.apache.thrift.protocol.TField("param", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getPersonalCouponList_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getPersonalCouponList_argsTupleSchemeFactory());
    }

    public CouponListRequestParam param; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      PARAM((short)1, "param");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // PARAM
            return PARAM;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.PARAM, new org.apache.thrift.meta_data.FieldMetaData("param", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, CouponListRequestParam.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getPersonalCouponList_args.class, metaDataMap);
    }

    public getPersonalCouponList_args() {
    }

    public getPersonalCouponList_args(
      CouponListRequestParam param)
    {
      this();
      this.param = param;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getPersonalCouponList_args(getPersonalCouponList_args other) {
      if (other.isSetParam()) {
        this.param = new CouponListRequestParam(other.param);
      }
    }

    public getPersonalCouponList_args deepCopy() {
      return new getPersonalCouponList_args(this);
    }

    @Override
    public void clear() {
      this.param = null;
    }

    public CouponListRequestParam getParam() {
      return this.param;
    }

    public getPersonalCouponList_args setParam(CouponListRequestParam param) {
      this.param = param;
      return this;
    }

    public void unsetParam() {
      this.param = null;
    }

    /** Returns true if field param is set (has been assigned a value) and false otherwise */
    public boolean isSetParam() {
      return this.param != null;
    }

    public void setParamIsSet(boolean value) {
      if (!value) {
        this.param = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case PARAM:
        if (value == null) {
          unsetParam();
        } else {
          setParam((CouponListRequestParam)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case PARAM:
        return getParam();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case PARAM:
        return isSetParam();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getPersonalCouponList_args)
        return this.equals((getPersonalCouponList_args)that);
      return false;
    }

    public boolean equals(getPersonalCouponList_args that) {
      if (that == null)
        return false;

      boolean this_present_param = true && this.isSetParam();
      boolean that_present_param = true && that.isSetParam();
      if (this_present_param || that_present_param) {
        if (!(this_present_param && that_present_param))
          return false;
        if (!this.param.equals(that.param))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_param = true && (isSetParam());
      list.add(present_param);
      if (present_param)
        list.add(param);

      return list.hashCode();
    }

    @Override
    public int compareTo(getPersonalCouponList_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetParam()).compareTo(other.isSetParam());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetParam()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.param, other.param);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getPersonalCouponList_args(");
      boolean first = true;

      sb.append("param:");
      if (this.param == null) {
        sb.append("null");
      } else {
        sb.append(this.param);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (param == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'param' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (param != null) {
        param.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getPersonalCouponList_argsStandardSchemeFactory implements SchemeFactory {
      public getPersonalCouponList_argsStandardScheme getScheme() {
        return new getPersonalCouponList_argsStandardScheme();
      }
    }

    private static class getPersonalCouponList_argsStandardScheme extends StandardScheme<getPersonalCouponList_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getPersonalCouponList_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // PARAM
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.param = new CouponListRequestParam();
                struct.param.read(iprot);
                struct.setParamIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getPersonalCouponList_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.param != null) {
          oprot.writeFieldBegin(PARAM_FIELD_DESC);
          struct.param.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getPersonalCouponList_argsTupleSchemeFactory implements SchemeFactory {
      public getPersonalCouponList_argsTupleScheme getScheme() {
        return new getPersonalCouponList_argsTupleScheme();
      }
    }

    private static class getPersonalCouponList_argsTupleScheme extends TupleScheme<getPersonalCouponList_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getPersonalCouponList_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.param.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getPersonalCouponList_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.param = new CouponListRequestParam();
        struct.param.read(iprot);
        struct.setParamIsSet(true);
      }
    }

  }

  public static class getPersonalCouponList_result implements org.apache.thrift.TBase<getPersonalCouponList_result, getPersonalCouponList_result._Fields>, java.io.Serializable, Cloneable, Comparable<getPersonalCouponList_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getPersonalCouponList_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getPersonalCouponList_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getPersonalCouponList_resultTupleSchemeFactory());
    }

    public PersonalCouponListResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PersonalCouponListResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getPersonalCouponList_result.class, metaDataMap);
    }

    public getPersonalCouponList_result() {
    }

    public getPersonalCouponList_result(
      PersonalCouponListResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getPersonalCouponList_result(getPersonalCouponList_result other) {
      if (other.isSetSuccess()) {
        this.success = new PersonalCouponListResult(other.success);
      }
    }

    public getPersonalCouponList_result deepCopy() {
      return new getPersonalCouponList_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public PersonalCouponListResult getSuccess() {
      return this.success;
    }

    public getPersonalCouponList_result setSuccess(PersonalCouponListResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((PersonalCouponListResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getPersonalCouponList_result)
        return this.equals((getPersonalCouponList_result)that);
      return false;
    }

    public boolean equals(getPersonalCouponList_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getPersonalCouponList_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getPersonalCouponList_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getPersonalCouponList_resultStandardSchemeFactory implements SchemeFactory {
      public getPersonalCouponList_resultStandardScheme getScheme() {
        return new getPersonalCouponList_resultStandardScheme();
      }
    }

    private static class getPersonalCouponList_resultStandardScheme extends StandardScheme<getPersonalCouponList_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getPersonalCouponList_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new PersonalCouponListResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getPersonalCouponList_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getPersonalCouponList_resultTupleSchemeFactory implements SchemeFactory {
      public getPersonalCouponList_resultTupleScheme getScheme() {
        return new getPersonalCouponList_resultTupleScheme();
      }
    }

    private static class getPersonalCouponList_resultTupleScheme extends TupleScheme<getPersonalCouponList_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getPersonalCouponList_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getPersonalCouponList_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new PersonalCouponListResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class drawCoupon_args implements org.apache.thrift.TBase<drawCoupon_args, drawCoupon_args._Fields>, java.io.Serializable, Cloneable, Comparable<drawCoupon_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("drawCoupon_args");

    private static final org.apache.thrift.protocol.TField PARAM_FIELD_DESC = new org.apache.thrift.protocol.TField("param", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new drawCoupon_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new drawCoupon_argsTupleSchemeFactory());
    }

    public DrawCouponParam param; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      PARAM((short)1, "param");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // PARAM
            return PARAM;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.PARAM, new org.apache.thrift.meta_data.FieldMetaData("param", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, DrawCouponParam.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(drawCoupon_args.class, metaDataMap);
    }

    public drawCoupon_args() {
    }

    public drawCoupon_args(
      DrawCouponParam param)
    {
      this();
      this.param = param;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public drawCoupon_args(drawCoupon_args other) {
      if (other.isSetParam()) {
        this.param = new DrawCouponParam(other.param);
      }
    }

    public drawCoupon_args deepCopy() {
      return new drawCoupon_args(this);
    }

    @Override
    public void clear() {
      this.param = null;
    }

    public DrawCouponParam getParam() {
      return this.param;
    }

    public drawCoupon_args setParam(DrawCouponParam param) {
      this.param = param;
      return this;
    }

    public void unsetParam() {
      this.param = null;
    }

    /** Returns true if field param is set (has been assigned a value) and false otherwise */
    public boolean isSetParam() {
      return this.param != null;
    }

    public void setParamIsSet(boolean value) {
      if (!value) {
        this.param = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case PARAM:
        if (value == null) {
          unsetParam();
        } else {
          setParam((DrawCouponParam)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case PARAM:
        return getParam();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case PARAM:
        return isSetParam();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof drawCoupon_args)
        return this.equals((drawCoupon_args)that);
      return false;
    }

    public boolean equals(drawCoupon_args that) {
      if (that == null)
        return false;

      boolean this_present_param = true && this.isSetParam();
      boolean that_present_param = true && that.isSetParam();
      if (this_present_param || that_present_param) {
        if (!(this_present_param && that_present_param))
          return false;
        if (!this.param.equals(that.param))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_param = true && (isSetParam());
      list.add(present_param);
      if (present_param)
        list.add(param);

      return list.hashCode();
    }

    @Override
    public int compareTo(drawCoupon_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetParam()).compareTo(other.isSetParam());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetParam()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.param, other.param);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("drawCoupon_args(");
      boolean first = true;

      sb.append("param:");
      if (this.param == null) {
        sb.append("null");
      } else {
        sb.append(this.param);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (param == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'param' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (param != null) {
        param.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class drawCoupon_argsStandardSchemeFactory implements SchemeFactory {
      public drawCoupon_argsStandardScheme getScheme() {
        return new drawCoupon_argsStandardScheme();
      }
    }

    private static class drawCoupon_argsStandardScheme extends StandardScheme<drawCoupon_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, drawCoupon_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // PARAM
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.param = new DrawCouponParam();
                struct.param.read(iprot);
                struct.setParamIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, drawCoupon_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.param != null) {
          oprot.writeFieldBegin(PARAM_FIELD_DESC);
          struct.param.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class drawCoupon_argsTupleSchemeFactory implements SchemeFactory {
      public drawCoupon_argsTupleScheme getScheme() {
        return new drawCoupon_argsTupleScheme();
      }
    }

    private static class drawCoupon_argsTupleScheme extends TupleScheme<drawCoupon_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, drawCoupon_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.param.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, drawCoupon_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.param = new DrawCouponParam();
        struct.param.read(iprot);
        struct.setParamIsSet(true);
      }
    }

  }

  public static class drawCoupon_result implements org.apache.thrift.TBase<drawCoupon_result, drawCoupon_result._Fields>, java.io.Serializable, Cloneable, Comparable<drawCoupon_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("drawCoupon_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new drawCoupon_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new drawCoupon_resultTupleSchemeFactory());
    }

    public DrawCouponResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, DrawCouponResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(drawCoupon_result.class, metaDataMap);
    }

    public drawCoupon_result() {
    }

    public drawCoupon_result(
      DrawCouponResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public drawCoupon_result(drawCoupon_result other) {
      if (other.isSetSuccess()) {
        this.success = new DrawCouponResult(other.success);
      }
    }

    public drawCoupon_result deepCopy() {
      return new drawCoupon_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public DrawCouponResult getSuccess() {
      return this.success;
    }

    public drawCoupon_result setSuccess(DrawCouponResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((DrawCouponResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof drawCoupon_result)
        return this.equals((drawCoupon_result)that);
      return false;
    }

    public boolean equals(drawCoupon_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(drawCoupon_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("drawCoupon_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class drawCoupon_resultStandardSchemeFactory implements SchemeFactory {
      public drawCoupon_resultStandardScheme getScheme() {
        return new drawCoupon_resultStandardScheme();
      }
    }

    private static class drawCoupon_resultStandardScheme extends StandardScheme<drawCoupon_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, drawCoupon_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new DrawCouponResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, drawCoupon_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class drawCoupon_resultTupleSchemeFactory implements SchemeFactory {
      public drawCoupon_resultTupleScheme getScheme() {
        return new drawCoupon_resultTupleScheme();
      }
    }

    private static class drawCoupon_resultTupleScheme extends TupleScheme<drawCoupon_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, drawCoupon_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, drawCoupon_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new DrawCouponResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class getRebateCouponList_args implements org.apache.thrift.TBase<getRebateCouponList_args, getRebateCouponList_args._Fields>, java.io.Serializable, Cloneable, Comparable<getRebateCouponList_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getRebateCouponList_args");

    private static final org.apache.thrift.protocol.TField PARAMS_FIELD_DESC = new org.apache.thrift.protocol.TField("params", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getRebateCouponList_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getRebateCouponList_argsTupleSchemeFactory());
    }

    public GetRebateCouponsParams params; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      PARAMS((short)1, "params");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // PARAMS
            return PARAMS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.PARAMS, new org.apache.thrift.meta_data.FieldMetaData("params", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, GetRebateCouponsParams.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getRebateCouponList_args.class, metaDataMap);
    }

    public getRebateCouponList_args() {
    }

    public getRebateCouponList_args(
      GetRebateCouponsParams params)
    {
      this();
      this.params = params;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getRebateCouponList_args(getRebateCouponList_args other) {
      if (other.isSetParams()) {
        this.params = new GetRebateCouponsParams(other.params);
      }
    }

    public getRebateCouponList_args deepCopy() {
      return new getRebateCouponList_args(this);
    }

    @Override
    public void clear() {
      this.params = null;
    }

    public GetRebateCouponsParams getParams() {
      return this.params;
    }

    public getRebateCouponList_args setParams(GetRebateCouponsParams params) {
      this.params = params;
      return this;
    }

    public void unsetParams() {
      this.params = null;
    }

    /** Returns true if field params is set (has been assigned a value) and false otherwise */
    public boolean isSetParams() {
      return this.params != null;
    }

    public void setParamsIsSet(boolean value) {
      if (!value) {
        this.params = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case PARAMS:
        if (value == null) {
          unsetParams();
        } else {
          setParams((GetRebateCouponsParams)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case PARAMS:
        return getParams();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case PARAMS:
        return isSetParams();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getRebateCouponList_args)
        return this.equals((getRebateCouponList_args)that);
      return false;
    }

    public boolean equals(getRebateCouponList_args that) {
      if (that == null)
        return false;

      boolean this_present_params = true && this.isSetParams();
      boolean that_present_params = true && that.isSetParams();
      if (this_present_params || that_present_params) {
        if (!(this_present_params && that_present_params))
          return false;
        if (!this.params.equals(that.params))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_params = true && (isSetParams());
      list.add(present_params);
      if (present_params)
        list.add(params);

      return list.hashCode();
    }

    @Override
    public int compareTo(getRebateCouponList_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetParams()).compareTo(other.isSetParams());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetParams()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.params, other.params);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getRebateCouponList_args(");
      boolean first = true;

      sb.append("params:");
      if (this.params == null) {
        sb.append("null");
      } else {
        sb.append(this.params);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (params == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'params' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (params != null) {
        params.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getRebateCouponList_argsStandardSchemeFactory implements SchemeFactory {
      public getRebateCouponList_argsStandardScheme getScheme() {
        return new getRebateCouponList_argsStandardScheme();
      }
    }

    private static class getRebateCouponList_argsStandardScheme extends StandardScheme<getRebateCouponList_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getRebateCouponList_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // PARAMS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.params = new GetRebateCouponsParams();
                struct.params.read(iprot);
                struct.setParamsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getRebateCouponList_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.params != null) {
          oprot.writeFieldBegin(PARAMS_FIELD_DESC);
          struct.params.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getRebateCouponList_argsTupleSchemeFactory implements SchemeFactory {
      public getRebateCouponList_argsTupleScheme getScheme() {
        return new getRebateCouponList_argsTupleScheme();
      }
    }

    private static class getRebateCouponList_argsTupleScheme extends TupleScheme<getRebateCouponList_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getRebateCouponList_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.params.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getRebateCouponList_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.params = new GetRebateCouponsParams();
        struct.params.read(iprot);
        struct.setParamsIsSet(true);
      }
    }

  }

  public static class getRebateCouponList_result implements org.apache.thrift.TBase<getRebateCouponList_result, getRebateCouponList_result._Fields>, java.io.Serializable, Cloneable, Comparable<getRebateCouponList_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("getRebateCouponList_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new getRebateCouponList_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new getRebateCouponList_resultTupleSchemeFactory());
    }

    public GetRebateCouponsResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, GetRebateCouponsResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(getRebateCouponList_result.class, metaDataMap);
    }

    public getRebateCouponList_result() {
    }

    public getRebateCouponList_result(
      GetRebateCouponsResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public getRebateCouponList_result(getRebateCouponList_result other) {
      if (other.isSetSuccess()) {
        this.success = new GetRebateCouponsResult(other.success);
      }
    }

    public getRebateCouponList_result deepCopy() {
      return new getRebateCouponList_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public GetRebateCouponsResult getSuccess() {
      return this.success;
    }

    public getRebateCouponList_result setSuccess(GetRebateCouponsResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((GetRebateCouponsResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof getRebateCouponList_result)
        return this.equals((getRebateCouponList_result)that);
      return false;
    }

    public boolean equals(getRebateCouponList_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(getRebateCouponList_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("getRebateCouponList_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class getRebateCouponList_resultStandardSchemeFactory implements SchemeFactory {
      public getRebateCouponList_resultStandardScheme getScheme() {
        return new getRebateCouponList_resultStandardScheme();
      }
    }

    private static class getRebateCouponList_resultStandardScheme extends StandardScheme<getRebateCouponList_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, getRebateCouponList_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new GetRebateCouponsResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, getRebateCouponList_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class getRebateCouponList_resultTupleSchemeFactory implements SchemeFactory {
      public getRebateCouponList_resultTupleScheme getScheme() {
        return new getRebateCouponList_resultTupleScheme();
      }
    }

    private static class getRebateCouponList_resultTupleScheme extends TupleScheme<getRebateCouponList_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, getRebateCouponList_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, getRebateCouponList_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new GetRebateCouponsResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

  public static class queryActionIdConfig_args implements org.apache.thrift.TBase<queryActionIdConfig_args, queryActionIdConfig_args._Fields>, java.io.Serializable, Cloneable, Comparable<queryActionIdConfig_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("queryActionIdConfig_args");

    private static final org.apache.thrift.protocol.TField PARAMS_FIELD_DESC = new org.apache.thrift.protocol.TField("params", org.apache.thrift.protocol.TType.STRUCT, (short)1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new queryActionIdConfig_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new queryActionIdConfig_argsTupleSchemeFactory());
    }

    public ActionIdConfigRequest params; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      PARAMS((short)1, "params");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // PARAMS
            return PARAMS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.PARAMS, new org.apache.thrift.meta_data.FieldMetaData("params", org.apache.thrift.TFieldRequirementType.REQUIRED, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ActionIdConfigRequest.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(queryActionIdConfig_args.class, metaDataMap);
    }

    public queryActionIdConfig_args() {
    }

    public queryActionIdConfig_args(
      ActionIdConfigRequest params)
    {
      this();
      this.params = params;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public queryActionIdConfig_args(queryActionIdConfig_args other) {
      if (other.isSetParams()) {
        this.params = new ActionIdConfigRequest(other.params);
      }
    }

    public queryActionIdConfig_args deepCopy() {
      return new queryActionIdConfig_args(this);
    }

    @Override
    public void clear() {
      this.params = null;
    }

    public ActionIdConfigRequest getParams() {
      return this.params;
    }

    public queryActionIdConfig_args setParams(ActionIdConfigRequest params) {
      this.params = params;
      return this;
    }

    public void unsetParams() {
      this.params = null;
    }

    /** Returns true if field params is set (has been assigned a value) and false otherwise */
    public boolean isSetParams() {
      return this.params != null;
    }

    public void setParamsIsSet(boolean value) {
      if (!value) {
        this.params = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case PARAMS:
        if (value == null) {
          unsetParams();
        } else {
          setParams((ActionIdConfigRequest)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case PARAMS:
        return getParams();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case PARAMS:
        return isSetParams();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof queryActionIdConfig_args)
        return this.equals((queryActionIdConfig_args)that);
      return false;
    }

    public boolean equals(queryActionIdConfig_args that) {
      if (that == null)
        return false;

      boolean this_present_params = true && this.isSetParams();
      boolean that_present_params = true && that.isSetParams();
      if (this_present_params || that_present_params) {
        if (!(this_present_params && that_present_params))
          return false;
        if (!this.params.equals(that.params))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_params = true && (isSetParams());
      list.add(present_params);
      if (present_params)
        list.add(params);

      return list.hashCode();
    }

    @Override
    public int compareTo(queryActionIdConfig_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetParams()).compareTo(other.isSetParams());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetParams()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.params, other.params);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("queryActionIdConfig_args(");
      boolean first = true;

      sb.append("params:");
      if (this.params == null) {
        sb.append("null");
      } else {
        sb.append(this.params);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      if (params == null) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'params' was not present! Struct: " + toString());
      }
      // check for sub-struct validity
      if (params != null) {
        params.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class queryActionIdConfig_argsStandardSchemeFactory implements SchemeFactory {
      public queryActionIdConfig_argsStandardScheme getScheme() {
        return new queryActionIdConfig_argsStandardScheme();
      }
    }

    private static class queryActionIdConfig_argsStandardScheme extends StandardScheme<queryActionIdConfig_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, queryActionIdConfig_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // PARAMS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.params = new ActionIdConfigRequest();
                struct.params.read(iprot);
                struct.setParamsIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, queryActionIdConfig_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.params != null) {
          oprot.writeFieldBegin(PARAMS_FIELD_DESC);
          struct.params.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class queryActionIdConfig_argsTupleSchemeFactory implements SchemeFactory {
      public queryActionIdConfig_argsTupleScheme getScheme() {
        return new queryActionIdConfig_argsTupleScheme();
      }
    }

    private static class queryActionIdConfig_argsTupleScheme extends TupleScheme<queryActionIdConfig_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, queryActionIdConfig_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        struct.params.write(oprot);
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, queryActionIdConfig_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        struct.params = new ActionIdConfigRequest();
        struct.params.read(iprot);
        struct.setParamsIsSet(true);
      }
    }

  }

  public static class queryActionIdConfig_result implements org.apache.thrift.TBase<queryActionIdConfig_result, queryActionIdConfig_result._Fields>, java.io.Serializable, Cloneable, Comparable<queryActionIdConfig_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("queryActionIdConfig_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.STRUCT, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new queryActionIdConfig_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new queryActionIdConfig_resultTupleSchemeFactory());
    }

    public ActionIdConfigQueryResult success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ActionIdConfigQueryResult.class)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(queryActionIdConfig_result.class, metaDataMap);
    }

    public queryActionIdConfig_result() {
    }

    public queryActionIdConfig_result(
      ActionIdConfigQueryResult success)
    {
      this();
      this.success = success;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public queryActionIdConfig_result(queryActionIdConfig_result other) {
      if (other.isSetSuccess()) {
        this.success = new ActionIdConfigQueryResult(other.success);
      }
    }

    public queryActionIdConfig_result deepCopy() {
      return new queryActionIdConfig_result(this);
    }

    @Override
    public void clear() {
      this.success = null;
    }

    public ActionIdConfigQueryResult getSuccess() {
      return this.success;
    }

    public queryActionIdConfig_result setSuccess(ActionIdConfigQueryResult success) {
      this.success = success;
      return this;
    }

    public void unsetSuccess() {
      this.success = null;
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return this.success != null;
    }

    public void setSuccessIsSet(boolean value) {
      if (!value) {
        this.success = null;
      }
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((ActionIdConfigQueryResult)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof queryActionIdConfig_result)
        return this.equals((queryActionIdConfig_result)that);
      return false;
    }

    public boolean equals(queryActionIdConfig_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true && this.isSetSuccess();
      boolean that_present_success = true && that.isSetSuccess();
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (!this.success.equals(that.success))
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true && (isSetSuccess());
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(queryActionIdConfig_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("queryActionIdConfig_result(");
      boolean first = true;

      sb.append("success:");
      if (this.success == null) {
        sb.append("null");
      } else {
        sb.append(this.success);
      }
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
      if (success != null) {
        success.validate();
      }
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class queryActionIdConfig_resultStandardSchemeFactory implements SchemeFactory {
      public queryActionIdConfig_resultStandardScheme getScheme() {
        return new queryActionIdConfig_resultStandardScheme();
      }
    }

    private static class queryActionIdConfig_resultStandardScheme extends StandardScheme<queryActionIdConfig_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, queryActionIdConfig_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
                struct.success = new ActionIdConfigQueryResult();
                struct.success.read(iprot);
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, queryActionIdConfig_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.success != null) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          struct.success.write(oprot);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class queryActionIdConfig_resultTupleSchemeFactory implements SchemeFactory {
      public queryActionIdConfig_resultTupleScheme getScheme() {
        return new queryActionIdConfig_resultTupleScheme();
      }
    }

    private static class queryActionIdConfig_resultTupleScheme extends TupleScheme<queryActionIdConfig_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, queryActionIdConfig_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          struct.success.write(oprot);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, queryActionIdConfig_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = new ActionIdConfigQueryResult();
          struct.success.read(iprot);
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

}
