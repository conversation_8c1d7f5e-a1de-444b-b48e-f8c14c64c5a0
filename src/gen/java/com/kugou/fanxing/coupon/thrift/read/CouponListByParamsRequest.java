/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.kugou.fanxing.coupon.thrift.read;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2020-03-10")
public class CouponListByParamsRequest implements org.apache.thrift.TBase<CouponListByParamsRequest, CouponListByParamsRequest._Fields>, java.io.Serializable, Cloneable, Comparable<CouponListByParamsRequest> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CouponListByParamsRequest");

  private static final org.apache.thrift.protocol.TField KUGOU_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("kugouId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField COUPON_CATEGORY_FIELD_DESC = new org.apache.thrift.protocol.TField("couponCategory", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField ACTION_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actionId", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField TIMESTAMP_FIELD_DESC = new org.apache.thrift.protocol.TField("timestamp", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField JSON_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("jsonData", org.apache.thrift.protocol.TType.STRING, (short)5);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new CouponListByParamsRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new CouponListByParamsRequestTupleSchemeFactory());
  }

  public long kugouId; // required
  public int couponCategory; // required
  public int actionId; // required
  public int timestamp; // required
  public String jsonData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    KUGOU_ID((short)1, "kugouId"),
    COUPON_CATEGORY((short)2, "couponCategory"),
    ACTION_ID((short)3, "actionId"),
    TIMESTAMP((short)4, "timestamp"),
    JSON_DATA((short)5, "jsonData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // KUGOU_ID
          return KUGOU_ID;
        case 2: // COUPON_CATEGORY
          return COUPON_CATEGORY;
        case 3: // ACTION_ID
          return ACTION_ID;
        case 4: // TIMESTAMP
          return TIMESTAMP;
        case 5: // JSON_DATA
          return JSON_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __KUGOUID_ISSET_ID = 0;
  private static final int __COUPONCATEGORY_ISSET_ID = 1;
  private static final int __ACTIONID_ISSET_ID = 2;
  private static final int __TIMESTAMP_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.KUGOU_ID, new org.apache.thrift.meta_data.FieldMetaData("kugouId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.COUPON_CATEGORY, new org.apache.thrift.meta_data.FieldMetaData("couponCategory", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ACTION_ID, new org.apache.thrift.meta_data.FieldMetaData("actionId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TIMESTAMP, new org.apache.thrift.meta_data.FieldMetaData("timestamp", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.JSON_DATA, new org.apache.thrift.meta_data.FieldMetaData("jsonData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CouponListByParamsRequest.class, metaDataMap);
  }

  public CouponListByParamsRequest() {
  }

  public CouponListByParamsRequest(
    long kugouId,
    int couponCategory,
    int actionId,
    int timestamp,
    String jsonData)
  {
    this();
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    this.couponCategory = couponCategory;
    setCouponCategoryIsSet(true);
    this.actionId = actionId;
    setActionIdIsSet(true);
    this.timestamp = timestamp;
    setTimestampIsSet(true);
    this.jsonData = jsonData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CouponListByParamsRequest(CouponListByParamsRequest other) {
    __isset_bitfield = other.__isset_bitfield;
    this.kugouId = other.kugouId;
    this.couponCategory = other.couponCategory;
    this.actionId = other.actionId;
    this.timestamp = other.timestamp;
    if (other.isSetJsonData()) {
      this.jsonData = other.jsonData;
    }
  }

  public CouponListByParamsRequest deepCopy() {
    return new CouponListByParamsRequest(this);
  }

  @Override
  public void clear() {
    setKugouIdIsSet(false);
    this.kugouId = 0;
    setCouponCategoryIsSet(false);
    this.couponCategory = 0;
    setActionIdIsSet(false);
    this.actionId = 0;
    setTimestampIsSet(false);
    this.timestamp = 0;
    this.jsonData = null;
  }

  public long getKugouId() {
    return this.kugouId;
  }

  public CouponListByParamsRequest setKugouId(long kugouId) {
    this.kugouId = kugouId;
    setKugouIdIsSet(true);
    return this;
  }

  public void unsetKugouId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  /** Returns true if field kugouId is set (has been assigned a value) and false otherwise */
  public boolean isSetKugouId() {
    return EncodingUtils.testBit(__isset_bitfield, __KUGOUID_ISSET_ID);
  }

  public void setKugouIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __KUGOUID_ISSET_ID, value);
  }

  public int getCouponCategory() {
    return this.couponCategory;
  }

  public CouponListByParamsRequest setCouponCategory(int couponCategory) {
    this.couponCategory = couponCategory;
    setCouponCategoryIsSet(true);
    return this;
  }

  public void unsetCouponCategory() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COUPONCATEGORY_ISSET_ID);
  }

  /** Returns true if field couponCategory is set (has been assigned a value) and false otherwise */
  public boolean isSetCouponCategory() {
    return EncodingUtils.testBit(__isset_bitfield, __COUPONCATEGORY_ISSET_ID);
  }

  public void setCouponCategoryIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COUPONCATEGORY_ISSET_ID, value);
  }

  public int getActionId() {
    return this.actionId;
  }

  public CouponListByParamsRequest setActionId(int actionId) {
    this.actionId = actionId;
    setActionIdIsSet(true);
    return this;
  }

  public void unsetActionId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTIONID_ISSET_ID);
  }

  /** Returns true if field actionId is set (has been assigned a value) and false otherwise */
  public boolean isSetActionId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTIONID_ISSET_ID);
  }

  public void setActionIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTIONID_ISSET_ID, value);
  }

  public int getTimestamp() {
    return this.timestamp;
  }

  public CouponListByParamsRequest setTimestamp(int timestamp) {
    this.timestamp = timestamp;
    setTimestampIsSet(true);
    return this;
  }

  public void unsetTimestamp() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
  }

  /** Returns true if field timestamp is set (has been assigned a value) and false otherwise */
  public boolean isSetTimestamp() {
    return EncodingUtils.testBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
  }

  public void setTimestampIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TIMESTAMP_ISSET_ID, value);
  }

  public String getJsonData() {
    return this.jsonData;
  }

  public CouponListByParamsRequest setJsonData(String jsonData) {
    this.jsonData = jsonData;
    return this;
  }

  public void unsetJsonData() {
    this.jsonData = null;
  }

  /** Returns true if field jsonData is set (has been assigned a value) and false otherwise */
  public boolean isSetJsonData() {
    return this.jsonData != null;
  }

  public void setJsonDataIsSet(boolean value) {
    if (!value) {
      this.jsonData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case KUGOU_ID:
      if (value == null) {
        unsetKugouId();
      } else {
        setKugouId((Long)value);
      }
      break;

    case COUPON_CATEGORY:
      if (value == null) {
        unsetCouponCategory();
      } else {
        setCouponCategory((Integer)value);
      }
      break;

    case ACTION_ID:
      if (value == null) {
        unsetActionId();
      } else {
        setActionId((Integer)value);
      }
      break;

    case TIMESTAMP:
      if (value == null) {
        unsetTimestamp();
      } else {
        setTimestamp((Integer)value);
      }
      break;

    case JSON_DATA:
      if (value == null) {
        unsetJsonData();
      } else {
        setJsonData((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case KUGOU_ID:
      return getKugouId();

    case COUPON_CATEGORY:
      return getCouponCategory();

    case ACTION_ID:
      return getActionId();

    case TIMESTAMP:
      return getTimestamp();

    case JSON_DATA:
      return getJsonData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case KUGOU_ID:
      return isSetKugouId();
    case COUPON_CATEGORY:
      return isSetCouponCategory();
    case ACTION_ID:
      return isSetActionId();
    case TIMESTAMP:
      return isSetTimestamp();
    case JSON_DATA:
      return isSetJsonData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof CouponListByParamsRequest)
      return this.equals((CouponListByParamsRequest)that);
    return false;
  }

  public boolean equals(CouponListByParamsRequest that) {
    if (that == null)
      return false;

    boolean this_present_kugouId = true;
    boolean that_present_kugouId = true;
    if (this_present_kugouId || that_present_kugouId) {
      if (!(this_present_kugouId && that_present_kugouId))
        return false;
      if (this.kugouId != that.kugouId)
        return false;
    }

    boolean this_present_couponCategory = true;
    boolean that_present_couponCategory = true;
    if (this_present_couponCategory || that_present_couponCategory) {
      if (!(this_present_couponCategory && that_present_couponCategory))
        return false;
      if (this.couponCategory != that.couponCategory)
        return false;
    }

    boolean this_present_actionId = true;
    boolean that_present_actionId = true;
    if (this_present_actionId || that_present_actionId) {
      if (!(this_present_actionId && that_present_actionId))
        return false;
      if (this.actionId != that.actionId)
        return false;
    }

    boolean this_present_timestamp = true;
    boolean that_present_timestamp = true;
    if (this_present_timestamp || that_present_timestamp) {
      if (!(this_present_timestamp && that_present_timestamp))
        return false;
      if (this.timestamp != that.timestamp)
        return false;
    }

    boolean this_present_jsonData = true && this.isSetJsonData();
    boolean that_present_jsonData = true && that.isSetJsonData();
    if (this_present_jsonData || that_present_jsonData) {
      if (!(this_present_jsonData && that_present_jsonData))
        return false;
      if (!this.jsonData.equals(that.jsonData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_kugouId = true;
    list.add(present_kugouId);
    if (present_kugouId)
      list.add(kugouId);

    boolean present_couponCategory = true;
    list.add(present_couponCategory);
    if (present_couponCategory)
      list.add(couponCategory);

    boolean present_actionId = true;
    list.add(present_actionId);
    if (present_actionId)
      list.add(actionId);

    boolean present_timestamp = true;
    list.add(present_timestamp);
    if (present_timestamp)
      list.add(timestamp);

    boolean present_jsonData = true && (isSetJsonData());
    list.add(present_jsonData);
    if (present_jsonData)
      list.add(jsonData);

    return list.hashCode();
  }

  @Override
  public int compareTo(CouponListByParamsRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetKugouId()).compareTo(other.isSetKugouId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKugouId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.kugouId, other.kugouId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCouponCategory()).compareTo(other.isSetCouponCategory());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCouponCategory()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.couponCategory, other.couponCategory);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetActionId()).compareTo(other.isSetActionId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActionId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actionId, other.actionId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTimestamp()).compareTo(other.isSetTimestamp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTimestamp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.timestamp, other.timestamp);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetJsonData()).compareTo(other.isSetJsonData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetJsonData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.jsonData, other.jsonData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("CouponListByParamsRequest(");
    boolean first = true;

    sb.append("kugouId:");
    sb.append(this.kugouId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("couponCategory:");
    sb.append(this.couponCategory);
    first = false;
    if (!first) sb.append(", ");
    sb.append("actionId:");
    sb.append(this.actionId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("timestamp:");
    sb.append(this.timestamp);
    first = false;
    if (!first) sb.append(", ");
    sb.append("jsonData:");
    if (this.jsonData == null) {
      sb.append("null");
    } else {
      sb.append(this.jsonData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'kugouId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'couponCategory' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'actionId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'timestamp' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CouponListByParamsRequestStandardSchemeFactory implements SchemeFactory {
    public CouponListByParamsRequestStandardScheme getScheme() {
      return new CouponListByParamsRequestStandardScheme();
    }
  }

  private static class CouponListByParamsRequestStandardScheme extends StandardScheme<CouponListByParamsRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CouponListByParamsRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // KUGOU_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.kugouId = iprot.readI64();
              struct.setKugouIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // COUPON_CATEGORY
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.couponCategory = iprot.readI32();
              struct.setCouponCategoryIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ACTION_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.actionId = iprot.readI32();
              struct.setActionIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // TIMESTAMP
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.timestamp = iprot.readI32();
              struct.setTimestampIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // JSON_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.jsonData = iprot.readString();
              struct.setJsonDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetKugouId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'kugouId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetCouponCategory()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'couponCategory' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetActionId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'actionId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetTimestamp()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'timestamp' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CouponListByParamsRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(KUGOU_ID_FIELD_DESC);
      oprot.writeI64(struct.kugouId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(COUPON_CATEGORY_FIELD_DESC);
      oprot.writeI32(struct.couponCategory);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ACTION_ID_FIELD_DESC);
      oprot.writeI32(struct.actionId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TIMESTAMP_FIELD_DESC);
      oprot.writeI32(struct.timestamp);
      oprot.writeFieldEnd();
      if (struct.jsonData != null) {
        oprot.writeFieldBegin(JSON_DATA_FIELD_DESC);
        oprot.writeString(struct.jsonData);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CouponListByParamsRequestTupleSchemeFactory implements SchemeFactory {
    public CouponListByParamsRequestTupleScheme getScheme() {
      return new CouponListByParamsRequestTupleScheme();
    }
  }

  private static class CouponListByParamsRequestTupleScheme extends TupleScheme<CouponListByParamsRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CouponListByParamsRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      oprot.writeI64(struct.kugouId);
      oprot.writeI32(struct.couponCategory);
      oprot.writeI32(struct.actionId);
      oprot.writeI32(struct.timestamp);
      BitSet optionals = new BitSet();
      if (struct.isSetJsonData()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetJsonData()) {
        oprot.writeString(struct.jsonData);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CouponListByParamsRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      struct.kugouId = iprot.readI64();
      struct.setKugouIdIsSet(true);
      struct.couponCategory = iprot.readI32();
      struct.setCouponCategoryIsSet(true);
      struct.actionId = iprot.readI32();
      struct.setActionIdIsSet(true);
      struct.timestamp = iprot.readI32();
      struct.setTimestampIsSet(true);
      BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        struct.jsonData = iprot.readString();
        struct.setJsonDataIsSet(true);
      }
    }
  }

}

