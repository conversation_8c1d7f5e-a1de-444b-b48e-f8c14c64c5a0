package com.kugou.fanxing.recharge.config;

import com.kugou.fanxing.recharge.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class RefundConfigTest {
    @Autowired
    private RefundConfig refundConfig;
    @Test
    public void getBlackTransactionId() {
        List<String> blockList = refundConfig.getBlackTransactionId();
        Assert.assertEquals(2, blockList.size());
    }
}
