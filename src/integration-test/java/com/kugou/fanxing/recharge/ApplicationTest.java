package com.kugou.fanxing.recharge;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.kugou.api.springcloud.GlobalIdServiceThrift.GlobalIdService;
import com.kugou.fanxing.ip.api.FXIpService;
import com.kugou.fanxing.ip.api.SimpleIpInfo;
import com.kugou.fanxing.recharge.alert.AlerterFacade;
import com.kugou.fanxing.recharge.model.po.RechargeAcrossPO;
import com.kugou.fanxing.recharge.model.request.IapOrderVerifyRequest;
import com.kugou.fanxing.recharge.model.vo.GatewayPayCallbackParam;
import com.kugou.fanxing.recharge.model.vo.GatewayPayRecordVo;
import com.kugou.fanxing.recharge.service.ApolloConfigService;
import com.kugou.fanxing.recharge.service.GatewayPaymentBasicService;
import com.kugou.fanxing.recharge.service.RechargeCommonService;
import com.kugou.fanxing.recharge.service.ValidatingService;
import com.kugou.fanxing.recharge.service.after.AfterRechargeService;
import com.kugou.fanxing.recharge.service.command.GetSimpleIPInfoByIpCommand;
import com.kugou.fanxing.recharge.service.common.DencryptService;
import com.kugou.fanxing.recharge.service.common.IpLocationService;
import com.kugou.fanxing.recharge.service.common.UserFacadeService;
import com.kugou.fanxing.recharge.service.refund.v2.RefundFacadeService;
import com.kugou.fanxing.userbaseinfo.service.UserModuleV2BizService;
import com.kugou.fanxing.userbaseinfo.vo.UserResponse;
import com.kugou.mfx.activity.infiltrate.thrift.service.Coupon;
import com.kugou.mfx.activity.infiltrate.thrift.service.CouponListResult;
import com.kugou.mfx.activity.infiltrate.thrift.service.CouponListService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.validation.ConstraintViolation;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class ApplicationTest {

    @Autowired
    private GlobalIdService.Iface globalIdService;
    @Autowired
    private UserModuleV2BizService.Iface userModuleV2BizService;
    @Autowired
    private DencryptService dencryptService;
    @Autowired
    private FXIpService.Iface fxIpService;
    @Autowired
    private GatewayPaymentBasicService gatewayPaymentBasicService;
    @Autowired
    private CouponListService.Iface couponListService;
    @Autowired
    private AlerterFacade alerterFacade;
    @Autowired
    private UserFacadeService userFacadeService;
    @Autowired
    private IpLocationService ipLocationService;
    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private AfterRechargeService afterRechargeService;
    @Autowired
    private RefundFacadeService refundFacadeService;
    @Autowired
    private RechargeCommonService rechargeCommonService;

    @Test
    public void testGlobalIdService() throws TException {
        long globalId = globalIdService.get();
        Assert.assertTrue(globalId > 0);
    }

    @Test
    @Ignore
    public void testUserIdMappingService() {
        long kugouId = 1290249156L;
        long userId = userFacadeService.getUserIdByKugouId(kugouId).orElse(0L);
        Assert.assertEquals(kugouId, userId);
    }

    @Test
    public void testUserModuleV2BizService() throws TException {
        long kugouId = 1290249156L;
        UserResponse userResponse = userModuleV2BizService.getUserByKugouId(kugouId);
        Assert.assertEquals("Kay", userResponse.getData().getNickName());
    }

    @Test
    @Ignore
    public void testDencryptService() {
        String account = dencryptService.decrypt("ss1nMQZGWbg_lTH_6OHG9w@DQA+AIQCAAA=");
        Assert.assertEquals("***********", account);
    }

    @Test
    @Ignore
    public void testFxIpService() {
        GetSimpleIPInfoByIpCommand command = new GetSimpleIPInfoByIpCommand(fxIpService, "*************");
        Optional<SimpleIpInfo> optionalSimpleIpInfo = command.execute();
        SimpleIpInfo simpleIpInfo = optionalSimpleIpInfo.orElseThrow(() -> new ContextedRuntimeException("testFxIpService"));
        log.warn("simpleIpInfo: {}", simpleIpInfo);
        Assert.assertEquals("中国", simpleIpInfo.getCountry());
        Assert.assertEquals("海南", simpleIpInfo.getProvice());
        Assert.assertEquals("海口", simpleIpInfo.getCity());
        // 中国海南
        Assert.assertFalse(this.ipLocationService.isOverseasIp("*************"));
        // 中国台湾
        Assert.assertTrue(this.ipLocationService.isOverseasIp("*************"));
        // 韩国首尔
        Assert.assertTrue(this.ipLocationService.isOverseasIp("**************"));
    }

    @Test
    public void testGatewayPaymentBasicService() {
        String[] rechargeOrderNumArr = StringUtils.split("R292019052919434507594771", ",");
        Arrays.stream(rechargeOrderNumArr).forEach(rechargeOrderNum -> {
            GatewayPayCallbackParam callbackParam = this.gatewayPaymentBasicService.getOrderByNo(StringUtils.trim(rechargeOrderNum));
            log.warn("callback: {}", callbackParam);
        });
        Date current = new Date();
        long sTime = DateUtils.addMinutes(current, -2).getTime() / 1000;
        long eTime = DateUtils.addMinutes(current, -1).getTime() / 1000;
        log.warn("sTime: {}, eTime: {}", sTime, eTime);
        List<GatewayPayRecordVo> gatewayPayRecordVoList = this.gatewayPaymentBasicService.getSuccessOrders(sTime, eTime);
        log.warn("gatewayPayRecordVoList: {}", gatewayPayRecordVoList);
    }

    @Test
    @Ignore
    public void testListCoupon() throws TException {
        CouponListResult couponListResult = couponListService.listCoupon();
        Assert.assertEquals(0, couponListResult.getCode());
        List<Coupon> couponList = couponListResult.getData();
        log.warn("couponListSize: {}", couponList.size());
    }

    @Test
    @Ignore("避免消息骚扰")
    public void alerterFacade() {
        alerterFacade.sendSMS("【酷狗直播】", "功能测试", 1290249156L);
        alerterFacade.sendRTX("aa", "bb", "shoukailiu");
        alerterFacade.sendMSG("aa", "bb", 1290249156L);
    }

    @Test
    @Ignore("测试环境不稳定")
    public void testUserFacadeService() {
        Assert.assertTrue(this.userFacadeService.isValidKugouId(1290249156L));
        Assert.assertFalse(this.userFacadeService.isValidKugouId(0L));
    }

    @Test
    public void isWithdrawErrorCode() {
        List<Integer> errorCodeList = Lists.newArrayList(20006,20010,30966,30968,30972);
        errorCodeList.parallelStream().forEach(errorCode -> Assert.assertTrue(apolloConfigService.isWithdrawErrorCode(errorCode)));
        errorCodeList = Lists.newArrayList(1, 2, 3);
        errorCodeList.parallelStream().forEach(errorCode -> Assert.assertFalse(apolloConfigService.isWithdrawErrorCode(errorCode)));
    }

    /**
     * 模拟触发下游返点
     */
    @Test
    @Ignore
    public void afterRechargeSuccess() {
        String json = " {\"addTime\":\"1640764455\",\"agentKugouId\":1290249156,\"amount\":\"300\",\"businessId\":\"\",\"cFrom\":7,\"channelId\":1,\"coin\":\"30000\",\"coinAfter\":0,\"coinBefore\":0,\"consumeOrderNum\":\"01202112291554160100014621\",\"coupon\":\"0\",\"couponId\":0,\"couponOrderId\":0,\"extend\":\"eyJjYWxsQmFja1NpZ24iOiJmM2ZkYjYwMTU3ZjZkZjRhNzI3MTNhODg4YzA2MDViMSIsImNhbGxCYWNrQXJnIjp7ImFtb3VudCI6IjAuMDIiLCJhZGRUaW1lIjoiMTY0MDc2NDQ1NSIsImNvdXBvbiI6IjAiLCJyZWJhdGUiOjAsImJ1eVJpY2hMZXZlbCI6W10sImNvbnN1bWVBcmdzIjpbXSwiYnVzaW5lc3NFeHQiOiJ7fSIsImNvdXBvbklkIjowLCJ2ZXJzaW9uIjoiMjAxNzAxMTEiLCJhZ2VudEt1Z291SWQiOjEyOTAyNDkxNTYsImNvdXBvbk9yZGVySWQiOjAsInBheVR5cGVJZCI6MzksIm1vbmV5IjoiMC4wMiIsInJlZmVyIjowLCJjRnJvbSI6NywiY2hhbm5lbElkIjoxLCJrdWdvdUlkIjo5OTE2MTI4MDJ9fQ==\",\"extraJsonData\":\"{\\\"rechargeRebateGray\\\":1}\",\"fromKugouId\":991612802,\"isSandbox\":\"0\",\"kugouId\":991612802,\"money\":\"300\",\"partner\":\"10026148\",\"payTypeId\":39,\"reType\":0,\"realAmount\":\"2\",\"rechargeOrderNum\":\"R092021122915541526211997\",\"rechargeTime\":1640764472,\"refer\":0,\"status\":1,\"tmallFirstRecharge\":false,\"tradeNo\":\"4200001322202112290816517474\",\"tradeTime\":1640764470}";
        RechargeAcrossPO rechargeAcrossPO = JSON.parseObject(json, RechargeAcrossPO.class);
        this.afterRechargeService.afterRechargeSuccess(rechargeAcrossPO);
    }

    @Test
    @Ignore
    public void associatedRefundOrders() {
        this.refundFacadeService.associatedRefundOrders(100);
    }

}
