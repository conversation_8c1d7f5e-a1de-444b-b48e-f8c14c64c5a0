package com.kugou.fanxing.recharge.controller.thrift;

import com.kugou.fanxing.recharge.config.HostConstant;
import com.kugou.fanxing.recharge.thrift.RechargeThriftService;
import com.kugou.fanxing.recharge.thrift.ResultSuccessList;
import org.apache.thrift.TException;
import org.apache.thrift.protocol.TJSONProtocol;
import org.apache.thrift.protocol.TProtocol;
import org.apache.thrift.transport.THttpClient;
import org.apache.thrift.transport.TTransportException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;

import static com.kugou.fanxing.recharge.constant.SysResultCode.RECHARGE_PARAM_ERROR;

@Ignore
public class RechargeThriftServiceImplTest {
//    public static final String PLATFORM_RECHARGE_SERVICE_ADDR = "http://127.0.0.1:18888";
    public static final String INTERFACE_getRechargeSuccessList = "getRechargeSuccessList";

    static THttpClient transport = null;
    static TProtocol protocol;
    static RechargeThriftService.Client client;
    static String URL = "/platform_recharge_service/thrift/rechargeThriftService";
    static Long defaultEndTime = System.currentTimeMillis()/1000;

    @Before
    public void init() throws TTransportException, IOException {
        transport = new THttpClient(String.format(HostConstant.url + URL));
        transport.setCustomHeader("protocol", "json");
        protocol = new TJSONProtocol(transport);
        client = new RechargeThriftService.Client(protocol);
    }


    @Test
    @Ignore
    /**
     * 查询失败-beginTime<0
     */
    public void getRechargeSuccessList_beginTime_lt0() throws IOException, TException {
        ResultSuccessList resultSuccessList = client.getRechargeSuccessList(-1, 1592104770, 0, 1000);
        System.out.println(resultSuccessList);
    }

    @Test
    @Ignore
    /**
     * 查询失败-beginTime=0
     */
    public void getRechargeSuccessList_beginTime_eq0() throws IOException, TException {
        ResultSuccessList resultSuccessList = client.getRechargeSuccessList(0, 1592104770, 0, 1000);
        System.out.println(resultSuccessList);
    }

    @Test
    @Ignore
    /**
     * 查询失败-endTime<0
     */
    public void getRechargeSuccessList_endTime_lt0() throws IOException, TException {
        ResultSuccessList resultSuccessList = client.getRechargeSuccessList(-1, 1592104770, 0, 1000);
        System.out.println(resultSuccessList);
    }

    @Test
    @Ignore
    /**
     * 查询失败-endTime=0
     */
    public void getRechargeSuccessList_endTime_eq0() throws IOException, TException {
        ResultSuccessList resultSuccessList = client.getRechargeSuccessList(0, 1592104770, 0, 1000);
        System.out.println(resultSuccessList);
    }

    @Test
    /**
     * 查询失败-开始时间小于结束时间
     */
    public void getRechargeSuccessList_startTimeLTendTime() throws IOException, TException {
        ResultSuccessList resultSuccessList = client.getRechargeSuccessList(1592104770, 1592104759, 0, 1000);
        System.out.println(resultSuccessList);
        Assert.assertTrue(resultSuccessList.getRet() == RECHARGE_PARAM_ERROR.getCode());
        Assert.assertTrue(resultSuccessList.getMsg().equals(RECHARGE_PARAM_ERROR.getMsg()));
    }

    @Test
    /**
     * 查询失败-batchSize < 0
     */
    public void getRechargeSuccessList_batchSzieLT0() throws IOException, TException {
        ResultSuccessList resultSuccessList = client.getRechargeSuccessList(1592104759, 1592104770, 0, -1);
        System.out.println(resultSuccessList);
        Assert.assertTrue(resultSuccessList.getRet() == 0);
        Assert.assertTrue(resultSuccessList.getLastRechargeId() == 0);
    }

    @Test
    /**
     * 查询失败-batchSize = 0
     */
    public void getRechargeSuccessList_batchSzieEQ0() throws IOException, TException {
        ResultSuccessList resultSuccessList = client.getRechargeSuccessList(1592104759, 1592104770, 0, 0);
        System.out.println(resultSuccessList);
        Assert.assertTrue(resultSuccessList.getRet() == 0);
        Assert.assertTrue(resultSuccessList.getLastRechargeId() == 0);
    }

    @Test
    /**
     * 查询成功-无数据
     */
    public void getRechargeSuccessList_success_noData() throws IOException, TException {
        ResultSuccessList resultSuccessList = client.getRechargeSuccessList(defaultEndTime, defaultEndTime, 0, 1000);
        System.out.println(resultSuccessList);
        Assert.assertTrue(resultSuccessList.getRet() == 0);
    }

    @Test
    /**
     * 查询成功-数据不为空
     */
    public void getRechargeSuccessList_success() throws IOException, TException {
        System.out.println(defaultEndTime);
        ResultSuccessList resultSuccessList = client.getRechargeSuccessList(defaultEndTime - 2592000, defaultEndTime, 0, 1000);
        System.out.println(defaultEndTime);
        System.out.println(resultSuccessList);
        Assert.assertTrue(resultSuccessList.getRet() == 0);
        Assert.assertTrue(resultSuccessList.getData().size() >= 0);
    }

    @Test
    /**
     * 查询成功-数据不为空
     * batchSize小于总数据条数
     */
    public void getRechargeSuccessList_success_batchSize1() throws IOException, TException {
        System.out.println(defaultEndTime);
        ResultSuccessList resultSuccessList = client.getRechargeSuccessList(defaultEndTime - 2592000, defaultEndTime, 0, 1000);
        System.out.println(defaultEndTime);
        System.out.println(resultSuccessList);
        Assert.assertTrue(resultSuccessList.getRet() == 0);
        Assert.assertTrue(resultSuccessList.getData().size() >= 0);
    }

    @Test
    /**
     * 查询成功-数据不为空
     * batchSize大于总数据条数
     */
    public void getRechargeSuccessList_success_batchSize2() throws IOException, TException {
        System.out.println(defaultEndTime);
        ResultSuccessList resultSuccessList = client.getRechargeSuccessList(defaultEndTime - 2592000, defaultEndTime, 0, 1000);
        System.out.println(defaultEndTime);
        System.out.println(resultSuccessList);
        Assert.assertTrue(resultSuccessList.getRet() == 0);
        Assert.assertTrue(resultSuccessList.getData().size() >= 0);
    }

}