package com.kugou.fanxing.recharge.controller;

import com.kugou.fanxing.recharge.Application;
import com.kugou.fanxing.recharge.model.request.GetPayH5Request;
import com.kugou.fanxing.recharge.model.request.WebCommonParam;
import com.kugou.fanxing.recharge.model.request.WxgzhRequest;
import com.kugou.fanxing.recharge.util.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Ignore("袁采方2022.10.10：对应方法已经删除，没地方用")
public class ApiV1WebWechatQRTest {

    @Autowired
    private WechatRechargeController wechatRechargeController;

    public ApiV1WebWechatQRTest() throws IOException {
    }

    /**
     * 网页微信付款码支付转JSAPI支付充值下单接口-正常下单
     * 袁采方2022.10.10：对应方法已经删除，没地方用
     */
    @Test
    public void webWechatPayQR2JSAPI() throws Exception {
//        WebCommonParam webCommonParam = new WebCommonParam();
//        WxgzhRequest wxgzhRequest = new WxgzhRequest();
//        wxgzhRequest.setAmount(new BigDecimal(1));
//        GetPayH5Request getPayH5Request = new GetPayH5Request();
//        JsonResult<Map<String, Object>> jsonResult =  wechatRechargeController.webWechatPayQR2JSAPI(webCommonParam,wxgzhRequest);
//        System.out.println(jsonResult);
//        Assert.assertTrue(jsonResult.getCode() == 0);
    }

}