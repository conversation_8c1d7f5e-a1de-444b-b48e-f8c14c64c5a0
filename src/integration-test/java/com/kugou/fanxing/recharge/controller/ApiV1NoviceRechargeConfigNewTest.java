package com.kugou.fanxing.recharge.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.tester.common.tools.HttpClientUtil;
import com.kugou.fanxing.tester.common.tools.UserUtil;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
@Ignore
public class ApiV1NoviceRechargeConfigNewTest {
    HttpClientUtil httpClientUtil = new HttpClientUtil();


    private String url = "http://fxtest.fxwork.kugou.com/recharge/api/v1/noviceRechargeConfigNew";

    public ApiV1NoviceRechargeConfigNewTest() throws IOException {
    }

    private Map<String,String> buildHeader() throws Exception {
        Map<String, String> map = new HashMap<>();
        map.put("Content-Type", "application/x-www-form-urlencoded");
        map.put("Cookie", "KuGoo="+UserUtil.getCookieByDb("415770726"));
        return map;
    }

    private Map<String,String> buildParams() {
        Map<String,String> map = new HashMap<>();
        return map;
    }

    @Test
    public void getConfigNew() throws Exception {
            String result = httpClientUtil.postFromUrlEncodedHeaders(url,buildParams(),buildHeader());
            JSONObject jsonObject = JSON.parseObject(result);
            Assert.assertTrue("返回失败, status不为1",jsonObject.get("status").equals(1));
    }
}
