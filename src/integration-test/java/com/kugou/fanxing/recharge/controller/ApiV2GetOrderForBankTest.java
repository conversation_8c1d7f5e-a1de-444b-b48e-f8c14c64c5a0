package com.kugou.fanxing.recharge.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kugou.fanxing.recharge.controller.callback.LiveRechargeCallbackController;
import com.kugou.fanxing.tester.common.tools.HttpClientUtil;
import com.kugou.fanxing.tester.common.tools.UserUtil;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class ApiV2GetOrderForBankTest {
    LiveRechargeCallbackController rechargeCallbackController = new LiveRechargeCallbackController();
    HttpClientUtil httpClientUtil = new HttpClientUtil();
//    private String url = "http://**********:18888/recharge/api/v2/getOrderForAlipay";
//    private String url = "http://fxdev.fxwork.kugou.com/recharge/api/v2/getOrderForAlipay";
    private String url = "http://fxtest.fxwork.kugou.com/recharge/api/v2/getOrderForBank";
//    private String url = "http://fxtest.fxwork.kugou.com/recharge/api/v1/getOrderForAlipay";

    public ApiV2GetOrderForBankTest() throws IOException {
    }


    private Map<String,String> buildParams() {
        Map<String,String> map = new HashMap<>();
        map.put("userId","********");
        map.put("kugouId","*********");
        map.put("amount","10");
        map.put("Extension1","星币充值");
        map.put("qr","2");
        map.put("size","5");
        map.put("margin","1");
        map.put("couponId","0");
        map.put("n","0.***************");
        map.put("mid","366b2055f4f33bdc5fa18c1097347bd2");
        map.put("pid","7");
        map.put("ext","{}");
        return map;
    }

    private Map<String,String> buildHeader() throws Exception {
        Map<String,String> map = new HashMap<>();
        map.put("Accept","application/json");
        map.put("appid","1131");
        map.put("Content-Type","application/x-www-form-urlencoded");
        map.put("kgid","*********");
        map.put("token",UserUtil.getTokenByDb("*********"));
        map.put("ext","{}");
        return map;
    }

    @Test
    /**
     * 充值页面银行卡下单——下单成功
     */
    public void getOrderForBankV2_success() throws Exception {
        String result = httpClientUtil.postFromUrlEncodedHeaders(url,buildParams(),buildHeader());
        JSONObject jsonObject = JSON.parseObject(result);
        Assert.assertTrue(jsonObject.get("code").equals(0));
    }

    @Test
    @Ignore
    /**
     * 充值页面银行卡下单,使用代金券-代金券已使用或正在使用中，请刷新页面重新查看
     */
    public void getOrderForBankV2_coupon() throws Exception {
        Map<String,String> map = buildParams();
        map.put("couponId","1512117877841689670");
        String result = httpClientUtil.postFromUrlEncodedHeaders(url,map,buildHeader());
        JSONObject jsonObject = JSON.parseObject(result);
        Assert.assertTrue(jsonObject.get("code").equals(100028));
//        result.contains()
    }

    @Test
    /**
     * 充值页面银行卡下单,不使用代金券——未登录
     */
    public void getOrderForBankV2_notLogin() throws Exception {
        Map<String,String> map = buildHeader();
        map.put("token","testToken");
        String result = httpClientUtil.postFromUrlEncodedHeaders(url,buildParams(),map);
        JSONObject jsonObject = JSON.parseObject(result);
        Assert.assertTrue(jsonObject.get("code").equals(100002));
    }

    @Test
    /**
     * 充值页面银行卡下单,使用代金券——未登录
     */
    public void getOrderForBankV2_notLogin_1() throws Exception {
        Map<String,String> header = buildHeader();
        header.put("token","testToken");
        Map<String,String> params = buildParams();
        params.put("couponId","1512117877841689670");
        String result = httpClientUtil.postFromUrlEncodedHeaders(url,params,header);
        JSONObject jsonObject = JSON.parseObject(result);
        Assert.assertTrue(jsonObject.get("code").equals(100002));
    }

    @Test
    @Ignore
    /**
     * 充值页面银行卡下单,不使用代金券——kugouid=0 userid=0
     * 不错校验，正常通过
     */
    public void getOrderForBankV2_kugouid_userid() throws Exception {
        Map<String,String> header = buildHeader();
        Map<String,String> params = buildParams();
        params.put("kugouId","0");
        params.put("userId","0");
        String result = httpClientUtil.postFromUrlEncodedHeaders(url,params,header);
        JSONObject jsonObject = JSON.parseObject(result);
        Assert.assertTrue(jsonObject.get("code").equals(0));
    }
}
