package com.kugou.fanxing.recharge.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kugou.fanxing.tester.common.tools.HttpClientUtil;
import com.kugou.fanxing.tester.common.tools.UserUtil;
import org.apache.http.message.BasicHeader;
import org.junit.Assert;
import org.junit.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class ApiV1GetRechargePresentListTest {
    HttpClientUtil httpClientUtil = new HttpClientUtil();
    private String url = "http://fxtest.fxwork.kugou.com/recharge/api/v1/getRechargePresentList";
//    private String url = "http://127.0.0.1:18888/recharge/api/v1/getRechargeAmountGear";

    public ApiV1GetRechargePresentListTest() throws IOException {
    }


    private Map<String,String> buildParams() {
        Map<String,String> map = new HashMap<>();
        map.put("std_plat","76");
        return map;
    }

    private BasicHeader[] buildHeader() throws Exception {
        BasicHeader[] headers = {
                new BasicHeader("Content-type", "application/x-www-form-urlencoded")
                ,new BasicHeader("appid", "1131")
                ,new BasicHeader("kgid", "415770726")
                ,new BasicHeader("token", UserUtil.getTokenByDb("415770726"))
        };
        return headers;
    }

    @Test
//    @Ignore
    /**

     */
    public void GetRechargeAmountGearPresentTest() throws Exception {
        String result = httpClientUtil.getSetHeaders(url,buildParams(),buildHeader());
        JSONObject jsonObject = JSON.parseObject(result);
        Assert.assertTrue(jsonObject.get("code").equals(0));
    }

}
