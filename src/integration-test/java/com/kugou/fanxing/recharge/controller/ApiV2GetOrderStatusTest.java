package com.kugou.fanxing.recharge.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.tester.common.tools.HttpClientUtil;
import com.kugou.fanxing.tester.common.tools.UserUtil;
import org.junit.Assert;
import org.junit.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class ApiV2GetOrderStatusTest {

    HttpClientUtil httpClientUtil = new HttpClientUtil();

    String host = "http://zuultest.fxwork.kugou.com/platform_recharge_service";
    String kugouId = "415770726";
    String userId = "39100745";

    public ApiV2GetOrderStatusTest() throws IOException {
    }

    public Map<String,String> buildParams() throws Exception {
        Map<String,String> map = new HashMap<>();
        map.put("rechargeOrderNum","1");
        map.put("kugouId",kugouId);
        map.put("token",UserUtil.getTokenByDb(kugouId));
        map.put("appId","1131");
        return map;
    }


    @Test
    public void getOrderStatus() throws Exception {
        Map<String,String> map = buildParams();
        String res = httpClientUtil.postFromUrlEncoded(host + UrlConstants.RECHARGE_ORDER_STATUS_V2, map);
        JSONObject resJson = JSON.parseObject(res);
        Assert.assertTrue(resJson.get("code").equals(0));
;
    }

}
