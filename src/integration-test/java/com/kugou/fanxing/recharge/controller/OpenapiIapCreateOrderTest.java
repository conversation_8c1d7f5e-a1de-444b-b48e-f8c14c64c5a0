package com.kugou.fanxing.recharge.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.tester.common.tools.HttpClientUtil;
import com.kugou.fanxing.tester.common.tools.UserUtil;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class OpenapiIapCreateOrderTest {
    HttpClientUtil httpClientUtil = new HttpClientUtil();

    String host = "http://zuultest.fxwork.kugou.com/platform_recharge_service";
    String kugouId = "*********";
    String userId = "********";

    public OpenapiIapCreateOrderTest() throws IOException {
    }


    public Map<String,String> buildParams() throws Exception {
            Map<String,String> map = new HashMap<>();
            map.put("amount","1");
            map.put("std_anid","526842f885f3bd0b19b58f8e02b401e7");
            map.put("std_plat","1");
            map.put("std_kid",kugouId);
            map.put("cardType","1");
            map.put("alipayBankId","undefined");
            map.put("union_ver","2");
            map.put("channel","254");
            map.put("pid","1");
            map.put("userId",userId);
            map.put("version","51200");
            map.put("n","*************");
            map.put("std_imei","36880954745559683384812531557076055868");
            map.put("token",UserUtil.getTokenByDb(kugouId));
            map.put("bankId","undefined");
            map.put("payType","35");
            map.put("agentUserId","0");
            map.put("std_dev","526842f885f3bd0b19b58f8e02b401e7");
            map.put("appId","1131");
            map.put("action","getMobilePayUrl");
            map.put("channelId","354");
            map.put("kugouId",kugouId);
            return map;
        }



    @Test
    @Ignore
    public void createOrder() throws Exception {
        Map<String,String> map = buildParams();
        String res = httpClientUtil.postFromUrlEncoded(host + UrlConstants.KUGOU_OPEN_IAP_CREATE_ORDER, map);
        JSONObject resJson = JSON.parseObject(res);
        Assert.assertTrue(resJson.get("code").equals(0));
    }

}
