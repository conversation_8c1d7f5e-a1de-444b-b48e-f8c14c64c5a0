package com.kugou.fanxing.recharge.controller.thrift;

import com.kugou.fanxing.recharge.config.HostConstant;
import com.kugou.fanxing.recharge.thrift.RechargeThriftService;
import com.kugou.fanxing.recharge.thrift.ResultList;
import org.apache.thrift.TException;
import org.apache.thrift.protocol.TJSONProtocol;
import org.apache.thrift.protocol.TProtocol;
import org.apache.thrift.transport.THttpClient;
import org.apache.thrift.transport.TTransportException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;

public class GetRechargeListTest {

    static THttpClient transport = null;
    static TProtocol protocol;
    static RechargeThriftService.Client client;
    static String URL = "/platform_recharge_service/thrift/rechargeThriftService";
    static Long defaultEndTime = System.currentTimeMillis()/1000;

    @Before
    public void init() throws TTransportException, IOException {
        transport = new THttpClient(String.format(HostConstant.url + URL));
        transport.setCustomHeader("protocol", "json");
        protocol = new TJSONProtocol(transport);
        client = new RechargeThriftService.Client(protocol);
    }

    @Test
    /**
     * 查询成功，无数据
     */
    public void getRechargeSuccessList() throws IOException, TException {
        ResultList resultSuccessList = client.getRechargeList(System.currentTimeMillis()/1000-3600, System.currentTimeMillis()/1000, 0, 100);
        Assert.assertEquals(0,resultSuccessList.getRet());
    }

}
