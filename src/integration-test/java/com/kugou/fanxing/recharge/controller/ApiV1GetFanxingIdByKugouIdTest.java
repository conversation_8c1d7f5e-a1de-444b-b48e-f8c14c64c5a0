package com.kugou.fanxing.recharge.controller;

import com.alibaba.fastjson.JSONObject;
import com.kugou.fanxing.tester.common.tools.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class ApiV1GetFanxingIdByKugouIdTest {


    HttpClientUtil httpClientUtil = new HttpClientUtil();
    String host = "http://zuultest.fxwork.kugou.com/platform_recharge_service/intranet/api/v1/getFanxingIdByKugouId";

    public Map<String,String> buildRequest(){
        Map<String,String> map = new HashMap<>();
        map.put("serverid","111");
        map.put("servertime","222");
        map.put("appid","333");
        map.put("clientver","444");
        map.put("mid","555");
        map.put("uuid","666");
        map.put("dfid","777");
        map.put("signature","888");
        return map;
    }


    @Test
    /**
     *kugouid转userid，kugouid和userid相同
     */
    public void openFapiaoCallbackTest_success_1() throws Exception {
        Optional<String> result = httpClientUtil.postJsonBody(host,buildRequest()," {\"kugouid\":\"415770726\"}");
        System.out.println(result);
        JSONObject jsonObject = JSONObject.parseObject(result.get());
        Assert.assertEquals(39100678,jsonObject.getJSONObject("data").get("fxid"));
    }

    @Test
    /**
     *kugouid转userid，kugouid和userid不同
     */
    public void openFapiaoCallbackTest_success_2() throws Exception {
        Optional<String> result = httpClientUtil.postJsonBody(host,buildRequest()," {\"kugouid\":\"1729388448\"}");
        System.out.println(result);
        JSONObject jsonObject = JSONObject.parseObject(result.get());
        Assert.assertEquals(1729388448,jsonObject.getJSONObject("data").get("fxid"));
    }


    @Test
    /**
     *kugouid转userid 不存在kugouid
     */
    public void openFapiaoCallbackTest_notExist() throws Exception {
        Optional<String> result = httpClientUtil.postJsonBody(host,buildRequest()," {\"kugouid\":\"001234567890\"}");
        System.out.println(result);
        JSONObject jsonObject = JSONObject.parseObject(result.get());
        Assert.assertEquals(0,jsonObject.getJSONObject("data").get("fxid"));
    }


    @Test
    /**
     *kugouid转userid 不存在kugouid=-1
     */
    public void openFapiaoCallbackTest_notExist_1() throws Exception {
        Optional<String> result = httpClientUtil.postJsonBody(host,buildRequest()," {\"kugouid\":\"-1\"}");
        System.out.println(result);
        JSONObject jsonObject = JSONObject.parseObject(result.get());
        Assert.assertEquals(0,jsonObject.getJSONObject("data").get("fxid"));
    }

    @Test
    /**
     *kugouid转userid 不存在kugouid=
     */
    public void openFapiaoCallbackTest_notExist_2() throws Exception {
        Optional<String> result = httpClientUtil.postJsonBody(host,buildRequest()," {\"kugouid\":\"\"}");
        System.out.println(result);
        JSONObject jsonObject = JSONObject.parseObject(result.get());
        Assert.assertEquals(0,jsonObject.getJSONObject("data").get("fxid"));
    }

    @Test
    /**
     *kugouid转userid,body为空
     */
    public void openFapiaoCallbackTest_notExist_3() throws Exception {
        Optional<String> result = httpClientUtil.postJsonBody(host,buildRequest()," {}");
        System.out.println(result);
        JSONObject jsonObject = JSONObject.parseObject(result.get());
        Assert.assertEquals(0,jsonObject.getJSONObject("data").get("fxid"));
    }


}