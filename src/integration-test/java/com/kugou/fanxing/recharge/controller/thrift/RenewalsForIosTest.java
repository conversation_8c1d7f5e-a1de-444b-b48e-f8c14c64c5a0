package com.kugou.fanxing.recharge.controller.thrift;

import com.kugou.fanxing.recharge.config.HostConstant;
import com.kugou.fanxing.recharge.thrift.RechargeThriftService;
import com.kugou.fanxing.recharge.thrift.ResultList;
import com.kugou.fanxing.recharge.thrift.callback.CallbackResponse;
import com.kugou.fanxing.recharge.thrift.callback.ConsumeParam;
import com.kugou.fanxing.recharge.thrift.callback.RechargeCallbackService;
import com.kugou.fanxing.recharge.thrift.callback.RenewalsForIosRequest;
import org.apache.thrift.TException;
import org.apache.thrift.protocol.TJSONProtocol;
import org.apache.thrift.protocol.TProtocol;
import org.apache.thrift.transport.THttpClient;
import org.apache.thrift.transport.TTransportException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;

public class RenewalsForIosTest {

    static THttpClient transport = null;
    static TProtocol protocol;
    static RechargeCallbackService.Client client;
    static String URL = "/platform_recharge_service/thrift/rechargeCallbackService";
    static Long defaultEndTime = System.currentTimeMillis()/1000;

    @Before
    public void init() throws TTransportException, IOException {
        transport = new THttpClient(String.format(HostConstant.url + URL));
        transport.setCustomHeader("protocol", "json");
        protocol = new TJSONProtocol(transport);
        client = new RechargeCallbackService.Client(protocol);
    }

    @Test
    @Ignore
    /**
     * 苹果充值续费物品回调，调用消费充扣失败。
     */
    public void renewalsForIosFailed() throws TException {
        RenewalsForIosRequest renewalsForIosRequest= new RenewalsForIosRequest();
        renewalsForIosRequest.setOrderNo("test");
        renewalsForIosRequest.setSign("");
        renewalsForIosRequest.setKugouId(*********);
        renewalsForIosRequest.setTime(1);
        renewalsForIosRequest.setCoin("10");
        renewalsForIosRequest.setMoney("10");
        renewalsForIosRequest.setSignBook("123");
        renewalsForIosRequest.setType(1);
        renewalsForIosRequest.setGoodsId("111");
        renewalsForIosRequest.setPid(1);
        renewalsForIosRequest.setVersion("12");
        renewalsForIosRequest.setChannelId(1);
        renewalsForIosRequest.setIsSandbox(1);
        renewalsForIosRequest.setTradeTime("2021-12-23 11:00:00");
        renewalsForIosRequest.setTradeNo("123");
        renewalsForIosRequest.setPartner("123");
        renewalsForIosRequest.setBusinessExt("");
        renewalsForIosRequest.setToKugouId(1);
        renewalsForIosRequest.setClientIp("127.0.0.1");
        ConsumeParam ConsumeParam = new ConsumeParam();
        ConsumeParam.setAccountChangeType(110231);
        ConsumeParam.setFromKugouId(*********);
        ConsumeParam.setToKugouId(1);
        ConsumeParam.setGiftId(1);
        ConsumeParam.setGiftNum(1);
        ConsumeParam.setRoomId(1);
        ConsumeParam.setGiftName("rose");
        ConsumeParam.setExt("");
        ConsumeParam.setFxcChangeDesc("test");
        ConsumeParam.setCoin(5);
        renewalsForIosRequest.setConsumeParam(ConsumeParam);
        renewalsForIosRequest.setExt("");
        renewalsForIosRequest.setBusinessId("123");
        renewalsForIosRequest.setTopic("test");
        renewalsForIosRequest.setBizEndTime(System.currentTimeMillis()/1000);
        CallbackResponse callbackResponse = client.renewalsForIos(renewalsForIosRequest);
        Assert.assertEquals(1,callbackResponse.getCode());
    }

}
