package com.kugou.fanxing.recharge.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kugou.fanxing.recharge.controller.callback.LiveRechargeCallbackController;
import com.kugou.fanxing.tester.common.tools.HttpClientUtil;
import com.kugou.fanxing.tester.common.tools.UserUtil;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class ApiV1SwitchListTest {
    LiveRechargeCallbackController rechargeCallbackController = new LiveRechargeCallbackController();
    HttpClientUtil httpClientUtil = new HttpClientUtil();
//    private String url = "http://**********:18888/recharge/api/v2/getOrderForAlipay";
//    private String url = "http://fxdev.fxwork.kugou.com/recharge/api/v2/getOrderForAlipay";
//    private String url = "http://fxtest.fxwork.kugou.com/recharge/api/v2/getOrderForAlipay";
//    private String url = "http://fxtest.fxwork.kugou.com/recharge/api/v1/switchList";
    private String url = "http://zuultest.fxwork.kugou.com/platform_recharge_service/recharge/api/v1/switchList";

    public ApiV1SwitchListTest() throws IOException {
    }


    private Map<String,String> buildParams() {
        Map<String,String> map = new HashMap<>();
        map.put("userId","39100745");
        map.put("kugouId","415770726");
        map.put("amount","10");
        map.put("Extension1","星币充值");
        map.put("qr","2");
        map.put("size","5");
        map.put("margin","1");
        map.put("couponId","0");
        map.put("n","0.759419315065863");
        map.put("mid","366b2055f4f33bdc5fa18c1097347bd2");
        map.put("pid","7");
        map.put("ext","{}");
        return map;
    }

    private Header[] buildHeader() throws Exception {
        Header[] headers = {
                new BasicHeader("Content-type", "application/x-www-form-urlencoded")
                ,new BasicHeader("appid", "1131")
                ,new BasicHeader("kgid", "415770726")
                ,new BasicHeader("token", UserUtil.getTokenByDb("415770726"))
        };
        return headers;
    }

    @Test
    /**
     *验证白名单内，灰度百分比内-返回true
     * 验证白名单外，灰度百分比外-返回false
     */
    public void getOrderForAlipayV2Test() throws Exception {
        String result = httpClientUtil.getSetHeaders(url,buildParams(),buildHeader());
        JSONObject jsonObject = JSON.parseObject(result);
        Assert.assertTrue(jsonObject.get("code").equals(0));
    }

}
