package com.kugou.fanxing.recharge.controller;

import com.kugou.fanxing.recharge.Application;
import com.kugou.fanxing.recharge.controller.fapiao.FapiaoController;
import com.kugou.fanxing.recharge.model.request.FapiaoCallbackRequest;
import com.kugou.fanxing.recharge.model.request.KupayCommonRequest;
import com.kugou.fanxing.tester.common.tools.HttpClientUtil;
import com.kugou.fanxing.tester.common.tools.MysqlUtils;
import com.kugou.fanxing.tester.common.tools.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.sql.Connection;
import java.sql.Driver;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class ApiV1FapiaoCallbackTest {
    @Autowired
    FapiaoController fapiaoController;

//    @Autowired
//    PlatformCertificationService platformCertificationService;

    HttpClientUtil httpClientUtil = new HttpClientUtil();
    MysqlUtils mysqlUtils = new MysqlUtils();
    Map<String,String> headers = new HashMap<>();
    Connection connection = MysqlUtils.connect(Driver.class.getCanonicalName(), "*********************************************************", "fanxing", "kugou2014");
    String host = "http://zuultest.fxwork.kugou.com/platform_recharge_service";
    String kugouId = "928696385";
    String userId = "39100745";


    public KupayCommonRequest buildKupayCommonRequest(){
        KupayCommonRequest kupayCommonRequest = new KupayCommonRequest();
        kupayCommonRequest.setAppid("123");
        kupayCommonRequest.setClientver("123");
        kupayCommonRequest.setDfid("123");
        kupayCommonRequest.setMid("123");
        kupayCommonRequest.setSignature("123");
        kupayCommonRequest.setServerid(123);
        kupayCommonRequest.setUuid("123");
        return kupayCommonRequest;
    }

    public FapiaoCallbackRequest buildFapiaoCallbackRequest(){
        FapiaoCallbackRequest fapiaoCallbackRequest = new FapiaoCallbackRequest();
        fapiaoCallbackRequest.setUserid(415770726);
        fapiaoCallbackRequest.setOut_trade_no("01202106301632400100011337");
        fapiaoCallbackRequest.setOrder_no("");
        fapiaoCallbackRequest.setCan_receipt(true);
        fapiaoCallbackRequest.setReceipt_status(1);
        fapiaoCallbackRequest.setReceipt_type(1);
        fapiaoCallbackRequest.setOperation_type(1);
        fapiaoCallbackRequest.setAdd_time(String.valueOf(System.currentTimeMillis()/1000));
        fapiaoCallbackRequest.setUpdate_time(String.valueOf(System.currentTimeMillis()/1000));
        return fapiaoCallbackRequest;
    }

    @Test
    /**
     *setCan_receipt=true
     */
    public void openFapiaoCallbackTest_true() throws Exception {
        String result = fapiaoController.openFapiaoCallback(buildKupayCommonRequest(),buildFapiaoCallbackRequest());
        Assert.assertEquals(result,"success");
    }

    @Test
    /**
     *setCan_receipt=false
     */
    public void openFapiaoCallbackTest_true_1() throws Exception {
        FapiaoCallbackRequest fapiaoCallbackRequest = buildFapiaoCallbackRequest();
        fapiaoCallbackRequest.setCan_receipt(false);
        String result = fapiaoController.openFapiaoCallback(buildKupayCommonRequest(),fapiaoCallbackRequest);
        Assert.assertEquals(result,"success");
    }

    @Test
    /**
     *setOut_trade_no=不存在的订单号
     */
    public void openFapiaoCallbackTest_false() throws Exception {
        FapiaoCallbackRequest fapiaoCallbackRequest = buildFapiaoCallbackRequest();
        fapiaoCallbackRequest.setOut_trade_no("123456789");
        String result = fapiaoController.openFapiaoCallback(buildKupayCommonRequest(),fapiaoCallbackRequest);
        Assert.assertEquals(result,"failure");
    }
}
