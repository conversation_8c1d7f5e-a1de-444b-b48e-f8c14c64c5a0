package com.kugou.fanxing.recharge.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.controller.callback.LiveRechargeCallbackController;
import com.kugou.fanxing.tester.common.tools.HttpClientUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;


public class V1PurchaseCallbackGoodsTest {
    LiveRechargeCallbackController rechargeCallbackController = new LiveRechargeCallbackController();
    HttpClientUtil httpClientUtil = new HttpClientUtil();
    RechargeConfig rechargeConfig = new RechargeConfig();

    private String urlGoodsJava = "http://zuultest.fxwork.kugou.com/platform_recharge_service/intranet/api/v1/purchase/callbackGoods";


//    trade_status=0, out_trade_no=01202103021745320100015771, total_fee=1.00, royalty_fee=0.000000, action=, trade_time=1614678415, trade_no=1000000783867005, partner=1316427701
    private Map<String,String> buildParams() {
        Map<String,String> map = new HashMap<>();
        map.put("trade_status","1");
        map.put("out_trade_no","01202104011806580100016354");
        map.put("total_fee","0.02");
        map.put("royalty_fee","0");
        map.put("action","callBackAcross");
        map.put("trade_time","20210401180713");
        map.put("trade_no","4200000938202104012866851912");
        map.put("partner","1218497501");
        map.put("appid","1084");
        map.put("areaid","01");
        map.put("time","1617271634");
        map.put("notify_id","c215a42e9530191a0bc8c50b50b43fe2");
        map.put("userid","1290249156");
        map.put("order_no","R092021040118065796380088");
        map.put("callback_status","0");
        map.put("extend","eyJjYWxsQmFja1NpZ24iOiJkZTZiNDc2YjBhZjVjNGNhODBiMjgyNjI0ZGEwNzRiNyIsImNhbGxCYWNrQXJnIjp7ImFtb3VudCI6IjAuMDIiLCJhZGRUaW1lIjoiMTYxNzI3MTYxOCIsImNvdXBvbiI6IjAiLCJyZWJhdGUiOjAsImJ1eVJpY2hMZXZlbCI6W10sImNvbnN1bWVBcmdzIjpbXSwiYnVzaW5lc3NFeHQiOiIiLCJjb3Vwb25JZCI6MCwidmVyc2lvbiI6IjIwMTcwMTExIiwiY291cG9uT3JkZXJJZCI6MCwicGF5VHlwZUlkIjo0MCwibW9uZXkiOiIwLjAyIiwicmVmZXIiOjAsImNGcm9tIjoxLCJjaGFubmVsSWQiOjAsImt1Z291SWQiOjEyOTAyNDkxNTZ9fQ==");
        Map<String, String> callBackSignArg = Maps.newHashMap();
        callBackSignArg.put("order_no", "R092021040118065796380088");
        callBackSignArg.put("userid", "1290249156");
        String callBackKey = rechargeConfig.getCallBackKey();
        String sign = DigestUtils.md5Hex(JSON.toJSONString(callBackSignArg) + callBackKey);
        map.put("sign",sign);
        return map;
    }




    @Test
    public void callBackGoods() throws IOException {
        Map<String,String> map = buildParams();
        String resJava = httpClientUtil.postFromUrlEncoded(urlGoodsJava,map);
        JSONObject resJavaJson = JSONObject.parseObject(resJava);
        System.out.println(resJavaJson);
    }
}