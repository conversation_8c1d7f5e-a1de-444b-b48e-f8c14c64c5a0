package com.kugou.fanxing.recharge.controller.thrift;

import com.kugou.fanxing.recharge.config.HostConstant;
import com.kugou.fanxing.recharge.thrift.callback.CallbackResponse;
import com.kugou.fanxing.recharge.thrift.callback.ConsumeParam;
import com.kugou.fanxing.recharge.thrift.callback.PurchaseForIosRequest;
import com.kugou.fanxing.recharge.thrift.callback.RechargeCallbackService;
import org.apache.thrift.TException;
import org.apache.thrift.protocol.TJSONProtocol;
import org.apache.thrift.protocol.TProtocol;
import org.apache.thrift.transport.THttpClient;
import org.apache.thrift.transport.TTransportException;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;

import static com.kugou.fanxing.biz.commons.util.FinanceSignUtils.makeSign;

public class PurchaseForIosTest {

    public static final String INTERFACE_getRechargeSuccessList = "getRechargeSuccessList";

    static THttpClient transport = null;
    static TProtocol protocol;
    static RechargeCallbackService.Client client;
    static String URL = "/platform_recharge_service/thrift/rechargeCallbackService";
    static Long defaultEndTime = System.currentTimeMillis()/1000;

    @Before
    public void init() throws TTransportException, IOException {
        transport = new THttpClient(String.format(HostConstant.url + URL));
        transport.setCustomHeader("protocol", "json");
        protocol = new TJSONProtocol(transport);
        client = new RechargeCallbackService.Client(protocol);
    }

    public PurchaseForIosRequest buildParams(){
        PurchaseForIosRequest purchaseForIosRequest = new PurchaseForIosRequest();
        purchaseForIosRequest.setSignBook("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");
        purchaseForIosRequest.setTradeNo("1000000756956362");
        purchaseForIosRequest.setGoodsId("com.fanxing.fxappstore.66coin");
        purchaseForIosRequest.setBusinessExt("");
        purchaseForIosRequest.setPid(2);
        purchaseForIosRequest.setType(1);
        purchaseForIosRequest.setVersion("49929");
        purchaseForIosRequest.setTradeTime("2020-12-21 15:36:13");
        purchaseForIosRequest.setMoney("1");
        purchaseForIosRequest.setPartner("");
        purchaseForIosRequest.setTime(1608536400);
        purchaseForIosRequest.setIsSandbox(1);
        purchaseForIosRequest.setChannelId(1009);
        purchaseForIosRequest.setKugouId(**********);
        purchaseForIosRequest.setCoin("66");
        purchaseForIosRequest.setOrderNo("66");
        purchaseForIosRequest.setClientIp("127.0.0.1");
        purchaseForIosRequest.setExt("");
        purchaseForIosRequest.setBusinessId("123");
        purchaseForIosRequest.setGiftId(1);
        ConsumeParam consumeParam = new ConsumeParam();
        consumeParam.setGiftName("测试");
        consumeParam.setExt("");
        consumeParam.setFxcChangeDesc("测试");
        consumeParam.setAccountChangeType(132041);
        purchaseForIosRequest.setConsumeParam(consumeParam);
        purchaseForIosRequest.setSign(makeSign(purchaseForIosRequest,""));
        return purchaseForIosRequest;
    }


    @Test
    @Ignore
    /**
     *
     */
    public void purchaseForIos() throws IOException, TException {
        CallbackResponse callbackResponse = client.purchaseForIos(buildParams());
        System.out.println(callbackResponse);
    }
}