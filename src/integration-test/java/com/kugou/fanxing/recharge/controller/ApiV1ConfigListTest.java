package com.kugou.fanxing.recharge.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.tester.common.tools.HttpClientUtil;
import com.kugou.fanxing.tester.common.tools.MysqlUtils;
import com.kugou.fanxing.tester.common.tools.UserUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.sql.Connection;
import java.sql.Driver;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ApiV1ConfigListTest {
    HttpClientUtil httpClientUtil = new HttpClientUtil();

//    String host = "http://zuultest.fxwork.kugou.com/platform_recharge_service";
    String host = "http://fxtest.fxwork.kugou.com";
    String kugouId = "415770726";
    String userId = "39100745";

    public Map<String,String> buildParams() throws Exception {
        Map<String,String> map = new HashMap<>();
        map.put("amount","1");
        map.put("std_kid",kugouId);
        map.put("userId",userId);
        map.put("appId","1131");
        map.put("token",UserUtil.getTokenByDb(kugouId));
        map.put("kugouId",kugouId);
        return map;
    }


    @Test
    /**
     * 成功查询-lastRechargeId=-1
     */
    public void getRechargeSwitch() throws Exception {
        Map<String,String> map = buildParams();
        String res = httpClientUtil.get(host + UrlConstants.GET_RECHARGE_CONFIG_LIST, map);
        JSONObject resJson = JSON.parseObject(res);
        Assert.assertTrue(resJson.get("code").equals(0));

    }


    @Test
    /**
     * 成功查询-lastRechargeId=-1
     */
    public void getRechargeSwitchH5() throws Exception {
        Map<String,String> map = buildParams();
        map.put("isH5","true");
        String res = httpClientUtil.get(host + UrlConstants.GET_RECHARGE_CONFIG_LIST, map);
        JSONObject resJson = JSON.parseObject(res);
        Assert.assertTrue(resJson.get("code").equals(0));

    }
}

