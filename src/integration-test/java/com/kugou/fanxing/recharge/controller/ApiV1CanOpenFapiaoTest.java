package com.kugou.fanxing.recharge.controller;

import com.kugou.fanxing.recharge.Application;
import com.kugou.fanxing.recharge.controller.fapiao.FapiaoController;
import com.kugou.fanxing.recharge.model.request.CanOpenFapiaoRequest;
import com.kugou.fanxing.recharge.model.request.KupayCommonRequest;
import com.kugou.fanxing.recharge.model.response.KupayCommonResponse;
import com.kugou.fanxing.tester.common.tools.HttpClientUtil;
import com.kugou.fanxing.tester.common.tools.MysqlUtils;
import com.kugou.fanxing.tester.common.tools.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.sql.Connection;
import java.sql.Driver;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class ApiV1CanOpenFapiaoTest {
    @Autowired
    FapiaoController fapiaoController;

    KupayCommonResponse kupayCommonResponse;
//    @Autowired
//    PlatformCertificationService platformCertificationService;

    HttpClientUtil httpClientUtil = new HttpClientUtil();
    MysqlUtils mysqlUtils = new MysqlUtils();
    Map<String,String> headers = new HashMap<>();
    Connection connection = MysqlUtils.connect(Driver.class.getCanonicalName(), "*********************************************************", "fanxing", "kugou2014");

    String host = "http://zuultest.fxwork.kugou.com/platform_recharge_service";
    String kugouId = "928696385";
    String userId = "39100745";


    public KupayCommonRequest buildKupayCommonRequest(){
        KupayCommonRequest kupayCommonRequest = new KupayCommonRequest();
        kupayCommonRequest.setAppid("123");
        kupayCommonRequest.setClientver("123");
        kupayCommonRequest.setDfid("123");
        kupayCommonRequest.setMid("123");
        kupayCommonRequest.setSignature("123");
        kupayCommonRequest.setServerid(123);
        kupayCommonRequest.setUuid("123");
        return kupayCommonRequest;
    }

    public CanOpenFapiaoRequest buildCanOpenFapiaoRequest(){
        CanOpenFapiaoRequest canOpenFapiaoRequest = new CanOpenFapiaoRequest();
        canOpenFapiaoRequest.setKugouid(415770726);
        return canOpenFapiaoRequest;
    }

    @Test
    @Ignore
    /**
     *
     */
    public void canOpenFapiaoTest_true() throws Exception {
        String result = httpClientUtil.postJson("http://fxtest.kugou.net/numtool/api/testTools/makeData/run","{\"toolName\":\"realNameAuthentication\",\"params\":\"415770726,123456789123456789\",\"token\":\"fanXingMockDataToken\",\"userName\":\"王振\"}");
        System.out.println(result);
        kupayCommonResponse = fapiaoController.canOpenFapiao(buildKupayCommonRequest(),buildCanOpenFapiaoRequest());
        Assert.assertEquals(kupayCommonResponse.getError_code(),0);
        Assert.assertEquals(kupayCommonResponse.getData().get("can_receipt"),true);
    }

    @Test
    @Ignore
    /**
     *
     */
    public void canOpenFapiaoTest_false() throws Exception {
        String result = httpClientUtil.postJson("http://fxtest.kugou.net/numtool/api/testTools/makeData/run","{\"toolName\":\"cleanRealNameAuthentication\",\"params\":\"415770726\",\"token\":\"fanXingMockDataToken\",\"userName\":\"王振\"}");
        System.out.println(result);
        kupayCommonResponse = fapiaoController.canOpenFapiao(buildKupayCommonRequest(),buildCanOpenFapiaoRequest());
        Assert.assertEquals(kupayCommonResponse.getError_code(),0);
        Assert.assertEquals(kupayCommonResponse.getData().get("can_receipt"),false);
    }


}
