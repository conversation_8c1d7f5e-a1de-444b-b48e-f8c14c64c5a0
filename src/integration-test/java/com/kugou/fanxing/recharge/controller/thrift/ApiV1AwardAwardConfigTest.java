package com.kugou.fanxing.recharge.controller.thrift;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.tester.common.tools.HttpClientUtil;
import com.kugou.fanxing.tester.common.tools.UserUtil;
import org.junit.Assert;
import org.junit.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class ApiV1AwardAwardConfigTest {
    HttpClientUtil httpClientUtil = new HttpClientUtil();

    String host = System.getenv("ideaEnv") != null && System.getenv("ideaEnv").equals("local")?"http://127.0.0.1:18888":"http://zuultest.fxwork.kugou.com/platform_recharge_service";
    String kugouId = "415770726";
    String userId = "39100745";

    public ApiV1AwardAwardConfigTest() throws IOException {
    }

    public Map<String,String> buildParams() throws Exception {
        Map<String,String> map = new HashMap<>();
        map.put("sourceId","0");
        map.put("bizCode","0");
        map.put("rechargeOrderNum","R092023056661683787825208");
        map.put("kugouId",kugouId);
        map.put("token",UserUtil.getTokenByDb(kugouId));
        map.put("appId","1131");
        map.put("index","0");
        map.put("page","1");
        map.put("pageSize","100");
        map.put("pass","");
        map.put("stdPlat","7");
        map.put("std_plat","7");
        return map;
    }


    @Test
    public void apiV1AwardAwardConfigTest() throws Exception {
        Map<String,String> map = buildParams();
        String res = httpClientUtil.get(host + UrlConstants.RECHARGE_AWARD_CONFIG, map);
        JSONObject resJson = JSON.parseObject(res);
        Assert.assertTrue(resJson.get("code").equals(0));
    }
}
