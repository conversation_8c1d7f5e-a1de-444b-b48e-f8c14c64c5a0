package com.kugou.fanxing.recharge.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.fanxing.tester.common.tools.HttpClientUtil;
import com.kugou.fanxing.tester.common.tools.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class ApiV1BankPayMTest {

    HttpClientUtil httpClientUtil = new HttpClientUtil();

    String host = "http://zuultest.fxwork.kugou.com/platform_recharge_service";
    String kugouId = "*********";
    String userId = "********";

    public ApiV1BankPayMTest() throws IOException {
    }

    public Map<String,String> buildParams() throws Exception {
        Map<String,String> map = new HashMap<>();
        map.put("amount","1");
        map.put("std_anid","526842f885f3bd0b19b58f8e02b401e7");
        map.put("std_plat","1");
        map.put("std_kid",kugouId);
        map.put("cardType","1");
        map.put("alipayBankId","undefined");
        map.put("union_ver","2");
        map.put("channel","254");
        map.put("pid","1");
        map.put("userId",userId);
        map.put("version","51200");
        map.put("n","*************");
        map.put("std_imei","36880954745559683384812531557076055868");
        map.put("token",UserUtil.getTokenByDb(kugouId));
        map.put("bankId","undefined");
        map.put("payType","35");
        map.put("agentUserId","0");
        map.put("std_dev","526842f885f3bd0b19b58f8e02b401e7");
        map.put("appId","1131");
        map.put("action","getMobilePayUrl");
        map.put("channelId","354");
        map.put("dfid","test");
        map.put("kfd","test");
        map.put("ssad","test");
        return map;
    }

    /**
     * 手机银行卡下单接口-正常下单
     */
    @Test
    @Ignore
    public void bankPayMV1() throws Exception {
        Map<String,String> map = buildParams();
        String res = httpClientUtil.postFromUrlEncoded(host + UrlConstants.GET_ORDER_FOR_BANKPAY_MOBILE, map);
        JSONObject resJson = JSON.parseObject(res);
        log.warn("resJson: {}", resJson);
        int status = JsonUtils.parseJsonPathChecked(resJson.toJSONString(), "$.status", Integer.class);
        Assert.assertEquals(1, status);
    }

    @Test
    @Ignore
    public void bankPayMV2() throws Exception {
        Map<String,String> map = buildParams();
        String res = httpClientUtil.postFromUrlEncoded(host + UrlConstants.GET_ORDER_FOR_BANKPAY_MOBILE_V2, map);
        JSONObject resJson = JSON.parseObject(res);
        log.warn("resJson: {}", resJson);
        int status = JsonUtils.parseJsonPathChecked(resJson.toJSONString(), "$.data.status", Integer.class);
        Assert.assertEquals(1, status);
    }
}
