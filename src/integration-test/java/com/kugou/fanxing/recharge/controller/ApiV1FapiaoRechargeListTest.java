package com.kugou.fanxing.recharge.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.tester.common.tools.HttpClientUtil;
import com.kugou.fanxing.tester.common.tools.MysqlUtils;
import com.kugou.fanxing.tester.common.tools.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.sql.Connection;
import java.sql.Driver;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class ApiV1FapiaoRechargeListTest {

    HttpClientUtil httpClientUtil = new HttpClientUtil();
    MysqlUtils mysqlUtils = new MysqlUtils();
    Connection connection = MysqlUtils.connect(Driver.class.getCanonicalName(), "jdbc:mysql://***********:3306/d_fanxing_recharge_allocate", "fanxing", "kugou2014");
    String host = "http://zuultest.fxwork.kugou.com/platform_recharge_service";
    String kugouId = "*********";
    String userId = "********";

    public Map<String,String> buildParams() throws Exception {
        Map<String,String> map = new HashMap<>();
        map.put("amount","1");
        map.put("std_anid","526842f885f3bd0b19b58f8e02b401e7");
        map.put("std_plat","1");
        map.put("std_kid",kugouId);
        map.put("cardType","1");
        map.put("alipayBankId","undefined");
        map.put("union_ver","2");
        map.put("channel","254");
        map.put("pid","1");
        map.put("userId",userId);
        map.put("version","51200");
        map.put("n","*************");
        map.put("std_imei","36880954745559683384812531557076055868");
        map.put("token",UserUtil.getTokenByDb(kugouId));
        map.put("bankId","undefined");
        map.put("payType","35");
        map.put("agentUserId","0");
        map.put("std_dev","526842f885f3bd0b19b58f8e02b401e7");
        map.put("appId","1131");
        map.put("action","getMobilePayUrl");
        map.put("channelId","354");
        map.put("kugouId",kugouId);
        map.put("lastRechargeId","-1");
        return map;
    }

    @Before
    public void Before(){
        //删除所有的数据库数据
        System.out.println(MysqlUtils.update(connection, MysqlUtils.Command.build().sql("delete from t_recharge_success_85 where kugouId = ?").params(kugouId)));
        //插入测试数据
        MysqlUtils.update(connection, "INSERT INTO t_recharge_success_85(rechargeId, rechargeOrderNum, outTradeNo, addTime, rechargeTime, kugouId, coin, amount, realAmount, money, coupon, payTypeId, refer, cFrom, channelId, serverRoom, reType, tradeTime, tradeNo, partner, businessId, couponId, couponStatus, couponOrderId, isSandbox, fapiaoOrderId, fapiaoStatus, clientIp, deviceId, orderNo) VALUES (1523704734518027582, 'R092021070614582560932849', '01202107061458260100013451', 1625554705, 1625554717, 928696385, 2.00, 0.02, 2.00, 0.02, 0.00, 3, 0, 7, 1, 0, 0, 1625554716, '2021070622001406541425201644', '2088521299085603', '', 0, 0, 0, 0, 0, 0, '', '', '');");
        MysqlUtils.update(connection, "INSERT INTO t_recharge_success_85(rechargeId, rechargeOrderNum, outTradeNo, addTime, rechargeTime, kugouId, coin, amount, realAmount, money, coupon, payTypeId, refer, cFrom, channelId, serverRoom, reType, tradeTime, tradeNo, partner, businessId, couponId, couponStatus, couponOrderId, isSandbox, fapiaoOrderId, fapiaoStatus, clientIp, deviceId, orderNo) VALUES (1523708420321429550, 'R092021070615125744431294', '01202107061512580100012343', 1625555577, 1625555596, 928696385, 2.00, 0.02, 2.00, 0.02, 0.00, 3, 0, 7, 1, 0, 0, 1625555595, '2021070622001406541425640851', '2088521299085603', '', 0, 0, 0, 0, 0, 0, '', '', '');");
        MysqlUtils.update(connection, "INSERT INTO t_recharge_success_85(rechargeId, rechargeOrderNum, outTradeNo, addTime, rechargeTime, kugouId, coin, amount, realAmount, money, coupon, payTypeId, refer, cFrom, channelId, serverRoom, reType, tradeTime, tradeNo, partner, businessId, couponId, couponStatus, couponOrderId, isSandbox, fapiaoOrderId, fapiaoStatus, clientIp, deviceId, orderNo) VALUES (1523708420321429551, 'R092021070615125744431295', '01202107061512580100012344', 1625555577, 1625555596, 928696385, 2.00, 0.02, 2.00, 0.02, 0.00, 3, 0, 7, 1, 0, 0, 1625555595, '2021070622001406541425640852', '2088521299085603', '', 0, 0, 0, 0, 0, 0, '', '', '123456789');");
        MysqlUtils.update(connection, "INSERT INTO t_recharge_success_85(rechargeId, rechargeOrderNum, outTradeNo, addTime, rechargeTime, kugouId, coin, amount, realAmount, money, coupon, payTypeId, refer, cFrom, channelId, serverRoom, reType, tradeTime, tradeNo, partner, businessId, couponId, couponStatus, couponOrderId, isSandbox, fapiaoOrderId, fapiaoStatus, clientIp, deviceId, orderNo) VALUES (1523708420321429552, 'R092021070615125744431296', '01202107061512580100012345', 1625555577, 1625555596, 928696385, 2.00, 0.02, 2.00, 0.02, 0.00, 3, 0, 7, 1, 0, 0, 1625555595, '2021070622001406541425640853', '2088521299085603', '', 0, 0, 0, 0, 0, 0, '', '', '123456789');");
        MysqlUtils.update(connection, "INSERT INTO t_recharge_success_85(rechargeId, rechargeOrderNum, outTradeNo, addTime, rechargeTime, kugouId, coin, amount, realAmount, money, coupon, payTypeId, refer, cFrom, channelId, serverRoom, reType, tradeTime, tradeNo, partner, businessId, couponId, couponStatus, couponOrderId, isSandbox, fapiaoOrderId, fapiaoStatus, clientIp, deviceId, orderNo) VALUES (1523708420321429553, 'R092021070615125744431297', '01202107061512580100012346', 1625555577, 1625555596, 928696385, 2.00, 0.02, 2.00, 0.02, 0.00, 3, 0, 7, 1, 0, 0, 1625555595, '2021070622001406541425640854', '2088521299085603', '', 0, 0, 0, 0, 0, 0, '', '', '123456789');");
        MysqlUtils.update(connection, "INSERT INTO t_recharge_success_85(rechargeId, rechargeOrderNum, outTradeNo, addTime, rechargeTime, kugouId, coin, amount, realAmount, money, coupon, payTypeId, refer, cFrom, channelId, serverRoom, reType, tradeTime, tradeNo, partner, businessId, couponId, couponStatus, couponOrderId, isSandbox, fapiaoOrderId, fapiaoStatus, clientIp, deviceId, orderNo) VALUES (1523708420321429554, 'R092021070615125744431298', '01202107061512580100012347', 1625555577, 1625555596, 928696385, 2.00, 0.02, 2.00, 0.02, 0.00, 3, 0, 7, 1, 0, 0, 1625555595, '2021070622001406541425640855', '2088521299085603', '', 0, 0, 0, 0, 0, 0, '', '', '');");
        MysqlUtils.update(connection, "INSERT INTO t_recharge_success_85(rechargeId, rechargeOrderNum, outTradeNo, addTime, rechargeTime, kugouId, coin, amount, realAmount, money, coupon, payTypeId, refer, cFrom, channelId, serverRoom, reType, tradeTime, tradeNo, partner, businessId, couponId, couponStatus, couponOrderId, isSandbox, fapiaoOrderId, fapiaoStatus, clientIp, deviceId, orderNo) VALUES (1523708420321429555, 'R092021070615125744431299', '01202107061512580100012348', 1625555577, 1625555596, 928696385, 2.00, 0.02, 2.00, 0.02, 0.00, 3, 0, 7, 1, 0, 0, 1625555595, '2021070622001406541425640856', '2088521299085603', '', 0, 0, 0, 0, 0, 0, '', '', '');");
        MysqlUtils.update(connection, "INSERT INTO t_recharge_success_85(rechargeId, rechargeOrderNum, outTradeNo, addTime, rechargeTime, kugouId, coin, amount, realAmount, money, coupon, payTypeId, refer, cFrom, channelId, serverRoom, reType, tradeTime, tradeNo, partner, businessId, couponId, couponStatus, couponOrderId, isSandbox, fapiaoOrderId, fapiaoStatus, clientIp, deviceId, orderNo) VALUES (1523708420321429556, 'R092021070615125744431290', '01202107061512580100012349', 1625555577, 1625555596, 928696385, 2.00, 0.02, 2.00, 0.02, 0.00, 3, 0, 7, 1, 0, 0, 1625555595, '2021070622001406541425640857', '2088521299085603', '', 0, 0, 0, 0, 0, 0, '', '', '');");
        MysqlUtils.disconnect(connection);
    }



    @Test
    /**
     * 成功查询-lastRechargeId=-1
     */
    public void getFapiaoRechargeList() throws Exception {
        Map<String,String> map = buildParams();
        String res = httpClientUtil.get(host + UrlConstants.GET_FAPIAO_RECHARGE_LIST, map);
        JSONObject resJson = JSON.parseObject(res);
        log.warn("resJson: {}", resJson);
        Assert.assertTrue(resJson.get("code").equals(0));
        JSONObject data = resJson.getJSONObject("data");
        List<Object> rechargeInfos = data.getJSONArray("rechargeInfos");
    }

    @Test
    /**
     * 成功查询-lastRechargeId=1518669697808401054
     */
    public void getFapiaoRechargeList_lastRechargeId() throws Exception {
        Map<String,String> map = buildParams();
        map.put("lastRechargeId","1523708420321429552");
        String res = httpClientUtil.get(host + UrlConstants.GET_FAPIAO_RECHARGE_LIST, map);
        JSONObject resJson = JSON.parseObject(res);
        log.warn("resJson: {}", resJson);
        Assert.assertTrue(resJson.get("code").equals(0));
        JSONObject data = resJson.getJSONObject("data");
        List<Object> rechargeInfos = data.getJSONArray("rechargeInfos");

    }

    @Test
    /**
     * 查询失败-token错误
     */
    public void getFapiaoRechargeList_token() throws Exception {
        Map<String,String> map = buildParams();
        map.put("token","test_token");
        String res = httpClientUtil.get(host + UrlConstants.GET_FAPIAO_RECHARGE_LIST, map);
        JSONObject resJson = JSON.parseObject(res);
        log.warn("resJson: {}", resJson);
        Assert.assertTrue(resJson.get("code").equals(100002));
    }

    @Test
    /**
     * 查询失败-userId和kugouid不匹配，未校验userid，正常返回
     */
    public void getFapiaoRechargeList_userid() throws Exception {
        Map<String,String> map = buildParams();
        map.put("userId","123456");
        String res = httpClientUtil.get(host + UrlConstants.GET_FAPIAO_RECHARGE_LIST, map);
        JSONObject resJson = JSON.parseObject(res);
        log.warn("resJson: {}", resJson);
        Assert.assertTrue(resJson.get("code").equals(0));
        JSONObject data = resJson.getJSONObject("data");

    }
}
