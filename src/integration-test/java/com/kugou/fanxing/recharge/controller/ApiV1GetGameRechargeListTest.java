package com.kugou.fanxing.recharge.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kugou.fanxing.recharge.constant.UrlConstants;
import com.kugou.fanxing.recharge.util.JsonUtils;
import com.kugou.fanxing.tester.common.tools.HttpClientUtil;
import com.kugou.fanxing.tester.common.tools.UserUtil;
import org.junit.Assert;
import org.junit.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class ApiV1GetGameRechargeListTest {

    HttpClientUtil httpClientUtil = new HttpClientUtil();

    String host = "http://zuultest.fxwork.kugou.com/platform_recharge_service";
    String kugouId = "415770726";
    String userId = "39100745";

    public ApiV1GetGameRechargeListTest() throws IOException {
    }

    public Map<String,String> buildParams() throws Exception {
        Map<String,String> map = new HashMap<>();
        map.put("kugouId",kugouId);
        map.put("token",UserUtil.getTokenByDb(kugouId));
        map.put("appId","1131");
        map.put("index","0");
        map.put("page","1");
        map.put("pageSize","100");
        map.put("pass","");
        map.put("payTypeId","0");
        return map;
    }


    @Test
    public void alipayH5() throws Exception {
        Map<String,String> map = buildParams();
        String res = httpClientUtil.get(host + UrlConstants.GET_GAME_RECHARGE_LIST, map);
        JSONObject resJson = JSON.parseObject(res);
        Assert.assertTrue(resJson.get("code").equals(0));
    }

}
