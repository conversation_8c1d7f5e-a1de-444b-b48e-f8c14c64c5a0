package com.kugou.fanxing.recharge.testbase;


import com.kugou.rpc.client.http.CatRequestInterceptor;
import com.kugou.rpc.client.http.HttpRetryHandler;
import com.kugou.rpc.client.thrift.THttpClient;
import org.apache.http.HttpRequestInterceptor;
import org.apache.http.NameValuePair;
import org.apache.http.client.CookieStore;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.impl.cookie.BasicClientCookie;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.thrift.protocol.TBinaryProtocol;
import org.apache.thrift.protocol.TProtocol;
import org.apache.thrift.transport.TTransport;
import org.apache.thrift.transport.TTransportException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.net.URI;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;


public class TestUtil {


	private static Logger  logger = LoggerFactory.getLogger(TestUtil.class);

	public static String postParam(String url, Map<String, Object> paramMap) {
		try {
			StringBuilder sb = new StringBuilder();
			List<NameValuePair> nvps = new ArrayList<NameValuePair>();
			for (Entry<String, Object> e : paramMap.entrySet()) {
				sb.append(e.getKey());
				sb.append("=");
				sb.append(e.getValue());
				sb.append("&");
				nvps.add(new BasicNameValuePair(e.getKey(), e.getValue().toString()));
			}
			int len = sb.length();
			sb.delete(len - 1, len);

			String responseData = "";


			CloseableHttpClient httpclient = HttpClients.createDefault();
			HttpPost httpPost = new HttpPost(url);
			httpPost.setEntity(new UrlEncodedFormEntity(nvps, "utf-8"));

			CloseableHttpResponse response = httpclient.execute(httpPost);


			if (response.getStatusLine().getStatusCode() != 200) {
				throw new RuntimeException("请求" + url + " 失败:" + response.getStatusLine().getStatusCode());
			}
			responseData = EntityUtils.toString(response.getEntity(), "utf-8").trim();

			logger.error(responseData);

			return responseData;
		} catch (Exception e) {
			e.printStackTrace();
			System.exit(0);
		}
		return "";
	}


	public static String getParam(String url, Map<String, Object> parametersMap) {
		try {
			StringBuilder sb = new StringBuilder();
			for (Entry<String, Object> e : parametersMap.entrySet()) {
				sb.append(e.getKey());
				sb.append("=");
				sb.append(e.getValue());
				sb.append("&");
			}
			int len = sb.length();

			if(len>0)
			{
				sb.delete(len - 1, len);
			}


			String responseData = "";
			
			HttpClientContext context = new HttpClientContext();
			setLoginCookie(context,parametersMap);
			
			CloseableHttpClient httpclient = HttpClients.createDefault();


			url+="?"+sb.toString();
			logger.warn("请求URL: "+url);
			URL r_url = new URL(url);
			URI uri = new URI(r_url.getProtocol(),null, r_url.getHost(),r_url.getPort(), r_url.getPath(), r_url.getQuery(), null);
			HttpGet httpGet = new HttpGet(uri);
			CloseableHttpResponse response = httpclient.execute(httpGet,context);
			if (response.getStatusLine().getStatusCode() != 200) {
				throw new RuntimeException("请求" + url + " 失败:" + response.getStatusLine().getStatusCode());
			}
			responseData = EntityUtils.toString(response.getEntity(), "utf-8").trim();
			return responseData;

		} catch (Exception e) {
			e.printStackTrace();
			System.exit(0);
		}

		return "";
	}
	static String http_host = "test-fxservice.fxwork.kugou.com";

	private static void setLoginCookie(HttpClientContext context,Map<String, Object> parametersMap) throws Exception {
		parametersMap.put("appId", parametersMap.get("appid"));
		String token = (String)parametersMap.get("token");
		Object kugouId = (Object)parametersMap.get("fromKugouId");
		Integer appId = (Integer)parametersMap.get("appId");
		String KooGo=String.format("KugooID=%s&t=%s&&a_id=%s", kugouId,token,appId);
		CookieStore cookieStore = context.getCookieStore();
		if (cookieStore == null) {
			cookieStore = new BasicCookieStore();
		}
		BasicClientCookie cookie = new BasicClientCookie("KuGoo", KooGo);
		cookie.setDomain(http_host.replace(":17006", ""));
		cookie.setPath("/");
		cookieStore.addCookie(cookie);
		
		
		cookie = new BasicClientCookie("KuGoo", KooGo);
		KooGo=String.format("{\"kugouid\":\"%s\",\"token\":\"%s\"}", kugouId,token);
		cookie = new BasicClientCookie("KgTokenLogin", URLEncoder.encode(KooGo,"UTF-8"));
		cookie.setDomain(http_host.replace(":17006", ""));
		cookie.setPath("/");
		cookieStore.addCookie(cookie);
		
		context.setCookieStore(cookieStore);

	}

	
	
	public static String getParamWithoutCookies(String url, Map<String, Object> parametersMap) {
		try {
			StringBuilder sb = new StringBuilder();
			for (Entry<String, Object> e : parametersMap.entrySet()) {
				sb.append(e.getKey());
				sb.append("=");
				sb.append(e.getValue());
				sb.append("&");
			}
			int len = sb.length();

			if(len>0)
			{
				sb.delete(len - 1, len);
			}


			String responseData = "";
			
			HttpClientContext context = new HttpClientContext();
			
			CloseableHttpClient httpclient = HttpClients.createDefault();


			url+="?"+sb.toString();
			logger.warn("请求URL: "+url);
			URL r_url = new URL(url);
			URI uri = new URI(r_url.getProtocol(),null, r_url.getHost(),r_url.getPort(), r_url.getPath(), r_url.getQuery(), null);
			HttpGet httpGet = new HttpGet(uri);
			CloseableHttpResponse response = httpclient.execute(httpGet,context);
			if (response.getStatusLine().getStatusCode() != 200) {
				throw new RuntimeException("请求" + url + " 失败:" + response.getStatusLine().getStatusCode());
			}
			responseData = EntityUtils.toString(response.getEntity(), "utf-8").trim();
			return responseData;

		} catch (Exception e) {
			e.printStackTrace();
			System.exit(0);
		}

		return "";
	}

	public static HttpClient builderHttpClient(){
		PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
		connectionManager.setMaxTotal(128);
		connectionManager.setDefaultMaxPerRoute(128);

		int readTimeout = 3000;

		int connectTimeout = 1000;

		RequestConfig config = RequestConfig.custom().setConnectionRequestTimeout(1000).setConnectTimeout(connectTimeout).setSocketTimeout(readTimeout).build();

		HttpClientBuilder builder = HttpClients.custom().setConnectionManager(connectionManager).setDefaultRequestConfig(config);

		//重试处理类
		builder.setRetryHandler(new HttpRetryHandler()) ;
		//builder.setRetryHandler() ;

		//builder.setDefaultHeaders(defaultHeaders)

		HttpRequestInterceptor interceptor = new CatRequestInterceptor() ;
		builder.addInterceptorFirst(interceptor) ;

		return builder.build();
	}

	public static Object createClient(String url,Class<?> cls) throws TTransportException, NoSuchMethodException, SecurityException, InstantiationException, IllegalAccessException, IllegalArgumentException, InvocationTargetException {
		TTransport transport = new THttpClient(url,builderHttpClient());
		transport.open();
		TProtocol protocol = new TBinaryProtocol(transport);
		Constructor<?> constructor = cls.getConstructor(TProtocol.class);
		return constructor.newInstance(protocol);
	}


}
