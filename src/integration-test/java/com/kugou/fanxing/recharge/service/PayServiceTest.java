package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.Application;
import com.kugou.fanxing.recharge.model.vo.RechargeAcrossListVO;
import com.kugou.fanxing.recharge.util.Pagination;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class PayServiceTest {

    @Autowired
    private PayService payService;

    @Test
    public void testGetRechargeList() {
        Pagination pagination = new Pagination.Builder(1, 100).build();
        RechargeAcrossListVO rechargeAcrossListVO = payService.getRechargeList(*********, 0, 1006, pagination);
        log.warn("rechargeAcrossListVO: {}", rechargeAcrossListVO);

        pagination = new Pagination.Builder(0, 100).build();
        rechargeAcrossListVO = payService.getRechargeList(*********, 1, 1006, pagination);
        log.warn("rechargeAcrossListVO: {}", rechargeAcrossListVO);
    }

    @Test
    public void getActionIdByAccountChangeType() {
        Assert.assertEquals("297", this.payService.getActionIdByAccountChangeType(110223));
        Assert.assertEquals("300", this.payService.getActionIdByAccountChangeType(110224));
        Assert.assertEquals("348", this.payService.getActionIdByAccountChangeType(110285));
        Assert.assertEquals("349", this.payService.getActionIdByAccountChangeType(110286));
    }

    @Test(expected = ContextedRuntimeException.class)
    public void getActionIdByAccountChangeTypeException() {
        this.payService.getActionIdByAccountChangeType(-1);
    }

}
