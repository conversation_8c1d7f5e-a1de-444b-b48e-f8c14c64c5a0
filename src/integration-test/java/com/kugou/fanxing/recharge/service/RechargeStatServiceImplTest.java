package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.Application;
import com.kugou.fanxing.recharge.controller.thrift.RechargeStatServiceImpl;
import com.kugou.fanxing.recharge.thrift.UserEverRechargeRequest;
import com.kugou.fanxing.recharge.thrift.UserEverRechargeResponse;
import com.kugou.fanxing.recharge.thrift.UserYearRechargeStatRequest;
import com.kugou.fanxing.recharge.thrift.UserYearRechargeStatResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static com.kugou.fanxing.biz.commons.util.FinanceSignUtils.makeSign;


@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class RechargeStatServiceImplTest {
    String alt = "Do8J5LheobKBHDL8";

    @Autowired
    private RechargeStatServiceImpl rechargeStatService;
    private UserYearRechargeStatRequest buildUserYearRechargeStatRequest(){
        UserYearRechargeStatRequest userYearRechargeStatRequest = new UserYearRechargeStatRequest();
        userYearRechargeStatRequest.setKugouId(129);
        userYearRechargeStatRequest.setAppId(10000010);
        userYearRechargeStatRequest.setSign(makeSign(userYearRechargeStatRequest,alt));
        return userYearRechargeStatRequest;
    }

    private UserEverRechargeRequest buildUserEverRechargeRequest(){
        UserEverRechargeRequest userEverRechargeRequest = new UserEverRechargeRequest();
        userEverRechargeRequest.setKugouId(129);
        userEverRechargeRequest.setAppId(10000010);
        userEverRechargeRequest.setSign(makeSign(userEverRechargeRequest,alt));
        return userEverRechargeRequest;
    }

    @Test
    /**
     * 查询用户前一年的充值金额-正常查询
     */
    public void queryUserRechargeList(){
        UserYearRechargeStatResponse response = rechargeStatService.userYearRechargeStat(buildUserYearRechargeStatRequest());
        System.out.println(response);
    }

    @Test
    /**
     * 查询用户前一年的充值金额-sign错误
     * 20230214：服务功能已下线
     */
    public void queryUserRechargeList_sign(){
        UserYearRechargeStatRequest userYearRechargeStatRequest = buildUserYearRechargeStatRequest();
        userYearRechargeStatRequest.setSign("testSign");
        UserYearRechargeStatResponse response = rechargeStatService.userYearRechargeStat(userYearRechargeStatRequest);
        System.out.println(response);
        Assert.assertTrue(response.code == 100025);
    }


    @Test
    /**
     * 查询用户历史充值总额-正常查询
     */
    public void userEverRecharge(){
        UserEverRechargeRequest userEverRechargeRequest = buildUserEverRechargeRequest();
        UserEverRechargeResponse response = rechargeStatService.userEverRecharge(userEverRechargeRequest);
        System.out.println(response);
    }

    @Test
    /**
     * 查询用户历史充值总额-sign错误
     */
    public void userEverRecharge_sign(){
        UserEverRechargeRequest userEverRechargeRequest = buildUserEverRechargeRequest();
        userEverRechargeRequest.setSign("testSing");
        UserEverRechargeResponse response = rechargeStatService.userEverRecharge(userEverRechargeRequest);
        System.out.println(response);
    }


}