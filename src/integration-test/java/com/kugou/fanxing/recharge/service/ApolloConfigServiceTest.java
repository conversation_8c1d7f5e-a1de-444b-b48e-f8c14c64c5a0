package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.Application;
import com.kugou.fanxing.recharge.config.RechargeConfig;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.model.bo.KupayAppInfoBO;
import com.kugou.fanxing.recharge.model.bo.KupayAppTypeInfoBO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Optional;


@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class ApolloConfigServiceTest {

    @Autowired
    private ApolloConfigService apolloConfigService;
    @Autowired
    private RechargeConfig rechargeConfig;

    @Test
    public void appKeyOf_10000001() {
        String res = apolloConfigService.appKeyOf(10000001);
        Assert.assertEquals("q4Ya9B7MVq5CYBXs", res);
    }

    @Test
    public void appKeyOf_10000002() {
        String res = apolloConfigService.appKeyOf(10000002);
        Assert.assertEquals("49e6qxHDhmkfqDyU", res);
    }

    @Test
    public void getKupayAppTypeInfoBO402() {
        KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(PayTypeIdEnum.PAY_TYPE_ID_39);
        Optional<KupayAppTypeInfoBO> optionalKupayAppTypeInfoBO = this.rechargeConfig.getKupayAppTypeInfoBO(402);
        if (optionalKupayAppTypeInfoBO.isPresent()) {
            KupayAppTypeInfoBO kupayAppTypeInfoBO = optionalKupayAppTypeInfoBO.get();
            kupayAppInfoPO = rechargeConfig.getKupayAppIdByAppId(kupayAppTypeInfoBO.getKupayAppId());
        }
        Assert.assertEquals(10047, kupayAppInfoPO.getKupayAppId());
    }

    @Test
    public void getKupayAppTypeInfoBO403() {
        KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(PayTypeIdEnum.PAY_TYPE_ID_39);
        Optional<KupayAppTypeInfoBO> optionalKupayAppTypeInfoBO = this.rechargeConfig.getKupayAppTypeInfoBO(403);
        if (optionalKupayAppTypeInfoBO.isPresent()) {
            KupayAppTypeInfoBO kupayAppTypeInfoBO = optionalKupayAppTypeInfoBO.get();
            kupayAppInfoPO = rechargeConfig.getKupayAppIdByAppId(kupayAppTypeInfoBO.getKupayAppId());
        }
        Assert.assertEquals(10046, kupayAppInfoPO.getKupayAppId());
    }

    @Test
    public void getKupayAppTypeInfoB6() {
        KupayAppInfoBO kupayAppInfoPO = rechargeConfig.getKupayAppIdByPayType(PayTypeIdEnum.PAY_TYPE_ID_39);
        Optional<KupayAppTypeInfoBO> optionalKupayAppTypeInfoBO = this.rechargeConfig.getKupayAppTypeInfoBO(6);
        if (optionalKupayAppTypeInfoBO.isPresent()) {
            KupayAppTypeInfoBO kupayAppTypeInfoBO = optionalKupayAppTypeInfoBO.get();
            kupayAppInfoPO = rechargeConfig.getKupayAppIdByAppId(kupayAppTypeInfoBO.getKupayAppId());
        }
        Assert.assertEquals(1084, kupayAppInfoPO.getKupayAppId());
    }
}