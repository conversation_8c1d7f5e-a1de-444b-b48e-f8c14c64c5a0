package com.kugou.fanxing.recharge.service.appstore;

import com.kugou.fanxing.recharge.model.bo.ReceiptInfoBO;
import groovy.util.logging.Slf4j;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Optional;

@Slf4j
@Ignore
@SpringBootTest
@RunWith(SpringRunner.class)
public class AppStoreReceiptServiceTest {

    @Autowired
    private AppStoreReceiptService appStoreReceiptService;

    @Test
    public void verifyReceipt() {
        String receiptData = "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";
        Optional<ReceiptInfoBO> optionalReceiptInfoBO = this.appStoreReceiptService.verifyReceipt(receiptData);
        Assert.assertTrue(optionalReceiptInfoBO.isPresent());
    }

}
