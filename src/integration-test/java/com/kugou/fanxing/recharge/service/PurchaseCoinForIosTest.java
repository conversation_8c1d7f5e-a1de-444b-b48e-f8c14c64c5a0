package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.Application;
import com.kugou.fanxing.recharge.constant.SysResultCode;
import com.kugou.fanxing.recharge.controller.thrift.RechargeCallbackServiceImpl;
import com.kugou.fanxing.recharge.thrift.callback.CallbackResponse;
import com.kugou.fanxing.recharge.thrift.callback.PurchaseCoinForIosRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;



@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class PurchaseCoinForIosTest {
    @Autowired
    private RechargeCallbackServiceImpl rechargeCallbackService;

    @Autowired
    private RechargeOrderService rechargeOrderService;

    private PurchaseCoinForIosRequest buildPurchaseCoinForIosRequest(){
        PurchaseCoinForIosRequest purchaseCoinForIosRequest = new PurchaseCoinForIosRequest();
        purchaseCoinForIosRequest.setSignBook("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");
        purchaseCoinForIosRequest.setTradeNo("1000000756956362");
        purchaseCoinForIosRequest.setGoodsId("com.fanxing.fxappstore.66coin");
        purchaseCoinForIosRequest.setBusinessExt("");
        purchaseCoinForIosRequest.setOrderNum("TID1000000756956362");
        purchaseCoinForIosRequest.setPid(2);
        purchaseCoinForIosRequest.setType(1);
        purchaseCoinForIosRequest.setVersion("49929");
        purchaseCoinForIosRequest.setTradeTime("2020-12-21 15:36:13");
        purchaseCoinForIosRequest.setMoney("1");
        purchaseCoinForIosRequest.setPartner("");
        purchaseCoinForIosRequest.setTime(1608536400);
        purchaseCoinForIosRequest.setIsSandbox(1);
        purchaseCoinForIosRequest.setKey("ad3ccfb4e329766b852cee37d23eb734");
        purchaseCoinForIosRequest.setChannelId(1009);
        purchaseCoinForIosRequest.setKugouId(1290249156);
        purchaseCoinForIosRequest.setCoin("66");
        return purchaseCoinForIosRequest;
    }

    @Test
    public void purchaseCoinForIos_null() {
        /*
        请求为空,orderNum不存在
         */
        PurchaseCoinForIosRequest purchaseCoinForIosRequest = new PurchaseCoinForIosRequest();
        purchaseCoinForIosRequest.setOrderNum("TID1000000756956362");
        CallbackResponse callbackResponse = rechargeCallbackService.purchaseCoinForIos(purchaseCoinForIosRequest);
        Assert.assertEquals(callbackResponse.code, SysResultCode.RECHARGE_PARAM_ERROR.getCode());
    }
    @Test
    public void purchaseCoinForIos_key() {
        /*
        key为空
         */
        PurchaseCoinForIosRequest purchaseCoinForIosRequest = buildPurchaseCoinForIosRequest();
        purchaseCoinForIosRequest.setKey("");
        CallbackResponse callbackResponse = rechargeCallbackService.purchaseCoinForIos(purchaseCoinForIosRequest);
        Assert.assertEquals(SysResultCode.RECHARGE_PARAM_ERROR.getCode(),callbackResponse.code);
    }

    @Test
    public void purchaseCoinForIos_kugouId() {
        /*
        kugouId=0
         */
        PurchaseCoinForIosRequest purchaseCoinForIosRequest = buildPurchaseCoinForIosRequest();
        purchaseCoinForIosRequest.setKugouId(0);
        CallbackResponse callbackResponse = rechargeCallbackService.purchaseCoinForIos(purchaseCoinForIosRequest);
        Assert.assertEquals(SysResultCode.RECHARGE_PARAM_ERROR.getCode(),callbackResponse.code);
    }

    @Test
    public void purchaseCoinForIos_kugouIdNotExits() {
        /*
        繁星用户ID不存在
         */
        PurchaseCoinForIosRequest purchaseCoinForIosRequest = buildPurchaseCoinForIosRequest();
        purchaseCoinForIosRequest.setKugouId(999999999);
        CallbackResponse callbackResponse = rechargeCallbackService.purchaseCoinForIos(purchaseCoinForIosRequest);
        Assert.assertEquals(SysResultCode.RECHARGE_PARAM_ERROR.getCode(),callbackResponse.code);
    }

    @Test
    public void purchaseCoinForIos_keyNotEqual() {
        /*
        key不匹配
         */
        PurchaseCoinForIosRequest purchaseCoinForIosRequest = buildPurchaseCoinForIosRequest();
        purchaseCoinForIosRequest.setKey("test_ad3ccfb4e329766b852cee37d23eb734");
        CallbackResponse callbackResponse = rechargeCallbackService.purchaseCoinForIos(purchaseCoinForIosRequest);
        Assert.assertEquals(SysResultCode.RECHARGE_PARAM_ERROR.getCode(),callbackResponse.code);
    }

    @Test
    @Ignore
    public void purchaseCoinForIos() {
        /*
        回调成功
         */
        PurchaseCoinForIosRequest purchaseCoinForIosRequest = buildPurchaseCoinForIosRequest();
        CallbackResponse callbackResponse = rechargeCallbackService.purchaseCoinForIos(purchaseCoinForIosRequest);
        Assert.assertEquals(SysResultCode.SUCCESS.getCode(),callbackResponse.code);
    }
}
