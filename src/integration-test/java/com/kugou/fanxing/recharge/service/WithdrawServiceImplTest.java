package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.Application;
import com.kugou.fanxing.recharge.controller.thrift.WithdrawServiceImpl;
import com.kugou.fanxing.recharge.withdraw.thrift.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static com.kugou.fanxing.biz.commons.util.FinanceSignUtils.makeSign;


@Slf4j
@Ignore
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class WithdrawServiceImplTest {
    String alt = "Do8J5LheobKBHDL8";

    @Autowired
    private WithdrawServiceImpl withdrawServiceImpl;
    private AccountChangeRequest buildAccountChangeRequest(){
        AccountChangeRequest accountChangeRequest = new AccountChangeRequest();
        accountChangeRequest.setOrderId(System.currentTimeMillis());
        accountChangeRequest.setAppId(********);
        accountChangeRequest.setKugouId(*********L);
        accountChangeRequest.setAccount("100");
        accountChangeRequest.setRealName("testName");
        accountChangeRequest.setToken("testToken");
        accountChangeRequest.setRequestTime((int) System.currentTimeMillis());
        accountChangeRequest.setIp("127.0.0.1");
        accountChangeRequest.setSign(makeSign(accountChangeRequest,alt));
        return accountChangeRequest;
    }

    private CreateWithdrawOrderRequest buildCreateWithdrawOrderRequest(){
        CreateWithdrawOrderRequest createWithdrawOrderRequest = new CreateWithdrawOrderRequest();
        createWithdrawOrderRequest.setOrderId(System.currentTimeMillis());
        createWithdrawOrderRequest.setAppId(********);
        createWithdrawOrderRequest.setBizAppId(1);
        createWithdrawOrderRequest.setPid(1);
        createWithdrawOrderRequest.setKugouId(*********);
        createWithdrawOrderRequest.setReqTime((int) System.currentTimeMillis());
        createWithdrawOrderRequest.setDrawTime((int) System.currentTimeMillis());
        createWithdrawOrderRequest.setTotalAmount(100);
        createWithdrawOrderRequest.setApplicationId("1");
        createWithdrawOrderRequest.setClientver("111");
        createWithdrawOrderRequest.setMid("test");
        createWithdrawOrderRequest.setUuid("test");
        createWithdrawOrderRequest.setDfid("test");
        createWithdrawOrderRequest.setToken("test");
        createWithdrawOrderRequest.setClientIp("127.0.0.1");
        createWithdrawOrderRequest.setSign(makeSign(createWithdrawOrderRequest,alt));
        return createWithdrawOrderRequest;
    }

    private CreateWithdrawOrderV2Request buildCreateWithdrawOrderV2Request(){
        CreateWithdrawOrderV2Request createWithdrawOrderV2Request = new CreateWithdrawOrderV2Request();
        createWithdrawOrderV2Request.setOrderId(System.currentTimeMillis());
        createWithdrawOrderV2Request.setAppId(********);
        createWithdrawOrderV2Request.setBizAppId(1);
        createWithdrawOrderV2Request.setPid(1);
        createWithdrawOrderV2Request.setKugouId(*********);
        createWithdrawOrderV2Request.setReqTime((int) System.currentTimeMillis());
        createWithdrawOrderV2Request.setDrawTime((int) System.currentTimeMillis());
        createWithdrawOrderV2Request.setTotalAmount(100);
        createWithdrawOrderV2Request.setApplicationId("1");
        createWithdrawOrderV2Request.setClientver("111");
        createWithdrawOrderV2Request.setMid("test");
        createWithdrawOrderV2Request.setUuid("test");
        createWithdrawOrderV2Request.setDfid("test");
        createWithdrawOrderV2Request.setToken("test");
        createWithdrawOrderV2Request.setClientIp("127.0.0.1");
        createWithdrawOrderV2Request.setSign(makeSign(createWithdrawOrderV2Request,alt));
        return createWithdrawOrderV2Request;
    }

    private AccountChangeV2Request buildAccountChangeV2Request(){
        AccountChangeV2Request accountChangeV2Request = new AccountChangeV2Request();
        accountChangeV2Request.setOrderId(System.currentTimeMillis());
        accountChangeV2Request.setAppId(********);
        accountChangeV2Request.setBizAppId(1);
        accountChangeV2Request.setKugouId(*********);
        accountChangeV2Request.setSign(makeSign(accountChangeV2Request,alt));
        return accountChangeV2Request;
    }

    private QueryWithdrawKugouIdRequest buildQueryWithdrawKugouIdRequest(){
        QueryWithdrawKugouIdRequest queryWithdrawKugouIdRequest = new QueryWithdrawKugouIdRequest();
        queryWithdrawKugouIdRequest.setAccount("100");
        queryWithdrawKugouIdRequest.setAppId(********);
        queryWithdrawKugouIdRequest.setReqTime((int) System.currentTimeMillis());
        queryWithdrawKugouIdRequest.setSign(makeSign(queryWithdrawKugouIdRequest,alt));
        return queryWithdrawKugouIdRequest;
    }

    private CancelWithdrawOrderRequest buildCancelWithdrawOrderRequest(){
        CancelWithdrawOrderRequest cancelWithdrawOrderRequest = new CancelWithdrawOrderRequest();
        cancelWithdrawOrderRequest.setOrderId(System.currentTimeMillis());
        cancelWithdrawOrderRequest.setAppId(********);
        cancelWithdrawOrderRequest.setKugouId(*********);
        cancelWithdrawOrderRequest.setReqTime((int) System.currentTimeMillis());
        cancelWithdrawOrderRequest.setSign(makeSign(cancelWithdrawOrderRequest,alt));
        return cancelWithdrawOrderRequest;
    }

    private QueryWithdrawOrderRequest buildQueryWithdrawOrderRequest(){
        QueryWithdrawOrderRequest queryWithdrawOrderRequest = new QueryWithdrawOrderRequest();
        queryWithdrawOrderRequest.setOrderId(System.currentTimeMillis());
        queryWithdrawOrderRequest.setAppId(********);
        queryWithdrawOrderRequest.setKugouId(*********);
        queryWithdrawOrderRequest.setReqTime((int) System.currentTimeMillis());
        queryWithdrawOrderRequest.setSign(makeSign(queryWithdrawOrderRequest,alt));
        return queryWithdrawOrderRequest;
    }



    @Test
    /**
     * 绑定提现账号信息-提现实名信息与实名认证不符
     */
    public void queryUserRechargeList(){
        AccountChangeRequest accountChangeRequest = buildAccountChangeRequest();
        WithdrawResult response = withdrawServiceImpl.bindAccountInfo(accountChangeRequest);
        System.out.println(response);
        Assert.assertTrue(response.code == ********);
    }

    @Test
    /**
     * 绑定提现账号信息-sign不通过
     */
    public void queryUserRechargeList_sign(){
        AccountChangeRequest accountChangeRequest = buildAccountChangeRequest();
        accountChangeRequest.setSign("testSing");
        WithdrawResult response = withdrawServiceImpl.bindAccountInfo(accountChangeRequest);
        System.out.println(response);
        Assert.assertTrue(response.code == 100009);
    }

    @Test
    /**
     * 创建提现订单-提现订单业务未注册
     */
    public void createWithdrawOrder(){
        CreateWithdrawOrderRequest createWithdrawOrderRequest = buildCreateWithdrawOrderRequest();
        WithdrawResult response = withdrawServiceImpl.createWithdrawOrder(createWithdrawOrderRequest);
        System.out.println(response);
        Assert.assertTrue(response.code == ********);
    }

    @Test
    /**
     * 创建提现订单-sign不通过
     */
    public void createWithdrawOrder_sign(){
        CreateWithdrawOrderRequest createWithdrawOrderRequest = buildCreateWithdrawOrderRequest();
        createWithdrawOrderRequest.setSign("testSing");
        WithdrawResult response = withdrawServiceImpl.createWithdrawOrder(createWithdrawOrderRequest);
        System.out.println(response);
        Assert.assertTrue(response.code == 100009);
    }

    @Test
    /**
     * 查询提现订单
     */
    public void queryWithdrawOrder(){
        QueryWithdrawOrderRequest queryWithdrawOrderRequest = buildQueryWithdrawOrderRequest();
        WithdrawOrderResult response = withdrawServiceImpl.queryWithdrawOrder(queryWithdrawOrderRequest);
        System.out.println(response);
        Assert.assertTrue(response.code == 0);
    }

    @Test
    /**
     * 查询提现订单-sign不通过
     */
    public void queryWithdrawOrder_sign(){
        QueryWithdrawOrderRequest queryWithdrawOrderRequest = buildQueryWithdrawOrderRequest();
        queryWithdrawOrderRequest.setSign("testSing");
        WithdrawOrderResult response = withdrawServiceImpl.queryWithdrawOrder(queryWithdrawOrderRequest);
        System.out.println(response);
        Assert.assertTrue(response.code == 100009);
    }

    @Test
    /**
     * 取消提现订单-请求参数不合法
     */
    public void cancelWithdrawOrder(){
        CancelWithdrawOrderRequest cancelWithdrawOrderRequest = buildCancelWithdrawOrderRequest();
        WithdrawResult response = withdrawServiceImpl.cancelWithdrawOrder(cancelWithdrawOrderRequest);
        System.out.println(response);
        Assert.assertTrue(response.code == 100001);
    }

    @Test
    /**
     * 取消提现订单-sign不通过
     */
    public void cancelWithdrawOrder_sign(){
        CancelWithdrawOrderRequest cancelWithdrawOrderRequest = buildCancelWithdrawOrderRequest();
        cancelWithdrawOrderRequest.setSign("testSign");
        WithdrawResult response = withdrawServiceImpl.cancelWithdrawOrder(cancelWithdrawOrderRequest);
        System.out.println(response);
        Assert.assertTrue(response.code == 100009);
    }


    @Test
    /**
     * 根据支付宝账号反查酷狗ID-请求参数不合法
     */
    public void getKugouIdByAliPayAccount(){
        QueryWithdrawKugouIdRequest queryWithdrawKugouIdRequest = buildQueryWithdrawKugouIdRequest();
        QueryWithdrawKugouIdResult response = withdrawServiceImpl.getKugouIdByAliPayAccount(queryWithdrawKugouIdRequest);
        System.out.println(response);
        Assert.assertTrue(response.code == 0);
    }

    @Test
    /**
     * 根据支付宝账号反查酷狗ID-sign不通过
     */
    public void getKugouIdByAliPayAccount_sign(){
        QueryWithdrawKugouIdRequest queryWithdrawKugouIdRequest = buildQueryWithdrawKugouIdRequest();
        queryWithdrawKugouIdRequest.setSign("tsetSign");
        QueryWithdrawKugouIdResult response = withdrawServiceImpl.getKugouIdByAliPayAccount(queryWithdrawKugouIdRequest);
        System.out.println(response);
        Assert.assertTrue(response.code == 100009);
    }

    @Test
    /**
     * 绑定提现账号信息-
     */
    public void bindAccountInfoV2(){
        AccountChangeV2Request accountChangeV2Request = buildAccountChangeV2Request();
        WithdrawResult response = withdrawServiceImpl.bindAccountInfoV2(accountChangeV2Request);
        System.out.println(response);
        Assert.assertTrue(response.code == 100001);
    }

    @Test
    /**
     * 绑定提现账号信息-sign不通过
     */
    public void bindAccountInfoV2_sign(){
        AccountChangeV2Request accountChangeV2Request = buildAccountChangeV2Request();
        accountChangeV2Request.setSign("tsetSign");
        WithdrawResult response = withdrawServiceImpl.bindAccountInfoV2(accountChangeV2Request);
        System.out.println(response);
        Assert.assertTrue(response.code == 100009);
    }

    @Test
    /**
     * 绑定提现账号信息-提现订单业务未注册
     */
    public void createWithdrawOrderV2(){
        CreateWithdrawOrderV2Request createWithdrawOrderV2Request = buildCreateWithdrawOrderV2Request();
        WithdrawResult response = withdrawServiceImpl.createWithdrawOrderV2(createWithdrawOrderV2Request);
        System.out.println(response);
        Assert.assertTrue(response.code == ********);
    }

    @Test
    /**
     * 绑定提现账号信息-sign不通过
     */
    public void createWithdrawOrderV2_sign(){
        CreateWithdrawOrderV2Request createWithdrawOrderV2Request = buildCreateWithdrawOrderV2Request();
        createWithdrawOrderV2Request.setSign("tsetSign");
        WithdrawResult response = withdrawServiceImpl.createWithdrawOrderV2(createWithdrawOrderV2Request);
        System.out.println(response);
        Assert.assertTrue(response.code == 100009);
    }


}