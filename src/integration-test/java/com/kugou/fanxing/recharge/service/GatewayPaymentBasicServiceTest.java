package com.kugou.fanxing.recharge.service;

import com.kugou.fanxing.recharge.Application;
import com.kugou.fanxing.recharge.model.vo.GatewayPayCallbackParam;
import com.kugou.fanxing.recharge.model.vo.GatewayPayRecordVo;
import com.kugou.fanxing.recharge.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class GatewayPaymentBasicServiceTest {

    @Autowired
    private GatewayPaymentBasicService gatewayPaymentBasicService;

    @Test
    public void testGetOrderByNo() {
        GatewayPayCallbackParam jsonObject = gatewayPaymentBasicService.getOrderByNo("R292020030121094629302328");
        log.warn("data: {}", jsonObject);
    }

    @Test
    public void testGetSuccessOrders() {
        long startTime = DateHelper.getCurrentSeconds() - 86400;
        long endTime = startTime + 60;
        List<GatewayPayRecordVo> successRecordVoList = gatewayPaymentBasicService.getSuccessOrders(startTime, endTime);
        log.warn("data: {}", successRecordVoList.toString());
    }

}
