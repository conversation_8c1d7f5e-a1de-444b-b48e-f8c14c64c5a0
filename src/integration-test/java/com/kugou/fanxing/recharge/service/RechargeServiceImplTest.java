package com.kugou.fanxing.recharge.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.kugou.fanxing.recharge.Application;
import com.kugou.fanxing.recharge.constant.PayTypeIdEnum;
import com.kugou.fanxing.recharge.controller.thrift.RechargeServiceImpl;
import com.kugou.fanxing.recharge.thrift.*;
import com.kugou.fanxing.recharge.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.Map;

import static com.kugou.fanxing.biz.commons.util.FinanceSignUtils.makeSign;


@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class RechargeServiceImplTest {
    String alt = "q4Ya9B7MVq5CYBXs";

    @Autowired
    private RechargeServiceImpl rechargeService;


    private QueryUserRechargeListRequest buildQueryUserRechargeListRequest(){
        QueryUserRechargeListRequest queryUserRechargeListRequest = new QueryUserRechargeListRequest();
        queryUserRechargeListRequest.setKugouId(415770726);
        queryUserRechargeListRequest.setMonth("202301");
        queryUserRechargeListRequest.setPage(1);
        queryUserRechargeListRequest.setPageSize(100);
        queryUserRechargeListRequest.setPid(5);
        queryUserRechargeListRequest.setAppId(********);
        queryUserRechargeListRequest.setTimestamp((int) System.currentTimeMillis());
        queryUserRechargeListRequest.setSign(makeSign(queryUserRechargeListRequest,alt));
        return queryUserRechargeListRequest;
    }

    private PurchaseProductRequestV2 buildPurchaseProductRequestV2(){
        PurchaseOrder purchaseOrder = new PurchaseOrder();
        purchaseOrder.setAccountChangeType(110403);
        purchaseOrder.setFromKugouId(1290249156L);
        purchaseOrder.setToKugouId(1290249156L);
        purchaseOrder.setGoodsId(0);
        purchaseOrder.setGoodsNum(1);
        purchaseOrder.setGoodsType(0);
        purchaseOrder.setExt("");
        Map<String, Object> extJsonMap = ImmutableMap.<String, Object>builder()
                .put("bizType", "1")
                .put("activityType", 110403)
                .put("roomId", 1020728)
                .put("bizNotifyUrl", "http://zuultest.fxwork.kugou.com/revenue_new_trial/revenue_new_trial/inner/recharge/renewbag/notify")
                .build();
        PurchaseProductRequestV2 request = new PurchaseProductRequestV2()
                .setBusinessId("********")
                .setBusinessTime(DateHelper.getCurrentSeconds())
                .setKugouId(1290249156L)
                .setSubject("购买续充礼包")
                .setAmount(new BigDecimal("0.01").stripTrailingZeros().toPlainString())
                .setClientIp("127.0.0.1")
                .setPid(7)
                .setOpenId("")
                .setExtJson(JSON.toJSONString(extJsonMap))
                .setOrderList(Lists.newArrayList(purchaseOrder))
                .setRedirectUrl("https://fx100.fxwork.kugou.com/")
                .setShowUrl("https://fx100.fxwork.kugou.com/")
                .setPayTypeId(PayTypeIdEnum.PAY_TYPE_ID_3.getPayTypeId());
        return request;
    }

    @Test
    @Ignore
    /**
     * 正常查询
     */
    public void queryUserRechargeList(){
        /*
        查询用户充值列表
         */
        QueryUserRechargeListResponse response = rechargeService.queryUserRechargeList(buildQueryUserRechargeListRequest());
        System.out.println(response);
    }

    @Test
    /**
     * 查询用户充值列表-sign错误
     */
    public void queryUserRechargeList_sign(){
        QueryUserRechargeListRequest queryUserRechargeListRequest = buildQueryUserRechargeListRequest();
        queryUserRechargeListRequest.setSign("test_sign");
        QueryUserRechargeListResponse response = rechargeService.queryUserRechargeList(queryUserRechargeListRequest);
        System.out.println(response);
        Assert.assertTrue(response.code == 100009);
    }


    @Test
    /**
     * 查询用户充值列表-kugouId<1
     */
    public void queryUserRechargeList_kugouId(){
        /*
        查询用户充值列表-sign错误
         */
        QueryUserRechargeListRequest queryUserRechargeListRequest = buildQueryUserRechargeListRequest();
        queryUserRechargeListRequest.setKugouId(0);
        QueryUserRechargeListResponse response = rechargeService.queryUserRechargeList(queryUserRechargeListRequest);
        System.out.println(response);
        Assert.assertTrue(response.code == 100001);
    }

    @Test
    /**
     * 查询用户充值列表-月份不合法
     */
    public void queryUserRechargeList_month(){
        /*
        查询用户充值列表-sign错误
         */
        QueryUserRechargeListRequest queryUserRechargeListRequest = buildQueryUserRechargeListRequest();
        queryUserRechargeListRequest.setMonth("testMonth");
        QueryUserRechargeListResponse response = rechargeService.queryUserRechargeList(queryUserRechargeListRequest);
        System.out.println(response);
        Assert.assertTrue(response.code == 100001);
    }

    @Test
    /**
     * 不传orderExpireTime，默认orderExpireTime = 0
     * kupaysParam:expire_time -> 20230331193427,取当前时间
     */
    public void createOrderForPurchaseProductV2_1(){
        PurchaseProductRequestV2 purchaseProductRequestV2 = buildPurchaseProductRequestV2();
        purchaseProductRequestV2.setSyncUrl("https://bing.com");
        PurchaseProductResponse response = rechargeService.createOrderForPurchaseProductV2(purchaseProductRequestV2);
        System.out.println(response);
        Assert.assertTrue(response.code == 0);
    }

    @Test
    /**
     * 传orderExpireTime，传一天前的时间 System.currentTimeMillis()/1000 - (60 * 60 * 24)
     * kupaysParam:expire_time -> 20230330194220
     */
    public void createOrderForPurchaseProductV2_2(){
        PurchaseProductRequestV2 purchaseProductRequestV2 = buildPurchaseProductRequestV2();
        purchaseProductRequestV2.setOrderExpireTime(System.currentTimeMillis()/1000 - (60 * 60 * 24));
        purchaseProductRequestV2.setSyncUrl("https://bing.com");
        PurchaseProductResponse response = rechargeService.createOrderForPurchaseProductV2(purchaseProductRequestV2);
        System.out.println(response);
        Assert.assertTrue(response.code == 0);
    }

    @Test
    /**
     * 不传SyncUrl
     */
    public void createOrderForPurchaseProductV2_3(){
        PurchaseProductRequestV2 purchaseProductRequestV2 = buildPurchaseProductRequestV2();
        PurchaseProductResponse response = rechargeService.createOrderForPurchaseProductV2(purchaseProductRequestV2);
        System.out.println(response);
        Assert.assertTrue(response.code == 0);
    }

    @Test
    /**
     * SyncUrl = ""
     */
    public void createOrderForPurchaseProductV2_4(){
        PurchaseProductRequestV2 purchaseProductRequestV2 = buildPurchaseProductRequestV2();
        purchaseProductRequestV2.setSyncUrl("");
        PurchaseProductResponse response = rechargeService.createOrderForPurchaseProductV2(purchaseProductRequestV2);
        System.out.println(response);
        Assert.assertTrue(response.code == 0);
    }
}
