namespace go kgthrift.service
namespace php SocketService
namespace java com.kugou.fanxing.thrift.acksocket.gather.service

include "./type_logic.thrift"
include "./type_logic_batch.thrift"

/**
* JSON协议推送服务
*
* 服务发现配置：
* appDispatchServiceV2.applicationName = platform_socket_message
* appDispatchServiceV2.protocol = thrift
* appDispatchServiceV2.className = com.kugou.fanxing.thrift.acksocket.gather.service.AppDispatchServiceV2
* appDispatchServiceV2.url = /socket/v2/message.thrift
**/
service AppDispatchServiceV2 {

    /**
    * 特殊通道
    * 推送消息到指定房间ID
    **/
    type_logic.BoolResponse sendToRoomWithSpecialChannel(1:i32 cmdId, 2:i32 roomId, 3:binary content, 4:type_logic.MsgOpt msgOpt),
    /**
    * 特殊通道
    * 推送消息到指定房间ID、指定酷狗ID
    **/
    type_logic.BoolResponse sendToUserWithSpecialChannel(1:i32 cmdId, 2:i32 roomId, 3:i64 toKid, 4:binary content, 5:type_logic.MsgOpt msgOpt),
    /**
    * 特殊通道
    * 推送消息到批量房间ID
    **/
    type_logic.BoolResponse sendToMultiRoomWithSpecialChannel(1:i32 cmdId,2:list<i32> roomIds,3:binary content,4:type_logic.MsgOpt msgOpt),
    /**
    * 特殊通道
    * 大部分情况下，调用方不需要调用该接口，如有需要请联系服务提供方
    * 推送消息到指定房间ID，附加用户EXT信息
    **/
    type_logic.BoolResponse sendToRoomFromUserV2WithSpecialChannel(1:i32 cmdId, 2:i32 roomId, 3:type_logic.FromUserV2 fromUser, 4:binary content, 5:type_logic.MsgOpt msgOpt),
    /**
    * 特殊通道
    * 大部分情况下，调用方不需要调用该接口，如有需要请联系服务提供方
    * 推送消息到指定房间ID、指定酷狗ID，附加用户EXT信息
    **/
    type_logic.BoolResponse sendToUserFromUserV2WithSpecialChannel(1:i32 cmdId, 2:i32 roomId, 3:i64 toKid, 4:type_logic.FromUserV2 fromUser, 5:binary content, 6:type_logic.MsgOpt msgOpt),
    /**
    * 推送消息到全部直播间
    **/
	type_logic.BoolResponse sendToAll(1:i32 bcId, 2:string bcKey, 3:i32 cmdId, 4:binary content, 5:type_logic.MsgOption msgOption),
	/**
	* 推送消息到指定房间ID
    **/
	type_logic.BoolResponse sendToRoom(1:i32 roomId, 2:i32 cmdId, 3:binary content, 4:type_logic.MsgOption msgOption),
	/**
	* 推送消息到指定房间ID、指定酷狗ID
    **/
	type_logic.BoolResponse sendToUser(1:i32 roomId, 2:i64 toKid, 3:i32 cmdId, 4:binary content, 5:type_logic.MsgOption msgOption),
    /**
    * 大部分情况下，调用方不需要调用该接口，如有需要请联系服务提供方
    * 推送消息到全部直播间，附加用户EXT信息
    **/
    type_logic.BoolResponse sendToAllFromUserV2(1:i32 bcId, 2:string bcKey, 3:i32 cmdId, 4:binary content, 5:type_logic.FromUserV2 fromUser, 6:type_logic.MsgOption msgOption),
    /**
    * 大部分情况下，调用方不需要调用该接口，如有需要请联系服务提供方
    * 推送消息到指定房间ID，附加用户EXT信息
    **/
    type_logic.BoolResponse sendToRoomFromUserV2(1:i32 roomId, 2:i32 cmdId, 3:binary content, 4:type_logic.FromUserV2 fromUser, 5:type_logic.MsgOption msgOption),
    /**
    * 大部分情况下，调用方不需要调用该接口，如有需要请联系服务提供方
    * 推送消息到指定房间ID、指定酷狗ID，附加用户EXT信息
    **/
    type_logic.BoolResponse sendToUserFromUserV2(1:i32 roomId, 2:i64 toKid, 3:i32 cmdId, 4:binary content, 5:type_logic.FromUserV2 fromUser, 6:type_logic.MsgOption msgOption),

    /**
    * 特殊通道
    *  批量 推送消息给指定kugouId（该用户在连接的任意房间均可收到）
    **/
    type_logic.BoolResponse sendToUserByKidBatchWithSpecialChannel(1:list<type_logic.BatchMessageSpecial> batchMessages),

    type_logic.BoolResponse batchSendToRoomMessage(1:type_logic_batch.LogicBatchRoomMessageRequest request),
    type_logic.BoolResponse batchSendToUserMessage(1:type_logic_batch.LogicBatchUserMessageRequest request),
    type_logic.BoolResponse batchSendToUserMessageWithoutRoomId(1:type_logic_batch.LogicBatchUserMessageWithoutRoomIdRequest request),
}