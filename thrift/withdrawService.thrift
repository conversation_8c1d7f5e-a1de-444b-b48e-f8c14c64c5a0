namespace java com.kugou.fanxing.recharge.withdraw.thrift

// 查询参数
struct WithdrawAccountQueryRequest {
	1:required i64 kugouId,             // 酷狗ID
	2:required i32 appId,               // 业务ID
	3:required i32 reqTime              // 请求时间
}

// 查询结果
struct AccountQueryResult{
    1:i32 code;                         // 返回码
    2:string msg;                       // 描述
    3:optional WithdrawAccountDTO data, // 提现订单
}

// 查询参数
struct WechatAccountQueryRequest {
	1:required i64 kugouId,             // 酷狗ID
	2:required i32 appId,               // 业务ID
    3:required i32 bizAppId,            // 网关业务ID
	4:required i32 reqTime              // 请求时间
}

// 查询结果
struct WechatAccountQueryResult{
    1:i32 code;                             // 返回码
    2:string msg;                           // 描述
    3:optional WithdrawAccountWechatDTO data,   // 提现订单
}

struct WithdrawAccountWechatDTO{
    1:i64 kugouId;                      // 酷狗ID
    2:string account;                   // 支付宝提现账号
    3:string realName;                  // 提现实名
    4:string openid;                    // 微信提现openid
    5:string nickname;                  // 微信提现昵称
    6:string avatar;                    // 微信提现头像
    7:i32 createTime;                   // 创建时间
}

struct WithdrawAccountDTO{
    1:i64 kugouId;                      // 酷狗ID
    2:i32 accountType;                  // 账户类型: 1 支付宝账号
    3:string account;                   // 提现账号
    4:string realName;                  // 提现实名
    5:i32 createTime;                   // 创建时间
}

// 绑定账户信息请求
struct AccountChangeRequest{
    1:required i64 orderId,             // 请求ID
    2:required i32 appId,               // 业务ID
    3:required i64 kugouId,             // 酷狗ID
    4:required string account,          // 支付宝账户
    5:required string realName,         // 真实姓名
    6:required string token,            // 用户token
    7:required i32 requestTime,         // 请求时间戳（单位：秒）
    8:required string ip,               // 客户端IP
    9:required string sign              // 签名
}

// 返回结果类
struct WithdrawResult{
    1:i32 code;                         // 返回码 0=成功
    2:string msg;                       // 错误描述
    3:string data;                      // 描述信息
}

// 创建提现请求
struct CreateWithdrawOrderRequest {
    1:required i64 orderId,             // 每次请求的ID，确保唯一
    2:required i32 appId,               // 直播业务ID，需申请
    3:required i32 bizAppId,            // 网关业务ID，需申请
    4:required i32 pid,                 // 平台ID
    5:required i64 kugouId,             // 酷狗ID
    6:required i32 reqTime,             // 请求时间
    7:required i32 drawTime,            // 提款时间，单位秒
    8:required i64 totalAmount,         // 总金额，单位为分
    9:required string applicationId,    // 终端应用编号
    10:required string clientver,       // 客户端版本号
    11:required string mid,             // 机器唯一码
    12:required string uuid,            // 客户端设备唯一标识
    13:required string dfid,            // 设备指纹ID，如取失败，填'-'
    14:required string token,           // 当前登录的用户令牌
    15:required string clientIp,        // 客户端ip
    16:required string sign             // 签名
}

// 取消提现请求
struct CancelWithdrawOrderRequest {
    1:required i32 appId,               // 业务ID
    2:required i64 kugouId,             // 酷狗ID
    3:required i64 reqTime,             // 业务请求时间（秒）
    4:required i64 orderId,             // 业务订单号
    5:required string reason,           // 取消原因
    6:required string sign              // 签名
}

// 查询提现请求
struct QueryWithdrawOrderRequest {
    1:required i32 appId,               // 业务ID
    2:required i64 kugouId,             // 酷狗ID
    3:required i64 reqTime,             // 请求时间
    4:required i64 orderId,             // 当前页
    5:required string sign              // 签名
}

// 查询提现请求响应
struct WithdrawOrderResult{
    1:i32 code;                         // 返回码
    2:string msg;                       // 描述
    3:optional WithdrawOrderDTO data;   // 提现记录
}

struct WithdrawOrderDTO {
    1:required i64 orderId,             // 业务订单号
    2:required string outTradeNo,       // 第三方交易号
    3:required string tradeNo,          // 网关交易号
    4:required i32 appId,               // 直播业务编码
    5:required i32 bizAppId,            // 网关业务编码
    6:required i64 kugouId,             // 酷狗ID
    7:required i32 status,              // 提现状态
    8:required i32 reqTime,             // 提现发起时间（单位：秒）
    9:required i32 drawTime,            // 提现到账时间（单位：秒）
    10:required string errorCode,       // 提现错误码
    11:required string errorMsg,        // 提现错误信息
    12:required string errorReason      // 第三方错误信息
}

struct QueryWithdrawKugouIdRequest{
    1:required string account,          // 支付宝账户
    2:required i64 reqTime,             // 请求时间
    3:required i32 appId,               // 业务ID
    4:required string sign              // 签名
}

// 返回结果类
struct QueryWithdrawKugouIdResult{
    1:i32 code;                         // 返回码 0=成功
    2:string msg;                       // 错误描述
    3:optional list<i64> data;                      // 描述信息
}

// 绑定账户信息请求
struct AccountChangeV2Request{
    1:required i64 orderId,             // 请求ID
    2:required i32 appId,               // 业务ID
    3:required i32 bizAppId,            // 网关业务ID
    4:required i64 kugouId,             // 酷狗ID
    5:required string account,          // 支付宝账户（默认空字符串）
    6:required string openid,           // 微信openid（默认空字符串）
    7:required string realName,         // 真实姓名
    8:required string nickname,         // 微信昵称
    9:required string avatar,           // 微信头像
    10:required string token,           // 用户token
    11:required i32 requestTime,        // 请求时间戳（单位：秒）
    12:required string ip,              // 客户端IP
    13:required string sign             // 签名
}

// 创建提现请求
struct CreateWithdrawOrderV2Request {
    1:required i64 orderId,             // 每次请求的ID，确保唯一
    2:required i32 appId,               // 直播业务ID，需申请
    3:required i32 bizAppId,            // 网关业务ID，需申请
    4:required i32 pid,                 // 平台ID
    5:required i64 kugouId,             // 酷狗ID
    6:required i32 drawType,            // 提现类型（1:支付宝；2:微信）
    7:required i32 reqTime,             // 请求时间
    8:required i32 drawTime,            // 提款时间，单位秒
    9:required i64 totalAmount,         // 总金额，单位为分
    10:required string applicationId,    // 终端应用编号
    11:required string clientver,       // 客户端版本号
    12:required string mid,             // 机器唯一码
    13:required string uuid,            // 客户端设备唯一标识
    14:required string dfid,            // 设备指纹ID，如取失败，填'-'
    15:required string token,           // 当前登录的用户令牌
    16:required string clientIp,        // 客户端ip
    17:required string sign             // 签名
}

service WithdrawService {

    // 查询支付宝提现账号
	AccountQueryResult queryAccountInfoByKugouId(1:required WithdrawAccountQueryRequest request);

    // 绑定提现账号
    WithdrawResult bindAccountInfo(1:required AccountChangeRequest request);

    // 发起提现请求
    WithdrawResult createWithdrawOrder(1:required CreateWithdrawOrderRequest request);

    // 取消提现订单
    WithdrawResult cancelWithdrawOrder(1:required CancelWithdrawOrderRequest request);

    // 查询提现订单
    WithdrawOrderResult queryWithdrawOrder(1:required QueryWithdrawOrderRequest request);

    // 根据支付宝账号反查酷狗ID
    QueryWithdrawKugouIdResult getKugouIdByAliPayAccount(1:required QueryWithdrawKugouIdRequest request);

    // 绑定提现账号V2
    WithdrawResult bindAccountInfoV2(1:required AccountChangeV2Request request);

    // 发起提现请求V2
    WithdrawResult createWithdrawOrderV2(1:required CreateWithdrawOrderV2Request request);

    // 查询微信提现账号
	WechatAccountQueryResult queryWechatAccountInfoByKugouId(1:required WechatAccountQueryRequest request);
}
