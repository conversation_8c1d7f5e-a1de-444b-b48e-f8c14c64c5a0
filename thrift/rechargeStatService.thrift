namespace java com.kugou.fanxing.recharge.thrift
namespace php PlatformRechargeService

struct UserYearRechargeStatData {
    1:required string rechargeAmount,
    2:required i64 kugouId
}
struct UserYearRechargeStatResponse {
    1:required i32 code,
    2:required string msg,
    3:optional UserYearRechargeStatData data
}
struct UserYearRechargeStatRequest {
    /** 应用号 */
    1:required i32 appId,
    2:required i64 kugouId,
    3:required string sign
}

struct UserEverRechargeDTO {
    //用户充值过的星币数量
    1:required i32 coins,
    2:required i64 kugouId
}
struct UserEverRechargeResponse {
    1:required i32 code,
    2:required string msg,
    3:optional UserEverRechargeDTO data
}
struct UserEverRechargeRequest {
    /** 应用号 */
    1:required i32 appId,
    2:required i64 kugouId,
    3:required string sign
}
service RechargeStatService {
    /** 获取用户前一年的充值金额 */
    UserYearRechargeStatResponse userYearRechargeStat(1:UserYearRechargeStatRequest userYearRechargeStatRequest);
    /** 获取用户历史总充值额 -- 单位星币 */
    UserEverRechargeResponse userEverRecharge(1:UserEverRechargeRequest userEverRechargeRequest);
}

