namespace java com.kugou.fanxing.recharge.thrift
namespace php PlatformRechargeService


/** 充值相关协议 */
service RechargeThriftService
{

    /** 通过时间段来查询批量成功订单，限制时间（最大周期为5分钟之内，时间间隔为1分钟） beginTime ：开始时间戳 ;endTime：结束时间戳; lastId：第一次传值1，第二次使用第一次返回的lastId ;pageSize：每页大小，最大不超过100 */
    ResultList getRechargeList(1:i64 beginTime,2:i64 endTime,3:i64 lastId,4:i32 pageSize);

    /** 通过时间段来查询批量成功订单，限制时间（最大周期为5分钟之内，时间间隔为1分钟） beginTime ：开始时间戳 ;endTime：结束时间戳; lastId：第一次传值1，第二次使用第一次返回的lastId ;pageSize：每页大小，最大不超过100 */
    ResultSuccessList getRechargeSuccessList(1:i64 beginTime,2:i64 endTime,3:i64 lastRechargeId,4:i32 batchSize);

    /** 通过时间段来查询批量成功订单，限制时间（最大周期为5分钟之内，时间间隔为1分钟） */
    ResultSuccessList getRechargeSuccessListV2(1:QueryRechargeSuccessRequest request);

    /** 获取首充列表 **/
    ResultList getFirstRechargeList(1:i64 beginTime,2:i64 endTime,3:i64 lastId,4:i32 pageSize);


    /** 获取充值信息 **/
    ResultInfo getRechargeInfo(1:string rechargeOrderNum);


    /** 判断是否是代理,1是，0不是 **/
    AgentInfo isAgent(1:i64 kugouId);

    /** 判断是否是天猫首充,1是，0不是 **/
    TmallFirstRechargeInfo isTmallFirstRecharge(1:string rechargeOrderNum);


     /** 获取充值信息 **/
     ResultInfo getRechargeInfoFromMaster(1:string rechargeOrderNum);

}

struct QueryRechargeSuccessRequest {
    1:i64 beginTime,
    2:i64 endTime,
    3:i64 lastRechargeId,
    4:i32 batchSize,
    5:bool includeSingCoin
}

struct TmallFirstRechargeInfo
{
    1:required i32 ret,
    2:required string msg,
    3:optional i32 isTmallFirstRecharge,/** 1是，0不是 */
}




struct AgentInfo
{
    1:required i32 ret,
    2:required string msg,
    3:optional i32 isAgent,/** 1是，0不是 */
}


struct ResultInfo
{
    1:required i32 ret,
    2:required string msg,
    3:optional RechargeInfo data,
}


struct ResultList
{
    1:required i32 ret,
    2:required string msg,
    3:optional list<RechargeInfo> data,
    4:optional i64 lastId,
}

struct RechargeInfo
{
    1:optional i64 id, /** 系统自增id（注意首充和充值的自增id是不一样的） */
    2:optional string rechargeOrderNum, /** 充值订单号 */
    4:optional string consumeOrderNum, /** 充值网关号 */
    5:optional i64 addTime, /** 添加时间 */
    6:optional i64 rechargeTime, /** 充值时间 */
    7:optional i64 kugouId, /** 充值酷狗id */
    8:optional i64 agentKugouId, /** 代理id */
    9:optional i64 fromKugouId, /** 谁充的 */
    10:optional string coin, /** 充值星币 */
    11:optional string amount, /** 充值金额 */
    12:optional string realAmount, /** 充值金额(扣除手续费后,分) */
    13:optional string coinBefore, /** 充值前星币 */
    14:optional string coinAfter, /** 充值网关号 */
    15:optional i32 payTypeId, /** 充值渠道 */
    16:optional string extraJsonData, /** 扩展属性字段 */
    17:optional string refer, /** 充值网关号 */
    18:optional i32 cFrom, /** 分来源渠道标识(0:官网,1:内嵌页,2:代理,3:销售) */
    19:optional i32 channelId, /** 充值网关号 */
    20:optional i32 reType, /** 充值类型 */
    21:optional string extend, /** 扩展信息 */
    22:optional string businessId, /** 扩展信息 */
}

struct ResultSuccessList
{
    1:required i32 ret,
    2:required string msg,
    3:optional list<RechargeSuccessInfo> data,
    4:optional i64 lastRechargeId,
}

struct RechargeSuccessInfo
{
    1:required i64 rechargeId, /** 唯一标识 */
    2:required string rechargeOrderNum, /** 充值订单号 */
    3:required string consumeOrderNum, /** 充值网关号 */
    4:required i64 addTime, /** 添加时间 */
    5:required i64 rechargeTime, /** 充值时间 */
    6:required i64 kugouId, /** 充值酷狗id */
    7:required i64 fromKugouId, /** 谁充的 */
    8:required string coin, /** 充值星币 */
    9:required string amount, /** 充值金额 */
    10:required string realAmount, /** 充值金额(扣除手续费后,分) */
    11:required i32 payTypeId, /** 充值渠道 */
    12:required string extraJsonData, /** 扩展属性字段 */
    13:required i32 refer, /** 充值网关号 */
    14:required i32 cFrom, /** 分来源渠道标识(0:官网,1:内嵌页,2:代理,3:销售) */
    15:required i32 channelId, /** 充值网关号 */
    16:required i32 reType, /** 充值类型 */
    17:required string extend, /** 扩展信息 */
    18:required string businessId, /** 业务标识 */
    19:required i64 tradeTime, /** 交易时间 */
    20:required string tradeNo, /** 交易单号 */
    21:required string partner, /** 交易商户 */
    22:required string coupon, /** 代金券金额 */
    23:required i32 couponStatus, /** 代金券状态 */
    24:required i64 couponOrderId, /** 代金券订单号 */
    25:required i32 isSandbox, /** 是否沙盒充值 */
    26:required string money, /** 实付金额 */
    27:required i64 couponId, /** 代金券标识 */
    28:required i32 serverRoom, /** 机房编号 */
    29:optional i32 coinType /** 币种（1:星币；2:唱币）*/
}


