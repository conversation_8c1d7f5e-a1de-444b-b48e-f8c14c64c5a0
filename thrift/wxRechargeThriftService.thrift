namespace java com.kugou.fanxing.recharge.thrift
namespace php WxRechargeThriftService

struct WxPayAndContractRequest {
       1:required string accessToken,
       2:required string bizSign,
       3:required string amount,
       4:required i64 fromKugouId,
       5:required i64 tokugouId,
       6:required string  businessId,
       7:required i64 businessTime,
       8:required string subject,
       9:required string syncUrl,
       10:required string clientIp,
       11:required i32 refer,
       12:required i32 cFrom,
       13:required i32 pid,
       14:required i32 channelId,
       15:required UserFundPlatParam userFundPlatParam,
       16:required i32 bizAppId,
       17:required string protocol,
       18:optional string ext,
       19:required string businessExt,
       20:required string businessType,
}

struct UserFundPlatParam {
        1:required string senderDepartmentId,
        2:required string senderProductId,
        3:required string senderMinorProductId,
        4:required string senderHardwarePlatform,
        5:required string senderChannelId,
        6:required string senderSubChannelId,
        7:required string receiverDepartmentId,
        8:required string accountChangeType,
        9:required string fxcChangeDesc,
        10:required string coin,
        11:required string fromKugouId,
        12:required string toKugouId,
        13:required string roomId,
        14:required string giftId,
        15:required string giftName,
        16:required string giftNum,
        17:required string actionId,
        18:optional string ext,
        19:required string pid,
        20:required string globalId,
        21:required string ip
}

struct WxPayAndContractResponse {
    1:required i32 code,
    2:required string msg,
    3:optional string data
}


service WxRechargeThriftService {
    /** 支付并签约 */
    WxPayAndContractResponse payAndContract(1:WxPayAndContractRequest request);
}






