namespace java com.kugou.fanxing.recharge.thrift.callback

struct ConsumeParam {
    1:required i32 accountChangeType,
    2:required i64 fromKugouId,
    3:required i64 toKugouId,
    4:required i32 giftId,
    5:required i32 giftNum,
    6:required string giftName,
    7:required i32 roomId,
    8:required string ext,
    9:required string fxcChangeDesc,
    10:required i32 coin
}

struct PurchaseCoinForIosRequest {
    1:required string orderNum,
    2:required string key,
    3:required i64 kugouId,
    4:required i32 time,
    5:required string coin,
    6:required string money,
    7:required string signBook,
    8:required i32 type,
    9:required string goodsId,
    10:required i32 pid,
    11:required string version,
    12:required i32 channelId,
    13:required i32 isSandbox,
    14:required string tradeTime,
    15:required string tradeNo,
    16:required string partner,
    17:required string businessExt,
    18:optional string consumeType,
    19:optional string roomId,
    20:optional string giftId,
    21:optional string giftNum,
    22:optional string appId,
    23:optional string bundleId,
    24:optional string areaCode,
    25:optional string timeZone,
    26:optional string currency,
    27:optional string currencyAmount,
    28:optional string usdAmount,
    29:optional string clientIp,
    30:optional i32 areaId,
    31:optional string outTradeNo
}

struct PurchaseForIosRequest {
    1:required string orderNo,
    2:required string sign,
    3:required i64 kugouId,
    4:required i32 time,
    5:required string coin,
    6:required string money,
    7:required string signBook,
    8:required i32 type,
    9:required string goodsId,
    10:required i32 pid,
    11:required string version,
    12:required i32 channelId,
    13:required i32 isSandbox,
    14:required string tradeTime,
    15:required string tradeNo,
    16:required string partner,
    17:required string businessExt,
    18:optional string consumeType,
    19:optional i32 roomId,
    20:optional i32 giftId,
    21:optional string giftNum,
    22:optional string appId,
    23:required i64 toKugouId,
    24:required string clientIp,
    25:required ConsumeParam ConsumeParam,
    26:required string ext,
    27:required string businessId,
    28:optional string bundleId,
    29:optional string areaCode,
    30:optional string timeZone,
    31:optional string currency,
    32:optional string currencyAmount,
    33:optional string usdAmount,
    34:optional i32 areaId,
    35:optional string outTradeNo
}

struct RenewalsForIosRequest {
    1:required string orderNo,
    2:required string sign,
    3:required i64 kugouId,
    4:required i32 time,
    5:required string coin,
    6:required string money,
    7:required string signBook,
    8:required i32 type,
    9:required string goodsId,
    10:required i32 pid,
    11:required string version,
    12:required i32 channelId,
    13:required i32 isSandbox,
    14:required string tradeTime,
    15:required string tradeNo,
    16:required string partner,
    17:required string businessExt,
    18:optional string consumeType,
    19:optional i32 roomId,
    20:optional i32 giftId,
    21:optional string giftNum,
    22:optional string appId,
    23:required i64 toKugouId,
    24:required string clientIp,
    25:required ConsumeParam ConsumeParam,
    26:required string ext,
    27:required string businessId,
    28:required string topic,
    29:required i32 bizId,
    30:required i64 bizEndTime,
    31:optional string bundleId,
    32:optional string areaCode,
    33:optional string timeZone,
    34:optional string currency,
    35:optional string currencyAmount,
    36:optional string usdAmount,
    37:optional i32 areaId,
    38:optional string outTradeNo
}

struct CallbackResponse {
    1:required i32 code,
    2:required string msg,
    3:optional string data
}

service RechargeCallbackService {
    /** 苹果充值购买星币回调接口 */
    CallbackResponse purchaseCoinForIos(1:PurchaseCoinForIosRequest request);
    /** 苹果充值购买物品回调接口 */
    CallbackResponse purchaseForIos(1:PurchaseForIosRequest request);
    /** 苹果充值续费物品回调接口 */
    CallbackResponse renewalsForIos(1:RenewalsForIosRequest request);
}