namespace go kgthrift.types
namespace php SocketService
namespace java com.kugou.fanxing.thrift.acksocket.gather.types

//消息推送错误码
enum DispatchErrCode{
    /**
    * 初始化状态
    **/
    NONE=-1
    /**
    * 调用成功
    **/
	Suc=0
	/**
	* 服务器内部错误
    **/
	ServerErr=1
	/**
    * 无效的kugouId
    **/
	InvalidKid=2
	/**
	* 无效的userId
    **/
	InvalidUid=3
	/**
	* 广播bcId和bcKey校验失败
    **/
	InvalidBcId=4
    /**
    * 无效的roomId
    **/
	InvalidRoomId=5
    /**
    * 无效的appId
    **/
	InvalidAppId=6
    /**
    * 不存在的用户
    **/
	UserNotFound=7
    /**
    * 无效的结束时间（应该是针对定时推送功能）
    **/
	InvalidEndTime=8
    /**
    * 无效的客户端clientId列表，当MsgOption.sendToAll为false时可能触发此错误
    **/
	InvalidClientIds=9
    /**
    * 无效的特殊命令
    **/
	InvalidSpecialCmd=10
    /**
    * 无效的命令号
    **/
	InvalidCmdId=11
    /**
    * 无效的消息体
    **/
	InvalidContent=12
    /**
    * 无效的全局Id
    **/
	InvalidGid=13
    /**
    * 无效的参数,参数校验不通过
    **/
	InvalidParameter=14
    /**
    * batchSize超过限制
    **/
	InvalidBatchSize=15
	/**
	* roomIdsSize超过限制
    **/
	InvalidRoomIdsSize=16
	/**
    * 命令号没有配置
    **/
    InvalidCmdConfig=17
}

struct FromUserV2{
    1:required i64 kugouId=0,
	2:required i32 roomId=0,
	3:required i32 appId=0,
	4:optional i64 userId=0,	//如果没有，socket补充
	5:optional string ext="",	//如果没有，socket补充，缓存时间为30s~1800s（默认写入缓存时间为1800s，如果缓存超过30s会尝试更新，若更新失败则会合作旧缓存）
}

//消息推送用户参数,ext为protobuf版本
struct FromUserProto {
    1:required i64 kugouId=0,
	2:required i32 roomId=0,
	3:required i32 appId=0,
	4:required i64 userId=0,
	/** 是否需要补充发送者ext */
	5:optional bool needExt = false
}

//后续接口应该遵遁：http://wiki.fxwork.kugou.net/#doc/G-技术组/基础技术组/规范/内部接口规范.md
//布尔类返回值
struct BoolResponse {
	1:required i32 ret,					//请参照0表示调用成功，其它为失败
	2:required string msg,				//提示消息
	3:required bool data,				//true表示业务成功，false表示业务失败
}

//消息推送附加参数
struct MsgOpt {
    /**发送到前端的时间截，如果你指定了时间截，将不会使用socket系统时间，单位秒*/
    1:optional i64 timestamp=0,
    /**是否启用ack机制*/
    2:optional bool ack=false,
    /**消息全局id*/
    3:optional i64 gid=0,
    /**是否上报本条消息到bi(1:是,0:否)*/
    4:optional i32 rpt=0,
    /**是否幂等*/
    5:optional bool isIdempotent=false,
    /**发送者kugouId*/
    6:optional i64 senderKid,
    /**发送者繁星id*/
    7:optional i64 senderUid,
}

//消息推送来源
enum MsgFrom {
    Default=0       //默认来源，外部应用（比如后端消息推送）
    NewSocket=1     //由newSocket发出的
    AckSocket=2     //由ackSocket发出的
}

enum MsgSource {
    System=0       //socket系统推送
    Nsq=1          //nsq bridge
    Thrift=2       //message service
}

//消息推送附加参数
struct MsgOption {
    /**是否发送给所有端，如是为true，将忽略clientIds*/
	1:required bool sendToAll,
	/**发送到指定终端，当sendToAll=false时生效，此时参数不能为空*/
	2:required set<i32> clientIds,
	/**发送到某个登录协议版本大于等于protocolVersion*/
	3:optional i32 protocolVersion=0,
	/**消息全局id*/
	4:optional i64 gid=0,
	/**是否上报本条消息到bi(1:是,0:否)*/
	5:optional i32 rpt=0,
	/**发送到某个登录协议版本小于等于maxProtocolVersion，0表示无上限*/
	6:optional i32 maxProtocolVersion=0,
	/**发送到前端的时间截，如果你指定了时间截，将不会使用socket系统时间，单位秒*/
	7:optional i64 timestamp=0,
	/**消息来源*/
	8:optional MsgFrom msgFrom=MsgFrom.Default,
	/**是否启用ack机制*/
	9:optional bool ack=false,
	/**不发送给此kugouId列表*/
	10:optional set<i64> exceptKugouIds,
	/**如果isSystem=true，优先级比Result.deadLink高，直接发送给用户*/
	11:optional bool isSystem=false,
	/**消息来源*/
	12:optional MsgSource Source=MsgSource.System,
	/**内容见,http://c.fxwork.kugou.net/pages/viewpage.action?pageId=18089077*/
	13:optional string jsonAttach="",
	/**是否幂等*/
	14:optional bool isIdempotent=false,
	/**限流时的优先级*/
	15:optional i32 plev,
	/**限流时优先级的权重*/
	16:optional i32 pvalue,
    /**发送者kugouId*/
    17:optional i64 senderKid,
    /**发送者繁星id*/
    18:optional i64 senderUid,
    /**不发送到指定中断，当sendToAll=false时生效*/
    19:optional set<i32> excludeClientIds,
}


/**
* 批量发送的消息参数
**/
struct BatchMsg {
    1:required BatchOption batchOption,
    2:required binary content
}

/**
* 批量发送的消息参数
**/
struct BatchOption{
    1:required i32 roomId,
    2:required i64 toKid,
    3:required i64 toUid,
    4:required i32 cmdId,
    5:required MsgOption msgOption
}

/**
* 批量根据kugouId发送消息入参
**/
struct BatchMessage{
    1:required i64 toKid,
    2:required i64 toUid,
    3:required binary content,
    4:required i32 cmdId,
    5:required MsgOption msgOption
}

/**
* 特殊通道：批量根据kugouId发送消息入参
**/
struct BatchMessageSpecial{
    1:required i64 toKid,
    2:required i64 toUid,
    3:required binary content,
    4:required i32 cmdId,
    5:required MsgOpt msgOpt
}