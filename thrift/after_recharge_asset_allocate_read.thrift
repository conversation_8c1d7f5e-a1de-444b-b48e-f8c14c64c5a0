namespace java com.kugou.platform.after.recharge.asset.allocate.thrift
namespace php  Services.AfterRecharge.AssertAllocate.Service

/** 充值后资产发放相关协议 */
service AfterRechargeAssetAllocateReadService {
    /** 获取某个用户的累计数据 **/
    PresentInfo getRechargePresentByNum(1:list<i64> money);
    /**
    * 根据金额列表获取 对应的充值赠品信息
    **/
    PresentInfo getRechargePresentInfo(1:GetRechargePresentRequest request);
}

struct GetRechargePresentRequest {
    /**
    * 请求的金额列表
    **/
    1:required list<i64> moneys,
    /**
    * 酷狗id
    **/
    2:optional i64 kgId,
    /**
    * 业务标识id
    **/
    3:optional string actFlag
}
/*业务类型*/
enum GoodsType {
    PRESENT = 1, //赠币：present
    STORAGE = 2, //礼物入仓：storage
    VIP     = 3, //购买VIP：vip
    MEDAL   = 4, //发送勋章：medal
    MOUNT   = 5, //购买座驾：mount
    GUARD   = 6, //购买守护：guard
    ASSET   = 7, //虚拟资产：asset
}

struct PresentInfo {
    1:required i32 ret,//1正常，0失败
    2:required string msg,
    3:optional map<i64,list<RechargePresentItem>> data,
}
struct RechargePresentItem {
    1:required i64 goodsType, /** 资产类型 */
    2:required i64 assetId, /** 资产id: 0代表星币，2000000043代表小摇一摇，2000000044代表大摇一摇 */
    3:required i64 assetType, /** 预留字段目前无用 */
    4:required i64 num, /** 资产数量 */
    5:required i64 expireTime,/** 过期时间，0代表未过期 */
}