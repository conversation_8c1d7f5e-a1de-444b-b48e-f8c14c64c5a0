namespace java com.kugou.fanxing.thrift.pay.v2

struct ConvertTradeNoRequest {
    /**业务调用标识id*/
    1:required i32 appId,
    /**直播支付的交易单号*/
    2:required string rechargeOrderNum,
    /**第三方支付交易单号*/
    3:required string tradeNo,
    /**签名**/
    4:required string sign,
}

struct TradeNoMappingDTO {
    /**直播支付的交易单号*/
    1:required string rechargeOrderNum,
    /**第三方支付交易单号*/
    2:required string tradeNo
}

struct ConvertTradeNoResponse {
    1:required i32 code,
    2:required string msg,
    3:optional TradeNoMappingDTO data
}

struct QueryRechargeOrderNumByTradeNoRequest {
    /**业务调用标识id*/
    1:required i32 appId,
    /**第三方支付交易单号*/
    2:required string tradeNo,
    /**签名**/
    3:required string sign,
}

struct QueryRechargeOrderNumByTradeNoResponse {
    1:required i32 code,
    2:required string msg,
    3:optional TradeNoMappingDTO data
}

struct ConvertBizOrderNoRequest {
    /**业务编号*/
    1:required i32 bizId,
    /**业务交易单号*/
    2:required string bizOrderNo,
    /**直播交易单号*/
    3:optional string rechargeOrderNum
}

struct BizOrderNoMappingDTO {
    /**直播交易单号*/
    1:required string rechargeOrderNum,
    /**业务交易单号*/
    2:required string bizOrderNo
}

struct ConvertBizOrderNoResponse {
    1:required i32 code,
    2:required string msg,
    3:optional BizOrderNoMappingDTO data
}

struct QueryFapiaoRechargeListRequest {
    1:required i64 kugouId,
    2:required i64 startRechargeTime,
    3:required i64 endRechargeTime,
    4:required i64 lastRechargeId,
    5:required i64 limit
}

struct QueryFapiaoRechargeListV2Request {
    1:required i64 kugouId,
    2:required i64 startRechargeTime,
    3:required i64 endRechargeTime,
    4:required i64 lastRechargeId,
    5:required i64 limit,
    6:required i32 payTypeId,
}

struct QueryFapiaoRechargeListResponse {
    1:required i32 code,
    2:required string msg,
    3:optional list<FapiaoRechargeInfo> data
}

struct FapiaoRechargeInfo {
    1:required i64 rechargeId,
    2:required i64 kugouId,
    3:required string rechargeOrderNum,
    4:required string outTradeNo,
    5:required string tradeNo,
    6:required string orderNo,
    7:required i64 rechargeTime,
    8:required string amount,
    9:required string money,
    10:required string coin,
    11:required i64 couponId,
    12:required i64 couponOrderId,
    13:required string coupon,
    14:required i32 couponStatus,
    15:required i32 reType
    16:required i32 payTypeId,
    17:required string fapiaoOrderId,
    18:required i32 fapiaoStatus,
    19:required string fapiaoStatusLabel,
    20:optional i32 coinType
}

struct SyncFapiaoInfoRequest {
    1:required i64 kugouId,
    2:required string outTradeNo,
    3:required string orderNo,
    4:required bool canReceipt,
    5:required i32 receiptStatus,
    6:required i32 receiptType,
    7:required i32 operationType
}

struct SyncFapiaoInfoResponse {
    1:required i32 code,
    2:required string msg,
    3:required bool syncSuccess
}

struct PromotionDetailRequest {
    1:required i64 kugouId,
    2:required string businessId,
    3:required string businessName,
    4:required string promoParam
}

struct PromotionDetailResponse {
    1:required i32 code,
    2:required string msg,
    3:optional PromotionDetailDto promotionDetailDto
}

struct PromotionDetailDto {
    1:required i64 kugouId,
    2:required string businessId,
    3:required string businessName,
    4:required string promoParam,
    5:required i32 orderNum,
    6:required i32 payedNum,
    7:required i32 totalUsage
}

struct SandboxRechargeLimitReq {
    /**业务调用标识id*/
    1:required i32 appId,
    /**直播支付的交易单号-幂等id*/
    2:required string orderNum,
    /**累积的key*/
    3:required string calKey,
    /**累积的value*/
    4:required string calValue,
    /**限制value*/
    5:required  i64 limitValue,
    /**签名**/
    6:required string sign,
}

struct SandboxRechargeLimitResponse {
    1:required i32 code,
    2:required string msg,
    3:optional string totalValue
}

struct QueryRechargeAwardRequest {
    1:required string bizCode,
    2:required i32 sourceId,
    3:required i64 kugouId,
    4:required string rechargeOrderNum,
    5:required i64 rechargeTime
}

struct QueryRechargeAwardResponse {
    1:required i32 code,
    2:required string msg,
    /**充值交易对应活动奖励信息*/
    3:optional RechargeAwardDto rechargeAwardDto
}

struct RechargeAwardDto {
    1:required string rechargeOrderNum,
    2:required i64 rechargeTime,
    3:required string amount
}

struct CanParticipateAwardRequest {
    1:required string bizCode,
    2:required i32 sourceId,
    3:required i64 kugouId,
    4:required i32 stdPlat
}

struct CanParticipateAwardResponse {
    1:required i32 code,
    2:required string msg,
    /**是否允许参与充值奖励活动*/
    3:required bool canParticipate
}

/**
* platform_pay_sevice protocol
**/
service PlatformPayV2Service {
    ConvertTradeNoResponse convertTradeNoToRechargeOrderNum(1:ConvertTradeNoRequest request);
    QueryRechargeOrderNumByTradeNoResponse queryRechargeOrderNumByTradeNo(1:QueryRechargeOrderNumByTradeNoRequest request);
    ConvertBizOrderNoResponse convertBizOrderNoToRechargeOrderNum(1:ConvertBizOrderNoRequest request);
    QueryFapiaoRechargeListResponse queryFapiaoRechargeList(1:QueryFapiaoRechargeListRequest request);
    QueryFapiaoRechargeListResponse queryFapiaoRechargeListV2(1:QueryFapiaoRechargeListV2Request request);
    SyncFapiaoInfoResponse syncFapiaoInfo(1:SyncFapiaoInfoRequest request);
    PromotionDetailResponse incrPromotionDetail(1:PromotionDetailRequest request);
    PromotionDetailResponse loadPromotionDetail(1:PromotionDetailRequest request);
    SandboxRechargeLimitResponse sandboxRechargeLimit(1:SandboxRechargeLimitReq request);
    CanParticipateAwardResponse canParticipateAward(1:CanParticipateAwardRequest request);
    QueryRechargeAwardResponse queryRechargeAward(1:QueryRechargeAwardRequest request);
}
