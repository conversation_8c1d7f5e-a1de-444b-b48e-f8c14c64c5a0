namespace java com.kugou.fanxing.recharge.thrift
namespace php PlatformRechargeService

struct QueryUserRechargeListRequest {
    /** 酷狗ID */
    1:required i64 kugouId,
    /** 月份，如：201912 */
    2:required string month,
    /** 当前页码，如：1 */
    3:required i32 page,
    /** 页面大小，如：12 */
    4:required i32 pageSize,
    /** 平台号 */
    5:required i32 pid,
    /** 应用号 */
    6:required i32 appId,
    /** 时间戳 */
    7:required i32 timestamp,
    /** 签名 */
    8:required string sign
}

struct QueryUserRechargeListRequestWithTime {
    /** 酷狗ID */
    1:required i64 kugouId,
    /** 当前页码，如：1 */
    2:required i32 page,
    /** 页面大小，如：12 */
    3:required i32 pageSize,
    /** 开始时间 -- 跨月时只会按照开始时间去获取当月数据，请注意 */
    4:required i64 startTime,
    /** 结束时间 */
    5:required i64 endTime,
    /** 应用号 */
    6:required i32 appId,
    /** 时间戳 */
    7:required i32 timestamp,
    /** 月份，如：201912 */
    8:required string month,
    /** 签名 */
    9:required string sign
}

struct QueryUserRechargeListResponse {
    1:required i32 code,
    2:required string msg,
    3:optional i32 count,
    4:optional list<RechargeAcrossDTO> data
}

struct RechargeAcrossDTO {
    1:required i32 payTypeId,
    2:required string payTypeLabel,
    3:required i32 status,
    4:required string statusLabel
    5:required string rechargeOrderNum,
    6:required i32 rechargeTime,
    7:required i32 tradeTime,
    8:required string coin,
    9:required string money,
    10:required string couponId,
    11:required string coupon,
    12:required i32 couponStatus,
    13:required string couponStatusLabel,
    14:required i32 couponErrorCode,
    15:required string couponErrorMsg
}

struct ApplyInfoDetailDTO {
    1:required i32 applyType,
    2:required i64 batchNo,
    3:optional string orderNums,
    4:optional i64 toKugouId,
    5:optional i64 toUserId,
    6:optional string newRechargeOrderNum,
    7:optional string toUserAmount,
    8:optional i32 status,
    9:optional string statusLabel,
    10:optional string createTime,
    11:optional i32 approveStatus,
    12:optional string approveRemark,
    13:optional string approveStatusLabel,
    14:optional string imagePath,
    15:optional string toUserCoin,
    16:optional string toUserName,
    17:optional string alipayAccount,
    18:optional string phone,
    19:optional string jsonExtend
}

struct QueryApproveListRequest {
    1:required i32 applyType,
    2:optional i64 batchNo,
    3:optional string rechargeOrderNum,
    4:required i32 page,
    5:required i32 pageSize
}

struct QueryApproveListResponse {
    1:required i32 code,
    2:required string msg,
    3:required i32 count,
    4:required list<ApplyInfoDetailDTO> data
}

struct RechargeApproveRequest {
    1:required i64 batchNo,
    2:required i32 status,
    3:optional string remark,
    4:optional string bankTransactionId
}

struct RechargeApproveResponse {
    1:required i32 code,
    2:required string msg,
    3:optional string data
}

struct PurchaseProductRequest {
    /**
     * 充值业务编号（充值服务分配）
     */
    1:required string businessId,
    /**
     * 充值下单时间（秒）
     */
    2:required i32 businessTime,
    /**
     * 充值金额
     */
    3:required string amount,
    /**
     * 用户ID
     */
    4:required i64 kugouId,
    /**
     * 主播ID
     */
    5:required i64 toKugouId,
    /**
     * 商品名称（第三方支付展示）
     */
    6:required string subject,
    /**
     * 同步回调地址（默认取网页referer）
     */
    7:required string syncUrl,
    /**
     * 重定向地址（默认:"")
     */
    8:required string redirectUrl,
    /**
     * 异步回调NSQ（必须带fx.前缀）
     */
    9:required string topic,
    /**
     * 客户端IP
     */
    10:required string clientIp,
    /**
     * 平台编号（详见：http://c.fxwork.kugou.net/pages/viewpage.action?pageId=25156183）
     */
    11:required i32 pid,
    /**
     * 充值代金券ID（默认：0）
     */
    12:required i64 couponId,
    /**
     * 充值扩展（JSON格式）
     */
    13:required string extJson,
    /**
     * 支付类型（充值服务分配）
     */
    14:required i32 payTypeId,
    /**
     * 微信账号（微信支付方式传递，默认：空字符串）
     */
    15:required string openId,
    /**
     * 消费参数（充扣模型使用）
     */
    16:required ConsumeParam consumeParam,
    /**
     * 参数签名
     */
    17:required string sign
}

struct ConsumeParam {
    /**
     * 消费业务类型（消费服务分配）
     */
    1:required i32 accountChangeType,
    /**
     * 用户酷狗ID
     */
    2:required i64 fromKugouId,
    /**
     * 主播酷狗ID（默认值：0)
     */
    3:required i64 toKugouId,
    /**
     * 礼物ID（默认值：待定）
     */
    4:required i64 giftId,
    /**
     * 礼物名称（默认值：业务名称）
     */
    5:required string giftName,
    /**
     * 礼物数量（默认值：1）
     */
    6:required i32 giftNum,
    /**
     * 主播房间（默认值：0）
     */
    7:required i32 roomId,
    /**
     * 消费扩展（JSON格式）
     */
    8:required string ext
}

struct PurchaseProductResponse {
    1:required i32 code,
    2:required string msg,
    3:optional string data
}

struct PurchaseCurrencyResponse {
    1:required i32 code,
    2:required string msg,
    3:optional PurchaseCurrencyDto data
}

struct PurchaseCurrencyDto {
    1:required string rechargeOrderNum,
    2:optional string outTradeNo,
    3:required string paymentJson
}

struct PurchaseProductRequestV2 {
    /**
     * 充值业务编号（充值服务分配）
     */
    1:required string businessId,
    /**
     * 业务订单号,不用下单的默认传0，否则传globalid
     */
    2:required i64 businessOrderId,
    /**
     * 充值下单时间（秒）
     */
    3:required i64 businessTime,
    /**
     * 充值金额
     */
    4:required string amount,
    /**
     * 用户ID
     */
    5:required i64 kugouId,
    /**
     * 商品名称（第三方支付展示）
     */
    6:required string subject,
    /**
     * 同步回调地址（默认取网页referer）
     */
    7:required string syncUrl,
    /**
     * 重定向地址（默认:"")
     */
    8:required string redirectUrl,
    /**
     * 客户端IP
     */
    9:required string clientIp,
    /**
     * 平台编号（详见：http://c.fxwork.kugou.net/pages/viewpage.action?pageId=25156183）
     */
    10:required i32 pid,
    /**
     * 购物清单
     */
    11:required list<PurchaseOrder> orderList;
    /**
     * 支付类型（充值服务分配）http://c.fxwork.kugou.net/pages/viewpage.action?pageId=6916007
     */
    12:required i32 payTypeId,
    /**
     * 微信账号（微信支付方式传递，默认：空字符串）
     */
    13:required string openId,
    /**
     * 充值扩展（JSON格式）
     * "coinType":2 "标志为唱币充扣"
     */
    14:required string extJson,
    /**
     * 参数签名
     */
    15:required string sign,
    /**
     * 订单支付过期时间（秒）
     */
     16:optional i64 orderExpireTime,
     /**
      * 取消支付之后的跳转地址
      */
     17:optional string showUrl
}

struct PurchaseCurrencyRequest {
    /**
     * 业务编号（充值服务分配）
     */
    1:required i32 businessId,
    /**
     * 业务订单编号
     */
    2:required string orderNo,
    /**
     * 业务订单时间（秒）
     */
    3:required i64 orderTime,
    /**
     * 订单过期时间（秒）
     */
    4:optional i64 orderExpireTime,
    /**
     * 支付方式（充值服务分配）
     */
    5:required i32 payTypeId,
    /**
     * 商品描述
     **/
    6:required string description,
    /**
     * 充值金额
     */
    7:required string amount,
    /**
     * 虚拟币种（1：星币；2:唱币）
     **/
    8:required i32 coinType,
    /**
     * 用户ID
     */
    9:required i64 kugouId,
    /**
     * 客户端IP
     */
    10:required string clientIp,
    /**
     * 平台编号（详见：http://c.fxwork.kugou.net/pages/viewpage.action?pageId=25156183）
     */
    11:required i32 stdPlat,
    /**
     * 房间号（默认：0，直播间内充值必须传递）
     **/
    12:required i32 roomId,
    /**
     * 微信openid（默认：""，微信公众号必须传递）
     **/
    13:optional string openid,
    /**
     * 充值代金券ID（默认：0，使用充值代金券必须传递）
     */
    14:optional i64 couponId,
    /**
     * 苹果内购货品吗（默认：""，苹果内购渠道必须传递）
     */
    15:optional string productId,
    /**
     * 客户端版本号（默认：""）
     */
    16:required string appVersion,
    /**
     * 参数签名
     */
    17:required string sign
}

struct PurchaseOrder {
    /**
     * 消费业务类型（消费服务分配）
     */
    1:required i32 accountChangeType,
    /**
     * 用户酷狗ID
     */
    2:required i64 fromKugouId,
    /**
     * 收礼者酷狗ID（默认值：0)
     */
    3:required i64 toKugouId,
    /**
     * 礼物ID（默认值：待定）
     */
    4:required i64 goodsId,
    /**
     * 礼物数量（默认值：1）
     */
    5:required i32 goodsNum,
    /**
     * 礼物数量（默认值：1）
     */
    6:required i32 goodsType,
    /**
     * 主播房间（默认值：0）
     */
    7:required i32 roomId,
    /**
     * 消费扩展（JSON格式）
     */
    8:required string ext
}

struct QueryUserRenewalRequest {
    /** 平台号 */
    1:required i32 pid,
    /** 充值购买（充值服务分配）*/
    2:required i32 appId,
    /** 酷狗ID */
    3:required i64 kugouId,
    /** 续费类型（1:支付宝；2:微信；3:苹果）*/
    4:required i32 renewalType,
    /** 充值购买（充值服务分配）*/
    5:required string businessId,
    /** 时间戳 */
    6:required i64 timestamp,
    /** 签名 */
    7:required string sign
}

struct UserRenewalInfoDTO {
    1:required i32 pid,
    2:required i64 kugouId,
    3:required i32 renewalType,
    4:required string businessId,
    5:required i64 lastRenewalTime,
    6:required i64 nextRenewalTime,
    7:optional string productId,
    8:required i32 signStatus
}

struct QueryUserRenewalResponse {
    1:required i32 code,
    2:required string msg,
    3:optional UserRenewalInfoDTO data
}

struct SyncKupayOfflineRechargeOrderRequest {
    1:required i64 kugouId,
    2:required string rechargeOrderNum,
    3:required string tradeNo,
    4:required i64 rechargeTime,
    5:required string money
}

struct SyncKupayOfflineRechargeOrderResponse {
    1:required i32 code,
    2:required string msg,
    3:optional SyncKupayOfflineRechargeOrderDTO data
}

struct SyncKupayOfflineRechargeOrderDTO {
    1:required string outTradeNo,
    2:required string orderNo,
    3:required string tradeNo,
    4:required string totalFee,
    5:required string subject,
    6:required string desc
}

struct QueryRechargeOrderRequest {
    1:required string rechargeOrderNum
}

struct QueryRechargeOrderByOrderNumRequest {
    1:required string orderNum
}

struct QueryRechargeOrderResponse {
    1:required i32 code,
    2:required string msg,
    3:optional RechargeOrderInfoDto data
}

struct RechargeOrderInfoDto {
    1:required string rechargeOrderNum, /** 直播充值订单号 */
    2:required string outTradeNo, /** 支付网关订单号 */
    3:required i64 addTime, /** 下单时间 */
    4:required i64 rechargeTime, /** 支付时间 */
    5:required i64 kugouId, /** 酷狗ID */
    6:required i64 agentKugouId, /** 代理酷狗ID */
    7:required string coin, /** 充值星币 */
    8:required string amount, /** 充值金额，单位：元 */
    9:required string money, /** 支付金额，单位：元 */
    10:required string coupon, /** 券抵金额，单位：元 */
    11:required string realAmount, /** 充值金额，单位：分 */
    12:required i32 payTypeId, /** 充值渠道 */
    13:required string extraJsonData, /** 扩展属性字段（小米支付、苹果支付） */
    14:required i32 refer, /** 充值来源 */
    15:required i32 cFrom, /** 客户端平台编号 */
    16:required i32 channelId, /** 充值渠道 */
    17:required i32 reType, /** 充值类型 */
    18:required string extend, /** 扩展信息，BASE64编码 */
    19:required string businessId, /** 充值业务编号 */
    20:required i64 tradeTime, /** 第三方交易时间 */
    21:required string tradeNo, /** 第三方交易单号 */
    22:required string partner, /** 第三方商户编号 */
    23:required i64 couponOrderId, /** 代金券订单编号 */
    24:required i32 couponStatus, /** 代金券使用状态 */
    25:required i64 couponId,  /** 代金券编号 */
    26:required i32 status,  /** 交易状态 0 待支付 1 已支付 */
    27:optional string orderNum /** 苹果充值交易号 **/
}

service RechargeService {
    /** 查询用户充值记录接口 */
    QueryUserRechargeListResponse queryUserRechargeList(1:QueryUserRechargeListRequest request);
    /** 查询线下充值列表接口 */
    QueryApproveListResponse queryOfflineRechargeApproveList(1:QueryApproveListRequest request);
    /** 审核线下充值申请信息 */
    RechargeApproveResponse approveOfflineRechargeApply(1:RechargeApproveRequest request);
    /** 充值充扣模型下单接口 */
    PurchaseProductResponse createOrderForPurchaseProduct(1:PurchaseProductRequest request);
    /** 充值充扣模型下单接口 */
    PurchaseProductResponse createOrderForPurchaseProductV2(1:PurchaseProductRequestV2 request);
    /** 充值星币模型下单接口 */
    PurchaseCurrencyResponse createOrderForPurchaseCurrency(1:PurchaseCurrencyRequest request);
    /** 查询用户续费信息接口 */
    QueryUserRenewalResponse queryUserRenewalInfo(1:QueryUserRenewalRequest request);
    /** 查询用户充值记录接口 */
    QueryUserRechargeListResponse queryUserRechargeListWithTime(1:QueryUserRechargeListRequestWithTime request);
    /** 同步线下充值支付网关 */
    SyncKupayOfflineRechargeOrderResponse syncKupayOfflineRechargeOrder(1:SyncKupayOfflineRechargeOrderRequest request);
    /** 查询用户充值到账记录 */
    QueryRechargeOrderResponse queryRechargeOrder(1:QueryRechargeOrderRequest request);
    /** 查询用户充值到账记录，根据苹果充值单号 **/
    QueryRechargeOrderResponse queryRechargeOrderByOrderNum(1:QueryRechargeOrderByOrderNumRequest request);
    /** 苹果内购收据上报接口 */
    ReportIapReceiptResponse reportIapReceipt(1:ReportIapReceiptRequest request);
    /** 苹果内购退款通知接口 */
    IapNotificationResponse iapNotification(1:IapNotificationRequest request);
    /** 查询充值订单信息接口 */
    QueryBizOrderResponse queryBizOrder(1:QueryBizOrderRequest request);
    /** 拉取支付成功订单流水 */
    QueryRechargeListResponse queryRechargeList(1:QueryRechargeListRequest request);
}

struct IapNotificationRequest {
    1:required i32 businessId,
    2:required string notification,
    3:required string sign
}

struct IapNotificationResponse {
    1:required i32 code,
    2:required string msg
}

struct ReportIapReceiptRequest {
    1:required i32 businessId,
    2:required string orderNo,
    3:required string rechargeOrderNum,
    4:required string receiptData,
    5:required string sign
}

struct ReportIapReceiptResponse {
    1:required i32 code,
    2:required string msg,
    3:optional ReportIapReceiptDto data
}

struct ReportIapReceiptDto {
    1:required string rechargeOrderNum,
    2:required string transactionId,
    3:required string outTradeNo
}

struct QueryBizOrderRequest {
    1:required i32 businessId,
    2:required string orderNo,
    3:required string sign;
}

struct QueryBizOrderResponse {
    1:required i32 code,
    2:required string msg,
    3:optional RechargeOrderInfoDto data
}

struct QueryRechargeListRequest {
    1:required i32 businessId,
    2:required i64 beginTime,
    3:required i64 endTime,
    4:required i64 lastRechargeId,
    5:required i32 batchSize,
    6:required set<i32> coinTypes,
    7:required string sign
}

struct QueryRechargeListResponse {
    1:required i32 code,
    2:required string msg,
    3:optional list<RechargeSuccessInfo> data,
    4:optional i64 lastRechargeId,
}

struct RechargeSuccessInfo {
    1:required i64 rechargeId, /** 唯一标识 */
    2:required string rechargeOrderNum, /** 直播订单号 */
    3:required string consumeOrderNum, /** 网关订单号 */
    4:required i64 addTime, /** 充值下单时间 */
    5:required i64 rechargeTime, /** 充值发货时间 */
    6:required i64 kugouId, /** 充值酷狗id */
    7:required i64 fromKugouId, /** 谁充的 */
    8:required string coin, /** 充值星币 */
    9:required string amount, /** 充值金额 */
    10:required string realAmount, /** 废弃 */
    11:required i32 payTypeId, /** 充值渠道 */
    12:required string extraJsonData, /** 扩展属性字段 */
    13:required i32 refer, /** 废弃 */
    14:required i32 cFrom, /** 充值平台 */
    15:required i32 channelId, /** 废弃 */
    16:required i32 reType, /** 充值类型 */
    17:required string extend, /** 扩展信息 */
    18:required string businessId, /** 业务标识 */
    19:required i64 tradeTime, /** 交易时间 */
    20:required string tradeNo, /** 交易单号 */
    21:required string partner, /** 交易商户 */
    22:required string coupon, /** 代金券金额 */
    23:required i32 couponStatus, /** 代金券状态 */
    24:required i64 couponOrderId, /** 代金券订单号 */
    25:required i32 isSandbox, /** 是否沙盒充值 */
    26:required string money, /** 实付金额 */
    27:required i64 couponId, /** 代金券标识 */
    28:required i32 serverRoom, /** 废弃 */
    29:optional i32 coinType /** 充值币种（0:星币；1:星币；2:唱币）*/
}


