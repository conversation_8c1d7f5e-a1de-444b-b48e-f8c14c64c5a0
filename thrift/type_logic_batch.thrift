namespace java com.kugou.fanxing.thrift.acksocket.gather.types.batch

include "type_logic.thrift"

struct LogicBatchMsgOption {
    /**消息全局id*/
    1:optional i64 gid = 0,
    /**是否幂等*/
    2:optional bool isIdempotent = false,
}

/**
* 房间广播消息对象
* 支持不同命令号、不同消息体，不同房间ID的批量消息
**/
struct LogicBatchRoomMessageRequest {
    1:required list<LogicRoomMessage> messageList,
    2:required LogicBatchMsgOption batchMsgOption,
}

/**
* 房间单播消息对象
* 支持不同命令号、不同消息体，不同房间ID、酷狗ID的批量消息
**/
struct LogicBatchUserMessageRequest {
    1:required list<LogicUserMessage> messageList,
    2:required LogicBatchMsgOption batchMsgOption,
}

struct LogicBatchUserMessageWithoutRoomIdRequest {
    1:required list<LogicUserMessageWithoutRoomId> messageList,
    2:required LogicBatchMsgOption batchMsgOption,
}

/**
* 房间消息对象
**/
struct LogicRoomMessage {
    1:required i32 cmd,
    2:required i32 roomId,
    4:required binary content,
    5:required type_logic.MsgOption msgOption
}

/**
* 用户消息对象
**/
struct LogicUserMessage {
    1:required i32 cmd,
    2:required i32 roomId,
    3:required i64 kugouId,
    4:required i64 userId,
    5:required binary content,
    6:required type_logic.MsgOption msgOption
}

/**
* 用户消息对象
**/
struct LogicUserMessageWithoutRoomId{
    1:required i32 cmd,
    2:required i64 kugouId,
    3:required i64 userId,
    4:required binary content,
    5:required type_logic.MsgOption msgOption
}
