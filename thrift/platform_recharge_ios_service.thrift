namespace java com.kugou.fanxing.thrift.recharge.ios

/**
 * 通用请求参数
 */
struct CommonParamsV1 {
    /** 业务ID */
    1:required string appId,
    /** 平台编号 */
    2:required i32 pid,
    /** 请求时间（毫秒） */
    3:required i64 timestamp
}

struct CommonParamsV2 {
    /** 业务ID */
    1:required string businessId,
    /** 平台编号 */
    2:required i32 pid,
    /** 请求时间（毫秒） */
    3:required i64 timestamp
}

struct QueryProductRequest {
    /** 应用号 */
    1:required CommonParamsV1 commonParam,
    /** 业务ID */
    2:required string productId,
    /** 签名 */
    3:required string sign
}

struct QueryProductListRequest {
    /** 应用号 */
    1:required CommonParamsV1 commonParam,
    /** 业务ID */
    2:required i32 productType,
    /** 签名 */
    3:required string sign
}

struct QueryProductResponse {
    1:required i32 code,
    2:required string msg,
    3:optional AppStoreProductDTO data
}

struct QueryProductListResponse {
    1:required i32 code,
    2:required string msg,
    3:required list<AppStoreProductDTO> data,
}

struct QueryProductListV2Request {
    /** 应用号 */
    1:required CommonParamsV2 commonParam,
    /** 业务ID */
    2:required i32 productType,
    /** 签名 */
    3:required string sign
}

struct QueryProductListV2Response {
    1:required i32 code,
    2:required string msg,
    3:required list<AppStoreProductV2DTO> data,
}

struct AppStoreProductDTO {
    1:required string productId,
    2:required i32 productType,
    3:required string productDesc,
    4:required string money,
    5:required string coin,
    6:required i32 autoSubscribe,
    7:required i32 autoSubscribeGroup,
    8:required i32 autoSubscribePeriod,
    9:required string autoSubscribePeriodUnit,
    10:required i32 introductoryOffers,
    11:required string introductoryOffersPrice,
    12:optional string ext,
    13:optional string bundleId
}

struct AppStoreProductV2DTO {
    1:required string productId,
    2:required i32 productType,
    3:required string productDesc,
    4:required string money,
    5:required string coin,
    6:required i32 autoSubscribe,
    7:required i32 autoSubscribeGroup,
    8:required i32 autoSubscribePeriod,
    9:required string autoSubscribePeriodUnit,
    10:required i32 introductoryOffers,
    11:required string introductoryOffersPrice,
    12:required i32 disable
}


struct QueryOutTradeNoRequest {
    1:required string rechargeOrderNum
}

struct QueryOutTradeNoResponse {
    1:required i32 code,
    2:required string msg,
    3:optional QueryOutTradeNoDTO data
}

struct QueryOutTradeNoDTO {
    1:required string rechargeOrderNum,
    2:required string transactionId,
    3:required string orderNo,
    4:required string outTradeNo
}

struct QueryTransactionIdByOrderNumRequest {
    /** 苹果充值交易号 */
    1:required string orderNum
}

struct QueryTransactionIdByOrderNumResponse {
    1:required i32 code,
    2:required string msg,
    /** 第三方支付充值交易号 */
    3:optional string transactionId
}

struct QueryOrderNumByTransactionIdRequest {
    /** 苹果充值交易号 */
    1:required string transactionId
}

struct QueryOrderNumByTransactionIdResponse {
    1:required i32 code,
    2:required string msg,
    /** 第三方支付充值交易号 */
    3:optional string orderNum
}

service AppStoreRpcService {
    QueryProductResponse queryProduct(1:QueryProductRequest request);
    QueryProductListResponse queryProductList(1:QueryProductListRequest request);
    QueryProductListV2Response queryProductListV2(1:QueryProductListV2Request request);
    QueryOutTradeNoResponse queryOutTradeNo(1:QueryOutTradeNoRequest request);
    QueryTransactionIdByOrderNumResponse queryTransactionIdByOrderNum(1:QueryTransactionIdByOrderNumRequest request);
    QueryOrderNumByTransactionIdResponse queryOrderNumByTransactionId(1:QueryOrderNumByTransactionIdRequest request);
}

