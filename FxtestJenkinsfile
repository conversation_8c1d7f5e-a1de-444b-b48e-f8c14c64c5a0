pipeline {
    agent none
    parameters {
        string(name: 'service_name', defaultValue: 'video', description: 'CI服务名')
        string(name: 'ref', defaultValue: 'master', description: '分支/Tag名')
        string(name: 'method', defaultValue: 'push', description: '触发方式')
        string(name: 'pid', defaultValue: '123', description: '项目ID')
        string(name: 'project', defaultValue: 'video/live_stream_control', description: '项目名')
        string(name: 'junitname', defaultValue: 'fxtest_junit_task', description: 'Junit项目名')
        string(name: 'project_uri', defaultValue: 'http://service.docker.kugou.net/foo/', description: '项目地址')
        string(name: 'username', defaultValue: 'shinhuang', description: 'OPD英文名')
        string(name: 'user_email', defaultValue: '<EMAIL>', description: '用户邮箱')
        string(name: 'namespace', defaultValue: 'ci-fxbase', description: '命名空间')
        string(name: 'release_name', defaultValue: '', description: '发布名')
        string(name: 'git_ssh_url', defaultValue: '', description: '项目Git地址')
    }
    stages {
        stage('代码测试') {
            agent { 
                label 'sonar'
            }
            steps {
                parallel '代码检测':{
                    echo "--buildStep-代码检测"
                    sh "/bin/bash ./fxtest_ci.sh scan ${params.project} ${params.ref}"
                },
                '单元测试':{
                    echo "--buildStep-单元测试"
                }
            }
        }

        stage('服务部署') {
            agent { 
                label 'make'
            }
            steps {
                    echo "--buildStep-服务部署"
                    sh "/bin/bash ./fxtest_ci.sh deploy ${params.project} ${params.ref}"
            }
        }
        stage('自动化测试') {
            agent { 
                label 'junit'
            }
            steps {
                    echo "--buildStep-自动化测试"
                    script{
                        build job: "${params.junitname}", parameters: [string(name: 'service_name', value: "${params.service_name}"), string(name: 'user_email', value: "${params.user_email}"),string(name: 'pid', value: "${params.pid}"), string(name: 'ref', value: "${params.ref}"), string(name: 'release_name', value: "${params.release_name}"), string(name: 'git_ssh_url', value: "${params.git_ssh_url}"), string(name: 'username', value: "${params.username}") , string(name: 'project', value: "${params.project}")]  
                    }
            }
        }
    }
}