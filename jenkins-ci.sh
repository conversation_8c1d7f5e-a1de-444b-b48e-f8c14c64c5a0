#!/bin/bash
#
# Jenkins pipeline 持续部署脚本
# <AUTHOR> <EMAIL>
#

echo -e "\n工作节点: $HOSTNAME"
echo -e "工作用户: $(whoami)"
echo -e "工作目录: $PWD"
echo -e "执行命令：$0 $@"

# 分析参数
option=$1
shift

ref=$1
image=$2
app_dir=${image##*/} #应用目录
version=$3
method=$4
ci_project=$5
username=$6
service_name=$7
namespace=$8
pid=$9

echo -e "\n切换分支/Tag"
git fetch --all 
git checkout -f $ref
git pull 

# 镜像名格式如：fxsoa/stream_push_addr_dispatcher
# 镜像名必须为小写
image=$(echo $image |tr '[A-Z]' '[a-z]') 
image_uri="docker.fanxing.kgidc.cn/$image:${version}"

# 调度后台
mgr_host='http://mgr.fxwork.kugou.net'
# Docker接入层
docker_service_host='http://docker.fxwork.kugou.com'

# 发送错误提醒
error_and_exit() {
	local name=$1
	local ret=$2
	local url=$3
	if [[ $ret -gt 0 ]]; then
		curl -s -d "pid=${pid}&name=${name}&url=${url}" "${mgr_host}/api/ci_error_msg"
		exit $ret
	fi
}

# main
case $option in 
	scan)
		detail='代码检测'
		echo -e "\n${detail}"
		curl -s -d "pid=${pid}&detail=${detail}" "${mgr_host}/api/ci_detail"
		if [[ $method = 'push' || $method = 'run' ]]; then
			echo -e "\n执行 Sonar 扫描：sonar_scanner.sh"
			/bin/bash /data1/sonar-scanner/sonar_scanner.sh "${version}" "${app_dir}" "src/main/java" "${pid}"
			error_and_exit $detail $?

		    report_file='.scannerwork/report-task.txt'
			echo -e "\nSonar 扫描结果文件：${report_file}"
			cat $report_file
			error_and_exit $detail $?

			source $report_file
			# 上报 Sonar 结果链接
			curl -s -d "pid=${pid}&type=sonar&url=${dashboardUrl}" "${mgr_host}/api/ci_sonar_url"
			# 获取 Sonar 结果
			echo -e "\n获取 Sonar 分析结果：check_result.py $ceTaskUrl"
			python /data1/sonar-scanner/check_result.py $ceTaskUrl
			ret=$?
			if  [[ $ret -eq 2 ]] ;then
				echo "Sonar 分析超时，请通知管理员"
				exit 0
			fi
			if  [[ $ret -eq 3 ]] ;then
				echo "Sonar 分析 search 接口返回错误，请通知管理员"
				exit 0
			fi
			[[ $ret -eq 1 ]] && error_and_exit $detail $ret
			exit $ret
		else
			echo -e "\n${method} 方式不执行代码检测"
		fi
		;;
	build)
		detail='编译代码'
		echo -e "\n${detail}"
		curl -s -d "pid=${pid}&detail=${detail}" "${mgr_host}/api/ci_detail" 

		echo -e "\n执行编译脚本：make.sh $app_dir"
		/bin/bash make.sh $app_dir 
		error_and_exit $detail $? 

		detail='制作镜像'
		echo -e "\n${detail}"
		curl -s -d "pid=${pid}&detail=${detail}" "${mgr_host}/api/ci_detail" 

		echo -e "\n制作镜像：${image_uri}"
		docker build -t ${image_uri} --rm=true --pull . 
		error_and_exit $detail $? 
		
		echo -e "\n测试镜像：${image_uri}"
		docker run --rm ${image_uri} /bin/bash -c 'echo success' 
		echo -e "\n推送镜像：${image_uri}"
		retry=0
		is_push_ok=false
		while [[ $retry -le 3 ]]; do
			docker push ${image_uri} && is_push_ok=true
			if $is_push_ok; then
				break
			fi
			let retry=$retry+1
			echo -e "重试第 ${retry} 次"
			sleep 5
		done
		if ! $is_push_ok; then
			echo -e "\n推送镜像：${image_uri} 失败，退出"
			error_and_exit $detail 1
		fi
		;;
	deploy)
        #
        # 部署项目
		detail='部署项目'
		echo -e "\n${detail}"
		curl -s -d "pid=${pid}&detail=${detail}" "${mgr_host}/api/ci_detail" 

		[[ -d "$app_dir" ]] && docker_app_file="${app_dir}/docker.app.json" || docker_app_file='./docker.app.json'
		if [[ ! -e "$docker_app_file" ]]; then 
			echo -e "未提供配置文件 docker.app.json"
			echo '{ "APP_ENV":"test" }' >$docker_app_file
		fi
		res=$(curl -s -F "project=$ci_project" -F "version=$version" -F "image=$image" -F "namespace=$namespace" -F "username=$username" -F "file=@${docker_app_file}" "${mgr_host}/api/docker_admin_deploy")
		if [[ $res != 'OK' ]]; then
			echo -e "部署失败，原因："
			echo $res
			error_and_exit $detail 1
		fi

		#
		# 服务启动
		detail='等待服务启动'
		echo -e "\n${detail}"
		curl -s -d "pid=${pid}&detail=${detail}" "${mgr_host}/api/ci_detail" 
		docker_project_url=$(curl -s "${mgr_host}/api/docker_admin_get_project_url/?project=${ci_project}&version=${version}")
		echo -e "\n项目地址 $docker_project_url"
		check_pod_url="${mgr_host}/api/docker_admin_check_pod?project=${ci_project}&namespace=${namespace}&version=${version}"
		echo -e "\n检查容器接口 $check_pod_url\n"
		i=1;
		while true; do 
			echo "第${i}次检查 $(date +%T)"
			res=$(curl -s $check_pod_url)
			if [[ $res = 'OK' ]]; then
				echo -e "\nOK, 服务已启动"
				break;
			fi
			if [[ $i -gt 30 ]]; then
				echo -e "\n超时退出，服务可能未启动，查看详情 ${docker_project_url}"
				error_and_exit $detail 1
			fi
			sleep 5
			let i=$i+1
		done

        #
        # 健康检查
		detail='服务健康检查'
		echo -e "\n${detail}"
		[[ -d "$app_dir" ]] && healthz_file="${app_dir}/health_check.conf" || healthz_file='./health_check.conf'
		if [[ -e "$healthz_file" ]]; then 
			echo -e "\n健康检查配置文件：${healthz_file}"
			cat $healthz_file |grep -v ^$
			source $healthz_file
		fi
        # 默认值
        [[ -z $CHECK_ENABLE ]] && CHECK_ENABLE=true
        [[ -z $CHECK_URI ]] && CHECK_URI="ctl/running"
        [[ -z $CHECK_TIMES ]] && CHECK_TIMES=30
        if $CHECK_ENABLE; then
            curl -s -d "pid=${pid}&detail=${detail}" "${mgr_host}/api/ci_detail"
            echo -e "\n检查服务健康接口 $docker_service_host/$ci_project/${CHECK_URI}\n"
            i=1;
            healthz_url="$docker_service_host/${CHECK_URI}"
            while true; do 
                echo "第${i}次检查 $(date +%T)"
                res=$(curl -s -H "Service: $ci_project" $healthz_url)
                if [[ $res = 'OK' ]]; then
                    echo -e "\nOK, 服务健康"
                    break;
                fi
                if [[ $i -gt $CHECK_TIMES ]]; then
                    echo -e "\n超时退出，服务不健康"
					error_and_exit $detail 1
                fi
                sleep 5
                let i=$i+1
            done
        fi
        ;;
    get_build_url)
		pid=$2
        name=$3
        release_name=$4
        jenkins_build_url=$(curl -s -d "name=$name&release=${release_name}" "${mgr_host}/api/jenkins_get_build")
        echo -e "\n本次 ${name} build 链接：$jenkins_build_url"
        ;;
    update_junit)
		pid=$2
		name=$3
		detail="创建 ${name}"
		echo -e "\n${detail}"
		curl -s -d "pid=${pid}&detail=${detail}" "${mgr_host}/api/ci_detail"
		curl -s -d "pid=${pid}&name=${name}" "${mgr_host}/api/jenkins_update_junit"
        ;;
esac

exit 0
