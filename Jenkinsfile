pipeline {
    agent none
    parameters {
        string(name: 'service_name', defaultValue: 'foo_bar', description: 'CI服务名')
        string(name: 'ref', defaultValue: 'master', description: '分支/Tag名')
        string(name: 'image', defaultValue: 'fxsoa/test', description: '镜像名')
        string(name: 'version', defaultValue: '1024', description: '版本号')
        string(name: 'method', defaultValue: 'push', description: '触发方式')
        string(name: 'pid', defaultValue: '123', description: '项目ID')
        string(name: 'project', defaultValue: 'ci-foo-bar', description: 'CI项目名')
        string(name: 'project_uri', defaultValue: 'http://service.docker.kugou.net/foo/', description: '项目地址')
        string(name: 'username', defaultValue: 'shinhuang', description: 'OPD英文名')
        string(name: 'user_email', defaultValue: '<EMAIL>', description: '用户邮箱')
        string(name: 'namespace', defaultValue: 'ci-fxbase', description: '命名空间')
        string(name: 'release_name', defaultValue: '', description: '发布名')
        string(name: 'git_ssh_url', defaultValue: '', description: '项目Git地址')
    }
    stages {
        stage('代码测试') {
            agent {
                label 'sonar'
            }
            steps {
                parallel '代码检测':{
                    sh "/bin/bash ./jenkins-ci.sh scan ${params.ref} ${params.image} ${params.version} ${params.method} ${params.project} ${params.username} ${params.service_name} ${params.namespace} ${params.pid}"
                },
                '单元测试':{
                    echo "test"
                }
            }
        }
        stage('制作镜像') {
            agent { 
                label 'docker'
            }
            steps {
                sh "/bin/bash ./jenkins-ci.sh build ${params.ref} ${params.image} ${params.version} ${params.method} ${params.project} ${params.username} ${params.service_name} ${params.namespace} ${params.pid}"
            }
        }
        stage('部署') {
            agent { 
                label 'docker'
            }
            steps {
                sh "/bin/bash ./jenkins-ci.sh deploy ${params.ref} ${params.image} ${params.version} ${params.method} ${params.project} ${params.username} ${params.service_name} ${params.namespace} ${params.pid}"
            }
        }
        stage('自动化测试') {
            agent { 
                label 'junit'
            }
            steps {
                parallel 'RF 接口测试':{
                    script{
                        try{
                            sh "/bin/bash /data1/CI_Script/check_rf_case.sh ${params.service_name} ${params.pid}"
                            env.Result_rf = "1"
                        }
                        catch (exc){
                            env.Result_rf = "0"
                        }
                        if (env.Result_rf=='1'){
                            sh "/bin/bash ./jenkins-ci.sh get_build_url ${params.ref} ${params.pid} 'rf_task' ${params.release_name}"
                            build job: 'rf_task', parameters: [string(name: 'service_name', value: "${params.service_name}"), string(name: 'project_uri', value: "${params.project_uri}"), string(name: 'user_email', value: "${params.user_email}"), string(name: 'pid', value: "${params.pid}"), string(name: 'release_name', value: "${params.release_name}")]
                        } else {
                            echo "${params.service_name} 无RF测试用例，退出"
                            sh "exit 0"
                        }
                    }
                }, 
                    'Junit 接口测试':{
                        script{
                            sh "/bin/bash ./jenkins-ci.sh update_junit ${params.ref} ${params.pid} junit_${params.service_name}"
                            sh "/bin/bash ./jenkins-ci.sh get_build_url ${params.ref} ${params.pid} junit_${params.service_name} ${params.release_name}"
                            build job: "junit_${params.service_name}", parameters: [string(name: 'service_name', value: "${params.service_name}"), string(name: 'user_email', value: "${params.user_email}"),string(name: 'pid', value: "${params.pid}"), string(name: 'ref', value: "${params.ref}"), string(name: 'release_name', value: "${params.release_name}"), string(name: 'image', value: "${params.image}"), string(name: 'git_ssh_url', value: "${params.git_ssh_url}")]  
                    }
                }
            }
        }
    }
}
