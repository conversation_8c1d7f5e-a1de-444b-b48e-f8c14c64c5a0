#!/bin/bash
#

#echo "ִ�� gradlew idea ..."
#bash gradlew idea || exit $?
echo "ִ�� gradlew clean deploy ..."
bash gradlew clean deploy || exit $?

bash gradlew processResources

mkdir -p outer/lib
rsync -aq --delete build/deploy/ outer/lib/

mkdir -p outer/etc
rsync -aq --delete src/main/resources/ outer/etc/

install -v -c -m 755 run.sh outer/

if cd outer/etc; then
    for conf in $(ls -d *.tpl); do
        mv -v $conf ${conf%.tpl}
    done
fi

exit 0
