apply {
    plugin 'java'
    plugin 'jacoco'
    plugin 'idea'
    plugin 'eclipse'
    plugin 'kotlin'
}

jacoco {
    toolVersion = "0.8.1"
}

repositories {
    maven { url "http://nexus.fanxing.kgidc.cn/nexus/content/groups/public/" }
    mavenCentral()
}

archivesBaseName = archivesProjectName

[compileJava, compileTestJava]*.options*.encoding = "UTF-8"
sourceSets.main.java.srcDirs += ["src/gen/java"]

sourceSets {
    unitTest {
        java.srcDirs += ["src/unit-test/java"]
        resources.srcDirs += "src/unit-test/resources"
        compileClasspath += sourceSets.main.output + sourceSets.test.output + configurations.unitTestCompile
        runtimeClasspath += main.output + test.output + compileClasspath + configurations.unitTestRuntime

    }
    integrationTest {
        java.srcDirs += ["src/integration-test/java"]
        resources.srcDirs += "src/integration-test/resources"
        compileClasspath += sourceSets.main.output + sourceSets.test.output + configurations.integrationTestCompile
        runtimeClasspath += main.output + test.output + compileClasspath + configurations.integrationTestRuntime
    }
}

configurations {
    integrationTestCompile {
        extendsFrom testCompile, compile
    }
    integrationTestRuntime {
        extendsFrom integrationTestCompile, testRuntime
    }
    unitTestCompile {
        extendsFrom testCompile, compile
    }
    unitTestRuntime {
        extendsFrom unitTestCompile, testRuntime
    }
    integrationTestAnnotationProcessor {
        extendsFrom testAnnotationProcessor
    }
    unitTestAnnotationProcessor {
        extendsFrom testAnnotationProcessor
    }
}

task unitTest(type: Test) {
    testClassesDirs = sourceSets.unitTest.output.classesDirs
    classpath = sourceSets.unitTest.runtimeClasspath
    reports {
        html.destination project.file("$project.buildDir/reports/unit-tests/")
        junitXml.destination project.file("$project.buildDir/unit-test-results/")
    }
}

task integrationTest(type: Test, dependsOn: jar) {
    testClassesDirs = sourceSets.integrationTest.output.classesDirs
    classpath = sourceSets.integrationTest.runtimeClasspath
    reports {
        html.destination = project.file("$project.buildDir/reports/integration-tests/")
        junitXml.destination project.file("$project.buildDir/integration-test-results/")
    }
}

task newintegrationTest(type: Test) {
    useJUnit {
        includeCategories 'com.kugou.fanxing.tester.common.tools.NewIntegrationTestCategory'
    }
    maxParallelForks = Runtime.runtime.availableProcessors().intdiv(2) ?: 1
    testClassesDirs = sourceSets.'integrationTest'.output.classesDirs
    classpath = sourceSets.'integrationTest'.runtimeClasspath
    reports {
        html.destination = project.file("$project.buildDir/reports/integration-tests/")
        junitXml.destination = project.file("$project.buildDir/integration-test-results/")
    }
}

configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'

    all*.exclude group: 'org.springframework.boot', module: 'spring-boot-starter-tomcat'
    all*.exclude group: 'com.google.collections', module: 'google-collections'
    all*.exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    all*.exclude group: 'com.kugou', module: 'kugou-cat'
    all*.exclude group: 'com.kugou.springboot', module: 'kugou-ibatis'
}


dependencies {
    compile "com.kugou.springboot:kugou-starter:${kugoubootVersion}"
    compile "com.kugou.springboot:kugou-rpc:${kugoubootVersion}"
    compile "com.kugou.springboot:kugou-cache:${kugoubootVersion}"
    compile "com.kugou.springboot:kugou-mq:${kugoubootVersion}"
    compile "com.kugou.springboot:kugou-saturn:${kugoubootVersion}"
    compile "com.kugou.springboot:kugou-spring-cloud:${kugoubootVersion}"
    compile "com.kugou.springboot:kugou-ibatis-with-monitor:${kugoubootVersion}"
    compile "com.kugou.springboot:kugou-logback-kafka:${kugoubootVersion}"
    compile "com.kugou.springboot:kugou-pulsar:${kugouPulsarVersion}"
    testCompile "com.kugou.springboot:kugou-test:${kugoubootVersion}"
    compile "fx_springboot_center:kw_idservice_api:1.1.0"
    compile 'fx_springboot_center:kw_checktoken_api:1.0.2'
    compile 'commons-validator:commons-validator:1.7'
    compile "com.squareup.okhttp3:okhttp:3.13.1"
    compile 'com.jayway.jsonpath:json-path:2.7.0'
    compile group: 'org.jsoup', name: 'jsoup', version: '1.11.3'
    compile("com.kugou.fanxing:risk-sdk-core:1.2.9") { exclude group: 'commons-io', module: 'commons-io' }
    compile 'com.kugou.fanxing.budget:budget-sdk-starter:1.0.1'
    compile "com.github.rholder:guava-retrying:2.0.0"
    compile "com.kugou.fanxing:fx-biz-commons:1.3.2"
    compile("com.kugou.fanxing:fx-commons:1.3.2")
    compile("com.alibaba:fastjson:1.2.83")
    compile 'fx_springboot_center:user_baseinfo_api:1.0.2'

    compileOnly "org.projectlombok:lombok:1.18.24"
    annotationProcessor "org.projectlombok:lombok:1.18.24"
    testCompile 'org.projectlombok:lombok:1.18.24'
    testAnnotationProcessor "org.projectlombok:lombok:1.18.24"

    testCompile "com.kugou.fanxing.tester.common.tools:commonTools:2.2.58"
    testCompile "com.kugou.springboot:kugou-test:${kugoubootVersion}"
    testCompile("org.mockito:mockito-inline:4.8.1")
    testCompile("org.mockito:mockito-core:4.8.1")

    compile "com.kugou.springboot:fx_star_service_api:1.3.2"
    testCompile "org.jetbrains.kotlin:kotlin-stdlib:$kotlinVersion"
    testCompile "org.jetbrains.kotlin:kotlin-test:1.1.51"
}

task copyLib(type: Sync, dependsOn: jar) {
    from configurations.compile
    from jar.archivePath
    into 'build/deploy'
}
jar.exclude '*.conf'
jar.exclude 'spring-logback.xml'

task deploy(dependsOn: copyLib)

//指定编码
tasks.withType(JavaCompile) {
    options.encoding = "UTF-8"
}

buildscript {
    repositories {
        mavenLocal()
        maven { url "http://nexus.fanxing.kgidc.cn/nexus/content/groups/public/" }
    }
    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion"
    }
}
